{"date": "April 25", "url": "https://wikipedia.org/wiki/April_25", "data": {"Events": [{"year": "404 BC", "text": "Admiral <PERSON><PERSON><PERSON> and King <PERSON><PERSON><PERSON> of Sparta blockade Athens and bring the Peloponnesian War to a successful conclusion.", "html": "404 BC - 404 BC - Admiral <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and King <a href=\"https://wikipedia.org/wiki/Pausanias_of_Sparta\" class=\"mw-redirect\" title=\"Pausanias of Sparta\">Pausanias of Sparta</a> blockade Athens and bring the <a href=\"https://wikipedia.org/wiki/Peloponnesian_War\" title=\"Peloponnesian War\">Peloponnesian War</a> to a successful conclusion.", "no_year_html": "404 BC - Admiral <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and King <a href=\"https://wikipedia.org/wiki/Pausanias_of_Sparta\" class=\"mw-redirect\" title=\"Pausanias of Sparta\">Pausanias of Sparta</a> blockade Athens and bring the <a href=\"https://wikipedia.org/wiki/Peloponnesian_War\" title=\"Peloponnesian War\">Peloponnesian War</a> to a successful conclusion.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lysander"}, {"title": "Pausanias of Sparta", "link": "https://wikipedia.org/wiki/Pausanias_of_Sparta"}, {"title": "Peloponnesian War", "link": "https://wikipedia.org/wiki/Peloponnesian_War"}]}, {"year": "775", "text": "The Battle of Bagrevand puts an end to an Armenian rebellion against the Abbasid Caliphate. Muslim control over the South Caucasus is solidified and its Islamization begins, while several major Armenian nakharar families lose power and their remnants flee to the Byzantine Empire.", "html": "775 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Bagrevand\" title=\"Battle of Bagrevand\">Battle of Bagrevand</a> puts an end to an <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenian</a> rebellion against the <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\">Abbasid Caliphate</a>. Muslim control over the <a href=\"https://wikipedia.org/wiki/South_Caucasus\" title=\"South Caucasus\">South Caucasus</a> is solidified and its Islamization begins, while several major Armenian <i><a href=\"https://wikipedia.org/wiki/Nakharar\" title=\"Nakharar\">nakharar</a></i> families lose power and their remnants flee to the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Bagrevand\" title=\"Battle of Bagrevand\">Battle of Bagrevand</a> puts an end to an <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenian</a> rebellion against the <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\">Abbasid Caliphate</a>. Muslim control over the <a href=\"https://wikipedia.org/wiki/South_Caucasus\" title=\"South Caucasus\">South Caucasus</a> is solidified and its Islamization begins, while several major Armenian <i><a href=\"https://wikipedia.org/wiki/Nakharar\" title=\"Nakharar\">nakharar</a></i> families lose power and their remnants flee to the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "links": [{"title": "Battle of Bagrevand", "link": "https://wikipedia.org/wiki/Battle_of_Bagrevand"}, {"title": "Armenians", "link": "https://wikipedia.org/wiki/Armenians"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abbasid_Caliphate"}, {"title": "South Caucasus", "link": "https://wikipedia.org/wiki/South_Caucasus"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "799", "text": "After mistreatment and disfigurement by the citizens of Rome, Pope <PERSON> flees to the Frankish court of king <PERSON><PERSON><PERSON><PERSON> at Paderborn for protection.", "html": "799 - After mistreatment and <a href=\"https://wikipedia.org/wiki/Disfigurement\" title=\"Disfigurement\">disfigurement</a> by the citizens of Rome, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Leo <PERSON>\">Pope <PERSON> III</a> flees to the <a href=\"https://wikipedia.org/wiki/Francia\" title=\"Francia\">Frankish</a> court of king <a href=\"https://wikipedia.org/wiki/Charlemagne\" title=\"Charlemagne\">Charlemagne</a> at <a href=\"https://wikipedia.org/wiki/Paderborn\" title=\"Paderborn\">Paderborn</a> for protection.", "no_year_html": "After mistreatment and <a href=\"https://wikipedia.org/wiki/Disfigurement\" title=\"Disfigurement\">disfigurement</a> by the citizens of Rome, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Leo <PERSON>\">Pope <PERSON></a> flees to the <a href=\"https://wikipedia.org/wiki/Francia\" title=\"Francia\">Frankish</a> court of king <a href=\"https://wikipedia.org/wiki/Charlemagne\" title=\"Charlemagne\">Cha<PERSON><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Paderborn\" title=\"Paderborn\">Paderborn</a> for protection.", "links": [{"title": "Disfigurement", "link": "https://wikipedia.org/wiki/Disfigurement"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Francia", "link": "https://wikipedia.org/wiki/Francia"}, {"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}, {"title": "Paderborn", "link": "https://wikipedia.org/wiki/Paderborn"}]}, {"year": "1134", "text": "The name Zagreb was mentioned for the first time in the Felician Charter relating to the establishment of the Zagreb Bishopric around 1094.", "html": "1134 - The name <a href=\"https://wikipedia.org/wiki/Zagreb\" title=\"Zagreb\">Zagreb</a> was mentioned for the first time in the Felician Charter relating to the establishment of the Zagreb Bishopric around 1094.", "no_year_html": "The name <a href=\"https://wikipedia.org/wiki/Zagreb\" title=\"Zagreb\">Zagreb</a> was mentioned for the first time in the Felician Charter relating to the establishment of the Zagreb Bishopric around 1094.", "links": [{"title": "Zagreb", "link": "https://wikipedia.org/wiki/Zagreb"}]}, {"year": "1607", "text": "Eighty Years' War: The Dutch fleet destroys the anchored Spanish fleet at Gibraltar.", "html": "1607 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: The Dutch fleet <a href=\"https://wikipedia.org/wiki/Battle_of_Gibraltar_(1607)\" title=\"Battle of Gibraltar (1607)\">destroys</a> the anchored Spanish fleet at <a href=\"https://wikipedia.org/wiki/Gibraltar\" title=\"Gibraltar\">Gibraltar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: The Dutch fleet <a href=\"https://wikipedia.org/wiki/Battle_of_Gibraltar_(1607)\" title=\"Battle of Gibraltar (1607)\">destroys</a> the anchored Spanish fleet at <a href=\"https://wikipedia.org/wiki/Gibraltar\" title=\"Gibraltar\">Gibraltar</a>.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Battle of Gibraltar (1607)", "link": "https://wikipedia.org/wiki/Battle_of_Gibraltar_(1607)"}, {"title": "Gibraltar", "link": "https://wikipedia.org/wiki/Gibraltar"}]}, {"year": "1644", "text": "Transition from Ming to Qing: The Chongzhen Emperor, the last Emperor of Ming China, commits suicide during a peasant rebellion led by <PERSON>.", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Transition_from_Ming_to_Qing\" title=\"Transition from Ming to Qing\">Transition from Ming to Qing</a>: The <a href=\"https://wikipedia.org/wiki/Chongzhen_Emperor\" title=\"Chongzhen Emperor\">Chongzhen Emperor</a>, the last Emperor of <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming China</a>, commits suicide during a peasant rebellion led by <a href=\"https://wikipedia.org/wiki/Li_Zicheng\" title=\"Li Zicheng\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Transition_from_Ming_to_Qing\" title=\"Transition from Ming to Qing\">Transition from Ming to Qing</a>: The <a href=\"https://wikipedia.org/wiki/Chongzhen_Emperor\" title=\"Chongzhen Emperor\">Chongzhen Emperor</a>, the last Emperor of <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming China</a>, commits suicide during a peasant rebellion led by <a href=\"https://wikipedia.org/wiki/Li_Zicheng\" title=\"Li Zicheng\"><PERSON></a>.", "links": [{"title": "Transition from Ming to Qing", "link": "https://wikipedia.org/wiki/Transition_from_Ming_to_Qing"}, {"title": "Chongzhen Emperor", "link": "https://wikipedia.org/wiki/Chongzhen_Emperor"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_<PERSON>icheng"}]}, {"year": "1707", "text": "A coalition of Britain, the Netherlands and Portugal is defeated by a Franco-Spanish army at Almansa (Spain) in the War of the Spanish Succession.", "html": "1707 - A coalition of Britain, the Netherlands and Portugal is defeated by a Franco-Spanish army at <a href=\"https://wikipedia.org/wiki/Battle_of_Almansa\" title=\"Battle of Almansa\">Almansa</a> (Spain) in the <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>.", "no_year_html": "A coalition of Britain, the Netherlands and Portugal is defeated by a Franco-Spanish army at <a href=\"https://wikipedia.org/wiki/Battle_of_Almansa\" title=\"Battle of Almansa\">Almansa</a> (Spain) in the <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>.", "links": [{"title": "Battle of Almansa", "link": "https://wikipedia.org/wiki/Battle_of_Almansa"}, {"title": "War of the Spanish Succession", "link": "https://wikipedia.org/wiki/War_of_the_Spanish_Succession"}]}, {"year": "1792", "text": "Highwayman <PERSON> becomes the first person executed by guillotine.", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Highwayman\" title=\"Highwayman\">Highwayman</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person executed by <a href=\"https://wikipedia.org/wiki/Guillotine\" title=\"Guillotine\">guillotine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Highwayman\" title=\"Highwayman\">Highwayman</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person executed by <a href=\"https://wikipedia.org/wiki/Guillotine\" title=\"Guillotine\">guillotine</a>.", "links": [{"title": "Highwayman", "link": "https://wikipedia.org/wiki/<PERSON>man"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Guillotine", "link": "https://wikipedia.org/wiki/Guillotine"}]}, {"year": "1792", "text": "\"La Marseillaise\" (the French national anthem) is composed by <PERSON>.", "html": "1792 - \"<a href=\"https://wikipedia.org/wiki/La_Marseillai<PERSON>\" title=\"La Marseillaise\">La Marseillaise</a>\" (the French <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a>) is composed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "\"<a href=\"https://wikipedia.org/wiki/<PERSON>_Marseillai<PERSON>\" title=\"La Marseillaise\"><PERSON> Marseillaise</a>\" (the French <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a>) is composed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "La Marseillaise", "link": "https://wikipedia.org/wiki/<PERSON>_Mars<PERSON>laise"}, {"title": "National anthem", "link": "https://wikipedia.org/wiki/National_anthem"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "Dano-Swedish War of 1808-1809: The Battle of Trangen took place at Trangen in Flisa, Hedemarkens Amt, between Swedish and Norwegian troops.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Dano-Swedish_War_of_1808%E2%80%931809\" class=\"mw-redirect\" title=\"Dano-Swedish War of 1808-1809\">Dano-Swedish War of 1808-1809</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Trangen\" title=\"Battle of Trangen\">Battle of Trangen</a> took place at Trangen in <a href=\"https://wikipedia.org/wiki/Flisa\" title=\"Flisa\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Hedmark\" title=\"Hedmark\">Hedemarkens Amt</a>, between Swedish and Norwegian troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dano-Swedish_War_of_1808%E2%80%931809\" class=\"mw-redirect\" title=\"Dano-Swedish War of 1808-1809\">Dano-Swedish War of 1808-1809</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Trangen\" title=\"Battle of Trangen\">Battle of Trangen</a> took place at Trangen in <a href=\"https://wikipedia.org/wiki/Flisa\" title=\"Flisa\">Flisa</a>, <a href=\"https://wikipedia.org/wiki/Hedmark\" title=\"Hedmark\">Hedemarkens Amt</a>, between Swedish and Norwegian troops.", "links": [{"title": "Dano-Swedish War of 1808-1809", "link": "https://wikipedia.org/wiki/Dano-Swedish_War_of_1808%E2%80%931809"}, {"title": "Battle of Trangen", "link": "https://wikipedia.org/wiki/Battle_of_Trangen"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Flisa"}, {"title": "Hedmark", "link": "https://wikipedia.org/wiki/Hedmark"}]}, {"year": "1829", "text": "<PERSON> arrives in HMS Challenger off the coast of modern-day Western Australia prior to declaring the Swan River Colony for the British Empire.", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Charles_<PERSON>\" title=\"Charles Fremantle\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/HMS_Challenger_(1826)\" title=\"HMS Challenger (1826)\">HMS <i>Challenger</i></a> off the coast of modern-day <a href=\"https://wikipedia.org/wiki/Western_Australia\" title=\"Western Australia\">Western Australia</a> prior to declaring the <a href=\"https://wikipedia.org/wiki/Swan_River_Colony\" title=\"Swan River Colony\">Swan River Colony</a> for the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_<PERSON>\" title=\"Charles Fremantle\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/HMS_Challenger_(1826)\" title=\"HMS Challenger (1826)\">HMS <i>Challenger</i></a> off the coast of modern-day <a href=\"https://wikipedia.org/wiki/Western_Australia\" title=\"Western Australia\">Western Australia</a> prior to declaring the <a href=\"https://wikipedia.org/wiki/Swan_River_Colony\" title=\"Swan River Colony\">Swan River Colony</a> for the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_<PERSON>"}, {"title": "HMS Challenger (1826)", "link": "https://wikipedia.org/wiki/HMS_Challenger_(1826)"}, {"title": "Western Australia", "link": "https://wikipedia.org/wiki/Western_Australia"}, {"title": "Swan River Colony", "link": "https://wikipedia.org/wiki/Swan_River_Colony"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}]}, {"year": "1846", "text": "Thornton Affair: Open conflict begins over the disputed border of Texas, triggering the Mexican-American War.", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Thornton_Affair\" title=\"Thornton Affair\">Thornton Affair</a>: Open conflict begins over the disputed border of <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a>, triggering the <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thornton_Affair\" title=\"Thornton Affair\">Thornton Affair</a>: Open conflict begins over the disputed border of <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a>, triggering the <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thornton_Affair"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}]}, {"year": "1849", "text": "The Governor General of Canada, Lord <PERSON>, signs the Rebellion Losses Bill, outraging Montreal's English population and triggering the Montreal Riots.", "html": "1849 - The <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 8th Earl <PERSON>\">Lord <PERSON></a>, signs the <a href=\"https://wikipedia.org/wiki/Rebellion_Losses_Bill\" title=\"Rebellion Losses Bill\">Rebellion Losses Bill</a>, outraging <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>'s <a href=\"https://wikipedia.org/wiki/English_Canadian\" class=\"mw-redirect\" title=\"English Canadian\">English</a> population and triggering the <a href=\"https://wikipedia.org/wiki/Montreal_Riots\" class=\"mw-redirect\" title=\"Montreal Riots\">Montreal Riots</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_<PERSON>_Elgin\" title=\"<PERSON>, 8th Earl <PERSON> Elgin\">Lord <PERSON></a>, signs the <a href=\"https://wikipedia.org/wiki/Rebellion_Losses_Bill\" title=\"Rebellion Losses Bill\">Rebellion Losses Bill</a>, outraging <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>'s <a href=\"https://wikipedia.org/wiki/English_Canadian\" class=\"mw-redirect\" title=\"English Canadian\">English</a> population and triggering the <a href=\"https://wikipedia.org/wiki/Montreal_Riots\" class=\"mw-redirect\" title=\"Montreal Riots\">Montreal Riots</a>.", "links": [{"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}, {"title": "<PERSON>, 8th Earl of Elgin", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Earl_of_Elgin"}, {"title": "Rebellion Losses Bill", "link": "https://wikipedia.org/wiki/Rebellion_Losses_Bill"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}, {"title": "English Canadian", "link": "https://wikipedia.org/wiki/English_Canadian"}, {"title": "Montreal Riots", "link": "https://wikipedia.org/wiki/Montreal_Riots"}]}, {"year": "1859", "text": "British and French engineers break ground for the Suez Canal.", "html": "1859 - British and French engineers break ground for the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>.", "no_year_html": "British and French engineers break ground for the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>.", "links": [{"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}]}, {"year": "1862", "text": "American Civil War: Forces under U.S. Admiral <PERSON> demand the surrender of the Confederate city of New Orleans, Louisiana.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Forces under <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">U.S.</a> <a href=\"https://wikipedia.org/wiki/Admiral\" title=\"Admiral\">Admiral</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Capture_of_New_Orleans\" title=\"Capture of New Orleans\">demand the surrender</a> of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> city of <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans, Louisiana</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Forces under <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">U.S.</a> <a href=\"https://wikipedia.org/wiki/Admiral\" title=\"Admiral\">Admiral</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Capture_of_New_Orleans\" title=\"Capture of New Orleans\">demand the surrender</a> of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> city of <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans, Louisiana</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Admiral", "link": "https://wikipedia.org/wiki/Admiral"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Capture of New Orleans", "link": "https://wikipedia.org/wiki/Capture_of_New_Orleans"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "New Orleans", "link": "https://wikipedia.org/wiki/New_Orleans"}]}, {"year": "1864", "text": "American Civil War: In the Battle of Marks' Mills, a force of 8,000 Confederate soldiers attacks 1,800 Union soldiers and a large number of wagon teamsters, killing or wounding 1,500 Union combatants.", "html": "1864 - American Civil War: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Marks%27_Mills\" title=\"Battle of Marks' Mills\">Battle of Marks' Mills</a>, a force of 8,000 <a href=\"https://wikipedia.org/wiki/Confederate_States_Army\" title=\"Confederate States Army\">Confederate</a> soldiers attacks 1,800 <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union</a> soldiers and a large number of wagon <a href=\"https://wikipedia.org/wiki/Teamster\" title=\"Teamster\">teamsters</a>, killing or wounding 1,500 Union combatants.", "no_year_html": "American Civil War: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Marks%27_Mills\" title=\"Battle of Marks' Mills\">Battle of Marks' Mills</a>, a force of 8,000 <a href=\"https://wikipedia.org/wiki/Confederate_States_Army\" title=\"Confederate States Army\">Confederate</a> soldiers attacks 1,800 <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union</a> soldiers and a large number of wagon <a href=\"https://wikipedia.org/wiki/Teamster\" title=\"Teamster\">teamsters</a>, killing or wounding 1,500 Union combatants.", "links": [{"title": "Battle of Marks' Mills", "link": "https://wikipedia.org/wiki/Battle_of_Marks%27_Mills"}, {"title": "Confederate States Army", "link": "https://wikipedia.org/wiki/Confederate_States_Army"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "Teamster", "link": "https://wikipedia.org/wiki/Teamster"}]}, {"year": "1882", "text": "French and Vietnamese troops clashed in Tonkin, when Commandant <PERSON> seized the citadel of Hanoi with a small force of marine infantry.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">French</a> and <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty\" title=\"Nguyễn dynasty\">Vietnamese</a> troops clashed in <a href=\"https://wikipedia.org/wiki/Tonkin\" title=\"Tonkin\">Tonkin</a>, when Commandant <a href=\"https://wikipedia.org/wiki/Henri_<PERSON>ivi%C3%A8re_(naval_officer)\" title=\"<PERSON> (naval officer)\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Hanoi_(1882)\" title=\"Battle of Hanoi (1882)\">seized</a> the <a href=\"https://wikipedia.org/wiki/Citadel_of_Hanoi\" class=\"mw-redirect\" title=\"Citadel of Hanoi\">citadel of Hanoi</a> with a small force of marine infantry.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">French</a> and <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty\" title=\"Nguyễn dynasty\">Vietnamese</a> troops clashed in <a href=\"https://wikipedia.org/wiki/Tonkin\" title=\"Tonkin\">Tonkin</a>, when Commandant <a href=\"https://wikipedia.org/wiki/Henri_<PERSON>ivi%C3%A8re_(naval_officer)\" title=\"<PERSON> (naval officer)\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Hanoi_(1882)\" title=\"Battle of Hanoi (1882)\">seized</a> the <a href=\"https://wikipedia.org/wiki/Citadel_of_Hanoi\" class=\"mw-redirect\" title=\"Citadel of Hanoi\">citadel of Hanoi</a> with a small force of marine infantry.", "links": [{"title": "French Third Republic", "link": "https://wikipedia.org/wiki/French_Third_Republic"}, {"title": "Nguyễn dynasty", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty"}, {"title": "Tonkin", "link": "https://wikipedia.org/wiki/Tonkin"}, {"title": "<PERSON> (naval officer)", "link": "https://wikipedia.org/wiki/Henri_Rivi%C3%A8re_(naval_officer)"}, {"title": "Battle of Hanoi (1882)", "link": "https://wikipedia.org/wiki/Battle_of_Hanoi_(1882)"}, {"title": "Citadel of Hanoi", "link": "https://wikipedia.org/wiki/Citadel_of_Hanoi"}]}, {"year": "1898", "text": "Spanish-American War: The United States Congress declares that a state of war between the U.S. and Spain has existed since April 21, when an American naval blockade of the Spanish colony of Cuba began.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: The United States Congress <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_upon_Spain\" class=\"mw-redirect\" title=\"United States declaration of war upon Spain\">declares that a state of war between the U.S. and Spain</a> has existed since April 21, when an American naval <a href=\"https://wikipedia.org/wiki/Blockade\" title=\"Blockade\">blockade</a> of the Spanish colony of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> began.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: The United States Congress <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_upon_Spain\" class=\"mw-redirect\" title=\"United States declaration of war upon Spain\">declares that a state of war between the U.S. and Spain</a> has existed since April 21, when an American naval <a href=\"https://wikipedia.org/wiki/Blockade\" title=\"Blockade\">blockade</a> of the Spanish colony of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> began.", "links": [{"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}, {"title": "United States declaration of war upon Spain", "link": "https://wikipedia.org/wiki/United_States_declaration_of_war_upon_Spain"}, {"title": "Blockade", "link": "https://wikipedia.org/wiki/Blockade"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1901", "text": "New York becomes the first U.S. state to require automobile license plates.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Vehicle_registration_plates_of_New_York\" title=\"Vehicle registration plates of New York\">New York becomes the first U.S. state to require automobile license plates</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vehicle_registration_plates_of_New_York\" title=\"Vehicle registration plates of New York\">New York becomes the first U.S. state to require automobile license plates</a>.", "links": [{"title": "Vehicle registration plates of New York", "link": "https://wikipedia.org/wiki/Vehicle_registration_plates_of_New_York"}]}, {"year": "1915", "text": "World War I: The Battle of Gallipoli begins: The invasion of the Turkish Gallipoli Peninsula by British, French, Indian, Newfoundland, Australian and New Zealand troops, begins with landings at Anzac Cove and Cape Helles.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Gallipoli\" class=\"mw-redirect\" title=\"Battle of Gallipoli\">Battle of Gallipoli</a> begins: The invasion of the Turkish Gallipoli Peninsula by British, French, Indian, Newfoundland, Australian and New Zealand troops, begins with landings at <a href=\"https://wikipedia.org/wiki/Landing_at_Anzac_Cove\" title=\"Landing at Anzac Cove\">Anzac Cove</a> and <a href=\"https://wikipedia.org/wiki/Landing_at_Cape_Helles\" title=\"Landing at Cape Helles\">Cape Helles</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Gallipoli\" class=\"mw-redirect\" title=\"Battle of Gallipoli\">Battle of Gallipoli</a> begins: The invasion of the Turkish Gallipoli Peninsula by British, French, Indian, Newfoundland, Australian and New Zealand troops, begins with landings at <a href=\"https://wikipedia.org/wiki/Landing_at_Anzac_Cove\" title=\"Landing at Anzac Cove\">Anzac Cove</a> and <a href=\"https://wikipedia.org/wiki/Landing_at_Cape_Helles\" title=\"Landing at Cape Helles\">Cape Helles</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Gallipoli", "link": "https://wikipedia.org/wiki/Battle_of_Gallipoli"}, {"title": "Landing at Anzac Cove", "link": "https://wikipedia.org/wiki/Landing_at_Anzac_Cove"}, {"title": "Landing at Cape Helles", "link": "https://wikipedia.org/wiki/Landing_at_Cape_Helles"}]}, {"year": "1916", "text": "Anzac Day is commemorated for the first time on the first anniversary of the landing at ANZAC Cove.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Anzac_Day\" title=\"Anzac Day\">Anzac Day</a> is commemorated for the first time on the first anniversary of the landing at ANZAC Cove.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anzac_Day\" title=\"Anzac Day\">Anzac Day</a> is commemorated for the first time on the first anniversary of the landing at ANZAC Cove.", "links": [{"title": "Anzac Day", "link": "https://wikipedia.org/wiki/Anzac_Day"}]}, {"year": "1920", "text": "At the San Remo conference, the principal Allied Powers of World War I adopt a resolution to determine the allocation of Class \"A\" League of Nations mandates for administration of the former Ottoman-ruled lands of the Middle East.", "html": "1920 - At the <a href=\"https://wikipedia.org/wiki/San_Remo_conference\" title=\"San Remo conference\">San Remo conference</a>, the principal <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied Powers of World War I</a> adopt a resolution to determine the allocation of Class \"A\" <a href=\"https://wikipedia.org/wiki/League_of_Nations_mandate\" title=\"League of Nations mandate\">League of Nations mandates</a> for administration of the former <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a>-ruled lands of the Middle East.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/San_Remo_conference\" title=\"San Remo conference\">San Remo conference</a>, the principal <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied Powers of World War I</a> adopt a resolution to determine the allocation of Class \"A\" <a href=\"https://wikipedia.org/wiki/League_of_Nations_mandate\" title=\"League of Nations mandate\">League of Nations mandates</a> for administration of the former <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a>-ruled lands of the Middle East.", "links": [{"title": "San Remo conference", "link": "https://wikipedia.org/wiki/San_Remo_conference"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}, {"title": "League of Nations mandate", "link": "https://wikipedia.org/wiki/League_of_Nations_mandate"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1933", "text": "Nazi Germany issues the Law Against Overcrowding in Schools and Universities limiting the number of Jewish students able to attend public schools and universities.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> issues the Law Against Overcrowding in Schools and Universities limiting the number of Jewish students able to attend public schools and universities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> issues the Law Against Overcrowding in Schools and Universities limiting the number of Jewish students able to attend public schools and universities.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1938", "text": "U.S. Supreme Court delivers its opinion in Erie Railroad Co. v. <PERSON> and overturns a century of federal common law.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a> delivers its opinion in <i><a href=\"https://wikipedia.org/wiki/Erie_Railroad_Co._v._<PERSON>\" title=\"Erie Railroad Co. v. <PERSON>\">Erie Railroad Co. v. <PERSON></a></i> and overturns a century of federal common law.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a> delivers its opinion in <i><a href=\"https://wikipedia.org/wiki/Erie_Railroad_Co._v._<PERSON>\" title=\"Erie Railroad Co. v. <PERSON>\">Erie Railroad Co. v. <PERSON></a></i> and overturns a century of federal common law.", "links": [{"title": "U.S. Supreme Court", "link": "https://wikipedia.org/wiki/U.S._Supreme_Court"}, {"title": "Erie Railroad Co. v. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erie_Railroad_Co._v._<PERSON><PERSON><PERSON>"}]}, {"year": "1944", "text": "The United Negro College Fund is incorporated.", "html": "1944 - The <a href=\"https://wikipedia.org/wiki/United_Negro_College_Fund\" class=\"mw-redirect\" title=\"United Negro College Fund\">United Negro College Fund</a> is incorporated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Negro_College_Fund\" class=\"mw-redirect\" title=\"United Negro College Fund\">United Negro College Fund</a> is incorporated.", "links": [{"title": "United Negro College Fund", "link": "https://wikipedia.org/wiki/United_Negro_College_Fund"}]}, {"year": "1945", "text": "World War II: United States and Soviet reconnaissance troops meet in Torgau and Strehla along the River Elbe, cutting the Wehrmacht of Nazi Germany in two. This would be later known as Elbe Day.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">United States</a> and <a href=\"https://wikipedia.org/wiki/Soviet_Armed_Forces\" title=\"Soviet Armed Forces\">Soviet</a> reconnaissance troops meet in <a href=\"https://wikipedia.org/wiki/Torgau\" title=\"Torgau\">Torgau</a> and <a href=\"https://wikipedia.org/wiki/Strehla\" title=\"Strehla\">Strehla</a> along the River <a href=\"https://wikipedia.org/wiki/Elbe\" title=\"Elbe\">Elbe</a>, cutting the <i><a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a></i> of <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> in two. This would be later known as <a href=\"https://wikipedia.org/wiki/Elbe_Day\" title=\"Elbe Day\">Elbe Day</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">United States</a> and <a href=\"https://wikipedia.org/wiki/Soviet_Armed_Forces\" title=\"Soviet Armed Forces\">Soviet</a> reconnaissance troops meet in <a href=\"https://wikipedia.org/wiki/Torgau\" title=\"Torgau\">Torgau</a> and <a href=\"https://wikipedia.org/wiki/Strehla\" title=\"Strehla\">Strehla</a> along the River <a href=\"https://wikipedia.org/wiki/Elbe\" title=\"Elbe\">Elbe</a>, cutting the <i><a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a></i> of <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> in two. This would be later known as <a href=\"https://wikipedia.org/wiki/Elbe_Day\" title=\"Elbe Day\">Elbe Day</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "United States Armed Forces", "link": "https://wikipedia.org/wiki/United_States_Armed_Forces"}, {"title": "Soviet Armed Forces", "link": "https://wikipedia.org/wiki/Soviet_Armed_Forces"}, {"title": "Torgau", "link": "https://wikipedia.org/wiki/Torgau"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Strehla"}, {"title": "Elbe", "link": "https://wikipedia.org/wiki/Elbe"}, {"title": "Wehrmacht", "link": "https://wikipedia.org/wiki/Wehrmacht"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Elbe Day", "link": "https://wikipedia.org/wiki/Elbe_Day"}]}, {"year": "1945", "text": "World War II: Liberation Day (Italy): The National Liberation Committee for Northern Italy calls for a general uprising against the German occupation and the Italian Social Republic.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Liberation_Day_(Italy)\" title=\"Liberation Day (Italy)\">Liberation Day (Italy)</a>: The <a href=\"https://wikipedia.org/wiki/National_Liberation_Committee_for_Northern_Italy\" title=\"National Liberation Committee for Northern Italy\">National Liberation Committee for Northern Italy</a> calls for a general uprising against the German occupation and the <a href=\"https://wikipedia.org/wiki/Italian_Social_Republic\" title=\"Italian Social Republic\">Italian Social Republic</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Liberation_Day_(Italy)\" title=\"Liberation Day (Italy)\">Liberation Day (Italy)</a>: The <a href=\"https://wikipedia.org/wiki/National_Liberation_Committee_for_Northern_Italy\" title=\"National Liberation Committee for Northern Italy\">National Liberation Committee for Northern Italy</a> calls for a general uprising against the German occupation and the <a href=\"https://wikipedia.org/wiki/Italian_Social_Republic\" title=\"Italian Social Republic\">Italian Social Republic</a>.", "links": [{"title": "Liberation Day (Italy)", "link": "https://wikipedia.org/wiki/Liberation_Day_(Italy)"}, {"title": "National Liberation Committee for Northern Italy", "link": "https://wikipedia.org/wiki/National_Liberation_Committee_for_Northern_Italy"}, {"title": "Italian Social Republic", "link": "https://wikipedia.org/wiki/Italian_Social_Republic"}]}, {"year": "1945", "text": "United Nations Conference on International Organization: Founding negotiations for the United Nations begin in San Francisco.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/United_Nations_Conference_on_International_Organization\" title=\"United Nations Conference on International Organization\">United Nations Conference on International Organization</a>: Founding negotiations for the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> begin in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations_Conference_on_International_Organization\" title=\"United Nations Conference on International Organization\">United Nations Conference on International Organization</a>: Founding negotiations for the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> begin in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "links": [{"title": "United Nations Conference on International Organization", "link": "https://wikipedia.org/wiki/United_Nations_Conference_on_International_Organization"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}]}, {"year": "1945", "text": "World War II: The last German troops retreat from Finnish soil in Lapland, ending the Lapland War. Military actions of the Second World War end in Finland.", "html": "1945 - World War II: The last German troops retreat from Finnish soil in Lapland, ending the <a href=\"https://wikipedia.org/wiki/Lapland_War\" title=\"Lapland War\">Lapland War</a>. Military actions of the Second World War end in Finland.", "no_year_html": "World War II: The last German troops retreat from Finnish soil in Lapland, ending the <a href=\"https://wikipedia.org/wiki/Lapland_War\" title=\"Lapland War\">Lapland War</a>. Military actions of the Second World War end in Finland.", "links": [{"title": "Lapland War", "link": "https://wikipedia.org/wiki/Lapland_War"}]}, {"year": "1951", "text": "Korean War: Assaulting Chinese forces are forced to withdraw after heavy fighting with UN forces, primarily made up of Australian and Canadian troops, at the Battle of Kapyong.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: Assaulting Chinese forces are forced to withdraw after heavy fighting with UN forces, primarily made up of Australian and Canadian troops, at the <a href=\"https://wikipedia.org/wiki/Battle_of_Kapyong\" title=\"Battle of Kapyong\">Battle of Kapyong</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: Assaulting Chinese forces are forced to withdraw after heavy fighting with UN forces, primarily made up of Australian and Canadian troops, at the <a href=\"https://wikipedia.org/wiki/Battle_of_Kapyong\" title=\"Battle of Kapyong\">Battle of Kapyong</a>.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Battle of Kapyong", "link": "https://wikipedia.org/wiki/Battle_of_Kapyong"}]}, {"year": "1953", "text": "<PERSON> and <PERSON> publish \"Molecular Structure of Nucleic Acids: A Structure for Deoxyribose Nucleic Acid\" describing the double helix structure of DNA.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publish \"<a href=\"https://wikipedia.org/wiki/Molecular_Structure_of_Nucleic_Acids:_A_Structure_for_Deoxyribose_Nucleic_Acid\" title=\"Molecular Structure of Nucleic Acids: A Structure for Deoxyribose Nucleic Acid\">Molecular Structure of Nucleic Acids: A Structure for Deoxyribose Nucleic Acid</a>\" describing the <a href=\"https://wikipedia.org/wiki/Double_helix\" class=\"mw-redirect\" title=\"Double helix\">double helix</a> structure of <a href=\"https://wikipedia.org/wiki/DNA\" title=\"DNA\">DNA</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publish \"<a href=\"https://wikipedia.org/wiki/Molecular_Structure_of_Nucleic_Acids:_A_Structure_for_Deoxyribose_Nucleic_Acid\" title=\"Molecular Structure of Nucleic Acids: A Structure for Deoxyribose Nucleic Acid\">Molecular Structure of Nucleic Acids: A Structure for Deoxyribose Nucleic Acid</a>\" describing the <a href=\"https://wikipedia.org/wiki/Double_helix\" class=\"mw-redirect\" title=\"Double helix\">double helix</a> structure of <a href=\"https://wikipedia.org/wiki/DNA\" title=\"DNA\">DNA</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Molecular Structure of Nucleic Acids: A Structure for Deoxyribose Nucleic Acid", "link": "https://wikipedia.org/wiki/Molecular_Structure_of_Nucleic_Acids:_A_Structure_for_Deoxyribose_Nucleic_Acid"}, {"title": "Double helix", "link": "https://wikipedia.org/wiki/Double_helix"}, {"title": "DNA", "link": "https://wikipedia.org/wiki/DNA"}]}, {"year": "1954", "text": "The first practical solar cell is publicly demonstrated by Bell Telephone Laboratories.", "html": "1954 - The first practical <a href=\"https://wikipedia.org/wiki/Solar_cell#History\" title=\"Solar cell\">solar cell</a> is publicly demonstrated by <a href=\"https://wikipedia.org/wiki/Bell_Labs\" title=\"Bell Labs\">Bell Telephone Laboratories</a>.", "no_year_html": "The first practical <a href=\"https://wikipedia.org/wiki/Solar_cell#History\" title=\"Solar cell\">solar cell</a> is publicly demonstrated by <a href=\"https://wikipedia.org/wiki/Bell_Labs\" title=\"Bell Labs\">Bell Telephone Laboratories</a>.", "links": [{"title": "Solar cell", "link": "https://wikipedia.org/wiki/Solar_cell#History"}, {"title": "Bell Labs", "link": "https://wikipedia.org/wiki/Bell_Labs"}]}, {"year": "1959", "text": "The Saint Lawrence Seaway, linking the North American Great Lakes and the Atlantic Ocean, officially opens to shipping.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/Saint_Lawrence_Seaway\" class=\"mw-redirect\" title=\"Saint Lawrence Seaway\">Saint Lawrence Seaway</a>, linking the North American <a href=\"https://wikipedia.org/wiki/Great_Lakes\" title=\"Great Lakes\">Great Lakes</a> and the Atlantic Ocean, officially opens to shipping.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Saint_Lawrence_Seaway\" class=\"mw-redirect\" title=\"Saint Lawrence Seaway\">Saint Lawrence Seaway</a>, linking the North American <a href=\"https://wikipedia.org/wiki/Great_Lakes\" title=\"Great Lakes\">Great Lakes</a> and the Atlantic Ocean, officially opens to shipping.", "links": [{"title": "Saint Lawrence Seaway", "link": "https://wikipedia.org/wiki/Saint_Lawrence_Seaway"}, {"title": "Great Lakes", "link": "https://wikipedia.org/wiki/Great_Lakes"}]}, {"year": "1960", "text": "The United States Navy submarine USS Triton completes the first submerged circumnavigation of the globe.", "html": "1960 - The United States Navy submarine <a href=\"https://wikipedia.org/wiki/USS_Triton_(SSRN-586)\" title=\"USS Triton (SSRN-586)\">USS <i>Triton</i></a> completes the <a href=\"https://wikipedia.org/wiki/Operation_Sandblast\" title=\"Operation Sandblast\">first submerged circumnavigation of the globe</a>.", "no_year_html": "The United States Navy submarine <a href=\"https://wikipedia.org/wiki/USS_Triton_(SSRN-586)\" title=\"USS Triton (SSRN-586)\">USS <i>Triton</i></a> completes the <a href=\"https://wikipedia.org/wiki/Operation_Sandblast\" title=\"Operation Sandblast\">first submerged circumnavigation of the globe</a>.", "links": [{"title": "USS Triton (SSRN-586)", "link": "https://wikipedia.org/wiki/USS_Triton_(SSRN-586)"}, {"title": "Operation Sandblast", "link": "https://wikipedia.org/wiki/Operation_Sandblast"}]}, {"year": "1961", "text": "<PERSON> is granted a patent for an integrated circuit.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is granted a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for an <a href=\"https://wikipedia.org/wiki/Integrated_circuit\" title=\"Integrated circuit\">integrated circuit</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is granted a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for an <a href=\"https://wikipedia.org/wiki/Integrated_circuit\" title=\"Integrated circuit\">integrated circuit</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Integrated circuit", "link": "https://wikipedia.org/wiki/Integrated_circuit"}]}, {"year": "1972", "text": "Vietnam War: Nguyen <PERSON>e Offensive: The North Vietnamese 320th Division forces 5,000 South Vietnamese troops to retreat and traps about 2,500 others northwest of Kontum.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Nguyen_Hue_Offensive\" class=\"mw-redirect\" title=\"Nguyen Hue Offensive\">Nguyen Hue Offensive</a>: The <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> 320th Division forces 5,000 <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> troops to retreat and traps about 2,500 others northwest of <a href=\"https://wikipedia.org/wiki/Kontum\" class=\"mw-redirect\" title=\"Kontum\">Kontum</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Nguyen_Hue_Offensive\" class=\"mw-redirect\" title=\"Nguyen Hue Offensive\">Nguyen Hue Offensive</a>: The <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> 320th Division forces 5,000 <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> troops to retreat and traps about 2,500 others northwest of <a href=\"https://wikipedia.org/wiki/Kontum\" class=\"mw-redirect\" title=\"Kontum\">Kontum</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON><PERSON><PERSON> Offensive", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Offensive"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kontum"}]}, {"year": "1974", "text": "Carnation Revolution: A leftist military coup in Portugal overthrows the authoritarian-conservative <PERSON><PERSON><PERSON> regime.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Carnation_Revolution\" title=\"Carnation Revolution\">Carnation Revolution</a>: A leftist military <a href=\"https://wikipedia.org/wiki/Coup\" class=\"mw-redirect\" title=\"Coup\">coup</a> in Portugal overthrows the authoritarian-conservative <i><a href=\"https://wikipedia.org/wiki/Estado_Novo_(Portugal)\" title=\"Estado Novo (Portugal)\">Estado Novo</a></i> regime.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carnation_Revolution\" title=\"Carnation Revolution\">Carnation Revolution</a>: A leftist military <a href=\"https://wikipedia.org/wiki/Coup\" class=\"mw-redirect\" title=\"Coup\">coup</a> in Portugal overthrows the authoritarian-conservative <i><a href=\"https://wikipedia.org/wiki/Estado_Novo_(Portugal)\" title=\"Estado Novo (Portugal)\">Estado Novo</a></i> regime.", "links": [{"title": "Carnation Revolution", "link": "https://wikipedia.org/wiki/Carnation_Revolution"}, {"title": "Coup", "link": "https://wikipedia.org/wiki/Coup"}, {"title": "<PERSON><PERSON><PERSON> (Portugal)", "link": "https://wikipedia.org/wiki/Estado_Novo_(Portugal)"}]}, {"year": "1980", "text": "One hundred forty-six people are killed when Dan-Air Flight 1008 crashes near Los Rodeos Airport in Tenerife, Canary Islands.", "html": "1980 - One hundred forty-six people are killed when <a href=\"https://wikipedia.org/wiki/Dan-Air_Flight_1008\" title=\"Dan-Air Flight 1008\">Dan-Air Flight 1008</a> crashes near <a href=\"https://wikipedia.org/wiki/Tenerife_North%E2%80%93Ciudad_de_La_Laguna_Airport\" title=\"Tenerife North-Ciudad de La Laguna Airport\">Los Rodeos Airport</a> in <a href=\"https://wikipedia.org/wiki/Tenerife\" title=\"Tenerife\">Tenerife</a>, <a href=\"https://wikipedia.org/wiki/Canary_Islands\" title=\"Canary Islands\">Canary Islands</a>.", "no_year_html": "One hundred forty-six people are killed when <a href=\"https://wikipedia.org/wiki/Dan-Air_Flight_1008\" title=\"Dan-Air Flight 1008\">Dan-Air Flight 1008</a> crashes near <a href=\"https://wikipedia.org/wiki/Tenerife_North%E2%80%93Ciudad_de_La_Laguna_Airport\" title=\"Tenerife North-Ciudad de La Laguna Airport\">Los Rodeos Airport</a> in <a href=\"https://wikipedia.org/wiki/Tenerife\" title=\"Tenerife\">Tenerife</a>, <a href=\"https://wikipedia.org/wiki/Canary_Islands\" title=\"Canary Islands\">Canary Islands</a>.", "links": [{"title": "Dan-Air Flight 1008", "link": "https://wikipedia.org/wiki/Dan-Air_Flight_1008"}, {"title": "Tenerife North-Ciudad de La Laguna Airport", "link": "https://wikipedia.org/wiki/Tenerife_North%E2%80%93Ciudad_de_La_Laguna_Airport"}, {"title": "Tenerife", "link": "https://wikipedia.org/wiki/Tenerife"}, {"title": "Canary Islands", "link": "https://wikipedia.org/wiki/Canary_Islands"}]}, {"year": "1981", "text": "More than 100 workers are exposed to radiation during repairs of at the Tsuruga Nuclear Power Plant in Japan.", "html": "1981 - More than 100 workers are exposed to radiation during repairs of at the <a href=\"https://wikipedia.org/wiki/Tsuruga_Nuclear_Power_Plant\" title=\"Tsuruga Nuclear Power Plant\">Tsuruga Nuclear Power Plant</a> in Japan.", "no_year_html": "More than 100 workers are exposed to radiation during repairs of at the <a href=\"https://wikipedia.org/wiki/Tsuruga_Nuclear_Power_Plant\" title=\"Tsuruga Nuclear Power Plant\">Tsuruga Nuclear Power Plant</a> in Japan.", "links": [{"title": "Tsuruga Nuclear Power Plant", "link": "https://wikipedia.org/wiki/Tsuruga_Nuclear_Power_Plant"}]}, {"year": "1982", "text": "Israel completes its withdrawal from the Sinai Peninsula per the Camp David Accords.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> completes its withdrawal from the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a> per the <a href=\"https://wikipedia.org/wiki/Camp_David_Accords\" title=\"Camp David Accords\">Camp David Accords</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> completes its withdrawal from the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a> per the <a href=\"https://wikipedia.org/wiki/Camp_David_Accords\" title=\"Camp David Accords\">Camp David Accords</a>.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Sinai Peninsula", "link": "https://wikipedia.org/wiki/Sinai_Peninsula"}, {"title": "Camp David Accords", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "Cold War: American schoolgirl <PERSON> is invited to visit the Soviet Union by its leader <PERSON> after he read her letter in which she expressed fears about nuclear war.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: American schoolgirl <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is invited to visit the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> by its leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> after he read her letter in which she expressed fears about <a href=\"https://wikipedia.org/wiki/Nuclear_warfare\" title=\"Nuclear warfare\">nuclear war</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: American schoolgirl <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is invited to visit the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> by its leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> after he read her letter in which she expressed fears about <a href=\"https://wikipedia.org/wiki/Nuclear_warfare\" title=\"Nuclear warfare\">nuclear war</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nuclear warfare", "link": "https://wikipedia.org/wiki/Nuclear_warfare"}]}, {"year": "1983", "text": "Pioneer 10 travels beyond Pluto's orbit.", "html": "1983 - <i><a href=\"https://wikipedia.org/wiki/Pioneer_10\" title=\"Pioneer 10\">Pioneer 10</a></i> travels beyond <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a>'s orbit.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Pioneer_10\" title=\"Pioneer 10\">Pioneer 10</a></i> travels beyond <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a>'s orbit.", "links": [{"title": "Pioneer 10", "link": "https://wikipedia.org/wiki/Pioneer_10"}, {"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}]}, {"year": "1990", "text": "<PERSON><PERSON> takes office as the President of Nicaragua, the first woman to hold the position.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> takes office as the <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a>, the first woman to hold the position.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> takes office as the <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a>, the first woman to hold the position.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Nicaragua", "link": "https://wikipedia.org/wiki/President_of_Nicaragua"}]}, {"year": "2001", "text": "President <PERSON> pledges U.S. military support in the event of a Chinese attack on Taiwan.", "html": "2001 - President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> pledges U.S. military support in the event of a Chinese attack on Taiwan.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> pledges U.S. military support in the event of a Chinese attack on Taiwan.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "The March for Women's Lives brings over 1 million protesters, mostly pro-choice, to Washington D.C. to protest the Partial-Birth Abortion Ban Act of 2003, and other restrictions on abortion.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/March_for_Women%27s_Lives\" class=\"mw-redirect\" title=\"March for Women's Lives\">March for Women's Lives</a> brings over 1 million protesters, mostly pro-choice, to Washington D.C. to protest the <a href=\"https://wikipedia.org/wiki/Partial-Birth_Abortion_Ban_Act\" title=\"Partial-Birth Abortion Ban Act\">Partial-Birth Abortion Ban Act</a> of 2003, and other restrictions on <a href=\"https://wikipedia.org/wiki/Abortion\" title=\"Abortion\">abortion</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/March_for_Women%27s_Lives\" class=\"mw-redirect\" title=\"March for Women's Lives\">March for Women's Lives</a> brings over 1 million protesters, mostly pro-choice, to Washington D.C. to protest the <a href=\"https://wikipedia.org/wiki/Partial-Birth_Abortion_Ban_Act\" title=\"Partial-Birth Abortion Ban Act\">Partial-Birth Abortion Ban Act</a> of 2003, and other restrictions on <a href=\"https://wikipedia.org/wiki/Abortion\" title=\"Abortion\">abortion</a>.", "links": [{"title": "March for Women's Lives", "link": "https://wikipedia.org/wiki/March_for_Women%27s_Lives"}, {"title": "Partial-Birth Abortion Ban Act", "link": "https://wikipedia.org/wiki/Partial-Birth_Abortion_Ban_Act"}, {"title": "Abortion", "link": "https://wikipedia.org/wiki/Abortion"}]}, {"year": "2005", "text": "The final piece of the Obelisk of Axum is returned to Ethiopia after being stolen by the invading Italian army in 1937.", "html": "2005 - The final piece of the <a href=\"https://wikipedia.org/wiki/Obelisk_of_Axum\" title=\"Obelisk of Axum\">Obelisk of Axum</a> is returned to <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> after being stolen by the invading Italian army in <a href=\"https://wikipedia.org/wiki/1937\" title=\"1937\">1937</a>.", "no_year_html": "The final piece of the <a href=\"https://wikipedia.org/wiki/Obelisk_of_Axum\" title=\"Obelisk of Axum\">Obelisk of Axum</a> is returned to <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> after being stolen by the invading Italian army in <a href=\"https://wikipedia.org/wiki/1937\" title=\"1937\">1937</a>.", "links": [{"title": "Obelisk of Axum", "link": "https://wikipedia.org/wiki/Obelisk_of_Axum"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "1937", "link": "https://wikipedia.org/wiki/1937"}]}, {"year": "2005", "text": "A seven-car commuter train derails and crashes into an apartment building near Amagasaki Station in Japan, killing 107, including the driver.", "html": "2005 - A seven-car commuter train <a href=\"https://wikipedia.org/wiki/Amagasaki_derailment\" title=\"Amagasaki derailment\">derails and crashes into an apartment building</a> near <a href=\"https://wikipedia.org/wiki/Amagasaki_Station_(JR_West)\" title=\"Amagasaki Station (JR West)\">Amagasaki Station</a> in Japan, killing 107, including the driver.", "no_year_html": "A seven-car commuter train <a href=\"https://wikipedia.org/wiki/Amagasaki_derailment\" title=\"Amagasaki derailment\">derails and crashes into an apartment building</a> near <a href=\"https://wikipedia.org/wiki/Amagasaki_Station_(JR_West)\" title=\"Amagasaki Station (JR West)\">Amagasaki Station</a> in Japan, killing 107, including the driver.", "links": [{"title": "<PERSON><PERSON><PERSON> derailment", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_derailment"}, {"title": "Amagasaki Station (JR West)", "link": "https://wikipedia.org/wiki/Amagasaki_Station_(JR_West)"}]}, {"year": "2005", "text": "Bulgaria and Romania sign the Treaty of Accession 2005 to join the European Union.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a> and <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Accession_2005\" title=\"Treaty of Accession 2005\">Treaty of Accession 2005</a> to join the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a> and <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Accession_2005\" title=\"Treaty of Accession 2005\">Treaty of Accession 2005</a> to join the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "links": [{"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}, {"title": "Treaty of Accession 2005", "link": "https://wikipedia.org/wiki/Treaty_of_Accession_2005"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "2007", "text": "<PERSON>'s funeral: The first to be sanctioned by the Russian Orthodox Church for a head of state since the funeral of Emperor <PERSON> in 1894.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>\" title=\"Death and state funeral of <PERSON>\"><PERSON>'s funeral</a>: The first to be sanctioned by the <a href=\"https://wikipedia.org/wiki/Russian_Orthodox_Church\" title=\"Russian Orthodox Church\">Russian Orthodox Church</a> for a head of state since the funeral of Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> III</a> in 1894.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>\" title=\"Death and state funeral of <PERSON>\"><PERSON>'s funeral</a>: The first to be sanctioned by the <a href=\"https://wikipedia.org/wiki/Russian_Orthodox_Church\" title=\"Russian Orthodox Church\">Russian Orthodox Church</a> for a head of state since the funeral of Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> III</a> in 1894.", "links": [{"title": "Death and state funeral of <PERSON>", "link": "https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON>"}, {"title": "Russian Orthodox Church", "link": "https://wikipedia.org/wiki/Russian_Orthodox_Church"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "2014", "text": "The Flint water crisis begins when officials at Flint, Michigan switch the city's water supply to the Flint River, leading to lead and bacteria contamination.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/Flint_water_crisis\" title=\"Flint water crisis\">Flint water crisis</a> begins when officials at <a href=\"https://wikipedia.org/wiki/Flint,_Michigan\" title=\"Flint, Michigan\">Flint, Michigan</a> switch the city's water supply to the <a href=\"https://wikipedia.org/wiki/Flint_River_(Michigan)\" title=\"Flint River (Michigan)\">Flint River</a>, leading to <a href=\"https://wikipedia.org/wiki/Lead_poisoning\" title=\"Lead poisoning\">lead</a> and <a href=\"https://wikipedia.org/wiki/Legionnaires%27_disease\" title=\"Legionnaires' disease\">bacteria contamination</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Flint_water_crisis\" title=\"Flint water crisis\">Flint water crisis</a> begins when officials at <a href=\"https://wikipedia.org/wiki/Flint,_Michigan\" title=\"Flint, Michigan\">Flint, Michigan</a> switch the city's water supply to the <a href=\"https://wikipedia.org/wiki/Flint_River_(Michigan)\" title=\"Flint River (Michigan)\">Flint River</a>, leading to <a href=\"https://wikipedia.org/wiki/Lead_poisoning\" title=\"Lead poisoning\">lead</a> and <a href=\"https://wikipedia.org/wiki/Legionnaires%27_disease\" title=\"Legionnaires' disease\">bacteria contamination</a>.", "links": [{"title": "Flint water crisis", "link": "https://wikipedia.org/wiki/Flint_water_crisis"}, {"title": "Flint, Michigan", "link": "https://wikipedia.org/wiki/Flint,_Michigan"}, {"title": "Flint River (Michigan)", "link": "https://wikipedia.org/wiki/Flint_River_(Michigan)"}, {"title": "Lead poisoning", "link": "https://wikipedia.org/wiki/Lead_poisoning"}, {"title": "Legionnaires' disease", "link": "https://wikipedia.org/wiki/Legionnaires%27_disease"}]}, {"year": "2015", "text": "At least 8,962 are killed in Nepal after a massive 7.8 magnitude earthquake strikes Nepal.", "html": "2015 - At least 8,962 are killed in Nepal after <a href=\"https://wikipedia.org/wiki/April_2015_Nepal_earthquake\" title=\"April 2015 Nepal earthquake\">a massive 7.8 magnitude earthquake strikes Nepal</a>.", "no_year_html": "At least 8,962 are killed in Nepal after <a href=\"https://wikipedia.org/wiki/April_2015_Nepal_earthquake\" title=\"April 2015 Nepal earthquake\">a massive 7.8 magnitude earthquake strikes Nepal</a>.", "links": [{"title": "April 2015 Nepal earthquake", "link": "https://wikipedia.org/wiki/April_2015_Nepal_earthquake"}]}], "Births": [{"year": "1214", "text": "<PERSON> of France (d. 1270)", "html": "1214 - <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> (d. 1270)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> (d. 1270)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}]}, {"year": "1228", "text": "<PERSON> of Germany (d. 1254)", "html": "1228 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Germany\" title=\"Conrad IV of Germany\"><PERSON> IV of Germany</a> (d. 1254)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Germany\" title=\"Conrad IV of Germany\"><PERSON> IV of Germany</a> (d. 1254)", "links": [{"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/Conrad_IV_of_Germany"}]}, {"year": "1284", "text": "<PERSON> of England (d. 1327)", "html": "1284 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> (d. 1327)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> (d. 1327)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1287", "text": "<PERSON>, 1st Earl of March, English politician, Lord Lieutenant of Ireland (d. 1330)", "html": "1287 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_March\" title=\"<PERSON>, 1st Earl of March\"><PERSON>, 1st Earl of March</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1330)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_March\" title=\"<PERSON>, 1st Earl of <PERSON>\"><PERSON>, 1st Earl of March</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1330)", "links": [{"title": "<PERSON>, 1st Earl of March", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_<PERSON>"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1502", "text": "<PERSON>, German theologian and academic (d. 1574)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Georg Major\"><PERSON></a>, German theologian and academic (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Georg Major\"><PERSON></a>, German theologian and academic (d. 1574)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1529", "text": "<PERSON>, Italian philosopher and scientist (d. 1597)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher and scientist (d. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher and scientist (d. 1597)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_Patrizi"}]}, {"year": "1599", "text": "<PERSON>, English general and politician, Lord Pro<PERSON>tor of Great Britain (d. 1658)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Protector\" title=\"Lord Protector\">Lord Protector of Great Britain</a> (d. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Protector\" title=\"Lord Protector\">Lord Protector of Great Britain</a> (d. 1658)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Pro<PERSON>tor", "link": "https://wikipedia.org/wiki/<PERSON>_Protector"}]}, {"year": "1621", "text": "<PERSON>, 1st Earl of Orrery, English soldier and politician (d. 1679)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Orrery\" title=\"<PERSON>, 1st Earl of Orrery\"><PERSON>, 1st Earl of Orrery</a>, English soldier and politician (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Orrery\" title=\"<PERSON>, 1st Earl of Orrery\"><PERSON>, 1st Earl of Orrery</a>, English soldier and politician (d. 1679)", "links": [{"title": "<PERSON>, 1st Earl of Orrery", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Orrery"}]}, {"year": "1666", "text": "<PERSON>, German organist and composer (d. 1727)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1727)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON>, 3rd Earl of Burlington, English architect and politician, Lord High Treasurer of Ireland (d. 1753)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Burlington\" title=\"<PERSON>, 3rd Earl of Burlington\"><PERSON>, 3rd Earl of Burlington</a>, English architect and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer_of_Ireland\" title=\"Lord High Treasurer of Ireland\">Lord High Treasurer of Ireland</a> (d. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Burlington\" title=\"<PERSON>, 3rd Earl of Burlington\"><PERSON>, 3rd Earl of Burlington</a>, English architect and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer_of_Ireland\" title=\"Lord High Treasurer of Ireland\">Lord High Treasurer of Ireland</a> (d. 1753)", "links": [{"title": "<PERSON>, 3rd Earl of Burlington", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Burlington"}, {"title": "Lord High Treasurer of Ireland", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer_of_Ireland"}]}, {"year": "1710", "text": "<PERSON>, Scottish astronomer and author (d. 1776)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_astronomer)\" title=\"<PERSON> (Scottish astronomer)\"><PERSON></a>, Scottish astronomer and author (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_astronomer)\" title=\"<PERSON> (Scottish astronomer)\"><PERSON></a>, Scottish astronomer and author (d. 1776)", "links": [{"title": "<PERSON> (Scottish astronomer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_astronomer)"}]}, {"year": "1723", "text": "<PERSON>, Italian composer (d. 1797)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1725", "text": "<PERSON>, 1st Viscount <PERSON>, English admiral and politician (d. 1786)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English admiral and politician (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English admiral and politician (d. 1786)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1767", "text": "<PERSON>, French general (d. 1847)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, Norwegian philologist and academic (d. 1850)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian philologist and academic (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian philologist and academic (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_<PERSON>drup"}]}, {"year": "1776", "text": "<PERSON> <PERSON>, Duchess of Gloucester and Edinburgh (d. 1857)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Gloucester_and_Edinburgh\" title=\"Princess <PERSON>, Duchess of Gloucester and Edinburgh\">Princess <PERSON>, Duchess of Gloucester and Edinburgh</a> (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Gloucester_and_Edinburgh\" title=\"Princess <PERSON>, Duchess of Gloucester and Edinburgh\">Princess <PERSON>, Duchess of Gloucester and Edinburgh</a> (d. 1857)", "links": [{"title": "Princess <PERSON>, Duchess of Gloucester and Edinburgh", "link": "https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Gloucester_and_Edinburgh"}]}, {"year": "1843", "text": "Princess <PERSON> of the United Kingdom (d. 1878)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/Princess_Alice_of_the_United_Kingdom\" title=\"Princess Alice of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Alice_of_the_United_Kingdom\" title=\"Princess Alice of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (d. 1878)", "links": [{"title": "Princess <PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/Princess_Alice_of_the_United_Kingdom"}]}, {"year": "1849", "text": "<PERSON>, German mathematician and academic (d. 1925)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON><PERSON>, German composer and educator (d. 1927)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German composer and educator (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German composer and educator (d. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON>, Spanish author, critic, and academic (d. 1901)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish author, critic, and academic (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish author, critic, and academic (d. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, American engineer and inventor (d. 1940)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and inventor (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and inventor (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, 1st Viscount <PERSON> of Fallodon, English ornithologist and politician, Secretary of State for Foreign and Commonwealth Affairs (d. 1933)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>_of_Fallodon\" title=\"<PERSON>, 1st Viscount <PERSON> of Fallodon\"><PERSON>, 1st Viscount <PERSON> of Fallodon</a>, English ornithologist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a> (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>_of_Fallodon\" title=\"<PERSON>, 1st Viscount <PERSON> of Fallodon\"><PERSON>, 1st Viscount <PERSON> of Fallodon</a>, English ornithologist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a> (d. 1933)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON> of Fallodon", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Grey_of_Fallodon"}, {"title": "Secretary of State for Foreign and Commonwealth Affairs", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs"}]}, {"year": "1868", "text": "<PERSON>, American pilot and engineer (d. 1910)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and engineer (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and engineer (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON>, French-English sailor (d. 1926)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-English sailor (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-English sailor (d. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON> <PERSON><PERSON>, English cricketer, footballer, educator, and politician (d. 1956)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Fry\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer, footballer, educator, and politician (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Fry\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer, footballer, educator, and politician (d. 1956)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, English poet, short story writer, and novelist (d. 1956)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, short story writer, and novelist (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, short story writer, and novelist (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, American author, creator of the <PERSON> Wig<PERSON><PERSON> series of children's stories (d. 1962)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author, creator of the Uncle <PERSON> series of children's stories (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author, creator of the <PERSON> Wig<PERSON> series of children's stories (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian businessman and inventor, developed <PERSON><PERSON>'s law, Nobel Prize laureate (d. 1937)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian businessman and inventor, developed <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_law\" class=\"mw-redirect\" title=\"<PERSON><PERSON>'s law\"><PERSON><PERSON>'s law</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian businessman and inventor, developed <a href=\"https://wikipedia.org/wiki/<PERSON>ni%27s_law\" class=\"mw-redirect\" title=\"<PERSON><PERSON>'s law\"><PERSON><PERSON>'s law</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>'s law", "link": "https://wikipedia.org/wiki/<PERSON>ni%27s_law"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1874", "text": "<PERSON>, English-Canadian race walker (d. 1937)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian race walker (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian race walker (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Canadian publisher, lawyer, and politician (d. 1958)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian publisher, lawyer, and politician (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian publisher, lawyer, and politician (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American gymnast and triathlete (d. 1946)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and triathlete (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and triathlete (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Scottish golfer (d. 1976)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Scottish golfer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Scottish golfer (d. 1976)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Beninese lawyer and critic (d. 1936)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tovalou_Hou%C3%A9nou\" title=\"<PERSON><PERSON> Tovalou <PERSON>ué<PERSON>u\"><PERSON><PERSON></a>, Beninese lawyer and critic (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tovalou_Hou%C3%A9nou\" title=\"<PERSON><PERSON>val<PERSON>\"><PERSON><PERSON></a>, Beninese lawyer and critic (d. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Tovalou_Hou%C3%A9nou"}]}, {"year": "1892", "text": "<PERSON>, American author (d. 1980)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American baseball player, coach, and manager (d. 1977)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Princess <PERSON> and Countess of Harewood (d. 1965)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_Royal_and_Countess_of_Harewood\" title=\"<PERSON>, Princess <PERSON> and Countess of Harewood\"><PERSON>, Princess <PERSON> and Countess of Harewood</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_Royal_and_Countess_of_Harewood\" title=\"<PERSON>, Princess <PERSON> and Countess of Harewood\"><PERSON>, Princess <PERSON> and Countess of Harewood</a> (d. 1965)", "links": [{"title": "<PERSON>, Princess <PERSON> and Countess of Harewood", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_Royal_and_Countess_of_Harewood"}]}, {"year": "1900", "text": "<PERSON><PERSON>, English politician and diplomat, Secretary-General of the United Nations (d. 1996)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Jebb\"><PERSON><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}]}, {"year": "1900", "text": "<PERSON>, Austrian-Swiss-American physicist and academic, Nobel Prize laureate (d. 1958)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1902", "text": "<PERSON>, German psychiatrist and academic (d. 1964)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and academic (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and academic (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American actress (d. 1984)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Russian mathematician and academic (d. 1987)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and academic (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and academic (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, New Zealand rugby player and referee (d. 1986)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%93pia\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and referee (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%93pia\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and referee (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_N%C4%93pia"}]}, {"year": "1906", "text": "<PERSON>, member of the Budapest Aid and Rescue Committee (d. 1964)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, member of the Budapest Aid and Rescue Committee (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brand\"><PERSON></a>, member of the Budapest Aid and Rescue Committee (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American colonel and Associate Justice of the United States Supreme Court (d. 1997)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American colonel and <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_United_States_Supreme_Court\" class=\"mw-redirect\" title=\"Associate Justice of the United States Supreme Court\">Associate Justice of the United States Supreme Court</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American colonel and <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_United_States_Supreme_Court\" class=\"mw-redirect\" title=\"Associate Justice of the United States Supreme Court\">Associate Justice of the United States Supreme Court</a> (d. 1997)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Associate Justice of the United States Supreme Court", "link": "https://wikipedia.org/wiki/Associate_Justice_of_the_United_States_Supreme_Court"}]}, {"year": "1908", "text": "<PERSON>, American journalist (d. 1965)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American architect, designed the Transamerica Pyramid (d. 1985)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Transamerica_Pyramid\" title=\"Transamerica Pyramid\">Transamerica Pyramid</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Transamerica_Pyramid\" title=\"Transamerica Pyramid\">Transamerica Pyramid</a> (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Transamerica Pyramid", "link": "https://wikipedia.org/wiki/Transamerica_Pyramid"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>,  New Zealand interpreter, military leader, politician, and murderer (d. 1976)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Arapeta_A<PERSON>e\" title=\"Arapeta Awatere\"><PERSON><PERSON><PERSON></a>, New Zealand interpreter, military leader, politician, and murderer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arapeta_<PERSON>\" title=\"Arapet<PERSON>watere\"><PERSON><PERSON><PERSON></a>, New Zealand interpreter, military leader, politician, and murderer (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arapeta_<PERSON>e"}]}, {"year": "1911", "text": "<PERSON>, Cuban baseball player and coach (d. 2014)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American gymnast (d. 1997)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American saxophonist (d. 1965)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Greek captain (d. 1944)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek captain (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek captain (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American author and academic (d. 1948)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American author and academic (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American author and academic (d. 1948)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1915", "text": "<PERSON><PERSON>, American journalist and author (d. 1978)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American golfer (d. 1994)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jerry Barber\"><PERSON></a>, American golfer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American singer (d. 1996)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, French racing driver (d. 2003)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, French racing driver (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, French racing driver (d. 2003)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1918", "text": "<PERSON>, South African-born English actor and singer (d. 2005)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born English actor and singer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born English actor and singer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, French-American astronomer and academic (d. 1995)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_de_Vaucouleurs\" title=\"<PERSON><PERSON> Vaucouleurs\"><PERSON><PERSON></a>, French-American astronomer and academic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_de_Vaucouleurs\" title=\"<PERSON><PERSON> Vaucouleurs\"><PERSON><PERSON></a>, French-American astronomer and academic (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_de_Vaucouleurs"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Swedish-American soprano and actress (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American soprano and actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American soprano and actress (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Norwegian speed skater (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian speed skater (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian speed skater (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Dutch painter and sculptor (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter and sculptor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter and sculptor (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English astronomer and academic", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Canadian ballerina (d. 2006)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, Canadian ballerina (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, Canadian ballerina (d. 2006)", "links": [{"title": "<PERSON> (dancer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)"}]}, {"year": "1923", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (d. 1992)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert King\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert King\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Swedish race walker (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (racewalker)\"><PERSON><PERSON><PERSON></a>, Swedish race walker (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (racewalker)\"><PERSON><PERSON><PERSON></a>, Swedish race walker (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON> (racewalker)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(racewalker)"}]}, {"year": "1924", "text": "<PERSON>, Italian pianist, composer, director, and playwright (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist, composer, director, and playwright (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mann<PERSON>\"><PERSON></a>, Italian pianist, composer, director, and playwright (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Brazilian singer-songwriter and zoologist (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter and zoologist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter and zoologist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, <PERSON>, English trade union leader and businessman", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English trade union leader and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English trade union leader and businessman", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, German comedian and journalist (d. 1986)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German comedian and journalist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German comedian and journalist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian academic and politician (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian academic and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian academic and politician (d. 2018)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(politician)"}]}, {"year": "1926", "text": "<PERSON>, American author and illustrator (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Austrian politician (d. 2008)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Argentine actress (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Spanish author (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Cor%C3%ADn_Tellado\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cor%C3%ADn_Tellado\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish author (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cor%C3%ADn_Tellado"}]}, {"year": "1927", "text": "<PERSON>, French author and illustrator (d. 2020)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American-Italian painter and sculptor (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Twombly\" title=\"<PERSON> Twombly\"><PERSON></a>, American-Italian painter and sculptor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Twomb<PERSON>\" title=\"<PERSON> Twombly\"><PERSON></a>, American-Italian painter and sculptor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Twombly"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, New Zealand long jumper, shot putter, and discus thrower (d. 2019)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand long jumper, shot putter, and discus thrower (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand long jumper, shot putter, and discus thrower (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actor, director, and screenwriter (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English admiral and surgeon (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and surgeon (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and surgeon (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, German lawyer and politician, Mayor of Hamburg (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Hamburg\" title=\"List of mayors of Hamburg\">Mayor of Hamburg</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Hamburg\" title=\"List of mayors of Hamburg\">Mayor of Hamburg</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Hamburg", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Hamburg"}]}, {"year": "1931", "text": "<PERSON>, Russian mathematician and physicist (d. 1980)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and physicist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and physicist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English painter and author (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and author (d. 2017)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1932", "text": "<PERSON>, Russian astrophysicist (d. 2019)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian astrophysicist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian astrophysicist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, African-American basketball player and minister (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lemon\" title=\"Meadowlar<PERSON> Lemon\"><PERSON><PERSON><PERSON></a>, African-American basketball player and minister (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lemon\" title=\"Meadowlar<PERSON> Lemon\"><PERSON><PERSON><PERSON></a>, African-American basketball player and minister (d. 2015)", "links": [{"title": "Meadowlark Lemon", "link": "https://wikipedia.org/wiki/Meadowlark_Lemon"}]}, {"year": "1932", "text": "<PERSON>, Romanian discus thrower and politician (d. 1998)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian discus thrower and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian discus thrower and politician (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American songwriter and producer (d. 2011)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American baseball player (d. 1992)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Northern Irish footballer and manager", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American pole vaulter (d. 1960)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Dutch footballer (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>jer<PERSON>at\" title=\"<PERSON><PERSON><PERSON> Kreijer<PERSON>at\"><PERSON><PERSON><PERSON></a>, Dutch footballer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Kreijer<PERSON>at\"><PERSON><PERSON><PERSON></a>, Dutch footballer (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Surinamese banker and politician, 1st Prime Minister of the Republic of Suriname (d. 2000)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Surinamese banker and politician, 1st Prime Minister of the Republic of Suriname (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Surinamese banker and politician, 1st Prime Minister of the Republic of Suriname (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American aerodynamicist and engineer (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aerodynamicist and engineer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aerodynamicist and engineer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Dutch painter and graphic designer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter and graphic designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter and graphic designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer and manager (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Tarcisio_Burgnich\" title=\"Tarc<PERSON>o Burgnich\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tarcisio_Burgnich\" title=\"Tarcisio Burgnich\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tarcisio_<PERSON>urg<PERSON>h"}]}, {"year": "1939", "text": "<PERSON>-<PERSON>, English academic and diplomat", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Baron <PERSON>, English historian and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English historian and academic", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English academic and British diplomat", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and British diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and British diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American actor and director", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pacino\"><PERSON></a>, American actor and director", "links": [{"title": "Al Pacino", "link": "https://wikipedia.org/wiki/Al_Pacino"}]}, {"year": "1941", "text": "<PERSON>, French actor, director, producer, and screenwriter (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Tavernier"}]}, {"year": "1941", "text": "<PERSON>, Australian librarian (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(librarian)\" title=\"<PERSON> (librarian)\"><PERSON></a>, Australian librarian (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(librarian)\" title=\"<PERSON> (librarian)\"><PERSON></a>, Australian librarian (d. 2024)", "links": [{"title": "<PERSON> (librarian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(librarian)"}]}, {"year": "1942", "text": "<PERSON>, American lawyer and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English singer-songwriter and actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English dancer (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dancer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dancer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English economist and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English geneticist and cancer researcher", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist and cancer researcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist and cancer researcher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American bass player <PERSON><PERSON>, songwriter, and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cook\"><PERSON><PERSON></a>, American bass player <a href=\"https://wikipedia.org/wiki/Creedence_Clearwater_Revival\" title=\"Creedence Clearwater Revival\">Creedence Clearwater Revival</a>, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cook\"><PERSON><PERSON></a>, American bass player <a href=\"https://wikipedia.org/wiki/Creedence_Clearwater_Revival\" title=\"Creedence Clearwater Revival\">Creedence Clearwater Revival</a>, songwriter, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Creedence Clearwater Revival", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Clearwater_Revival"}]}, {"year": "1945", "text": "<PERSON>, American theorist and author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish singer-songwriter and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B6rn_U<PERSON><PERSON>eus"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Talia_Shire\" title=\"Talia Shire\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Talia_Shire\" title=\"Talia Shire\"><PERSON><PERSON></a>, American actress", "links": [{"title": "Talia Shire", "link": "https://wikipedia.org/wiki/Talia_Shire"}]}, {"year": "1946", "text": "<PERSON>, Irish lawyer and politician, Attorney General of Ireland (d. 2018)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Ireland\" title=\"Attorney General of Ireland\">Attorney General of Ireland</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Ireland\" title=\"Attorney General of Ireland\">Attorney General of Ireland</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Attorney General of Ireland", "link": "https://wikipedia.org/wiki/Attorney_General_of_Ireland"}]}, {"year": "1946", "text": "<PERSON>, Russian colonel, lawyer, and politician (d. 2022)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, lawyer, and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, lawyer, and politician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Dutch footballer and manager (d. 2016)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian singer and drug dealer (d. 2020)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and drug dealer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and drug dealer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English cricketer and sportscaster", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Taiwanese politician, 39th Premier of the Republic of China", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Yu_Shyi-kun\" class=\"mw-redirect\" title=\"<PERSON> Shy<PERSON>-kun\"><PERSON></a>, Taiwanese politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_the_Republic_of_China\" title=\"Premier of the Republic of China\">Premier of the Republic of China</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Shyi-kun\" class=\"mw-redirect\" title=\"Yu Shyi-kun\"><PERSON></a>, Taiwanese politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_the_Republic_of_China\" title=\"Premier of the Republic of China\">Premier of the Republic of China</a>", "links": [{"title": "<PERSON>kun", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-kun"}, {"title": "Premier of the Republic of China", "link": "https://wikipedia.org/wiki/Premier_of_the_Republic_of_China"}]}, {"year": "1949", "text": "<PERSON>, Argentinian footballer and race car driver", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vicente_Pern%C3%ADa"}]}, {"year": "1949", "text": "<PERSON>, French economist, lawyer, and politician, French Minister of Finance", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Economy,_Finances_and_Industry_(France)\" class=\"mw-redirect\" title=\"Minister of the Economy, Finances and Industry (France)\">French Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Economy,_Finances_and_Industry_(France)\" class=\"mw-redirect\" title=\"Minister of the Economy, Finances and Industry (France)\">French Minister of Finance</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of the Economy, Finances and Industry (France)", "link": "https://wikipedia.org/wiki/Minister_of_the_Economy,_Finances_and_Industry_(France)"}]}, {"year": "1949", "text": "<PERSON>, English poet, journalist and literary critic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, journalist and literary critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, journalist and literary critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Northern Irish lawyer and judge", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English drummer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, German politician (d. 2016)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Ukrainian high jumper", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Valentyna_Kozyr\" title=\"Valentyna Ko<PERSON>r\"><PERSON><PERSON><PERSON></a>, Ukrainian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentyna_Kozyr\" title=\"Valentyna Ko<PERSON>r\"><PERSON><PERSON><PERSON></a>, Ukrainian high jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valentyna_Kozyr"}]}, {"year": "1951", "text": "<PERSON>, Scottish politician, Minister of State for Trade", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Trade\" class=\"mw-redirect\" title=\"Minister of State for Trade\">Minister of State for Trade</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Trade\" class=\"mw-redirect\" title=\"Minister of State for Trade\">Minister of State for Trade</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of State for Trade", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Trade"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Norwegian pianist and composer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Ke<PERSON>_Bj%C3%B8<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ke<PERSON>_Bj%C3%B8<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ketil_Bj%C3%B8<PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Russian ice hockey player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, French footballer and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American animator, producer, and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian cricketer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English economist, author, and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anthony_<PERSON>enables"}]}, {"year": "1954", "text": "<PERSON>, English author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American football player and sportscaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish educator and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/R%C3%B3is%C3%ADn_Shortall\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%B3is%C3%ADn_Shortall\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish educator and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%B3is%C3%ADn_Shortall"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian footballer and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Am%C3%A9rico_Gallego\" title=\"Américo Gallego\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am%C3%A9rico_Gallego\" title=\"Américo Gallego\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Am%C3%A9rico_Gallego"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian actor and singer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Parvi<PERSON>_<PERSON>ui\" title=\"<PERSON>rvi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Parvi<PERSON>_<PERSON>\" title=\"<PERSON>rvi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Parviz_Parastui"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American businessman, co-founded Starbucks", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Starbucks\" title=\"Starbucks\">Starbucks</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Starbucks\" title=\"Starbucks\">Starbucks</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}, {"title": "Starbucks", "link": "https://wikipedia.org/wiki/Starbucks"}]}, {"year": "1956", "text": "<PERSON>, French actress, director, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Nigerian professor, media scholar", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian professor, media scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian professor, media scholar", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1957", "text": "<PERSON>, Dutch cyclist and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>j"}]}, {"year": "1958", "text": "<PERSON>, American politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Scottish singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Scottish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Scottish singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1958", "text": "<PERSON><PERSON>, British journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English diplomat, British High Commissioner to Australia", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/British_High_Commissioner_to_Australia\" class=\"mw-redirect\" title=\"British High Commissioner to Australia\">British High Commissioner to Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/British_High_Commissioner_to_Australia\" class=\"mw-redirect\" title=\"British High Commissioner to Australia\">British High Commissioner to Australia</a>", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_(diplomat)"}, {"title": "British High Commissioner to Australia", "link": "https://wikipedia.org/wiki/British_High_Commissioner_to_Australia"}]}, {"year": "1959", "text": "<PERSON>, Canadian actor and director", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American baseball player (d. 2016)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American singer (d. 2002)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English journalist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Indian-American journalist and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Souza\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Souza\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-American journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dinesh_D%27Souza"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Slovenian ski jumper", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Mira<PERSON>_<PERSON><PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mira<PERSON>_<PERSON><PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian ski jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miran_Tepe%C5%A1"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Dutch footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American businesswoman (d. 2013)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joy Covey\"><PERSON></a>, American businesswoman (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Cove<PERSON>\" title=\"Joy Covey\"><PERSON></a>, American businesswoman (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joy_Covey"}]}, {"year": "1963", "text": "<PERSON>, Scottish footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor, voice artist, comedian and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, comedian and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, comedian and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1965", "text": "<PERSON>, American bass player and songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1965", "text": "<PERSON>, American puppeteer and voice actor (d. 2014)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)\" title=\"<PERSON> (puppeteer)\"><PERSON></a>, American puppeteer and voice actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)\" title=\"<PERSON> (puppeteer)\"><PERSON></a>, American puppeteer and voice actor (d. 2014)", "links": [{"title": "<PERSON> (puppeteer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)"}]}, {"year": "1966", "text": "<PERSON>, Argentinian-Italian rugby player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Dom%C3%<PERSON><PERSON><PERSON><PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Argentinian-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_Dom%C3%AD<PERSON><PERSON><PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Argentinian-Italian rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/Diego_Dom%C3%ADng<PERSON>z_(rugby_union)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Dutch sociologist, academic, and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sociologist, academic, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sociologist, academic, and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1967", "text": "<PERSON>, American swimmer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/1968\" title=\"1968\">1968</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1968\" title=\"1968\">1968</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "1968", "link": "https://wikipedia.org/wiki/1968"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Dutch director and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American swimmer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American football player and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American actress and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON><PERSON>ger\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9<PERSON>_<PERSON>ger\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_Zellweger"}]}, {"year": "1970", "text": "<PERSON>, American skateboarder, actor, comedian and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American skateboarder, actor, comedian and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American skateboarder, actor, comedian and producer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1971", "text": "<PERSON>, Spanish dancer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sara_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Spanish triple jumper", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Castrejana\"><PERSON><PERSON></a>, Spanish triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Castre<PERSON>\" title=\"<PERSON><PERSON> Castrejana\"><PERSON><PERSON></a>, Spanish triple jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Carlota_Castrejana"}]}, {"year": "1973", "text": "<PERSON>, German tennis player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American baseball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, South African rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, German tennis player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%BCttler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%BCttler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rainer_Sch%C3%BCttler"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Cypriot singer-songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantino<PERSON>_<PERSON>oforou"}]}, {"year": "1977", "text": "<PERSON>, American actress and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter, guitarist, and actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matthew_West"}]}, {"year": "1978", "text": "<PERSON>, English swimmer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1980", "text": "<PERSON>, Australian actor and television host", "html": "1980 - <a href=\"https://wikipedia.org/wiki/1980\" title=\"1980\">1980</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1980\" title=\"1980\">1980</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and television host", "links": [{"title": "1980", "link": "https://wikipedia.org/wiki/1980"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Spanish cyclist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Brazilian racing driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/1981\" title=\"1981\">1981</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1981\" title=\"1981\">1981</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver", "links": [{"title": "1981", "link": "https://wikipedia.org/wiki/1981"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English sprinter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English sprinter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Swedish skier", "html": "1981 - <a href=\"https://wikipedia.org/wiki/An<PERSON>_P%C3%A4rson\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_P%C3%A4rson\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anja_P%C3%A4rson"}]}, {"year": "1982", "text": "<PERSON>, English cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/1982\" title=\"1982\">1982</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1982\" title=\"1982\">1982</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "1982", "link": "https://wikipedia.org/wiki/1982"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>on\" title=\"<PERSON><PERSON><PERSON> Thurston\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Thurston\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Dutch racing driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Russian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American triathlete", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American triathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, German heptathlete", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German heptathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Togolese footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Togolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Togolese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American-South Korean singer-songwriter and dancer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-South Korean singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jay Park\"><PERSON></a>, American-South Korean singer-songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jay_<PERSON>"}]}, {"year": "1988", "text": " <PERSON>, English actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian skier", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A8<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Dutch darts player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch darts player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch darts player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, the 11th Panchen <PERSON>", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ekyi_Nyima\" title=\"G<PERSON>hun Choekyi Nyima\"><PERSON><PERSON><PERSON></a>, the 11th Panchen <PERSON>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Nyima\" title=\"G<PERSON>hun Choekyi Nyima\"><PERSON><PERSON><PERSON></a>, the 11th Panchen <PERSON>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, French racing driver", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89ric_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89ric_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French racing driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89<PERSON>_<PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1991", "text": "<PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Poyer"}]}, {"year": "1991", "text": "<PERSON>, American ice dancer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American race car driver", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Varane\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Varane\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rapha%C3%ABl_Varane"}]}, {"year": "1994", "text": "<PERSON>, Jamaican hurdler", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American musician", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1996", "text": "<PERSON>, Australian swimmer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, German-American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sabally\"><PERSON><PERSON></a>, German-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>bally\"><PERSON><PERSON></a>, German-American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Swedish footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "501", "text": "<PERSON><PERSON><PERSON>, saint and archbishop of Lyon (b. 455)", "html": "501 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(archbishop_of_Lyon)\" title=\"<PERSON><PERSON><PERSON> (archbishop of Lyon)\"><PERSON><PERSON><PERSON></a>, saint and <a href=\"https://wikipedia.org/wiki/Archbishop_of_Lyon\" class=\"mw-redirect\" title=\"Archbishop of Lyon\">archbishop of Lyon</a> (b. 455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(archbishop_of_Lyon)\" title=\"<PERSON><PERSON><PERSON> (archbishop of Lyon)\"><PERSON><PERSON><PERSON></a>, saint and <a href=\"https://wikipedia.org/wiki/Archbishop_of_Lyon\" class=\"mw-redirect\" title=\"Archbishop of Lyon\">archbishop of Lyon</a> (b. 455)", "links": [{"title": "<PERSON><PERSON><PERSON> (archbishop of Lyon)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(archbishop_of_Lyon)"}, {"title": "Archbishop of Lyon", "link": "https://wikipedia.org/wiki/<PERSON>_of_Lyon"}]}, {"year": "775", "text": "<PERSON><PERSON><PERSON>, Armenian prince", "html": "775 - <a href=\"https://wikipedia.org/wiki/Smbat_VII_Bagratuni\" title=\"Smbat VII Bagratuni\">Smbat VII Bagratuni</a>, Armenian prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Smbat_VII_Bagratuni\" title=\"Smbat VII Bagratuni\">Smbat VII Bagratuni</a>, Armenian prince", "links": [{"title": "Smbat VII Bagratuni", "link": "https://wikipedia.org/wiki/Smbat_VII_Bagratuni"}]}, {"year": "775", "text": "<PERSON><PERSON><PERSON>, Armenian prince", "html": "775 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VI_Mamikonian\" title=\"<PERSON><PERSON><PERSON> VI Mamikonian\"><PERSON><PERSON><PERSON> VI <PERSON></a>, Armenian prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VI_Mamikonian\" title=\"<PERSON><PERSON><PERSON> VI Mamikonian\"><PERSON><PERSON><PERSON> <PERSON> Mamikon<PERSON></a>, Armenian prince", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "908", "text": "<PERSON>, Chinese chancellor", "html": "908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Tang dynasty", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty"}]}, {"year": "1074", "text": "<PERSON>, Margrave of Baden", "html": "1074 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden\" class=\"mw-redirect\" title=\"<PERSON>, Margrave of Baden\"><PERSON>, Margrave of Baden</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden\" class=\"mw-redirect\" title=\"<PERSON>, Margrave of Baden\"><PERSON>, Margrave of Baden</a>", "links": [{"title": "<PERSON>, Margrave of Baden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden"}]}, {"year": "1077", "text": "<PERSON><PERSON><PERSON> I of Hungary (b. 1040)", "html": "1077 - <a href=\"https://wikipedia.org/wiki/G%C3%A9za_I_of_Hungary\" title=\"Géza I of Hungary\">Géza I of Hungary</a> (b. 1040)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9za_I_of_Hungary\" title=\"Géza I of Hungary\">Géza I of Hungary</a> (b. 1040)", "links": [{"title": "Géza I of Hungary", "link": "https://wikipedia.org/wiki/G%C3%A9za_I_of_Hungary"}]}, {"year": "1185", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1178)", "html": "1185 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1178)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1178)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1217", "text": "<PERSON>, Landgrave of Thuringia", "html": "1217 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON>, Landgrave of Thuringia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON>, Landgrave of Thuringia</a>", "links": [{"title": "<PERSON>, Landgrave of Thuringia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Thuringia"}]}, {"year": "1228", "text": "Queen <PERSON> of Jerusalem (b. 1212)", "html": "1228 - Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Jerusalem\" title=\"<PERSON> II of Jerusalem\"><PERSON> of Jerusalem</a> (b. 1212)", "no_year_html": "Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jerusalem\" title=\"<PERSON> II of Jerusalem\"><PERSON> of Jerusalem</a> (b. 1212)", "links": [{"title": "<PERSON> of Jerusalem", "link": "https://wikipedia.org/wiki/Isabella_II_of_Jerusalem"}]}, {"year": "1243", "text": "<PERSON><PERSON><PERSON> of Valperga, Bishop of Aosta", "html": "1243 - <a href=\"https://wikipedia.org/wiki/Boniface_of_Valperga\" title=\"Bonif<PERSON> of Valperga\"><PERSON><PERSON><PERSON> of Valperga</a>, Bishop of Aosta", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boniface_of_Valperga\" title=\"Bonif<PERSON> of Valperga\"><PERSON><PERSON><PERSON> of Valperga</a>, Bishop of Aosta", "links": [{"title": "Boniface of Valperga", "link": "https://wikipedia.org/wiki/Boniface_of_Valperga"}]}, {"year": "1264", "text": "<PERSON>, 2nd Earl of Winchester, medieval English nobleman; Earl of Winchester (b. 1195)", "html": "1264 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Winchester\" title=\"<PERSON>, 2nd Earl of Winchester\"><PERSON>, 2nd Earl of Winchester</a>, medieval English nobleman; Earl of Winchester (b. 1195)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Winchester\" title=\"<PERSON>, 2nd Earl of Winchester\"><PERSON>, 2nd Earl of Winchester</a>, medieval English nobleman; Earl of Winchester (b. 1195)", "links": [{"title": "<PERSON>, 2nd Earl of Winchester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Winchester"}]}, {"year": "1295", "text": "<PERSON><PERSON> of Castile (b. 1258)", "html": "1295 - <a href=\"https://wikipedia.org/wiki/Sancho_IV_of_Castile\" title=\"Sancho IV of Castile\"><PERSON><PERSON> IV of Castile</a> (b. 1258)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sancho_IV_of_Castile\" title=\"Sancho IV of Castile\"><PERSON><PERSON> IV of Castile</a> (b. 1258)", "links": [{"title": "<PERSON><PERSON> IV of Castile", "link": "https://wikipedia.org/wiki/Sancho_IV_of_Castile"}]}, {"year": "1342", "text": "<PERSON> <PERSON> (b. 1285)", "html": "1342 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> (b. 1285)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> (b. 1285)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1397", "text": "<PERSON>, 2nd Earl of Kent, English nobleman", "html": "1397 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Kent\" title=\"<PERSON>, 2nd Earl <PERSON> Kent\"><PERSON>, 2nd Earl of Kent</a>, English nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_<PERSON>_Kent\" title=\"<PERSON>, 2nd Earl of Kent\"><PERSON>, 2nd Earl of Kent</a>, English nobleman", "links": [{"title": "<PERSON>, 2nd Earl of Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Kent"}]}, {"year": "1472", "text": "<PERSON>, Italian author, poet, and philosopher (b. 1404)", "html": "1472 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author, poet, and philosopher (b. 1404)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author, poet, and philosopher (b. 1404)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1516", "text": "<PERSON>, English diplomat (b. 1467)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat (b. 1467)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat (b. 1467)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1566", "text": "<PERSON>, French poet and author (b. 1520)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1520)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louise_Lab%C3%A9"}]}, {"year": "1566", "text": "<PERSON>, mistress of King <PERSON> of France (b. 1499)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, mistress of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (b. 1499)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, mistress of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (b. 1499)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1595", "text": "<PERSON><PERSON><PERSON>, Italian poet and songwriter (b. 1544)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/Torqua<PERSON>_<PERSON>sso\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and songwriter (b. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Torqua<PERSON>_<PERSON>sso\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and songwriter (b. 1544)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Torquato_Tasso"}]}, {"year": "1605", "text": "<PERSON><PERSON><PERSON>, Siamese King of Ayutthaya Kingdom (b. c. 1555)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/Naresuan\" title=\"Naresuan\"><PERSON><PERSON><PERSON></a>, Siamese King of <a href=\"https://wikipedia.org/wiki/Ayutthaya_Kingdom\" title=\"Ayutthaya Kingdom\">Ayutthaya Kingdom</a> (b. c. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naresuan\" title=\"Naresuan\"><PERSON><PERSON><PERSON></a>, Siamese King of <a href=\"https://wikipedia.org/wiki/Ayutthaya_Kingdom\" title=\"Ayutthaya Kingdom\">Ayutthaya Kingdom</a> (b. c. 1555)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naresuan"}, {"title": "Ayutthaya Kingdom", "link": "https://wikipedia.org/wiki/Ayutthaya_Kingdom"}]}, {"year": "1644", "text": "Chongzhen Emperor of China (b. 1611)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Chongzhen_Emperor\" title=\"Chongzhen Emperor\">Chongzhen Emperor</a> of China (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chongzhen_Emperor\" title=\"Chongzhen Emperor\">Chongzhen Emperor</a> of China (b. 1611)", "links": [{"title": "Chongzhen Emperor", "link": "https://wikipedia.org/wiki/Chongzhen_Emperor"}]}, {"year": "1660", "text": "<PERSON>, English cleric and theologian (b. 1605)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric and theologian (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric and theologian (b. 1605)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1690", "text": "<PERSON> the Younger, Flemish painter and educator (b. 1610)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Flemish painter and educator (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Flemish painter and educator (b. 1610)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Younger"}]}, {"year": "1744", "text": "<PERSON>, Swedish astronomer, physicist, and mathematician (b. 1701)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish astronomer, physicist, and mathematician (b. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish astronomer, physicist, and mathematician (b. 1701)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON><PERSON><PERSON>, French minister, physicist, and academic (b. 1700)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French minister, physicist, and academic (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French minister, physicist, and academic (b. 1700)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1800", "text": "<PERSON>, English poet (b. 1731)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1731)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON>, French mathematician and physicist (b. 1781)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Sim%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and physicist (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sim%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and physicist (b. 1781)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sim%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Russian painter and sculptor (b. 1783)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian painter and sculptor (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian painter and sculptor (b. 1783)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ls<PERSON>"}]}, {"year": "1875", "text": "12th Dalai Lama (b. 1857)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/12th_Dalai_Lama\" title=\"12th Dalai Lama\">12th Dalai Lama</a> (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/12th_Dalai_Lama\" title=\"12th Dalai Lama\">12th Dalai Lama</a> (b. 1857)", "links": [{"title": "12th Dalai Lama", "link": "https://wikipedia.org/wiki/12th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, English author (b. 1820)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, Canadian tribal chief (b. 1830)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Crowfoot\" title=\"Crowfoot\"><PERSON><PERSON></a>, Canadian tribal chief (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crowfoot\" title=\"Crowfoot\"><PERSON><PERSON></a>, Canadian tribal chief (b. 1830)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>foot"}]}, {"year": "1891", "text": "<PERSON>, English priest and educator (b. 1811)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and educator (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and educator (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, French explorer (b. 1840)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Estonian-German geologist and explorer (b. 1822)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German geologist and explorer (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German geologist and explorer (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American composer and educator (b. 1839)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Italian journalist and author (b. 1862)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and author (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and author (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Canadian bishop (b. 1859)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian bishop (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian bishop (b. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American journalist, lawyer, and politician, 6th United States Assistant Secretary of State (b. 1830)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Assistant Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State"}]}, {"year": "1919", "text": "<PERSON>, American businessman and philanthropist (b. 1836)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, American journalist and women's rights advocate (b. 1828)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American journalist and women's rights advocate (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American journalist and women's rights advocate (b. 1828)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 8th Premier of Quebec (b. 1840)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Russian general (b. 1878)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>rangel\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>rangel\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>el"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Bengali aristocrat and philanthropist (b. 1871)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bengali aristocrat and philanthropist (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bengali aristocrat and philanthropist (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Turkish commander and politician (b. 1881)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k\" title=\"<PERSON><PERSON> Bozok\"><PERSON><PERSON></a>, Turkish commander and politician (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zok\" title=\"<PERSON><PERSON> Bozok\"><PERSON><PERSON></a>, Turkish commander and politician (b. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>-<PERSON><PERSON>, Russian director, producer, and playwright (b. 1858)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director, producer, and playwright (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director, producer, and playwright (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American cartoonist (b. 1880)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Irish-American baseball player (b. 1859)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American baseball player (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American baseball player (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American engineer and politician, 24th Governor of California (b. 1859)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American engineer and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American engineer and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a> (b. 1859)", "links": [{"title": "<PERSON> (American politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(American_politician)"}, {"title": "Governor of California", "link": "https://wikipedia.org/wiki/Governor_of_California"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss composer (b. 1903)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%BCh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss composer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%BCh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss composer (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Georg_Fr%C3%BCh"}]}, {"year": "1950", "text": "<PERSON>, English educationalist and Director of Education of the Colony of Transvaal (b. 1867)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educationalist and Director of Education of the <a href=\"https://wikipedia.org/wiki/Colony_of_Transvaal\" class=\"mw-redirect\" title=\"Colony of Transvaal\">Colony of Transvaal</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educationalist and Director of Education of the <a href=\"https://wikipedia.org/wiki/Colony_of_Transvaal\" class=\"mw-redirect\" title=\"Colony of Transvaal\">Colony of Transvaal</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Colony of Transvaal", "link": "https://wikipedia.org/wiki/Colony_of_Transvaal"}]}, {"year": "1961", "text": "<PERSON>, American discus thrower and shot putter (b. 1875)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower and shot putter (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower and shot putter (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress (b. 1915)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English actor (b. 1906)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Hungarian-American actress (b. 1896)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actress (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actress (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Maltese architect and developer (b. 1888)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese architect and developer (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese architect and developer (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Israeli singer and songwriter (b. 1947)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli singer and songwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli singer and songwriter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English director and producer (b. 1906)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Israeli engineer and educator (b. 1886)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli engineer and educator (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli engineer and educator (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American cardinal (b. 1907)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American priest and author (b. 1897)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/William_<PERSON>_<PERSON>\" title=\"William <PERSON>\"><PERSON></a>, American priest and author (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_S<PERSON>_Bow<PERSON>n\" title=\"William <PERSON>\"><PERSON></a>, American priest and author (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_<PERSON>_Bowdern"}]}, {"year": "1988", "text": "<PERSON>, American singer-songwriter (b. 1944)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American journalist and author (b. 1904)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American saxophonist, composer, and actor (b. 1923)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter (b. 1965)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter (b. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yutaka_Ozaki"}]}, {"year": "1995", "text": "<PERSON>, American game show host (b. 1925)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Fleming"}]}, {"year": "1995", "text": "<PERSON>, American actress, singer, and dancer (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Ukrainian military historian (b. 1903)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian military historian (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian military historian (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American graphic designer and director (b. 1920)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bass\"><PERSON></a>, American graphic designer and director (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bass\"><PERSON></a>, American graphic designer and director (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American author and photographer (b. 1910)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Morris\"><PERSON></a>, American author and photographer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Morris\"><PERSON></a>, American author and photographer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, 3rd Baron <PERSON>, Irish journalist and author (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, Irish journalist and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, Irish journalist and author (b. 1914)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American singer-songwriter and producer (b. 1951)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>man\"><PERSON></a>, American singer-songwriter and producer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>man\"><PERSON></a>, American singer-songwriter and producer (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, French mathematician and statistician (b. 1924)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and statistician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and statistician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American director and producer (b. 1911)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Italian racing driver (b. 1956)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American rapper and dancer (b. 1971)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and dancer (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and dancer (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Kenyan runner (b. 1966)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English-American poet and academic (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American poet and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American poet and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American politician (b. 1935)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician (b. 1935)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "2005", "text": "Swami <PERSON>, Indian monk and educator (b. 1908)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Swami_<PERSON>\" class=\"mw-redirect\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian monk and educator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swami_<PERSON>\" class=\"mw-redirect\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian monk and educator (b. 1908)", "links": [{"title": "Swami <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American-Canadian journalist, author, and activist (b. 1916)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist, author, and activist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist, author, and activist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Welsh politician and independent member of parliament (b. 1948)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician and independent member of parliament (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician and independent member of parliament (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English footballer and manager (b. 1945)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, English footballer and manager (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, English footballer and manager (b. 1945)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2007", "text": "<PERSON>, English footballer and cricketer (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and cricketer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and cricketer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American singer-songwriter (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, English trumpet player, composer, and radio host (b. 1921)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpet player, composer, and radio host (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpet player, composer, and radio host (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American actress and singer (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actress and singer (b. 1935)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>e"}]}, {"year": "2010", "text": "<PERSON>, English novelist, short story writer, essayist, and poet (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, short story writer, essayist, and poet (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, short story writer, essayist, and poet (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, British musician (b. 1957)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Poly_Styrene\" title=\"Poly Styrene\"><PERSON><PERSON></a>, British musician (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Poly_Styrene\" title=\"Poly Styrene\"><PERSON><PERSON></a>, British musician (b. 1957)", "links": [{"title": "Poly Styrene", "link": "https://wikipedia.org/wiki/Poly_Styrene"}]}, {"year": "2012", "text": "<PERSON>, Australian footballer (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American rancher and politician (b. 1910)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rancher and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rancher and politician (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, American ballerina and educator (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American ballerina and educator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American ballerina and educator (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Irish painter and illustrator (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and illustrator (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and illustrator (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Scottish biochemist and politician (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish biochemist and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish biochemist and politician (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American composer and conductor (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian virologist and academic (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_<PERSON>\" title=\"György Berencsi\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian virologist and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_<PERSON>\" title=\"György Berencsi\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian virologist and academic (b. 1941)", "links": [{"title": "György Be<PERSON>csi", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_<PERSON>i"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Camp\"><PERSON></a>, American baseball player (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Camp\"><PERSON></a>, American baseball player (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian priest and politician (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian priest and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian priest and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soldier, lawyer, and judge (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American soldier, lawyer, and judge (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American soldier, lawyer, and judge (b. 1923)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2014", "text": "<PERSON>, American football player and coach (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Spanish footballer and manager (b. 1968)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, German journalist and author (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German journalist and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German journalist and author (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American-Canadian baseball player and manager (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian baseball player and manager (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian baseball player and manager (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German geographer and academic (b. 1948)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and academic (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and academic (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American screenwriter and novelist (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and novelist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and novelist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player (b. 1956)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1956)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2016", "text": "<PERSON>, Australian politician, 33rd Premier of New South Wales (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1922)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, Pakistani actress, playwright and director of social theater, and women's rights activist (b. 1956)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani actress, playwright and director of social theater, and women's rights activist (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani actress, playwright and director of social theater, and women's rights activist (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Made<PERSON><PERSON>_<PERSON>au<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American basketball player (b. 1940)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American singer, activist, and actor (b. 1927)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, activist, and actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, activist, and actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American television actress (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television actress (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television actress (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, French director, cinematographer and screenwriter (b. 1961)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, cinematographer and screenwriter (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, cinematographer and screenwriter (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}