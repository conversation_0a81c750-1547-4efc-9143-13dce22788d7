{"date": "March 31", "url": "https://wikipedia.org/wiki/March_31", "data": {"Events": [{"year": "307", "text": "After divorcing his wife <PERSON><PERSON><PERSON>, <PERSON> marries <PERSON><PERSON>, daughter of the retired Roman emperor <PERSON><PERSON>.", "html": "307 - After divorcing his wife <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, daughter of the retired <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Maximian\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "After divorcing his wife <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, daughter of the retired <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Maximian\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Minervina", "link": "https://wikipedia.org/wiki/Minervina"}, {"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fausta"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}]}, {"year": "1146", "text": "<PERSON> of Clairvaux preaches his famous sermon in a field at Vézelay, urging the necessity of a Second Crusade. <PERSON> is present, and joins the Crusade.", "html": "1146 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Clairvaux\"><PERSON> of Clairvaux</a> preaches his famous sermon in a field at <a href=\"https://wikipedia.org/wiki/V%C3%A9zelay\" title=\"Vézelay\">V<PERSON><PERSON><PERSON></a>, urging the necessity of a <a href=\"https://wikipedia.org/wiki/Second_Crusade\" title=\"Second Crusade\">Second Crusade</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France\" title=\"Louis <PERSON> of France\"><PERSON> VII</a> is present, and joins the Crusade.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Clairvaux\"><PERSON> of <PERSON>vaux</a> preaches his famous sermon in a field at <a href=\"https://wikipedia.org/wiki/V%C3%A9zelay\" title=\"Vézelay\">V<PERSON><PERSON><PERSON></a>, urging the necessity of a <a href=\"https://wikipedia.org/wiki/Second_Crusade\" title=\"Second Crusade\">Second Crusade</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Louis VII of France\"><PERSON> VII</a> is present, and joins the Crusade.", "links": [{"title": "<PERSON> of Clairvaux", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vézelay", "link": "https://wikipedia.org/wiki/V%C3%A9zelay"}, {"title": "Second Crusade", "link": "https://wikipedia.org/wiki/Second_Crusade"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VII_of_France"}]}, {"year": "1174", "text": "A conspiracy against <PERSON><PERSON><PERSON>, aiming to restore the Fatimid Caliphate, is revealed in Cairo, involving senior figures of the former Fatimid regime and the poet <PERSON><PERSON>. Modern historians doubt the extent and danger of the conspiracy reported in official sources, but its ringleaders will be publicly executed over the following weeks.", "html": "1174 - A <a href=\"https://wikipedia.org/wiki/Pro-Fatimid_conspiracy_against_<PERSON>adin\" title=\"Pro-Fatimid conspiracy against <PERSON><PERSON><PERSON>\">conspiracy</a> against <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"Saladi<PERSON>\"><PERSON><PERSON><PERSON></a>, aiming to restore the <a href=\"https://wikipedia.org/wiki/Fatimid_Caliphate\" title=\"Fatimid Caliphate\"><PERSON><PERSON><PERSON></a>, is revealed in <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, involving senior figures of the former Fatimid regime and the poet <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON> al-<PERSON>\"><PERSON><PERSON></a>. Modern historians doubt the extent and danger of the conspiracy reported in official sources, but its ringleaders will be publicly executed over the following weeks.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Pro-Fatimid_conspiracy_against_<PERSON>adin\" title=\"Pro-Fatimid conspiracy against <PERSON><PERSON><PERSON>\">conspiracy</a> against <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"Saladin\"><PERSON><PERSON><PERSON></a>, aiming to restore the <a href=\"https://wikipedia.org/wiki/Fatimid_Caliphate\" title=\"Fatimid Caliphate\"><PERSON><PERSON><PERSON></a>, is revealed in <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, involving senior figures of the former Fatimid regime and the poet <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>. Modern historians doubt the extent and danger of the conspiracy reported in official sources, but its ringleaders will be publicly executed over the following weeks.", "links": [{"title": "Pro-Fatimid conspiracy against <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pro-Fatimid_conspiracy_against_<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>n"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fatimid_Caliphate"}, {"title": "Cairo", "link": "https://wikipedia.org/wiki/Cairo"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1492", "text": "Queen <PERSON> of Castile issues the Alhambra Decree, ordering her 150,000 Jewish and Muslim subjects to convert to Christianity or face expulsion.", "html": "1492 - Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> I of Castile\"><PERSON> of Castile</a> issues the <a href=\"https://wikipedia.org/wiki/Alhambra_Decree\" title=\"Alhambra Decree\">Alhambra Decree</a>, ordering her 150,000 <a href=\"https://wikipedia.org/wiki/Spanish_and_Portuguese_Jews\" title=\"Spanish and Portuguese Jews\">Jewish</a> and <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Muslim</a> subjects to convert to <a href=\"https://wikipedia.org/wiki/Christianity\" title=\"Christianity\">Christianity</a> or face expulsion.", "no_year_html": "Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> I of Castile\"><PERSON> of Castile</a> issues the <a href=\"https://wikipedia.org/wiki/Alhambra_Decree\" title=\"Alhambra Decree\">Alhambra Decree</a>, ordering her 150,000 <a href=\"https://wikipedia.org/wiki/Spanish_and_Portuguese_Jews\" title=\"Spanish and Portuguese Jews\">Jewish</a> and <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Muslim</a> subjects to convert to <a href=\"https://wikipedia.org/wiki/Christianity\" title=\"Christianity\">Christianity</a> or face expulsion.", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}, {"title": "Alhambra Decree", "link": "https://wikipedia.org/wiki/Alhambra_Decree"}, {"title": "Spanish and Portuguese Jews", "link": "https://wikipedia.org/wiki/Spanish_and_Portuguese_Jews"}, {"title": "Moors", "link": "https://wikipedia.org/wiki/Moors"}, {"title": "Christianity", "link": "https://wikipedia.org/wiki/Christianity"}]}, {"year": "1521", "text": "<PERSON> and fifty of his men came ashore to present-day Limasawa to participate in the first Catholic mass in the Philippines.", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Magellan%27s_circumnavigation\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s circumnavigation\">fifty of his men</a> came ashore to present-day <a href=\"https://wikipedia.org/wiki/Limasawa\" title=\"Limasawa\">Limasawa</a> to participate in the <a href=\"https://wikipedia.org/wiki/First_Mass_in_the_Philippines\" title=\"First Mass in the Philippines\">first Catholic mass in the Philippines</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Magellan%27s_circumnavigation\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s circumnavigation\">fifty of his men</a> came ashore to present-day <a href=\"https://wikipedia.org/wiki/Limasawa\" title=\"Limasawa\">Limasawa</a> to participate in the <a href=\"https://wikipedia.org/wiki/First_Mass_in_the_Philippines\" title=\"First Mass in the Philippines\">first Catholic mass in the Philippines</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>'s circumnavigation", "link": "https://wikipedia.org/wiki/Magellan%27s_circumnavigation"}, {"title": "Limasawa", "link": "https://wikipedia.org/wiki/Limasawa"}, {"title": "First Mass in the Philippines", "link": "https://wikipedia.org/wiki/First_Mass_in_the_Philippines"}]}, {"year": "1657", "text": "The Long Parliament presents the Humble Petition and Advice offering <PERSON> the British throne, which he eventually declines.", "html": "1657 - The <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a> presents the <a href=\"https://wikipedia.org/wiki/Humble_Petition_and_Advice\" title=\"Humble Petition and Advice\">Humble Petition and Advice</a> offering <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> the British throne, which he eventually declines.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a> presents the <a href=\"https://wikipedia.org/wiki/Humble_Petition_and_Advice\" title=\"Humble Petition and Advice\">Humble Petition and Advice</a> offering <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> the British throne, which he eventually declines.", "links": [{"title": "Long Parliament", "link": "https://wikipedia.org/wiki/Long_Parliament"}, {"title": "Humble Petition and Advice", "link": "https://wikipedia.org/wiki/Humble_Petition_and_Advice"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1706", "text": "The last session of history of the Catalan Courts, the parliamentary body of the Principality of Catalonia, ends. Catalonia's constitutional modernisation passed by the Courts aims to improve the guarantee of individual, political and economic rights (among them, the secrecy of correspondence).", "html": "1706 - The last session of history of the <a href=\"https://wikipedia.org/wiki/Catalan_Courts\" title=\"Catalan Courts\">Catalan Courts</a>, the parliamentary body of the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a>, ends. <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>'s <a href=\"https://wikipedia.org/wiki/Catalan_constitutions\" title=\"Catalan constitutions\">constitutional</a> modernisation passed by the Courts aims to improve the guarantee of individual, political and economic rights (among them, the <a href=\"https://wikipedia.org/wiki/Secrecy_of_correspondence\" title=\"Secrecy of correspondence\">secrecy of correspondence</a>).", "no_year_html": "The last session of history of the <a href=\"https://wikipedia.org/wiki/Catalan_Courts\" title=\"Catalan Courts\">Catalan Courts</a>, the parliamentary body of the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a>, ends. <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>'s <a href=\"https://wikipedia.org/wiki/Catalan_constitutions\" title=\"Catalan constitutions\">constitutional</a> modernisation passed by the Courts aims to improve the guarantee of individual, political and economic rights (among them, the <a href=\"https://wikipedia.org/wiki/Secrecy_of_correspondence\" title=\"Secrecy of correspondence\">secrecy of correspondence</a>).", "links": [{"title": "Catalan Courts", "link": "https://wikipedia.org/wiki/Catalan_Courts"}, {"title": "Principality of Catalonia", "link": "https://wikipedia.org/wiki/Principality_of_Catalonia"}, {"title": "Catalonia", "link": "https://wikipedia.org/wiki/Catalonia"}, {"title": "Catalan constitutions", "link": "https://wikipedia.org/wiki/Catalan_constitutions"}, {"title": "Secrecy of correspondence", "link": "https://wikipedia.org/wiki/Secrecy_of_correspondence"}]}, {"year": "1717", "text": "A sermon on \"The Nature of the Kingdom of Christ\" by <PERSON>, the Bishop of Bangor, preached in the presence of King <PERSON> of Great Britain, provokes the Bangorian Controversy.", "html": "1717 - A sermon on \"The Nature of the Kingdom of Christ\" by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bangor\" title=\"Bishop of Bangor\">Bishop of Bangor</a>, preached in the presence of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> of Great Britain\"><PERSON> of Great Britain</a>, provokes the <a href=\"https://wikipedia.org/wiki/Bangorian_Controversy\" title=\"Bangorian Controversy\">Bangorian Controversy</a>.", "no_year_html": "A sermon on \"The Nature of the Kingdom of Christ\" by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bangor\" title=\"Bishop of Bangor\">Bishop of Bangor</a>, preached in the presence of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> of Great Britain\"><PERSON> of Great Britain</a>, provokes the <a href=\"https://wikipedia.org/wiki/Bangorian_Controversy\" title=\"Bangorian Controversy\">Bangorian Controversy</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of Bangor", "link": "https://wikipedia.org/wiki/Bishop_of_Bangor"}, {"title": "<PERSON> of Great Britain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain"}, {"title": "Bangorian Controversy", "link": "https://wikipedia.org/wiki/Bangorian_Controversy"}]}, {"year": "1761", "text": "The 1761 Lisbon earthquake strikes off the Iberian Peninsula with an estimated magnitude of 8.5, six years after another quake destroyed the city.", "html": "1761 - The <a href=\"https://wikipedia.org/wiki/1761_Lisbon_earthquake\" title=\"1761 Lisbon earthquake\">1761 Lisbon earthquake</a> strikes off the <a href=\"https://wikipedia.org/wiki/Iberian_Peninsula\" title=\"Iberian Peninsula\">Iberian Peninsula</a> with an estimated <a href=\"https://wikipedia.org/wiki/Richter_magnitude_scale\" class=\"mw-redirect\" title=\"Richter magnitude scale\">magnitude</a> of 8.5, six years after another quake destroyed the city.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1761_Lisbon_earthquake\" title=\"1761 Lisbon earthquake\">1761 Lisbon earthquake</a> strikes off the <a href=\"https://wikipedia.org/wiki/Iberian_Peninsula\" title=\"Iberian Peninsula\">Iberian Peninsula</a> with an estimated <a href=\"https://wikipedia.org/wiki/Richter_magnitude_scale\" class=\"mw-redirect\" title=\"Richter magnitude scale\">magnitude</a> of 8.5, six years after another quake destroyed the city.", "links": [{"title": "1761 Lisbon earthquake", "link": "https://wikipedia.org/wiki/1761_Lisbon_earthquake"}, {"title": "Iberian Peninsula", "link": "https://wikipedia.org/wiki/Iberian_Peninsula"}, {"title": "Richter magnitude scale", "link": "https://wikipedia.org/wiki/Richter_magnitude_scale"}]}, {"year": "1774", "text": "American Revolution: The Kingdom of Great Britain orders the port of Boston, Massachusetts closed pursuant to the Boston Port Act.", "html": "1774 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> orders the port of <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> closed pursuant to the <a href=\"https://wikipedia.org/wiki/Boston_Port_Act\" title=\"Boston Port Act\">Boston Port Act</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> orders the port of <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> closed pursuant to the <a href=\"https://wikipedia.org/wiki/Boston_Port_Act\" title=\"Boston Port Act\">Boston Port Act</a>.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}, {"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}, {"title": "Boston Port Act", "link": "https://wikipedia.org/wiki/Boston_Port_Act"}]}, {"year": "1814", "text": "The Sixth Coalition occupies Paris after <PERSON>'s Grande Armée capitulates.", "html": "1814 - The <a href=\"https://wikipedia.org/wiki/War_of_the_Sixth_Coalition\" title=\"War of the Sixth Coalition\">Sixth Coalition</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Paris_(1814)\" title=\"Battle of Paris (1814)\">occupies Paris</a> after <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Grande_Arm%C3%A9e\" title=\"Grande Armée\">Grande Armée</a> capitulates.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/War_of_the_Sixth_Coalition\" title=\"War of the Sixth Coalition\">Sixth Coalition</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Paris_(1814)\" title=\"Battle of Paris (1814)\">occupies Paris</a> after <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Grande_Arm%C3%A9e\" title=\"Grande Armée\">Grande Armée</a> capitulates.", "links": [{"title": "War of the Sixth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Sixth_Coalition"}, {"title": "Battle of Paris (1814)", "link": "https://wikipedia.org/wiki/Battle_of_Paris_(1814)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Grande Armée", "link": "https://wikipedia.org/wiki/Grande_Arm%C3%A9e"}]}, {"year": "1854", "text": "Commodore <PERSON> signs the Convention of Kanagawa with the Tokugawa Shogunate, opening the ports of Shimoda and Hakodate to American trade.", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Commodore_(United_States)\" title=\"Commodore (United States)\">Commodore</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Convention_of_Kanagawa\" title=\"Convention of Kanagawa\">Convention of Kanagawa</a> with the <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa Shogunate</a>, opening the ports of <a href=\"https://wikipedia.org/wiki/Shimoda,_Shizuoka\" title=\"Shimoda, Shizuoka\">Shimoda</a> and <a href=\"https://wikipedia.org/wiki/Hakodate\" title=\"Hakodate\">Hakodate</a> to American trade.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Commodore_(United_States)\" title=\"Commodore (United States)\">Commodore</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Convention_of_Kanagawa\" title=\"Convention of Kanagawa\">Convention of Kanagawa</a> with the <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa Shogunate</a>, opening the ports of <a href=\"https://wikipedia.org/wiki/Shimoda,_Shizuoka\" title=\"Shimoda, Shizuoka\">Shimoda</a> and <a href=\"https://wikipedia.org/wiki/Hakodate\" title=\"Hakodate\">Hakodate</a> to American trade.", "links": [{"title": "<PERSON> (United States)", "link": "https://wikipedia.org/wiki/Commodore_(United_States)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Convention of Kanagawa", "link": "https://wikipedia.org/wiki/Convention_of_Kanagawa"}, {"title": "Tokugawa shogunate", "link": "https://wikipedia.org/wiki/Tokugawa_shogunate"}, {"title": "Shimoda, Shizuoka", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Shizuoka"}, {"title": "Hakodate", "link": "https://wikipedia.org/wiki/Hakodate"}]}, {"year": "1885", "text": "The United Kingdom establishes the Bechuanaland Protectorate.", "html": "1885 - The <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom</a> establishes the <a href=\"https://wikipedia.org/wiki/Bechuanaland_Protectorate\" title=\"Bechuanaland Protectorate\">Bechuanaland Protectorate</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom</a> establishes the <a href=\"https://wikipedia.org/wiki/Bechuanaland_Protectorate\" title=\"Bechuanaland Protectorate\">Bechuanaland Protectorate</a>.", "links": [{"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "Bechuanaland Protectorate", "link": "https://wikipedia.org/wiki/Bechuanaland_Protectorate"}]}, {"year": "1889", "text": "The Eiffel Tower is officially opened.", "html": "1889 - The <a href=\"https://wikipedia.org/wiki/Eiffel_Tower\" title=\"Eiffel Tower\">Eiffel Tower</a> is officially opened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Eiffel_Tower\" title=\"Eiffel Tower\">Eiffel Tower</a> is officially opened.", "links": [{"title": "Eiffel Tower", "link": "https://wikipedia.org/wiki/Eiffel_Tower"}]}, {"year": "1899", "text": "Malolos, capital of the First Philippine Republic, is captured by American forces.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Malolos\" title=\"Malolos\">Malolos</a>, capital of the <a href=\"https://wikipedia.org/wiki/First_Philippine_Republic\" title=\"First Philippine Republic\">First Philippine Republic</a>, <a href=\"https://wikipedia.org/wiki/Capture_of_Malolos\" title=\"Capture of Malolos\">is captured by American forces</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malolos\" title=\"Malolos\">Malolos</a>, capital of the <a href=\"https://wikipedia.org/wiki/First_Philippine_Republic\" title=\"First Philippine Republic\">First Philippine Republic</a>, <a href=\"https://wikipedia.org/wiki/Capture_of_Malolos\" title=\"Capture of Malolos\">is captured by American forces</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mal<PERSON>s"}, {"title": "First Philippine Republic", "link": "https://wikipedia.org/wiki/First_Philippine_Republic"}, {"title": "Capture of Malolos", "link": "https://wikipedia.org/wiki/Capture_of_Malolos"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON> by <PERSON><PERSON> premieres at the National Opera House in Prague.", "html": "1901 - <i><a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_(opera)\" title=\"Rusal<PERSON> (opera)\">R<PERSON><PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> premieres at the National Opera House in Prague.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(opera)\" title=\"R<PERSON><PERSON> (opera)\">R<PERSON><PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> premieres at the National Opera House in Prague.", "links": [{"title": "<PERSON><PERSON><PERSON> (opera)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(opera)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k"}]}, {"year": "1905", "text": "<PERSON> of Germany declares his support for Moroccan independence in Tangier, beginning the First Moroccan Crisis.", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\">Kaiser <PERSON> of Germany</a> declares his support for Moroccan independence in <a href=\"https://wikipedia.org/wiki/Tangier\" title=\"Tangier\"><PERSON><PERSON></a>, beginning the <a href=\"https://wikipedia.org/wiki/First_Moroccan_Crisis\" title=\"First Moroccan Crisis\">First Moroccan Crisis</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\">Kaiser <PERSON> of Germany</a> declares his support for Moroccan independence in <a href=\"https://wikipedia.org/wiki/Tangier\" title=\"Tang<PERSON>\"><PERSON><PERSON></a>, beginning the <a href=\"https://wikipedia.org/wiki/First_Moroccan_Crisis\" title=\"First Moroccan Crisis\">First Moroccan Crisis</a>.", "links": [{"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}, {"title": "Tangier", "link": "https://wikipedia.org/wiki/<PERSON>ier"}, {"title": "First Moroccan Crisis", "link": "https://wikipedia.org/wiki/First_Moroccan_Crisis"}]}, {"year": "1906", "text": "The Intercollegiate Athletic Association of the United States (later the National Collegiate Athletic Association) is established to set rules for college sports in the United States.", "html": "1906 - The Intercollegiate Athletic Association of the United States (later the <a href=\"https://wikipedia.org/wiki/National_Collegiate_Athletic_Association\" title=\"National Collegiate Athletic Association\">National Collegiate Athletic Association</a>) is established to set rules for <a href=\"https://wikipedia.org/wiki/College_sports_in_the_United_States\" class=\"mw-redirect\" title=\"College sports in the United States\">college sports in the United States</a>.", "no_year_html": "The Intercollegiate Athletic Association of the United States (later the <a href=\"https://wikipedia.org/wiki/National_Collegiate_Athletic_Association\" title=\"National Collegiate Athletic Association\">National Collegiate Athletic Association</a>) is established to set rules for <a href=\"https://wikipedia.org/wiki/College_sports_in_the_United_States\" class=\"mw-redirect\" title=\"College sports in the United States\">college sports in the United States</a>.", "links": [{"title": "National Collegiate Athletic Association", "link": "https://wikipedia.org/wiki/National_Collegiate_Athletic_Association"}, {"title": "College sports in the United States", "link": "https://wikipedia.org/wiki/College_sports_in_the_United_States"}]}, {"year": "1909", "text": "Serbia formally withdraws its opposition to Austro-Hungarian actions in the Bosnian Crisis.", "html": "1909 - Serbia formally withdraws its opposition to Austro-Hungarian actions in the <a href=\"https://wikipedia.org/wiki/Bosnian_Crisis\" title=\"Bosnian Crisis\">Bosnian Crisis</a>.", "no_year_html": "Serbia formally withdraws its opposition to Austro-Hungarian actions in the <a href=\"https://wikipedia.org/wiki/Bosnian_Crisis\" title=\"Bosnian Crisis\">Bosnian Crisis</a>.", "links": [{"title": "Bosnian Crisis", "link": "https://wikipedia.org/wiki/Bosnian_Crisis"}]}, {"year": "1913", "text": "The Vienna Concert Society rioted during a performance of modernist music by <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, causing a premature end to the concert due to violence; this concert became known as the Skandalkonzert.", "html": "1913 - The Vienna Concert Society rioted during a performance of <a href=\"https://wikipedia.org/wiki/Modernism_(music)\" title=\"Modernism (music)\">modernist music</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Alban_Berg\" title=\"Alban Berg\">Alban <PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, causing a premature end to the concert due to violence; this concert became known as the <a href=\"https://wikipedia.org/wiki/Skandalkonzert\" title=\"Skandalkonzert\">Skandalkonzert</a>.", "no_year_html": "The Vienna Concert Society rioted during a performance of <a href=\"https://wikipedia.org/wiki/Modernism_(music)\" title=\"Modernism (music)\">modernist music</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Alban_Berg\" title=\"Alban Berg\">Alban <PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, causing a premature end to the concert due to violence; this concert became known as the <a href=\"https://wikipedia.org/wiki/Skandalkonzert\" title=\"Skandalkonzert\">Skandalkonzert</a>.", "links": [{"title": "Modernism (music)", "link": "https://wikipedia.org/wiki/Modernism_(music)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Alba<PERSON>", "link": "https://wikipedia.org/wiki/Alban_Berg"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Skandalkonzert", "link": "https://wikipedia.org/wiki/Skandalkonzert"}]}, {"year": "1917", "text": "According to the terms of the Treaty of the Danish West Indies, the islands become American possessions.", "html": "1917 - According to the terms of the <a href=\"https://wikipedia.org/wiki/Treaty_of_the_Danish_West_Indies\" title=\"Treaty of the Danish West Indies\">Treaty of the Danish West Indies</a>, the islands become American possessions.", "no_year_html": "According to the terms of the <a href=\"https://wikipedia.org/wiki/Treaty_of_the_Danish_West_Indies\" title=\"Treaty of the Danish West Indies\">Treaty of the Danish West Indies</a>, the islands become American possessions.", "links": [{"title": "Treaty of the Danish West Indies", "link": "https://wikipedia.org/wiki/Treaty_of_the_Danish_West_Indies"}]}, {"year": "1918", "text": "Massacre of ethnic Azerbaijanis is committed by allied armed groups of Armenian Revolutionary Federation and Bolsheviks. Nearly 12,000 Azerbaijani Muslims are killed.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/March_Days\" title=\"March Days\">Massacre of ethnic Azerbaijanis</a> is committed by allied armed groups of <a href=\"https://wikipedia.org/wiki/Armenian_Revolutionary_Federation\" title=\"Armenian Revolutionary Federation\">Armenian Revolutionary Federation</a> and <a href=\"https://wikipedia.org/wiki/Bolsheviks\" title=\"Bolsheviks\">Bolsheviks</a>. Nearly 12,000 <a href=\"https://wikipedia.org/wiki/Azerbaijanis\" title=\"Azerbaijanis\">Azerbaijani</a> Muslims are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/March_Days\" title=\"March Days\">Massacre of ethnic Azerbaijanis</a> is committed by allied armed groups of <a href=\"https://wikipedia.org/wiki/Armenian_Revolutionary_Federation\" title=\"Armenian Revolutionary Federation\">Armenian Revolutionary Federation</a> and <a href=\"https://wikipedia.org/wiki/Bolsheviks\" title=\"Bolsheviks\">Bolsheviks</a>. Nearly 12,000 <a href=\"https://wikipedia.org/wiki/Azerbaijanis\" title=\"Azerbaijanis\">Azerbaijani</a> Muslims are killed.", "links": [{"title": "March Days", "link": "https://wikipedia.org/wiki/March_Days"}, {"title": "Armenian Revolutionary Federation", "link": "https://wikipedia.org/wiki/Armenian_Revolutionary_Federation"}, {"title": "Bolsheviks", "link": "https://wikipedia.org/wiki/Bolsheviks"}, {"title": "Azerbaijanis", "link": "https://wikipedia.org/wiki/Azerbaijanis"}]}, {"year": "1918", "text": "Daylight saving time goes into effect in the United States for the first time.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Daylight_saving_time\" title=\"Daylight saving time\">Daylight saving time</a> goes into effect in the United States for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Daylight_saving_time\" title=\"Daylight saving time\">Daylight saving time</a> goes into effect in the United States for the first time.", "links": [{"title": "Daylight saving time", "link": "https://wikipedia.org/wiki/Daylight_saving_time"}]}, {"year": "1921", "text": "The Royal Australian Air Force is formed.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Royal_Australian_Air_Force\" title=\"Royal Australian Air Force\">Royal Australian Air Force</a> is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Australian_Air_Force\" title=\"Royal Australian Air Force\">Royal Australian Air Force</a> is formed.", "links": [{"title": "Royal Australian Air Force", "link": "https://wikipedia.org/wiki/Royal_Australian_Air_Force"}]}, {"year": "1930", "text": "The Motion Picture Production Code is instituted, imposing strict guidelines on the treatment of sex, crime, religion and violence in film, in the U.S., for the next thirty-eight years.", "html": "1930 - The <a href=\"https://wikipedia.org/wiki/Motion_Picture_Production_Code\" class=\"mw-redirect\" title=\"Motion Picture Production Code\">Motion Picture Production Code</a> is instituted, imposing strict guidelines on the treatment of sex, crime, religion and violence in film, in the U.S., for the next thirty-eight years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Motion_Picture_Production_Code\" class=\"mw-redirect\" title=\"Motion Picture Production Code\">Motion Picture Production Code</a> is instituted, imposing strict guidelines on the treatment of sex, crime, religion and violence in film, in the U.S., for the next thirty-eight years.", "links": [{"title": "Motion Picture Production Code", "link": "https://wikipedia.org/wiki/Motion_Picture_Production_Code"}]}, {"year": "1931", "text": "An earthquake in Nicaragua destroys Managua; killing 2,000.", "html": "1931 - An <a href=\"https://wikipedia.org/wiki/1931_Nicaragua_earthquake\" title=\"1931 Nicaragua earthquake\">earthquake in Nicaragua</a> destroys <a href=\"https://wikipedia.org/wiki/Managua\" title=\"Managua\">Managua</a>; killing 2,000.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1931_Nicaragua_earthquake\" title=\"1931 Nicaragua earthquake\">earthquake in Nicaragua</a> destroys <a href=\"https://wikipedia.org/wiki/Managua\" title=\"Managua\">Managua</a>; killing 2,000.", "links": [{"title": "1931 Nicaragua earthquake", "link": "https://wikipedia.org/wiki/1931_Nicaragua_earthquake"}, {"title": "Managua", "link": "https://wikipedia.org/wiki/Managua"}]}, {"year": "1931", "text": "A Transcontinental & Western Air airliner crashes near Bazaar, Kansas, killing eight, including University of Notre Dame head football coach <PERSON><PERSON><PERSON>.", "html": "1931 - A <a href=\"https://wikipedia.org/wiki/1931_Transcontinental_%26_Western_Air_Fokker_F-10_crash\" title=\"1931 Transcontinental &amp; Western Air Fokker F-10 crash\">Transcontinental &amp; Western Air airliner crashes</a> near <a href=\"https://wikipedia.org/wiki/Bazaar,_Kansas\" title=\"Bazaar, Kansas\">Bazaar, Kansas</a>, killing eight, including <a href=\"https://wikipedia.org/wiki/University_of_Notre_Dame\" title=\"University of Notre Dame\">University of Notre Dame</a> head football coach <a href=\"https://wikipedia.org/wiki/Knut<PERSON>_<PERSON>\" title=\"Knute <PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1931_Transcontinental_%26_Western_Air_Fokker_F-10_crash\" title=\"1931 Transcontinental &amp; Western Air Fokker F-10 crash\">Transcontinental &amp; Western Air airliner crashes</a> near <a href=\"https://wikipedia.org/wiki/Bazaar,_Kansas\" title=\"Bazaar, Kansas\">Bazaar, Kansas</a>, killing eight, including <a href=\"https://wikipedia.org/wiki/University_of_Notre_Dame\" title=\"University of Notre Dame\">University of Notre Dame</a> head football coach <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Knute <PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "1931 Transcontinental & Western Air Fokker F-10 crash", "link": "https://wikipedia.org/wiki/1931_Transcontinental_%26_Western_Air_Fokker_F-10_crash"}, {"title": "Bazaar, Kansas", "link": "https://wikipedia.org/wiki/Bazaar,_Kansas"}, {"title": "University of Notre Dame", "link": "https://wikipedia.org/wiki/University_of_Notre_Dame"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ne"}]}, {"year": "1933", "text": "The Civilian Conservation Corps is established with the mission of relieving rampant unemployment in the United States.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/Civilian_Conservation_Corps\" title=\"Civilian Conservation Corps\">Civilian Conservation Corps</a> is established with the mission of relieving rampant unemployment in the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Civilian_Conservation_Corps\" title=\"Civilian Conservation Corps\">Civilian Conservation Corps</a> is established with the mission of relieving rampant unemployment in the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "links": [{"title": "Civilian Conservation Corps", "link": "https://wikipedia.org/wiki/Civilian_Conservation_Corps"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}, {"year": "1939", "text": "Events preceding World War II in Europe: Prime Minister <PERSON> pledges British military support to the Second Polish Republic in the event of an invasion by Nazi Germany.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Events_preceding_World_War_II_in_Europe\" title=\"Events preceding World War II in Europe\">Events preceding World War II in Europe</a>: Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> pledges British military support to the <a href=\"https://wikipedia.org/wiki/Second_Polish_Republic\" title=\"Second Polish Republic\">Second Polish Republic</a> in the event of an invasion by <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Events_preceding_World_War_II_in_Europe\" title=\"Events preceding World War II in Europe\">Events preceding World War II in Europe</a>: Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> pledges British military support to the <a href=\"https://wikipedia.org/wiki/Second_Polish_Republic\" title=\"Second Polish Republic\">Second Polish Republic</a> in the event of an invasion by <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>.", "links": [{"title": "Events preceding World War II in Europe", "link": "https://wikipedia.org/wiki/Events_preceding_World_War_II_in_Europe"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Second Polish Republic", "link": "https://wikipedia.org/wiki/Second_Polish_Republic"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1942", "text": "World War II: Japanese forces invade Christmas Island, then a British possession.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Christmas_Island\" title=\"Battle of Christmas Island\">Japanese forces invade Christmas Island</a>, then a British possession.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Christmas_Island\" title=\"Battle of Christmas Island\">Japanese forces invade Christmas Island</a>, then a British possession.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Christmas Island", "link": "https://wikipedia.org/wiki/Battle_of_Christmas_Island"}]}, {"year": "1945", "text": "World War II: A defecting German pilot delivers a Messerschmitt Me 262A-1, the world's first operational jet-powered fighter aircraft, to the Americans, the first to fall into Allied hands.", "html": "1945 - World War II: A defecting <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> pilot delivers a <a href=\"https://wikipedia.org/wiki/Messerschmitt_Me_262A-1\" class=\"mw-redirect\" title=\"Messerschmitt Me 262A-1\">Messerschmitt Me 262A-1</a>, the world's first operational <a href=\"https://wikipedia.org/wiki/Turbojet\" title=\"Turbojet\">jet-powered</a> <a href=\"https://wikipedia.org/wiki/Fighter_aircraft\" title=\"Fighter aircraft\">fighter aircraft</a>, to the Americans, the first to fall into Allied hands.", "no_year_html": "World War II: A defecting <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> pilot delivers a <a href=\"https://wikipedia.org/wiki/Messerschmitt_Me_262A-1\" class=\"mw-redirect\" title=\"Messerschmitt Me 262A-1\">Messerschmitt Me 262A-1</a>, the world's first operational <a href=\"https://wikipedia.org/wiki/Turbojet\" title=\"Turbojet\">jet-powered</a> <a href=\"https://wikipedia.org/wiki/Fighter_aircraft\" title=\"Fighter aircraft\">fighter aircraft</a>, to the Americans, the first to fall into Allied hands.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Messerschmitt Me 262A-1", "link": "https://wikipedia.org/wiki/Messerschmitt_Me_262A-1"}, {"title": "Turbojet", "link": "https://wikipedia.org/wiki/Turbojet"}, {"title": "Fighter aircraft", "link": "https://wikipedia.org/wiki/Fighter_aircraft"}]}, {"year": "1949", "text": "The Dominion of Newfoundland joins the Canadian Confederation and becomes the 10th Province of Canada.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/Dominion_of_Newfoundland\" title=\"Dominion of Newfoundland\">Dominion of Newfoundland</a> joins the <a href=\"https://wikipedia.org/wiki/Canadian_Confederation\" title=\"Canadian Confederation\">Canadian Confederation</a> and becomes the 10th <a href=\"https://wikipedia.org/wiki/Provinces_and_territories_of_Canada\" title=\"Provinces and territories of Canada\">Province of Canada</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dominion_of_Newfoundland\" title=\"Dominion of Newfoundland\">Dominion of Newfoundland</a> joins the <a href=\"https://wikipedia.org/wiki/Canadian_Confederation\" title=\"Canadian Confederation\">Canadian Confederation</a> and becomes the 10th <a href=\"https://wikipedia.org/wiki/Provinces_and_territories_of_Canada\" title=\"Provinces and territories of Canada\">Province of Canada</a>.", "links": [{"title": "Dominion of Newfoundland", "link": "https://wikipedia.org/wiki/Dominion_of_Newfoundland"}, {"title": "Canadian Confederation", "link": "https://wikipedia.org/wiki/Canadian_Confederation"}, {"title": "Provinces and territories of Canada", "link": "https://wikipedia.org/wiki/Provinces_and_territories_of_Canada"}]}, {"year": "1951", "text": "Remington Rand delivers the first UNIVAC I computer to the United States Census Bureau.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Remington_Rand\" title=\"Remington Rand\">Remington Rand</a> delivers the first <a href=\"https://wikipedia.org/wiki/UNIVAC_I\" title=\"UNIVAC I\">UNIVAC I</a> computer to the <a href=\"https://wikipedia.org/wiki/United_States_Census_Bureau\" title=\"United States Census Bureau\">United States Census Bureau</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Remington_Rand\" title=\"Remington Rand\">Remington Rand</a> delivers the first <a href=\"https://wikipedia.org/wiki/UNIVAC_I\" title=\"UNIVAC I\">UNIVAC I</a> computer to the <a href=\"https://wikipedia.org/wiki/United_States_Census_Bureau\" title=\"United States Census Bureau\">United States Census Bureau</a>.", "links": [{"title": "Remington Rand", "link": "https://wikipedia.org/wiki/Remington_Rand"}, {"title": "UNIVAC I", "link": "https://wikipedia.org/wiki/UNIVAC_I"}, {"title": "United States Census Bureau", "link": "https://wikipedia.org/wiki/United_States_Census_Bureau"}]}, {"year": "1957", "text": "Elections to the Territorial Assembly of the French colony Upper Volta are held. After the elections PDU and MDV form a government.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/1957_Upper_Voltan_Territorial_Assembly_election\" title=\"1957 Upper Voltan Territorial Assembly election\">Elections to the Territorial Assembly</a> of the French colony <a href=\"https://wikipedia.org/wiki/French_Upper_Volta\" title=\"French Upper Volta\">Upper Volta</a> are held. After the elections <a href=\"https://wikipedia.org/wiki/Unified_Democratic_Party\" title=\"Unified Democratic Party\">PDU</a> and <a href=\"https://wikipedia.org/wiki/Voltaic_Democratic_Movement\" title=\"Voltaic Democratic Movement\">MDV</a> form a government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1957_Upper_Voltan_Territorial_Assembly_election\" title=\"1957 Upper Voltan Territorial Assembly election\">Elections to the Territorial Assembly</a> of the French colony <a href=\"https://wikipedia.org/wiki/French_Upper_Volta\" title=\"French Upper Volta\">Upper Volta</a> are held. After the elections <a href=\"https://wikipedia.org/wiki/Unified_Democratic_Party\" title=\"Unified Democratic Party\">PDU</a> and <a href=\"https://wikipedia.org/wiki/Voltaic_Democratic_Movement\" title=\"Voltaic Democratic Movement\">MDV</a> form a government.", "links": [{"title": "1957 Upper Voltan Territorial Assembly election", "link": "https://wikipedia.org/wiki/1957_Upper_Voltan_Territorial_Assembly_election"}, {"title": "French Upper Volta", "link": "https://wikipedia.org/wiki/French_Upper_Volta"}, {"title": "Unified Democratic Party", "link": "https://wikipedia.org/wiki/Unified_Democratic_Party"}, {"title": "Voltaic Democratic Movement", "link": "https://wikipedia.org/wiki/Voltaic_Democratic_Movement"}]}, {"year": "1958", "text": "In the Canadian federal election, the Progressive Conservatives, led by <PERSON>, win the largest percentage of seats in Canadian history, with 208 seats of 265.", "html": "1958 - In the <a href=\"https://wikipedia.org/wiki/1958_Canadian_federal_election\" title=\"1958 Canadian federal election\">Canadian federal election</a>, the <a href=\"https://wikipedia.org/wiki/Progressive_Conservative_Party_of_Canada\" title=\"Progressive Conservative Party of Canada\">Progressive Conservatives</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, win the largest percentage of seats in Canadian history, with 208 seats of 265.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/1958_Canadian_federal_election\" title=\"1958 Canadian federal election\">Canadian federal election</a>, the <a href=\"https://wikipedia.org/wiki/Progressive_Conservative_Party_of_Canada\" title=\"Progressive Conservative Party of Canada\">Progressive Conservatives</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, win the largest percentage of seats in Canadian history, with 208 seats of 265.", "links": [{"title": "1958 Canadian federal election", "link": "https://wikipedia.org/wiki/1958_Canadian_federal_election"}, {"title": "Progressive Conservative Party of Canada", "link": "https://wikipedia.org/wiki/Progressive_Conservative_Party_of_Canada"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "The 14th Dalai Lama, crosses the border into India and is granted political asylum.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/14th_<PERSON><PERSON>_Lama\" title=\"14th Dalai Lama\">14th Dalai Lama</a>, crosses the border into India and is granted <a href=\"https://wikipedia.org/wiki/Political_asylum\" class=\"mw-redirect\" title=\"Political asylum\">political asylum</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/14th_<PERSON><PERSON>_Lama\" title=\"14th Dalai Lama\">14th Dalai Lama</a>, crosses the border into India and is granted <a href=\"https://wikipedia.org/wiki/Political_asylum\" class=\"mw-redirect\" title=\"Political asylum\">political asylum</a>.", "links": [{"title": "14th Dalai Lama", "link": "https://wikipedia.org/wiki/14th_<PERSON><PERSON>_<PERSON>"}, {"title": "Political asylum", "link": "https://wikipedia.org/wiki/Political_asylum"}]}, {"year": "1964", "text": "Brazilian General <PERSON><PERSON><PERSON><PERSON><PERSON> orders his troops to move towards Rio de Janeiro, beginning the coup d'état and 21 years of military dictatorship.", "html": "1964 - Brazilian General <a href=\"https://wikipedia.org/wiki/Ol%C3%ADmpio_Mour%C3%A3o_Fil<PERSON>\" title=\"<PERSON>límpio Mourão Filho\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> orders his troops to <a href=\"https://wikipedia.org/wiki/Operation_Popeye_(Brazil)\" title=\"Operation Popeye (Brazil)\">move towards Rio de Janeiro</a>, beginning the <a href=\"https://wikipedia.org/wiki/1964_Brazilian_coup_d%27%C3%A9tat\" title=\"1964 Brazilian coup d'état\">coup d'état</a> and 21 years of <a href=\"https://wikipedia.org/wiki/Military_dictatorship_in_Brazil\" title=\"Military dictatorship in Brazil\">military dictatorship</a>.", "no_year_html": "Brazilian General <a href=\"https://wikipedia.org/wiki/Ol%C3%ADmpio_Mour%C3%A3o_Filho\" title=\"Olímpio Mourão Filho\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> orders his troops to <a href=\"https://wikipedia.org/wiki/Operation_Popeye_(Brazil)\" title=\"Operation Popeye (Brazil)\">move towards Rio de Janeiro</a>, beginning the <a href=\"https://wikipedia.org/wiki/1964_Brazilian_coup_d%27%C3%A9tat\" title=\"1964 Brazilian coup d'état\">coup d'état</a> and 21 years of <a href=\"https://wikipedia.org/wiki/Military_dictatorship_in_Brazil\" title=\"Military dictatorship in Brazil\">military dictatorship</a>.", "links": [{"title": "Olímp<PERSON>", "link": "https://wikipedia.org/wiki/Ol%C3%ADmpio_Mour%C3%A3o_Filho"}, {"title": "Operation Popeye (Brazil)", "link": "https://wikipedia.org/wiki/Operation_Popeye_(Brazil)"}, {"title": "1964 Brazilian coup d'état", "link": "https://wikipedia.org/wiki/1964_Brazilian_coup_d%27%C3%A9tat"}, {"title": "Military dictatorship in Brazil", "link": "https://wikipedia.org/wiki/Military_dictatorship_in_Brazil"}]}, {"year": "1966", "text": "The Soviet Union launches Luna 10 which later becomes the first space probe to enter orbit around the Moon.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches <a href=\"https://wikipedia.org/wiki/Luna_10\" title=\"Luna 10\">Luna 10</a> which later becomes the first <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> to enter orbit around the Moon.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches <a href=\"https://wikipedia.org/wiki/Luna_10\" title=\"Luna 10\">Luna 10</a> which later becomes the first <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> to enter orbit around the Moon.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Luna 10", "link": "https://wikipedia.org/wiki/Luna_10"}, {"title": "Space probe", "link": "https://wikipedia.org/wiki/Space_probe"}]}, {"year": "1966", "text": "The Labour Party under <PERSON> wins the 1966 United Kingdom general election.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour Party</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/1966_United_Kingdom_general_election\" title=\"1966 United Kingdom general election\">1966 United Kingdom general election</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour Party</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/1966_United_Kingdom_general_election\" title=\"1966 United Kingdom general election\">1966 United Kingdom general election</a>.", "links": [{"title": "Labour Party (UK)", "link": "https://wikipedia.org/wiki/Labour_Party_(UK)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1966 United Kingdom general election", "link": "https://wikipedia.org/wiki/1966_United_Kingdom_general_election"}]}, {"year": "1968", "text": "American President <PERSON> speaks to the nation of \"Steps to Limit the War in Vietnam\" in a television address. At the conclusion of his speech, he announces: \"I shall not seek, and I will not accept, the nomination of my party for another term as your President.\"", "html": "1968 - American <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> speaks to the nation of \"Steps to Limit the War in Vietnam\" in a television address. At the conclusion of his speech, he announces: \"I shall not seek, and I will not accept, the nomination of my party for another term as your President.\"", "no_year_html": "American <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> speaks to the nation of \"Steps to Limit the War in Vietnam\" in a television address. At the conclusion of his speech, he announces: \"I shall not seek, and I will not accept, the nomination of my party for another term as your President.\"", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "Explorer 1 re-enters the Earth's atmosphere after 12 years in orbit.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Explorer_1\" title=\"Explorer 1\">Explorer 1</a> re-enters the <a href=\"https://wikipedia.org/wiki/Atmosphere_of_Earth\" title=\"Atmosphere of Earth\">Earth's atmosphere</a> after 12 years in orbit.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Explorer_1\" title=\"Explorer 1\">Explorer 1</a> re-enters the <a href=\"https://wikipedia.org/wiki/Atmosphere_of_Earth\" title=\"Atmosphere of Earth\">Earth's atmosphere</a> after 12 years in orbit.", "links": [{"title": "Explorer 1", "link": "https://wikipedia.org/wiki/Explorer_1"}, {"title": "Atmosphere of Earth", "link": "https://wikipedia.org/wiki/Atmosphere_of_Earth"}]}, {"year": "1980", "text": "The Chicago, Rock Island and Pacific Railroad operates its final train after being ordered to liquidate its assets because of bankruptcy and debts owed to creditors.", "html": "1980 - The <a href=\"https://wikipedia.org/wiki/Chicago,_Rock_Island_and_Pacific_Railroad\" title=\"Chicago, Rock Island and Pacific Railroad\">Chicago, Rock Island and Pacific Railroad</a> operates its final train after being ordered to <a href=\"https://wikipedia.org/wiki/Liquidation\" title=\"Liquidation\">liquidate</a> its assets because of <a href=\"https://wikipedia.org/wiki/Bankruptcy\" title=\"Bankruptcy\">bankruptcy</a> and <a href=\"https://wikipedia.org/wiki/Debt\" title=\"Debt\">debts</a> owed to <a href=\"https://wikipedia.org/wiki/Creditor\" title=\"Creditor\">creditors</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chicago,_Rock_Island_and_Pacific_Railroad\" title=\"Chicago, Rock Island and Pacific Railroad\">Chicago, Rock Island and Pacific Railroad</a> operates its final train after being ordered to <a href=\"https://wikipedia.org/wiki/Liquidation\" title=\"Liquidation\">liquidate</a> its assets because of <a href=\"https://wikipedia.org/wiki/Bankruptcy\" title=\"Bankruptcy\">bankruptcy</a> and <a href=\"https://wikipedia.org/wiki/Debt\" title=\"Debt\">debts</a> owed to <a href=\"https://wikipedia.org/wiki/Creditor\" title=\"Creditor\">creditors</a>.", "links": [{"title": "Chicago, Rock Island and Pacific Railroad", "link": "https://wikipedia.org/wiki/Chicago,_Rock_Island_and_Pacific_Railroad"}, {"title": "Liquidation", "link": "https://wikipedia.org/wiki/Liquidation"}, {"title": "Bankruptcy", "link": "https://wikipedia.org/wiki/Bankruptcy"}, {"title": "Debt", "link": "https://wikipedia.org/wiki/Debt"}, {"title": "Creditor", "link": "https://wikipedia.org/wiki/Creditor"}]}, {"year": "1986", "text": "Mexicana de Aviación Flight 940 crashes into the Sierra Madre Oriental mountain range near the Mexican town of Maravatío, killing 167.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Mexicana_de_Aviaci%C3%B3n_Flight_940\" title=\"Mexicana de Aviación Flight 940\">Mexicana de Aviación Flight 940</a> crashes into the <a href=\"https://wikipedia.org/wiki/Sierra_Madre_Oriental\" title=\"Sierra Madre Oriental\">Sierra Madre Oriental</a> mountain range near the Mexican town of <a href=\"https://wikipedia.org/wiki/Maravat%C3%ADo\" title=\"Maravatío\">Maravatío</a>, killing 167.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexicana_de_Aviaci%C3%B3n_Flight_940\" title=\"Mexicana de Aviación Flight 940\">Mexicana de Aviación Flight 940</a> crashes into the <a href=\"https://wikipedia.org/wiki/Sierra_Madre_Oriental\" title=\"Sierra Madre Oriental\">Sierra Madre Oriental</a> mountain range near the Mexican town of <a href=\"https://wikipedia.org/wiki/Maravat%C3%ADo\" title=\"Maravatío\">Maravatío</a>, killing 167.", "links": [{"title": "Mexicana de Aviación Flight 940", "link": "https://wikipedia.org/wiki/Mexicana_de_Aviaci%C3%B3n_Flight_940"}, {"title": "Sierra Madre Oriental", "link": "https://wikipedia.org/wiki/Sierra_Madre_Oriental"}, {"title": "Maravatío", "link": "https://wikipedia.org/wiki/Maravat%C3%ADo"}]}, {"year": "1990", "text": "Approximately 200,000 protesters take to the streets of London to protest against the newly introduced Poll Tax.", "html": "1990 - Approximately <a href=\"https://wikipedia.org/wiki/Poll_Tax_Riots\" class=\"mw-redirect\" title=\"Poll Tax Riots\">200,000 protesters take to the streets of London</a> to protest against the newly introduced <a href=\"https://wikipedia.org/wiki/Poll_tax_(Great_Britain)\" title=\"Poll tax (Great Britain)\">Poll Tax</a>.", "no_year_html": "Approximately <a href=\"https://wikipedia.org/wiki/Poll_Tax_Riots\" class=\"mw-redirect\" title=\"Poll Tax Riots\">200,000 protesters take to the streets of London</a> to protest against the newly introduced <a href=\"https://wikipedia.org/wiki/Poll_tax_(Great_Britain)\" title=\"Poll tax (Great Britain)\">Poll Tax</a>.", "links": [{"title": "Poll Tax Riots", "link": "https://wikipedia.org/wiki/Poll_Tax_Riots"}, {"title": "Poll tax (Great Britain)", "link": "https://wikipedia.org/wiki/Poll_tax_(Great_Britain)"}]}, {"year": "1991", "text": "Georgian independence referendum: Nearly 99 percent of the voters support the country's independence from the Soviet Union.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/1991_Georgian_independence_referendum\" title=\"1991 Georgian independence referendum\">Georgian independence referendum</a>: Nearly 99 percent of the voters support the country's independence from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1991_Georgian_independence_referendum\" title=\"1991 Georgian independence referendum\">Georgian independence referendum</a>: Nearly 99 percent of the voters support the country's independence from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "1991 Georgian independence referendum", "link": "https://wikipedia.org/wiki/1991_Georgian_independence_referendum"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1991", "text": "The Warsaw Pact formally disbands.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Warsaw_Pact\" title=\"Warsaw Pact\">Warsaw Pact</a> formally disbands.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Warsaw_Pact\" title=\"Warsaw Pact\">Warsaw Pact</a> formally disbands.", "links": [{"title": "Warsaw Pact", "link": "https://wikipedia.org/wiki/Warsaw_Pact"}]}, {"year": "1992", "text": "The USS Missouri, the last active United States Navy battleship, is decommissioned in Long Beach, California.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/USS_Missouri_(BB-63)\" title=\"USS Missouri (BB-63)\">USS <i>Missouri</i></a>, the last active <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> <a href=\"https://wikipedia.org/wiki/Battleship\" title=\"Battleship\">battleship</a>, is decommissioned in <a href=\"https://wikipedia.org/wiki/Long_Beach,_California\" title=\"Long Beach, California\">Long Beach, California</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/USS_Missouri_(BB-63)\" title=\"USS Missouri (BB-63)\">USS <i>Missouri</i></a>, the last active <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> <a href=\"https://wikipedia.org/wiki/Battleship\" title=\"Battleship\">battleship</a>, is decommissioned in <a href=\"https://wikipedia.org/wiki/Long_Beach,_California\" title=\"Long Beach, California\">Long Beach, California</a>.", "links": [{"title": "USS Missouri (BB-63)", "link": "https://wikipedia.org/wiki/USS_Missouri_(BB-63)"}, {"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "Battleship", "link": "https://wikipedia.org/wiki/Battleship"}, {"title": "Long Beach, California", "link": "https://wikipedia.org/wiki/Long_Beach,_California"}]}, {"year": "1992", "text": "The Treaty of Federation is signed in Moscow.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Federation\" title=\"Treaty of Federation\">Treaty of Federation</a> is signed in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Federation\" title=\"Treaty of Federation\">Treaty of Federation</a> is signed in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>.", "links": [{"title": "Treaty of Federation", "link": "https://wikipedia.org/wiki/Treaty_of_Federation"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}]}, {"year": "1993", "text": "The Macao Basic Law is adopted by the Eighth National People's Congress of China to take effect December 20, 1999. Resumption by China of the Exercise of Sovereignty over Macao", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Macao_Basic_Law\" title=\"Macao Basic Law\">Macao Basic Law</a> is adopted by the <a href=\"https://wikipedia.org/wiki/8th_National_People%27s_Congress\" title=\"8th National People's Congress\">Eighth National People's Congress of China</a> to take effect December 20, 1999. <a rel=\"nofollow\" class=\"external text\" href=\"https://wikipedia.orghttps://www.fmprc.gov.cn/mfa_eng/ziliao_665539/3602_665543/3604_665547/t18052.shtml\">Resumption by China of the Exercise of Sovereignty over Macao</a>", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Macao_Basic_Law\" title=\"Macao Basic Law\">Macao Basic Law</a> is adopted by the <a href=\"https://wikipedia.org/wiki/8th_National_People%27s_Congress\" title=\"8th National People's Congress\">Eighth National People's Congress of China</a> to take effect December 20, 1999. <a rel=\"nofollow\" class=\"external text\" href=\"https://wikipedia.orghttps://www.fmprc.gov.cn/mfa_eng/ziliao_665539/3602_665543/3604_665547/t18052.shtml\">Resumption by China of the Exercise of Sovereignty over Macao</a>", "links": [{"title": "Macao Basic Law", "link": "https://wikipedia.org/wiki/Macao_Basic_Law"}, {"title": "8th National People's Congress", "link": "https://wikipedia.org/wiki/8th_National_People%27s_Congress"}]}, {"year": "1995", "text": "<PERSON> is murdered by her fan club president <PERSON><PERSON><PERSON> at a Days Inn in Corpus Christi, Texas.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Selena\" title=\"Selena\">Selena</a> is <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>\" title=\"Murder of Selena\">murdered</a> by her fan club president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d%C3%ADvar\" title=\"Yolanda <PERSON>\"><PERSON><PERSON><PERSON></a> at a <a href=\"https://wikipedia.org/wiki/Days_Inn\" title=\"Days Inn\">Days Inn</a> in <a href=\"https://wikipedia.org/wiki/Corpus_Christi,_Texas\" title=\"Corpus Christi, Texas\">Corpus Christi, Texas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Selena\" title=\"<PERSON>\">Selena</a> is <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>\" title=\"Murder of Selena\">murdered</a> by her fan club president <a href=\"https://wikipedia.org/wiki/Yo<PERSON><PERSON>_<PERSON>d%C3%ADvar\" title=\"Yolanda Saldívar\"><PERSON><PERSON><PERSON></a> at a <a href=\"https://wikipedia.org/wiki/Days_Inn\" title=\"Days Inn\">Days Inn</a> in <a href=\"https://wikipedia.org/wiki/Corpus_Christi,_Texas\" title=\"Corpus Christi, Texas\">Corpus Christi, Texas</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Selena"}, {"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a_Sald%C3%ADvar"}, {"title": "Days Inn", "link": "https://wikipedia.org/wiki/Days_Inn"}, {"title": "Corpus Christi, Texas", "link": "https://wikipedia.org/wiki/Corpus_Christi,_Texas"}]}, {"year": "1995", "text": "TAROM Flight 371, an Airbus A310-300, crashes near Balotesti, Romania, killing all 60 people on board.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/TAROM_Flight_371\" title=\"TAROM Flight 371\">TAROM Flight 371</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A310\" title=\"Airbus A310\">Airbus A310-300</a>, crashes near <a href=\"https://wikipedia.org/wiki/Balote%C8%99ti\" title=\"Balotești\">Balotesti</a>, Romania, killing all 60 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TAROM_Flight_371\" title=\"TAROM Flight 371\">TAROM Flight 371</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A310\" title=\"Airbus A310\">Airbus A310-300</a>, crashes near <a href=\"https://wikipedia.org/wiki/Balote%C8%99ti\" title=\"Balotești\">Balotesti</a>, Romania, killing all 60 people on board.", "links": [{"title": "TAROM Flight 371", "link": "https://wikipedia.org/wiki/TAROM_Flight_371"}, {"title": "Airbus A310", "link": "https://wikipedia.org/wiki/Airbus_A310"}, {"title": "Balotești", "link": "https://wikipedia.org/wiki/Balote%C8%99ti"}]}, {"year": "1998", "text": "Netscape releases Mozilla source code under an open source license.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Netscape\" title=\"Netscape\">Netscape</a> releases <a href=\"https://wikipedia.org/wiki/Mozilla\" title=\"Mozilla\">Mozilla</a> source code under an open source license.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Netscape\" title=\"Netscape\">Netscape</a> releases <a href=\"https://wikipedia.org/wiki/Mozilla\" title=\"Mozilla\">Mozilla</a> source code under an open source license.", "links": [{"title": "Netscape", "link": "https://wikipedia.org/wiki/Netscape"}, {"title": "Mozilla", "link": "https://wikipedia.org/wiki/Mozilla"}]}, {"year": "2004", "text": "Iraq War in Anbar Province: In Fallujah, Iraq, four American private military contractors working for Blackwater USA, are killed after being ambushed.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Iraq_War_in_Anbar_Province\" class=\"mw-redirect\" title=\"Iraq War in Anbar Province\">Iraq War in Anbar Province</a>: In <a href=\"https://wikipedia.org/wiki/Fallujah\" title=\"Fallujah\">Fallujah</a>, Iraq, four American <a href=\"https://wikipedia.org/wiki/Private_military_contractor\" class=\"mw-redirect\" title=\"Private military contractor\">private military contractors</a> working for <a href=\"https://wikipedia.org/wiki/Blackwater_USA\" class=\"mw-redirect\" title=\"Blackwater USA\">Blackwater USA</a>, are killed after being <a href=\"https://wikipedia.org/wiki/2004_Fallujah_ambush\" title=\"2004 Fallujah ambush\">ambushed</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War_in_Anbar_Province\" class=\"mw-redirect\" title=\"Iraq War in Anbar Province\">Iraq War in Anbar Province</a>: In <a href=\"https://wikipedia.org/wiki/Fallujah\" title=\"Fallujah\">Fallujah</a>, Iraq, four American <a href=\"https://wikipedia.org/wiki/Private_military_contractor\" class=\"mw-redirect\" title=\"Private military contractor\">private military contractors</a> working for <a href=\"https://wikipedia.org/wiki/Blackwater_USA\" class=\"mw-redirect\" title=\"Blackwater USA\">Blackwater USA</a>, are killed after being <a href=\"https://wikipedia.org/wiki/2004_Fallujah_ambush\" title=\"2004 Fallujah ambush\">ambushed</a>.", "links": [{"title": "Iraq War in Anbar Province", "link": "https://wikipedia.org/wiki/Iraq_War_in_Anbar_Province"}, {"title": "Fallujah", "link": "https://wikipedia.org/wiki/Fallujah"}, {"title": "Private military contractor", "link": "https://wikipedia.org/wiki/Private_military_contractor"}, {"title": "Blackwater USA", "link": "https://wikipedia.org/wiki/Blackwater_USA"}, {"title": "2004 Fallujah ambush", "link": "https://wikipedia.org/wiki/2004_Fallujah_ambush"}]}, {"year": "2005", "text": "The dwarf planet <PERSON><PERSON><PERSON> is discovered by a team led by astronomer <PERSON> at the Palomar Observatory.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Dwarf_planet\" title=\"Dwarf planet\">dwarf planet</a> <a href=\"https://wikipedia.org/wiki/Makemake\" title=\"Makemake\"><PERSON>ma<PERSON></a> is discovered by a team led by astronomer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Palomar_Observatory\" title=\"Palomar Observatory\">Palomar Observatory</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dwarf_planet\" title=\"Dwarf planet\">dwarf planet</a> <a href=\"https://wikipedia.org/wiki/Makemake\" title=\"Makemake\"><PERSON>ma<PERSON></a> is discovered by a team led by astronomer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Palomar_Observatory\" title=\"Palomar Observatory\">Palomar Observatory</a>.", "links": [{"title": "Dwarf planet", "link": "https://wikipedia.org/wiki/Dwarf_planet"}, {"title": "Makemake", "link": "https://wikipedia.org/wiki/Makemake"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Palomar Observatory", "link": "https://wikipedia.org/wiki/Palomar_Observatory"}]}, {"year": "2016", "text": "NASA astronaut <PERSON> and Roscosmos cosmonaut <PERSON> return to Earth after a yearlong mission at the International Space Station.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Roscosmos\" title=\"Roscosmos\">Roscosmos</a> cosmonaut <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> return to Earth after a <a href=\"https://wikipedia.org/wiki/ISS_year-long_mission\" title=\"ISS year-long mission\">yearlong mission</a> at the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Roscosmos\" title=\"Roscosmos\">Roscosmos</a> cosmonaut <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> return to Earth after a <a href=\"https://wikipedia.org/wiki/ISS_year-long_mission\" title=\"ISS year-long mission\">yearlong mission</a> at the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(astronaut)"}, {"title": "Roscosmos", "link": "https://wikipedia.org/wiki/Roscosmos"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "ISS year-long mission", "link": "https://wikipedia.org/wiki/ISS_year-long_mission"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2018", "text": "Start of the 2018 Armenian revolution.", "html": "2018 - Start of the <a href=\"https://wikipedia.org/wiki/2018_Armenian_revolution\" class=\"mw-redirect\" title=\"2018 Armenian revolution\">2018 Armenian revolution</a>.", "no_year_html": "Start of the <a href=\"https://wikipedia.org/wiki/2018_Armenian_revolution\" class=\"mw-redirect\" title=\"2018 Armenian revolution\">2018 Armenian revolution</a>.", "links": [{"title": "2018 Armenian revolution", "link": "https://wikipedia.org/wiki/2018_Armenian_revolution"}]}, {"year": "2023", "text": "A historic tornado outbreak occurs in the American Midwest and its northern South", "html": "2023 - A <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_March_31_%E2%80%93_April_1,_2023\" title=\"Tornado outbreak of March 31 - April 1, 2023\">historic tornado outbreak</a> occurs in the American Midwest and its northern South", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_March_31_%E2%80%93_April_1,_2023\" title=\"Tornado outbreak of March 31 - April 1, 2023\">historic tornado outbreak</a> occurs in the American Midwest and its northern South", "links": [{"title": "Tornado outbreak of March 31 - April 1, 2023", "link": "https://wikipedia.org/wiki/Tornado_outbreak_of_March_31_%E2%80%93_April_1,_2023"}]}], "Births": [{"year": "1360", "text": "<PERSON><PERSON> of Lancaster (d. 1415)", "html": "1360 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> of Lancaster\"><PERSON><PERSON> of Lancaster</a> (d. 1415)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> of Lancaster\"><PERSON><PERSON> of Lancaster</a> (d. 1415)", "links": [{"title": "<PERSON><PERSON> of Lancaster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Lancaster"}]}, {"year": "1499", "text": "<PERSON> (d. 1565)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_IV\" title=\"Pope Pius IV\">Pope <PERSON> IV</a> (d. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"Pope Pius IV\"><PERSON> <PERSON> IV</a> (d. 1565)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1504", "text": "<PERSON>, Indian religious leader (d. 1552)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian religious leader (d. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>, Indian religious leader (d. 1552)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON> of France (d. 1559)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (d. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (d. 1559)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1536", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (d. 1565)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/Ashikaga_Yoshiteru\" title=\"Ashikaga Yoshiteru\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (d. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashika<PERSON>_Yoshiteru\" title=\"Ashikaga Yoshiteru\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (d. 1565)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ashikaga_Yoshiteru"}]}, {"year": "1596", "text": "<PERSON>, French mathematician and philosopher (d. 1650)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (d. 1650)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>"}]}, {"year": "1601", "text": "<PERSON><PERSON><PERSON>, Italian linguist and lexicographer (d. 1654)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian linguist and lexicographer (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian linguist and lexicographer (d. 1654)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, English poet and politician (d. 1678)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and politician (d. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and politician (d. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1651", "text": "<PERSON>, Elector <PERSON>, German husband of Princess <PERSON><PERSON> of Denmark (d. 1685)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>ector_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a>, German husband of <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_of_Denmark\" title=\"Princess <PERSON><PERSON> of Denmark\">Princess <PERSON><PERSON> of Denmark</a> (d. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>ector_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a>, German husband of <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_of_Denmark\" title=\"Princess <PERSON><PERSON> of Denmark\">Princess <PERSON><PERSON> of Denmark</a> (d. 1685)", "links": [{"title": "<PERSON>, Elector <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>"}, {"title": "Princess <PERSON><PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON><PERSON>_of_Denmark"}]}, {"year": "1675", "text": "<PERSON> (d. 1758)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\"><PERSON> <PERSON></a> (d. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1685", "text": "<PERSON>, German composer (d. 1750)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON> of Spain (d. 1781)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/Mariana_Victoria_of_Spain\" title=\"Mariana Victoria of Spain\">Mariana Victoria of Spain</a> (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mariana_Victoria_of_Spain\" title=\"Mariana Victoria of Spain\"><PERSON> Victoria of Spain</a> (d. 1781)", "links": [{"title": "Mariana Victoria of Spain", "link": "https://wikipedia.org/wiki/Mariana_Victoria_of_Spain"}]}, {"year": "1723", "text": "<PERSON> Denmark (d. 1766)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/Frederick_V_of_Denmark\" title=\"Frederick V of Denmark\"><PERSON> of Denmark</a> (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frederick_V_of_Denmark\" title=\"Frederick <PERSON> of Denmark\"><PERSON> of Denmark</a> (d. 1766)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/Frederick_V_of_Denmark"}]}, {"year": "1730", "text": "<PERSON>, French mathematician and theorist (d. 1783)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_B%C3%A9zout\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theorist (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_B%C3%A9zout\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theorist (d. 1783)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_B%C3%A9zout"}]}, {"year": "1732", "text": "<PERSON>, Austrian pianist and composer (d. 1809)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1740", "text": "<PERSON><PERSON><PERSON>, Greek politician (d. 1849)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/Panouts<PERSON>_Notaras\" title=\"Panoutsos Notaras\"><PERSON><PERSON><PERSON></a>, Greek politician (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan<PERSON><PERSON>_Notaras\" title=\"Panoutsos Notaras\"><PERSON><PERSON><PERSON></a>, Greek politician (d. 1849)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panoutsos_Notaras"}]}, {"year": "1747", "text": "<PERSON>, German pianist and composer (d. 1800)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, French physicist and engineer (d. 1859)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_la_Tour\" title=\"<PERSON> Tour\"><PERSON></a>, French physicist and engineer (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Tour\" title=\"<PERSON> Tour\"><PERSON></a>, French physicist and engineer (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch zoologist and ornithologist (d. 1858)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch zoologist and ornithologist (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch zoologist and ornithologist (d. 1858)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, American lawyer and politician, 2nd United States Secretary of the Interior (d. 1852)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1809", "text": "<PERSON>, English poet and translator (d. 1883)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and translator (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and translator (d. 1883)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1809", "text": "<PERSON>, Swedish composer (d. 1864)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish composer (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish composer (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Otto_Lindblad"}]}, {"year": "1813", "text": "<PERSON>, Mexican general and unconstitutional interim president (1858 and 1860-1862) (d. 1898)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Mar%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and unconstitutional interim president (1858 and 1860-1862) (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Mar%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and unconstitutional interim president (1858 and 1860-1862) (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Mar%C3%ADa_Zuloaga"}]}, {"year": "1819", "text": "<PERSON><PERSON><PERSON><PERSON>, Prince of Hohenlohe-Schillingsfürst (d. 1901)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Prince_of_Hohenlohe-Schillingsf%C3%BCrst\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prince of Hohenlohe-Schillingsfürst\"><PERSON><PERSON><PERSON><PERSON>, Prince of Hohenlohe-Schillingsfürst</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Prince_of_Hohenlohe-Schillingsf%C3%BCrst\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prince of Hohenlohe-Schillingsfürst\"><PERSON><PERSON><PERSON><PERSON>, Prince of Hohenlohe-Schillingsfürst</a> (d. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Prince of Hohenlohe-Schillingsfürst", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Prince_of_Hohenlohe-Schillingsf%C3%BCrst"}]}, {"year": "1823", "text": "<PERSON>, American author (d. 1886)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Chesnut\" title=\"<PERSON> Boykin Chesnut\"><PERSON>es<PERSON></a>, American author (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Chesnut\" title=\"Mary Boykin Chesnut\"><PERSON> Chesnut</a>, American author (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, American writer and essayist (d. 1896)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and essayist (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and essayist (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, American artist (d. 1910)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, Swiss sailor (d. 1904)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, Swiss sailor (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, Swiss sailor (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pourtal%C3%A8s"}]}, {"year": "1847", "text": "<PERSON><PERSON>, Russian mathematician and theorist (d. 1878)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and theorist (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and theorist (d. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, New Zealand lawyer and politician, 20th Prime Minister of New Zealand (d. 1936)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Zealand_politician)\" title=\"<PERSON> (New Zealand politician)\"><PERSON></a>, New Zealand lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(New_Zealand_politician)\" title=\"<PERSON> (New Zealand politician)\"><PERSON></a>, New Zealand lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1936)", "links": [{"title": "<PERSON> (New Zealand politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Zealand_politician)"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1855", "text": "<PERSON>, American businessman (d. 1899)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, Hungarian actor and screenwriter (d. 1924)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian actor and screenwriter (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian actor and screenwriter (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON>, Indian physician (d. 1887)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian physician (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian physician (d. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1871", "text": "<PERSON>, Irish journalist and politician, 3rd President of Dáil Éireann (d. 1922)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_D%C3%A1il_%C3%89ireann\" title=\"President of Dáil Éireann\">President of Dáil Éireann</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_D%C3%A1il_%C3%89ireann\" title=\"President of Dáil Éireann\">President of Dáil Éireann</a> (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Dáil Éireann", "link": "https://wikipedia.org/wiki/President_of_D%C3%A1il_%C3%89ireann"}]}, {"year": "1872", "text": "<PERSON>, Russian ballet manager and critic, founded the Ballets Russes (d. 1929)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballet manager and critic, founded the <a href=\"https://wikipedia.org/wiki/Ballets_Russes\" title=\"Ballets Russes\">Ballets Russes</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballet manager and critic, founded the <a href=\"https://wikipedia.org/wiki/Ballets_Russes\" title=\"Ballets Russes\">Ballets Russes</a> (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ballets Russes", "link": "https://wikipedia.org/wiki/Ballets_Russes"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican revolutionary general, governor of Sonora (d. 1920)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Benjam%C3%ADn_G._<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Mexican revolutionary general, governor of Sonora (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Benjam%C3%ADn_G._<PERSON>\" title=\"Ben<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican revolutionary general, governor of Sonora (d. 1920)", "links": [{"title": "Benjamín G. Hill", "link": "https://wikipedia.org/wiki/Benjam%C3%ADn_G._Hill"}]}, {"year": "1874", "text": "<PERSON>, French violinist and composer (d. 1934)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Serbian author (d. 1927)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian author (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian author (d. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>av_Stankovi%C4%87"}]}, {"year": "1878", "text": "<PERSON>, American boxer (d. 1946)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (d. 1946)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Dutch-American astronomer and academic (d. 1946)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American astronomer and academic (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American astronomer and academic (d. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Bulgarian-American painter and illustrator (d. 1930)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Pascin\" class=\"mw-redirect\" title=\"Pascin\"><PERSON><PERSON><PERSON></a>, Bulgarian-American painter and illustrator (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pascin\" class=\"mw-redirect\" title=\"Pas<PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian-American painter and illustrator (d. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pascin"}]}, {"year": "1890", "text": "<PERSON>, American jumper (d. 1961)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(track_and_field)\" title=\"<PERSON> (track and field)\"><PERSON></a>, American jumper (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(track_and_field)\" title=\"<PERSON> (track and field)\"><PERSON></a>, American jumper (d. 1961)", "links": [{"title": "<PERSON> (track and field)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(track_and_field)"}]}, {"year": "1890", "text": "<PERSON>, Australian-English physicist and academic, Nobel Prize laureate (d. 1971)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1891", "text": "<PERSON>, Hungarian-American actor and director (d. 1976)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actor and director (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actor and director (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>arconi"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Austrian conductor and manager (d. 1954)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian conductor and manager (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian conductor and manager (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, German physician and historian (d. 1982)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BC<PERSON>pfordt\" title=\"<PERSON>\"><PERSON></a>, German physician and historian (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BChlpfordt\" title=\"<PERSON>\"><PERSON></a>, German physician and historian (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_M%C3%BChlpfordt"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, American author and academic (d. 1968)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and academic (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and academic (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Duke of Gloucester (d. 1974)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester\" title=\"Prince <PERSON>, Duke of Gloucester\">Prince <PERSON>, Duke of Gloucester</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester\" title=\"Prince <PERSON>, Duke of Gloucester\">Prince <PERSON>, Duke of Gloucester</a> (d. 1974)", "links": [{"title": "<PERSON> <PERSON>, Duke of Gloucester", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester"}]}, {"year": "1905", "text": "<PERSON>, English director and screenwriter (d. 1986)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, English director and screenwriter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, English director and screenwriter (d. 1986)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_(director)"}]}, {"year": "1905", "text": "<PERSON>, Australian rugby league player (d. 1991)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese physicist and academic, Nobel Prize laureate (d. 1979)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Sin-Itiro_Tomonaga\" class=\"mw-redirect\" title=\"Sin-Itiro Tomonaga\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sin-Itiro_Tomonaga\" class=\"mw-redirect\" title=\"Sin-Itiro Tomonaga\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sin-<PERSON>iro_<PERSON>ga"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1908", "text": "<PERSON>, American vibraphone player and composer (d. 1999)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Red_Norvo\" title=\"Red Norvo\"><PERSON></a>, American vibraphone player and composer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Norvo\" title=\"Red Norvo\"><PERSON></a>, American vibraphone player and composer (d. 1999)", "links": [{"title": "Red Norvo", "link": "https://wikipedia.org/wiki/Red_Norvo"}]}, {"year": "1911", "text": "<PERSON>, American guitarist (d. 1987)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, German soprano (d. 1986)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gr%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soprano (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soprano (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elisabeth_Gr%C3%BCmmer"}]}, {"year": "1912", "text": "<PERSON>, American soldier and author (d. 2009)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, African-American singer and guitarist (d. 2006)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American singer and guitarist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American singer and guitarist (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Baker"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Mexican poet and diplomat, Nobel Prize laureate (d. 1998)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Octavio_Paz\" title=\"Octavio Paz\"><PERSON><PERSON><PERSON></a>, Mexican poet and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octavio_<PERSON>\" title=\"Octavio Paz\"><PERSON><PERSON><PERSON></a>, Mexican poet and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>avi<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Swedish author (d. 1991)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish author (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English historian and author (d. 1993)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Japanese sergeant (d. 1997)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sergeant (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sergeant (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American voice actress (d. 2012)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American voice actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bliss\"><PERSON><PERSON></a>, American voice actress (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ille_Bliss"}]}, {"year": "1916", "text": "<PERSON>, American golfer (d. 2008)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bo<PERSON>\"><PERSON></a>, American golfer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Jr., American lawyer and judge (d. 1979)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and judge (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and judge (d. 1979)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1917", "text": "<PERSON>, American violinist and educator (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and educator (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and educator (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American director (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ted_<PERSON>\" title=\"Ted Post\"><PERSON></a>, American director (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ted_Post"}]}, {"year": "1919", "text": "<PERSON>, American football player (d. 1993)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Duchess of Devonshire, British aristocrat, socialite and author (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Devonshire\" title=\"<PERSON>, Duchess of Devonshire\"><PERSON>, Duchess of Devonshire</a>, British aristocrat, socialite and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Devonshire\" title=\"<PERSON>, Duchess of Devonshire\"><PERSON>, Duchess of Devonshire</a>, British aristocrat, socialite and author (d. 2014)", "links": [{"title": "<PERSON>, Duchess of Devonshire", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Devonshire"}]}, {"year": "1921", "text": "<PERSON>, African-American blues singer-songwriter and guitarist (d. 1999)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American blues singer-songwriter and guitarist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American blues singer-songwriter and guitarist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actress and casting director (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and casting director (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and casting director (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Norwegian chemical engineer and inventor (d. 1997)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian chemical engineer and inventor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian chemical engineer and inventor (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actor and singer (d. 1999)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Irish actor (d. 1982)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Irish actor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Irish actor (d. 1982)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1923", "text": "<PERSON>, American basketball player (d. 1993)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Don_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Belgian footballer (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>mon"}]}, {"year": "1924", "text": "<PERSON>, American author and academic (d. 1998)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Leo_<PERSON>glia\" title=\"Leo Buscaglia\"><PERSON></a>, American author and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leo_<PERSON>glia\" title=\"Leo Buscaglia\"><PERSON></a>, American author and academic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Buscaglia"}]}, {"year": "1924", "text": "<PERSON>, American director and producer (d. 2002)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian actor and director (d. 1999)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor and director (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor and director (d. 1999)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1926", "text": "<PERSON>, English novelist (d. 2005)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Italian director, set designer, author, and illustrator (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director, set designer, author, and illustrator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director, set designer, author, and illustrator (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beni_Montresor"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American colonel and engineer (d. 2006)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American colonel and engineer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American colonel and engineer (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American labor union leader and activist (d. 1993)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American labor union leader and activist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American labor union leader and activist (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Spanish cardinal (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADnez_Somalo\" title=\"<PERSON>\"><PERSON></a>, Spanish cardinal (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADnez_Somalo\" title=\"<PERSON>\"><PERSON></a>, Spanish cardinal (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eduardo_Mart%C3%ADnez_Somalo"}]}, {"year": "1927", "text": "<PERSON>, Russian pilot (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pilot (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pilot (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_Ilyushin"}]}, {"year": "1927", "text": "<PERSON>, American businessman and politician (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Canadian ice hockey player (d. 1988)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bud_MacPherson"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 1975)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Frizzell\" title=\"<PERSON><PERSON> Frizzell\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Frizzell\" title=\"<PERSON>y Frizzell\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lefty_Frizzell"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Belgian-American fashion designer, founded Liz <PERSON> Inc. (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fashion_designer)\" class=\"mw-redirect\" title=\"<PERSON> (fashion designer)\"><PERSON></a>, Belgian-American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Inc.\" class=\"mw-redirect\" title=\"Liz <PERSON> Inc.\">Liz <PERSON>.</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(fashion_designer)\" class=\"mw-redirect\" title=\"<PERSON> (fashion designer)\"><PERSON></a>, Belgian-American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Inc.\" class=\"mw-redirect\" title=\"Liz <PERSON> Inc.\">Liz <PERSON> Inc.</a> (d. 2007)", "links": [{"title": "<PERSON> (fashion designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fashion_designer)"}, {"title": "Liz <PERSON> Inc.", "link": "https://wikipedia.org/wiki/Liz_<PERSON>_Inc."}]}, {"year": "1929", "text": "<PERSON>, American lawyer and author (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Polish-American psychiatrist (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American psychiatrist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American psychiatrist (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r"}]}, {"year": "1930", "text": "<PERSON>, American football player and coach (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American golfer (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Barber\" title=\"Miller Barber\"><PERSON></a>, American golfer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miller_Barber\" title=\"Miller Barber\"><PERSON></a>, American golfer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Belarusian shot putter (d. 1997)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian shot putter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian shot putter (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American author (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Japanese director and screenwriter (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director and screenwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter and bassist  (d. 1999)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bassist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bassist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Romanian poet (d. 1983)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Nichita_St%C4%83nes<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian poet (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nichita_St%C4%83nescu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian poet (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nichita_St%C4%83nescu"}]}, {"year": "1934", "text": "<PERSON>, American actor", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actress and singer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>mi<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Soviet pilot and cosmonaut (d. 1966)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet pilot and cosmonaut (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet pilot and cosmonaut (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Italian physicist and academic, Nobel Prize laureate", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlo_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Indian poet and author (d. 2009)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and author (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ya"}]}, {"year": "1935", "text": "<PERSON>, American singer-songwriter, trumpet player, and producer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, trumpet player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, trumpet player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American author (d. 2005)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American poet and novelist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and novelist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American economist and academic (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English biologist and academic (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Indian politician, 22nd Governor of Kerala (d. 2019)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian politician, 22nd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Kerala\" class=\"mw-redirect\" title=\"List of Governors of Kerala\">Governor of Kerala</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian politician, 22nd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Kerala\" class=\"mw-redirect\" title=\"List of Governors of Kerala\">Governor of Kerala</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Kerala", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Kerala"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, German runner", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>t<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian ice hockey player, coach, and manager (d. 2005)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American football player (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)\" title=\"<PERSON> (cornerback)\"><PERSON></a>, American football player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)\" title=\"<PERSON> (cornerback)\"><PERSON></a>, American football player (d. 2024)", "links": [{"title": "<PERSON> (cornerback)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian basketball player (d. 2005)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/T%C3%B5nno_<PERSON>ets\" title=\"Tõnn<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian basketball player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%B5nno_Lepmets\" title=\"Tõnn<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian basketball player (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%B5nno_Lepmets"}]}, {"year": "1938", "text": "<PERSON>, American pianist, composer, and conductor (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Scottish academic and politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Georgian anthropologist and politician, 1st President of Georgia (d. 1993)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Zviad_Gamsakhurdia\" title=\"<PERSON>via<PERSON> Gamsakhur<PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian anthropologist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President of Georgia</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zvia<PERSON>_Gamsakhurdia\" title=\"<PERSON>via<PERSON> Gamsakhur<PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian anthropologist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President of Georgia</a> (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zviad_Gamsakhurdia"}, {"title": "President of Georgia", "link": "https://wikipedia.org/wiki/President_of_Georgia"}]}, {"year": "1939", "text": "<PERSON>, American actor, director, and screenwriter (d. 2020)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Israel_Horovitz\" title=\"Israel Horovitz\"><PERSON></a>, American actor, director, and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Horovitz\" title=\"Israel Horovitz\"><PERSON></a>, American actor, director, and screenwriter (d. 2020)", "links": [{"title": "Israel Ho<PERSON>itz", "link": "https://wikipedia.org/wiki/Israel_Horovitz"}]}, {"year": "1939", "text": "<PERSON>, American lawyer and judge (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, German director and producer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Volker_Schl%C3%B6ndorff\" title=\"<PERSON><PERSON>hlöndorff\"><PERSON><PERSON>öndo<PERSON></a>, German director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Volker_Schl%C3%B6ndorff\" title=\"<PERSON><PERSON>hlöndorff\"><PERSON><PERSON>öndorf<PERSON></a>, German director and producer", "links": [{"title": "Volker Schlöndorff", "link": "https://wikipedia.org/wiki/Volker_Schl%C3%B6ndorff"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, German footballer (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1940", "text": "<PERSON>-<PERSON>, English production designer and art director (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English production designer and art director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English production designer and art director (d. 2013)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American lawyer and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American lawyer and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Italian author and illustrator (d. 1995)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Australian swimmer (d. 2013)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Swedish politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American guitarist and producer (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American far-right radio host and author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American far-right radio host and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American far-right radio host and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Swedish director and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, English costume designer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English costume designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English costume designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, French singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Angus King\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Angus King\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_King"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American computer scientist and engineer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor and comedian", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Welsh actress (d. 1995)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>log\"><PERSON><PERSON><PERSON></a>, Welsh actress (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Myfan<PERSON>_<PERSON>log"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Venezuelan baseball player (d. 1984)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Gonzalo_M%C3%A1rquez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gonzalo_M%C3%A1rquez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gonzalo_M%C3%A1rquez"}]}, {"year": "1946", "text": "<PERSON>, English politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Rwandan-American mathematician and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Augustin_<PERSON>\" title=\"Augustin <PERSON>\"><PERSON><PERSON></a>, Rwandan-American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augustin_<PERSON>\" title=\"Augustin <PERSON>\"><PERSON><PERSON></a>, Rwandan-American mathematician and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Augustin_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American tennis player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Danish-Faroese pianist, composer, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-Faroese pianist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-Faroese pianist, composer, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English academic and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, English academic and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1947", "text": "<PERSON>, Colombian economist and politician, 36th President of Colombia", "html": "1947 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian economist and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian economist and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_<PERSON><PERSON>ria"}, {"title": "President of Colombia", "link": "https://wikipedia.org/wiki/President_of_Colombia"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Israeli physicist and economist (d. 2011)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli physicist and economist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>t\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli physicist and economist (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian politician and diplomat, 20th Premier of Manitoba", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician and diplomat, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Manitoba\" title=\"Premier of Manitoba\">Premier of Manitoba</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician and diplomat, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Manitoba\" title=\"Premier of Manitoba\">Premier of Manitoba</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Manitoba", "link": "https://wikipedia.org/wiki/Premier_of_Manitoba"}]}, {"year": "1948", "text": "<PERSON>, American soldier and politician, 45th Vice President of the United States and Nobel Prize laureate", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 45th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 45th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gore"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "Gus<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian ice hockey player (d. 2023)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Hungarian chess player and author (d. 2023)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A1s_Adorj%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian chess player and author (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A1s_Adorj%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian chess player and author (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A1s_Adorj%C3%A1n"}]}, {"year": "1950", "text": "<PERSON>, American football player and actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American anthropologist and academic (d. 2016)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American guitarist and composer (d. 2014)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, President of Serbia and Montenegro", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Svetozar_Marovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, President of Serbia and Montenegro", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Svetozar_Marovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, President of Serbia and Montenegro", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Svetozar_Marovi%C4%87"}]}, {"year": "1955", "text": "<PERSON>, Scottish-Australian guitarist and songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English businessman and politician, former Shadow Leader of the House of Commons", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, former <a href=\"https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons\" title=\"Shadow Leader of the House of Commons\">Shadow Leader of the House of Commons</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, former <a href=\"https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons\" title=\"Shadow Leader of the House of Commons\">Shadow Leader of the House of Commons</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shadow Leader of the House of Commons", "link": "https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons"}]}, {"year": "1958", "text": "<PERSON>, Austrian politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Swiss poet and translator", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss poet and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss poet and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American sprinter and football player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American sprinter and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American sprinter and football player", "links": [{"title": "<PERSON> (wide receiver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)"}]}, {"year": "1961", "text": "<PERSON>, American screenwriter and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Finnish footballer and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Greek boxer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian actor and dancer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English accountant and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American ice hockey player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American tennis player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fendick\" title=\"<PERSON> Fendick\"><PERSON></a>, American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Fendick\" title=\"Patty Fendick\"><PERSON></a>, American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, French mountaineer (d. 2006)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mountaineer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mountaineer (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American author and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English runner and journalist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a>, English runner and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American race car driver", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nick_Firestone"}]}, {"year": "1968", "text": "<PERSON>, Brazilian footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Sampaio"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Burundian-Swedish politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>yam<PERSON>_<PERSON>\" title=\"<PERSON>yam<PERSON>bu<PERSON>\"><PERSON><PERSON><PERSON></a>, Burundian-Swedish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>yam<PERSON>bu<PERSON>\"><PERSON><PERSON><PERSON></a>, Burundian-Swedish politician", "links": [{"title": "Nyamko <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American basketball player and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Slovenian politician, 7th Prime Minister of Slovenia", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Alenka_Bratu%C5%A1ek\" title=\"<PERSON>en<PERSON> Bratušek\"><PERSON><PERSON><PERSON></a>, Slovenian politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovenia\" title=\"Prime Minister of Slovenia\">Prime Minister of Slovenia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alenka_Bratu%C5%A1ek\" title=\"<PERSON>en<PERSON> Bratušek\"><PERSON><PERSON><PERSON></a>, Slovenian politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovenia\" title=\"Prime Minister of Slovenia\">Prime Minister of Slovenia</a>", "links": [{"title": "<PERSON><PERSON><PERSON> B<PERSON>uš<PERSON>", "link": "https://wikipedia.org/wiki/Alenka_Bratu%C5%A1ek"}, {"title": "Prime Minister of Slovenia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Slovenia"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Norwegian actress and writer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Linn_Sk%C3%A5ber\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian actress and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Linn_Sk%C3%A5ber\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian actress and writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Linn_Sk%C3%A5ber"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Cypriot footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_As<PERSON>tis\" title=\"Demetris Assiotis\"><PERSON><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_<PERSON>\" title=\"De<PERSON>ris Assiotis\"><PERSON><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demetris_Assiotis"}]}, {"year": "1971", "text": "<PERSON>, English footballer and referee", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Russian ice hockey player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American animator, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Scottish actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Chilean-Spanish director and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1bar\" title=\"<PERSON>\"><PERSON></a>, Chilean-Spanish director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1bar\" title=\"<PERSON>\"><PERSON></a>, Chilean-Spanish director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Amen%C3%A1bar"}]}, {"year": "1972", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Italian footballer and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1972)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1972)\"><PERSON></a>, Italian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1972)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1972)\"><PERSON></a>, Italian footballer and coach", "links": [{"title": "<PERSON> (footballer born 1972)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1972)"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Greek hammer thrower", "html": "1972 - <a href=\"https://wikipedia.org/wiki/H<PERSON><PERSON>_<PERSON>ihroniou\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ihronio<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek hammer thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>s_Polihroniou"}]}, {"year": "1972", "text": "<PERSON>, American businessman, co-founded Twitter and Pyra Labs", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Internet_entrepreneur)\" title=\"<PERSON> (Internet entrepreneur)\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Twitter\" title=\"Twitter\">Twitter</a> and <a href=\"https://wikipedia.org/wiki/Pyra_Labs\" title=\"Pyra Labs\">Pyra Labs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Internet_entrepreneur)\" title=\"<PERSON> (Internet entrepreneur)\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Twitter\" title=\"Twitter\">Twitter</a> and <a href=\"https://wikipedia.org/wiki/Pyra_Labs\" title=\"Pyra Labs\">Pyra Labs</a>", "links": [{"title": "<PERSON> (Internet entrepreneur)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Internet_entrepreneur)"}, {"title": "Twitter", "link": "https://wikipedia.org/wiki/Twitter"}, {"title": "Pyra Labs", "link": "https://wikipedia.org/wiki/Pyra_Labs"}]}, {"year": "1973", "text": "<PERSON>, English ballet dancer and choreographer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballet dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballet dancer and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, German director, producer, and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Russian singer, composer and songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Russian singer, composer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Russian singer, composer and songwriter", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1974", "text": "<PERSON>, Swedish bass player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Finnish swimmer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)"}]}, {"year": "1975", "text": "<PERSON>, Australian rugby player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1975", "text": "<PERSON>, Scottish rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Scottish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Scottish rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1975", "text": "<PERSON>, American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Latvian-Russian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Igors_S%C4%BCesar%C4%8Duks\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian-Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Igors_S%C4%BCesar%C4%8Duks\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian-Russian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Igors_S%C4%BCesar%C4%8Duks"}]}, {"year": "1976", "text": "<PERSON>, Scottish swimmer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Scottish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Scottish swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>(swimmer)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Japanese bass player, songwriter, and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Japanese bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Japanese bass player, songwriter, and producer", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)"}]}, {"year": "1977", "text": "<PERSON>, Australian race car driver", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian cricketer and footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(sportsman)\" title=\"<PERSON> (sportsman)\"><PERSON></a>, Australian cricketer and footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(sportsman)\" title=\"<PERSON> (sportsman)\"><PERSON></a>, Australian cricketer and footballer", "links": [{"title": "<PERSON> (sportsman)", "link": "https://wikipedia.org/wiki/<PERSON>_(sportsman)"}]}, {"year": "1978", "text": "<PERSON>, English footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_<PERSON>en"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Israeli footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Scottish martial artist and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish martial artist and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish martial artist and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Argentinian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American skier", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian rugby league player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Danish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Swedish lawyer and blogger", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish lawyer and blogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish lawyer and blogger", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bo"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Swedish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>cha"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter, guitarist, and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Japanese actress, voice actress and singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress, voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress, voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Belgian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South Korean footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>-you", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-you"}]}, {"year": "1981", "text": "<PERSON>, Gambian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Pa_Dembo_Touray\" title=\"Pa Dembo Touray\"><PERSON> De<PERSON> Touray</a>, Gambian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pa_Dembo_Touray\" title=\"Pa Dembo Touray\"><PERSON> Touray</a>, Gambian footballer", "links": [{"title": "Pa Dembo Touray", "link": "https://wikipedia.org/wiki/Pa_Dembo_Touray"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Dutch swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> van <PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> van <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Tal <PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Tal <PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American author, artist, and sex educator", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, artist, and sex educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, artist, and sex educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Childress\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Childress\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ress"}]}, {"year": "1982", "text": "<PERSON>, American actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American painter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Taiwanese baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Taiwanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Taiwanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, South African cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>la"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Canadian voice actress and musician", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Ashleigh_Ball\" title=\"Ashleigh Ball\"><PERSON><PERSON> Ball</a>, Canadian voice actress and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashleigh_Ball\" title=\"Ashleigh Ball\"><PERSON><PERSON> Ball</a>, Canadian voice actress and musician", "links": [{"title": "Ashleigh Ball", "link": "https://wikipedia.org/wiki/Ashleigh_Ball"}]}, {"year": "1983", "text": "<PERSON>, Swiss-German musician", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sophie Hunger\"><PERSON></a>, Swiss-German musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sophie Hunger\"><PERSON></a>, Swiss-German musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Greek gymnast", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vlasios_Maras"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1984", "text": "<PERSON>, American soccer player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_soccer)\" title=\"<PERSON> (American soccer)\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_soccer)\" title=\"<PERSON> (American soccer)\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON> (American soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_soccer)"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (wide receiver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)"}]}, {"year": "1984", "text": "<PERSON>, Latvian sled racer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian sled racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian sled racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Estonian heptathlete", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nd\" title=\"<PERSON><PERSON> Kand\"><PERSON><PERSON></a>, Estonian heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nd\" title=\"<PERSON><PERSON> Kand\"><PERSON><PERSON></a>, Estonian heptathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nd"}]}, {"year": "1984", "text": "<PERSON>, Peruvian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rodr%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Peruvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Peruvian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADguez"}]}, {"year": "1984", "text": "<PERSON>, English rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Danish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1985)\" title=\"<PERSON><PERSON> (footballer, born 1985)\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1985)\" title=\"<PERSON><PERSON> (footballer, born 1985)\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1985)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1985)"}]}, {"year": "1985", "text": "<PERSON>, Ukrainian race car driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sheets\" title=\"Kory Sheets\"><PERSON><PERSON> She<PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sheets\" title=\"Kory Sheets\"><PERSON><PERSON> Sheets</a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Sheets"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Swedish wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_Sj%C3%B6berg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_Sj%C3%B6berg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jalmar_Sj%C3%B6berg"}]}, {"year": "1986", "text": "<PERSON>, Austrian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Scottish rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_rugby_union_player)\" class=\"mw-redirect\" title=\"<PERSON> (Scottish rugby union player)\"><PERSON></a>, Scottish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_rugby_union_player)\" class=\"mw-redirect\" title=\"<PERSON> (Scottish rugby union player)\"><PERSON></a>, Scottish rugby player", "links": [{"title": "<PERSON> (Scottish rugby union player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_rugby_union_player)"}]}, {"year": "1986", "text": "<PERSON>, Portuguese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Nordin_Amrabat\" title=\"Nordin Amrabat\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nordin_Amrabat\" title=\"Nordin Amrabat\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nordin_Amrabat"}]}, {"year": "1987", "text": "<PERSON>, Mexican footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Indian chess player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Humpy_Koneru\" class=\"mw-redirect\" title=\"Humpy Koneru\"><PERSON><PERSON><PERSON></a>, Indian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Humpy_Koneru\" class=\"mw-redirect\" title=\"Humpy Koneru\"><PERSON><PERSON><PERSON></a>, Indian chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Humpy_Koneru"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Danish ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Russian figure skater", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1988", "text": "<PERSON>, Belgian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian singer and songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Namibian cricketer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Spanish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Alberto_<PERSON>%C3%ADn_Romo_Garc%C3%ADa_Ad%C3%A1mez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alberto_<PERSON>%C3%ADn_Romo_Garc%C3%ADa_Ad%C3%A1mez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberto_Mart%C3%ADn_Romo_Garc%C3%ADa_Ad%C3%A1mez"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Slovenian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Nejc_Vidmar\" title=\"Nejc Vidmar\"><PERSON><PERSON><PERSON></a>, Slovenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nej<PERSON>_Vidmar\" title=\"Nejc Vidmar\"><PERSON><PERSON><PERSON></a>, Slovenian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nejc_Vidmar"}]}, {"year": "1989", "text": "<PERSON>, Chinese swimmer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Irish journalist (d. 2019)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>K<PERSON>\"><PERSON><PERSON></a>, Irish journalist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>K<PERSON>\"><PERSON><PERSON></a>, Irish journalist (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ee"}]}, {"year": "1990", "text": "<PERSON>, Swedish tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Serbian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Milan_Milanovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Milanovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milan_Milanovi%C4%87"}]}, {"year": "1991", "text": "<PERSON>, Dutch footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian cricketer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Swedish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Afghan member of the International Olympic Committee", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan member of the International Olympic Committee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan member of the International Olympic Committee", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian surfer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surfer)\" title=\"<PERSON> (surfer)\"><PERSON></a>, Australian surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surfer)\" title=\"<PERSON> (surfer)\"><PERSON></a>, Australian surfer", "links": [{"title": "<PERSON> (surfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surfer)"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Danish road cyclist", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_W%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish road cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_W%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish road cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mads_W%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1996", "text": "<PERSON>, American actress, comedian, and television host", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}]}, {"year": "1998", "text": "<PERSON>, American-born Canadian ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-born Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-born Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Japhet_Tanganga\" title=\"Japhet Tanganga\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_Tanganga\" title=\"Japhet Tanganga\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Japhet_Tanganga"}]}, {"year": "1999", "text": "<PERSON>, Irish Singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Danish professional footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish professional footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Ukrainian-born pair skater", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-born <a href=\"https://wikipedia.org/wiki/Pair_skater\" class=\"mw-redirect\" title=\"Pair skater\">pair skater</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-born <a href=\"https://wikipedia.org/wiki/Pair_skater\" class=\"mw-redirect\" title=\"Pair skater\">pair skater</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Denys_Strekalin"}, {"title": "Pair skater", "link": "https://wikipedia.org/wiki/Pair_skater"}]}, {"year": "1999", "text": "<PERSON>, Polish professional footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish professional footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Argentine rugby union player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Santiago_Chocobares\" title=\"Santiago Chocobares\"><PERSON></a>, Argentine rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Chocobares\" title=\"Santiago Chocobares\"><PERSON></a>, Argentine rugby union player", "links": [{"title": "Santiago Chocobares", "link": "https://wikipedia.org/wiki/Santiago_Chocobares"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Canadian professional soccer player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tabla\"><PERSON><PERSON></a>, Canadian professional soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tabla\"><PERSON><PERSON></a>, Canadian professional soccer player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bla"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian figure skater", "html": "1999 - <a href=\"https://wikipedia.org/wiki/El%C5%BEbieta_Kropa\" title=\"Elžbieta Kropa\">Elžbieta Kropa</a>, Lithuanian <a href=\"https://wikipedia.org/wiki/Figure_skater\" class=\"mw-redirect\" title=\"Figure skater\">figure skater</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El%C5%BEbieta_Kropa\" title=\"Elžbieta Kropa\">Elžbieta Kropa</a>, Lithuanian <a href=\"https://wikipedia.org/wiki/Figure_skater\" class=\"mw-redirect\" title=\"Figure skater\">figure skater</a>", "links": [{"title": "Elžbieta Kropa", "link": "https://wikipedia.org/wiki/El%C5%BEbieta_Kropa"}, {"title": "Figure skater", "link": "https://wikipedia.org/wiki/Figure_skater"}]}, {"year": "1999", "text": "<PERSON><PERSON>, German born professional footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German born professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German born professional footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Jamaican track and field athlete", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Shiann <PERSON>\"><PERSON><PERSON></a>, Jamaican track and field athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Shiann <PERSON>\"><PERSON><PERSON></a>, Jamaican track and field athlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Welsh professional footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1999)\" title=\"<PERSON> (footballer, born 1999)\"><PERSON></a>, Welsh professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1999)\" title=\"<PERSON> (footballer, born 1999)\"><PERSON></a>, Welsh professional footballer", "links": [{"title": "<PERSON> (footballer, born 1999)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1999)"}]}, {"year": "1999", "text": "<PERSON>, Italian professional footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian professional footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ul"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Estonian professional basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian professional basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian professional basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Danish ice hockey player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_R%C3%B8ndbjerg\" title=\"<PERSON>\"><PERSON></a>, Danish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_R%C3%B8ndbjerg\" title=\"<PERSON>\"><PERSON></a>, Danish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jonas_R%C3%B8ndbjerg"}]}, {"year": "1999", "text": "<PERSON>, Singaporean sports shooter", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean <a href=\"https://wikipedia.org/wiki/Shooting_sport\" class=\"mw-redirect\" title=\"Shooting sport\">sports shooter</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean <a href=\"https://wikipedia.org/wiki/Shooting_sport\" class=\"mw-redirect\" title=\"Shooting sport\">sports shooter</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shooting sport", "link": "https://wikipedia.org/wiki/Shooting_sport"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Portuguese football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>na"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Slovakian skier", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Jan%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovakian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Jan%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovakian skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tereza_Jan%C4%8Dov%C3%A1"}]}, {"year": "1999", "text": "<PERSON><PERSON>, German female canoeist", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German female <a href=\"https://wikipedia.org/wiki/Canoeing\" title=\"Canoeing\">canoeist</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German female <a href=\"https://wikipedia.org/wiki/Canoeing\" title=\"Canoeing\">canoeist</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Canoeing", "link": "https://wikipedia.org/wiki/Canoeing"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Sri Lankan-Australian professional squash player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan-Australian professional squash player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> V<PERSON>\"><PERSON><PERSON></a>, Sri Lankan-Australian professional squash player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ana"}]}, {"year": "1999", "text": "<PERSON>,  English cricketer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Providence_Cowdrill\" title=\"Providence Cowdrill\">Providence Cowdrill</a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Providence_Cowdrill\" title=\"Providence Cowdrill\">Providence Cowdrill</a>, English cricketer", "links": [{"title": "Providence Cowdrill", "link": "https://wikipedia.org/wiki/Providence_Cowdrill"}]}, {"year": "1999", "text": "<PERSON>, Brazilian footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Greek professional footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek professional footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Austrian professional footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian professional footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Argentine professional footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine professional footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Argentine professional footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Argentine professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Argentine professional footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, American professional footballer", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>hit<PERSON>\"><PERSON></a>, American professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>hiting\"><PERSON></a>, American professional footballer", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}], "Deaths": [{"year": "32 BC", "text": "<PERSON>, Roman nobleman of the Equestrian order (b. 109 BC)", "html": "32 BC - 32 BC - <a href=\"https://wikipedia.org/wiki/Titus_Pomponius_Atticus\" title=\"Titus Pomponius Atticus\"><PERSON></a>, Roman nobleman of the <a href=\"https://wikipedia.org/wiki/Equestrian_order\" class=\"mw-redirect\" title=\"Equestrian order\">Equestrian order</a> (b. 109 BC)", "no_year_html": "32 BC - <a href=\"https://wikipedia.org/wiki/Titus_Pomponius_Atticus\" title=\"Titus Po<PERSON>nius <PERSON>ticus\"><PERSON></a>, Roman nobleman of the <a href=\"https://wikipedia.org/wiki/Equestrian_order\" class=\"mw-redirect\" title=\"Equestrian order\">Equestrian order</a> (b. 109 BC)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Titus_Pomponius_Atticus"}, {"title": "Equestrian order", "link": "https://wikipedia.org/wiki/Equestrian_order"}]}, {"year": "528", "text": "<PERSON><PERSON>, emperor of Northern Wei (b. 510)", "html": "528 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei\" title=\"Emperor <PERSON><PERSON> of Northern Wei\"><PERSON><PERSON></a>, emperor of Northern Wei (b. 510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei\" title=\"Emperor <PERSON><PERSON> of Northern Wei\"><PERSON><PERSON></a>, emperor of Northern Wei (b. 510)", "links": [{"title": "Emperor <PERSON><PERSON> of Northern Wei", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei"}]}, {"year": "963", "text": "<PERSON><PERSON> ibn <PERSON>, Saffarid emir (b. 906)", "html": "963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, Saffarid emir (b. 906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, Saffarid emir (b. 906)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1241", "text": "<PERSON><PERSON>, voivode of Transylvania", "html": "1241 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_son_of_S%C3%B3ly<PERSON>\" title=\"<PERSON><PERSON>, son of Só<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Voivode_of_Transylvania\" title=\"Voivode of Transylvania\">voivode of Transylvania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_son_of_S%C3%B3lyom\" title=\"<PERSON><PERSON>, son of S<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Voivode_of_Transylvania\" title=\"Voivode of Transylvania\">voivode of Transylvania</a>", "links": [{"title": "<PERSON><PERSON>, son of <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_son_of_S%C3%B3lyom"}, {"title": "Voivode of Transylvania", "link": "https://wikipedia.org/wiki/Voivode_of_Transylvania"}]}, {"year": "1251", "text": "<PERSON> of Modena, Italian bishop and diplomat", "html": "1251 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Modena\" title=\"<PERSON> of Modena\"><PERSON> of Modena</a>, Italian bishop and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Modena\" title=\"<PERSON> of Modena\"><PERSON> of Modena</a>, Italian bishop and diplomat", "links": [{"title": "<PERSON> of Modena", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mode<PERSON>"}]}, {"year": "1340", "text": "<PERSON> of Moscow, Russian Grand Duke (b. 1288)", "html": "1340 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Moscow\" title=\"<PERSON> of Moscow\"><PERSON> of Moscow</a>, Russian Grand Duke (b. 1288)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Moscow\" title=\"<PERSON> of Moscow\"><PERSON> of Moscow</a>, Russian Grand Duke (b. 1288)", "links": [{"title": "<PERSON> of Moscow", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Moscow"}]}, {"year": "1342", "text": "<PERSON><PERSON> Sepolcro, Italian Augustinian friar", "html": "1342 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Borgo_San_Sepolcro\" title=\"<PERSON><PERSON> di Borgo San Sepolcro\"><PERSON><PERSON> <PERSON> Borgo San Sepolcro</a>, Italian <a href=\"https://wikipedia.org/wiki/Augustinians\" title=\"Augustinians\">Augustinian</a> friar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Borgo_San_Sepolcro\" title=\"<PERSON><PERSON> di Borgo San Sepolcro\"><PERSON><PERSON> Borgo San Sepolcro</a>, Italian <a href=\"https://wikipedia.org/wiki/Augustinians\" title=\"Augustinians\">Augustinian</a> friar", "links": [{"title": "<PERSON><PERSON>rgo San Sepolcro", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>rgo_San_Sepolcro"}, {"title": "Augustinians", "link": "https://wikipedia.org/wiki/Augustinians"}]}, {"year": "1462", "text": "<PERSON><PERSON><PERSON> <PERSON> of Constantinople, patriarch of Constantinople", "html": "1462 - <a href=\"https://wikipedia.org/wiki/Isidore_II_of_Constantinople\" title=\"Isido<PERSON> II of Constantinople\"><PERSON><PERSON><PERSON> II of Constantinople</a>, patriarch of Constantinople", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isido<PERSON>_II_of_Constantinople\" title=\"Isido<PERSON> II of Constantinople\"><PERSON><PERSON><PERSON> II of Constantinople</a>, patriarch of Constantinople", "links": [{"title": "Is<PERSON>re II of Constantinople", "link": "https://wikipedia.org/wiki/Isidore_II_of_Constantinople"}]}, {"year": "1491", "text": "<PERSON><PERSON><PERSON>, Italian Roman Catholic priest (b. 1411)", "html": "1491 - <a href=\"https://wikipedia.org/wiki/Bonaventura_Tornielli\" title=\"Bonaventura Tornielli\">Bonaven<PERSON></a>, Italian Roman Catholic priest (b. 1411)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bonaventura_Tornielli\" title=\"Bonaventura Tornielli\"><PERSON><PERSON><PERSON></a>, Italian Roman Catholic priest (b. 1411)", "links": [{"title": "Bonaventura Tornielli", "link": "https://wikipedia.org/wiki/Bonaventura_Tornielli"}]}, {"year": "1547", "text": "<PERSON>, French king (b. 1494)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON></a>, French king (b. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON></a>, French king (b. 1494)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1567", "text": "<PERSON>, Landgrave of Hesse (b. 1504)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse\" title=\"<PERSON>, Landgrave of Hesse\"><PERSON>, Landgrave of Hesse</a> (b. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse\" title=\"<PERSON>, Landgrave of Hesse\"><PERSON>, Landgrave of Hesse</a> (b. 1504)", "links": [{"title": "<PERSON>, Landgrave of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse"}]}, {"year": "1621", "text": "<PERSON>, Spanish king (b. 1578)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON></a>, Spanish king (b. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> III</a>, Spanish king (b. 1578)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1622", "text": "<PERSON><PERSON><PERSON>, Royal Governor of La Florida (b. 1554)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/Gonzalo_M%C3%A9ndez_de_Can%C3%A7o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Royal Governor of La Florida (b. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gonzalo_M%C3%A9ndez_de_Can%C3%A7o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Royal Governor of La Florida (b. 1554)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gonzalo_M%C3%A9ndez_de_Can%C3%A7o"}]}, {"year": "1631", "text": "<PERSON>, English lawyer and poet (b. 1572)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and poet (b. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and poet (b. 1572)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1671", "text": "<PERSON>, wife of <PERSON> of England (b. 1637)", "html": "1671 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"James II of England\"><PERSON> of England</a> (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"James II of England\"><PERSON> of England</a> (b. 1637)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1723", "text": "<PERSON>, 3rd Earl of Clarendon, English soldier and politician, 14th Colonial Governor of New York (b. 1661)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Clarendon\" title=\"<PERSON>, 3rd Earl of Clarendon\"><PERSON>, 3rd Earl of Clarendon</a>, English soldier and politician, 14th <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_New_York\" title=\"List of colonial governors of New York\">Colonial Governor of New York</a> (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Clarendon\" title=\"<PERSON>, 3rd Earl of Clarendon\"><PERSON>, 3rd Earl of Clarendon</a>, English soldier and politician, 14th <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_New_York\" title=\"List of colonial governors of New York\">Colonial Governor of New York</a> (b. 1661)", "links": [{"title": "<PERSON>, 3rd Earl of Clarendon", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Clarendon"}, {"title": "List of colonial governors of New York", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_New_York"}]}, {"year": "1741", "text": "<PERSON> the Elder, Dutch scholar and author (b. 1668)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Dutch scholar and author (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Dutch scholar and author (b. 1668)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder"}]}, {"year": "1751", "text": "<PERSON>, Prince of Wales, Hanoverian-born heir to the British throne (b. 1707)[citation needed]", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a>, Hanoverian-born heir to the British throne (b. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a>, Hanoverian-born heir to the British throne (b. 1707)", "links": [{"title": "<PERSON>, Prince of Wales", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON>, Nigerian merchant, author, and activist (b. 1745)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian merchant, author, and activist (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian merchant, author, and activist (b. 1745)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, English painter and educator (b. 1776)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and educator (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and educator (b. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, American lawyer and politician, 7th Vice President of the United States (b. 1782)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1782)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1855", "text": "<PERSON>, English novelist and poet (b. 1816)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/Charlotte_Bront%C3%AB\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, English novelist and poet (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charlotte_Bront%C3%AB\" title=\"<PERSON> Brontë\"><PERSON></a>, English novelist and poet (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charlotte_Bront%C3%AB"}]}, {"year": "1877", "text": "<PERSON>, French mathematician and philosopher (b. 1801)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON>, Polish violinist and composer (b. 1835)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish violinist and composer (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish violinist and composer (b. 1835)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wski"}]}, {"year": "1885", "text": "<PERSON>, German composer and conductor (b. 1819)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, American lawyer and politician, 28th Speaker of the United States House of Representatives (b. 1823)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1823)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1910", "text": "<PERSON>, Greek poet, essayist and art critic (b. 1856)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9as\" title=\"<PERSON>\"><PERSON></a>, Greek poet, essayist and art critic (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9as\" title=\"<PERSON>\"><PERSON></a>, Greek poet, essayist and art critic (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9as"}]}, {"year": "1913", "text": "<PERSON><PERSON> <PERSON><PERSON>, American banker and financier (b. 1837)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. P. Morgan\"><PERSON><PERSON> <PERSON><PERSON></a>, American banker and financier (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. P. Morgan\"><PERSON><PERSON> <PERSON><PERSON></a>, American banker and financier (b. 1837)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, English-Scottish runner and captain (b. 1882)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Wyndham_Halswelle\" title=\"Wyndham Halswelle\">Wyn<PERSON> Halswelle</a>, English-Scottish runner and captain (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wyndham_Halswelle\" title=\"Wyndham Halswelle\"><PERSON><PERSON><PERSON>s<PERSON>e</a>, English-Scottish runner and captain (b. 1882)", "links": [{"title": "Wyndham Halswelle", "link": "https://wikipedia.org/wiki/Wyndham_Halswelle"}]}, {"year": "1917", "text": "<PERSON>, German physiologist and immunologist, Nobel Prize laureate (b. 1854)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1920", "text": "<PERSON>, Bengali Islamic scholar and author (b. 1869)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bengali Islamic scholar and author (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bengali Islamic scholar and author (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English painter and illustrator (b. 1855)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1927", "text": "<PERSON>, Chinese scholar and political reformer (b. 1858)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese scholar and political reformer (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese scholar and political reformer (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, German politician, Mayor of Marburg (b. 1836)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ludwig_Sch%C3%BCler"}, {"title": "List of mayors of Marburg", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Marburg"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, American football player and coach (b. 1888)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ne"}]}, {"year": "1935", "text": "<PERSON>, Georgian-American businessman and diplomat, founded Prince <PERSON> perfume (b. 1885)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-American businessman and diplomat, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Prince <PERSON> perfume</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-American businessman and diplomat, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Prince <PERSON> perfume</a> (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Greek general (b. 1887)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Japanese admiral (b. 1885)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese admiral (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Koga\" title=\"<PERSON><PERSON> Ko<PERSON>\"><PERSON><PERSON></a>, Japanese admiral (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mine<PERSON>_<PERSON>ga"}]}, {"year": "1945", "text": "<PERSON>, New Zealand banker and politician (b. 1884)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand banker and politician (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand banker and politician (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (b. 1881)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1950", "text": "<PERSON>, Estonian architect (b. 1890)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Jr., American lawyer and politician (b. 1877)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and politician (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and politician (b. 1877)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1956", "text": "<PERSON>, Italian-American race car driver and actor (b. 1884)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American race car driver and actor (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American race car driver and actor (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American politician and librarian (b. 1893)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician and librarian (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician and librarian (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, officer of the Greek Navy and director of the Greek Radio Orchestra (b. 1913)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, officer of the Greek Navy and director of the Greek Radio Orchestra (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, officer of the Greek Navy and director of the Greek Radio Orchestra (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rom<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American baseball player (b. 1885)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Grover_Lowdermilk\" title=\"Grover Lowdermilk\"><PERSON><PERSON></a>, American baseball player (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grover_Lowdermilk\" title=\"Grover Lowdermilk\"><PERSON><PERSON></a>, American baseball player (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Grover_Lowdermilk"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Soviet Commander during the Winter War and the Eastern Front of World War II (b. 1894)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet Commander during the <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a> and the <a href=\"https://wikipedia.org/wiki/Eastern_Front_(World_War_II)\" title=\"Eastern Front (World War II)\">Eastern Front</a> of World War II (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet Commander during the <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a> and the <a href=\"https://wikipedia.org/wiki/Eastern_Front_(World_War_II)\" title=\"Eastern Front (World War II)\">Eastern Front</a> of World War II (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Winter War", "link": "https://wikipedia.org/wiki/Winter_War"}, {"title": "Eastern Front (World War II)", "link": "https://wikipedia.org/wiki/Eastern_Front_(World_War_II)"}]}, {"year": "1975", "text": "<PERSON>, English golfer (b. 1897)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American photographer and director (b. 1890)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Paul <PERSON>\"><PERSON></a>, American photographer and director (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1905)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>wyn"}]}, {"year": "1978", "text": "<PERSON>, American-Canadian physiologist and biochemist, co-discovered <PERSON><PERSON><PERSON> (b. 1899)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(medical_scientist)\" title=\"<PERSON> (medical scientist)\"><PERSON></a>, American-Canadian physiologist and biochemist, co-discovered <a href=\"https://wikipedia.org/wiki/Insulin\" title=\"Insulin\">Insulin</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(medical_scientist)\" title=\"<PERSON> (medical scientist)\"><PERSON></a>, American-Canadian physiologist and biochemist, co-discovered <a href=\"https://wikipedia.org/wiki/Insulin\" title=\"Insulin\">Insulin</a> (b. 1899)", "links": [{"title": "<PERSON> (medical scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(medical_scientist)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Insulin"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech poet and author (b. 1905)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Vladim%C3%<PERSON>r_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech poet and author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>im%C3%<PERSON>r_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech poet and author (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladim%C3%ADr_<PERSON>lan"}]}, {"year": "1980", "text": "<PERSON>, American sprinter and long jumper (b. 1913)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and long jumper (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and long jumper (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, English author and playwright (b. 1889)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and playwright (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and playwright (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enid_<PERSON>ld"}]}, {"year": "1983", "text": "<PERSON>, Australian author and academic (b. 1902)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and academic (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and academic (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor and director (b. 1925)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jerry_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian lawyer and politician, 20th Prime Minister of Australia (b. 1908)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek columnist, political and social analyst and historian (b. 1905)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>konstantino<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek columnist, political and social analyst and historian (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>stantino<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek columnist, political and social analyst and historian (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theofylaktos_Papakonstantinou"}]}, {"year": "1993", "text": "<PERSON>, American actor and martial artist (b. 1965)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and martial artist (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and martial artist (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Lithuanian-American songwriter (b. 1900)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Mitchell_Parish\" title=\"Mitchell Parish\">Mitchell Parish</a>, Lithuanian-American songwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mitchell_Parish\" title=\"Mitchell Parish\">Mitchell Parish</a>, Lithuanian-American songwriter (b. 1900)", "links": [{"title": "Mitchell Parish", "link": "https://wikipedia.org/wiki/Mitchell_Parish"}]}, {"year": "1995", "text": "<PERSON>, American singer-songwriter (b. 1971)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Selena\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Selena\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Selena"}]}, {"year": "1996", "text": "<PERSON>, Italian automobile designer and engineer (b. 1905)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian automobile designer and engineer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian automobile designer and engineer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dante_<PERSON>osa"}]}, {"year": "1996", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1958)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American lawyer, activist, and politician (b. 1920)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_A<PERSON>zug\" title=\"<PERSON> Abzug\"><PERSON></a>, American lawyer, activist, and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>zug\" title=\"Bella Abzug\"><PERSON></a>, American lawyer, activist, and politician (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Abzug"}]}, {"year": "1998", "text": "<PERSON>, American race car driver (b. 1924)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lock"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, American pianist (b. 1933)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Russian linguist and ethnographer (b. 1922)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian linguist and ethnographer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian linguist and ethnographer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, German-born French photographer and photojournalist (b. 1908)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Gis%C3%A8<PERSON>_<PERSON>eund\" title=\"<PERSON><PERSON><PERSON><PERSON> Freund\"><PERSON><PERSON><PERSON><PERSON></a>, German-born French photographer and photojournalist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gis%C3%A8le_<PERSON>eund\" title=\"<PERSON><PERSON><PERSON><PERSON>eund\"><PERSON><PERSON><PERSON><PERSON></a>, German-born French photographer and photojournalist (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gis%C3%A8le_<PERSON>eund"}]}, {"year": "2000", "text": "<PERSON>, English guitarist and member of the band <PERSON> (b. 1952)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Guitarist\" title=\"Guitarist\">guitarist</a> and member of the band <PERSON> (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Guitarist\" title=\"Guitarist\">guitarist</a> and member of the band <PERSON> (b. 1952)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}, {"title": "Guitarist", "link": "https://wikipedia.org/wiki/Guitarist"}]}, {"year": "2001", "text": "<PERSON>, English footballer (b. 1967)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1915)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2002", "text": "<PERSON>, English comedian, actor, and screenwriter (b. 1928)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Indian activist and politician (b. 1924)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Moturu_Udayam\" title=\"Moturu Udayam\"><PERSON><PERSON><PERSON></a>, Indian activist and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moturu_Udayam\" title=\"Moturu Udayam\"><PERSON><PERSON><PERSON></a>, Indian activist and politician (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Moturu_Udayam"}]}, {"year": "2002", "text": "<PERSON>, Argentine Archaeologist (b. 1913)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine Archaeologist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine Archaeologist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English-Canadian mathematician and academic (b. 1907)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian mathematician and academic (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian mathematician and academic (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actress (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Danish singer-songwriter, pianist, and producer (b. 1949)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish singer-songwriter, pianist, and producer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish singer-songwriter, pianist, and producer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American soldier (b. 1965)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American oncologist and academic (b. 1951)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American oncologist and academic (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American oncologist and academic (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Filipino lawyer and politician (b. 1905)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American businessman (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American saxophonist and composer (b. 1931)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Austrian-American psychologist and philosopher (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American psychologist and philosopher (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American psychologist and philosopher (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American director, producer, screenwriter, and actor (b. 1911)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and actor (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American equipment manager (b. 1926)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American equipment manager (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American equipment manager (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Argentinian lawyer and politician, 46th President of Argentina (b. 1927)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Alfons%C3%ADn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian lawyer and politician, 46th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Alfons%C3%ADn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian lawyer and politician, 46th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_Alfons%C3%ADn"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Indian-Singaporean lawyer and judge (b. 1911)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Singaporean lawyer and judge (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Singaporean lawyer and judge (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, American journalist (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ter<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> ter<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American journalist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ter<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> ter<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American journalist (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ter<PERSON><PERSON>t"}]}, {"year": "2010", "text": "<PERSON>, Welsh rugby union player (b. 1945)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh <a href=\"https://wikipedia.org/wiki/Rugby_union\" title=\"Rugby union\">rugby union</a> player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh <a href=\"https://wikipedia.org/wiki/Rugby_union\" title=\"Rugby union\">rugby union</a> player (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Rugby union", "link": "https://wikipedia.org/wiki/Rugby_union"}]}, {"year": "2011", "text": "<PERSON>, American boxer and trainer (b. 1922)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Australian journalist and author (b. 1935)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(satirist)\" title=\"<PERSON> (satirist)\"><PERSON></a>, Australian journalist and author (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(satirist)\" title=\"<PERSON> (satirist)\"><PERSON></a>, Australian journalist and author (b. 1935)", "links": [{"title": "<PERSON> (satirist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(satirist)"}]}, {"year": "2011", "text": "<PERSON>, the first First Nations woman to join the Canadian Armed Forces (b. 1920)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/First_Nations_in_Canada\" title=\"First Nations in Canada\">First Nations</a> woman to join the <a href=\"https://wikipedia.org/wiki/Canadian_Armed_Forces\" title=\"Canadian Armed Forces\">Canadian Armed Forces</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/First_Nations_in_Canada\" title=\"First Nations in Canada\">First Nations</a> woman to join the <a href=\"https://wikipedia.org/wiki/Canadian_Armed_Forces\" title=\"Canadian Armed Forces\">Canadian Armed Forces</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mary_<PERSON>"}, {"title": "First Nations in Canada", "link": "https://wikipedia.org/wiki/First_Nations_in_Canada"}, {"title": "Canadian Armed Forces", "link": "https://wikipedia.org/wiki/Canadian_Armed_Forces"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Norwegian footballer and coach (b. 1921)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and coach (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and coach (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Scottish singer and actress (b. 1941)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish singer and actress (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish singer and actress (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>kill"}]}, {"year": "2011", "text": "<PERSON>, American businessman and philanthropist (b. 1927)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, New Zealand-Australian nurse and politician (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian nurse and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian nurse and politician (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American physicist and academic (b. 1914)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American stained glass artist (b. 1914)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Stained_glass\" title=\"Stained glass\">stained glass</a> artist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Stained_glass\" title=\"Stained glass\">stained glass</a> artist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Stained glass", "link": "https://wikipedia.org/wiki/Stained_glass"}]}, {"year": "2012", "text": "<PERSON>, American baseball player (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Italian painter (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American economist and academic (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and academic (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and academic (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, French archbishop (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, French archbishop (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, French archbishop (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian singer and politician (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bridge\"><PERSON></a>, Australian singer and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bridge\"><PERSON></a>, Australian singer and politician (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American illustrator (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illustrator)\" title=\"<PERSON> (illustrator)\"><PERSON></a>, American illustrator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illustrator)\" title=\"<PERSON> (illustrator)\"><PERSON></a>, American illustrator (b. 1926)", "links": [{"title": "<PERSON> (illustrator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illustrator)"}]}, {"year": "2013", "text": "<PERSON>, Iranian lawyer and politician, Iranian Minister of Interior (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Iran)\" title=\"Ministry of Interior (Iran)\">Iranian Minister of Interior</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Iran)\" title=\"Ministry of Interior (Iran)\">Iranian Minister of Interior</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Interior (Iran)", "link": "https://wikipedia.org/wiki/Ministry_of_Interior_(Iran)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Russian ice hockey player (b. 1980)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player (b. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Spanish economist, historian, and academic (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish economist, historian, and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish economist, historian, and academic (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Belgian painter (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian painter, historian, and curator (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter, historian, and curator (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter, historian, and curator (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Japanese author and illustrator (b. 1983)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Cocoa_<PERSON>wara\" title=\"Cocoa Fujiwara\"><PERSON><PERSON></a>, Japanese author and illustrator (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cocoa_Fujiwara\" title=\"Cocoa Fujiwara\"><PERSON><PERSON></a>, Japanese author and illustrator (b. 1983)", "links": [{"title": "Cocoa Fujiwara", "link": "https://wikipedia.org/wiki/Cocoa_Fujiwara"}]}, {"year": "2015", "text": "<PERSON>, Colombian lawyer and politician (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_D%C3%ADaz\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_D%C3%ADaz\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_D%C3%ADaz"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Czech-English historian, author, and academic (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-English historian, author, and academic (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-English historian, author, and academic (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Scottish comedian, actor and screenwriter (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish comedian, actor and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish comedian, actor and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, German politician (b. 1927)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Iraqi-born English architect and academic, designed the Bridge Pavilion (b. 1950)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi-born English architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Bridge_Pavilion\" title=\"Bridge Pavilion\">Bridge Pavilion</a> (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi-born English architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Bridge_Pavilion\" title=\"Bridge Pavilion\">Bridge Pavilion</a> (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Bridge Pavilion", "link": "https://wikipedia.org/wiki/Bridge_Pavilion"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Hungarian author, Nobel Prize laureate (b. 1929)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Imre_Kert%C3%A9sz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Imre_Kert%C3%A9sz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Imre_Kert%C3%A9sz"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2016", "text": "<PERSON>, British writer and television broadcaster (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer and television broadcaster (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer and television broadcaster (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American artist and LGBT rights activist (b. 1951)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American artist and LGBT rights activist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American artist and LGBT rights activist (b. 1951)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "2017", "text": "<PERSON>, American artist (b. 1933)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2018", "text": "<PERSON>,  inventor of the Newton Starting Blocks (b. 1933)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, inventor of the Newton Starting Blocks (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Newton\"><PERSON></a>, inventor of the Newton Starting Blocks (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, American rapper (b. 1985)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Hu<PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper (b. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>le"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Ugandan-South African scientist and researcher (b. 1956)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ugandan-South African scientist and researcher (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ugandan-South African scientist and researcher (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American baseball player (b. 1951)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Bangladeshi teacher and parliamentarian (b. 1952)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi teacher and parliamentarian (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi teacher and parliamentarian (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, former American All-American Girls Professional Baseball League (AAGPBL) player (b. 1933)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former American All-American Girls Professional Baseball League (<a href=\"https://wikipedia.org/wiki/All-American_Girls_Professional_Baseball_League\" title=\"All-American Girls Professional Baseball League\">AAGPBL</a>) player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former American All-American Girls Professional Baseball League (<a href=\"https://wikipedia.org/wiki/All-American_Girls_Professional_Baseball_League\" title=\"All-American Girls Professional Baseball League\">AAGPBL</a>) player (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "All-American Girls Professional Baseball League", "link": "https://wikipedia.org/wiki/All-American_Girls_Professional_Baseball_League"}]}, {"year": "2022", "text": "<PERSON>, French fashion photographer (b. 1943)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion photographer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion photographer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>,  New Zealand lawyer specialising in constitutional law (b. 1945)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand lawyer specialising in constitutional law (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand lawyer specialising in constitutional law (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American actress (b. 1927)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rush\"><PERSON></a>, American actress (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}