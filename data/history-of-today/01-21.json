{"date": "January 21", "url": "https://wikipedia.org/wiki/January_21", "data": {"Events": [{"year": "763", "text": "Following the Battle of Bakhamra between Alids and Abbasids near Kufa, the Alid rebellion ends with the death of <PERSON>, brother of <PERSON> ibn <PERSON>.", "html": "763 - Following the <a href=\"https://wikipedia.org/wiki/Alid_revolt_of_762%E2%80%93763\" title=\"Alid revolt of 762-763\">Battle of Bakhamra</a> between <a href=\"https://wikipedia.org/wiki/Ali<PERSON>\" title=\"Ali<PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Abbasids\" class=\"mw-redirect\" title=\"Abbasids\">Abbasids</a> near <a href=\"https://wikipedia.org/wiki/Kufa\" title=\"Ku<PERSON>\">Ku<PERSON></a>, the Alid rebellion ends with the death of <PERSON>, brother of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>.", "no_year_html": "Following the <a href=\"https://wikipedia.org/wiki/Alid_revolt_of_762%E2%80%93763\" title=\"Alid revolt of 762-763\">Battle of Bakhamra</a> between <a href=\"https://wikipedia.org/wiki/Ali<PERSON>\" title=\"Ali<PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Abbasids\" class=\"mw-redirect\" title=\"Abbasids\">Abbasids</a> near <a href=\"https://wikipedia.org/wiki/Kufa\" title=\"Ku<PERSON>\">Ku<PERSON></a>, the Alid rebellion ends with the death of <PERSON>, brother of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>.", "links": [{"title": "Alid revolt of 762-763", "link": "https://wikipedia.org/wiki/Ali<PERSON>_revolt_of_762%E2%80%93763"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>fa"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1525", "text": "The Swiss Anabaptist Movement is founded when <PERSON>, <PERSON>, <PERSON>, and about a dozen others baptize each other in the home of <PERSON><PERSON>'s mother in Zürich, breaking a thousand-year tradition of church-state union.", "html": "1525 - The <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Swiss</a> <a href=\"https://wikipedia.org/wiki/Anabaptist\" class=\"mw-redirect\" title=\"Anabaptist\">Anabaptist</a> Movement is founded when <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and about a dozen others baptize each other in the home of <PERSON><PERSON>'s mother in <a href=\"https://wikipedia.org/wiki/Z%C3%<PERSON>rich\" class=\"mw-redirect\" title=\"Zürich\">Zürich</a>, breaking a thousand-year tradition of church-state union.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Swiss</a> <a href=\"https://wikipedia.org/wiki/Anabaptist\" class=\"mw-redirect\" title=\"Anabaptist\">Anabaptist</a> Movement is founded when <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and about a dozen others baptize each other in the home of <PERSON><PERSON>'s mother in <a href=\"https://wikipedia.org/wiki/Z%C3%BCrich\" class=\"mw-redirect\" title=\"Zürich\">Zürich</a>, breaking a thousand-year tradition of church-state union.", "links": [{"title": "Switzerland", "link": "https://wikipedia.org/wiki/Switzerland"}, {"title": "Anabaptist", "link": "https://wikipedia.org/wiki/Anabaptist"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Zürich", "link": "https://wikipedia.org/wiki/Z%C3%BCrich"}]}, {"year": "1535", "text": "Following the Affair of the Placards, the French king leads an anti-Protestant procession through Paris.", "html": "1535 - Following the <a href=\"https://wikipedia.org/wiki/Affair_of_the_Placards\" title=\"Affair of the Placards\">Affair of the Placards</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> I of France\">the French king</a> leads an anti-<a href=\"https://wikipedia.org/wiki/Protestant\" class=\"mw-redirect\" title=\"Protestant\">Protestant</a> procession through Paris.", "no_year_html": "Following the <a href=\"https://wikipedia.org/wiki/Affair_of_the_Placards\" title=\"Affair of the Placards\">Affair of the Placards</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> I of France\">the French king</a> leads an anti-<a href=\"https://wikipedia.org/wiki/Protestant\" class=\"mw-redirect\" title=\"Protestant\">Protestant</a> procession through Paris.", "links": [{"title": "Affair of the Placards", "link": "https://wikipedia.org/wiki/Affair_of_the_Placards"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "Protestant", "link": "https://wikipedia.org/wiki/Protestant"}]}, {"year": "1720", "text": "Sweden and Prussia sign the Treaty of Stockholm.", "html": "1720 - Sweden and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussia</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Stockholm_(Great_Northern_War)\" class=\"mw-redirect\" title=\"Treaty of Stockholm (Great Northern War)\">Treaty of Stockholm</a>.", "no_year_html": "Sweden and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussia</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Stockholm_(Great_Northern_War)\" class=\"mw-redirect\" title=\"Treaty of Stockholm (Great Northern War)\">Treaty of Stockholm</a>.", "links": [{"title": "Kingdom of Prussia", "link": "https://wikipedia.org/wiki/Kingdom_of_Prussia"}, {"title": "Treaty of Stockholm (Great Northern War)", "link": "https://wikipedia.org/wiki/Treaty_of_Stockholm_(Great_Northern_War)"}]}, {"year": "1749", "text": "The Teatro Filarmonico in Verona is destroyed by fire, as a result of a torch being left behind in the box of a nobleman after a performance. It is rebuilt in 1754.", "html": "1749 - The <a href=\"https://wikipedia.org/wiki/Teatro_Filarmonico\" title=\"Teatro Filarmonico\">Teatro Filarmonico</a> in Verona is destroyed by fire, as a result of a torch being left behind in the box of a nobleman after a performance. It is rebuilt in 1754.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Teatro_Filarmonico\" title=\"Teatro Filarmonico\">Teatro Filarmonico</a> in Verona is destroyed by fire, as a result of a torch being left behind in the box of a nobleman after a performance. It is rebuilt in 1754.", "links": [{"title": "Teatro Filarmonico", "link": "https://wikipedia.org/wiki/Teatro_Filarmonico"}]}, {"year": "1774", "text": "<PERSON> becomes Sultan of the Ottoman Empire and <PERSON><PERSON><PERSON> of Islam.", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a> becomes <a href=\"https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Sultan of the Ottoman Empire\">Sultan of the Ottoman Empire</a> and <a href=\"https://wikipedia.org/wiki/Caliph_of_Islam\" class=\"mw-redirect\" title=\"Caliph of Islam\">Caliph of Islam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a> becomes <a href=\"https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Sultan of the Ottoman Empire\">Sultan of the Ottoman Empire</a> and <a href=\"https://wikipedia.org/wiki/Caliph_of_Islam\" class=\"mw-redirect\" title=\"Caliph of Islam\">Caliph of Islam</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sultan of the Ottoman Empire", "link": "https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire"}, {"title": "Cal<PERSON>h of Islam", "link": "https://wikipedia.org/wiki/Caliph_of_Islam"}]}, {"year": "1789", "text": "The first American novel, The Power of Sympathy or the Triumph of Nature Founded in Truth by <PERSON>, is printed in Boston.", "html": "1789 - The first American novel, <a href=\"https://wikipedia.org/wiki/The_Power_of_Sympathy\" title=\"The Power of Sympathy\"><i>The Power of Sympathy or the Triumph of Nature Founded in Truth</i></a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"William <PERSON> Brown\"><PERSON></a>, is printed in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>.", "no_year_html": "The first American novel, <a href=\"https://wikipedia.org/wiki/The_Power_of_Sympathy\" title=\"The Power of Sympathy\"><i>The Power of Sympathy or the Triumph of Nature Founded in Truth</i></a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"William <PERSON> Brown\"><PERSON></a>, is printed in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>.", "links": [{"title": "The Power of Sympathy", "link": "https://wikipedia.org/wiki/The_Power_of_Sympathy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brown"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}]}, {"year": "1793", "text": "After being found guilty of treason by the French National Convention, <PERSON> of France is executed by guillotine.", "html": "1793 - After being found guilty of treason by the French <a href=\"https://wikipedia.org/wiki/National_Convention\" title=\"National Convention\">National Convention</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> XVI of France\"><PERSON> of France</a> is executed by <a href=\"https://wikipedia.org/wiki/Guillotine\" title=\"Guillotine\">guillotine</a>.", "no_year_html": "After being found guilty of treason by the French <a href=\"https://wikipedia.org/wiki/National_Convention\" title=\"National Convention\">National Convention</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_XVI_of_France\" class=\"mw-redirect\" title=\"Louis XVI of France\"><PERSON> of France</a> is executed by <a href=\"https://wikipedia.org/wiki/Guillotine\" title=\"Guillotine\">guillotine</a>.", "links": [{"title": "National Convention", "link": "https://wikipedia.org/wiki/National_Convention"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}, {"title": "Guillotine", "link": "https://wikipedia.org/wiki/Guillotine"}]}, {"year": "1824", "text": "The Ashantis defeat British forces in the Gold Coast during the First Anglo-Ashanti War.", "html": "1824 - The <a href=\"https://wikipedia.org/wiki/Ashantis\" class=\"mw-redirect\" title=\"Ashantis\">Ashantis</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Nsamankow\" title=\"Battle of Nsamankow\">defeat</a> <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> forces in the <a href=\"https://wikipedia.org/wiki/Gold_Coast_(British_colony)\" title=\"Gold Coast (British colony)\">Gold Coast</a> during the <a href=\"https://wikipedia.org/wiki/First_Anglo%E2%80%93Ashanti_War\" title=\"First Anglo-Ashanti War\">First Anglo-Ashanti War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ashantis\" class=\"mw-redirect\" title=\"Ashantis\">Ashantis</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Nsamankow\" title=\"Battle of Nsamankow\">defeat</a> <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> forces in the <a href=\"https://wikipedia.org/wiki/Gold_Coast_(British_colony)\" title=\"Gold Coast (British colony)\">Gold Coast</a> during the <a href=\"https://wikipedia.org/wiki/First_Anglo%E2%80%93Ashanti_War\" title=\"First Anglo-Ashanti War\">First Anglo-Ashanti War</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ash<PERSON>s"}, {"title": "Battle of Nsamankow", "link": "https://wikipedia.org/wiki/Battle_of_Nsamankow"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "Gold Coast (British colony)", "link": "https://wikipedia.org/wiki/Gold_Coast_(British_colony)"}, {"title": "First Anglo-Ashanti War", "link": "https://wikipedia.org/wiki/First_Anglo%E2%80%93<PERSON>hanti_War"}]}, {"year": "1854", "text": "The RMS Tayleur sinks off Lambay Island on her maiden voyage from Liverpool to Australia with great loss of life.", "html": "1854 - The <a href=\"https://wikipedia.org/wiki/RMS_Tayleur\" title=\"RMS Tayleur\">RMS <i>Tayleur</i></a> sinks off <a href=\"https://wikipedia.org/wiki/Lambay_Island\" title=\"Lambay Island\">Lambay Island</a> on her maiden voyage from Liverpool to Australia with great loss of life.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/RMS_Tayleur\" title=\"RMS Tayleur\">RMS <i><PERSON><PERSON><PERSON></i></a> sinks off <a href=\"https://wikipedia.org/wiki/Lambay_Island\" title=\"Lambay Island\">Lambay Island</a> on her maiden voyage from Liverpool to Australia with great loss of life.", "links": [{"title": "RMS <PERSON>ur", "link": "https://wikipedia.org/wiki/RMS_Tayleur"}, {"title": "Lambay Island", "link": "https://wikipedia.org/wiki/Lambay_Island"}]}, {"year": "1893", "text": "The Tati Concessions Land, formerly part of Matabeleland, is formally annexed to the Bechuanaland Protectorate, now Botswana.", "html": "1893 - The <a href=\"https://wikipedia.org/wiki/Tati_Concessions_Land\" title=\"Tati Concessions Land\">Tati Concessions Land</a>, formerly part of <a href=\"https://wikipedia.org/wiki/Matabeleland\" title=\"Matabeleland\">Matabeleland</a>, is formally annexed to the <a href=\"https://wikipedia.org/wiki/Bechuanaland_Protectorate\" title=\"Bechuanaland Protectorate\">Bechuanaland Protectorate</a>, now <a href=\"https://wikipedia.org/wiki/Botswana\" title=\"Botswana\">Botswana</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tati_Concessions_Land\" title=\"Tati Concessions Land\">Tati Concessions Land</a>, formerly part of <a href=\"https://wikipedia.org/wiki/Matabeleland\" title=\"Matabeleland\">Matabeleland</a>, is formally annexed to the <a href=\"https://wikipedia.org/wiki/Bechuanaland_Protectorate\" title=\"Bechuanaland Protectorate\">Bechuanaland Protectorate</a>, now <a href=\"https://wikipedia.org/wiki/Botswana\" title=\"Botswana\">Botswana</a>.", "links": [{"title": "Tati Concessions Land", "link": "https://wikipedia.org/wiki/Tati_Concessions_Land"}, {"title": "Matabeleland", "link": "https://wikipedia.org/wiki/Matabeleland"}, {"title": "Bechuanaland Protectorate", "link": "https://wikipedia.org/wiki/Bechuanaland_Protectorate"}, {"title": "Botswana", "link": "https://wikipedia.org/wiki/Botswana"}]}, {"year": "1908", "text": "New York City passes the Sullivan Ordinance, making it illegal for women to smoke in public, only to have the measure vetoed by the mayor.", "html": "1908 - New York City passes the <a href=\"https://wikipedia.org/wiki/Sullivan_Ordinance\" title=\"Sullivan Ordinance\">Sullivan Ordinance</a>, making it illegal for women to smoke in public, only to have the measure vetoed by the mayor.", "no_year_html": "New York City passes the <a href=\"https://wikipedia.org/wiki/Sullivan_Ordinance\" title=\"Sullivan Ordinance\">Sullivan Ordinance</a>, making it illegal for women to smoke in public, only to have the measure vetoed by the mayor.", "links": [{"title": "Sullivan Ordinance", "link": "https://wikipedia.org/wiki/Sullivan_Ordinance"}]}, {"year": "1911", "text": "The first Monte Carlo Rally takes place.", "html": "1911 - The first <a href=\"https://wikipedia.org/wiki/Monte_Carlo_Rally\" title=\"Monte Carlo Rally\">Monte Carlo Rally</a> takes place.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Monte_Carlo_Rally\" title=\"Monte Carlo Rally\">Monte Carlo Rally</a> takes place.", "links": [{"title": "Monte Carlo Rally", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "Kiwanis International is founded in Detroit.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Kiwanis_International\" class=\"mw-redirect\" title=\"Kiwanis International\">Kiwanis International</a> is founded in <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kiwanis_International\" class=\"mw-redirect\" title=\"Kiwanis International\">Kiwanis International</a> is founded in <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>.", "links": [{"title": "Kiwanis International", "link": "https://wikipedia.org/wiki/Kiwanis_International"}, {"title": "Detroit", "link": "https://wikipedia.org/wiki/Detroit"}]}, {"year": "1919", "text": "A revolutionary Irish parliament is founded and declares the independence of the Irish Republic. One of the first engagements of the Irish War of Independence takes place.", "html": "1919 - A <a href=\"https://wikipedia.org/wiki/First_D%C3%A1il\" title=\"First Dáil\">revolutionary Irish parliament</a> is founded and <a href=\"https://wikipedia.org/wiki/Irish_Declaration_of_Independence\" title=\"Irish Declaration of Independence\">declares the independence</a> of the <a href=\"https://wikipedia.org/wiki/Irish_Republic\" title=\"Irish Republic\">Irish Republic</a>. One of the <a href=\"https://wikipedia.org/wiki/Soloheadbeg_ambush\" title=\"Soloheadbeg ambush\">first engagements</a> of the <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a> takes place.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/First_D%C3%A1il\" title=\"First Dáil\">revolutionary Irish parliament</a> is founded and <a href=\"https://wikipedia.org/wiki/Irish_Declaration_of_Independence\" title=\"Irish Declaration of Independence\">declares the independence</a> of the <a href=\"https://wikipedia.org/wiki/Irish_Republic\" title=\"Irish Republic\">Irish Republic</a>. One of the <a href=\"https://wikipedia.org/wiki/Soloheadbeg_ambush\" title=\"Soloheadbeg ambush\">first engagements</a> of the <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a> takes place.", "links": [{"title": "First Dáil", "link": "https://wikipedia.org/wiki/First_D%C3%A1il"}, {"title": "Irish Declaration of Independence", "link": "https://wikipedia.org/wiki/Irish_Declaration_of_Independence"}, {"title": "Irish Republic", "link": "https://wikipedia.org/wiki/Irish_Republic"}, {"title": "Soloheadbeg ambush", "link": "https://wikipedia.org/wiki/Soloheadbeg_ambush"}, {"title": "Irish War of Independence", "link": "https://wikipedia.org/wiki/Irish_War_of_Independence"}]}, {"year": "1925", "text": "Albania declares itself a republic.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albania</a> declares itself a republic.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albania</a> declares itself a republic.", "links": [{"title": "Albania", "link": "https://wikipedia.org/wiki/Albania"}]}, {"year": "1931", "text": "Sir <PERSON> is sworn in as the first Australian-born Governor-General of Australia.", "html": "1931 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as the first Australian-born <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a>.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as the first Australian-born <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}]}, {"year": "1932", "text": "Finland and the Soviet Union sign a non-aggression treaty.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> sign a <a href=\"https://wikipedia.org/wiki/Soviet%E2%80%93Finnish_Non-Aggression_Pact\" title=\"Soviet-Finnish Non-Aggression Pact\">non-aggression treaty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> sign a <a href=\"https://wikipedia.org/wiki/Soviet%E2%80%93Finnish_Non-Aggression_Pact\" title=\"Soviet-Finnish Non-Aggression Pact\">non-aggression treaty</a>.", "links": [{"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Soviet-Finnish Non-Aggression Pact", "link": "https://wikipedia.org/wiki/Soviet%E2%80%93Finnish_Non-Aggression_Pact"}]}, {"year": "1941", "text": "Sparked by the murder of a German officer in Bucharest, Romania the day before, members of the Iron Guard engaged in a rebellion and pogrom killing 125 Jews.", "html": "1941 - Sparked by the murder of a German officer in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>, Romania the day before, members of the <a href=\"https://wikipedia.org/wiki/Iron_Guard\" title=\"Iron Guard\">Iron Guard</a> engaged in a <a href=\"https://wikipedia.org/wiki/Legionnaires%27_rebellion_and_Bucharest_pogrom\" title=\"Legionnaires' rebellion and Bucharest pogrom\">rebellion and pogrom</a> killing 125 Jews.", "no_year_html": "Sparked by the murder of a German officer in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>, Romania the day before, members of the <a href=\"https://wikipedia.org/wiki/Iron_Guard\" title=\"Iron Guard\">Iron Guard</a> engaged in a <a href=\"https://wikipedia.org/wiki/Legionnaires%27_rebellion_and_Bucharest_pogrom\" title=\"Legionnaires' rebellion and Bucharest pogrom\">rebellion and pogrom</a> killing 125 Jews.", "links": [{"title": "Bucharest", "link": "https://wikipedia.org/wiki/Bucharest"}, {"title": "Iron Guard", "link": "https://wikipedia.org/wiki/Iron_Guard"}, {"title": "Legionnaires' rebellion and Bucharest pogrom", "link": "https://wikipedia.org/wiki/Legionnaires%27_rebellion_and_Bucharest_pogrom"}]}, {"year": "1942", "text": "The Jewish resistance organization, Fareynikte Partizaner Organizatsye, based in the Vilna Ghetto was established.", "html": "1942 - The <a href=\"https://wikipedia.org/wiki/Jewish_resistance_in_German-occupied_Europe\" title=\"Jewish resistance in German-occupied Europe\">Jewish resistance</a> organization, <a href=\"https://wikipedia.org/wiki/Fareynikte_Partizaner_Organizatsye\" title=\"Fareynikte Partizaner Organizatsye\">Fareynikte Partizaner Organizatsye</a>, based in the <a href=\"https://wikipedia.org/wiki/Vilna_Ghetto\" title=\"Vilna Ghetto\">Vilna Ghetto</a> was established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Jewish_resistance_in_German-occupied_Europe\" title=\"Jewish resistance in German-occupied Europe\">Jewish resistance</a> organization, <a href=\"https://wikipedia.org/wiki/Fareynikte_Partizaner_Organizatsye\" title=\"Fareynikte Partizaner Organizatsye\">Fareynikte Partizaner Organizatsye</a>, based in the <a href=\"https://wikipedia.org/wiki/Vilna_Ghetto\" title=\"Vilna Ghetto\">Vilna Ghetto</a> was established.", "links": [{"title": "Jewish resistance in German-occupied Europe", "link": "https://wikipedia.org/wiki/Jewish_resistance_in_German-occupied_Europe"}, {"title": "Fareynikte Partizaner Organizatsye", "link": "https://wikipedia.org/wiki/Fareynikte_Partizaner_Organizatsye"}, {"title": "Vilna Ghetto", "link": "https://wikipedia.org/wiki/Vilna_Ghetto"}]}, {"year": "1943", "text": "As part of Operation Animals, British SOE saboteurs destroy the railway bridge over the Asopos River, and guerrillas of the Greek People's Liberation Army ambush and destroy a German convoy at the Battle of Sarantaporos.", "html": "1943 - As part of <a href=\"https://wikipedia.org/wiki/Operation_Animals\" title=\"Operation Animals\">Operation Animals</a>, British <a href=\"https://wikipedia.org/wiki/Special_Operations_Executive\" title=\"Special Operations Executive\">SOE</a> saboteurs <a href=\"https://wikipedia.org/wiki/Operation_Washing\" title=\"Operation Washing\">destroy</a> the railway bridge over the Asopos River, and guerrillas of the <a href=\"https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army\" class=\"mw-redirect\" title=\"Greek People's Liberation Army\">Greek People's Liberation Army</a> ambush and destroy a German convoy at the <a href=\"https://wikipedia.org/wiki/Battle_of_Sarantaporos_(1943)\" title=\"Battle of Sarantaporos (1943)\">Battle of Sarantaporos</a>.", "no_year_html": "As part of <a href=\"https://wikipedia.org/wiki/Operation_Animals\" title=\"Operation Animals\">Operation Animals</a>, British <a href=\"https://wikipedia.org/wiki/Special_Operations_Executive\" title=\"Special Operations Executive\">SOE</a> saboteurs <a href=\"https://wikipedia.org/wiki/Operation_Washing\" title=\"Operation Washing\">destroy</a> the railway bridge over the Asopos River, and guerrillas of the <a href=\"https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army\" class=\"mw-redirect\" title=\"Greek People's Liberation Army\">Greek People's Liberation Army</a> ambush and destroy a German convoy at the <a href=\"https://wikipedia.org/wiki/Battle_of_Sarantaporos_(1943)\" title=\"Battle of Sarantaporos (1943)\">Battle of Sarantaporos</a>.", "links": [{"title": "Operation Animals", "link": "https://wikipedia.org/wiki/Operation_Animals"}, {"title": "Special Operations Executive", "link": "https://wikipedia.org/wiki/Special_Operations_Executive"}, {"title": "Operation Washing", "link": "https://wikipedia.org/wiki/Operation_Washing"}, {"title": "Greek People's Liberation Army", "link": "https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army"}, {"title": "Battle of Sarantaporos (1943)", "link": "https://wikipedia.org/wiki/Battle_of_Sarantaporos_(1943)"}]}, {"year": "1948", "text": "The Flag of Quebec is adopted and flown for the first time over the National Assembly of Quebec. The day is marked annually as Québec Flag Day.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Flag_of_Quebec\" title=\"Flag of Quebec\">Flag of Quebec</a> is adopted and flown for the first time over the <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Quebec\" title=\"National Assembly of Quebec\">National Assembly of Quebec</a>. The day is marked annually as Québec Flag Day.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Flag_of_Quebec\" title=\"Flag of Quebec\">Flag of Quebec</a> is adopted and flown for the first time over the <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Quebec\" title=\"National Assembly of Quebec\">National Assembly of Quebec</a>. The day is marked annually as Québec Flag Day.", "links": [{"title": "Flag of Quebec", "link": "https://wikipedia.org/wiki/Flag_of_Quebec"}, {"title": "National Assembly of Quebec", "link": "https://wikipedia.org/wiki/National_Assembly_of_Quebec"}]}, {"year": "1950", "text": "American lawyer and government official <PERSON><PERSON> is convicted of perjury.", "html": "1950 - American lawyer and government official <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> His<PERSON>\"><PERSON><PERSON></a> is convicted of <a href=\"https://wikipedia.org/wiki/Perjury\" title=\"Perjury\">perjury</a>.", "no_year_html": "American lawyer and government official <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hiss\"><PERSON><PERSON></a> is convicted of <a href=\"https://wikipedia.org/wiki/Perjury\" title=\"Perjury\">perjury</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hiss"}, {"title": "Perjury", "link": "https://wikipedia.org/wiki/Perjury"}]}, {"year": "1951", "text": "The catastrophic eruption of Mount Lamington in Papua New Guinea claims 2,942 lives.", "html": "1951 - The catastrophic <a href=\"https://wikipedia.org/wiki/1951_eruption_of_Mount_Lamington\" title=\"1951 eruption of Mount Lamington\">eruption of Mount Lamington</a> in Papua New Guinea claims 2,942 lives.", "no_year_html": "The catastrophic <a href=\"https://wikipedia.org/wiki/1951_eruption_of_Mount_Lamington\" title=\"1951 eruption of Mount Lamington\">eruption of Mount Lamington</a> in Papua New Guinea claims 2,942 lives.", "links": [{"title": "1951 eruption of Mount Lamington", "link": "https://wikipedia.org/wiki/1951_eruption_of_Mount_Lamington"}]}, {"year": "1954", "text": "The first nuclear-powered submarine, the USS Nautilus, is launched in Groton, Connecticut by <PERSON><PERSON>, the First Lady of the United States.", "html": "1954 - The first <a href=\"https://wikipedia.org/wiki/Nuclear_marine_propulsion\" title=\"Nuclear marine propulsion\">nuclear-powered</a> submarine, the <a href=\"https://wikipedia.org/wiki/USS_Nautilus_(SSN-571)\" title=\"USS Nautilus (SSN-571)\">USS <i>Nautilus</i></a>, is <a href=\"https://wikipedia.org/wiki/Ceremonial_ship_launching\" title=\"Ceremonial ship launching\">launched</a> in <a href=\"https://wikipedia.org/wiki/Groton,_Connecticut\" title=\"Groton, Connecticut\">Groton, Connecticut</a> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Nuclear_marine_propulsion\" title=\"Nuclear marine propulsion\">nuclear-powered</a> submarine, the <a href=\"https://wikipedia.org/wiki/USS_Nautilus_(SSN-571)\" title=\"USS Nautilus (SSN-571)\">USS <i>Nautilus</i></a>, is <a href=\"https://wikipedia.org/wiki/Ceremonial_ship_launching\" title=\"Ceremonial ship launching\">launched</a> in <a href=\"https://wikipedia.org/wiki/Groton,_Connecticut\" title=\"Groton, Connecticut\">Groton, Connecticut</a> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a>.", "links": [{"title": "Nuclear marine propulsion", "link": "https://wikipedia.org/wiki/Nuclear_marine_propulsion"}, {"title": "USS Nautilus (SSN-571)", "link": "https://wikipedia.org/wiki/USS_Nautilus_(SSN-571)"}, {"title": "Ceremonial ship launching", "link": "https://wikipedia.org/wiki/Ceremonial_ship_launching"}, {"title": "Groton, Connecticut", "link": "https://wikipedia.org/wiki/Groton,_Connecticut"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1960", "text": "<PERSON> 1B, a Mercury spacecraft, lifts off from Wallops Island, Virginia with <PERSON>, a female rhesus monkey on board.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Little_Joe_1B\" title=\"Little Joe 1B\">Little Joe 1B</a>, a <a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Mercury</a> spacecraft, lifts off from <a href=\"https://wikipedia.org/wiki/Wallops_Island\" title=\"Wallops Island\">Wallops Island</a>, <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> with <a href=\"https://wikipedia.org/wiki/Miss_Sam\" class=\"mw-redirect\" title=\"Miss Sam\">Miss Sam</a>, a female <a href=\"https://wikipedia.org/wiki/Rhesus_macaque\" title=\"Rhesus macaque\">rhesus monkey</a> on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Joe_1B\" title=\"Little Joe 1B\">Little Joe 1B</a>, a <a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Mercury</a> spacecraft, lifts off from <a href=\"https://wikipedia.org/wiki/Wallops_Island\" title=\"Wallops Island\">Wallops Island</a>, <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> with <a href=\"https://wikipedia.org/wiki/Miss_Sam\" class=\"mw-redirect\" title=\"Miss Sam\">Miss Sam</a>, a female <a href=\"https://wikipedia.org/wiki/Rhesus_macaque\" title=\"Rhesus macaque\">rhesus monkey</a> on board.", "links": [{"title": "<PERSON> 1B", "link": "https://wikipedia.org/wiki/<PERSON>_Joe_1B"}, {"title": "Project Mercury", "link": "https://wikipedia.org/wiki/Project_Mercury"}, {"title": "Wallops Island", "link": "https://wikipedia.org/wiki/Wallops_Island"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "Miss Sam", "link": "https://wikipedia.org/wiki/Miss_<PERSON>"}, {"title": "Rhesus macaque", "link": "https://wikipedia.org/wiki/Rhesus_macaque"}]}, {"year": "1960", "text": "Avianca Flight 671 crashes at Montego Bay, Jamaica airport, killing 37 people.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Avianca_Flight_671\" title=\"Avianca Flight 671\">Avianca Flight 671</a> crashes at <a href=\"https://wikipedia.org/wiki/Montego_Bay\" title=\"Montego Bay\">Montego Bay</a>, Jamaica airport, killing 37 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avianca_Flight_671\" title=\"Avianca Flight 671\">Avianca Flight 671</a> crashes at <a href=\"https://wikipedia.org/wiki/Montego_Bay\" title=\"Montego Bay\">Montego Bay</a>, Jamaica airport, killing 37 people.", "links": [{"title": "Avianca Flight 671", "link": "https://wikipedia.org/wiki/Avianca_Flight_671"}, {"title": "Montego Bay", "link": "https://wikipedia.org/wiki/Montego_Bay"}]}, {"year": "1960", "text": "A coal mine collapses at Holly Country, South Africa, killing 435 miners.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Coalbrook_mining_disaster\" title=\"Coalbrook mining disaster\">A coal mine collapses</a> at <a href=\"https://wikipedia.org/wiki/Holly_Country\" title=\"Holly Country\">Holly Country</a>, South Africa, killing 435 miners.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coalbrook_mining_disaster\" title=\"Coalbrook mining disaster\">A coal mine collapses</a> at <a href=\"https://wikipedia.org/wiki/Holly_Country\" title=\"Holly Country\">Holly Country</a>, South Africa, killing 435 miners.", "links": [{"title": "Coalbrook mining disaster", "link": "https://wikipedia.org/wiki/Coalbrook_mining_disaster"}, {"title": "Holly Country", "link": "https://wikipedia.org/wiki/Holly_Country"}]}, {"year": "1963", "text": "The Chicago North Shore and Milwaukee Railroad ends operation.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Chicago_North_Shore_and_Milwaukee_Railroad\" title=\"Chicago North Shore and Milwaukee Railroad\">Chicago North Shore and Milwaukee Railroad</a> ends operation.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chicago_North_Shore_and_Milwaukee_Railroad\" title=\"Chicago North Shore and Milwaukee Railroad\">Chicago North Shore and Milwaukee Railroad</a> ends operation.", "links": [{"title": "Chicago North Shore and Milwaukee Railroad", "link": "https://wikipedia.org/wiki/Chicago_North_Shore_and_Milwaukee_Railroad"}]}, {"year": "1968", "text": "Vietnam War, Battle of Khe Sanh: One of the most publicized and controversial battles of the war begins.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>, <a href=\"https://wikipedia.org/wiki/Battle_of_Khe_Sanh\" title=\"Battle of Khe Sanh\">Battle of Khe Sanh</a>: One of the most publicized and controversial battles of the war begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>, <a href=\"https://wikipedia.org/wiki/Battle_of_Khe_Sanh\" title=\"Battle of Khe Sanh\">Battle of Khe Sanh</a>: One of the most publicized and controversial battles of the war begins.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Battle of Khe Sanh", "link": "https://wikipedia.org/wiki/Battle_of_Khe_Sanh"}]}, {"year": "1968", "text": "A B-52 bomber crashes near Thule Air Base, contaminating the area after its nuclear payload ruptures. One of the four bombs remains unaccounted for after the cleanup operation is complete.", "html": "1968 - A <a href=\"https://wikipedia.org/wiki/B-52\" class=\"mw-redirect\" title=\"B-52\">B-52</a> bomber <a href=\"https://wikipedia.org/wiki/1968_Thule_Air_Base_B-52_crash\" title=\"1968 Thule Air Base B-52 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Thule_Air_Base\" class=\"mw-redirect\" title=\"Thule Air Base\">Thule Air Base</a>, contaminating the area after its nuclear payload ruptures. One of the four bombs remains unaccounted for after the cleanup operation is complete.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/B-52\" class=\"mw-redirect\" title=\"B-52\">B-52</a> bomber <a href=\"https://wikipedia.org/wiki/1968_Thule_Air_Base_B-52_crash\" title=\"1968 Thule Air Base B-52 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Thule_Air_Base\" class=\"mw-redirect\" title=\"Thule Air Base\">Thule Air Base</a>, contaminating the area after its nuclear payload ruptures. One of the four bombs remains unaccounted for after the cleanup operation is complete.", "links": [{"title": "B-52", "link": "https://wikipedia.org/wiki/B-52"}, {"title": "1968 Thule Air Base B-52 crash", "link": "https://wikipedia.org/wiki/1968_Thule_Air_Base_B-52_crash"}, {"title": "Thule Air Base", "link": "https://wikipedia.org/wiki/Thule_Air_Base"}]}, {"year": "1971", "text": "The current Emley Moor transmitting station, the tallest free-standing structure in the United Kingdom, begins transmitting UHF broadcasts.", "html": "1971 - The current <a href=\"https://wikipedia.org/wiki/Emley_Moor_transmitting_station\" title=\"Emley Moor transmitting station\">Emley Moor transmitting station</a>, the tallest free-standing structure in the United Kingdom, begins transmitting <a href=\"https://wikipedia.org/wiki/UHF\" class=\"mw-redirect\" title=\"UHF\">UHF</a> broadcasts.", "no_year_html": "The current <a href=\"https://wikipedia.org/wiki/Emley_Moor_transmitting_station\" title=\"Emley Moor transmitting station\">Emley Moor transmitting station</a>, the tallest free-standing structure in the United Kingdom, begins transmitting <a href=\"https://wikipedia.org/wiki/UHF\" class=\"mw-redirect\" title=\"UHF\">UHF</a> broadcasts.", "links": [{"title": "Emley Moor transmitting station", "link": "https://wikipedia.org/wiki/Emley_Moor_transmitting_station"}, {"title": "UHF", "link": "https://wikipedia.org/wiki/UHF"}]}, {"year": "1976", "text": "Commercial service of Concorde begins with the London-Bahrain and Paris-Rio routes.", "html": "1976 - Commercial service of <i><a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a></i> begins with the <a href=\"https://wikipedia.org/wiki/Concorde#Scheduled_flights\" title=\"Concorde\">London-Bahrain and Paris-Rio</a> routes.", "no_year_html": "Commercial service of <i><a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a></i> begins with the <a href=\"https://wikipedia.org/wiki/Concorde#Scheduled_flights\" title=\"Concorde\">London-Bahrain and Paris-Rio</a> routes.", "links": [{"title": "Concorde", "link": "https://wikipedia.org/wiki/Concorde"}, {"title": "Concorde", "link": "https://wikipedia.org/wiki/Concorde#Scheduled_flights"}]}, {"year": "1980", "text": "Iran Air Flight 291 crashes in the Alborz Mountains while on approach to Mehrabad International Airport in Tehran, Iran, killing 128 people.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Iran_Air_Flight_291\" title=\"Iran Air Flight 291\">Iran Air Flight 291</a> crashes in the <a href=\"https://wikipedia.org/wiki/Alborz\" title=\"Alborz\">Alborz Mountains</a> while on approach to <a href=\"https://wikipedia.org/wiki/Mehrabad_International_Airport\" title=\"Mehrabad International Airport\">Mehrabad International Airport</a> in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, Iran, killing 128 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran_Air_Flight_291\" title=\"Iran Air Flight 291\">Iran Air Flight 291</a> crashes in the <a href=\"https://wikipedia.org/wiki/Alborz\" title=\"Alborz\">Alborz Mountains</a> while on approach to <a href=\"https://wikipedia.org/wiki/Mehrabad_International_Airport\" title=\"Mehrabad International Airport\">Mehrabad International Airport</a> in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, Iran, killing 128 people.", "links": [{"title": "Iran Air Flight 291", "link": "https://wikipedia.org/wiki/Iran_Air_Flight_291"}, {"title": "Alborz", "link": "https://wikipedia.org/wiki/Alborz"}, {"title": "Mehrabad International Airport", "link": "https://wikipedia.org/wiki/Mehrabad_International_Airport"}, {"title": "Tehran", "link": "https://wikipedia.org/wiki/Tehran"}]}, {"year": "1981", "text": "Production of the DeLorean sports car begins in Dunmurry, Northern Ireland, United Kingdom.", "html": "1981 - Production of the <a href=\"https://wikipedia.org/wiki/DMC_DeLorean\" title=\"DMC DeLorean\">DeLorean</a> sports car begins in <a href=\"https://wikipedia.org/wiki/Dunmurry\" title=\"Dunmurry\">Dunmurry</a>, Northern Ireland, United Kingdom.", "no_year_html": "Production of the <a href=\"https://wikipedia.org/wiki/DMC_DeLorean\" title=\"DMC DeLorean\">DeLorean</a> sports car begins in <a href=\"https://wikipedia.org/wiki/Dunmurry\" title=\"Dunmurry\">Dunmurry</a>, Northern Ireland, United Kingdom.", "links": [{"title": "DMC DeLorean", "link": "https://wikipedia.org/wiki/DMC_DeLorean"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1985", "text": "Galaxy Airlines Flight 203 crashes near Reno-Tahoe International Airport in Reno, Nevada, killing 70 people.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Galaxy_Airlines_Flight_203\" title=\"Galaxy Airlines Flight 203\">Galaxy Airlines Flight 203</a> crashes near <a href=\"https://wikipedia.org/wiki/Reno%E2%80%93Tahoe_International_Airport\" title=\"Reno-Tahoe International Airport\">Reno-Tahoe International Airport</a> in <a href=\"https://wikipedia.org/wiki/Reno,_Nevada\" title=\"Reno, Nevada\">Reno, Nevada</a>, killing 70 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Galaxy_Airlines_Flight_203\" title=\"Galaxy Airlines Flight 203\">Galaxy Airlines Flight 203</a> crashes near <a href=\"https://wikipedia.org/wiki/Reno%E2%80%93Tahoe_International_Airport\" title=\"Reno-Tahoe International Airport\">Reno-Tahoe International Airport</a> in <a href=\"https://wikipedia.org/wiki/Reno,_Nevada\" title=\"Reno, Nevada\">Reno, Nevada</a>, killing 70 people.", "links": [{"title": "Galaxy Airlines Flight 203", "link": "https://wikipedia.org/wiki/Galaxy_Airlines_Flight_203"}, {"title": "Reno-Tahoe International Airport", "link": "https://wikipedia.org/wiki/Reno%E2%80%93Tahoe_International_Airport"}, {"title": "Reno, Nevada", "link": "https://wikipedia.org/wiki/Reno,_Nevada"}]}, {"year": "1997", "text": "The U.S. House of Representatives votes 395-28 to reprimand <PERSON><PERSON> for ethics violations, making him the first Speaker of the House to be so disciplined.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/U.S._House_of_Representatives\" class=\"mw-redirect\" title=\"U.S. House of Representatives\">U.S. House of Representatives</a> votes 395-28 to reprimand <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> for ethics violations, making him the first Speaker of the House to be so disciplined.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/U.S._House_of_Representatives\" class=\"mw-redirect\" title=\"U.S. House of Representatives\">U.S. House of Representatives</a> votes 395-28 to reprimand <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> for ethics violations, making him the first Speaker of the House to be so disciplined.", "links": [{"title": "U.S. House of Representatives", "link": "https://wikipedia.org/wiki/U.S._House_of_Representatives"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1999", "text": "War on Drugs: In one of the largest drug busts in American history, the United States Coast Guard intercepts a ship with over 4,300 kilograms (9,500 lb) of cocaine on board.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/War_on_Drugs\" class=\"mw-redirect\" title=\"War on Drugs\">War on Drugs</a>: In one of the largest drug busts in American history, the <a href=\"https://wikipedia.org/wiki/United_States_Coast_Guard\" title=\"United States Coast Guard\">United States Coast Guard</a> intercepts a ship with over 4,300 kilograms (9,500 lb) of <a href=\"https://wikipedia.org/wiki/Cocaine\" title=\"Cocaine\">cocaine</a> on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_on_Drugs\" class=\"mw-redirect\" title=\"War on Drugs\">War on Drugs</a>: In one of the largest drug busts in American history, the <a href=\"https://wikipedia.org/wiki/United_States_Coast_Guard\" title=\"United States Coast Guard\">United States Coast Guard</a> intercepts a ship with over 4,300 kilograms (9,500 lb) of <a href=\"https://wikipedia.org/wiki/Cocaine\" title=\"Cocaine\">cocaine</a> on board.", "links": [{"title": "War on Drugs", "link": "https://wikipedia.org/wiki/War_on_Drugs"}, {"title": "United States Coast Guard", "link": "https://wikipedia.org/wiki/United_States_Coast_Guard"}, {"title": "Cocaine", "link": "https://wikipedia.org/wiki/Cocaine"}]}, {"year": "2000", "text": "Ecuador: After the Ecuadorian Congress is seized by indigenous organizations, <PERSON><PERSON> <PERSON><PERSON>, <PERSON> and <PERSON> depose President <PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON> is later replaced by <PERSON><PERSON>, who resigns and allows Vice-President <PERSON> to succeed <PERSON><PERSON><PERSON>.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>: After the Ecuadorian Congress is seized by indigenous organizations, Col. <a href=\"https://wikipedia.org/wiki/Luc<PERSON>_Guti%C3%A9rrez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON> and <PERSON> depose President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>. <PERSON><PERSON><PERSON><PERSON> is later replaced by <PERSON><PERSON> <PERSON>, who resigns and allows Vice-President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to succeed <PERSON><PERSON><PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>: After the Ecuadorian Congress is seized by indigenous organizations, Col. <a href=\"https://wikipedia.org/wiki/Luc<PERSON>_Guti%C3%A9rrez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON> and <PERSON> depose President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>. <PERSON><PERSON><PERSON><PERSON> is later replaced by <PERSON><PERSON> <PERSON>, who resigns and allows Vice-President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to succeed <PERSON><PERSON><PERSON>.", "links": [{"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucio_Guti%C3%A9rrez"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "A 7.6 magnitude earthquake strikes the Mexican state of Colima, killing 29 and leaving approximately 10,000 people homeless.", "html": "2003 - A 7.6 magnitude <a href=\"https://wikipedia.org/wiki/2003_Colima_earthquake\" title=\"2003 Colima earthquake\">earthquake</a> strikes the Mexican state of <a href=\"https://wikipedia.org/wiki/Colima\" title=\"Colima\">Colima</a>, killing 29 and leaving approximately 10,000 people homeless.", "no_year_html": "A 7.6 magnitude <a href=\"https://wikipedia.org/wiki/2003_Colima_earthquake\" title=\"2003 Colima earthquake\">earthquake</a> strikes the Mexican state of <a href=\"https://wikipedia.org/wiki/Colima\" title=\"Colima\">Colima</a>, killing 29 and leaving approximately 10,000 people homeless.", "links": [{"title": "2003 Colima earthquake", "link": "https://wikipedia.org/wiki/2003_Colima_earthquake"}, {"title": "Colima", "link": "https://wikipedia.org/wiki/Colima"}]}, {"year": "2004", "text": "NASA's MER-A (the Mars Rover Spirit) ceases communication with mission control. The problem lies in the management of its flash memory and is fixed remotely from Earth on February 6.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/MER-A\" class=\"mw-redirect\" title=\"MER-A\">MER-A</a> (the Mars Rover <i>Spirit</i>) ceases communication with mission control. The problem lies in the management of its <a href=\"https://wikipedia.org/wiki/Flash_memory\" title=\"Flash memory\">flash memory</a> and is fixed remotely from Earth on February 6.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/MER-A\" class=\"mw-redirect\" title=\"MER-A\">MER-A</a> (the Mars Rover <i>Spirit</i>) ceases communication with mission control. The problem lies in the management of its <a href=\"https://wikipedia.org/wiki/Flash_memory\" title=\"Flash memory\">flash memory</a> and is fixed remotely from Earth on February 6.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "MER-A", "link": "https://wikipedia.org/wiki/MER-A"}, {"title": "Flash memory", "link": "https://wikipedia.org/wiki/Flash_memory"}]}, {"year": "2005", "text": "In Belmopan, Belize, the unrest over the government's new taxes erupts into riots.", "html": "2005 - In <a href=\"https://wikipedia.org/wiki/Belmopan\" title=\"Belmopan\">Belmopan</a>, Belize, the <a href=\"https://wikipedia.org/wiki/2005_Belize_unrest\" title=\"2005 Belize unrest\">unrest over the government's new taxes</a> erupts into riots.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Belmopan\" title=\"Belmopan\">Belmopan</a>, Belize, the <a href=\"https://wikipedia.org/wiki/2005_Belize_unrest\" title=\"2005 Belize unrest\">unrest over the government's new taxes</a> erupts into riots.", "links": [{"title": "Belmopan", "link": "https://wikipedia.org/wiki/Belmopan"}, {"title": "2005 Belize unrest", "link": "https://wikipedia.org/wiki/2005_Belize_unrest"}]}, {"year": "2009", "text": "Israel withdraws from the Gaza Strip, officially ending a three-week war it had with Hamas. However, intermittent fire by both sides continues in the weeks to follow.", "html": "2009 - Israel withdraws from the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a>, officially ending a <a href=\"https://wikipedia.org/wiki/Gaza_War_(2008%E2%80%9309)\" class=\"mw-redirect\" title=\"Gaza War (2008-09)\">three-week war</a> it had with Hamas. However, intermittent fire by both sides continues in the weeks to follow.", "no_year_html": "Israel withdraws from the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a>, officially ending a <a href=\"https://wikipedia.org/wiki/Gaza_War_(2008%E2%80%9309)\" class=\"mw-redirect\" title=\"Gaza War (2008-09)\">three-week war</a> it had with Hamas. However, intermittent fire by both sides continues in the weeks to follow.", "links": [{"title": "Gaza Strip", "link": "https://wikipedia.org/wiki/Gaza_Strip"}, {"title": "Gaza War (2008-09)", "link": "https://wikipedia.org/wiki/Gaza_War_(2008%E2%80%9309)"}]}, {"year": "2011", "text": "Anti-government demonstrations take place in Tirana, Albania. Four people died from gunshots, allegedly fired from armed police protecting the Prime Minister's office.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/2011_Albanian_opposition_demonstrations\" title=\"2011 Albanian opposition demonstrations\">Anti-government demonstrations</a> take place in <a href=\"https://wikipedia.org/wiki/Tirana\" title=\"Tirana\">Tirana</a>, Albania. Four people died from gunshots, allegedly fired from armed police protecting the Prime Minister's office.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2011_Albanian_opposition_demonstrations\" title=\"2011 Albanian opposition demonstrations\">Anti-government demonstrations</a> take place in <a href=\"https://wikipedia.org/wiki/Tirana\" title=\"Tirana\">Tirana</a>, Albania. Four people died from gunshots, allegedly fired from armed police protecting the Prime Minister's office.", "links": [{"title": "2011 Albanian opposition demonstrations", "link": "https://wikipedia.org/wiki/2011_Albanian_opposition_demonstrations"}, {"title": "Tirana", "link": "https://wikipedia.org/wiki/Tirana"}]}, {"year": "2014", "text": "Rojava conflict: The Jazira Canton declares its autonomy from the Syrian Arab Republic.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Rojava_conflict\" title=\"Rojava conflict\">Rojava conflict</a>: The <a href=\"https://wikipedia.org/wiki/Jazira_Region\" title=\"Jazira Region\">Jazira Canton</a> declares its autonomy from the <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syrian Arab Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rojava_conflict\" title=\"Rojava conflict\">Rojava conflict</a>: The <a href=\"https://wikipedia.org/wiki/Jazira_Region\" title=\"Jazira Region\">Jazira Canton</a> declares its autonomy from the <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syrian Arab Republic</a>.", "links": [{"title": "Rojava conflict", "link": "https://wikipedia.org/wiki/Rojava_conflict"}, {"title": "Jazira Region", "link": "https://wikipedia.org/wiki/Jazira_Region"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "2017", "text": "Over 400 cities across America and 160+ countries worldwide participate in a large-scale women's march, on <PERSON>'s first full day as President of the United States.", "html": "2017 - Over 400 cities across America and 160+ countries worldwide participate in a large-scale <a href=\"https://wikipedia.org/wiki/2017_Women%27s_March\" title=\"2017 Women's March\">women's march</a>, on <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s first full day as <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "no_year_html": "Over 400 cities across America and 160+ countries worldwide participate in a large-scale <a href=\"https://wikipedia.org/wiki/2017_Women%27s_March\" title=\"2017 Women's March\">women's march</a>, on <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s first full day as <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "links": [{"title": "2017 Women's March", "link": "https://wikipedia.org/wiki/2017_Women%27s_March"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "2023", "text": "<PERSON><PERSON>, 72, opens fire in a dance studio in Monterey Park, California, killing eleven people and injuring nine others before later committing suicide. It is the worst mass shooting in Los Angeles County since the 2008 Covina massacre.", "html": "2023 - <PERSON><PERSON>, 72, <a href=\"https://wikipedia.org/wiki/2023_Monterey_Park_shooting\" title=\"2023 Monterey Park shooting\">opens fire</a> in a dance studio in <a href=\"https://wikipedia.org/wiki/Monterey_Park,_California\" title=\"Monterey Park, California\">Monterey Park, California</a>, killing eleven people and injuring nine others before later committing suicide. It is the worst mass shooting in Los Angeles County since the 2008 <a href=\"https://wikipedia.org/wiki/Covina_massacre\" title=\"Covina massacre\">Covina massacre</a>.", "no_year_html": "<PERSON><PERSON>, 72, <a href=\"https://wikipedia.org/wiki/2023_Monterey_Park_shooting\" title=\"2023 Monterey Park shooting\">opens fire</a> in a dance studio in <a href=\"https://wikipedia.org/wiki/Monterey_Park,_California\" title=\"Monterey Park, California\">Monterey Park, California</a>, killing eleven people and injuring nine others before later committing suicide. It is the worst mass shooting in Los Angeles County since the 2008 <a href=\"https://wikipedia.org/wiki/Covina_massacre\" title=\"Covina massacre\">Covina massacre</a>.", "links": [{"title": "2023 Monterey Park shooting", "link": "https://wikipedia.org/wiki/2023_Monterey_Park_shooting"}, {"title": "Monterey Park, California", "link": "https://wikipedia.org/wiki/Monterey_Park,_California"}, {"title": "Covina massacre", "link": "https://wikipedia.org/wiki/Covina_massacre"}]}, {"year": "2025", "text": "A fire at the Grand Kartal Hotel in the Kartalkaya ski resort in Bolu Province, Turkey, results in 78 people dead and 51 injured.", "html": "2025 - A <a href=\"https://wikipedia.org/wiki/2025_Kartalkaya_hotel_fire\" title=\"2025 Kartalkaya hotel fire\">fire</a> at the Grand Kartal Hotel in the <a href=\"https://wikipedia.org/wiki/Kartalkaya\" title=\"Kartalkaya\">Kartalkaya</a> ski resort in <a href=\"https://wikipedia.org/wiki/Bolu_Province\" title=\"Bolu Province\">Bolu Province</a>, Turkey, results in 78 people dead and 51 injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2025_Kartalkaya_hotel_fire\" title=\"2025 Kartalkaya hotel fire\">fire</a> at the Grand Kartal Hotel in the <a href=\"https://wikipedia.org/wiki/Kartalkaya\" title=\"Kartalkaya\">Kartalkaya</a> ski resort in <a href=\"https://wikipedia.org/wiki/Bolu_Province\" title=\"Bolu Province\">Bolu Province</a>, Turkey, results in 78 people dead and 51 injured.", "links": [{"title": "2025 Kartalkaya hotel fire", "link": "https://wikipedia.org/wiki/2025_Kartalkaya_hotel_fire"}, {"title": "Kartalkaya", "link": "https://wikipedia.org/wiki/Kartalkaya"}, {"title": "Bolu Province", "link": "https://wikipedia.org/wiki/Bolu_Province"}]}], "Births": [{"year": "1264", "text": "<PERSON>, Prince of Scotland (d. 1284)", "html": "1264 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Scotland\" title=\"<PERSON>, Prince of Scotland\"><PERSON>, Prince of Scotland</a> (d. 1284)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Scotland\" title=\"<PERSON>, Prince of Scotland\"><PERSON>, Prince of Scotland</a> (d. 1284)", "links": [{"title": "<PERSON>, Prince of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Scotland"}]}, {"year": "1277", "text": "<PERSON><PERSON><PERSON>, lord of Milan (d. 1328)", "html": "1277 - <a href=\"https://wikipedia.org/wiki/<PERSON>az<PERSON>_<PERSON>_Visconti\" title=\"Galeazzo I Visconti\"><PERSON><PERSON><PERSON> <PERSON></a>, lord of <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a> (d. 1328)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>nti\" title=\"Galeazzo I Visconti\"><PERSON><PERSON><PERSON> <PERSON></a>, lord of <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a> (d. 1328)", "links": [{"title": "Galeazzo I Visconti", "link": "https://wikipedia.org/wiki/<PERSON>az<PERSON>_<PERSON>_<PERSON>"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}]}, {"year": "1338", "text": "<PERSON> of France (d. 1380)", "html": "1338 - <a href=\"https://wikipedia.org/wiki/Charles_V_of_France\" title=\"Charles V of France\"><PERSON> of France</a> (d. 1380)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_V_of_France\" title=\"Charles V of France\"><PERSON> of France</a> (d. 1380)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_V_of_France"}]}, {"year": "1493", "text": "<PERSON>, Italian cardinal and diplomat (d. 1556)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and diplomat (d. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and diplomat (d. 1556)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1598", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese samurai and daimyō (d. 1645)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/Matsudai<PERSON>_<PERSON>\" title=\"Mat<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese samurai and daimyō (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matsudai<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese samurai and daimyō (d. 1645)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1612", "text": "<PERSON> of Nassau-Dietz, count of Nassau-Dietz (d. 1640)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Nassau-Dietz\" title=\"<PERSON> of Nassau-Dietz\"><PERSON> of Nassau-Dietz</a>, count of <a href=\"https://wikipedia.org/wiki/Nassau-Dietz\" class=\"mw-redirect\" title=\"Nassau-Dietz\">Nassau-Dietz</a> (d. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Nassau-Dietz\" title=\"<PERSON> of Nassau-Dietz\"><PERSON> of Nassau-Dietz</a>, count of <a href=\"https://wikipedia.org/wiki/Nassau-Dietz\" class=\"mw-redirect\" title=\"Nassau-Dietz\">Nassau-Dietz</a> (d. 1640)", "links": [{"title": "<PERSON> of Nassau-Dietz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Nassau-Dietz"}, {"title": "Nassau-Dietz", "link": "https://wikipedia.org/wiki/Nassau-Dietz"}]}, {"year": "1636", "text": "<PERSON><PERSON><PERSON><PERSON>, Maltese Baroque sculptor (baptised; d. 1667)", "html": "1636 - <a href=\"https://wikipedia.org/wiki/Melchiorre_Caf%C3%A0\" title=\"Melchiorre Cafà\"><PERSON><PERSON><PERSON><PERSON></a>, Maltese Baroque sculptor (baptised; d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Melchiorre_Caf%C3%A0\" title=\"Melchiorre Cafà\"><PERSON><PERSON><PERSON><PERSON></a>, Maltese Baroque sculptor (baptised; d. 1667)", "links": [{"title": "Mel<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Melchiorre_Caf%C3%A0"}]}, {"year": "1655", "text": "<PERSON>, Italian painter (d. 1704)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Italian painter (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Italian painter (d. 1704)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1659", "text": "<PERSON><PERSON><PERSON>, Dutch painter (d. 1722)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_van_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_van_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> van <PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1722)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>f"}]}, {"year": "1675", "text": "Duchess <PERSON><PERSON><PERSON> of Saxe-Lauenburg, Margra<PERSON>e of Baden-Baden (d. 1733)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/Duchess_<PERSON><PERSON><PERSON>_of_Saxe-Lauenburg\" class=\"mw-redirect\" title=\"Duchess <PERSON><PERSON><PERSON> of Saxe-Lauenburg\">Duchess <PERSON><PERSON><PERSON> of Saxe-Lauenburg</a>, <PERSON><PERSON><PERSON><PERSON> of <a href=\"https://wikipedia.org/wiki/Baden-Baden\" title=\"Baden-Baden\">Baden-Baden</a> (d. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Duchess_<PERSON><PERSON><PERSON>_of_Saxe-Lauenburg\" class=\"mw-redirect\" title=\"Duchess <PERSON><PERSON><PERSON> of Saxe-Lauenburg\">Duchess <PERSON><PERSON><PERSON> of Saxe-Lauenburg</a>, <PERSON><PERSON><PERSON><PERSON> of <a href=\"https://wikipedia.org/wiki/Baden-Baden\" title=\"Baden-Baden\">Baden-Baden</a> (d. 1733)", "links": [{"title": "Duchess <PERSON><PERSON><PERSON> of Saxe-Lauenburg", "link": "https://wikipedia.org/wiki/Duchess_<PERSON><PERSON><PERSON>_of_Saxe-Lauenburg"}, {"title": "Baden-Baden", "link": "https://wikipedia.org/wiki/Baden-Baden"}]}, {"year": "1714", "text": "<PERSON>, Italian anatomist (d. 1774)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian anatomist (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian anatomist (d. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON>, Spanish military officer and governor of Cuba (d. 1779)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/Antonio_Mar%C3%ADa_de_Bucareli_y_Urs%C3%BAa\" class=\"mw-redirect\" title=\"<PERSON> y Ursúa\"><PERSON> y Ursú<PERSON></a>, Spanish military officer and governor of Cuba (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antonio_Mar%C3%ADa_de_Bucareli_y_Urs%C3%BAa\" class=\"mw-redirect\" title=\"<PERSON>ucareli y Ursúa\"><PERSON> y Ursú<PERSON></a>, Spanish military officer and governor of Cuba (d. 1779)", "links": [{"title": "<PERSON>i y Ursúa", "link": "https://wikipedia.org/wiki/Antonio_Mar%C3%ADa_de_Bucareli_y_Urs%C3%BAa"}]}, {"year": "1721", "text": "<PERSON>, Scottish-English general and politician, Governor of Minorca (d. 1794)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1721)\" title=\"<PERSON> (British Army officer, born 1721)\"><PERSON></a>, Scottish-English general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Minorca\" class=\"mw-redirect\" title=\"Governor of Minorca\">Governor of Minorca</a> (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer,_born_1721)\" title=\"<PERSON> (British Army officer, born 1721)\"><PERSON></a>, Scottish-English general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Minorca\" class=\"mw-redirect\" title=\"Governor of Minorca\">Governor of Minorca</a> (d. 1794)", "links": [{"title": "<PERSON> (British Army officer, born 1721)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1721)"}, {"title": "Governor of Minorca", "link": "https://wikipedia.org/wiki/Governor_of_Minorca"}]}, {"year": "1732", "text": "<PERSON>, Duke of Württemberg, son of <PERSON>, Duke of Württemberg, and Princess <PERSON> of Thurn and Taxis (d. 1797)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON>, Duke of Württemberg\"><PERSON>, Duke of Württemberg</a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Württemberg\"><PERSON>, Duke of Württemberg</a>, and <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_Augusta_of_Thurn_and_Taxis\" class=\"mw-redirect\" title=\"Princess <PERSON> of Thurn and Taxis\">Princess <PERSON> of Thurn and Taxis</a> (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON>, Duke of Württemberg\"><PERSON>, Duke of Württemberg</a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Württemberg\"><PERSON>, Duke of Württemberg</a>, and <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_Augusta_of_Thurn_and_Taxis\" class=\"mw-redirect\" title=\"Princess <PERSON> Augusta of Thurn and Taxis\">Princess <PERSON> of Thurn and Taxis</a> (d. 1797)", "links": [{"title": "<PERSON>, Duke of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_W%C3%BCrt<PERSON><PERSON>"}, {"title": "<PERSON>, Duke of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg"}, {"title": "Princess <PERSON> of Thurn and Taxis", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Thurn_and_Taxis"}]}, {"year": "1738", "text": "<PERSON>, American general (d. 1789)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1741", "text": "<PERSON><PERSON> of Volozhin, Orthodox rabbi (d. 1821)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Volozhin\" title=\"<PERSON><PERSON> of Volozhin\"><PERSON><PERSON> of Volozhin</a>, Orthodox rabbi (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Volozhin\" title=\"<PERSON><PERSON> of Volozhin\"><PERSON><PERSON> of Volozhin</a>, Orthodox rabbi (d. 1821)", "links": [{"title": "<PERSON><PERSON> of Volozhin", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Volozhin"}]}, {"year": "1763", "text": "<PERSON><PERSON>, younger brother of French Revolutionary leader <PERSON><PERSON><PERSON> (d. 1794)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/Augustin_<PERSON>\" title=\"August<PERSON>\">August<PERSON></a>, younger brother of French Revolutionary leader <a href=\"https://wikipedia.org/wiki/Maxim<PERSON>en_Robespierre\" title=\"Maximilien Robespierre\"><PERSON><PERSON><PERSON></a> (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augustin_<PERSON>\" title=\"Augustin <PERSON>\">August<PERSON></a>, younger brother of French Revolutionary leader <a href=\"https://wikipedia.org/wiki/Maximilien_Robespierre\" title=\"Maximilien Robespierre\"><PERSON><PERSON><PERSON></a> (d. 1794)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maximilien_Robespierre"}]}, {"year": "1775", "text": "<PERSON>, Spanish opera singer and composer (d. 1832)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, Spanish opera singer and composer (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, Spanish opera singer and composer (d. 1832)", "links": [{"title": "<PERSON> (tenor)", "link": "https://wikipedia.org/wiki/Manuel_Garc%C3%ADa_(tenor)"}]}, {"year": "1784", "text": "<PERSON>, English painter (d. 1849)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, Royal Navy officer, hydrographer, astronomer and numismatist (d. 1865)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> officer, hydrographer, astronomer and numismatist (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> officer, hydrographer, astronomer and numismatist (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}]}, {"year": "1796", "text": "Princess <PERSON> of Hesse-Kassel, consort of <PERSON>, Grand Duke of Mecklenburg-Strelitz (d. 1880)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse-Kassel\" title=\"Princess <PERSON> of Hesse-Kassel\">Princess <PERSON> of Hesse-Kassel</a>, consort of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Mecklenburg-Strelitz\" title=\"<PERSON>, Grand Duke of Mecklenburg-Strelitz\"><PERSON>, Grand Duke of Mecklenburg-Strelitz</a> (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse-Kassel\" title=\"Princess <PERSON> of Hesse-Kassel\">Princess <PERSON> of Hesse-Kassel</a>, consort of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Mecklenburg-Strelitz\" title=\"<PERSON>, Grand Duke of Mecklenburg-Strelitz\"><PERSON>, Grand Duke of Mecklenburg-Strelitz</a> (d. 1880)", "links": [{"title": "Princess <PERSON> of Hesse-Kassel", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse-Kassel"}, {"title": "<PERSON>, Grand Duke of Mecklenburg-Strelitz", "link": "https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Mecklenburg-Strelitz"}]}, {"year": "1797", "text": "<PERSON>, French author and journalist (d. 1866)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French author and journalist (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French author and journalist (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry"}]}, {"year": "1800", "text": "<PERSON>, German Lutheran minister (d. 1864)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Lutheran minister (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Lutheran minister (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, Australian entrepreneur and explorer (d. 1839)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Batman\" title=\"John Batman\"><PERSON></a>, Australian entrepreneur and explorer (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Batman\" title=\"John Batman\"><PERSON></a>, Australian entrepreneur and explorer (d. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON><PERSON>, Austrian painter (d. 1871)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> von <PERSON>\"><PERSON><PERSON></a>, Austrian painter (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> von <PERSON>\"><PERSON><PERSON> <PERSON></a>, Austrian painter (d. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, 16th President of Peru (d. 1875)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3stomo_Torrico\" title=\"<PERSON>\"><PERSON></a>, 16th President of Peru (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3stomo_Torrico\" title=\"<PERSON>\"><PERSON></a>, 16th President of Peru (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%B3stomo_Torrico"}]}, {"year": "1810", "text": "<PERSON>, French general (d. 1892)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, 1st Duke of Abercorn, British statesman (d. 1885)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Abercorn\" title=\"<PERSON>, 1st Duke of Abercorn\"><PERSON>, 1st Duke of Abercorn</a>, British statesman (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Abercorn\" title=\"<PERSON>, 1st Duke of Abercorn\"><PERSON>, 1st Duke of Abercorn</a>, British statesman (d. 1885)", "links": [{"title": "<PERSON>, 1st Duke of Abercorn", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_<PERSON>_<PERSON>orn"}]}, {"year": "1813", "text": "<PERSON>, American general, explorer, and politician, 5th Territorial Governor of Arizona (d. 1890)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fr%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, explorer, and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Arizona\" class=\"mw-redirect\" title=\"List of Governors of Arizona\">Territorial Governor of Arizona</a> (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, explorer, and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Arizona\" class=\"mw-redirect\" title=\"List of Governors of Arizona\">Territorial Governor of Arizona</a> (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>._Fr%C3%A9mont"}, {"title": "List of Governors of Arizona", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Arizona"}]}, {"year": "1813", "text": "<PERSON>, Italian statesman and author (d. 1862)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian statesman and author (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian statesman and author (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, German bibliographer and historian (d. 1885)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A4sse\" title=\"<PERSON>\"><PERSON></a>, German bibliographer and historian (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A4sse\" title=\"<PERSON>\"><PERSON></a>, German bibliographer and historian (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A4sse"}]}, {"year": "1815", "text": "<PERSON>, American dentist (d. 1848)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, German ornithologist and illustrator (d. 1899)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ornithologist and illustrator (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ornithologist and illustrator (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON><PERSON><PERSON>, Belgian mechanical engineer (d. 1901)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Egide_<PERSON>alschaerts\" title=\"<PERSON>gide Walschaerts\"><PERSON><PERSON><PERSON></a>, Belgian mechanical engineer (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egi<PERSON>_<PERSON>als<PERSON>s\" title=\"Egi<PERSON> Walschaerts\"><PERSON><PERSON><PERSON></a>, Belgian mechanical engineer (d. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Egide_<PERSON>s"}]}, {"year": "1824", "text": "<PERSON><PERSON>, American general (d. 1863)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jackson\" title=\"Stone<PERSON> Jackson\"><PERSON><PERSON></a>, American general (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stone<PERSON>_Jackson\" title=\"<PERSON><PERSON> Jackson\"><PERSON><PERSON></a>, American general (d. 1863)", "links": [{"title": "<PERSON><PERSON> Jackson", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Jackson"}]}, {"year": "1827", "text": "<PERSON>, Russian mathematician and theorist (d. 1900)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and theorist (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and theorist (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON> of Sweden (d. 1907)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Oscar_II_of_Sweden\" class=\"mw-redirect\" title=\"Oscar II of Sweden\">Oscar II of Sweden</a> (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_II_of_Sweden\" class=\"mw-redirect\" title=\"Oscar II of Sweden\">Oscar II of Sweden</a> (d. 1907)", "links": [{"title": "Oscar II of Sweden", "link": "https://wikipedia.org/wiki/Oscar_II_of_Sweden"}]}, {"year": "1831", "text": "<PERSON>, English-Australian politician, 10th Premier of Victoria (d. 1889)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1839", "text": "<PERSON><PERSON>, Italian Roman Catholic nun (d. 1894)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian Roman Catholic nun (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian Roman Catholic nun (d. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, English physician and feminist (d. 1912)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and feminist (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and feminist (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1841", "text": "<PERSON><PERSON><PERSON>, French philosopher and author (d. 1929)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/%C3%89douard_<PERSON><PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and author (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>uard_<PERSON><PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and author (d. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON><PERSON>%C3%A9"}]}, {"year": "1843", "text": "<PERSON><PERSON>, French engineer (d. 1897)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Levassor\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French engineer (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Levassor\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French engineer (d. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Levassor"}]}, {"year": "1845", "text": "<PERSON>, Norwegian painter (d. 1932)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Dutch mathematician and academic (d. 1923)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician and academic (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician and academic (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, French music scholar (d. 1916)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French music scholar (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French music scholar (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, French chemist (d. 1930)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, French soldier and composer (d. 1933)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, French soldier and composer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, French soldier and composer (d. 1933)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1851", "text": "<PERSON>, Italian Roman Catholic priest (d. 1926)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Roman Catholic priest (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Roman Catholic priest (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, German classical and economic historian (d. 1929)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German classical and economic historian (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German classical and economic historian (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, Italian spiritualist (d. 1918)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Eusapia_Palladino\" title=\"Eusapia Palladino\"><PERSON>usa<PERSON></a>, Italian spiritualist (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eusa<PERSON>_Palladino\" title=\"Eusapia Palladino\"><PERSON><PERSON><PERSON></a>, Italian spiritualist (d. 1918)", "links": [{"title": "Eusa<PERSON>", "link": "https://wikipedia.org/wiki/Eusapia_Palladino"}]}, {"year": "1855", "text": "Princess <PERSON> of Bourbon-Two Sicilies, the youngest daughter of King <PERSON> of the Two Sicilies (d. 1874)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Bourbon-Two_Sicilies\" title=\"Princess <PERSON> of Bourbon-Two Sicilies\">Princess <PERSON> of Bourbon-Two Sicilies</a>, the youngest daughter of King <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_the_Two_Sicilies\" title=\"<PERSON> II of the Two Sicilies\"><PERSON> II of the Two Sicilies</a> (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Bourbon-Two_Sicilies\" title=\"Princess <PERSON> of Bourbon-Two Sicilies\">Princess <PERSON> of Bourbon-Two Sicilies</a>, the youngest daughter of King <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_the_Two_Sicilies\" title=\"<PERSON> II of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (d. 1874)", "links": [{"title": "Princess <PERSON> of Bourbon-Two Sicilies", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Bourbon-Two_Sicilies"}, {"title": "<PERSON> of the Two Sicilies", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_the_Two_Sicilies"}]}, {"year": "1860", "text": "<PERSON>, Swedish lawyer and politician, 11th Prime Minister of Sweden (d. 1915)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1864", "text": "<PERSON>, British author (d. 1926)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Israel_<PERSON>\" title=\"Israel Zangwill\"><PERSON></a>, British author (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Zangwill\" title=\"Israel Zangwill\"><PERSON></a>, British author (d. 1926)", "links": [{"title": "Israel Zangwill", "link": "https://wikipedia.org/wiki/Israel_<PERSON><PERSON>will"}]}, {"year": "1865", "text": "<PERSON>, German gynecologist and radiologist (d. 1921)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German gynecologist and radiologist (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German gynecologist and radiologist (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, German paramedic and author (d. 1921)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German paramedic and author (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German paramedic and author (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON>, Belgian-French general (d. 1965)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d\" title=\"Maxime Weygand\"><PERSON><PERSON></a>, Belgian-French general (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d\" title=\"Maxim<PERSON> Weygand\"><PERSON><PERSON></a>, Belgian-French general (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maxime_Weygand"}]}, {"year": "1868", "text": "<PERSON>, German chemist (d. 1946)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Russian mystic (d. 1916)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mystic (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mystic (d. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>sputin"}]}, {"year": "1871", "text": "<PERSON>, Russian ballerina (d. 1962)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballerina (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballerina (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Italian revolutionary syndicalist (d. 1959)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian revolutionary syndicalist (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian revolutionary syndicalist (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arturo_<PERSON>la"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON>, French mathematician (d. 1932)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, German orientalist (d. 1964)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German orientalist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German orientalist (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian director and screenwriter (d. 1948)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Negroni\"><PERSON><PERSON><PERSON><PERSON></a>, Italian director and screenwriter (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>ni\"><PERSON><PERSON><PERSON><PERSON></a>, Italian director and screenwriter (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Armenian poet and activist (d. 1948)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian poet and activist (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian poet and activist (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Belgian-American astronomer (d. 1974)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American astronomer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American astronomer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Swedish runner (d. 1959)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ernst Fast\"><PERSON></a>, Swedish runner (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ernst Fast\"><PERSON></a>, Swedish runner (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, French archaeologist, architect and historian (d. 1965)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archaeologist, architect and historian (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archaeologist, architect and historian (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>ard"}]}, {"year": "1881", "text": "<PERSON>, Yugoslav politician (d. 1968)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Yugoslav politician (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Yugoslav politician (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Russian mathematician and theologian (d. 1937)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and theologian (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and theologian (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Australian-American swimmer (d. 1972)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American swimmer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American swimmer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Norwegian poet and educator (d. 1929)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian poet and educator (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian poet and educator (d. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Bohemian writer (d. 1941)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bohemian writer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bohemian writer (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, British tug of war competitor (d. 1926)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tug of war", "link": "https://wikipedia.org/wiki/Tug_of_war"}]}, {"year": "1885", "text": "<PERSON>, British painter and designer (d. 1978)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter and designer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter and designer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Italian engineer and explorer (d. 1978)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Umberto_Nobile\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian engineer and explorer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umberto_Nobile\" title=\"<PERSON><PERSON> No<PERSON>e\"><PERSON><PERSON></a>, Italian engineer and explorer (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Umberto_No<PERSON>e"}]}, {"year": "1885", "text": "<PERSON>, English runner (d. 1932)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English runner (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English runner (d. 1932)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(athlete)"}]}, {"year": "1886", "text": "<PERSON>, American director and producer (d. 1950)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, German psychologist and phenomenologist (d. 1967)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hler\" title=\"<PERSON>\"><PERSON></a>, German psychologist and phenomenologist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hler\" title=\"<PERSON>\"><PERSON></a>, German psychologist and phenomenologist (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wolfgang_K%C3%B6hler"}]}, {"year": "1887", "text": "<PERSON>, American New Thought writer (d. 1960)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American New Thought writer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American New Thought writer (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Canadian ice hockey player (d. 1926)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9zina\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9zina\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georges_V%C3%A9zina"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, American sociologist and political activist (d. 1968)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>irim_Sorokin\" title=\"<PERSON>iri<PERSON> Sorokin\"><PERSON><PERSON><PERSON></a>, American sociologist and political activist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>iri<PERSON>_<PERSON>rok<PERSON>\" title=\"<PERSON>iri<PERSON> Sorokin\"><PERSON><PERSON><PERSON></a>, American sociologist and political activist (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pitirim_<PERSON>rokin"}]}, {"year": "1889", "text": "<PERSON>, wife and muse of <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> (d. 1971)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, wife and muse of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, wife and muse of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German Army lieutenant and lawyer (d. 1952)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Army lieutenant and lawyer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Army lieutenant and lawyer (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Portuguese marathon runner (d. 1912)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Francisco_L%C3%A1zaro\" title=\"<PERSON>\"><PERSON></a>, Portuguese marathon runner (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_L%C3%A1zaro\" title=\"Francisco L<PERSON>\"><PERSON></a>, Portuguese marathon runner (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_L%C3%A1zaro"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish fashion designer, founded <PERSON><PERSON>ciaga (d. 1972)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Crist%C3%B3bal_Balenciaga\" title=\"Cristóbal <PERSON>len<PERSON>ga\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish fashion designer, founded <a href=\"https://wikipedia.org/wiki/Balenciaga\" title=\"Balenciaga\"><PERSON><PERSON>ciaga</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crist%C3%B3bal_Balenciaga\" title=\"Cristóbal <PERSON>len<PERSON>ga\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish fashion designer, founded <a href=\"https://wikipedia.org/wiki/Balenciaga\" title=\"Balenciaga\"><PERSON><PERSON>ciaga</a> (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Crist%C3%B3bal_<PERSON><PERSON>ga"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Balenciaga"}]}, {"year": "1895", "text": "<PERSON>, French astrophysicist and astronomer (d. 1977)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astrophysicist and astronomer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astrophysicist and astronomer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Japanese anarchist, author and feminist (d. 1923)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Noe_It%C5%8D\" class=\"mw-redirect\" title=\"Noe Itō\"><PERSON><PERSON></a>, Japanese anarchist, author and feminist (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Noe_It%C5%8D\" class=\"mw-redirect\" title=\"Noe Itō\"><PERSON><PERSON></a>, Japanese anarchist, author and feminist (d. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Noe_It%C5%8D"}]}, {"year": "1896", "text": "<PERSON>, American pilot and journalist (d. 1950)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and journalist (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and journalist (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, younger sister of <PERSON> (d. 1960)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, younger sister of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, younger sister of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, American actor (d. 1973)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Finnish wrestler (d. 1968)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Ma<PERSON>_<PERSON>%C3%A4\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish wrestler (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish wrestler (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ma<PERSON>_Perttil%C3%A4"}]}, {"year": "1897", "text": "<PERSON>, French sculptor (d. 1954)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Ich%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French sculptor (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Ich%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French sculptor (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Ich%C3%A9"}]}, {"year": "1898", "text": "<PERSON>, Polish-Hungarian-American cinematographer, producer and director (d. 1964)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Polish-Hungarian-American cinematographer, producer and director (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Polish-Hungarian-American cinematographer, producer and director (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Mat%C3%A9"}]}, {"year": "1898", "text": "<PERSON>, Shah of Persia (d. 1930)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON> of Persia (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON> of Persia (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, German chemist (d. 1941)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, British general practitioner and convict (d. 1983)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British general practitioner and convict (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British general practitioner and convict (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer and manager (d. 1969)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Gyula_M%C3%A1ndi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gyula_M%C3%A1ndi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gyula_M%C3%A1ndi"}]}, {"year": "1899", "text": "<PERSON>, Russian-American pianist and composer (d. 1977)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American pianist and composer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American pianist and composer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Swedish actor and director (d. 1965)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Elof_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor and director (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elof_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor and director (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elof_<PERSON>rle"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Austrian engineer (d. 1994)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian engineer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian engineer (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sel<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Spanish Cardinal (d. 1971)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish <PERSON> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish <PERSON> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Spanish footballer and manager (d. 1978)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American film editor (d. 1974)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_editor)\" title=\"<PERSON> (film editor)\"><PERSON></a>, American film editor (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_editor)\" title=\"<PERSON> (film editor)\"><PERSON></a>, American film editor (d. 1974)", "links": [{"title": "<PERSON> (film editor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_editor)"}]}, {"year": "1903", "text": "<PERSON>, French weightlifter (d. 1945)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French weightlifter (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French weightlifter (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Canadian ice hockey player (d. 1997)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 1997)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Dutch footballer (d. 1984)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> van <PERSON>\"><PERSON><PERSON></a>, Dutch footballer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> van <PERSON>\"><PERSON><PERSON></a>, Dutch footballer (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French fashion designer, founded Christian Dior S.A. (d. 1957)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, French fashion designer, founded <a href=\"https://wikipedia.org/wiki/Christian_Dior_S.A.\" class=\"mw-redirect\" title=\"Christian Dior S.A.\">Christian <PERSON>or S.A.</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, French fashion designer, founded <a href=\"https://wikipedia.org/wiki/Christian_Dior_S.A.\" class=\"mw-redirect\" title=\"Christian Dior S.A.\">Christian Dior S.A.</a> (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Dior"}, {"title": "Christian <PERSON>or S.A.", "link": "https://wikipedia.org/wiki/Christian_Dior_S.A."}]}, {"year": "1905", "text": "<PERSON>, German-American acrobat and tightrope walker, founded The Flying Wallendas (d. 1978)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American acrobat and tightrope walker, founded <a href=\"https://wikipedia.org/wiki/The_Flying_Wallendas\" title=\"The Flying Wallendas\">The Flying Wallendas</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American acrobat and tightrope walker, founded <a href=\"https://wikipedia.org/wiki/The_Flying_Wallendas\" title=\"The Flying Wallendas\">The Flying Wallendas</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Flying Wallendas", "link": "https://wikipedia.org/wiki/The_Flying_Wallendas"}]}, {"year": "1906", "text": "<PERSON>, Russian choreographer (d. 2007)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian choreographer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian choreographer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Italian boxer (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian boxer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian boxer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Macedonian composer and conductor (d. 2004)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian composer and conductor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian composer and conductor (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Serbian footballer (d. 1970)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>jevi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teofilo_Spasojevi%C4%87"}]}, {"year": "1910", "text": "<PERSON>, German athlete (d. 1984)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German athlete (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German athlete (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American lawyer and politician, 15th Governor of Washington (d. 2011)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_of_Washington\" class=\"mw-redirect\" title=\"Governor of Washington\">Governor of Washington</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_of_Washington\" class=\"mw-redirect\" title=\"Governor of Washington\">Governor of Washington</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Washington", "link": "https://wikipedia.org/wiki/Governor_of_Washington"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Japanese footballer (d. 1975)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian shooter (d. 1976)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/K%C3%A1roly_Tak%C3%A1cs\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian shooter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A1roly_Tak%C3%A1cs\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian shooter (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A1roly_Tak%C3%A1cs"}]}, {"year": "1911", "text": "<PERSON>, Australian wrestler (d. 2003)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian wrestler (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian wrestler (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Korean footballer and manager (d. 2003)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>yung\" title=\"<PERSON>hyung\"><PERSON></a>, Korean footballer and manager (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>hyung\"><PERSON></a>, Korean footballer and manager (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>yung"}]}, {"year": "1912", "text": "<PERSON>, German-American biochemist and academic, Nobel Prize laureate (d. 2000)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1915", "text": "<PERSON>, French mathematician (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>owicz\" title=\"<PERSON>\"><PERSON></a>, French mathematician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>owicz\" title=\"<PERSON>\"><PERSON></a>, French mathematician (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON><PERSON>icz"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Italian sprinter (d. 1981)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sprinter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sprinter (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1916", "text": "<PERSON>, Italian footballer (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish midwife; American and Yiddish-language actress; producer of the Yiddish stage (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>ypora_Spaisman\" title=\"Zypora Spaisman\"><PERSON><PERSON><PERSON><PERSON></a>, Polish midwife; American and Yiddish-language actress; producer of the Yiddish stage (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ypor<PERSON>_Spaisman\" title=\"Zypora Spaisman\"><PERSON><PERSON><PERSON><PERSON></a>, Polish midwife; American and Yiddish-language actress; producer of the Yiddish stage (d. 2002)", "links": [{"title": "Zypora Spaisman", "link": "https://wikipedia.org/wiki/Zypora_Spaisman"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, H&M founder (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/H%26M\" title=\"H&amp;M\">H&amp;M</a> founder (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/H%26M\" title=\"H&amp;M\">H&amp;M</a> founder (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erlin<PERSON>_<PERSON>"}, {"title": "H&M", "link": "https://wikipedia.org/wiki/H%26M"}]}, {"year": "1918", "text": "<PERSON>, English footballer (d. 1998)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Italian cellist and conductor (d. 1989)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and conductor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and conductor (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American soldier (d. 2011)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Scottish-English captain and pilot (d. 2016)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, Scottish-English captain and pilot (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, Scottish-English captain and pilot (d. 2016)", "links": [{"title": "<PERSON> (pilot)", "link": "https://wikipedia.org/wiki/<PERSON>_(pilot)"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, first Prime Minister of Barbados (d. 1987)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, first Prime Minister of Barbados (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, first Prime Minister of Barbados (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Canadian lawyer and politician, 23rd Canadian Minister of Labour (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Alexander"}, {"title": "Minister of Labour (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Labour_(Canada)"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American actor (d. 1994)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>as\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Telly_Savalas"}]}, {"year": "1922", "text": "<PERSON>, English actor (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Croatian Marxist humanist (d. 2002)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Predra<PERSON>_<PERSON>\" title=\"<PERSON>dra<PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian Marxist humanist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Predra<PERSON>_<PERSON>\" title=\"Predra<PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian Marxist humanist (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Predra<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Argentine actor (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Spanish singer, dancer, and actress (d. 1995)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lola <PERSON>\"><PERSON></a>, Spanish singer, dancer, and actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lola Flores\"><PERSON></a>, Spanish singer, dancer, and actress (d. 1995)", "links": [{"title": "Lola <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Spanish footballer (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Pahi%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pahi%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pahi%C3%B1o"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Azerbaijani Composer, first professional female author of an opera in the East (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Azerbaijani Composer, first professional female author of an opera in the East (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Azerbaijani Composer, first professional female author of an opera in the East (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English actor, singer, and screenwriter (d. 1992)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Benny_Hill\" title=\"Benny Hill\"><PERSON></a>, English actor, singer, and screenwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Benny_Hill\" title=\"Benny Hill\"><PERSON></a>, English actor, singer, and screenwriter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor (d. 1993)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_Aidman"}]}, {"year": "1925", "text": "<PERSON>, Scottish footballer (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Austrian-English author (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>on"}]}, {"year": "1925", "text": "<PERSON>, American wrestler and manager (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, British director (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British director (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British director (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Italian composer (d. 1980)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Italian composer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Italian composer (d. 1980)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_Evangelisti_(composer)"}]}, {"year": "1926", "text": "<PERSON>, American bodybuilder and actor (d. 2000)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, French architect (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American neurosurgeon (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurosurgeon (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurosurgeon (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Australian rugby league player and coach (d. 1985)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, German footballer (d. 2003)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer (d. 2003)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Argentinian general and politician, 41st President of Argentina (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Reynal<PERSON>_<PERSON>e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian general and politician, 41st President of Argentina (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rey<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian general and politician, 41st President of Argentina (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Reynaldo_Bignone"}]}, {"year": "1928", "text": "<PERSON>, American political scientist and academic, founded the Albert Einstein Institution (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic, founded the <a href=\"https://wikipedia.org/wiki/Albert_<PERSON>_Institution\" title=\"Albert Einstein Institution\">Albert Einstein Institution</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic, founded the <a href=\"https://wikipedia.org/wiki/Albert_<PERSON>_<PERSON>\" title=\"Albert Einstein Institution\">Albert Einstein Institution</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sharp"}, {"title": "Albert Einstein Institution", "link": "https://wikipedia.org/wiki/Albert_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American filmmaker (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American filmmaker (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American filmmaker (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Zambian lawyer and politician, 1st Prime Minister of Zambia (d. 2001)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zambian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Zambia\" title=\"Prime Minister of Zambia\">Prime Minister of Zambia</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zambian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Zambia\" title=\"Prime Minister of Zambia\">Prime Minister of Zambia</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mainz<PERSON>_<PERSON>na"}, {"title": "Prime Minister of Zambia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Zambia"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Soviet cosmonaut (d. 1990)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet cosmonaut (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet cosmonaut (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Japanese actress (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English footballer (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Senegalese politician (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Habib_Thiam\" title=\"Habib Thiam\"><PERSON><PERSON><PERSON></a>, Senegalese politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Habib_Thiam\" title=\"Habib Thiam\"><PERSON><PERSON><PERSON></a>, Senegalese politician (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Thiam"}]}, {"year": "1934", "text": "<PERSON>, Irish actress", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Spanish cyclist", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Mexican footballer (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Alfonso_Portugal\" title=\"Alfonso Portugal\"><PERSON></a>, Mexican footballer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonso_Portugal\" title=\"Alfonso Portugal\"><PERSON></a>, Mexican footballer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfonso_Portugal"}]}, {"year": "1934", "text": "<PERSON>, American actress (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American basketball player (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian fencer (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Judit_%C3%81goston-Mendel%C3%A9nyi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian fencer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Judit_%C3%81goston-Mendel%C3%A9nyi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian fencer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Judit_%C3%81goston-Mendel%C3%A9nyi"}]}, {"year": "1937", "text": "<PERSON> <PERSON>, Duke in Bavaria, the youngest son of <PERSON><PERSON>, Duke of Bavaria", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_in_Bavaria\" class=\"mw-redirect\" title=\"Prince <PERSON>, Duke in Bavaria\">Prince <PERSON>, Duke in Bavaria</a>, the youngest son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duke_of_Bavaria\" title=\"<PERSON><PERSON>, Duke of Bavaria\"><PERSON><PERSON>, Duke of Bavaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_in_Bavaria\" class=\"mw-redirect\" title=\"Prince <PERSON>, Duke in Bavaria\">Prince <PERSON>, Duke in Bavaria</a>, the youngest son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duke_of_Bavaria\" title=\"<PERSON><PERSON>, Duke of Bavaria\"><PERSON><PERSON>, Duke of Bavaria</a>", "links": [{"title": "<PERSON>, Duke in Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_in_Bavaria"}, {"title": "<PERSON><PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Duke_of_Bavaria"}]}, {"year": "1938", "text": "<PERSON><PERSON>, radio personality (d. 1995)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Wolfman_Jack\" title=\"Wolfman Jack\"><PERSON><PERSON></a>, radio personality (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wolfman_Jack\" title=\"Wolfman Jack\"><PERSON><PERSON></a>, radio personality (d. 1995)", "links": [{"title": "Wolfman <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Jack"}]}, {"year": "1938", "text": "<PERSON>, Italian footballer (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, French sprinter (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sprinter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sprinter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, German footballer (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lutz\" title=\"Friedel Lutz\"><PERSON><PERSON><PERSON></a>, German footballer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lutz\" title=\"Fr<PERSON><PERSON> Lutz\"><PERSON><PERSON><PERSON></a>, German footballer (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tz"}]}, {"year": "1939", "text": "<PERSON>, American dancer and choreographer (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Russian volleyball player and coach (d. 2005)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian volleyball player and coach (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian volleyball player and coach (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American golfer and sportscaster", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English novelist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English novelist", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>, Saudi Arabian prince (d. 2013)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ud\" title=\"<PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON></a>, Saudi Arabian prince (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>aziz_<PERSON>_Saud\" title=\"<PERSON><PERSON><PERSON> bin <PERSON><PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON></a>, Saudi Arabian prince (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Spanish tenor and conductor", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Pl%C3%A1cido_<PERSON>\" title=\"Plácid<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Spanish tenor and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pl%C3%A1cido_<PERSON>\" title=\"Plácid<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Spanish tenor and conductor", "links": [{"title": "<PERSON>l<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pl%C3%A1cido_Domingo"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2013)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Haven<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Chinese-born American film producer, co-founded Orion Pictures", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-born American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Orion_Pictures\" title=\"Orion Pictures\">Orion Pictures</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-born American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Orion_Pictures\" title=\"Orion Pictures\">Orion Pictures</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Orion Pictures", "link": "https://wikipedia.org/wiki/Orion_Pictures"}]}, {"year": "1941", "text": "<PERSON>, Polish-American wrestler and bodybuilder", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American wrestler and bodybuilder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American wrestler and bodybuilder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American author and critic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, German singer, producer, and news anchor (d. 2008)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer, producer, and news anchor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer, producer, and news anchor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Prime Minister of Guinea (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prime Minister of Guinea (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prime Minister of Guinea (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_Camara"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter, guitarist, and actor (d. 2020)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, North Korean speed skater", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hwa\" title=\"Han Pil-hwa\"><PERSON></a>, North Korean speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wa\" title=\"Han Pil-hwa\"><PERSON></a>, North Korean speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>wa"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter (d. 2003)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American producer and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian water polo player (d. 2017)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian water polo player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian water polo player (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Icelandic actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Arnar_J%C3%<PERSON><PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Icelandic actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Icelandic actor", "links": [{"title": "<PERSON><PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/Arnar_J%C3%B3<PERSON><PERSON>_(actor)"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Belgian footballer (d. 2015)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Alfons Peeters\"><PERSON><PERSON><PERSON></a>, Belgian footballer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Alfons Peeters\"><PERSON><PERSON><PERSON></a>, Belgian footballer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alfons_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Italian violinist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Uto_Ughi\" title=\"Uto Ughi\"><PERSON><PERSON></a>, Italian violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uto_Ughi\" title=\"Uto Ughi\"><PERSON><PERSON></a>, Italian violinist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uto_Ughi"}]}, {"year": "1945", "text": "<PERSON>, Australian rugby league player and coach (d. 2011)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English drummer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English actor and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Swiss singer (d. 2011)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss singer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, El Salvadoran footballer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Pineda\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, El Salvadoran footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Pineda\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, El Salvadoran footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Pineda"}]}, {"year": "1946", "text": "<PERSON>, Spanish footballer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Polish former alpine skier", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(born_1947)\" title=\"<PERSON><PERSON><PERSON> (born 1947)\"><PERSON><PERSON><PERSON></a>, Polish former alpine skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(born_1947)\" title=\"<PERSON><PERSON><PERSON> (born 1947)\"><PERSON><PERSON><PERSON></a>, Polish former alpine skier", "links": [{"title": "<PERSON><PERSON><PERSON> (born 1947)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(born_1947)"}]}, {"year": "1947", "text": "<PERSON>, American actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American mathematician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Scottish singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, French singer-songwriter and actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American clinical psychologist (d. 2017)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clinical psychologist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clinical psychologist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Italian footballer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Argentine footballer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish footballer (d. 2016)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_(footballer)"}]}, {"year": "1948", "text": "<PERSON>, Argentine footballer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American basketball coach and player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball coach and player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball coach and player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Vietnamese politician and 7th President of Vietnam", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Tr%C6%B0%C6%A1ng_T%E1%BA%A5n_Sang\" title=\"Trương Tấn Sang\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese politician and 7th <a href=\"https://wikipedia.org/wiki/President_of_Vietnam\" title=\"President of Vietnam\">President of Vietnam</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tr%C6%B0%C6%A1ng_T%E1%BA%A5n_Sang\" title=\"Trương Tấn Sang\">T<PERSON><PERSON><PERSON><PERSON></a>, Vietnamese politician and 7th <a href=\"https://wikipedia.org/wiki/President_of_Vietnam\" title=\"President of Vietnam\">President of Vietnam</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%C6%B0%C6%A1ng_T%E1%BA%A5n_Sang"}, {"title": "President of Vietnam", "link": "https://wikipedia.org/wiki/President_of_Vietnam"}]}, {"year": "1950", "text": "<PERSON>, German javelin thrower", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German javelin thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American politician and diplomat, 36th United States Secretary of Commerce", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 36th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 36th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}]}, {"year": "1950", "text": "<PERSON>, Spanish racewalker", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADn_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON> (racewalker)\"><PERSON></a>, Spanish racewalker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADn_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON> (racewalker)\"><PERSON></a>, Spanish racewalker", "links": [{"title": "<PERSON> (racewalker)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADn_(racewalker)"}]}, {"year": "1950", "text": "<PERSON>, Trinidadian-English singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Billy Ocean\"><PERSON></a>, Trinidadian-English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Billy Ocean\"><PERSON></a>, Trinidadian-English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Billy_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Dutch politician and diplomat, Dutch Minister for Development Cooperation", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_for_Development_Cooperation_(Netherlands)\" class=\"mw-redirect\" title=\"Minister for Development Cooperation (Netherlands)\">Dutch Minister for Development Cooperation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_for_Development_Cooperation_(Netherlands)\" class=\"mw-redirect\" title=\"Minister for Development Cooperation (Netherlands)\">Dutch Minister for Development Cooperation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister for Development Cooperation (Netherlands)", "link": "https://wikipedia.org/wiki/Minister_for_Development_Cooperation_(Netherlands)"}]}, {"year": "1951", "text": "<PERSON>, American lawyer, judge, and politician, 82nd United States Attorney General", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 82nd <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 82nd <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1952", "text": "<PERSON>, Swiss activist and murderer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss activist and murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss activist and murderer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Austrian alpine skier", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian alpine skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian alpine skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Russian chess player (d. 2010)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American businessman and philanthropist, co-founded Microsoft (d. 2018)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Microsoft", "link": "https://wikipedia.org/wiki/Microsoft"}]}, {"year": "1953", "text": "<PERSON>, Spanish cyclist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Y%C3%A1%C3%B1<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Y%C3%A1%C3%B1<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/Felipe_Y%C3%A1%C3%B1ez_(cyclist)"}]}, {"year": "1954", "text": "<PERSON>, German politician of the Christian Democratic Union", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, German politician of the Christian Democratic Union", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, German politician of the Christian Democratic Union", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Burkinabé director, producer, and screenwriter (d. 2018)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Idr<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Idr<PERSON>\"><PERSON><PERSON><PERSON></a>, Burkinabé director, producer, and screenwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>dr<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Idr<PERSON> O<PERSON>ogo\"><PERSON><PERSON><PERSON></a>, Burkinabé director, producer, and screenwriter (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Idr<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English footballer and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American tennis player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1955", "text": "<PERSON>, American painter and sculptor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Italian politician and President of Sicily", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian politician and President of Sicily", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian politician and President of Sicily", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American actor and director", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American actress and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Iraqi footballer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Ukrainian politician (d. 2015)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Ukrainian politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Ukrainian politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Canadian actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Russian pistol shooter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pistol shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pistol shooter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Scottish footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American basketball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American hard rock and heavy metal drummer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hard rock and heavy metal drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hard rock and heavy metal drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Austrian alpine skier", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Cornelia_Pr%C3%B6ll\" title=\"Cornelia Pröll\">Cornelia Pröll</a>, Austrian alpine skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornelia_Pr%C3%B6ll\" title=\"Cornelia Pröll\">Cornelia Pröll</a>, Austrian alpine skier", "links": [{"title": "Cornelia Pröll", "link": "https://wikipedia.org/wiki/Cornelia_Pr%C3%B6ll"}]}, {"year": "1961", "text": "<PERSON><PERSON> Croatian journalist (d. 2008)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Croatian journalist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Croatian journalist (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ivo_Pukani%C4%87"}]}, {"year": "1961", "text": "<PERSON>, English footballer (d. 2024)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1961)\" title=\"<PERSON> (footballer, born 1961)\"><PERSON></a>, English footballer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1961)\" title=\"<PERSON> (footballer, born 1961)\"><PERSON></a>, English footballer (d. 2024)", "links": [{"title": "<PERSON> (footballer, born 1961)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1961)"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Russian cyclist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American economist and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, French actress, director and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Italian footballer and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Slovenian politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>haler\"><PERSON><PERSON></a>, Slovenian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Thaler\"><PERSON><PERSON></a>, Slovenian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, French actress (d. 2003)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Dutch theoretical physicist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theoretical physicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theoretical physicist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Nigerian-American basketball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian-American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, German basketball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>le<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>le<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>le<PERSON>_<PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German ski jumper", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ski_jumper)\" title=\"<PERSON> (ski jumper)\"><PERSON></a>, German ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ski_jumper)\" title=\"<PERSON> (ski jumper)\"><PERSON></a>, German ski jumper", "links": [{"title": "<PERSON> (ski jumper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ski_jumper)"}]}, {"year": "1964", "text": "<PERSON>, English musician and actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rald_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9ral<PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9ral<PERSON>_<PERSON>i"}]}, {"year": "1964", "text": "<PERSON>, Spanish footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian water polo player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Aleksandar_%C5%A0o%C5%A1tar\" title=\"Aleksandar <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian water polo player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksandar_%C5%A0o%C5%A1tar\" title=\"<PERSON>eksan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian water polo player", "links": [{"title": "Aleksan<PERSON>", "link": "https://wikipedia.org/wiki/Aleksandar_%C5%A0o%C5%A1tar"}]}, {"year": "1964", "text": "<PERSON>, English footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1965", "text": "<PERSON>, British artist, musician and singer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British artist, musician and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British artist, musician and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON> <PERSON>, American DJ, rapper, and producer (d. 2002)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Jam_Master_Jay\" title=\"Jam Master Jay\">Jam Master <PERSON></a>, American DJ, rapper, and producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jam_Master_Jay\" title=\"Jam Master Jay\"><PERSON> Master <PERSON></a>, American DJ, rapper, and producer (d. 2002)", "links": [{"title": "Jam Master <PERSON>", "link": "https://wikipedia.org/wiki/Jam_Master_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ada"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ulf_Dahl%C3%A9n"}]}, {"year": "1967", "text": "<PERSON>, Slovenian footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1\" title=\"<PERSON>\"><PERSON></a>, Slovenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1\" title=\"<PERSON>\"><PERSON></a>, Slovenian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_Jermani%C5%A1"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Armenian chess player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Artash<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artashes_<PERSON>ian\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artashes_Minasian"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Japanese film director and landscaper", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Gor%C5%8D_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese film director and landscaper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gor%C5%8D_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese film director and landscaper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gor%C5%8D_<PERSON><PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Russian ice skater", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Artur Dmitriev\"><PERSON><PERSON></a>, Russian ice skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ur D<PERSON>v\"><PERSON><PERSON></a>, Russian ice skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Russian volleyball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian volleyball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, French director", "html": "1968 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French director", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian politician, 46th Premier of South Australia", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 46th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 46th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1968", "text": "<PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Israeli chess Grandmaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli chess Grandmaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli chess Grandmaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Finnish-Belarusian decathlete", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4m%C3%A4l%C3%A4inen\" title=\"<PERSON>\"><PERSON></a>, Finnish-Belarusian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4m%C3%A4l%C3%A4inen\" title=\"<PERSON>\"><PERSON></a>, Finnish-Belarusian decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eduard_H%C3%A4m%C3%A4l%C3%A4inen"}]}, {"year": "1969", "text": "<PERSON><PERSON>, French-American actress and singer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese comic artist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tsu<PERSON><PERSON> Nekoi\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese comic artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese comic artist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON><PERSON>oi"}]}, {"year": "1970", "text": "<PERSON><PERSON>, former Croatian footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Alen_Bok%C5%A1i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, former Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alen_Bok%C5%A1i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, former Croatian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alen_Bok%C5%A1i%C4%87"}]}, {"year": "1970", "text": "<PERSON>, French actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Marina_Fo%C3%AFs\" title=\"<PERSON>\"><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marina_Fo%C3%AFs\" title=\"<PERSON>\"><PERSON></a>, French actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Fo%C3%AFs"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Israeli-American director, producer and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-American director, producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-American director, producer and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Faroese footballer and entertainer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Uni_Arge\" title=\"Uni Arge\"><PERSON><PERSON></a>, Faroese footballer and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uni_Arge\" title=\"Uni Arge\"><PERSON><PERSON></a>, Faroese footballer and entertainer", "links": [{"title": "Uni Arge", "link": "https://wikipedia.org/wiki/Uni_Arge"}]}, {"year": "1971", "text": "<PERSON>, Spanish footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American basketball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Russian footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Russian speed skater", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American ice hockey player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Doug Weight\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Doug Weight\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Algerian footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Swedish businessman and politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Bosnian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Sead_<PERSON>vi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sea<PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sead_Kapetanovi%C4%87"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Japanese composer and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer, musician and actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Cat_Power\" title=\"Cat Power\"><PERSON></a>, American singer, musician and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cat_Power\" title=\"Cat Power\"><PERSON></a>, American singer, musician and actress", "links": [{"title": "Cat Power", "link": "https://wikipedia.org/wiki/Cat_Power"}]}, {"year": "1972", "text": "<PERSON>, American curler", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Italian cross-country skier", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Sa<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Valbus<PERSON>\"><PERSON><PERSON></a>, Italian cross-country skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sa<PERSON>_<PERSON>\" title=\"Sabina Valbusa\"><PERSON><PERSON></a>, Italian cross-country skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>busa"}]}, {"year": "1973", "text": "<PERSON>, English cyclist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American musician and DJ", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American musician and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American musician and DJ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Lithuanian modern pentathlete", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian modern pentathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian modern pentathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ed<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Peruvian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ri\" title=\"<PERSON>lav<PERSON> Mae<PERSON>ri\"><PERSON><PERSON><PERSON></a>, Peruvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ri\" title=\"<PERSON>lav<PERSON>ri\"><PERSON><PERSON><PERSON></a>, Peruvian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Flavio_Maestri"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Spanish actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Malena_Alterio"}]}, {"year": "1974", "text": "<PERSON>, American animator, screenwriter and voice actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Atoms\"><PERSON></a>, American animator, screenwriter and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Atoms\"><PERSON></a>, American animator, screenwriter and voice actor", "links": [{"title": "<PERSON> Atoms", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, German-Finnish Internet entrepreneur and political activist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Finnish Internet entrepreneur and political activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Finnish Internet entrepreneur and political activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Burundian middle-distance runner", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Arth%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Burundian middle-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arth%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Burundian middle-distance runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arth%C3%A9mon_<PERSON>a"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Italian cyclist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist,_born_1974)\" title=\"<PERSON> (cyclist, born 1974)\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist,_born_1974)\" title=\"<PERSON> (cyclist, born 1974)\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON> (cyclist, born 1974)", "link": "https://wikipedia.org/wiki/<PERSON>_(cyclist,_born_1974)"}]}, {"year": "1975", "text": "<PERSON>, English footballer and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, French rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8de\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8de\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_Castaign%C3%A8de"}]}, {"year": "1975", "text": "<PERSON>, American speedskater", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ol<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speedskater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ol<PERSON>\" title=\"<PERSON>ol<PERSON>\"><PERSON></a>, American speedskater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_FitzRandolph"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Japanese race car driver", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ji_<PERSON>de"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Spanish footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1975)\" title=\"<PERSON><PERSON> (footballer, born 1975)\"><PERSON><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/It<PERSON>_(footballer,_born_1975)\" title=\"<PERSON><PERSON> (footballer, born 1975)\"><PERSON><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON><PERSON> (footballer, born 1975)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1975)"}]}, {"year": "1975", "text": "<PERSON>, Dutch footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American jazz pianist, composer and educator", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American jazz pianist, composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American jazz pianist, composer and educator", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Romanian director", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Florin_%C8%98erban\" title=\"<PERSON>lor<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florin_%C8%98erban\" title=\"<PERSON>lor<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Florin_%C8%98erban"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Belarusian footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belarusian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belarusian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Lithuanian-Ukrainian banker and politician; 15th Ukrainian Minister of Economic Development", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-Ukrainian banker and politician; 15th <a href=\"https://wikipedia.org/wiki/Ministry_of_Economic_Development_and_Trade_(Ukraine)\" class=\"mw-redirect\" title=\"Ministry of Economic Development and Trade (Ukraine)\">Ukrainian Minister of Economic Development</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-Ukrainian banker and politician; 15th <a href=\"https://wikipedia.org/wiki/Ministry_of_Economic_Development_and_Trade_(Ukraine)\" class=\"mw-redirect\" title=\"Ministry of Economic Development and Trade (Ukraine)\">Ukrainian Minister of Economic Development</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aivaras_Abromavi%C4%8Dius"}, {"title": "Ministry of Economic Development and Trade (Ukraine)", "link": "https://wikipedia.org/wiki/Ministry_of_Economic_Development_and_Trade_(Ukraine)"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Latvian cyclist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Rai<PERSON>_Belohvo%C5%A1%C4%8Diks\" title=\"<PERSON><PERSON> Belohvoščiks\"><PERSON><PERSON></a>, Latvian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rai<PERSON>_Belohvo%C5%A1%C4%8Diks\" title=\"<PERSON>vis Belohvoščiks\"><PERSON><PERSON></a>, Latvian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Raivis_Belohvo%C5%A1%C4%8Diks"}]}, {"year": "1976", "text": "<PERSON>, English singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, German actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Italian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Latvian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1977", "text": "<PERSON>, Saudi Arabian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, South African footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, German hammer thrower", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German hammer thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Italian cyclist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, German runner", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor, director, and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, French sprinter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Lo%C3%AFc_Lerouge\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lo%C3%AFc_Lerouge\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lo%C3%AFc_Lerouge"}]}, {"year": "1978", "text": "<PERSON><PERSON>, German triathlete", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German triathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Al-<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_<PERSON>_L%C3%B3pez\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_<PERSON>_L%C3%B3pez\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hern%C3%A1n_Rodrigo_L%C3%B3pez"}]}, {"year": "1978", "text": "<PERSON>, Swiss cross-country skier", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cross-country skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cross-country skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Russian ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Namibian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Namibian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Namibian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Spanish singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>endi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Melendi"}]}, {"year": "1979", "text": "<PERSON>, Irish rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Driscoll\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27Driscoll\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dris<PERSON>ll"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Australian rower", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>-won, South Korean badminton player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-won\" title=\"<PERSON>-won\"><PERSON>-won</a>, South Korean badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-won\" title=\"<PERSON>-won\"><PERSON>-won</a>, South Korean badminton player", "links": [{"title": "<PERSON>won", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-won"}]}, {"year": "1980", "text": "<PERSON>, Japanese singer-songwriter and voice actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer-songwriter and voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer-songwriter and voice actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Spanish rally diver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish rally diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish rally diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Xavier_Pons"}]}, {"year": "1981", "text": "<PERSON>, Hong Kong singer-songwriter and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Chinese fencer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese fencer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wu_<PERSON>ong"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South Korean singer and actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Korean_singer)\" title=\"<PERSON> (South Korean singer)\"><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Korean_singer)\" title=\"<PERSON> (South Korean singer)\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON> (South Korean singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Korean_singer)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Polish actress, dancer, and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish actress, dancer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish actress, dancer, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South Korean actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-won\" title=\"<PERSON>yeo-won\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-won\" title=\"<PERSON> Ryeo-won\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Swedish filmmaker", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Brazilian singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Michel_Tel%C3%B3"}]}, {"year": "1982", "text": "<PERSON>, Venezuelan footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Venezuelan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Venezuelan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, French tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, French boxer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, German footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American-Italian basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Russian actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Israeli physician, author and molecular biologist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli physician, author and molecular biologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli physician, author and molecular biologist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Canadian-American wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Spanish golfer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>uir%C3%B3s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>uir%C3%B3s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>uir%C3%B3s"}]}, {"year": "1983", "text": "<PERSON>, Italian swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Dutch water polo player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch water polo player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch water polo player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian alpine skier", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian alpine skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian alpine skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Volz\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>z\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z"}]}, {"year": "1984", "text": "<PERSON>, American actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ng<PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ot<PERSON>_<PERSON>ata"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Russian boxer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Artur_Beterbiev\" title=\"Artur Beterbiev\"><PERSON><PERSON></a>, Russian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artur_<PERSON>biev\" title=\"Artur Beterbiev\"><PERSON><PERSON></a>, Russian boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Danish singer and songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ra Dion<PERSON>\"><PERSON><PERSON></a>, Danish singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ra <PERSON>\"><PERSON><PERSON></a>, Danish singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ra_<PERSON>e"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese voice actress and singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Russian model and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, North Korean artistic gymnast", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ri_<PERSON>-gwang\" title=\"Ri Se-gwang\"><PERSON><PERSON> <PERSON><PERSON></a>, North Korean artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>i_<PERSON>-gwang\" title=\"Ri Se-gwang\"><PERSON><PERSON> <PERSON><PERSON>wang</a>, North Korean artistic gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>wang"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Russian basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, Russian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, Russian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)"}]}, {"year": "1985", "text": "<PERSON>, American ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Arzo"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Brazilian mixed martial artist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bar<PERSON>\" title=\"<PERSON><PERSON> Barboza\"><PERSON><PERSON></a>, Brazilian mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bar<PERSON>\" title=\"<PERSON><PERSON> Barboza\"><PERSON><PERSON></a>, Brazilian mixed martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edson_Barboza"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Brazilian swimmer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Gomes_J%C3%BAnior\" title=\"<PERSON>\"><PERSON></a>, Brazilian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Gomes_J%C3%BAnior\" title=\"<PERSON>\"><PERSON></a>, Brazilian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Gomes_J%C3%BAnior"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Javi_L%C3%B<PERSON><PERSON><PERSON>_(footballer,_born_1986)\" title=\"<PERSON><PERSON> (footballer, born 1986)\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON>_L%C3%B3<PERSON><PERSON>_(footballer,_born_1986)\" title=\"<PERSON><PERSON> (footballer, born 1986)\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1986)", "link": "https://wikipedia.org/wiki/Javi_L%C3%B3<PERSON><PERSON>_(footballer,_born_1986)"}]}, {"year": "1986", "text": "<PERSON>, Dominican Republic volleyball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA\" title=\"<PERSON>\"><PERSON></a>, Dominican Republic volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA\" title=\"<PERSON>\"><PERSON></a>, Dominican Republic volleyball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>mbr%C3%BA"}]}, {"year": "1986", "text": "<PERSON>, American ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Quick\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Indian actor (d. 2020)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Peruvian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_V%C3%ADlchez\" title=\"Óscar Vílchez\"><PERSON><PERSON><PERSON></a>, Peruvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_V%C3%ADlchez\" title=\"Óscar Vílchez\"><PERSON><PERSON><PERSON></a>, Peruvian footballer", "links": [{"title": "Ó<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_V%C3%ADlchez"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Latvian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Oskars_B%C4%81rtulis\" title=\"<PERSON><PERSON><PERSON> B<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oskars_B%C4%81rtulis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oskars_B%C4%81rtulis"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Swedish politician", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, South African rower", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Kenyan runner", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ge"}]}, {"year": "1987", "text": "<PERSON>, Canadian soccer player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, German cyclist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Slovenian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ma%C5%A1a_Zec_Pe%C5%A1kiri%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma%C5%A1a_Zec_Pe%C5%A1kiri%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ma%C5%A1a_Zec_Pe%C5%A1kiri%C4%8D"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Japanese actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Filipino actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American decathlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Ashton_Eaton\" title=\"Ashton Eaton\"><PERSON></a>, American decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashton_Eaton\" title=\"Ashton Eaton\"><PERSON></a>, American decathlete", "links": [{"title": "Ashton Eaton", "link": "https://wikipedia.org/wiki/<PERSON>_Eaton"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Latvian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fr<PERSON>\"><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Italian-American model and actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Macedonian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Aleksandar_Lazevski\" title=\"Aleksandar <PERSON>zevski\">Aleksan<PERSON></a>, Macedonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksandar_Lazevski\" title=\"Aleksandar <PERSON>zevski\">Aleksan<PERSON></a>, Macedonian footballer", "links": [{"title": "Aleksandar <PERSON>", "link": "https://wikipedia.org/wiki/Aleksandar_<PERSON>zevski"}]}, {"year": "1988", "text": "<PERSON>, Ecuadorian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/%C3%81ngel_Mena\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81ngel_Mena\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%81ngel_Mena"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Canadian tennis player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Val%C3%A9rie_T%C3%A9treault\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Val%C3%A9rie_T%C3%A9treault\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Val%C3%A9rie_T%C3%A9treault"}]}, {"year": "1988", "text": "<PERSON>, Belgian swimmer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Serbian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Nemanja_Tomi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nemanja_Tomi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nemanja_Tomi%C4%87"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Do%C4%9Fu%C5%9F_Ba<PERSON>bay\" title=\"Doğuş Balbay\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Do%C4%9Fu%C5%9F_Ba<PERSON><PERSON>\" title=\"Doğ<PERSON>ş Balbay\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "Doğuş Balbay", "link": "https://wikipedia.org/wiki/Do%C4%9Fu%C5%9F_<PERSON><PERSON>bay"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American indoor volleyball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American indoor volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American indoor volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Congolese footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/F%C3%A9r%C3%A9bory_Dor%C3%A9\" title=\"Férébory Doré\"><PERSON>érébor<PERSON></a>, Congolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9r%C3%A9bory_Dor%C3%A9\" title=\"Férébory Doré\"><PERSON><PERSON>ré<PERSON><PERSON></a>, Congolese footballer", "links": [{"title": "Féré<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9r%C3%A9bory_Dor%C3%A9"}]}, {"year": "1989", "text": "<PERSON>, Russian swimmer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Armenian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Chinese tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" class=\"mw-redirect\" title=\"<PERSON> (tennis)\"><PERSON></a>, Chinese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" class=\"mw-redirect\" title=\"<PERSON> (tennis)\"><PERSON></a>, Chinese tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1990", "text": "<PERSON>, Zimbabwean footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Knowledge_Musona\" title=\"Knowledge Musona\"><PERSON></a>, Zimbabwean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Knowledge_Musona\" title=\"Knowledge Musona\"><PERSON></a>, Zimbabwean footballer", "links": [{"title": "Knowledge Musona", "link": "https://wikipedia.org/wiki/Knowledge_Musona"}]}, {"year": "1990", "text": "<PERSON>, American model and actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Omani footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Al<PERSON>Bus<PERSON>\"><PERSON></a>, Omani footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Omani footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Czech cyclist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rt"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Brayden_McNabb\" title=\"Bray<PERSON> McNabb\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brayden_McNabb\" title=\"Bray<PERSON> McNabb\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bray<PERSON>_<PERSON>b"}]}, {"year": "1991", "text": "<PERSON>, Italian gymnast", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Norwegian cyclist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>str%C3%B8m\" title=\"<PERSON>\"><PERSON></a>, Norwegian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>str%C3%B8m\" title=\"<PERSON>\"><PERSON></a>, Norwegian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bystr%C3%B8m"}]}, {"year": "1992", "text": "<PERSON>, Australian tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ton_Howden\" title=\"Quinton Howden\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>den\" title=\"<PERSON><PERSON>ton Howden\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Howden"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Ghanaian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wa<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, German ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BChnhackl\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_K%C3%BChnhack<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tom_K%C3%BChnhackl"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Mezquida\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Mezquida\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%A1s_Mezquida"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Paraguayan tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Ver%C3%B3nica_Cepede_<PERSON>\" title=\"Verónica Cepede <PERSON>\"><PERSON>er<PERSON><PERSON></a>, Paraguayan tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ver%C3%B3nica_Cepede_<PERSON>\" title=\"Verónica Cepede <PERSON>\">V<PERSON><PERSON><PERSON></a>, Paraguayan tennis player", "links": [{"title": "<PERSON>erón<PERSON>", "link": "https://wikipedia.org/wiki/Ver%C3%B3nica_Cepede_Royg"}]}, {"year": "1992", "text": "<PERSON>, Hungarian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ha"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Swedish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Amin_<PERSON>\" title=\"Amin <PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amin_<PERSON>\" title=\"<PERSON>in <PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Am<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian-English tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" class=\"mw-redirect\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian-English tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" class=\"mw-redirect\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian-English tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, French basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Johann%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marine_Johann%C3%A8s"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Australian soccer player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian soccer player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_C%C3%B4ng_Ph%C6%B0%E1%BB%A3ng\" title=\"<PERSON>uy<PERSON><PERSON>ư<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_C%C3%B4ng_Ph%C6%B0%E1%BB%A3ng\" title=\"<PERSON>uyễ<PERSON> Phượ<PERSON>\"><PERSON><PERSON><PERSON><PERSON>ư<PERSON></a>, Vietnamese footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_C%C3%B4ng_Ph%C6%B0%E1%BB%A3ng"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Russian cross-country skier", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>upak\" title=\"<PERSON><PERSON><PERSON> Stupak\"><PERSON><PERSON><PERSON></a>, Russian cross-country skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Stupak\"><PERSON><PERSON><PERSON></a>, Russian cross-country skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Spanish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Cristian_Pav%C3%B3n\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C<PERSON>ian_Pav%C3%B3n\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cristian_Pav%C3%B3n"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Guinean basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guinean basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guinean basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actor, musician and singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, musician and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, musician and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, German-Georgian mixed martial artist", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Georgian mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Georgian mixed martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ilia_Topuria"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Ecuadorian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Pervis_Estupi%C3%B1%C3%A1n\" title=\"Pervis <PERSON>upiñán\"><PERSON><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pervis_Estupi%C3%B1%C3%A1n\" title=\"Pervis Estupiñán\"><PERSON><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pervis_Estupi%C3%B1%C3%A1n"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Indian actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Japanese actor", "html": "1999 - <a href=\"https://wikipedia.org/wiki/F%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C5%<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, South Korean footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-bum\" title=\"<PERSON><PERSON>-bum\"><PERSON><PERSON>b<PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-bum\" title=\"<PERSON><PERSON>-bum\"><PERSON><PERSON>b<PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON><PERSON>bum", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-bum"}]}, {"year": "2003", "text": "<PERSON>, Tunisian footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>jbri"}]}, {"year": "2004", "text": "Princess <PERSON> of Norway", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Norway\" title=\"Princess <PERSON> of Norway\">Princess <PERSON> of Norway</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Norway\" title=\"Princess <PERSON> of Norway\">Princess <PERSON> of Norway</a>", "links": [{"title": "Princess <PERSON> of Norway", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Norway"}]}, {"year": "2005", "text": "IShowSpeed, American YouTuber", "html": "2005 - <a href=\"https://wikipedia.org/wiki/IShowSpeed\" title=\"IShowSpeed\">IShowSpeed</a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/IShowSpeed\" title=\"IShowSpeed\">IShowSpeed</a>, American YouTuber", "links": [{"title": "IShowSpeed", "link": "https://wikipedia.org/wiki/IShowSpeed"}]}, {"year": "2007", "text": "<PERSON>, English darts player", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English darts player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English darts player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "420", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, king of the Sassanid Empire", "html": "420 - <a href=\"https://wikipedia.org/wiki/Yazdegerd_I\" title=\"Yazdegerd I\"><PERSON><PERSON><PERSON><PERSON><PERSON> I</a>, king of the <a href=\"https://wikipedia.org/wiki/Sassanid_Empire\" class=\"mw-redirect\" title=\"Sassanid Empire\">Sassanid Empire</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yazdegerd_I\" title=\"Yazdegerd I\"><PERSON><PERSON><PERSON><PERSON><PERSON> I</a>, king of the <a href=\"https://wikipedia.org/wiki/Sassanid_Empire\" class=\"mw-redirect\" title=\"Sassanid Empire\">Sassanid Empire</a>", "links": [{"title": "Yazdegerd I", "link": "https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON><PERSON>_I"}, {"title": "Sassanid Empire", "link": "https://wikipedia.org/wiki/Sassanid_Empire"}]}, {"year": "496", "text": "<PERSON><PERSON><PERSON><PERSON> of Pavia, Italian bishop and saint (b. 438)", "html": "496 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_of_Pavia\" title=\"<PERSON><PERSON><PERSON><PERSON> of Pavia\"><PERSON><PERSON><PERSON><PERSON> of Pavia</a>, Italian bishop and saint (b. 438)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Pavia\" title=\"<PERSON><PERSON><PERSON><PERSON> of Pavia\"><PERSON><PERSON><PERSON><PERSON> of Pavia</a>, Italian bishop and saint (b. 438)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Pavia", "link": "https://wikipedia.org/wiki/Epiphanius_of_Pavia"}]}, {"year": "917", "text": "<PERSON><PERSON><PERSON>, Duke of Swabia (b. 880)", "html": "917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Swabia\" title=\"<PERSON><PERSON><PERSON>, Duke of Swabia\"><PERSON><PERSON><PERSON>, Duke of Swabia</a> (b. 880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Swabia\" title=\"<PERSON><PERSON><PERSON>, Duke of Swabia\"><PERSON><PERSON><PERSON>, Duke of Swabia</a> (b. 880)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Swabia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Swabia"}]}, {"year": "918", "text": "<PERSON>, Chinese general", "html": "918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Later_Liang)\" title=\"<PERSON> (Later Liang)\"><PERSON></a>, Chinese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Later_Liang)\" title=\"<PERSON> (Later Liang)\"><PERSON></a>, Chinese general", "links": [{"title": "<PERSON> (Later Liang)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Later_<PERSON>)"}]}, {"year": "939", "text": "<PERSON>, Chinese emperor (b. 900)", "html": "939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor (b. 900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor (b. 900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "942", "text": "<PERSON>, Chinese general (Five Dynasties)", "html": "942 - <a href=\"https://wikipedia.org/wiki/An_Chongrong\" title=\"An Chongrong\"><PERSON></a>, Chinese general (<a href=\"https://wikipedia.org/wiki/Five_Dynasties\" class=\"mw-redirect\" title=\"Five Dynasties\">Five Dynasties</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An_Chongrong\" title=\"An Chongrong\"><PERSON></a>, Chinese general (<a href=\"https://wikipedia.org/wiki/Five_Dynasties\" class=\"mw-redirect\" title=\"Five Dynasties\">Five Dynasties</a>)", "links": [{"title": "An Chongrong", "link": "https://wikipedia.org/wiki/An_Cho<PERSON>rong"}, {"title": "Five Dynasties", "link": "https://wikipedia.org/wiki/Five_Dynasties"}]}, {"year": "945", "text": "<PERSON>, Chinese general and governor", "html": "945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and governor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and governor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Yang_<PERSON>"}]}, {"year": "1118", "text": "<PERSON> (b. 1050)", "html": "1118 - <a href=\"https://wikipedia.org/wiki/Pope_Paschal_II\" title=\"Pope Paschal II\">Pope Paschal II</a> (b. 1050)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Paschal_II\" title=\"Pope Paschal II\"><PERSON>schal II</a> (b. 1050)", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_II"}]}, {"year": "1203", "text": "<PERSON>, Abbess of Quedlinburg (b. 1139)", "html": "1203 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg\" title=\"<PERSON>, Abbess of Quedlinburg\"><PERSON>, Abbess of Quedlinburg</a> (b. 1139)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg\" title=\"<PERSON>, Abbess of Quedlinburg\"><PERSON>, Abbess of Quedlinburg</a> (b. 1139)", "links": [{"title": "<PERSON>, Abbess of Quedlinburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg"}]}, {"year": "1320", "text": "<PERSON><PERSON><PERSON>, Icelandic bishop (b. c. 1260)", "html": "1320 - <a href=\"https://wikipedia.org/wiki/%C3%81rn<PERSON>_<PERSON><PERSON><PERSON><PERSON>_(bishop)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (bishop)\"><PERSON><PERSON><PERSON></a>, Icelandic bishop (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1260</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(bishop)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (bishop)\"><PERSON><PERSON><PERSON></a>, Icelandic bishop (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1260</span>)", "links": [{"title": "<PERSON><PERSON><PERSON> (bishop)", "link": "https://wikipedia.org/wiki/%C3%81rn<PERSON>_<PERSON><PERSON><PERSON><PERSON>_(bishop)"}]}, {"year": "1527", "text": "<PERSON>, Spanish explorer (b. 1489)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish explorer (b. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish explorer (b. 1489)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1546", "text": "<PERSON><PERSON>, Japanese daimyō (b. 1491)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON>zai_Sukemasa\" title=\"Azai Sukemasa\"><PERSON><PERSON></a>, Japanese daimyō (b. 1491)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>zai_<PERSON>\" title=\"Azai Sukemasa\"><PERSON><PERSON></a>, Japanese daimyō (b. 1491)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1609", "text": "<PERSON>, French historian and scholar (b. 1540)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and scholar (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and scholar (b. 1540)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1638", "text": "<PERSON><PERSON><PERSON>, Italian composer (b. 1570)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (b. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (b. 1570)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1670", "text": "<PERSON>, French highwayman (b. 1643)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Highwayman\" title=\"Highwayman\">highwayman</a> (b. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Highwayman\" title=\"Highwayman\">highwayman</a> (b. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Highwayman", "link": "https://wikipedia.org/wiki/<PERSON>man"}]}, {"year": "1683", "text": "<PERSON>, 1st Earl of Shaftesbury, English politician, Chancellor of the Exchequer (b. 1621)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Shaftesbury\" title=\"<PERSON>, 1st Earl of Shaftesbury\"><PERSON>, 1st Earl of Shaftesbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Shaftesbury\" title=\"<PERSON>, 1st Earl of Shaftesbury\"><PERSON>, 1st Earl of Shaftesbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1621)", "links": [{"title": "<PERSON>, 1st Earl of Shaftesbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Shaftesbury"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1699", "text": "<PERSON><PERSON><PERSON>, English historian and academic (b. 1616)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Walker\" title=\"Obad<PERSON> Walker\"><PERSON><PERSON><PERSON></a>, English historian and academic (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Walker\" title=\"Obad<PERSON> Walker\"><PERSON><PERSON><PERSON></a>, English historian and academic (b. 1616)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON><PERSON>_<PERSON>"}]}, {"year": "1706", "text": "<PERSON><PERSON>, French scholar and critic (b. 1649)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French scholar and critic (b. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French scholar and critic (b. 1649)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, German mystic and critic (b. 1638)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mystic and critic (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mystic and critic (b. 1638)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1722", "text": "<PERSON>, 2nd Duke of Bolton, English politician, Lord Lieutenant of Ireland (b. 1661)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Bolton\" title=\"<PERSON>, 2nd Duke of Bolton\"><PERSON>, 2nd Duke of Bolton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_<PERSON>_Bolton\" title=\"<PERSON>, 2nd Duke of Bolton\"><PERSON>, 2nd Duke of Bolton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1661)", "links": [{"title": "<PERSON>, 2nd Duke of Bolton", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_<PERSON>_Bolton"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1731", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian poet and translator (b. 1675)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/Ignjat_%C4%90ur%C4%91evi%C4%87\" title=\"<PERSON>gn<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian poet and translator (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ignjat_%C4%90ur%C4%91evi%C4%87\" title=\"<PERSON>gn<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian poet and translator (b. 1675)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ignjat_%C4%90ur%C4%91evi%C4%87"}]}, {"year": "1773", "text": "<PERSON>, French playwright and author (b. 1689)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and author (b. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and author (b. 1689)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON>, Ottoman sultan (b. 1717)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mustafa III\"><PERSON></a>, Ottoman sultan (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III\" title=\"Mustafa III\"><PERSON></a>, Ottoman sultan (b. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_III"}]}, {"year": "1775", "text": "<PERSON><PERSON><PERSON>, Russian rebel (b. 1742)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian rebel (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian rebel (b. 1742)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, French-German philosopher and author (b. 1723)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/Baron_<PERSON>%27<PERSON><PERSON><PERSON>\" title=\"Baron <PERSON>\">Baron <PERSON></a>, French-German philosopher and author (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baron_<PERSON>%27<PERSON><PERSON><PERSON>\" title=\"Baron <PERSON>\">Baron <PERSON></a>, French-German philosopher and author (b. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON>bach"}]}, {"year": "1793", "text": "<PERSON> France (b. 1754)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XVI_of_France\" class=\"mw-redirect\" title=\"<PERSON> XVI of France\"><PERSON> of France</a> (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XVI_of_France\" class=\"mw-redirect\" title=\"<PERSON> XVI of France\"><PERSON> of France</a> (b. 1754)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}]}, {"year": "1795", "text": "<PERSON>, English navigator and explorer (b. 1728)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English navigator and explorer (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English navigator and explorer (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, Irish-American educator and judge", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American educator and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American educator and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, American engineer and politician (b. 1729)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician (b. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON><PERSON>, French botanist and author (b. 1737)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French botanist and author (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French botanist and author (b. 1737)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian cleric, journalist, and poet (b. 1761)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/Cayetano_Jos%C3%A9_Rodr%C3%ADguez\" title=\"<PERSON>ayeta<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian cleric, journalist, and poet (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cayetano_Jos%C3%A9_Rodr%C3%ADguez\" title=\"Cayeta<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian cleric, journalist, and poet (b. 1761)", "links": [{"title": "Cayeta<PERSON>", "link": "https://wikipedia.org/wiki/Cayetano_Jos%C3%A9_Rodr%C3%ADguez"}]}, {"year": "1831", "text": "<PERSON>, German poet and author (b. 1781)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German poet and author (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German poet and author (b. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, German actor and composer (b. 1801)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and composer (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and composer (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON>, Austrian-Czech author and poet (b. 1820)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Bo%C5%BEena_N%C4%9Bmcov%C3%A1\" title=\"Božena Němcová\"><PERSON><PERSON><PERSON> Němcová</a>, Austrian-Czech author and poet (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bo%C5%BEena_N%C4%9Bmcov%C3%A1\" title=\"Božena Němcová\"><PERSON><PERSON><PERSON> Němcová</a>, Austrian-Czech author and poet (b. 1820)", "links": [{"title": "Božena Němcová", "link": "https://wikipedia.org/wiki/Bo%C5%BEena_N%C4%9Bmcov%C3%A1"}]}, {"year": "1870", "text": "<PERSON>, Russian philosopher and author (b. 1812)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philosopher and author (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philosopher and author (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Austrian playwright and poet (b. 1791)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian playwright and poet (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian playwright and poet (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_Grillparzer"}]}, {"year": "1881", "text": "<PERSON>, Swiss lawyer and politician (b. 1802)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Canadian-American lieutenant and composer (b. 1842)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Calixa_Lavall%C3%A9e\" title=\"Calixa Lavallée\"><PERSON><PERSON><PERSON></a>, Canadian-American lieutenant and composer (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Calixa_Lavall%C3%A9e\" title=\"Calixa Lavallée\"><PERSON><PERSON><PERSON></a>, Canadian-American lieutenant and composer (b. 1842)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Calixa_Lavall%C3%A9e"}]}, {"year": "1901", "text": "<PERSON><PERSON>, American engineer, co-founded Western Electric (b. 1835)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American engineer, co-founded <a href=\"https://wikipedia.org/wiki/Western_Electric\" title=\"Western Electric\">Western Electric</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American engineer, co-founded <a href=\"https://wikipedia.org/wiki/Western_Electric\" title=\"Western Electric\">Western Electric</a> (b. 1835)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Western Electric", "link": "https://wikipedia.org/wiki/Western_Electric"}]}, {"year": "1914", "text": "<PERSON>, Norwegian painter and illustrator (b. 1857)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter and illustrator (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter and illustrator (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Polish pianist and music teacher (b. 1857)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish pianist and music teacher (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish pianist and music teacher (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON> of Korea (b. 1852)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Gojong_of_Korea\" title=\"Gojong of Korea\">Gojong of Korea</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gojong_of_Korea\" title=\"Gojong of Korea\">Gojong of Korea</a> (b. 1852)", "links": [{"title": "Gojong of Korea", "link": "https://wikipedia.org/wiki/Gojong_of_Korea"}]}, {"year": "1919", "text": "<PERSON>, Ottoman general and politician, 277th Grand Vizier of the Ottoman Empire (b. 1839)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman general and politician, 277th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman general and politician, 277th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1924", "text": "<PERSON>, Russian lawyer and politician (b. 1870)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lawyer and politician (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lawyer and politician (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lenin"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Italian physician and pathologist, Nobel Prize laureate (b. 1843)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Camillo_Golgi\" title=\"Camillo Golgi\"><PERSON><PERSON></a>, Italian physician and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Camillo_Golgi\" title=\"Camillo Golgi\"><PERSON><PERSON></a>, Italian physician and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1843)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camillo_Golgi"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1928", "text": "<PERSON>, American general and engineer (b. 1858)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Goethals\" title=\"George Washington Goethals\"><PERSON></a>, American general and engineer (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Goethals\" title=\"George Washington Goethals\"><PERSON></a>, American general and engineer (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington_Goethals"}]}, {"year": "1931", "text": "<PERSON>, Russian pianist, composer, and conductor (b. 1863)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, English writer and critic (b. 1880)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Strachey\" title=\"<PERSON><PERSON><PERSON> Strachey\"><PERSON><PERSON><PERSON></a>, English writer and critic (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Strachey\" title=\"<PERSON><PERSON><PERSON> Strachey\"><PERSON><PERSON><PERSON></a>, English writer and critic (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON> Strachey", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Strachey"}]}, {"year": "1933", "text": "<PERSON>, Irish author, poet, and critic (b. 1852)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Irish author, poet, and critic (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Irish author, poet, and critic (b. 1852)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_(novelist)"}]}, {"year": "1937", "text": "<PERSON>, Canadian-American actress and singer (b. 1896)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, French actor, director, and producer (b. 1861)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9li%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and producer (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9li%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and producer (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georges_M%C3%A9li%C3%A8s"}]}, {"year": "1945", "text": "<PERSON><PERSON>, founder of the Indian National Army (b. 1886)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/Indian_National_Army\" title=\"Indian National Army\">Indian National Army</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/Indian_National_Army\" title=\"Indian National Army\">Indian National Army</a> (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Indian National Army", "link": "https://wikipedia.org/wiki/Indian_National_Army"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>-<PERSON>, Italian composer and educator (b. 1876)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>-Ferrari\"><PERSON><PERSON><PERSON></a>, Italian composer and educator (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>-Ferrari\"><PERSON><PERSON><PERSON></a>, Italian composer and educator (b. 1876)", "links": [{"title": "<PERSON><PERSON><PERSON> Wolf-Ferrari", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, British novelist, essayist, and critic (b. 1903)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, essayist, and critic (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, essayist, and critic (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, German-American runner and coach (b. 1880)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, German-American runner and coach (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, German-American runner and coach (b. 1880)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "1959", "text": "<PERSON>, American director, producer, and screenwriter (b. 1881)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, pioneering Canadian forensic pathologist (b. 1882)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, pioneering Canadian <a href=\"https://wikipedia.org/wiki/Forensic_pathologist\" class=\"mw-redirect\" title=\"Forensic pathologist\">forensic pathologist</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, pioneering Canadian <a href=\"https://wikipedia.org/wiki/Forensic_pathologist\" class=\"mw-redirect\" title=\"Forensic pathologist\">forensic pathologist</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Forensic pathologist", "link": "https://wikipedia.org/wiki/Forensic_pathologist"}]}, {"year": "1959", "text": "<PERSON>, American child actor and hunting guide (b. 1927)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American child actor and hunting guide (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American child actor and hunting guide (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Swiss author and poet (b. 1887)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and poet (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and poet (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "Acharya <PERSON><PERSON>, Indian author, poet, and academic (b. 1893)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Acharya_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Acharya <PERSON><PERSON><PERSON><PERSON>\">Acharya <PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and academic (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Acharya_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Acharya <PERSON><PERSON><PERSON><PERSON>\">Acharya <PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and academic (b. 1893)", "links": [{"title": "A<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Greek-Swedish painter (b. 1881)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Swedish painter (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Swedish painter (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actress (b. 1915)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Italian poet and journalist (b. 1906)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet and journalist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet and journalist (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1978", "text": "<PERSON><PERSON>, English scholar and author (b. 1898)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English scholar and author (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English scholar and author (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American bass player (b. 1949)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Greek playwright and poet (b. 1893)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek playwright and poet (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek playwright and poet (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer (b. 1934)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American chef and author (b. 1903)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American wrestler and promoter (b. 1930)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and promoter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and promoter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American lieutenant, lawyer, and politician (b. 1926)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian Aboriginal rights activist (b. 1919)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian Aboriginal rights activist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian Aboriginal rights activist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player (b. 1922)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American pianist and saxophonist (b. 1914)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and saxophonist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and saxophonist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian land rights activist (b. 1936)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian land rights activist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ma<PERSON>\"><PERSON></a>, Australian land rights activist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American baseball player and manager (b. 1903)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Son of the former President of the Syrian Arab Republic <PERSON><PERSON><PERSON> (b. 1962)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_al-Assad\" title=\"<PERSON><PERSON> al-Assad\"><PERSON><PERSON></a>, Son of the former President of the <a href=\"https://wikipedia.org/wiki/Syrian_Arab_Republic\" class=\"mw-redirect\" title=\"Syrian Arab Republic\">Syrian Arab Republic</a> <a href=\"https://wikipedia.org/wiki/Ha<PERSON><PERSON>_al-Assad\" title=\"<PERSON><PERSON><PERSON> al<PERSON>Assad\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_al-Assad\" title=\"<PERSON><PERSON> al-Assad\"><PERSON><PERSON></a>, Son of the former President of the <a href=\"https://wikipedia.org/wiki/Syrian_Arab_Republic\" class=\"mw-redirect\" title=\"Syrian Arab Republic\">Syrian Arab Republic</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al-Assad\" title=\"<PERSON><PERSON><PERSON> al<PERSON>Assad\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a> (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Syrian Arab Republic", "link": "https://wikipedia.org/wiki/Syrian_Arab_Republic"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor, director, and producer (b. 1920)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lord\"><PERSON></a>, American actor, director, and producer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actress (b. 1938)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American singer (b. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American-Canadian poet and songwriter (b. 1933)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American-Canadian poet and songwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American-Canadian poet and songwriter (b. 1933)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "2003", "text": "<PERSON>, Estonian journalist and author (b. 1916)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and author (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Bulgarian author and playwright (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian author and playwright (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian author and playwright (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Dutch author and poet (b. 1907)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author and poet (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author and poet (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American journalist and critic (b. 1917)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Estonian cellist, composer, and pastor (b. 1921)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian cellist, composer, and pastor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian cellist, composer, and pastor (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d"}]}, {"year": "2006", "text": "<PERSON>, Kosovo journalist and politician, 1st President of Kosovo (b. 1944)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kosovo journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Kosovo\" title=\"President of Kosovo\">President of Kosovo</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kosovo journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Kosovo\" title=\"President of Kosovo\">President of Kosovo</a> (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Kosovo", "link": "https://wikipedia.org/wiki/President_of_Kosovo"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Estonian journalist, politician and diplomat (b. 1946)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist, politician and diplomat (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist, politician and diplomat (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Canadian author, playwright, guitarist, and composer (b. 1953)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author, playwright, guitarist, and composer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author, playwright, guitarist, and composer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Greek-American costume designer (b. 1922)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>dge\" title=\"<PERSON><PERSON>dge\"><PERSON><PERSON></a>, Greek-American costume designer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>dge\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American costume designer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>dge"}]}, {"year": "2011", "text": "<PERSON>, American sculptor and photographer (b. 1938)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and photographer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and photographer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Indian director, producer, and screenwriter (b. 1958)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/E._V._V._Satyanarayana\" title=\"E. V. V. Satyanarayana\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E._V._V._Satyanarayana\" title=\"E. V. V. Satyanaray<PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1958)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E._V._<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Turkish geophysicist and academic (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ahmet_Mete_I%C5%9F%C4%B1kara\" title=\"Ahmet Mete Işıkara\"><PERSON><PERSON> Işıkara</a>, Turkish geophysicist and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahmet_Mete_I%C5%9F%C4%B1kara\" title=\"Ahmet Mete Işıkara\"><PERSON><PERSON><PERSON>şı<PERSON></a>, Turkish geophysicist and academic (b. 1941)", "links": [{"title": "<PERSON><PERSON> Mete Işıkara", "link": "https://wikipedia.org/wiki/Ahmet_Mete_I%C5%9F%C4%B1kara"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Thai academic and politician, Deputy Prime Minister of Thailand (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Chumpol_Silpa-archa\" title=\"Chumpol Silpa-archa\"><PERSON><PERSON><PERSON> Si<PERSON>-archa</a>, Thai academic and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Thailand\" title=\"Deputy Prime Minister of Thailand\">Deputy Prime Minister of Thailand</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chumpol_Silpa-archa\" title=\"Chumpol Silpa-archa\"><PERSON><PERSON><PERSON> Si<PERSON>pa-archa</a>, Thai academic and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Thailand\" title=\"Deputy Prime Minister of Thailand\">Deputy Prime Minister of Thailand</a> (b. 1940)", "links": [{"title": "Chumpol Silpa-archa", "link": "https://wikipedia.org/wiki/Chumpol_Silpa-archa"}, {"title": "Deputy Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Thailand"}]}, {"year": "2013", "text": "<PERSON>, English director, producer, and screenwriter (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Michael Winner\"><PERSON></a>, English director, producer, and screenwriter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Michael Winner\"><PERSON></a>, English director, producer, and screenwriter (b. 1935)", "links": [{"title": "<PERSON> Winner", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American scholar, theologian, and author (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, theologian, and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, theologian, and author (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English lawyer and politician, Secretary of State for Business, Innovation and Skills (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Business, Innovation and Skills</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Business, Innovation and Skills</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Business, Innovation and Skills", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Liberian lawyer and politician, 18th Chief Justice of Liberia (b. 1946)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Liberian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Liberia\" title=\"Chief Justice of Liberia\">Chief Justice of Liberia</a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Liberian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Liberia\" title=\"Chief Justice of Liberia\">Chief Justice of Liberia</a> (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Justice of Liberia", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Liberia"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Cuban author and dissident (b. 1974)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Canek_S%C3%A1<PERSON><PERSON>_Guevara\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban author and dissident (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canek_S%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban author and dissident (b. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Canek_S%C3%<PERSON><PERSON><PERSON>_<PERSON>ra"}]}, {"year": "2016", "text": "<PERSON>, American skier (b. 1960)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(skier)\" title=\"<PERSON> (skier)\"><PERSON></a>, American skier (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(skier)\" title=\"<PERSON> (skier)\"><PERSON></a>, American skier (b. 1960)", "links": [{"title": "<PERSON> (skier)", "link": "https://wikipedia.org/wiki/<PERSON>_(skier)"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, a 1992-Padma Bhushan award winner Indian classical dancer, choreographer and instructor. (b. 1918)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a 1992-<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> award winner Indian classical dancer, choreographer and instructor. (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a 1992-<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> award winner Indian classical dancer, choreographer and instructor. (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American actress (b. 1925)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Count of Paris, Head of the House of Orléans (b. 1933)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Paris_(1933%E2%80%932019)\" title=\"<PERSON>, Count of Paris (1933-2019)\"><PERSON>, Count of Paris</a>, Head of the House of Orléans (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Paris_(1933%E2%80%932019)\" title=\"<PERSON>, Count of Paris (1933-2019)\"><PERSON>, Count of Paris</a>, Head of the House of Orléans (b. 1933)", "links": [{"title": "<PERSON>, Count of Paris (1933-2019)", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_Paris_(1933%E2%80%932019)"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Argentine footballer (b. 1990)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Emiliano_Sala\" title=\"Emiliano Sala\"><PERSON><PERSON></a>, Argentine footballer (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emiliano_Sala\" title=\"Emiliano Sala\"><PERSON><PERSON></a>, Argentine footballer (b. 1990)", "links": [{"title": "Emiliano <PERSON>", "link": "https://wikipedia.org/wiki/Emiliano_Sala"}]}, {"year": "2019", "text": "<PERSON>, American politician, author and civil rights activist (b. 1926)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, author and civil rights activist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, author and civil rights activist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Welsh actor, director, and screenwriter (b. 1942)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor, director, and screenwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor, director, and screenwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American high school basketball coach (b. 1931)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American high school basketball coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American high school basketball coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ten"}]}, {"year": "2022", "text": "<PERSON>, American actor and comedian (b. 1953)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Chilean socialite, First Lady of Chile (b. 1919)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z%C3%BAn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean socialite, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Chile\" title=\"First Lady of Chile\">First Lady of Chile</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BAn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean socialite, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Chile\" title=\"First Lady of Chile\">First Lady of Chile</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leonor_Oyarz%C3%BAn"}, {"title": "First Lady of Chile", "link": "https://wikipedia.org/wiki/First_Lady_of_Chile"}]}, {"year": "2025", "text": "<PERSON><PERSON><PERSON>, Salvadoran politician, 79th President of El Salvador (b. 1959)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran politician, 79th <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran politician, 79th <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of El Salvador", "link": "https://wikipedia.org/wiki/President_of_El_Salvador"}]}, {"year": "2025", "text": "<PERSON>, Canadian keyboard player, songwriter, and producer (b. 1937)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian keyboard player, songwriter, and producer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian keyboard player, songwriter, and producer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}