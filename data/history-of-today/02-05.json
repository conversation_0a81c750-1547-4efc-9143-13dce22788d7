{"date": "February 5", "url": "https://wikipedia.org/wiki/February_5", "data": {"Events": [{"year": "2 BC", "text": "<PERSON> is granted the title pater patriae by the Roman Senate.", "html": "2 BC - 2 BC - <a href=\"https://wikipedia.org/wiki/Caesar_Augustus\" class=\"mw-redirect\" title=\"Caesar Augustus\">Caesar Augustus</a> is granted the title <i><a href=\"https://wikipedia.org/wiki/Pater_patriae\" class=\"mw-redirect\" title=\"Pater patriae\">pater patriae</a></i> by the <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a>.", "no_year_html": "2 BC - <a href=\"https://wikipedia.org/wiki/Caesar_Augustus\" class=\"mw-redirect\" title=\"Caesar Augustus\">Caesar Augustus</a> is granted the title <i><a href=\"https://wikipedia.org/wiki/Pater_patriae\" class=\"mw-redirect\" title=\"Pater patriae\">pater patriae</a></i> by the <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pater patriae", "link": "https://wikipedia.org/wiki/Pater_patriae"}, {"title": "Roman Senate", "link": "https://wikipedia.org/wiki/Roman_Senate"}]}, {"year": "62", "text": "Earthquake in Pompeii, Italy.", "html": "62 - <a href=\"https://wikipedia.org/wiki/AD_62_Pompeii_earthquake\" title=\"AD 62 Pompeii earthquake\">Earthquake</a> in <a href=\"https://wikipedia.org/wiki/Pompeii\" title=\"Pompeii\">Pompeii</a>, Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/AD_62_Pompeii_earthquake\" title=\"AD 62 Pompeii earthquake\">Earthquake</a> in <a href=\"https://wikipedia.org/wiki/Pompeii\" title=\"Pompeii\">Pompeii</a>, Italy.", "links": [{"title": "AD 62 Pompeii earthquake", "link": "https://wikipedia.org/wiki/AD_62_Pompeii_earthquake"}, {"title": "Pompeii", "link": "https://wikipedia.org/wiki/Pompeii"}]}, {"year": "756", "text": "Chinese New Year; <PERSON> proclaims himself Emperor of China and founds the short-lived state of Yan.", "html": "756 - <a href=\"https://wikipedia.org/wiki/Chinese_New_Year\" title=\"Chinese New Year\">Chinese New Year</a>; <a href=\"https://wikipedia.org/wiki/An_Lushan\" title=\"An Lushan\"><PERSON></a> proclaims himself <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Emperor of China</a> and founds the short-lived state of <a href=\"https://wikipedia.org/wiki/Yan_(An%E2%80%93Shi)\" title=\"<PERSON> (An-Shi)\">Yan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chinese_New_Year\" title=\"Chinese New Year\">Chinese New Year</a>; <a href=\"https://wikipedia.org/wiki/An_Lushan\" title=\"<PERSON> Lu<PERSON>\"><PERSON></a> proclaims himself <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Emperor of China</a> and founds the short-lived state of <a href=\"https://wikipedia.org/wiki/Yan_(An%E2%80%93Shi)\" title=\"Yan (An-Shi)\">Yan</a>.", "links": [{"title": "Chinese New Year", "link": "https://wikipedia.org/wiki/Chinese_New_Year"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>shan"}, {"title": "Emperor of China", "link": "https://wikipedia.org/wiki/Emperor_of_China"}, {"title": "<PERSON> (An-Shi)", "link": "https://wikipedia.org/wiki/<PERSON>_(An%E2%80%93Shi)"}]}, {"year": "1576", "text": "<PERSON> of Navarre abjures Catholicism at Tours and rejoins the Protestant forces in the French Wars of Religion.", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of Navarre</a> <a href=\"https://wikipedia.orghttps://en.wiktionary.org/wiki/abjure\" class=\"extiw\" title=\"wikt:abjure\">abjures</a> Catholicism at <a href=\"https://wikipedia.org/wiki/Tours\" title=\"Tours\">Tours</a> and rejoins the Protestant forces in the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of Navarre</a> <a href=\"https://wikipedia.orghttps://en.wiktionary.org/wiki/abjure\" class=\"extiw\" title=\"wikt:abjure\">abjures</a> Catholicism at <a href=\"https://wikipedia.org/wiki/Tours\" title=\"Tours\">Tours</a> and rejoins the Protestant forces in the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}, {"title": "wikt:abjure", "link": "https://wikipedia.orghttps://en.wiktionary.org/wiki/abjure"}, {"title": "Tours", "link": "https://wikipedia.org/wiki/Tours"}, {"title": "French Wars of Religion", "link": "https://wikipedia.org/wiki/French_Wars_of_Religion"}]}, {"year": "1597", "text": "A group of early Japanese Christians are killed by the new government of Japan for being seen as a threat to Japanese society.", "html": "1597 - A group of <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON>_Kirishitan\" title=\"Kakure Kirishitan\">early Japanese Christians</a> are <a href=\"https://wikipedia.org/wiki/26_Martyrs_of_Japan\" title=\"26 Martyrs of Japan\">killed</a> by the new government of Japan for being seen as a threat to Japanese society.", "no_year_html": "A group of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ki<PERSON>\" title=\"Kaku<PERSON> Kiris<PERSON>n\">early Japanese Christians</a> are <a href=\"https://wikipedia.org/wiki/26_Martyrs_of_Japan\" title=\"26 Martyrs of Japan\">killed</a> by the new government of Japan for being seen as a threat to Japanese society.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}, {"title": "26 Martyrs of Japan", "link": "https://wikipedia.org/wiki/26_Martyrs_of_Japan"}]}, {"year": "1783", "text": "In Calabria, a sequence of strong earthquakes begins.", "html": "1783 - In <a href=\"https://wikipedia.org/wiki/Calabria\" title=\"Calabria\">Calabria</a>, a <a href=\"https://wikipedia.org/wiki/1783_Calabrian_earthquakes\" title=\"1783 Calabrian earthquakes\">sequence of strong earthquakes</a> begins.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Calabria\" title=\"Calabria\">Calabria</a>, a <a href=\"https://wikipedia.org/wiki/1783_Calabrian_earthquakes\" title=\"1783 Calabrian earthquakes\">sequence of strong earthquakes</a> begins.", "links": [{"title": "Calabria", "link": "https://wikipedia.org/wiki/Calabria"}, {"title": "1783 Calabrian earthquakes", "link": "https://wikipedia.org/wiki/1783_Calabrian_earthquakes"}]}, {"year": "1810", "text": "Peninsular War: Siege of Cádiz begins.", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_C%C3%A1diz\" title=\"Siege of Cádiz\">Siege of Cádiz</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_C%C3%A1diz\" title=\"Siege of Cádiz\">Siege of Cádiz</a> begins.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "Siege of Cádiz", "link": "https://wikipedia.org/wiki/Siege_of_C%C3%A1diz"}]}, {"year": "1818", "text": "<PERSON><PERSON><PERSON> ascends to the thrones of Sweden and Norway.", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON> John\"><PERSON><PERSON><PERSON></a> ascends to the thrones of Sweden and Norway.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles XIV John\"><PERSON><PERSON><PERSON></a> ascends to the thrones of Sweden and Norway.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "The New Hermitage Museum in Saint Petersburg, Russia, one of the largest and oldest museums in the world, opens to the public.", "html": "1852 - The New <a href=\"https://wikipedia.org/wiki/Hermitage_Museum\" title=\"Hermitage Museum\">Hermitage Museum</a> in <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, Russia, one of the largest and oldest museums in the world, opens to the public.", "no_year_html": "The New <a href=\"https://wikipedia.org/wiki/Hermitage_Museum\" title=\"Hermitage Museum\">Hermitage Museum</a> in <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, Russia, one of the largest and oldest museums in the world, opens to the public.", "links": [{"title": "Hermitage Museum", "link": "https://wikipedia.org/wiki/Hermitage_Museum"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON>, Prince of Moldavia, is also elected as prince of Wallachia, joining the two principalities as a personal union called the United Principalities, an autonomous region within the Ottoman Empire, which ushered in the birth of the modern Romanian state.", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Prince of <a href=\"https://wikipedia.org/wiki/Moldavia\" title=\"Moldavia\">Molda<PERSON></a>, is also elected as prince of <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a>, joining the two principalities as a <a href=\"https://wikipedia.org/wiki/Personal_union\" title=\"Personal union\">personal union</a> called the <a href=\"https://wikipedia.org/wiki/United_Principalities_of_Moldavia_and_Wallachia\" title=\"United Principalities of Moldavia and Wallachia\">United Principalities</a>, an autonomous region within the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, which ushered in the birth of the modern <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romanian</a> state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Prince of <a href=\"https://wikipedia.org/wiki/Moldavia\" title=\"Moldavia\">Molda<PERSON></a>, is also elected as prince of <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a>, joining the two principalities as a <a href=\"https://wikipedia.org/wiki/Personal_union\" title=\"Personal union\">personal union</a> called the <a href=\"https://wikipedia.org/wiki/United_Principalities_of_Moldavia_and_Wallachia\" title=\"United Principalities of Moldavia and Wallachia\">United Principalities</a>, an autonomous region within the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, which ushered in the birth of the modern <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romanian</a> state.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Moldavia", "link": "https://wikipedia.org/wiki/Moldavia"}, {"title": "Wallachia", "link": "https://wikipedia.org/wiki/Wallachia"}, {"title": "Personal union", "link": "https://wikipedia.org/wiki/Personal_union"}, {"title": "United Principalities of Moldavia and Wallachia", "link": "https://wikipedia.org/wiki/United_Principalities_of_Moldavia_and_Wallachia"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}]}, {"year": "1869", "text": "The largest alluvial gold nugget in history, called the \"Welcome Stranger\", is found in Moliagul, Victoria, Australia.", "html": "1869 - The largest <a href=\"https://wikipedia.org/wiki/Alluvium\" title=\"Alluvium\">alluvial</a> <a href=\"https://wikipedia.org/wiki/Gold\" title=\"Gold\">gold</a> nugget in history, called the \"<a href=\"https://wikipedia.org/wiki/Welcome_Stranger\" title=\"Welcome Stranger\">Welcome Stranger</a>\", is found in <a href=\"https://wikipedia.org/wiki/Moliagul\" title=\"Moliagul\">Moliagul</a>, Victoria, Australia.", "no_year_html": "The largest <a href=\"https://wikipedia.org/wiki/Alluvium\" title=\"Alluvium\">alluvial</a> <a href=\"https://wikipedia.org/wiki/Gold\" title=\"Gold\">gold</a> nugget in history, called the \"<a href=\"https://wikipedia.org/wiki/Welcome_Stranger\" title=\"Welcome Stranger\">Welcome Stranger</a>\", is found in <a href=\"https://wikipedia.org/wiki/Moliagul\" title=\"Moliagul\">Moliagul</a>, Victoria, Australia.", "links": [{"title": "Alluvium", "link": "https://wikipedia.org/wiki/Alluvium"}, {"title": "Gold", "link": "https://wikipedia.org/wiki/Gold"}, {"title": "Welcome Stranger", "link": "https://wikipedia.org/wiki/Welcome_Stranger"}, {"title": "Moliagul", "link": "https://wikipedia.org/wiki/Moliagul"}]}, {"year": "1885", "text": "King <PERSON> of Belgium establishes the Congo as a personal possession.", "html": "1885 - King <a href=\"https://wikipedia.org/wiki/Leopold_II_of_Belgium\" title=\"Leopold II of Belgium\">Leopold II of Belgium</a> establishes the <a href=\"https://wikipedia.org/wiki/Congo_Free_State\" title=\"Congo Free State\">Congo</a> as a personal possession.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Leopold_II_of_Belgium\" title=\"Leopold II of Belgium\">Leopold II of Belgium</a> establishes the <a href=\"https://wikipedia.org/wiki/Congo_Free_State\" title=\"Congo Free State\">Congo</a> as a personal possession.", "links": [{"title": "Leopold II of Belgium", "link": "https://wikipedia.org/wiki/Leopold_II_of_Belgium"}, {"title": "Congo Free State", "link": "https://wikipedia.org/wiki/Congo_Free_State"}]}, {"year": "1901", "text": "J. P. Morgan incorporates U.S. Steel in the state of New Jersey, although the company would not start doing business until February 25 and the assets of Andrew Carnegie's Carnegie Steel Company, Elbert H. Gary's Federal Steel Company, and <PERSON>'s National Steel Company were not acquired until April 1.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. P. Morgan\"><PERSON><PERSON> <PERSON><PERSON></a> incorporates <a href=\"https://wikipedia.org/wiki/U.S._Steel\" title=\"U.S. Steel\">U.S. Steel</a> in the state of <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a>, although the company would not start doing business until February 25 and the assets of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Andrew Carnegie\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Carnegie_Steel_Company\" title=\"Carnegie Steel Company\">Carnegie Steel Company</a>, <a href=\"https://wikipedia.org/wiki/Elbert_H._Gary\" class=\"mw-redirect\" title=\"Elbert H. Gary\"><PERSON><PERSON> Gary</a>'s <a href=\"https://wikipedia.org/wiki/Federal_Steel_Company\" class=\"mw-redirect\" title=\"Federal Steel Company\">Federal Steel Company</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%22Judge%22_<PERSON>\" class=\"mw-redirect\" title='<PERSON> <PERSON> \"Judge\" Moore'>William Henry Moore</a>'s National Steel Company were not acquired until April 1.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. P. Morgan\"><PERSON><PERSON> <PERSON><PERSON></a> incorporates <a href=\"https://wikipedia.org/wiki/U.S._Steel\" title=\"U.S. Steel\">U.S. Steel</a> in the state of <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a>, although the company would not start doing business until February 25 and the assets of <a href=\"https://wikipedia.org/wiki/Andrew_Carnegie\" title=\"Andrew Carnegie\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Carnegie_Steel_Company\" title=\"Carnegie Steel Company\">Carnegie Steel Company</a>, <a href=\"https://wikipedia.org/wiki/Elbert_H._Gary\" class=\"mw-redirect\" title=\"Elbert H. Gary\">Elbert H. Gary</a>'s <a href=\"https://wikipedia.org/wiki/Federal_Steel_Company\" class=\"mw-redirect\" title=\"Federal Steel Company\">Federal Steel Company</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%22Judge%22_<PERSON>\" class=\"mw-redirect\" title='<PERSON> \"Judge\" Moore'>William Henry Moore</a>'s National Steel Company were not acquired until April 1.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "U.S. Steel", "link": "https://wikipedia.org/wiki/U.S._Steel"}, {"title": "New Jersey", "link": "https://wikipedia.org/wiki/New_Jersey"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Carnegie Steel Company", "link": "https://wikipedia.org/wiki/Carnegie_Steel_Company"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Federal Steel Company", "link": "https://wikipedia.org/wiki/Federal_Steel_Company"}, {"title": "<PERSON> \"Judge\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%22Judge%22_<PERSON>"}]}, {"year": "1905", "text": "In Mexico, the General Hospital of Mexico is inaugurated, started with four basic specialties.", "html": "1905 - In Mexico, the <a href=\"https://wikipedia.org/wiki/General_Hospital_of_Mexico\" title=\"General Hospital of Mexico\">General Hospital of Mexico</a> is inaugurated, started with four basic specialties.", "no_year_html": "In Mexico, the <a href=\"https://wikipedia.org/wiki/General_Hospital_of_Mexico\" title=\"General Hospital of Mexico\">General Hospital of Mexico</a> is inaugurated, started with four basic specialties.", "links": [{"title": "General Hospital of Mexico", "link": "https://wikipedia.org/wiki/General_Hospital_of_Mexico"}]}, {"year": "1907", "text": "Belgian chemist <PERSON> announces the creation of Bakelite, the world's first synthetic plastic.", "html": "1907 - Belgian chemist <a href=\"https://wikipedia.org/wiki/Leo_<PERSON>\" title=\"Leo Ba<PERSON>\"><PERSON></a> announces the creation of <a href=\"https://wikipedia.org/wiki/Bakelite\" title=\"Bakelite\">Bakelite</a>, the world's first synthetic <a href=\"https://wikipedia.org/wiki/Plastic\" title=\"Plastic\">plastic</a>.", "no_year_html": "Belgian chemist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the creation of <a href=\"https://wikipedia.org/wiki/Bakelite\" title=\"Bakelite\">Bakelite</a>, the world's first synthetic <a href=\"https://wikipedia.org/wiki/Plastic\" title=\"Plastic\">plastic</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Baekeland"}, {"title": "Bakelite", "link": "https://wikipedia.org/wiki/Bakelite"}, {"title": "Plastic", "link": "https://wikipedia.org/wiki/Plastic"}]}, {"year": "1913", "text": "Greek military aviators <PERSON> and <PERSON><PERSON><PERSON><PERSON> perform the first naval air mission in history, with a Farman MF.7 hydroplane.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Hellenic_Air_Force\" title=\"Hellenic Air Force\">Greek military aviators</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(aviator)\" title=\"<PERSON><PERSON><PERSON><PERSON> (aviator)\"><PERSON><PERSON><PERSON><PERSON></a> perform the first <a href=\"https://wikipedia.org/wiki/Naval_aviation\" title=\"Naval aviation\">naval air</a> mission in history, with a <a href=\"https://wikipedia.org/wiki/Farman_MF.7\" title=\"Farman MF.7\">Farman MF.7</a> hydroplane.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hellenic_Air_Force\" title=\"Hellenic Air Force\">Greek military aviators</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(aviator)\" title=\"<PERSON><PERSON><PERSON><PERSON> (aviator)\"><PERSON><PERSON><PERSON><PERSON></a> perform the first <a href=\"https://wikipedia.org/wiki/Naval_aviation\" title=\"Naval aviation\">naval air</a> mission in history, with a <a href=\"https://wikipedia.org/wiki/Farman_MF.7\" title=\"Farman MF.7\">Farman MF.7</a> hydroplane.", "links": [{"title": "Hellenic Air Force", "link": "https://wikipedia.org/wiki/Hellenic_Air_Force"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (aviator)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(aviator)"}, {"title": "Naval aviation", "link": "https://wikipedia.org/wiki/Naval_aviation"}, {"title": "Farman MF.7", "link": "https://wikipedia.org/wiki/Farman_MF.7"}]}, {"year": "1913", "text": "<PERSON>'s last opera L'incoronazione di Poppea was performed theatrically for the first time in more than 250 years.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s last opera <i><a href=\"https://wikipedia.org/wiki/L%27incoronazione_di_Poppea\" title=\"L'incoronazione di Poppea\">L'incoronazione di Poppea</a></i> was performed theatrically for the first time in more than 250 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s last opera <i><a href=\"https://wikipedia.org/wiki/L%27incoronazione_di_Poppea\" title=\"L'incoronazione di Poppea\">L'incoronazione di Poppea</a></i> was performed theatrically for the first time in more than 250 years.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "L'incoronazione di Poppea", "link": "https://wikipedia.org/wiki/L%27incoronazione_di_Poppea"}]}, {"year": "1917", "text": "The current constitution of Mexico is adopted, establishing a federal republic with powers separated into independent executive, legislative, and judicial branches.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/Constitution_of_Mexico\" title=\"Constitution of Mexico\">current constitution</a> of Mexico is adopted, establishing a <a href=\"https://wikipedia.org/wiki/Federal_republic\" title=\"Federal republic\">federal republic</a> with powers separated into independent executive, legislative, and judicial branches.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Constitution_of_Mexico\" title=\"Constitution of Mexico\">current constitution</a> of Mexico is adopted, establishing a <a href=\"https://wikipedia.org/wiki/Federal_republic\" title=\"Federal republic\">federal republic</a> with powers separated into independent executive, legislative, and judicial branches.", "links": [{"title": "Constitution of Mexico", "link": "https://wikipedia.org/wiki/Constitution_of_Mexico"}, {"title": "Federal republic", "link": "https://wikipedia.org/wiki/Federal_republic"}]}, {"year": "1917", "text": "The Congress of the United States passes the Immigration Act of 1917 over President <PERSON>'s veto.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress of the United States</a> passes the <a href=\"https://wikipedia.org/wiki/Immigration_Act_of_1917\" title=\"Immigration Act of 1917\">Immigration Act of 1917</a> over President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s veto.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress of the United States</a> passes the <a href=\"https://wikipedia.org/wiki/Immigration_Act_of_1917\" title=\"Immigration Act of 1917\">Immigration Act of 1917</a> over President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s veto.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Immigration Act of 1917", "link": "https://wikipedia.org/wiki/Immigration_Act_of_1917"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1918", "text": "<PERSON> shoots down a German airplane; this is the first aerial victory by the U.S. military.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> shoots down a German airplane; this is the first aerial victory by the U.S. military.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> shoots down a German airplane; this is the first aerial victory by the U.S. military.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "SS Tuscania is torpedoed off the coast of Ireland; it is the first ship carrying American troops to Europe to be torpedoed and sunk.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/SS_Tuscania_(1914)\" title=\"SS Tuscania (1914)\">SS <i>Tuscania</i></a> is torpedoed off the coast of Ireland; it is the first ship carrying American troops to Europe to be torpedoed and sunk.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SS_Tuscania_(1914)\" title=\"SS Tuscania (1914)\">SS <i>Tuscania</i></a> is torpedoed off the coast of Ireland; it is the first ship carrying American troops to Europe to be torpedoed and sunk.", "links": [{"title": "SS Tuscania (1914)", "link": "https://wikipedia.org/wiki/SS_Tuscania_(1914)"}]}, {"year": "1919", "text": "<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> <PERSON><PERSON> launch United Artists.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Douglas Fairbanks\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/D._W._Griffith\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> launch <a href=\"https://wikipedia.org/wiki/United_Artists\" title=\"United Artists\">United Artists</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Douglas Fairbanks\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/D._W<PERSON>_<PERSON>\" title=\"D<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> launch <a href=\"https://wikipedia.org/wiki/United_Artists\" title=\"United Artists\">United Artists</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United Artists", "link": "https://wikipedia.org/wiki/United_Artists"}]}, {"year": "1924", "text": "The Royal Greenwich Observatory begins broadcasting the hourly time signals known as the Greenwich Time Signal.", "html": "1924 - The <a href=\"https://wikipedia.org/wiki/Royal_Observatory,_Greenwich\" title=\"Royal Observatory, Greenwich\">Royal Greenwich Observatory</a> begins broadcasting the hourly time signals known as the <a href=\"https://wikipedia.org/wiki/Greenwich_Time_Signal\" title=\"Greenwich Time Signal\">Greenwich Time Signal</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Observatory,_Greenwich\" title=\"Royal Observatory, Greenwich\">Royal Greenwich Observatory</a> begins broadcasting the hourly time signals known as the <a href=\"https://wikipedia.org/wiki/Greenwich_Time_Signal\" title=\"Greenwich Time Signal\">Greenwich Time Signal</a>.", "links": [{"title": "Royal Observatory, Greenwich", "link": "https://wikipedia.org/wiki/Royal_Observatory,_Greenwich"}, {"title": "Greenwich Time Signal", "link": "https://wikipedia.org/wiki/Greenwich_Time_Signal"}]}, {"year": "1933", "text": "Mutiny on Royal Netherlands Navy warship HNLMS De Zeven Provinciën off the coast of Sumatra, Dutch East Indies.", "html": "1933 - Mutiny on <a href=\"https://wikipedia.org/wiki/Royal_Netherlands_Navy\" title=\"Royal Netherlands Navy\">Royal Netherlands Navy</a> warship <a href=\"https://wikipedia.org/wiki/HNLMS_De_Zeven_Provinci%C3%ABn_(1909)\" title=\"HNLMS De Zeven Provinciën (1909)\">HNLMS <i>De Zeven Provinciën</i></a> off the coast of <a href=\"https://wikipedia.org/wiki/Sumatra\" title=\"Sumatra\">Sumatra</a>, <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>.", "no_year_html": "Mutiny on <a href=\"https://wikipedia.org/wiki/Royal_Netherlands_Navy\" title=\"Royal Netherlands Navy\">Royal Netherlands Navy</a> warship <a href=\"https://wikipedia.org/wiki/HNLMS_De_Zeven_Provinci%C3%ABn_(1909)\" title=\"HNLMS De Zeven Provinciën (1909)\">HNLMS <i>De Zeven Provinciën</i></a> off the coast of <a href=\"https://wikipedia.org/wiki/Sumatra\" title=\"Sumatra\">Sumatra</a>, <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>.", "links": [{"title": "Royal Netherlands Navy", "link": "https://wikipedia.org/wiki/Royal_Netherlands_Navy"}, {"title": "HNLMS De Zeven Provinciën (1909)", "link": "https://wikipedia.org/wiki/HNLMS_De_Zeven_Provinci%C3%ABn_(1909)"}, {"title": "Sumatra", "link": "https://wikipedia.org/wiki/Sumatra"}, {"title": "Dutch East Indies", "link": "https://wikipedia.org/wiki/Dutch_East_Indies"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON> becomes the 68th \"Caudillo de España\", or Leader of Spain.", "html": "1939 - <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the 68th \"<i><PERSON><PERSON><PERSON> de España</i>\", or Leader of Spain.", "no_year_html": "<PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the 68th \"<i>Caudillo de España</i>\", or Leader of Spain.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}]}, {"year": "1941", "text": "World War II: Allied forces begin the Battle of Keren to capture Keren, Eritrea.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces begin the <a href=\"https://wikipedia.org/wiki/Battle_of_Keren\" title=\"Battle of Keren\">Battle of Keren</a> to capture <a href=\"https://wikipedia.org/wiki/Keren,_Eritrea\" title=\"Keren, Eritrea\">Keren, Eritrea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces begin the <a href=\"https://wikipedia.org/wiki/Battle_of_Keren\" title=\"Battle of Keren\">Battle of Keren</a> to capture <a href=\"https://wikipedia.org/wiki/Keren,_Eritrea\" title=\"Keren, Eritrea\">Keren, Eritrea</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Battle of Keren", "link": "https://wikipedia.org/wiki/Battle_of_Keren"}, {"title": "Keren, Eritrea", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Erit<PERSON>"}]}, {"year": "1945", "text": "World War II: General <PERSON> returns to Manila.", "html": "1945 - World War II: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>.", "no_year_html": "World War II: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Manila", "link": "https://wikipedia.org/wiki/Manila"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON> is nominated to be the first president of the United Arab Republic.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Gamal Abdel Nasser\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a> is nominated to be the first president of the <a href=\"https://wikipedia.org/wiki/United_Arab_Republic\" title=\"United Arab Republic\">United Arab Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Gama<PERSON> Abdel Nasser\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a> is nominated to be the first president of the <a href=\"https://wikipedia.org/wiki/United_Arab_Republic\" title=\"United Arab Republic\">United Arab Republic</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United Arab Republic", "link": "https://wikipedia.org/wiki/United_Arab_Republic"}]}, {"year": "1958", "text": "A hydrogen bomb known as the Tybee Bomb is lost by the US Air Force off the coast of Savannah, Georgia, never to be recovered.", "html": "1958 - A <a href=\"https://wikipedia.org/wiki/Thermonuclear_weapon\" title=\"Thermonuclear weapon\">hydrogen bomb</a> known as the Tybee Bomb is <a href=\"https://wikipedia.org/wiki/1958_Tybee_Island_mid-air_collision\" title=\"1958 Tybee Island mid-air collision\">lost</a> by the US Air Force off the coast of <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>, never to be recovered.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Thermonuclear_weapon\" title=\"Thermonuclear weapon\">hydrogen bomb</a> known as the Tybee Bomb is <a href=\"https://wikipedia.org/wiki/1958_Tybee_Island_mid-air_collision\" title=\"1958 Tybee Island mid-air collision\">lost</a> by the US Air Force off the coast of <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>, never to be recovered.", "links": [{"title": "Thermonuclear weapon", "link": "https://wikipedia.org/wiki/Thermonuclear_weapon"}, {"title": "1958 Tybee Island mid-air collision", "link": "https://wikipedia.org/wiki/1958_Tybee_Island_mid-air_collision"}, {"title": "Savannah, Georgia", "link": "https://wikipedia.org/wiki/Savannah,_Georgia"}]}, {"year": "1962", "text": "French President <PERSON> calls for Algeria to be granted independence.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">French President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> calls for <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a> to be granted independence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">French President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> calls for <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a> to be granted independence.", "links": [{"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "1963", "text": "The European Court of Justice's ruling in Van Gend en Loos v Nederlandse Administratie der Belastingen establishes the principle of direct effect, one of the most important, if not the most important, decisions in the development of European Union law.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/European_Court_of_Justice\" title=\"European Court of Justice\">European Court of Justice</a>'s ruling in <i><a href=\"https://wikipedia.org/wiki/Van_Gend_en_Loos_v_Nederlandse_Administratie_der_Belastingen\" title=\"Van Gend en Loos v Nederlandse Administratie der Belastingen\">Van Gend en Loos v Nederlandse Administratie der Belastingen</a></i> establishes the principle of <a href=\"https://wikipedia.org/wiki/Direct_effect_of_European_Union_law\" title=\"Direct effect of European Union law\">direct effect</a>, one of the most important, if not the most important, decisions in the development of <a href=\"https://wikipedia.org/wiki/European_Union_law\" class=\"mw-redirect\" title=\"European Union law\">European Union law</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/European_Court_of_Justice\" title=\"European Court of Justice\">European Court of Justice</a>'s ruling in <i><a href=\"https://wikipedia.org/wiki/Van_Gend_en_Loos_v_Nederlandse_Administratie_der_Belastingen\" title=\"Van Gend en Loos v Nederlandse Administratie der Belastingen\">Van Gend en Loos v Nederlandse Administratie der Belastingen</a></i> establishes the principle of <a href=\"https://wikipedia.org/wiki/Direct_effect_of_European_Union_law\" title=\"Direct effect of European Union law\">direct effect</a>, one of the most important, if not the most important, decisions in the development of <a href=\"https://wikipedia.org/wiki/European_Union_law\" class=\"mw-redirect\" title=\"European Union law\">European Union law</a>.", "links": [{"title": "European Court of Justice", "link": "https://wikipedia.org/wiki/European_Court_of_Justice"}, {"title": "Van Gend en Loos v Nederlandse Administratie der Belastingen", "link": "https://wikipedia.org/wiki/<PERSON>_Gend_en_Loos_v_Nederlandse_Administratie_der_Belastingen"}, {"title": "Direct effect of European Union law", "link": "https://wikipedia.org/wiki/Direct_effect_of_European_Union_law"}, {"title": "European Union law", "link": "https://wikipedia.org/wiki/European_Union_law"}]}, {"year": "1967", "text": "Cultural Revolution: The Shanghai People's Commune is formally proclaimed, with <PERSON> and <PERSON> being appointed as its leaders.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Shanghai_People%27s_Commune\" class=\"mw-redirect\" title=\"Shanghai People's Commune\">Shanghai People's Commune</a> is formally proclaimed, with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> being appointed as its leaders.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Shanghai_People%27s_Commune\" class=\"mw-redirect\" title=\"Shanghai People's Commune\">Shanghai People's Commune</a> is formally proclaimed, with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> being appointed as its leaders.", "links": [{"title": "Cultural Revolution", "link": "https://wikipedia.org/wiki/Cultural_Revolution"}, {"title": "Shanghai People's Commune", "link": "https://wikipedia.org/wiki/Shanghai_People%27s_Commune"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "Astronauts land on the Moon in the Apollo 14 mission.", "html": "1971 - Astronauts land on the Moon in the <a href=\"https://wikipedia.org/wiki/Apollo_14\" title=\"Apollo 14\">Apollo 14</a> mission.", "no_year_html": "Astronauts land on the Moon in the <a href=\"https://wikipedia.org/wiki/Apollo_14\" title=\"Apollo 14\">Apollo 14</a> mission.", "links": [{"title": "Apollo 14", "link": "https://wikipedia.org/wiki/Apollo_14"}]}, {"year": "1975", "text": "Riots break out in Lima, Peru after the police forces go on strike the day before. The uprising (locally known as the Limazo) is bloodily suppressed by the military dictatorship.", "html": "1975 - Riots break out in <a href=\"https://wikipedia.org/wiki/Lima\" title=\"Lima\">Lima, Peru</a> after the police forces go on strike <a href=\"https://wikipedia.org/wiki/February_4\" title=\"February 4\">the day before</a>. The uprising (locally known as the <i><a href=\"https://wikipedia.org/wiki/Limazo\" title=\"Limazo\">Limazo</a></i>) is bloodily suppressed by the military dictatorship.", "no_year_html": "Riots break out in <a href=\"https://wikipedia.org/wiki/Lima\" title=\"Lima\">Lima, Peru</a> after the police forces go on strike <a href=\"https://wikipedia.org/wiki/February_4\" title=\"February 4\">the day before</a>. The uprising (locally known as the <i><a href=\"https://wikipedia.org/wiki/Limazo\" title=\"Limazo\">Lima<PERSON></a></i>) is bloodily suppressed by the military dictatorship.", "links": [{"title": "Lima", "link": "https://wikipedia.org/wiki/Lima"}, {"title": "February 4", "link": "https://wikipedia.org/wiki/February_4"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Limazo"}]}, {"year": "1981", "text": "Operation Soap: The Metropolitan Toronto Police Force raids four gay bathhouses in Toronto, Ontario, Canada, arresting just under 300, triggering mass protest and rallies.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Operation_Soap\" title=\"Operation Soap\">Operation Soap</a>: The <a href=\"https://wikipedia.org/wiki/Toronto_Police_Service\" title=\"Toronto Police Service\">Metropolitan Toronto Police Force</a> raids four gay bathhouses in <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a>, Ontario, Canada, arresting just under 300, triggering mass protest and rallies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Soap\" title=\"Operation Soap\">Operation Soap</a>: The <a href=\"https://wikipedia.org/wiki/Toronto_Police_Service\" title=\"Toronto Police Service\">Metropolitan Toronto Police Force</a> raids four gay bathhouses in <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a>, Ontario, Canada, arresting just under 300, triggering mass protest and rallies.", "links": [{"title": "Operation Soap", "link": "https://wikipedia.org/wiki/Operation_Soap"}, {"title": "Toronto Police Service", "link": "https://wikipedia.org/wiki/Toronto_Police_Service"}, {"title": "Toronto", "link": "https://wikipedia.org/wiki/Toronto"}]}, {"year": "1985", "text": "<PERSON><PERSON>, then the mayor of Rome, and <PERSON><PERSON><PERSON>, then the mayor of Carthage, meet in Tunis to sign a treaty of friendship officially ending the Third Punic War which lasted 2,131 years.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ugo_Vetere\" title=\"Ugo Vetere\"><PERSON><PERSON></a>, then the mayor of Rome, and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, then the mayor of <a href=\"https://wikipedia.org/wiki/Carthage\" title=\"Carthage\">Carthage</a>, meet in <a href=\"https://wikipedia.org/wiki/Tunis\" title=\"Tunis\">Tunis</a> to sign a treaty of friendship officially ending the <a href=\"https://wikipedia.org/wiki/Third_Punic_War\" title=\"Third Punic War\">Third Punic War</a> which lasted 2,131 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ugo_Vetere\" title=\"Ugo Vetere\"><PERSON><PERSON></a>, then the mayor of Rome, and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, then the mayor of <a href=\"https://wikipedia.org/wiki/Carthage\" title=\"Carthage\">Carthage</a>, meet in <a href=\"https://wikipedia.org/wiki/Tunis\" title=\"Tunis\">Tunis</a> to sign a treaty of friendship officially ending the <a href=\"https://wikipedia.org/wiki/Third_Punic_War\" title=\"Third Punic War\">Third Punic War</a> which lasted 2,131 years.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ugo_<PERSON>etere"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bi"}, {"title": "Carthage", "link": "https://wikipedia.org/wiki/Carthage"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nis"}, {"title": "Third Punic War", "link": "https://wikipedia.org/wiki/Third_Punic_War"}]}, {"year": "1988", "text": "<PERSON> is indicted on drug smuggling and money laundering charges.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is indicted on <a href=\"https://wikipedia.org/wiki/Illegal_drug_trade\" title=\"Illegal drug trade\">drug smuggling</a> and <a href=\"https://wikipedia.org/wiki/Money_laundering\" title=\"Money laundering\">money laundering</a> charges.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is indicted on <a href=\"https://wikipedia.org/wiki/Illegal_drug_trade\" title=\"Illegal drug trade\">drug smuggling</a> and <a href=\"https://wikipedia.org/wiki/Money_laundering\" title=\"Money laundering\">money laundering</a> charges.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Illegal drug trade", "link": "https://wikipedia.org/wiki/Illegal_drug_trade"}, {"title": "Money laundering", "link": "https://wikipedia.org/wiki/Money_laundering"}]}, {"year": "1994", "text": "<PERSON> is convicted of the 1963 murder of civil rights leader <PERSON><PERSON><PERSON>.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of the 1963 murder of <a href=\"https://wikipedia.org/wiki/Civil_and_political_rights\" title=\"Civil and political rights\">civil rights</a> leader <a href=\"https://wikipedia.org/wiki/Medgar_Evers\" title=\"Medgar <PERSON>s\">Medgar <PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of the 1963 murder of <a href=\"https://wikipedia.org/wiki/Civil_and_political_rights\" title=\"Civil and political rights\">civil rights</a> leader <a href=\"https://wikipedia.org/wiki/Medgar_Ever<PERSON>\" title=\"Medgar <PERSON>s\">Medgar <PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Civil and political rights", "link": "https://wikipedia.org/wiki/Civil_and_political_rights"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Medgar_<PERSON>s"}]}, {"year": "1994", "text": "Markale massacres, more than 60 people are killed and some 200 wounded as a mortar shell explodes in a downtown marketplace in Sarajevo.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Markale_massacres\" title=\"Markale massacres\">Markale massacres</a>, more than 60 people are killed and some 200 wounded as a mortar shell explodes in a downtown marketplace in <a href=\"https://wikipedia.org/wiki/Sarajevo\" title=\"Sarajevo\">Sarajevo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Markale_massacres\" title=\"Markale massacres\">Markale massacres</a>, more than 60 people are killed and some 200 wounded as a mortar shell explodes in a downtown marketplace in <a href=\"https://wikipedia.org/wiki/Sarajevo\" title=\"Sarajevo\">Sarajevo</a>.", "links": [{"title": "Markale massacres", "link": "https://wikipedia.org/wiki/Markale_massacres"}, {"title": "Sarajevo", "link": "https://wikipedia.org/wiki/Sarajevo"}]}, {"year": "1997", "text": "The so-called Big Three banks in Switzerland announce the creation of a $71 million fund to aid Holocaust survivors and their families.", "html": "1997 - The so-called <a href=\"https://wikipedia.org/wiki/Banking_in_Switzerland#Major_banks\" title=\"Banking in Switzerland\">Big Three banks</a> in Switzerland announce the creation of a $71 million fund to aid <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">Holocaust</a> survivors and their families.", "no_year_html": "The so-called <a href=\"https://wikipedia.org/wiki/Banking_in_Switzerland#Major_banks\" title=\"Banking in Switzerland\">Big Three banks</a> in Switzerland announce the creation of a $71 million fund to aid <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">Holocaust</a> survivors and their families.", "links": [{"title": "Banking in Switzerland", "link": "https://wikipedia.org/wiki/Banking_in_Switzerland#Major_banks"}, {"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}]}, {"year": "2000", "text": "Russian forces massacre at least 60 civilians in the Novye Aldi suburb of Grozny, Chechnya.", "html": "2000 - Russian forces <a href=\"https://wikipedia.org/wiki/Novye_Aldi_massacre\" title=\"Novye Aldi massacre\">massacre</a> at least 60 civilians in the Novye Aldi suburb of <a href=\"https://wikipedia.org/wiki/Grozny\" title=\"Grozny\">Grozny</a>, Chechnya.", "no_year_html": "Russian forces <a href=\"https://wikipedia.org/wiki/Novye_Aldi_massacre\" title=\"Novye Aldi massacre\">massacre</a> at least 60 civilians in the Novye Aldi suburb of <a href=\"https://wikipedia.org/wiki/Grozny\" title=\"Grozny\">Grozny</a>, Chechnya.", "links": [{"title": "Novye Aldi massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Aldi_massacre"}, {"title": "Grozny", "link": "https://wikipedia.org/wiki/Grozny"}]}, {"year": "2004", "text": "Rebels from the Revolutionary Artibonite Resistance Front capture the city of Gonaïves, starting the 2004 Haiti rebellion.", "html": "2004 - Rebels from the <a href=\"https://wikipedia.org/wiki/National_Revolutionary_Front_for_the_Liberation_and_Reconstruction_of_Haiti\" title=\"National Revolutionary Front for the Liberation and Reconstruction of Haiti\">Revolutionary Artibonite Resistance Front</a> capture the city of <a href=\"https://wikipedia.org/wiki/Gona%C3%AFves\" title=\"Gonaïves\">Gonaïves</a>, starting the <a href=\"https://wikipedia.org/wiki/2004_Haitian_coup_d%27%C3%A9tat\" title=\"2004 Haitian coup d'état\">2004 Haiti rebellion</a>.", "no_year_html": "Rebels from the <a href=\"https://wikipedia.org/wiki/National_Revolutionary_Front_for_the_Liberation_and_Reconstruction_of_Haiti\" title=\"National Revolutionary Front for the Liberation and Reconstruction of Haiti\">Revolutionary Artibonite Resistance Front</a> capture the city of <a href=\"https://wikipedia.org/wiki/Gona%C3%AFves\" title=\"Gonaïves\">Gonaïves</a>, starting the <a href=\"https://wikipedia.org/wiki/2004_Haitian_coup_d%27%C3%A9tat\" title=\"2004 Haitian coup d'état\">2004 Haiti rebellion</a>.", "links": [{"title": "National Revolutionary Front for the Liberation and Reconstruction of Haiti", "link": "https://wikipedia.org/wiki/National_Revolutionary_Front_for_the_Liberation_and_Reconstruction_of_Haiti"}, {"title": "Gonaïves", "link": "https://wikipedia.org/wiki/Gona%C3%AFves"}, {"title": "2004 Haitian coup d'état", "link": "https://wikipedia.org/wiki/2004_Haitian_coup_d%27%C3%A9tat"}]}, {"year": "2008", "text": "A major tornado outbreak across the Southern United States kills 57.", "html": "2008 - A <a href=\"https://wikipedia.org/wiki/2008_Super_Tuesday_tornado_outbreak\" title=\"2008 Super Tuesday tornado outbreak\">major tornado outbreak</a> across the <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">Southern United States</a> kills 57.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2008_Super_Tuesday_tornado_outbreak\" title=\"2008 Super Tuesday tornado outbreak\">major tornado outbreak</a> across the <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">Southern United States</a> kills 57.", "links": [{"title": "2008 Super Tuesday tornado outbreak", "link": "https://wikipedia.org/wiki/2008_Super_Tuesday_tornado_outbreak"}, {"title": "Southern United States", "link": "https://wikipedia.org/wiki/Southern_United_States"}]}, {"year": "2016", "text": "New Zealand politician <PERSON> is hit by a flung rubber dildo in a Waitangi Day protest.", "html": "2016 - New Zealand politician <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Waitangi_dildo_incident\" title=\"Waitangi dildo incident\">hit by a flung rubber dildo</a> in a <a href=\"https://wikipedia.org/wiki/Waitangi_Day\" title=\"Waitangi Day\">Waitangi Day</a> protest.", "no_year_html": "New Zealand politician <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Waitangi_dildo_incident\" title=\"Waitangi dildo incident\">hit by a flung rubber dildo</a> in a <a href=\"https://wikipedia.org/wiki/Waitangi_Day\" title=\"Waitangi Day\">Waitangi Day</a> protest.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Waitangi dildo incident", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>i_dildo_incident"}, {"title": "Waitangi Day", "link": "https://wikipedia.org/wiki/Waitangi_Day"}]}, {"year": "2019", "text": "<PERSON> <PERSON> becomes the first Pope in history to visit and perform papal mass in the Arabian Peninsula during his visit to Abu Dhabi.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> becomes the first Pope in history to visit and perform <a href=\"https://wikipedia.org/wiki/Papal_Mass\" title=\"Papal Mass\">papal mass</a> in the <a href=\"https://wikipedia.org/wiki/Arabian_Peninsula\" title=\"Arabian Peninsula\">Arabian Peninsula</a> during his visit to <a href=\"https://wikipedia.org/wiki/Abu_Dhabi\" title=\"Abu Dhabi\">Abu Dhabi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> becomes the first Pope in history to visit and perform <a href=\"https://wikipedia.org/wiki/Papal_Mass\" title=\"Papal Mass\">papal mass</a> in the <a href=\"https://wikipedia.org/wiki/Arabian_Peninsula\" title=\"Arabian Peninsula\">Arabian Peninsula</a> during his visit to <a href=\"https://wikipedia.org/wiki/Abu_Dhabi\" title=\"Abu Dhabi\">Abu Dhabi</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Papal Mass", "link": "https://wikipedia.org/wiki/Papal_Mass"}, {"title": "Arabian Peninsula", "link": "https://wikipedia.org/wiki/Arabian_Peninsula"}, {"title": "Abu Dhabi", "link": "https://wikipedia.org/wiki/Abu_Dhabi"}]}, {"year": "2020", "text": "United States President <PERSON> is acquitted by the United States Senate in his first impeachment trial.", "html": "2020 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is acquitted by the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> in his <a href=\"https://wikipedia.org/wiki/First_impeachment_trial_of_<PERSON>_<PERSON>\" title=\"First impeachment trial of <PERSON>\">first impeachment trial</a>.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is acquitted by the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> in his <a href=\"https://wikipedia.org/wiki/First_impeachment_trial_of_<PERSON>_<PERSON>\" title=\"First impeachment trial of <PERSON>\">first impeachment trial</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "First impeachment trial of <PERSON>", "link": "https://wikipedia.org/wiki/First_impeachment_trial_of_<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "Pegasus Airlines Flight 2193 overshoots the runway at Sabiha Gökçen International Airport and crashes, killing three people and injuring 179.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Pegasus_Airlines_Flight_2193\" title=\"Pegasus Airlines Flight 2193\">Pegasus Airlines Flight 2193</a> overshoots the runway at <a href=\"https://wikipedia.org/wiki/Sabiha_G%C3%B6k%C3%A7en_International_Airport\" title=\"Sabiha Gökçen International Airport\">Sabiha Gökçen International Airport</a> and crashes, killing three people and injuring 179.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pegasus_Airlines_Flight_2193\" title=\"Pegasus Airlines Flight 2193\">Pegasus Airlines Flight 2193</a> overshoots the runway at <a href=\"https://wikipedia.org/wiki/Sabiha_G%C3%B6k%C3%A7en_International_Airport\" title=\"Sabiha Gökçen International Airport\">Sabiha Gökçen International Airport</a> and crashes, killing three people and injuring 179.", "links": [{"title": "Pegasus Airlines Flight 2193", "link": "https://wikipedia.org/wiki/Pegasus_Airlines_Flight_2193"}, {"title": "Sabiha Gökçen International Airport", "link": "https://wikipedia.org/wiki/Sabiha_G%C3%B6k%C3%A7en_International_Airport"}]}, {"year": "2021", "text": "Police riot in Mexico City as they try to break up a demonstration by cyclists who were protesting after a bus ran over a bicyclist. Eleven police officers are arrested.", "html": "2021 - Police riot in <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City</a> as they try to break up a demonstration by cyclists who were protesting after a bus ran over a bicyclist. Eleven police officers are arrested.", "no_year_html": "Police riot in <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City</a> as they try to break up a demonstration by cyclists who were protesting after a bus ran over a bicyclist. Eleven police officers are arrested.", "links": [{"title": "Mexico City", "link": "https://wikipedia.org/wiki/Mexico_City"}]}], "Births": [{"year": "976", "text": "<PERSON><PERSON>, emperor of Japan (d. 1017)", "html": "976 - <a href=\"https://wikipedia.org/wiki/Emperor_Sanj%C5%8D\" title=\"Emperor Sanjō\"><PERSON><PERSON></a>, emperor of Japan (d. 1017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Sanj%C5%8D\" title=\"Emperor Sanjō\"><PERSON><PERSON></a>, emperor of Japan (d. 1017)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Sanj%C5%8D"}]}, {"year": "1321", "text": "<PERSON> <PERSON>, marquess of Montferrat (d. 1372)", "html": "1321 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_of_Montferrat\" title=\"<PERSON>, Marquis of Montferrat\"><PERSON> II</a>, marquess of Montferrat (d. 1372)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_of_Montferrat\" title=\"<PERSON>, Marquis of Montferrat\"><PERSON> II</a>, marquess of Montferrat (d. 1372)", "links": [{"title": "<PERSON>, Marquis of Montferrat", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_of_Montferrat"}]}, {"year": "1438", "text": "<PERSON>, duke of Savoy (d. 1497)", "html": "1438 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON></a>, duke of Savoy (d. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON></a>, duke of Savoy (d. 1497)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy"}]}, {"year": "1505", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss statesman and historian (d. 1572)", "html": "1505 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss statesman and historian (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss statesman and historian (d. 1572)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON> Châlon, prince of Orange (d. 1544)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_of_Chalon\" title=\"<PERSON> of Chalon\"><PERSON> of Châlon</a>, prince of Orange (d. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_of_Chalon\" title=\"<PERSON> of Chalon\"><PERSON> of Châlon</a>, prince of Orange (d. 1544)", "links": [{"title": "<PERSON> of Chalon", "link": "https://wikipedia.org/wiki/Ren%C3%A9_of_Chalon"}]}, {"year": "1525", "text": "<PERSON><PERSON>, Croatian Catholic cardinal (d. 1587)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1kovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian Catholic cardinal (d. 1587)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1kovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian Catholic cardinal (d. 1587)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>raj_Dra%C5%A1kovi%C4%87"}]}, {"year": "1533", "text": "<PERSON>, Croatian-Hungarian nobleman and diplomat (d. 1589)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-Hungarian nobleman and diplomat (d. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-Hungarian nobleman and diplomat (d. 1589)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1534", "text": "<PERSON>, Italian soldier, composer, and critic (d. 1612)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier, composer, and critic (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier, composer, and critic (d. 1612)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON><PERSON>"}]}, {"year": "1589", "text": "<PERSON><PERSON><PERSON>, Spanish poet and educator (d. 1669)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/Este<PERSON>_<PERSON>_<PERSON>\" title=\"Esteban Manuel <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Spanish poet and educator (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Este<PERSON>_<PERSON>_<PERSON>\" title=\"Esteban Manuel <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Spanish poet and educator (d. 1669)", "links": [{"title": "Esteban Manuel <PERSON> Villegas", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1594", "text": "<PERSON><PERSON><PERSON>, Italian violinist and composer (d. 1663)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_Marini\" title=\"<PERSON><PERSON><PERSON> Marini\"><PERSON><PERSON><PERSON></a>, Italian violinist and composer (d. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian violinist and composer (d. 1663)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Biagio_Marini"}]}, {"year": "1608", "text": "<PERSON><PERSON>, German mathematician and physicist (d. 1666)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>t\"><PERSON><PERSON></a>, German mathematician and physicist (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mathematician and physicist (d. 1666)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1626", "text": "<PERSON>, marquise <PERSON>, French author (d. 1696)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_marquise_de_<PERSON>%C3%A9vign%C3%A9\" title=\"<PERSON>, marquise <PERSON>\"><PERSON>, marquise <PERSON></a>, French author (d. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_marquise_de_<PERSON>%C3%A9vign%C3%A9\" title=\"<PERSON>, marquise <PERSON>\"><PERSON>, marquise <PERSON></a>, French author (d. 1696)", "links": [{"title": "<PERSON><PERSON><PERSON>, marquise <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>,_marquise_de_S%C3%A9vign%C3%A9"}]}, {"year": "1650", "text": "<PERSON>, French general (d. 1708)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1708)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1703", "text": "<PERSON>, Irish-American minister (d. 1764)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American minister (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American minister (d. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nent"}]}, {"year": "1723", "text": "<PERSON>, Scottish-American minister and academic (d. 1794)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American minister and academic (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American minister and academic (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1725", "text": "<PERSON>, American lawyer and politician (d. 1783)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American lawyer and politician (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American lawyer and politician (d. 1783)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1748", "text": "<PERSON>, German composer and conductor (d. 1798)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, English lieutenant and politician, Prime Minister of the United Kingdom (d. 1850)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1795", "text": "<PERSON>, Austrian mineralogist, geologist, and physicist (d. 1871)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mineralogist, geologist, and physicist (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mineralogist, geologist, and physicist (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, Finnish poet and hymn-writer (d. 1877)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish poet and hymn-writer (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish poet and hymn-writer (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, German painter and poet (d. 1885)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and poet (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and poet (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carl_<PERSON>g"}]}, {"year": "1810", "text": "<PERSON>, Norwegian violinist and composer (d. 1880)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bull\" title=\"Ole Bull\"><PERSON></a>, Norwegian violinist and composer (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ole_Bull\" title=\"Ole Bull\"><PERSON></a>, Norwegian violinist and composer (d. 1880)", "links": [{"title": "Ole Bull", "link": "https://wikipedia.org/wiki/Ole_Bull"}]}, {"year": "1827", "text": "<PERSON>, Irish-Australian activist and politician (d. 1889)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian activist and politician (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian activist and politician (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, American evangelist and publisher, founded Moody Church, Moody Bible Institute, and Moody Publishers (d. 1899)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and publisher, founded <a href=\"https://wikipedia.org/wiki/Moody_Church\" title=\"Moody Church\">Moody Church</a>, <a href=\"https://wikipedia.org/wiki/Moody_Bible_Institute\" title=\"Moody Bible Institute\">Moody Bible Institute</a>, and <a href=\"https://wikipedia.org/wiki/Moody_Publishers\" class=\"mw-redirect\" title=\"Moody Publishers\">Moody Publishers</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and publisher, founded <a href=\"https://wikipedia.org/wiki/Moody_Church\" title=\"Moody Church\">Moody Church</a>, <a href=\"https://wikipedia.org/wiki/Moody_Bible_Institute\" title=\"Moody Bible Institute\">Moody Bible Institute</a>, and <a href=\"https://wikipedia.org/wiki/Moody_Publishers\" class=\"mw-redirect\" title=\"Moody Publishers\">Moody Publishers</a> (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Moody Church", "link": "https://wikipedia.org/wiki/Moody_Church"}, {"title": "Moody Bible Institute", "link": "https://wikipedia.org/wiki/Moody_Bible_Institute"}, {"title": "Moody Publishers", "link": "https://wikipedia.org/wiki/Moody_Publishers"}]}, {"year": "1840", "text": "<PERSON>, Scottish businessman, co-founded Dunlop Rubber (d. 1921)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman, co-founded <a href=\"https://wikipedia.org/wiki/Dun<PERSON>_Rubber\" title=\"Dunlop Rubber\">Dunlop Rubber</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman, co-founded <a href=\"https://wikipedia.org/wiki/Dun<PERSON>_Rubber\" title=\"Dunlop Rubber\">Dunlop Rubber</a> (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ber"}]}, {"year": "1840", "text": "<PERSON><PERSON>, American engineer, invented the Maxim gun (d. 1916)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Maxim\" title=\"<PERSON>ram Maxim\"><PERSON><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Maxim_gun\" title=\"Maxim gun\">Maxim gun</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Hiram Maxim\"><PERSON><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Maxim_gun\" title=\"Maxim gun\">Maxim gun</a> (d. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Maxim gun", "link": "https://wikipedia.org/wiki/Maxim_gun"}]}, {"year": "1847", "text": "<PERSON>, Estonian missionary and engraver (d. 1903)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian missionary and engraver (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian missionary and engraver (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON><PERSON>, French author and critic (d. 1907)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author and critic (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author and critic (d. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Chilean lieutenant (d. 1882)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lieutenant (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lieutenant (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON>, Japanese field marshal and politician, 9th Prime Minister of Japan (d. 1919)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Masatake\" title=\"Terauchi Masatake\"><PERSON><PERSON><PERSON></a>, Japanese field marshal and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Masatake\" title=\"Terauchi Masatake\"><PERSON><PERSON><PERSON></a>, Japanese field marshal and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ake"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON>, Irish politician, 3rd and last Governor-General of the Irish Free State (d. 1963)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ua_Buachalla\" title=\"<PERSON><PERSON>all Ua Buachalla\"><PERSON><PERSON><PERSON></a>, Irish politician, 3rd and last <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Irish_Free_State\" title=\"Governor-General of the Irish Free State\">Governor-General of the Irish Free State</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ua_Buachalla\" title=\"<PERSON><PERSON>all Ua Buachalla\"><PERSON><PERSON><PERSON> Bua<PERSON></a>, Irish politician, 3rd and last <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Irish_Free_State\" title=\"Governor-General of the Irish Free State\">Governor-General of the Irish Free State</a> (d. 1963)", "links": [{"title": "Domhnall U<PERSON> Buachalla", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ua<PERSON>la"}, {"title": "Governor-General of the Irish Free State", "link": "https://wikipedia.org/wiki/Governor-General_of_the_Irish_Free_State"}]}, {"year": "1870", "text": "<PERSON>, British painter and book illustrator (d. 1938)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON></a>, British painter and book illustrator (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON></a>, British painter and book illustrator (d. 1938)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Canadian ice hockey player (d. 1931)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, French engineer and businessman, founded Citroën (d. 1935)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Citro%C3%ABn\" title=\"<PERSON>\"><PERSON></a>, French engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Citro%C3%ABn\" title=\"Citroën\">Citroën</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Citro%C3%ABn\" title=\"<PERSON>\"><PERSON></a>, French engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Citro%C3%ABn\" title=\"Citroën\">Citroën</a> (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Citro%C3%ABn"}, {"title": "Citroën", "link": "https://wikipedia.org/wiki/Citro%C3%ABn"}]}, {"year": "1880", "text": "<PERSON>, French pilot and engineer (d. 1973)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pilot and engineer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pilot and engineer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, English cricketer and footballer (d. 1962)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer and footballer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer and footballer (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1889", "text": "<PERSON>, English cricketer (d. 1962)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, Turkish officer and politician (d. 1950)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish officer and politician (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish officer and politician (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>er"}]}, {"year": "1891", "text": "<PERSON><PERSON>, Italian rower (d. 1976)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Petronio\" title=\"Renato Petronio\"><PERSON><PERSON></a>, Italian rower (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Petronio\" title=\"Renato Petronio\"><PERSON><PERSON></a>, Italian rower (d. 1976)", "links": [{"title": "<PERSON>ato Petronio", "link": "https://wikipedia.org/wiki/Renato_Petronio"}]}, {"year": "1892", "text": "<PERSON>, American tennis player (d. 1979)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Dutch businessman and politician, 3rd Secretary General of NATO (d. 1979)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman and politician, 3rd <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman and politician, 3rd <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary General of NATO", "link": "https://wikipedia.org/wiki/Secretary_General_of_NATO"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American soldier, politician, and diplomat, 5th United States Ambassador to the United Nations (d. 1965)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> II</a>, American soldier, politician, and diplomat, 5th <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_States_to_the_United_Nations\" title=\"List of ambassadors of the United States to the United Nations\">United States Ambassador to the United Nations</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> II</a>, American soldier, politician, and diplomat, 5th <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_States_to_the_United_Nations\" title=\"List of ambassadors of the United States to the United Nations\">United States Ambassador to the United Nations</a> (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "List of ambassadors of the United States to the United Nations", "link": "https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_States_to_the_United_Nations"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Japanese diplomat, ambassador to the United Nations (d. 1994)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Matsudaira\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese diplomat, ambassador to the United Nations (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>sudai<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese diplomat, ambassador to the United Nations (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ra"}]}, {"year": "1903", "text": "<PERSON>, American businesswoman and philanthropist (d. 1975)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and philanthropist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and philanthropist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American actor (d. 1988)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Norwegian politician (d. 2007)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Birgit_Dalland\" title=\"Birgit Dalland\"><PERSON><PERSON><PERSON></a>, Norwegian politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Birgit_Dalland\" title=\"Birgit Dalland\"><PERSON><PERSON><PERSON></a>, Norwegian politician (d. 2007)", "links": [{"title": "Birgit Dalland", "link": "https://wikipedia.org/wiki/Birgit_Dalland"}]}, {"year": "1907", "text": "<PERSON>, French politician, Prime Minister of France (d. 2000)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1908", "text": "<PERSON>, Dutch swimmer and diver (d. 1948)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer and diver (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer and diver (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Welsh-American actress (d. 1932)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>twistle\" title=\"<PERSON><PERSON> Entwistle\"><PERSON><PERSON></a>, Welsh-American actress (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>twistle\" title=\"<PERSON><PERSON> Entwistle\"><PERSON><PERSON></a>, Welsh-American actress (d. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peg_Entwistle"}]}, {"year": "1908", "text": "<PERSON><PERSON>, German criminal (d. 1939)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German criminal (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German criminal (d. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish violinist and composer (d. 1969)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Gra%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish violinist and composer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gra%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish violinist and composer (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gra%C5%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, French-Canadian biologist and academic (d. 2007)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian biologist and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian biologist and academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Argentinian footballer (d. 2010)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Swedish tenor (d. 1960)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bj%C3%B6rling\" title=\"<PERSON><PERSON>jörling\"><PERSON><PERSON></a>, Swedish tenor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bj%C3%B6rling\" title=\"<PERSON><PERSON>jörling\"><PERSON><PERSON></a>, Swedish tenor (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jussi_Bj%C3%B6rling"}]}, {"year": "1914", "text": "<PERSON>, American novelist, short story writer, and essayist (d. 1997)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English physiologist, biophysicist, and academic, Nobel Prize laureate (d. 1998)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist, biophysicist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist, biophysicist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1915", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 1990)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1917", "text": "<PERSON>, American academic and president of Pace University (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and president of Pace University (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and president of Pace University (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Japanese actress (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American actor (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Red_Buttons\" title=\"Red Buttons\"><PERSON></a>, American actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Buttons\" title=\"Red Buttons\"><PERSON></a>, American actor (d. 2006)", "links": [{"title": "<PERSON> Buttons", "link": "https://wikipedia.org/wiki/Red_Buttons"}]}, {"year": "1919", "text": "<PERSON>, American actor (d. 1973)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Greek economist and politician, Prime Minister of Greece (d. 1996)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek economist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek economist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1921", "text": "<PERSON>, German-born English production designer and art director (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born English production designer and art director (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Adam\"><PERSON></a>, German-born English production designer and art director (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American country music singer-songwriter and guitarist (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American physician and academic (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cardinal (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Durai<PERSON><PERSON>_<PERSON>\" title=\"Durai<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cardinal (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Durai<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Duraisa<PERSON>\">Durai<PERSON><PERSON></a>, Indian cardinal (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American pianist and composer (d. 2000)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(song_composer)\" title=\"<PERSON> (song composer)\"><PERSON></a>, American pianist and composer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(song_composer)\" title=\"<PERSON> (song composer)\"><PERSON></a>, American pianist and composer (d. 2000)", "links": [{"title": "<PERSON> (song composer)", "link": "https://wikipedia.org/wiki/<PERSON>(song_composer)"}]}, {"year": "1927", "text": "<PERSON>, Dutch captain and pilot (d. 1977)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Z<PERSON>\"><PERSON></a>, Dutch captain and pilot (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Z<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch captain and pilot (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Romanian editor, literary critic and writer (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Hristu_C%C3%A2ndroveanu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian editor, literary critic and writer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hristu_C%C3%A2ndroveanu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian editor, literary critic and writer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hristu_C%C3%A2ndroveanu"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Swedish author, actor, and director (d. 1985)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author, actor, and director (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author, actor, and director (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American priest, sociologist, and author (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, sociologist, and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, sociologist, and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON> <PERSON><PERSON>, Israeli-American historian and political scientist (d. 1997)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Israeli-American historian and political scientist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> V<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Israeli-American historian and political scientist (d. 1997)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_Vatikiotis"}]}, {"year": "1929", "text": "<PERSON>, American session drummer (d. 2019)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American session drummer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American session drummer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, French pianist and composer (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Luc Ferrari\"><PERSON></a>, French pianist and composer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Luc Ferrari\"><PERSON></a>, French pianist and composer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Austrian politician, 19th Chancellor of Austria (d. 2008)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, 19th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, 19th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Austria", "link": "https://wikipedia.org/wiki/Chancellor_of_Austria"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Italian footballer and manager (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Finnish director and screenwriter (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish director and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish director and screenwriter (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B6rn_<PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON> <PERSON><PERSON>, English author, poet, and critic (d. 1973)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author, poet, and critic (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author, poet, and critic (d. 1973)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> Johnson", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American baseball player (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian ice hockey player, coach, and sportscaster", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hockey)\" class=\"mw-redirect\" title=\"<PERSON> (hockey)\"><PERSON></a>, Canadian ice hockey player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hockey)\" class=\"mw-redirect\" title=\"<PERSON> (hockey)\"><PERSON></a>, Canadian ice hockey player, coach, and sportscaster", "links": [{"title": "<PERSON> (hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hockey)"}]}, {"year": "1935", "text": "<PERSON>, Scottish singer-songwriter and guitarist (d. 1982)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist (d. 1982)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1935", "text": "<PERSON>, South African military commander (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African military commander (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African military commander (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian poet and academic (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/K._<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"K<PERSON> <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Indian poet and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K._S<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"K. S<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Indian poet and academic (d. 2020)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actor and singer (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2022)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Belgian runner", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Estonian-American astronomer and mathematician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-American astronomer and mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-American astronomer and mathematician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al<PERSON>_<PERSON>re"}]}, {"year": "1937", "text": "<PERSON>, Chinese computer scientist and academic (d. 2006)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, Chinese computer scientist and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, Chinese computer scientist and academic (d. 2006)", "links": [{"title": "<PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)"}]}, {"year": "1938", "text": "<PERSON>, Colombian lawyer, jurist, and diplomat", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer, jurist, and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer, jurist, and diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English cricketer (d. 2005)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American financial journalist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financial journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financial journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON> <PERSON><PERSON>, Swiss painter, sculptor, and set designer (d. 2014)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Swiss painter, sculptor, and set designer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Swiss painter, sculptor, and set designer (d. 2014)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American wrestler (d. 2006)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (d. 2006)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)"}]}, {"year": "1941", "text": "<PERSON>, American actor, producer, and screenwriter (d. 2010)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American country music singer (d. 2007)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cargill\"><PERSON><PERSON></a>, American country music singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cargill\"><PERSON><PERSON></a>, American country music singer (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gill"}]}, {"year": "1941", "text": "<PERSON>, American actor and playwright", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American soul singer-songwriter and pianist (d. 2023)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Strong\"><PERSON></a>, American soul singer-songwriter and pianist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Strong\"><PERSON></a>, American soul singer-songwriter and pianist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Swiss engineer and politician, 85th President of the Swiss Confederation", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss engineer and politician, 85th <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_the_Swiss_Confederation\" title=\"List of presidents of the Swiss Confederation\">President of the Swiss Confederation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss engineer and politician, 85th <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_the_Swiss_Confederation\" title=\"List of presidents of the Swiss Confederation\">President of the Swiss Confederation</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_presidents_of_the_Swiss_Confederation"}]}, {"year": "1942", "text": "<PERSON>, American football player, sportscaster, and businessman", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American engineer and businessman, founded Atari, Inc.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Atari,_Inc.\" title=\"Atari, Inc.\">Atari, Inc.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Atari,_Inc.\" title=\"Atari, Inc.\">Atari, Inc.</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Atari, Inc.", "link": "https://wikipedia.org/wiki/Atari,_Inc."}]}, {"year": "1943", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American football player and sportscaster", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Czech and Slovak footballer and manager", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Du%C5%<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech and Slovak footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du%C5%<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech and Slovak footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Du%C5%<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON> <PERSON><PERSON>, American guitarist and songwriter (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American guitarist and songwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American guitarist and songwriter (d. 2019)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian journalist, author, and illustrator (d. 1988)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Henfil\" title=\"Henfil\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian journalist, author, and illustrator (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henfil\" title=\"Henfil\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian journalist, author, and illustrator (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Henfil"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 51st <PERSON><PERSON><PERSON><PERSON> (d. 1971)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 51st <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 51st <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1945", "text": "<PERSON>, English lawyer and politician, Minister of Agriculture, Fisheries and Food", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture,_Fisheries_and_Food\" title=\"Minister of Agriculture, Fisheries and Food\">Minister of Agriculture, Fisheries and Food</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture,_Fisheries_and_Food\" title=\"Minister of Agriculture, Fisheries and Food\">Minister of Agriculture, Fisheries and Food</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Agriculture, Fisheries and Food", "link": "https://wikipedia.org/wiki/Minister_of_Agriculture,_Fisheries_and_Food"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Israeli journalist and author (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli journalist and author (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American engineer and astronaut (d. 2023)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>leave\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ve\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._Cleave"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Italian politician, Italian Minister of Justice", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian politician, <a href=\"https://wikipedia.org/wiki/Italian_Minister_of_Justice\" class=\"mw-redirect\" title=\"Italian Minister of Justice\">Italian Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian politician, <a href=\"https://wikipedia.org/wiki/Italian_Minister_of_Justice\" class=\"mw-redirect\" title=\"Italian Minister of Justice\">Italian Minister of Justice</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Italian Minister of Justice", "link": "https://wikipedia.org/wiki/Italian_Minister_of_Justice"}]}, {"year": "1947", "text": "<PERSON>, American race car driver and sportscaster", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swedish footballer and manager (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Sven-G%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish footballer and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sven-G%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish footballer and manager (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sven-G%C3%B<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, British-American actor and director", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, American director and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American director and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English actor (d. 2023)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, German politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Mongolian cosmonaut and academic (d. 2021)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Maidarjavyn_Ganzorig\" title=\"Maidarjavyn Ganzorig\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian cosmonaut and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maidarjavyn_Ganzorig\" title=\"Maidarjavyn Ganzorig\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian cosmonaut and academic (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maidarja<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Canadian educator and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A8res\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A8res\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian educator and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y<PERSON>_Valli%C3%A8res"}]}, {"year": "1950", "text": "<PERSON>, American actor and singer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1950", "text": "<PERSON>, Mexican footballer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Mordovian engineer and politician, 1st Head of the Republic of Mordovia", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mordovian engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/Head_of_the_Republic_of_Mordovia\" title=\"Head of the Republic of Mordovia\">Head of the Republic of Mordovia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mordovian engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/Head_of_the_Republic_of_Mordovia\" title=\"Head of the Republic of Mordovia\">Head of the Republic of Mordovia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Head of the Republic of Mordovia", "link": "https://wikipedia.org/wiki/Head_of_the_Republic_of_Mordovia"}]}, {"year": "1952", "text": "<PERSON>, French singer-songwriter and producer (d. 1986)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and producer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and producer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Ukrainian-Russian geographer, economist, and academic", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian geographer, economist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian geographer, economist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Filipino singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American basketball player and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Paraguayan footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1953)\" title=\"<PERSON> (footballer, born 1953)\"><PERSON></a>, Paraguayan footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1953)\" title=\"<PERSON> (footballer, born 1953)\"><PERSON></a>, Paraguayan footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1953)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1953)"}]}, {"year": "1954", "text": "<PERSON>, American drummer and songwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian journalist and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_author)\" title=\"<PERSON> (Australian author)\"><PERSON></a>, Australian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_author)\" title=\"<PERSON> (Australian author)\"><PERSON></a>, Australian journalist and author", "links": [{"title": "<PERSON> (Australian author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_author)"}]}, {"year": "1955", "text": "<PERSON>, American baseball player and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1956", "text": "<PERSON>, American drummer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Mexican race car driver", "html": "1956 - <a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_Re<PERSON>que\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_Re<PERSON>que\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_Rebaque"}]}, {"year": "1956", "text": "<PERSON>, American author and illustrator", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Japanese actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mao Daichi\"><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mao Daichi\"><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Estonian hammer thrower and politician (d. 2021)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>ri_<PERSON>m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian hammer thrower and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>ri_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian hammer thrower and politician (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCri_Tamm"}]}, {"year": "1959", "text": "<PERSON>, Canadian-American lawyer and politician, 47th Governor of Michigan", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American lawyer and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American lawyer and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Michigan", "link": "https://wikipedia.org/wiki/Governor_of_Michigan"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Greek soprano and musicologist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek soprano and musicologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek soprano and musicologist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian businesswoman and politician, 6th Mayor of Mississauga", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Mississauga\" class=\"mw-redirect\" title=\"List of mayors of Mississauga\">Mayor of Mississauga</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Mississauga\" class=\"mw-redirect\" title=\"List of mayors of Mississauga\">Mayor of Mississauga</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Mississauga", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Mississauga"}]}, {"year": "1960", "text": "<PERSON><PERSON>, English footballer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Greek footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actress, screenwriter, producer and director", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, screenwriter, producer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, screenwriter, producer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American film director and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Korean poet, pastor, historical theologian", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-moo\" title=\"<PERSON> Se<PERSON>-moo\"><PERSON>-moo</a>, Korean poet, pastor, historical theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-moo\" title=\"<PERSON> Se<PERSON>-moo\"><PERSON>-moo</a>, Korean poet, pastor, historical theologian", "links": [{"title": "<PERSON>oo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-moo"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter, bass player, and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Algerian-French tennis player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian-French tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian-French tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Romanian footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Romanian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Romanian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>gi"}]}, {"year": "1965", "text": "<PERSON>, American bass player and songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Spanish footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Quique_S%C3%A1nchez_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quique_S%C3%A1nchez_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Quique_S%C3%<PERSON><PERSON>ez_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Spanish golfer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Olaz%C3%A1bal\" title=\"<PERSON>\"><PERSON></a>, Spanish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Olaz%C3%A1bal\" title=\"<PERSON>\"><PERSON></a>, Spanish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Olaz%C3%A1bal"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Slovenian skier (d. 1993)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/R<PERSON>_Petrovi%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian skier (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON>_Petrovi%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian skier (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rok_Petrovi%C4%8D"}]}, {"year": "1967", "text": "<PERSON>, American actor and comedian", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Puerto Rican-American baseball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American rock singer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Finnish race car driver", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gr%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gr%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcus_Gr%C3%B6nholm"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter, dancer, and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Welsh actor and director", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American voice actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Belgian basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian cricketer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian politician, 47th Premier of Tasmania", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 47th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 47th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1971", "text": "<PERSON>, French ice hockey player (d. 1996)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice hockey player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice hockey player (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American country singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "Queen <PERSON> of Denmark", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>_of_Denmark\" title=\"Queen <PERSON> of Denmark\">Queen <PERSON> of Denmark</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON>_of_Denmark\" title=\"Queen <PERSON> of Denmark\">Queen <PERSON> of Denmark</a>", "links": [{"title": "Queen <PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Queen_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player, coach, and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch singer-songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian rugby league player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1975", "text": "<PERSON>, Dutch footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English sailor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian rugby league player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Spanish cyclist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1nchez\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Samuel_S%C3%A1nchez\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Samuel_S%C3%A1nchez"}]}, {"year": "1979", "text": "<PERSON>, American entrepreneur and television personality", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American programmer, created LiveJournal", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American programmer, created <a href=\"https://wikipedia.org/wiki/LiveJournal\" title=\"LiveJournal\">LiveJournal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American programmer, created <a href=\"https://wikipedia.org/wiki/LiveJournal\" title=\"LiveJournal\">LiveJournal</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "LiveJournal", "link": "https://wikipedia.org/wiki/LiveJournal"}]}, {"year": "1980", "text": "<PERSON>, Scottish politician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, French director and screenwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-L%C3%B8ve\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-L%C3%B8ve\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, French director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-L%C3%B8ve"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Czech-Greek footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vyntra\" title=\"<PERSON><PERSON> Vyntra\"><PERSON><PERSON></a>, Czech-Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vyntra\" title=\"<PERSON><PERSON> Vyntra\"><PERSON><PERSON></a>, Czech-Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Loukas_Vyntra"}]}, {"year": "1982", "text": "<PERSON>, Spanish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Laura_del_R%C3%ADo\" title=\"Laura del Río\"><PERSON> Río</a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_R%C3%ADo\" title=\"Laura del Río\"><PERSON></a>, Spanish footballer", "links": [{"title": "Laura <PERSON> Río", "link": "https://wikipedia.org/wiki/Laura_del_R%C3%ADo"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Kopeck%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Kopeck%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Kopeck%C3%BD"}]}, {"year": "1982", "text": "<PERSON>, Argentinian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Norwegian handball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, Norwegian handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, Norwegian handball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/An<PERSON>_<PERSON>seng-Edin"}]}, {"year": "1984", "text": "<PERSON>, Argentinian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor, film producer, and former model", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, film producer, and former model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, film producer, and former model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Croatian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Vedran_%C4%86orluka\" title=\"Vedran Ćorluka\"><PERSON><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vedran_%C4%86orluka\" title=\"Vedran Ćorluka\"><PERSON><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vedran_%C4%86orluka"}]}, {"year": "1986", "text": "<PERSON>, American rapper, singer, and entrepreneur", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper, singer, and entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper, singer, and entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Australian rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby player", "links": [{"title": "Sekope <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>pu"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American race car driver", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Chilean footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Chilean_footballer)\" title=\"<PERSON> (Chilean footballer)\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Chilean_footballer)\" title=\"<PERSON> (Chilean footballer)\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON> (Chilean footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Chilean_footballer)"}]}, {"year": "1987", "text": "<PERSON>, American actor, singer, and entrepreneur", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Ukrainian-American tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American-Israeli sprinter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American-Israeli sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American-Israeli sprinter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "1988", "text": "<PERSON>, Mexican model", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Russian tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Melnikova"}]}, {"year": "1989", "text": "<PERSON>, American actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Russian chess player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Scottish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Rhodes\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Rhodes"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Swedish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1991", "text": "<PERSON>, Albanian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Dutch footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Neymar\" title=\"<PERSON>ey<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Neymar\" title=\"<PERSON>ey<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Neymar"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Canadian ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Belgian-Albanian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-Albanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-Albanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Swedish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ius\" title=\"<PERSON><PERSON>ten<PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ius"}]}, {"year": "1997", "text": "<PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, South Korean rapper", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Jisung\" title=\"Jisung\"><PERSON><PERSON><PERSON></a>, South Korean rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jisung\" title=\"Jisung\"><PERSON><PERSON><PERSON></a>, South Korean rapper", "links": [{"title": "Jisung", "link": "https://wikipedia.org/wiki/Jisung"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Bhutanese prince", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Jigme_<PERSON>gy<PERSON>_<PERSON>\" title=\"Jigme Namgy<PERSON>\">Jig<PERSON></a>, Bhutanese prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jigme_<PERSON><PERSON>_<PERSON>\" title=\"Jigme Namgy<PERSON>\">Jig<PERSON></a>, Bhutanese prince", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jig<PERSON>_<PERSON><PERSON>_<PERSON>ck"}]}], "Deaths": [{"year": "523", "text": "<PERSON><PERSON><PERSON> of Vienne, Gallo-Roman bishop", "html": "523 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_of_Vienne\" title=\"<PERSON><PERSON><PERSON> of Vienne\"><PERSON><PERSON><PERSON> of Vienne</a>, Gallo-Roman bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Vienne\" title=\"<PERSON><PERSON><PERSON> of Vienne\"><PERSON><PERSON><PERSON> of Vienne</a>, Gallo-Roman bishop", "links": [{"title": "<PERSON><PERSON><PERSON> of Vienne", "link": "https://wikipedia.org/wiki/Avitus_of_Vienne"}]}, {"year": "994", "text": "<PERSON>, duke of Aquitaine (b. 937)", "html": "994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON></a>, duke of Aquitaine (b. 937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON></a>, duke of Aquitaine (b. 937)", "links": [{"title": "<PERSON>, Duke of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine"}]}, {"year": "1015", "text": "Adelaide, German abbess and saint", "html": "1015 - <a href=\"https://wikipedia.org/wiki/Adelaide,_Abbess_of_Vilich\" title=\"Adelaide, Abbess of Vilich\">Adelaide</a>, German abbess and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide,_Abbess_of_Vilich\" title=\"Adelaide, Abbess of Vilich\">Adelaide</a>, German abbess and saint", "links": [{"title": "<PERSON>, Abbess of Vilich", "link": "https://wikipedia.org/wiki/Adelaide,_Abbess_of_<PERSON><PERSON><PERSON>"}]}, {"year": "1036", "text": "<PERSON>, Anglo-Saxon prince", "html": "1036 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Saxon prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>g\"><PERSON></a>, Anglo-Saxon prince", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "1146", "text": "<PERSON><PERSON><PERSON><PERSON>, Arab emir of Zaragoza", "html": "1146 - <a href=\"https://wikipedia.org/wiki/Zafadola\" title=\"Zafadola\"><PERSON><PERSON><PERSON><PERSON></a>, Arab emir of Zaragoza", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zafadola\" title=\"Zafadola\"><PERSON><PERSON><PERSON><PERSON></a>, Arab emir of Zaragoza", "links": [{"title": "Z<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zafadola"}]}, {"year": "1578", "text": "<PERSON>, Italian painter (b. 1520)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1520)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1661", "text": "<PERSON><PERSON><PERSON>, Chinese emperor of the Qing Dynasty (b. 1638)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/Shunzhi_Emperor\" title=\"Shunzhi Emperor\"><PERSON><PERSON><PERSON></a>, Chinese emperor of the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing Dynasty</a> (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shunzhi_Emperor\" title=\"Shunzhi Emperor\"><PERSON><PERSON><PERSON></a>, Chinese emperor of the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing Dynasty</a> (b. 1638)", "links": [{"title": "Shunzhi Emperor", "link": "https://wikipedia.org/wiki/Shunz<PERSON>_Emperor"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}]}, {"year": "1705", "text": "<PERSON>, German theologian and author (b. 1635)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (b. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (b. 1635)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON>, French jurist and politician, Chancellor of France (b. 1668)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois_d%27Aguesseau\" title=\"<PERSON>\"><PERSON></a>, French jurist and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_France\" title=\"Chancellor of France\">Chancellor of France</a> (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A7ois_d%27Aguesseau\" title=\"<PERSON>\"><PERSON></a>, French jurist and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_France\" title=\"Chancellor of France\">Chancellor of France</a> (b. 1668)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois_d%27A<PERSON><PERSON>"}, {"title": "Chancellor of France", "link": "https://wikipedia.org/wiki/Chancellor_of_France"}]}, {"year": "1754", "text": "<PERSON><PERSON>, Dutch astronomer and cartographer (b. 1678)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch astronomer and cartographer (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch astronomer and cartographer (b. 1678)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1766", "text": "Count <PERSON>, Austrian field marshal (b. 1705)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Count <PERSON></a>, Austrian field marshal (b. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Count <PERSON></a>, Austrian field marshal (b. 1705)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON><PERSON><PERSON>, German theologian and academic (b. 1692)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German theologian and academic (b. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German theologian and academic (b. 1692)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eusebius_Amort"}]}, {"year": "1790", "text": "<PERSON>, Scottish physician and chemist (b. 1710)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician and chemist (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician and chemist (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON><PERSON><PERSON><PERSON>, Corsican commander and politician (b. 1725)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Corsican commander and politician (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Corsican commander and politician (b. 1725)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, king of Sweden (b. 1748)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, king of Sweden (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> XIII\"><PERSON></a>, king of Sweden (b. 1748)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Scottish philosopher, historian, and academic (b. 1795)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish philosopher, historian, and academic (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish philosopher, historian, and academic (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Spanish orientalist and diplomat (b. 1841)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish orientalist and diplomat (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish orientalist and diplomat (b. 1841)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolf<PERSON>_<PERSON>deney<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish author (b. 1807)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author (b. 1807)", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gare-Carl%C3%A9n"}]}, {"year": "1915", "text": "<PERSON>, American baseball player and manager (b. 1850)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "J<PERSON>r II Al-Sabah, Kuwaiti ruler (b. 1860)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Jaber_II_Al-Sabah\" title=\"Jaber II Al-Sabah\">Jaber II Al-Sabah</a>, Kuwaiti ruler (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaber_II_Al-Sabah\" title=\"Jaber II Al-Sabah\">Jaber II Al-Sabah</a>, Kuwaiti ruler (b. 1860)", "links": [{"title": "Jaber II Al-Sabah", "link": "https://wikipedia.org/wiki/Jaber_II_Al-Sabah"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Croatian engineer, invented the mechanical pencil (b. 1871)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lav<PERSON><PERSON><PERSON>\">Slavol<PERSON><PERSON> <PERSON></a>, Croatian engineer, invented the <a href=\"https://wikipedia.org/wiki/Mechanical_pencil\" title=\"Mechanical pencil\">mechanical pencil</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lavol<PERSON><PERSON>\">Slavol<PERSON><PERSON> <PERSON></a>, Croatian engineer, invented the <a href=\"https://wikipedia.org/wiki/Mechanical_pencil\" title=\"Mechanical pencil\">mechanical pencil</a> (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Mechanical pencil", "link": "https://wikipedia.org/wiki/Mechanical_pencil"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Indian mystic and educator (b. 1882)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian mystic and educator (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian mystic and educator (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek politician, 118th Prime Minister of Greece (b. 1849)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Athanasios_Eftaxias\" title=\"<PERSON>hanasios Eftaxias\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician, 118th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Athanasios_Eftaxias\" title=\"Athanasios Eftaxias\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician, 118th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1849)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Athanasios_Eftaxias"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1933", "text": "<PERSON>, English-Australian miner and politician (b. 1863)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English-Australian miner and politician (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English-Australian miner and politician (b. 1863)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Russian-German psychoanalyst and author (b. 1861)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Russian-German psychoanalyst and author (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Russian-German psychoanalyst and author (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9"}]}, {"year": "1938", "text": "<PERSON>, German lawyer and jurist (b. 1903)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and jurist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and jurist (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Australian journalist, author, and poet (b. 1864)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Paterson\"><PERSON><PERSON></a>, Australian journalist, author, and poet (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Paterson\"><PERSON><PERSON></a>, Australian journalist, author, and poet (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Paterson"}]}, {"year": "1941", "text": "<PERSON>, Estonian lawyer and politician, 1st Prime Minister of Estonia (b. 1875)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a> (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1946", "text": "<PERSON>, English actor and playwright (b. 1868)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, German general (b. 1883)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, English pianist and composer (b. 1877)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English pianist and composer (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English pianist and composer (b. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Verne"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Iranian politician, diplomat, writer and poet (b. 1876)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Hossein_Sami%27i\" title=\"Hossein <PERSON>'i\"><PERSON><PERSON><PERSON>i</a>, Iranian politician, diplomat, writer and poet (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hossein_<PERSON>%27i\" title=\"Hossein <PERSON>'i\"><PERSON><PERSON><PERSON>i</a>, Iranian politician, diplomat, writer and poet (b. 1876)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hossein_Sami%27i"}]}, {"year": "1955", "text": "<PERSON>, Bulgarian religious reformer and author (b. 1885)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian religious reformer and author (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian religious reformer and author (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Lebanese surgeon and author (b. 1890)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese surgeon and author (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese surgeon and author (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, French-Swiss composer (b. 1890)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss composer (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss composer (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American businessman, founded <PERSON><PERSON><PERSON><PERSON> (b. 1872)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"L<PERSON><PERSON><PERSON>Bean\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>Bean\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L.L.Bean"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American actress (b. 1902)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American baseball player, coach, and manager (b. 1913)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American poet, author, critic, and translator (b. 1887)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, author, critic, and translator (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, author, critic, and translator (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American saxophonist (<PERSON> & His Comets) (b. 1926)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_His_Comets\" title=\"<PERSON> &amp; His Comets\"><PERSON> &amp; His Comets</a>) (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_His_Comets\" title=\"<PERSON> &amp; His Comets\"><PERSON> &amp; His Comets</a>) (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> & His Comets", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_His_Comets"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Swedish physicist and academic (b. 1894)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish physicist and academic (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish physicist and academic (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American politician, 83rd Governor of Connecticut (b. 1919)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 83rd <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Connecticut\" title=\"List of governors of Connecticut\">Governor of Connecticut</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 83rd <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Connecticut\" title=\"List of governors of Connecticut\">Governor of Connecticut</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of governors of Connecticut", "link": "https://wikipedia.org/wiki/List_of_governors_of_Connecticut"}]}, {"year": "1982", "text": "<PERSON>, Kenyan-South African physician and union leader (b. 1953)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-South African physician and union leader (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-South African physician and union leader (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American chemist and academic (b. 1925)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor and producer (b. 1902)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor and producer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor and producer (b. 1902)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1989", "text": "<PERSON>, American pianist and composer (b. 1937)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor (b. 1903)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Argentinian-Brazilian physiologist and academic (b. 1913)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Brazilian physiologist and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Brazilian physiologist and academic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Irish footballer and politician, 7th Irish Minister for Health (b. 1922)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Se%C3%A1n_F<PERSON>gan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish footballer and politician, 7th <a href=\"https://wikipedia.org/wiki/Minister_for_Health_(Ireland)\" title=\"Minister for Health (Ireland)\">Irish Minister for Health</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%A1n_F<PERSON>gan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish footballer and politician, 7th <a href=\"https://wikipedia.org/wiki/Minister_for_Health_(Ireland)\" title=\"Minister for Health (Ireland)\">Irish Minister for Health</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%A1n_Flanagan"}, {"title": "Minister for Health (Ireland)", "link": "https://wikipedia.org/wiki/Minister_for_Health_(Ireland)"}]}, {"year": "1993", "text": "<PERSON>, American director, producer, and screenwriter (b. 1909)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American author and illustrator (b. 1916)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_P%C3%A8<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actor (b. 1935)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English-American diplomat, 58th United States Ambassador to France (b. 1920)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American diplomat, 58th <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_States_to_France\" title=\"List of ambassadors of the United States to France\">United States Ambassador to France</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American diplomat, 58th <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_States_to_France\" title=\"List of ambassadors of the United States to France\">United States Ambassador to France</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of ambassadors of the United States to France", "link": "https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_States_to_France"}]}, {"year": "1997", "text": "<PERSON>, French historian and author (b. 1906)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>e"}]}, {"year": "1998", "text": "<PERSON>, American guitarist (b. 1963)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist (b. 1963)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Russian-American economist and academic, Nobel Prize laureate (b. 1906)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2000", "text": "<PERSON>, French director and screenwriter (b. 1901)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American animator (b. 1908)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Togolese general and politician, President of Togo (b. 1937)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Gnassingb%C3%A9_Eyad%C3%A9ma\" title=\"Gnassingbé Eyadéma\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Togolese general and politician, <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_Togo\" title=\"List of presidents of Togo\">President of Togo</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gnassingb%C3%A9_Eyad%C3%A9ma\" title=\"Gnassingbé Eyadéma\"><PERSON><PERSON>sing<PERSON><PERSON> E<PERSON></a>, Togolese general and politician, <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_Togo\" title=\"List of presidents of Togo\">President of Togo</a> (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gnassingb%C3%A9_Eyad%C3%A9ma"}, {"title": "List of presidents of Togo", "link": "https://wikipedia.org/wiki/List_of_presidents_of_Togo"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Polish gynecologist and sexologist (b. 1921)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wis%C5%82ocka\" title=\"<PERSON><PERSON><PERSON> Wisłocka\"><PERSON><PERSON><PERSON></a>, Polish gynecologist and sexologist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wis%C5%82ocka\" title=\"<PERSON><PERSON><PERSON> W<PERSON>łock<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish gynecologist and sexologist (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wis%C5%82ocka"}]}, {"year": "2006", "text": "<PERSON>, Puerto Rican-American actress (b. 1927)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actress (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, New Zealand-American soldier, lawyer, and politician, 43rd Lieutenant Governor of California (b. 1930)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-American soldier, lawyer, and politician, 43rd <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_California\" title=\"Lieutenant Governor of California\">Lieutenant Governor of California</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-American soldier, lawyer, and politician, 43rd <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_California\" title=\"Lieutenant Governor of California\">Lieutenant Governor of California</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of California", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_California"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Indian guru, founded Transcendental Meditation (b. 1918)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>h_Yogi\" title=\"Ma<PERSON><PERSON> Mahesh Yogi\"><PERSON><PERSON><PERSON> <PERSON>h <PERSON>gi</a>, Indian guru, founded <a href=\"https://wikipedia.org/wiki/Transcendental_Meditation\" title=\"Transcendental Meditation\">Transcendental Meditation</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Yogi\" title=\"Ma<PERSON>shi Mahesh Yogi\"><PERSON><PERSON><PERSON> <PERSON>h <PERSON>gi</a>, Indian guru, founded <a href=\"https://wikipedia.org/wiki/Transcendental_Meditation\" title=\"Transcendental Meditation\">Transcendental Meditation</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>gi"}, {"title": "Transcendental Meditation", "link": "https://wikipedia.org/wiki/Transcendental_Meditation"}]}, {"year": "2010", "text": "<PERSON>, Canadian ice hockey player and activist (b. 1988)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and activist (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and activist (b. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, South African lawyer, anti-apartheid leader, and diplomat, 13th South Africa Ambassador to United States (b. 1924)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer, anti-apartheid leader, and diplomat, 13th <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_South_Africa_to_the_United_States\" title=\"List of ambassadors of South Africa to the United States\">South Africa Ambassador to United States</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer, anti-apartheid leader, and diplomat, 13th <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_South_Africa_to_the_United_States\" title=\"List of ambassadors of South Africa to the United States\">South Africa Ambassador to United States</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of ambassadors of South Africa to the United States", "link": "https://wikipedia.org/wiki/List_of_ambassadors_of_South_Africa_to_the_United_States"}]}, {"year": "2011", "text": "<PERSON>, English author and radio host (b. 1939)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and radio host (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and radio host (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American actress and casting director (b. 1921)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and casting director (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and casting director (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American actor (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American keyboard player, conductor, and producer (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, conductor, and producer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, conductor, and producer (b. 1930)", "links": [{"title": "Al De Lory", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>., American publisher (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON>.</a>, American publisher (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON>.</a>, American publisher (b. 1924)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2012", "text": "<PERSON>, Dutch sprinter (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sprinter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sprinter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Uruguayan journalist and politician, Minister of Foreign Affairs for Uruguay (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Reinal<PERSON>_<PERSON>ar<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan journalist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Relations_of_Uruguay\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Relations of Uruguay\">Minister of Foreign Affairs for Uruguay</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan journalist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Relations_of_Uruguay\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Relations of Uruguay\">Minister of Foreign Affairs for Uruguay</a> (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>inal<PERSON>_<PERSON>gano"}, {"title": "List of Ministers of Foreign Relations of Uruguay", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Relations_of_Uruguay"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Norwegian composer and conductor (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Egi<PERSON>_<PERSON>\" title=\"E<PERSON><PERSON> Ho<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian composer and conductor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>\" title=\"E<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian composer and conductor (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>l_<PERSON>land"}]}, {"year": "2013", "text": "<PERSON>, New Zealand soldier and politician, 23rd New Zealand Minister of Health (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand soldier and politician, 23rd <a href=\"https://wikipedia.org/wiki/Minister_of_Health_(New_Zealand)\" title=\"Minister of Health (New Zealand)\">New Zealand Minister of Health</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand soldier and politician, 23rd <a href=\"https://wikipedia.org/wiki/Minister_of_Health_(New_Zealand)\" title=\"Minister of Health (New Zealand)\">New Zealand Minister of Health</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Health (New Zealand)", "link": "https://wikipedia.org/wiki/Minister_of_Health_(New_Zealand)"}]}, {"year": "2014", "text": "<PERSON>, American political scientist and academic (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer and politician, Minister of Finance of Sri Lanka (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"K. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Sri_Lanka)\" title=\"Minister of Finance (Sri Lanka)\">Minister of Finance of Sri Lanka</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"K. N<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Sri_Lanka)\" title=\"Minister of Finance (Sri Lanka)\">Minister of Finance of Sri Lanka</a> (b. 1933)", "links": [{"title": "K. N. Cho<PERSON>y", "link": "https://wikipedia.org/wiki/K._<PERSON>._<PERSON>y"}, {"title": "Minister of Finance (Sri Lanka)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Sri_Lanka)"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Italian actress and singer (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress and singer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress and singer (b. 1931)", "links": [{"title": "Marisa Del Frate", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>tch"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2015", "text": "<PERSON>, Polish-American author (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Filipino martial artist (b. 1919)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Ciriaco_Ca%C3%B1ete\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino martial artist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ciriaco_Ca%C3%B1ete\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino martial artist (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ciriaco_Ca%C3%B1ete"}]}, {"year": "2020", "text": "<PERSON>,  American actor (b. 1916)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Canadian actor (b. 1929)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Pakistani military officer and politician, 10th President of Pakistan (b. 1943)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani military officer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani military officer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>f"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "2024", "text": "<PERSON>, American country singer (b. 1961)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON><PERSON><PERSON>, American record producer, co-founded Murder Inc Records (b. 1970)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/Irv_<PERSON>\" title=\"<PERSON>r<PERSON>\"><PERSON><PERSON><PERSON></a>, American record producer, co-founded <a href=\"https://wikipedia.org/wiki/Murder_Inc_Records\" class=\"mw-redirect\" title=\"Murder Inc Records\">Murder Inc Records</a> (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irv_<PERSON>\" title=\"Irv <PERSON>\"><PERSON><PERSON><PERSON></a>, American record producer, co-founded <a href=\"https://wikipedia.org/wiki/Murder_Inc_Records\" class=\"mw-redirect\" title=\"Murder Inc Records\">Murder Inc Records</a> (b. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irv_<PERSON>"}, {"title": "Murder Inc Records", "link": "https://wikipedia.org/wiki/Murder_Inc_Records"}]}]}}