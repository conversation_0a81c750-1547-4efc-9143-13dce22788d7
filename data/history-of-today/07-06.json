{"date": "July 6", "url": "https://wikipedia.org/wiki/July_6", "data": {"Events": [{"year": "371 BC", "text": "The Battle of Leuctra shatters Sparta's reputation of military invincibility.", "html": "371 BC - 371 BC - The <a href=\"https://wikipedia.org/wiki/Battle_of_Leuctra\" title=\"Battle of Leuctra\">Battle of Leuctra</a> shatters Sparta's reputation of military invincibility.", "no_year_html": "371 BC - The <a href=\"https://wikipedia.org/wiki/Battle_of_Leuctra\" title=\"Battle of Leuctra\">Battle of Leuctra</a> shatters Sparta's reputation of military invincibility.", "links": [{"title": "Battle of Leuctra", "link": "https://wikipedia.org/wiki/Battle_of_Leuctra"}]}, {"year": "640", "text": "Battle of Heliopolis: The Muslim Arab army under <PERSON><PERSON><PERSON> defeat the Byzantine forces near Heliopolis (Egypt).", "html": "640 - <a href=\"https://wikipedia.org/wiki/Battle_of_Heliopolis\" title=\"Battle of Heliopolis\">Battle of Heliopolis</a>: The Muslim Arab army under <a href=\"https://wikipedia.org/wiki/%27Amr_ibn_al-%27As\" class=\"mw-redirect\" title=\"'<PERSON><PERSON> ibn al<PERSON>'<PERSON>\">'<PERSON><PERSON> ibn <PERSON>'<PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> forces near <a href=\"https://wikipedia.org/wiki/Heliopolis_(Ancient_Egypt)\" class=\"mw-redirect\" title=\"Heliopolis (Ancient Egypt)\">Heliopolis</a> (<a href=\"https://wikipedia.org/wiki/Diocese_of_Egypt_(Late_Antiquity)\" class=\"mw-redirect\" title=\"Diocese of Egypt (Late Antiquity)\">Egypt</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Heliopolis\" title=\"Battle of Heliopolis\">Battle of Heliopolis</a>: The Muslim Arab army under <a href=\"https://wikipedia.org/wiki/%27Amr_ibn_al-%27As\" class=\"mw-redirect\" title=\"'<PERSON><PERSON> ibn <PERSON>'<PERSON>\">'<PERSON><PERSON> ibn <PERSON>'As</a> defeat the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> forces near <a href=\"https://wikipedia.org/wiki/Heliopolis_(Ancient_Egypt)\" class=\"mw-redirect\" title=\"Heliopolis (Ancient Egypt)\">Heliopolis</a> (<a href=\"https://wikipedia.org/wiki/Diocese_of_Egypt_(Late_Antiquity)\" class=\"mw-redirect\" title=\"Diocese of Egypt (Late Antiquity)\">Egypt</a>).", "links": [{"title": "Battle of Heliopolis", "link": "https://wikipedia.org/wiki/Battle_of_Heliopolis"}, {"title": "'<PERSON><PERSON> ibn <PERSON>'<PERSON>", "link": "https://wikipedia.org/wiki/%27A<PERSON><PERSON>_<PERSON>_al-%27As"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Heliopolis (Ancient Egypt)", "link": "https://wikipedia.org/wiki/Heliopolis_(Ancient_Egypt)"}, {"title": "Diocese of Egypt (Late Antiquity)", "link": "https://wikipedia.org/wiki/Diocese_of_Egypt_(Late_Antiquity)"}]}, {"year": "1253", "text": "<PERSON><PERSON><PERSON> is crowned King of Lithuania.", "html": "1253 - <a href=\"https://wikipedia.org/wiki/Mindaugas\" title=\"Mindaugas\"><PERSON><PERSON><PERSON></a> is crowned King of <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mindaugas\" title=\"Mindaugas\"><PERSON><PERSON><PERSON></a> is crowned King of <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "links": [{"title": "Mindaugas", "link": "https://wikipedia.org/wiki/Mindaugas"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}]}, {"year": "1348", "text": "<PERSON> <PERSON> issues a papal bull protecting the Jews accused of having caused the Black Death.", "html": "1348 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_VI\" title=\"Pope Clement VI\">Pope Clement VI</a> issues a <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> <a href=\"https://wikipedia.org/wiki/Antisemitism#Persecutions_during_the_Middle_Ages\" title=\"Antisemitism\">protecting</a> the <a href=\"https://wikipedia.org/wiki/Jews_in_the_Middle_Ages#Black_Death\" class=\"mw-redirect\" title=\"Jews in the Middle Ages\">Jews accused</a> of having caused the <a href=\"https://wikipedia.org/wiki/Black_Death\" title=\"Black Death\">Black Death</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_VI\" title=\"Pope Clement VI\">Pope Clement VI</a> issues a <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> <a href=\"https://wikipedia.org/wiki/Antisemitism#Persecutions_during_the_Middle_Ages\" title=\"Antisemitism\">protecting</a> the <a href=\"https://wikipedia.org/wiki/Jews_in_the_Middle_Ages#Black_Death\" class=\"mw-redirect\" title=\"Jews in the Middle Ages\">Jews accused</a> of having caused the <a href=\"https://wikipedia.org/wiki/Black_Death\" title=\"Black Death\">Black Death</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Papal bull", "link": "https://wikipedia.org/wiki/Papal_bull"}, {"title": "Antisemitism", "link": "https://wikipedia.org/wiki/Antisemitism#Persecutions_during_the_Middle_Ages"}, {"title": "Jews in the Middle Ages", "link": "https://wikipedia.org/wiki/Jews_in_the_Middle_Ages#Black_Death"}, {"title": "Black Death", "link": "https://wikipedia.org/wiki/Black_Death"}]}, {"year": "1411", "text": "Ming China's Admiral <PERSON> returns to Nanjing after the third treasure voyage and presents the Sinhalese king, captured during the Ming-Kotte War, to the Yongle Emperor.", "html": "1411 - <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming China</a>'s Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> He</a> returns to <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a> after the <a href=\"https://wikipedia.org/wiki/Ming_treasure_voyages#Third_voyage\" title=\"Ming treasure voyages\">third treasure voyage</a> and presents the <a href=\"https://wikipedia.org/wiki/Sinhalese_people\" title=\"Sinhalese people\">Sinhalese</a> king, captured during the <a href=\"https://wikipedia.org/wiki/Ming%E2%80%93Kotte_War\" title=\"Ming-Kotte War\">Ming-Kotte War</a>, to the <a href=\"https://wikipedia.org/wiki/Yongle_Emperor\" title=\"Yongle Emperor\">Yongle Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming China</a>'s Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> He</a> returns to <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a> after the <a href=\"https://wikipedia.org/wiki/Ming_treasure_voyages#Third_voyage\" title=\"Ming treasure voyages\">third treasure voyage</a> and presents the <a href=\"https://wikipedia.org/wiki/Sinhalese_people\" title=\"Sinhalese people\">Sinhalese</a> king, captured during the <a href=\"https://wikipedia.org/wiki/Ming%E2%80%93Kotte_War\" title=\"Ming-Kotte War\">Ming-Kotte War</a>, to the <a href=\"https://wikipedia.org/wiki/Yongle_Emperor\" title=\"Yongle Emperor\">Yongle Emperor</a>.", "links": [{"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nanjing", "link": "https://wikipedia.org/wiki/Nanjing"}, {"title": "Ming treasure voyages", "link": "https://wikipedia.org/wiki/Ming_treasure_voyages#Third_voyage"}, {"title": "Sinhalese people", "link": "https://wikipedia.org/wiki/Sinhalese_people"}, {"title": "Ming-Kotte War", "link": "https://wikipedia.org/wiki/Ming%E2%80%93Kotte_War"}, {"title": "Yongle Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}]}, {"year": "1415", "text": "<PERSON> is condemned by the assembly of the council in the Konstanz Cathedral as a heretic and sentenced to be burned at the stake.", "html": "1415 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is condemned by the assembly of the council in the <a href=\"https://wikipedia.org/wiki/Konstanz_Minster\" title=\"Konstanz Minster\">Konstanz Cathedral</a> as a heretic and sentenced to be burned at the stake.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is condemned by the assembly of the council in the <a href=\"https://wikipedia.org/wiki/Konstanz_Minster\" title=\"Konstanz Minster\">Konstanz Cathedral</a> as a heretic and sentenced to be burned at the stake.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Konstanz Minster", "link": "https://wikipedia.org/wiki/Konstanz_Minster"}]}, {"year": "1438", "text": "A temporary compromise between the rebellious Transylvanian peasants and the noblemen is signed in Kolozsmonostor Abbey.", "html": "1438 - A temporary compromise between the <a href=\"https://wikipedia.org/wiki/Transylvanian_peasant_revolt\" title=\"Transylvanian peasant revolt\">rebellious Transylvanian peasants</a> and the noblemen is signed in <a href=\"https://wikipedia.org/wiki/Kolozsmonostor_Abbey\" title=\"Kolozsmonostor Abbey\">Kolozsmonostor Abbey</a>.", "no_year_html": "A temporary compromise between the <a href=\"https://wikipedia.org/wiki/Transylvanian_peasant_revolt\" title=\"Transylvanian peasant revolt\">rebellious Transylvanian peasants</a> and the noblemen is signed in <a href=\"https://wikipedia.org/wiki/Kolozsmonostor_Abbey\" title=\"Kolozsmonostor Abbey\">Kolozsmonostor Abbey</a>.", "links": [{"title": "Transylvanian peasant revolt", "link": "https://wikipedia.org/wiki/Transylvanian_peasant_revolt"}, {"title": "Kolozsmonostor Abbey", "link": "https://wikipedia.org/wiki/Kolozsmonostor_Abbey"}]}, {"year": "1483", "text": "<PERSON> and <PERSON> are crowned King and Queen of England.", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are crowned King and Queen of England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are crowned King and Queen of England.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1484", "text": "Portuguese sea captain <PERSON><PERSON> finds the mouth of the Congo River.", "html": "1484 - Portuguese sea captain <a href=\"https://wikipedia.org/wiki/Diogo_C%C3%A3o\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> finds the mouth of the <a href=\"https://wikipedia.org/wiki/Congo_River\" title=\"Congo River\">Congo River</a>.", "no_year_html": "Portuguese sea captain <a href=\"https://wikipedia.org/wiki/Diogo_C%C3%A3o\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> finds the mouth of the <a href=\"https://wikipedia.org/wiki/Congo_River\" title=\"Congo River\">Congo River</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Diogo_C%C3%A3o"}, {"title": "Congo River", "link": "https://wikipedia.org/wiki/Congo_River"}]}, {"year": "1495", "text": "First Italian War: Battle of Fornovo: Charles <PERSON> defeats the Holy League.", "html": "1495 - <a href=\"https://wikipedia.org/wiki/First_Italian_War\" class=\"mw-redirect\" title=\"First Italian War\">First Italian War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Fornovo\" title=\"Battle of Fornovo\">Battle of Fornovo</a>: <a href=\"https://wikipedia.org/wiki/Charles_VIII_of_France\" title=\"Charles VIII of France\"><PERSON> VIII</a> defeats the <a href=\"https://wikipedia.org/wiki/Italian_War_of_1494%E2%80%9398\" class=\"mw-redirect\" title=\"Italian War of 1494-98\">Holy League</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Italian_War\" class=\"mw-redirect\" title=\"First Italian War\">First Italian War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Fornovo\" title=\"Battle of Fornovo\">Battle of Fornovo</a>: <a href=\"https://wikipedia.org/wiki/Charles_VIII_of_France\" title=\"Charles VIII of France\"><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Italian_War_of_1494%E2%80%9398\" class=\"mw-redirect\" title=\"Italian War of 1494-98\">Holy League</a>.", "links": [{"title": "First Italian War", "link": "https://wikipedia.org/wiki/First_Italian_War"}, {"title": "Battle of Fornovo", "link": "https://wikipedia.org/wiki/Battle_of_Fornovo"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_France"}, {"title": "Italian War of 1494-98", "link": "https://wikipedia.org/wiki/Italian_War_of_1494%E2%80%9398"}]}, {"year": "1536", "text": "The explorer <PERSON> lands at St. Malo at the end of his second expedition to North America. He returns with none of the gold he expected to find.", "html": "1536 - The explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands at St. Malo at the end of his second expedition to North America. He returns with none of the gold he expected to find.", "no_year_html": "The explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands at St. Malo at the end of his second expedition to North America. He returns with none of the gold he expected to find.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1557", "text": "King <PERSON> of Spain, consort of Queen <PERSON> of England, sets out from Dover to war with France, which eventually resulted in the loss of the city of Calais, the last English possession on the continent, and <PERSON> I never seeing her husband again.", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\">King <PERSON> of Spain</a>, consort of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\">Queen <PERSON> of England</a>, sets out from <a href=\"https://wikipedia.org/wiki/Dover\" title=\"Dover\">Dover</a> to war with France, which eventually resulted in the loss of the city of <a href=\"https://wikipedia.org/wiki/Calais\" title=\"Calais\">Calais</a>, the last <a href=\"https://wikipedia.org/wiki/Countries_of_the_United_Kingdom\" title=\"Countries of the United Kingdom\">English</a> possession on the continent, and <PERSON> I never seeing her husband again.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\">King <PERSON> II of Spain</a>, consort of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\">Queen <PERSON> of England</a>, sets out from <a href=\"https://wikipedia.org/wiki/Dover\" title=\"Dover\">Dover</a> to war with France, which eventually resulted in the loss of the city of <a href=\"https://wikipedia.org/wiki/Calais\" title=\"Calais\">Calais</a>, the last <a href=\"https://wikipedia.org/wiki/Countries_of_the_United_Kingdom\" title=\"Countries of the United Kingdom\">English</a> possession on the continent, and <PERSON> I never seeing her husband again.", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Dover", "link": "https://wikipedia.org/wiki/Dover"}, {"title": "Calais", "link": "https://wikipedia.org/wiki/Calais"}, {"title": "Countries of the United Kingdom", "link": "https://wikipedia.org/wiki/Countries_of_the_United_Kingdom"}]}, {"year": "1560", "text": "The Treaty of Edinburgh is signed by Scotland and England.", "html": "1560 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Edinburgh\" title=\"Treaty of Edinburgh\">Treaty of Edinburgh</a> is signed by Scotland and England.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Edinburgh\" title=\"Treaty of Edinburgh\">Treaty of Edinburgh</a> is signed by Scotland and England.", "links": [{"title": "Treaty of Edinburgh", "link": "https://wikipedia.org/wiki/Treaty_of_Edinburgh"}]}, {"year": "1573", "text": "Córdoba, Argentina, is founded by <PERSON><PERSON><PERSON><PERSON>.", "html": "1573 - <a href=\"https://wikipedia.org/wiki/C%C3%B3rdoba,_Argentina\" title=\"Córdoba, Argentina\">Córdoba, Argentina</a>, is founded by <a href=\"https://wikipedia.org/wiki/Jer%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%B3rdoba,_Argentina\" title=\"Córdoba, Argentina\">Córdoba, Argentina</a>, is founded by <a href=\"https://wikipedia.org/wiki/Jer%C3%B3ni<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Córdoba, Argentina", "link": "https://wikipedia.org/wiki/C%C3%B3rdoba,_Argentina"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jer%C3%B3ni<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1573", "text": "French Wars of Religion: Siege of La Rochelle ends.", "html": "1573 - <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_La_Rochelle_(1572%E2%80%9373)\" class=\"mw-redirect\" title=\"Siege of La Rochelle (1572-73)\">Siege of La Rochelle</a> ends.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_La_Rochelle_(1572%E2%80%9373)\" class=\"mw-redirect\" title=\"Siege of La Rochelle (1572-73)\">Siege of La Rochelle</a> ends.", "links": [{"title": "French Wars of Religion", "link": "https://wikipedia.org/wiki/French_Wars_of_Religion"}, {"title": "Siege of La Rochelle (1572-73)", "link": "https://wikipedia.org/wiki/Siege_of_La_<PERSON>_(1572%E2%80%9373)"}]}, {"year": "1614", "text": "Raid on Żejtun: The south east of Malta, and the town of Żejtun, suffer a raid from Ottoman forces. This was the last unsuccessful attempt by the Ottomans to conquer the island of Malta.", "html": "1614 - <a href=\"https://wikipedia.org/wiki/Raid_on_%C5%BBejtun\" title=\"Raid on Żejtun\">Raid on Żejtun</a>: The south east of <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>, and the town of <a href=\"https://wikipedia.org/wiki/%C5%BBejtun\" title=\"Żejtun\">Żejtun</a>, suffer a raid from <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> forces. This was the last unsuccessful attempt by the Ottomans to conquer the island of <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raid_on_%C5%BBejtun\" title=\"Raid on Żejtun\">Raid on Żejtun</a>: The south east of <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>, and the town of <a href=\"https://wikipedia.org/wiki/%C5%BBejtun\" title=\"Żejtun\">Żejtun</a>, suffer a raid from <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> forces. This was the last unsuccessful attempt by the Ottomans to conquer the island of <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>.", "links": [{"title": "Raid on Żejtun", "link": "https://wikipedia.org/wiki/Raid_on_%C5%BBejtun"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%BBejtun"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}]}, {"year": "1630", "text": "Thirty Years' War: Four thousand Swedish troops under <PERSON><PERSON> land in Pomerania, Germany.", "html": "1630 - <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: Four thousand Swedish troops under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON><PERSON>phus of Sweden\"><PERSON><PERSON></a> land in <a href=\"https://wikipedia.org/wiki/Pomerania\" title=\"Pomerania\">Pomerania</a>, Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: Four thousand Swedish troops under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON><PERSON>phus of Sweden\"><PERSON><PERSON></a> land in <a href=\"https://wikipedia.org/wiki/Pomerania\" title=\"Pomerania\">Pomerania</a>, Germany.", "links": [{"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}, {"title": "<PERSON><PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_of_Sweden"}, {"title": "Pomerania", "link": "https://wikipedia.org/wiki/Pomerania"}]}, {"year": "1685", "text": "Battle of Sedgemoor: Last battle of the Monmouth Rebellion. Troops of King <PERSON> defeat troops of <PERSON>, 1st Duke of Monmouth.", "html": "1685 - <a href=\"https://wikipedia.org/wiki/Battle_of_Sedgemoor\" title=\"Battle of Sedgemoor\">Battle of Sedgemoor</a>: Last battle of the <a href=\"https://wikipedia.org/wiki/Monmouth_Rebellion\" title=\"Monmouth Rebellion\">Monmouth Rebellion</a>. Troops of King <PERSON> II defeat troops of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Monmouth\" title=\"<PERSON>, 1st Duke of Monmouth\"><PERSON>, 1st Duke of Monmouth</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Sedgemoor\" title=\"Battle of Sedgemoor\">Battle of Sedgemoor</a>: Last battle of the <a href=\"https://wikipedia.org/wiki/Monmouth_Rebellion\" title=\"Monmouth Rebellion\">Monmouth Rebellion</a>. Troops of King <PERSON> defeat troops of <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Monmouth\" title=\"<PERSON>, 1st Duke of Monmouth\"><PERSON>, 1st Duke of Monmouth</a>.", "links": [{"title": "Battle of Sedgemoor", "link": "https://wikipedia.org/wiki/Battle_of_Sedgemoor"}, {"title": "Monmouth Rebellion", "link": "https://wikipedia.org/wiki/Monmouth_Rebellion"}, {"title": "<PERSON>, 1st Duke of Monmouth", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Monmouth"}]}, {"year": "1751", "text": "<PERSON> <PERSON> suppresses the Patriarchate of Aquileia and establishes from its territory the Archdiocese of Udine and Gorizia.", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> suppresses the <a href=\"https://wikipedia.org/wiki/Patriarchate_of_Aquileia\" title=\"Patriarchate of Aquileia\">Patriarchate of Aquileia</a> and establishes from its territory the Arch<a href=\"https://wikipedia.org/wiki/Diocese\" title=\"Diocese\">diocese</a> of <a href=\"https://wikipedia.org/wiki/Udine\" title=\"Udine\">Udine</a> and <a href=\"https://wikipedia.org/wiki/Gorizia\" title=\"Gorizia\">Gorizia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\">Pope <PERSON></a> suppresses the <a href=\"https://wikipedia.org/wiki/Patriarchate_of_Aquileia\" title=\"Patriarchate of Aquileia\">Patriarchate of Aquileia</a> and establishes from its territory the Arch<a href=\"https://wikipedia.org/wiki/Diocese\" title=\"Diocese\">diocese</a> of <a href=\"https://wikipedia.org/wiki/Udine\" title=\"Udine\">Udine</a> and <a href=\"https://wikipedia.org/wiki/Gorizia\" title=\"Gorizia\">Gorizia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Patriarchate of Aquileia", "link": "https://wikipedia.org/wiki/Patriarchate_of_Aquileia"}, {"title": "Diocese", "link": "https://wikipedia.org/wiki/Diocese"}, {"title": "Udine", "link": "https://wikipedia.org/wiki/Udine"}, {"title": "Gorizia", "link": "https://wikipedia.org/wiki/Gorizia"}]}, {"year": "1777", "text": "American Revolutionary War: Siege of Fort Ticonderoga: After a bombardment by British artillery under General <PERSON>, American forces retreat from Fort Ticonderoga, New York.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Ticonderoga_(1777)\" title=\"Siege of Fort Ticonderoga (1777)\">Siege of Fort Ticonderoga</a>: After a bombardment by British <a href=\"https://wikipedia.org/wiki/Artillery\" title=\"Artillery\">artillery</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">American forces</a> retreat from <a href=\"https://wikipedia.org/wiki/Fort_Ticonderoga\" title=\"Fort Ticonderoga\">Fort Ticonderoga</a>, New York.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Ticonderoga_(1777)\" title=\"Siege of Fort Ticonderoga (1777)\">Siege of Fort Ticonderoga</a>: After a bombardment by British <a href=\"https://wikipedia.org/wiki/Artillery\" title=\"Artillery\">artillery</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">American forces</a> retreat from <a href=\"https://wikipedia.org/wiki/Fort_Ticonderoga\" title=\"Fort Ticonderoga\">Fort Ticonderoga</a>, New York.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Siege of Fort Ticonderoga (1777)", "link": "https://wikipedia.org/wiki/Siege_of_Fort_Ticonderoga_(1777)"}, {"title": "Artillery", "link": "https://wikipedia.org/wiki/Artillery"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Fort Ticonderoga", "link": "https://wikipedia.org/wiki/Fort_Ticonderoga"}]}, {"year": "1779", "text": "Battle of Grenada: The French defeat British naval forces in the Caribbean during the American Revolutionary War.", "html": "1779 - <a href=\"https://wikipedia.org/wiki/Battle_of_Grenada\" title=\"Battle of Grenada\">Battle of Grenada</a>: The French defeat British naval forces in the <a href=\"https://wikipedia.org/wiki/Caribbean\" title=\"Caribbean\">Caribbean</a> during the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Grenada\" title=\"Battle of Grenada\">Battle of Grenada</a>: The French defeat British naval forces in the <a href=\"https://wikipedia.org/wiki/Caribbean\" title=\"Caribbean\">Caribbean</a> during the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>.", "links": [{"title": "Battle of Grenada", "link": "https://wikipedia.org/wiki/Battle_of_Grenada"}, {"title": "Caribbean", "link": "https://wikipedia.org/wiki/Caribbean"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1791", "text": "At Padua, the Emperor <PERSON> calls on the monarchs of Europe to join him in demanding the king of France <PERSON>'s freedom.", "html": "1791 - At <a href=\"https://wikipedia.org/wiki/Padua\" title=\"Padua\">Padua</a>, the Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> calls on the monarchs of Europe to join him in demanding the king of France <a href=\"https://wikipedia.org/wiki/<PERSON>_XVI\" title=\"<PERSON> XVI\"><PERSON></a>'s freedom.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/Padua\" title=\"Padua\">Padua</a>, the Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> calls on the monarchs of Europe to join him in demanding the king of France <a href=\"https://wikipedia.org/wiki/<PERSON>_XVI\" title=\"Louis XVI\"><PERSON></a>'s freedom.", "links": [{"title": "Padua", "link": "https://wikipedia.org/wiki/Padua"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "First Battle of Algeciras: Outnumbered French Navy ships defeat the Royal Navy in the fortified Spanish port of Algeciras.", "html": "1801 - <a href=\"https://wikipedia.org/wiki/First_Battle_of_Algeciras\" title=\"First Battle of Algeciras\">First Battle of Algeciras</a>: Outnumbered <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">French Navy</a> ships defeat the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> in the fortified Spanish port of <a href=\"https://wikipedia.org/wiki/Algeciras\" title=\"Algeciras\">Algeciras</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Battle_of_Algeciras\" title=\"First Battle of Algeciras\">First Battle of Algeciras</a>: Outnumbered <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">French Navy</a> ships defeat the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> in the fortified Spanish port of <a href=\"https://wikipedia.org/wiki/Algeciras\" title=\"Algeciras\">Algeciras</a>.", "links": [{"title": "First Battle of Algeciras", "link": "https://wikipedia.org/wiki/First_Battle_of_Algeciras"}, {"title": "French Navy", "link": "https://wikipedia.org/wiki/French_Navy"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Algeciras", "link": "https://wikipedia.org/wiki/Algeciras"}]}, {"year": "1809", "text": "The second day of the Battle of Wagram; France defeats the Austrian army in the largest battle to date of the Napoleonic Wars.", "html": "1809 - The second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Wagram\" title=\"Battle of Wagram\">Battle of Wagram</a>; France defeats the Austrian army in the largest battle to date of the Napoleonic Wars.", "no_year_html": "The second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Wagram\" title=\"Battle of Wagram\">Battle of Wagram</a>; France defeats the Austrian army in the largest battle to date of the Napoleonic Wars.", "links": [{"title": "Battle of Wagram", "link": "https://wikipedia.org/wiki/Battle_of_Wagram"}]}, {"year": "1854", "text": "The Republican Party of the United States held its first convention in Jackson, Michigan.", "html": "1854 - The <a href=\"https://wikipedia.org/wiki/Republican_Party_(United_States)\" title=\"Republican Party (United States)\">Republican Party</a> of the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> held its first convention in <a href=\"https://wikipedia.org/wiki/Jackson,_Michigan\" title=\"Jackson, Michigan\">Jackson, Michigan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republican_Party_(United_States)\" title=\"Republican Party (United States)\">Republican Party</a> of the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> held its first convention in <a href=\"https://wikipedia.org/wiki/Jackson,_Michigan\" title=\"Jackson, Michigan\">Jackson, Michigan</a>.", "links": [{"title": "Republican Party (United States)", "link": "https://wikipedia.org/wiki/Republican_Party_(United_States)"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Jackson, Michigan", "link": "https://wikipedia.org/wiki/Jackson,_Michigan"}]}, {"year": "1885", "text": "<PERSON> successfully tests his vaccine against rabies on <PERSON>, a boy who was bitten by a rabid dog.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> successfully tests his <a href=\"https://wikipedia.org/wiki/Vaccine\" title=\"Vaccine\">vaccine</a> against <a href=\"https://wikipedia.org/wiki/Rabies\" title=\"Rabies\">rabies</a> on <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a boy who was bitten by a rabid dog.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> successfully tests his <a href=\"https://wikipedia.org/wiki/Vaccine\" title=\"Vaccine\">vaccine</a> against <a href=\"https://wikipedia.org/wiki/Rabies\" title=\"Rabies\">rabies</a> on <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a boy who was bitten by a rabid dog.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Vaccine", "link": "https://wikipedia.org/wiki/Vaccine"}, {"title": "Rabies", "link": "https://wikipedia.org/wiki/Rabies"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, monarch of the Kingdom of Hawaii, is forced to sign the Bayonet Constitution, which transfers much of the king's authority to the Legislature of the Kingdom of Hawaii.", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%81ka<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, monarch of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Kingdom of Hawaii\">Kingdom of Hawaii</a>, is forced to sign the <a href=\"https://wikipedia.org/wiki/Bayonet_Constitution\" class=\"mw-redirect\" title=\"Bayonet Constitution\">Bayonet Constitution</a>, which transfers much of the king's authority to the <a href=\"https://wikipedia.org/wiki/Legislature_of_the_Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Legislature of the Kingdom of Hawaii\">Legislature of the Kingdom of Hawaii</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%81kaua\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, monarch of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Kingdom of Hawaii\">Kingdom of Hawaii</a>, is forced to sign the <a href=\"https://wikipedia.org/wiki/Bayonet_Constitution\" class=\"mw-redirect\" title=\"Bayonet Constitution\">Bayonet Constitution</a>, which transfers much of the king's authority to the <a href=\"https://wikipedia.org/wiki/Legislature_of_the_Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Legislature of the Kingdom of Hawaii\">Legislature of the Kingdom of Hawaii</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%81kaua"}, {"title": "Kingdom of Hawaii", "link": "https://wikipedia.org/wiki/Kingdom_of_Hawaii"}, {"title": "Bayonet Constitution", "link": "https://wikipedia.org/wiki/Bayonet_Constitution"}, {"title": "Legislature of the Kingdom of Hawaii", "link": "https://wikipedia.org/wiki/Legislature_of_the_Kingdom_of_Hawaii"}]}, {"year": "1892", "text": "Three thousand eight hundred striking steelworkers engage in a day-long battle with Pinkerton agents during the Homestead Strike, leaving ten dead and dozens wounded.", "html": "1892 - Three thousand eight hundred <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">striking</a> steelworkers engage in a day-long battle with <a href=\"https://wikipedia.org/wiki/Pinkerton_National_Detective_Agency\" class=\"mw-redirect\" title=\"Pinkerton National Detective Agency\">Pinkerton</a> agents during the <a href=\"https://wikipedia.org/wiki/Homestead_Strike\" class=\"mw-redirect\" title=\"Homestead Strike\">Homestead Strike</a>, leaving ten dead and dozens wounded.", "no_year_html": "Three thousand eight hundred <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">striking</a> steelworkers engage in a day-long battle with <a href=\"https://wikipedia.org/wiki/Pinkerton_National_Detective_Agency\" class=\"mw-redirect\" title=\"Pinkerton National Detective Agency\">Pinkerton</a> agents during the <a href=\"https://wikipedia.org/wiki/Homestead_Strike\" class=\"mw-redirect\" title=\"Homestead Strike\">Homestead Strike</a>, leaving ten dead and dozens wounded.", "links": [{"title": "Strike action", "link": "https://wikipedia.org/wiki/Strike_action"}, {"title": "Pinkerton National Detective Agency", "link": "https://wikipedia.org/wiki/Pinkerton_National_Detective_Agency"}, {"title": "Homestead Strike", "link": "https://wikipedia.org/wiki/Homestead_Strike"}]}, {"year": "1917", "text": "World War I: Arabian troops led by <PERSON><PERSON> <PERSON><PERSON> (\"Lawrence of Arabia\") and <PERSON><PERSON> <PERSON><PERSON> capture Aqaba from the Ottoman Empire during the Arab Revolt.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Arab\" class=\"mw-redirect\" title=\"Arab\">Arabian</a> troops led by <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> (\"Lawrence of Arabia\") and <a href=\"https://wikipedia.org/wiki/Auda_ibu_Tayi\" class=\"mw-redirect\" title=\"Auda ibu Tayi\">Auda ibu Tayi</a> capture <a href=\"https://wikipedia.org/wiki/Battle_of_Aqaba\" title=\"Battle of Aqaba\">Aqaba</a> from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> during the <a href=\"https://wikipedia.org/wiki/Arab_Revolt\" title=\"Arab Revolt\">Arab Revolt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Arab\" class=\"mw-redirect\" title=\"Arab\">Arabian</a> troops led by <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> (\"Lawrence of Arabia\") and <a href=\"https://wikipedia.org/wiki/Auda_ibu_Tayi\" class=\"mw-redirect\" title=\"Auda ibu Tayi\">Auda ibu Tayi</a> capture <a href=\"https://wikipedia.org/wiki/Battle_of_Aqaba\" title=\"Battle of Aqaba\">Aqaba</a> from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> during the <a href=\"https://wikipedia.org/wiki/Arab_Revolt\" title=\"Arab Revolt\">Arab Revolt</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Arab", "link": "https://wikipedia.org/wiki/Arab"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Auda_ibu_Tayi"}, {"title": "Battle of Aqaba", "link": "https://wikipedia.org/wiki/Battle_of_Aqaba"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Arab Revolt", "link": "https://wikipedia.org/wiki/Arab_Revolt"}]}, {"year": "1918", "text": "The Left SR uprising in Russia starts with the assassination of German ambassador <PERSON> by Cheka members.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Left_SR_uprising\" title=\"Left SR uprising\">Left SR uprising</a> in Russia starts with the assassination of German ambassador <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> by <a href=\"https://wikipedia.org/wiki/Cheka\" title=\"Cheka\">Cheka</a> members.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Left_SR_uprising\" title=\"Left SR uprising\">Left SR uprising</a> in Russia starts with the assassination of German ambassador <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> by <a href=\"https://wikipedia.org/wiki/Cheka\" title=\"Cheka\">Cheka</a> members.", "links": [{"title": "Left SR uprising", "link": "https://wikipedia.org/wiki/Left_SR_uprising"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cheka", "link": "https://wikipedia.org/wiki/Cheka"}]}, {"year": "1919", "text": "The British dirigible R34 lands in New York, completing the first crossing of the Atlantic Ocean by an airship.", "html": "1919 - The British <a href=\"https://wikipedia.org/wiki/R34_(airship)\" class=\"mw-redirect\" title=\"R34 (airship)\">dirigible R34</a> lands in New York, completing the first crossing of the Atlantic Ocean by an airship.", "no_year_html": "The British <a href=\"https://wikipedia.org/wiki/R34_(airship)\" class=\"mw-redirect\" title=\"R34 (airship)\">dirigible R34</a> lands in New York, completing the first crossing of the Atlantic Ocean by an airship.", "links": [{"title": "R34 (airship)", "link": "https://wikipedia.org/wiki/R34_(airship)"}]}, {"year": "1933", "text": "The first Major League Baseball All-Star Game is played in Chicago's Comiskey Park. The American League defeated the National League 4-2.", "html": "1933 - The first <a href=\"https://wikipedia.org/wiki/Major_League_Baseball_All-Star_Game\" title=\"Major League Baseball All-Star Game\">Major League Baseball All-Star Game</a> is played in Chicago's <a href=\"https://wikipedia.org/wiki/Comiskey_Park\" title=\"Comiskey Park\">Comiskey Park</a>. The <a href=\"https://wikipedia.org/wiki/American_League\" title=\"American League\">American League</a> defeated the <a href=\"https://wikipedia.org/wiki/National_League_(baseball)\" title=\"National League (baseball)\">National League</a> 4-2.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Major_League_Baseball_All-Star_Game\" title=\"Major League Baseball All-Star Game\">Major League Baseball All-Star Game</a> is played in Chicago's <a href=\"https://wikipedia.org/wiki/Comiskey_Park\" title=\"Comiskey Park\">Comiskey Park</a>. The <a href=\"https://wikipedia.org/wiki/American_League\" title=\"American League\">American League</a> defeated the <a href=\"https://wikipedia.org/wiki/National_League_(baseball)\" title=\"National League (baseball)\">National League</a> 4-2.", "links": [{"title": "Major League Baseball All-Star Game", "link": "https://wikipedia.org/wiki/Major_League_Baseball_All-Star_Game"}, {"title": "Comiskey Park", "link": "https://wikipedia.org/wiki/Comiskey_Park"}, {"title": "American League", "link": "https://wikipedia.org/wiki/American_League"}, {"title": "National League (baseball)", "link": "https://wikipedia.org/wiki/National_League_(baseball)"}]}, {"year": "1936", "text": "A major breach of the Manchester Bolton & Bury Canal in England sends millions of gallons of water cascading 200 feet (61 m) into the River Irwell.", "html": "1936 - A major breach of the <a href=\"https://wikipedia.org/wiki/Manchester_Bolton_%26_Bury_Canal\" title=\"Manchester Bolton &amp; Bury Canal\">Manchester Bolton &amp; Bury Canal</a> in England sends millions of gallons of water cascading 200 feet (61 m) into the <a href=\"https://wikipedia.org/wiki/River_Irwell\" title=\"River Irwell\">River Irwell</a>.", "no_year_html": "A major breach of the <a href=\"https://wikipedia.org/wiki/Manchester_Bolton_%26_Bury_Canal\" title=\"Manchester Bolton &amp; Bury Canal\">Manchester Bolton &amp; Bury Canal</a> in England sends millions of gallons of water cascading 200 feet (61 m) into the <a href=\"https://wikipedia.org/wiki/River_Irwell\" title=\"River Irwell\">River Irwell</a>.", "links": [{"title": "Manchester Bolton & Bury Canal", "link": "https://wikipedia.org/wiki/Manchester_Bolton_%26_Bury_Canal"}, {"title": "River Irwell", "link": "https://wikipedia.org/wiki/River_Irwell"}]}, {"year": "1937", "text": "Spanish Civil War: Battle of Brunete: The battle begins with Spanish Republican troops going on the offensive against the Nationalists to relieve pressure on Madrid.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Brunete\" title=\"Battle of Brunete\">Battle of Brunete</a>: The battle begins with Spanish Republican troops going on the offensive against the Nationalists to relieve pressure on Madrid.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Brunete\" title=\"Battle of Brunete\">Battle of Brunete</a>: The battle begins with Spanish Republican troops going on the offensive against the Nationalists to relieve pressure on Madrid.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Battle of Brunete", "link": "https://wikipedia.org/wiki/Battle_of_Brunete"}]}, {"year": "1939", "text": "Anti-Jewish legislation in prewar Nazi Germany closes the last remaining Jewish enterprises.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Anti-Jewish_legislation_in_prewar_Nazi_Germany\" class=\"mw-redirect\" title=\"Anti-Jewish legislation in prewar Nazi Germany\">Anti-Jewish legislation in prewar Nazi Germany</a> closes the last remaining Jewish enterprises.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anti-Jewish_legislation_in_prewar_Nazi_Germany\" class=\"mw-redirect\" title=\"Anti-Jewish legislation in prewar Nazi Germany\">Anti-Jewish legislation in prewar Nazi Germany</a> closes the last remaining Jewish enterprises.", "links": [{"title": "Anti-Jewish legislation in prewar Nazi Germany", "link": "https://wikipedia.org/wiki/Anti-Jewish_legislation_in_prewar_Nazi_Germany"}]}, {"year": "1940", "text": "Story Bridge, a major landmark in Brisbane, as well as Australia's longest cantilever bridge is formally opened.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Story_Bridge\" title=\"Story Bridge\">Story Bridge</a>, a major landmark in <a href=\"https://wikipedia.org/wiki/Brisbane\" title=\"Brisbane\">Brisbane</a>, as well as Australia's longest cantilever bridge is formally opened.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Story_Bridge\" title=\"Story Bridge\">Story Bridge</a>, a major landmark in <a href=\"https://wikipedia.org/wiki/Brisbane\" title=\"Brisbane\">Brisbane</a>, as well as Australia's longest cantilever bridge is formally opened.", "links": [{"title": "Story Bridge", "link": "https://wikipedia.org/wiki/Story_Bridge"}, {"title": "Brisbane", "link": "https://wikipedia.org/wiki/Brisbane"}]}, {"year": "1941", "text": "World War II: The German army launches its offensive to encircle several Soviet armies near Smolensk.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German army launches its <a href=\"https://wikipedia.org/wiki/Battle_of_Smolensk_(1941)\" title=\"Battle of Smolensk (1941)\">offensive to encircle several Soviet armies</a> near <a href=\"https://wikipedia.org/wiki/Smolensk\" title=\"Smolensk\">Smolensk</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German army launches its <a href=\"https://wikipedia.org/wiki/Battle_of_Smolensk_(1941)\" title=\"Battle of Smolensk (1941)\">offensive to encircle several Soviet armies</a> near <a href=\"https://wikipedia.org/wiki/Smolensk\" title=\"Smolensk\">Smolensk</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Smolensk (1941)", "link": "https://wikipedia.org/wiki/Battle_of_Smolensk_(1941)"}, {"title": "Smolensk", "link": "https://wikipedia.org/wiki/Smolensk"}]}, {"year": "1942", "text": "<PERSON> and her family go into hiding in the \"Secret Annexe\" above her father's office in an Amsterdam warehouse.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and her family go into hiding in the \"<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anne <PERSON>\"><PERSON></a>\" above her father's office in an <a href=\"https://wikipedia.org/wiki/Amsterdam\" title=\"Amsterdam\">Amsterdam</a> warehouse.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and her family go into hiding in the \"<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Anne <PERSON>\"><PERSON></a>\" above her father's office in an <a href=\"https://wikipedia.org/wiki/Amsterdam\" title=\"Amsterdam\">Amsterdam</a> warehouse.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Amsterdam", "link": "https://wikipedia.org/wiki/Amsterdam"}]}, {"year": "1944", "text": "<PERSON> refuses to move to the back of a bus, leading to a court-martial.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> refuses to move to the back of a bus, leading to a <a href=\"https://wikipedia.org/wiki/Court-martial\" title=\"Court-martial\">court-martial</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> refuses to move to the back of a bus, leading to a <a href=\"https://wikipedia.org/wiki/Court-martial\" title=\"Court-martial\">court-martial</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Court-martial", "link": "https://wikipedia.org/wiki/Court-martial"}]}, {"year": "1944", "text": "The Hartford circus fire, one of America's worst fire disasters, kills approximately 168 people and injures over 700 in Hartford, Connecticut.", "html": "1944 - The <a href=\"https://wikipedia.org/wiki/Hartford_circus_fire\" title=\"Hartford circus fire\">Hartford circus fire</a>, one of America's worst <a href=\"https://wikipedia.org/wiki/List_of_fires\" title=\"List of fires\">fire disasters</a>, kills approximately 168 people and injures over 700 in <a href=\"https://wikipedia.org/wiki/Hartford,_Connecticut\" title=\"Hartford, Connecticut\">Hartford, Connecticut</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hartford_circus_fire\" title=\"Hartford circus fire\">Hartford circus fire</a>, one of America's worst <a href=\"https://wikipedia.org/wiki/List_of_fires\" title=\"List of fires\">fire disasters</a>, kills approximately 168 people and injures over 700 in <a href=\"https://wikipedia.org/wiki/Hartford,_Connecticut\" title=\"Hartford, Connecticut\">Hartford, Connecticut</a>.", "links": [{"title": "Hartford circus fire", "link": "https://wikipedia.org/wiki/Hartford_circus_fire"}, {"title": "List of fires", "link": "https://wikipedia.org/wiki/List_of_fires"}, {"title": "Hartford, Connecticut", "link": "https://wikipedia.org/wiki/Hartford,_Connecticut"}]}, {"year": "1947", "text": "Referendum held in Sylhet to decide its fate in the Partition of India.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/1947_Sylhet_referendum\" title=\"1947 Sylhet referendum\">Referendum</a> held in <a href=\"https://wikipedia.org/wiki/Sylhet_region\" class=\"mw-redirect\" title=\"Sylhet region\">Sylhet</a> to decide its fate in the <a href=\"https://wikipedia.org/wiki/Partition_of_India\" title=\"Partition of India\">Partition of India</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1947_Sylhet_referendum\" title=\"1947 Sylhet referendum\">Referendum</a> held in <a href=\"https://wikipedia.org/wiki/Sylhet_region\" class=\"mw-redirect\" title=\"Sylhet region\">Sylhet</a> to decide its fate in the <a href=\"https://wikipedia.org/wiki/Partition_of_India\" title=\"Partition of India\">Partition of India</a>.", "links": [{"title": "1947 Sylhet referendum", "link": "https://wikipedia.org/wiki/1947_Sylhet_referendum"}, {"title": "Sylhet region", "link": "https://wikipedia.org/wiki/Sylhet_region"}, {"title": "Partition of India", "link": "https://wikipedia.org/wiki/Partition_of_India"}]}, {"year": "1947", "text": "The AK-47 goes into production in the Soviet Union.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/AK-47\" title=\"AK-47\">AK-47</a> goes into production in the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/AK-47\" title=\"AK-47\">AK-47</a> goes into production in the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "AK-47", "link": "https://wikipedia.org/wiki/AK-47"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1957", "text": "<PERSON><PERSON> wins at the Wimbledon Championships, becoming the first black athlete to do so.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> wins at the <a href=\"https://wikipedia.org/wiki/Wimbledon_Championships\" title=\"Wimbledon Championships\">Wimbledon Championships</a>, becoming the first black athlete to do so.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> wins at the <a href=\"https://wikipedia.org/wiki/Wimbledon_Championships\" title=\"Wimbledon Championships\">Wimbledon Championships</a>, becoming the first black athlete to do so.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Wimbledon Championships", "link": "https://wikipedia.org/wiki/Wimbledon_Championships"}]}, {"year": "1957", "text": "<PERSON> and <PERSON> meet for the first time, as teenagers at Woolton Fete, three years before forming the Beatles.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> meet for the first time, as teenagers at Woolton Fete, three years before forming <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">the Beatles</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> meet for the first time, as teenagers at Woolton Fete, three years before forming <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">the Beatles</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}]}, {"year": "1962", "text": "As a part of Operation Plowshare, the Sedan nuclear test takes place.", "html": "1962 - As a part of <a href=\"https://wikipedia.org/wiki/Operation_Plowshare\" class=\"mw-redirect\" title=\"Operation Plowshare\">Operation Plowshare</a>, the <a href=\"https://wikipedia.org/wiki/Sedan_(nuclear_test)\" title=\"Sedan (nuclear test)\">Sedan nuclear test</a> takes place.", "no_year_html": "As a part of <a href=\"https://wikipedia.org/wiki/Operation_Plowshare\" class=\"mw-redirect\" title=\"Operation Plowshare\">Operation Plowshare</a>, the <a href=\"https://wikipedia.org/wiki/Sedan_(nuclear_test)\" title=\"Sedan (nuclear test)\">Sedan nuclear test</a> takes place.", "links": [{"title": "Operation Plowshare", "link": "https://wikipedia.org/wiki/Operation_Plowshare"}, {"title": "Sedan (nuclear test)", "link": "https://wikipedia.org/wiki/Sedan_(nuclear_test)"}]}, {"year": "1962", "text": "The Late Late Show, the world's longest-running chat show by the same broadcaster, airs on RTÉ One for the first time.", "html": "1962 - <i><a href=\"https://wikipedia.org/wiki/The_Late_Late_Show_(Ireland)\" class=\"mw-redirect\" title=\"The Late Late Show (Ireland)\">The Late Late Show</a></i>, the world's longest-running <a href=\"https://wikipedia.org/wiki/Talk_show\" title=\"Talk show\">chat show</a> by the same broadcaster, airs on <a href=\"https://wikipedia.org/wiki/RT%C3%89_One\" title=\"RTÉ One\">RTÉ One</a> for the first time.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Late_Late_Show_(Ireland)\" class=\"mw-redirect\" title=\"The Late Late Show (Ireland)\">The Late Late Show</a></i>, the world's longest-running <a href=\"https://wikipedia.org/wiki/Talk_show\" title=\"Talk show\">chat show</a> by the same broadcaster, airs on <a href=\"https://wikipedia.org/wiki/RT%C3%89_One\" title=\"RTÉ One\">RTÉ One</a> for the first time.", "links": [{"title": "The Late Late Show (Ireland)", "link": "https://wikipedia.org/wiki/The_Late_Late_Show_(Ireland)"}, {"title": "Talk show", "link": "https://wikipedia.org/wiki/Talk_show"}, {"title": "RTÉ One", "link": "https://wikipedia.org/wiki/RT%C3%89_One"}]}, {"year": "1964", "text": "Malawi declares its independence from the United Kingdom.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Malawi\" title=\"Malawi\">Malawi</a> declares its independence from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malawi\" title=\"Malawi\">Malawi</a> declares its independence from the United Kingdom.", "links": [{"title": "Malawi", "link": "https://wikipedia.org/wiki/Malawi"}]}, {"year": "1966", "text": "Malawi becomes a republic, with <PERSON> Banda as its first President.", "html": "1966 - Malawi becomes a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a>, with <a href=\"https://wikipedia.org/wiki/Hastings_Banda\" title=\"Hastings Banda\">Hastings Banda</a> as its first <a href=\"https://wikipedia.org/wiki/President_of_Malawi\" title=\"President of Malawi\">President</a>.", "no_year_html": "Malawi becomes a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a>, with <a href=\"https://wikipedia.org/wiki/Hastings_Banda\" title=\"Hastings Banda\">Hastings Banda</a> as its first <a href=\"https://wikipedia.org/wiki/President_of_Malawi\" title=\"President of Malawi\">President</a>.", "links": [{"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}, {"title": "Hastings Banda", "link": "https://wikipedia.org/wiki/Hastings_Banda"}, {"title": "President of Malawi", "link": "https://wikipedia.org/wiki/President_of_Malawi"}]}, {"year": "1967", "text": "Nigerian Civil War: Nigerian forces invade Biafra, beginning the war.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Nigerian_Civil_War\" title=\"Nigerian Civil War\">Nigerian Civil War</a>: <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigerian</a> forces invade <a href=\"https://wikipedia.org/wiki/Biafra\" title=\"Biafra\">Biafra</a>, beginning the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nigerian_Civil_War\" title=\"Nigerian Civil War\">Nigerian Civil War</a>: <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigerian</a> forces invade <a href=\"https://wikipedia.org/wiki/Biafra\" title=\"Biafra\">Biafra</a>, beginning the war.", "links": [{"title": "Nigerian Civil War", "link": "https://wikipedia.org/wiki/Nigerian_Civil_War"}, {"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "Biafra", "link": "https://wikipedia.org/wiki/Biafra"}]}, {"year": "1975", "text": "The Comoros declares independence from France.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Comoros\" title=\"Comoros\">Comoros</a> declares independence from France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Comoros\" title=\"Comoros\">Comoros</a> declares independence from France.", "links": [{"title": "Comoros", "link": "https://wikipedia.org/wiki/Comoros"}]}, {"year": "1982", "text": "While attempting to return to Sheremetyevo International Airport, Aeroflot Flight 411, an Ilyushin Il-62, crashes near Mendeleyevo, Moscow Oblast, killing all 90 people on board.", "html": "1982 - While attempting to return to <a href=\"https://wikipedia.org/wiki/Sheremetyevo_International_Airport\" title=\"Sheremetyevo International Airport\">Sheremetyevo International Airport</a>, <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_411\" title=\"Aeroflot Flight 411\">Aeroflot Flight 411</a>, an <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-62\" title=\"Ilyushin Il-62\">Ilyushin Il-62</a>, crashes near <a href=\"https://wikipedia.org/wiki/Mendeleyevo,_Moscow_Oblast\" title=\"Mendeleyevo, Moscow Oblast\">Mendeleyevo, Moscow Oblast</a>, killing all 90 people on board.", "no_year_html": "While attempting to return to <a href=\"https://wikipedia.org/wiki/Sheremetyevo_International_Airport\" title=\"Sheremetyevo International Airport\">Sheremetyevo International Airport</a>, <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_411\" title=\"Aeroflot Flight 411\">Aeroflot Flight 411</a>, an <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-62\" title=\"Ilyushin Il-62\">Ilyushin Il-62</a>, crashes near <a href=\"https://wikipedia.org/wiki/Mendeleyevo,_Moscow_Oblast\" title=\"Mendeleyevo, Moscow Oblast\">Mendeleyevo, Moscow Oblast</a>, killing all 90 people on board.", "links": [{"title": "Sheremetyevo International Airport", "link": "https://wikipedia.org/wiki/Sheremetyevo_International_Airport"}, {"title": "Aeroflot Flight 411", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_411"}, {"title": "Ilyushin Il-62", "link": "https://wikipedia.org/wiki/Ilyushin_Il-62"}, {"title": "Mendeleyevo, Moscow Oblast", "link": "https://wikipedia.org/wiki/Mendeleyevo,_Moscow_Oblast"}]}, {"year": "1988", "text": "The Piper Alpha drilling platform in the North Sea is destroyed by explosions and fires. One hundred sixty-seven oil workers are killed, making it the world's worst offshore oil disaster in terms of direct loss of life.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/Piper_Alpha\" title=\"Piper Alpha\">Piper Alpha</a> drilling platform in the <a href=\"https://wikipedia.org/wiki/North_Sea\" title=\"North Sea\">North Sea</a> is destroyed by explosions and fires. One hundred sixty-seven oil workers are killed, making it the world's worst offshore oil disaster in terms of direct loss of life.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Piper_Alpha\" title=\"Piper Alpha\">Piper Alpha</a> drilling platform in the <a href=\"https://wikipedia.org/wiki/North_Sea\" title=\"North Sea\">North Sea</a> is destroyed by explosions and fires. One hundred sixty-seven oil workers are killed, making it the world's worst offshore oil disaster in terms of direct loss of life.", "links": [{"title": "Piper Alpha", "link": "https://wikipedia.org/wiki/Piper_Alpha"}, {"title": "North Sea", "link": "https://wikipedia.org/wiki/North_Sea"}]}, {"year": "1989", "text": "The Tel Aviv-Jerusalem bus 405 suicide attack: Sixteen bus passengers are killed when a member of the Palestinian Islamic Jihad took control of the bus and drove it over a cliff.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/Tel_Aviv%E2%80%93Jerusalem_bus_405_suicide_attack\" class=\"mw-redirect\" title=\"Tel Aviv-Jerusalem bus 405 suicide attack\">Tel Aviv-Jerusalem bus 405 suicide attack</a>: Sixteen bus passengers are killed when a member of the Palestinian Islamic Jihad took control of the bus and drove it over a cliff.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tel_Aviv%E2%80%93Jerusalem_bus_405_suicide_attack\" class=\"mw-redirect\" title=\"Tel Aviv-Jerusalem bus 405 suicide attack\">Tel Aviv-Jerusalem bus 405 suicide attack</a>: Sixteen bus passengers are killed when a member of the Palestinian Islamic Jihad took control of the bus and drove it over a cliff.", "links": [{"title": "Tel Aviv-Jerusalem bus 405 suicide attack", "link": "https://wikipedia.org/wiki/Tel_Aviv%E2%80%93Jerusalem_bus_405_suicide_attack"}]}, {"year": "1995", "text": "In the Bosnian War, under the command of General <PERSON><PERSON>, Serbia begins its attack on the Bosnian town of Srebrenica.", "html": "1995 - In the <a href=\"https://wikipedia.org/wiki/Bosnian_War\" title=\"Bosnian War\">Bosnian War</a>, under the command of General <a href=\"https://wikipedia.org/wiki/Rat<PERSON>_Mladi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbia begins its attack on the Bosnian town of <a href=\"https://wikipedia.org/wiki/Srebrenica\" title=\"Srebrenica\">Srebrenica</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Bosnian_War\" title=\"Bosnian War\">Bosnian War</a>, under the command of General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mladi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbia begins its attack on the Bosnian town of <a href=\"https://wikipedia.org/wiki/Srebrenica\" title=\"Srebrenica\">Srebrenica</a>.", "links": [{"title": "Bosnian War", "link": "https://wikipedia.org/wiki/Bosnian_War"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ratko_Mladi%C4%87"}, {"title": "Srebrenica", "link": "https://wikipedia.org/wiki/Srebrenica"}]}, {"year": "1996", "text": "A McDonnell Douglas MD-88 operating as Delta Air Lines Flight 1288 experiences a turbine engine failure during takeoff from Pensacola International Airport, killing two and injuring five of the 147 people on board.", "html": "1996 - A <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-88</a> operating as <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines_Flight_1288\" title=\"Delta Air Lines Flight 1288\">Delta Air Lines Flight 1288</a> experiences a <a href=\"https://wikipedia.org/wiki/Turbine_engine_failure\" title=\"Turbine engine failure\">turbine engine failure</a> during takeoff from <a href=\"https://wikipedia.org/wiki/Pensacola_International_Airport\" title=\"Pensacola International Airport\">Pensacola International Airport</a>, killing two and injuring five of the 147 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-88</a> operating as <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines_Flight_1288\" title=\"Delta Air Lines Flight 1288\">Delta Air Lines Flight 1288</a> experiences a <a href=\"https://wikipedia.org/wiki/Turbine_engine_failure\" title=\"Turbine engine failure\">turbine engine failure</a> during takeoff from <a href=\"https://wikipedia.org/wiki/Pensacola_International_Airport\" title=\"Pensacola International Airport\">Pensacola International Airport</a>, killing two and injuring five of the 147 people on board.", "links": [{"title": "McDonnell Douglas MD-80", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-80"}, {"title": "Delta Air Lines Flight 1288", "link": "https://wikipedia.org/wiki/Delta_Air_Lines_Flight_1288"}, {"title": "Turbine engine failure", "link": "https://wikipedia.org/wiki/Turbine_engine_failure"}, {"title": "Pensacola International Airport", "link": "https://wikipedia.org/wiki/Pensacola_International_Airport"}]}, {"year": "1997", "text": "The Troubles: In response to the Drumcree dispute, five days of mass protests, riots and gun battles begin in Irish nationalist districts of Northern Ireland.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: In response to the <a href=\"https://wikipedia.org/wiki/Drumcree_conflict\" title=\"Drumcree conflict\">Drumcree dispute</a>, five days of <a href=\"https://wikipedia.org/wiki/1997_Northern_Ireland_riots\" title=\"1997 Northern Ireland riots\">mass protests, riots and gun battles</a> begin in Irish nationalist districts of <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: In response to the <a href=\"https://wikipedia.org/wiki/Drumcree_conflict\" title=\"Drumcree conflict\">Drumcree dispute</a>, five days of <a href=\"https://wikipedia.org/wiki/1997_Northern_Ireland_riots\" title=\"1997 Northern Ireland riots\">mass protests, riots and gun battles</a> begin in Irish nationalist districts of <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Drumcree conflict", "link": "https://wikipedia.org/wiki/Drumcree_conflict"}, {"title": "1997 Northern Ireland riots", "link": "https://wikipedia.org/wiki/1997_Northern_Ireland_riots"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "1998", "text": "Hong Kong International Airport opens in Chek Lap Kok, Hong Kong, replacing Kai Tak Airport as the city's international airport.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Hong_Kong_International_Airport\" title=\"Hong Kong International Airport\">Hong Kong International Airport</a> opens in <a href=\"https://wikipedia.org/wiki/Chek_<PERSON>_<PERSON>\" title=\"Chek <PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Hong_Kong\" title=\"Hong Kong\">Hong Kong</a>, replacing <a href=\"https://wikipedia.org/wiki/Kai_Tak_Airport\" title=\"Kai Tak Airport\">Kai Tak Airport</a> as the city's international airport.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hong_Kong_International_Airport\" title=\"Hong Kong International Airport\">Hong Kong International Airport</a> opens in <a href=\"https://wikipedia.org/wiki/Chek_<PERSON>_<PERSON>\" title=\"Chek <PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Hong_Kong\" title=\"Hong Kong\">Hong Kong</a>, replacing <a href=\"https://wikipedia.org/wiki/Kai_Tak_Airport\" title=\"Kai Tak Airport\">Kai Tak Airport</a> as the city's international airport.", "links": [{"title": "Hong Kong International Airport", "link": "https://wikipedia.org/wiki/Hong_Kong_International_Airport"}, {"title": "Chek <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ek_<PERSON><PERSON>_<PERSON>"}, {"title": "Hong Kong", "link": "https://wikipedia.org/wiki/Hong_Kong"}, {"title": "Kai Tak Airport", "link": "https://wikipedia.org/wiki/Kai_Tak_Airport"}]}, {"year": "2003", "text": "The 70-metre Yevpatoria Planetary Radar sends a METI message (Cosmic Call 2) to five stars: Hip 4872, HD 245409, 55 Cancri (HD 75732), HD 10307 and 47 Ursae Majoris (HD 95128). The messages will arrive to these stars in 2036, 2040, 2044, and 2049, respectively.", "html": "2003 - The 70-metre <a href=\"https://wikipedia.org/wiki/Yevpatoria\" title=\"Yevpatoria\">Yevpatoria</a> Planetary Radar sends a <a href=\"https://wikipedia.org/wiki/Message_to_Extra-Terrestrial_Intelligence\" class=\"mw-redirect\" title=\"Message to Extra-Terrestrial Intelligence\">METI</a> message (<a href=\"https://wikipedia.org/wiki/Cosmic_Call_2\" class=\"mw-redirect\" title=\"Cosmic Call 2\">Cosmic Call 2</a>) to five stars: <a href=\"https://wikipedia.org/wiki/Cassiopeia_(constellation)\" title=\"Cassiopeia (constellation)\">Hip 4872</a>, <a href=\"https://wikipedia.org/wiki/Orion_(constellation)\" title=\"Orion (constellation)\">HD 245409</a>, <a href=\"https://wikipedia.org/wiki/55_Cancri\" title=\"55 Cancri\">55 Cancri</a> (HD 75732), <a href=\"https://wikipedia.org/wiki/HD_10307\" title=\"HD 10307\">HD 10307</a> and <a href=\"https://wikipedia.org/wiki/47_Ursae_Majoris\" title=\"47 Ursae Majoris\">47 Ursae Majoris</a> (HD 95128). The messages will arrive to these stars in 2036, 2040, 2044, and 2049, respectively.", "no_year_html": "The 70-metre <a href=\"https://wikipedia.org/wiki/Yevpatoria\" title=\"Yevpatoria\">Yevpatoria</a> Planetary Radar sends a <a href=\"https://wikipedia.org/wiki/Message_to_Extra-Terrestrial_Intelligence\" class=\"mw-redirect\" title=\"Message to Extra-Terrestrial Intelligence\">METI</a> message (<a href=\"https://wikipedia.org/wiki/Cosmic_Call_2\" class=\"mw-redirect\" title=\"Cosmic Call 2\">Cosmic Call 2</a>) to five stars: <a href=\"https://wikipedia.org/wiki/Cassiopeia_(constellation)\" title=\"Cassiopeia (constellation)\">Hip 4872</a>, <a href=\"https://wikipedia.org/wiki/Orion_(constellation)\" title=\"Orion (constellation)\">HD 245409</a>, <a href=\"https://wikipedia.org/wiki/55_Cancri\" title=\"55 Cancri\">55 Cancri</a> (HD 75732), <a href=\"https://wikipedia.org/wiki/HD_10307\" title=\"HD 10307\">HD 10307</a> and <a href=\"https://wikipedia.org/wiki/47_Ursae_Majoris\" title=\"47 Ursae Majoris\">47 Ursae Majoris</a> (HD 95128). The messages will arrive to these stars in 2036, 2040, 2044, and 2049, respectively.", "links": [{"title": "Yevpatoria", "link": "https://wikipedia.org/wiki/Yevpatoria"}, {"title": "Message to Extra-Terrestrial Intelligence", "link": "https://wikipedia.org/wiki/Message_to_Extra-Terrestrial_Intelligence"}, {"title": "Cosmic Call 2", "link": "https://wikipedia.org/wiki/Cosmic_Call_2"}, {"title": "Cassiopeia (constellation)", "link": "https://wikipedia.org/wiki/Cassiopeia_(constellation)"}, {"title": "Orion (constellation)", "link": "https://wikipedia.org/wiki/Orion_(constellation)"}, {"title": "55 Cancri", "link": "https://wikipedia.org/wiki/55_<PERSON><PERSON>ri"}, {"title": "HD 10307", "link": "https://wikipedia.org/wiki/HD_10307"}, {"title": "47 Ursae <PERSON>", "link": "https://wikipedia.org/wiki/47_<PERSON><PERSON><PERSON>_<PERSON>is"}]}, {"year": "2006", "text": "The Nathu La pass between India and China, sealed during the Sino-Indian War, re-opens for trade after 44 years.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_La\" title=\"Nat<PERSON> La\"><PERSON><PERSON></a> pass between India and China, sealed during the <a href=\"https://wikipedia.org/wiki/Sino-Indian_War\" title=\"Sino-Indian War\">Sino-Indian War</a>, re-opens for trade after 44 years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Nat<PERSON> La\"><PERSON><PERSON></a> pass between India and China, sealed during the <a href=\"https://wikipedia.org/wiki/Sino-Indian_War\" title=\"Sino-Indian War\">Sino-Indian War</a>, re-opens for trade after 44 years.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nathu_La"}, {"title": "Sino-Indian War", "link": "https://wikipedia.org/wiki/Sino-Indian_War"}]}, {"year": "2013", "text": "At least 42 people are killed in a shooting at a school in Yobe State, Nigeria.", "html": "2013 - At least 42 people are killed in a <a href=\"https://wikipedia.org/wiki/Mamudo_school_massacre\" title=\"Mamudo school massacre\">shooting</a> at a school in <a href=\"https://wikipedia.org/wiki/Yobe_State\" title=\"Yobe State\">Yobe State</a>, <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>.", "no_year_html": "At least 42 people are killed in a <a href=\"https://wikipedia.org/wiki/Mamudo_school_massacre\" title=\"Mamudo school massacre\">shooting</a> at a school in <a href=\"https://wikipedia.org/wiki/Yobe_State\" title=\"Yobe State\">Yobe State</a>, <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>.", "links": [{"title": "Mamudo school massacre", "link": "https://wikipedia.org/wiki/Mamudo_school_massacre"}, {"title": "Yobe State", "link": "https://wikipedia.org/wiki/Yobe_State"}, {"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}]}, {"year": "2013", "text": "A Boeing 777 operating as Asiana Airlines Flight 214 crashes at San Francisco International Airport, killing three and injuring 181 of the 307 people on board.", "html": "2013 - A <a href=\"https://wikipedia.org/wiki/Boeing_777\" title=\"Boeing 777\">Boeing 777</a> operating as <a href=\"https://wikipedia.org/wiki/Asiana_Airlines_Flight_214\" title=\"Asiana Airlines Flight 214\">Asiana Airlines Flight 214</a> crashes at <a href=\"https://wikipedia.org/wiki/San_Francisco_International_Airport\" title=\"San Francisco International Airport\">San Francisco International Airport</a>, killing three and injuring 181 of the 307 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Boeing_777\" title=\"Boeing 777\">Boeing 777</a> operating as <a href=\"https://wikipedia.org/wiki/Asiana_Airlines_Flight_214\" title=\"Asiana Airlines Flight 214\">Asiana Airlines Flight 214</a> crashes at <a href=\"https://wikipedia.org/wiki/San_Francisco_International_Airport\" title=\"San Francisco International Airport\">San Francisco International Airport</a>, killing three and injuring 181 of the 307 people on board.", "links": [{"title": "Boeing 777", "link": "https://wikipedia.org/wiki/Boeing_777"}, {"title": "Asiana Airlines Flight 214", "link": "https://wikipedia.org/wiki/Asiana_Airlines_Flight_214"}, {"title": "San Francisco International Airport", "link": "https://wikipedia.org/wiki/San_Francisco_International_Airport"}]}, {"year": "2013", "text": "A 73-car oil train derails in the town of Lac-Mégantic, Quebec and explodes into flames, killing at least 47 people and destroying more than 30 buildings in the town's central area.", "html": "2013 - A 73-car oil train <a href=\"https://wikipedia.org/wiki/Lac-M%C3%A9gantic_derailment\" class=\"mw-redirect\" title=\"Lac-Mégantic derailment\">derails</a> in the town of <a href=\"https://wikipedia.org/wiki/Lac-M%C3%A9gantic,_Quebec\" title=\"Lac-Mégantic, Quebec\">Lac-Mégantic, Quebec</a> and explodes into flames, killing at least 47 people and destroying more than 30 buildings in the town's central area.", "no_year_html": "A 73-car oil train <a href=\"https://wikipedia.org/wiki/Lac-M%C3%A9gantic_derailment\" class=\"mw-redirect\" title=\"Lac-Mégantic derailment\">derails</a> in the town of <a href=\"https://wikipedia.org/wiki/Lac-M%C3%A9gantic,_Quebec\" title=\"Lac-Mégantic, Quebec\">Lac-Mégantic, Quebec</a> and explodes into flames, killing at least 47 people and destroying more than 30 buildings in the town's central area.", "links": [{"title": "Lac-Mégantic derailment", "link": "https://wikipedia.org/wiki/Lac-M%C3%A9gantic_derailment"}, {"title": "Lac-<PERSON>, Quebec", "link": "https://wikipedia.org/wiki/Lac-M%C3%A9gantic,_Quebec"}]}, {"year": "2021", "text": "An Antonov An-26 operating as Petropavlovsk-Kamchatsky Air Flight 251 crashes on approach to Palana Airport, killing all 28 aboard.", "html": "2021 - An <a href=\"https://wikipedia.org/wiki/Antonov_An-26\" title=\"Antonov An-26\">Antonov An-26</a> operating as <a href=\"https://wikipedia.org/wiki/Petropavlovsk-Kamchatsky_Air_Flight_251_(2021)\" title=\"Petropavlovsk-Kamchatsky Air Flight 251 (2021)\">Petropavlovsk-Kamchatsky Air Flight 251</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Palana_Airport\" title=\"Palana Airport\">Palana Airport</a>, killing all 28 aboard.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Antonov_An-26\" title=\"Antonov An-26\">Antonov An-26</a> operating as <a href=\"https://wikipedia.org/wiki/Petropavlovsk-Kamchatsky_Air_Flight_251_(2021)\" title=\"Petropavlovsk-Kamchatsky Air Flight 251 (2021)\">Petropavlovsk-Kamchatsky Air Flight 251</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Palana_Airport\" title=\"Palana Airport\">Palana Airport</a>, killing all 28 aboard.", "links": [{"title": "Antonov An-26", "link": "https://wikipedia.org/wiki/Antonov_An-26"}, {"title": "Petropavlovsk-Kamchatsky Air Flight 251 (2021)", "link": "https://wikipedia.org/wiki/Petropavlovsk-Kamchatsky_Air_Flight_251_(2021)"}, {"title": "Palana Airport", "link": "https://wikipedia.org/wiki/Palana_Airport"}]}, {"year": "2022", "text": "The Georgia Guidestones, a monument in the United States, are heavily damaged in a bombing, and are dismantled later the same day.", "html": "2022 - The <a href=\"https://wikipedia.org/wiki/Georgia_Guidestones\" title=\"Georgia Guidestones\">Georgia Guidestones</a>, a monument in the United States, are heavily damaged in a bombing, and are dismantled later the same day.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Georgia_Guidestones\" title=\"Georgia Guidestones\">Georgia Guidestones</a>, a monument in the United States, are heavily damaged in a bombing, and are dismantled later the same day.", "links": [{"title": "Georgia Guidestones", "link": "https://wikipedia.org/wiki/Georgia_Guidestones"}]}], "Births": [{"year": "1387", "text": "Queen <PERSON> of Navarre (d. 1441)", "html": "1387 - Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> I of Navarre\"><PERSON> of Navarre</a> (d. 1441)", "no_year_html": "Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> I of Navarre\"><PERSON> of Navarre</a> (d. 1441)", "links": [{"title": "<PERSON> I of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_I_of_Navarre"}]}, {"year": "1423", "text": "<PERSON>, Italian mathematician and architect (d. 1497)", "html": "1423 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and architect (d. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and architect (d. 1497)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1580", "text": "<PERSON>, German lute player and composer (d. 1646)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4us\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Lute\" title=\"<PERSON><PERSON>\">lute</a> player and composer (d. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4us\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Lute\" title=\"Lute\">lute</a> player and composer (d. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A4us"}, {"title": "Lute", "link": "https://wikipedia.org/wiki/Lute"}]}, {"year": "1623", "text": "<PERSON><PERSON><PERSON>, Italian violinist and composer (d. 1676)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian violinist and composer (d. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian violinist and composer (d. 1676)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1678", "text": "<PERSON>, Italian cellist and composer (d. 1729)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (d. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (d. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, French biologist and academic (d. 1758)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and academic (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and academic (d. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1701", "text": "<PERSON>, Countess of Harold, English aristocrat and philanthropist (d. 1785)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Harold\" title=\"<PERSON>, Countess of Harold\"><PERSON>, Countess of Harold</a>, English aristocrat and philanthropist (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Harold\" title=\"<PERSON>, Countess of Harold\"><PERSON>, Countess of Harold</a>, English aristocrat and philanthropist (d. 1785)", "links": [{"title": "<PERSON>, Countess of Harold", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_<PERSON>_<PERSON>"}]}, {"year": "1736", "text": "<PERSON>, American general and politician (d. 1802)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "<PERSON>, Scottish-American captain (d. 1792)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American captain (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American captain (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, Scottish-American poet, ornithologist, and illustrator (d. 1813)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ornithologist)\" title=\"<PERSON> (ornithologist)\"><PERSON></a>, Scottish-American poet, ornithologist, and illustrator (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ornithologist)\" title=\"<PERSON> (ornithologist)\"><PERSON></a>, Scottish-American poet, ornithologist, and illustrator (d. 1813)", "links": [{"title": "<PERSON> (ornithologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ornithologist)"}]}, {"year": "1782", "text": "<PERSON> of Spain (d. 1824)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Lucca\" title=\"<PERSON>, Duchess of Lucca\"><PERSON> of Spain</a> (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Lucca\" title=\"<PERSON>, Duchess of Lucca\"><PERSON> of Spain</a> (d. 1824)", "links": [{"title": "<PERSON>, Duchess of Lucca", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Lucca"}]}, {"year": "1785", "text": "<PERSON>, English botanist and academic (d. 1865)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and academic (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and academic (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON> of Spain (d. 1846)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/Mar%C3%AD<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%AD<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (d. 1846)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_of_Spain"}]}, {"year": "1796", "text": "<PERSON> of Russia (d. 1855)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1855)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1797", "text": "<PERSON>, 2nd Marquess of Anglesey (d. 1869)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Anglesey\" title=\"<PERSON>, 2nd Marquess of Anglesey\"><PERSON>, 2nd Marquess of Anglesey</a> (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Anglesey\" title=\"<PERSON>, 2nd Marquess of Anglesey\"><PERSON>, 2nd Marquess of Anglesey</a> (d. 1869)", "links": [{"title": "<PERSON>, 2nd Marquess of Anglesey", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Anglesey"}]}, {"year": "1799", "text": "<PERSON>, American author  (d. 1879)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1879)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, Swiss anatomist and physiologist (d. 1905)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss anatomist and physiologist (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss anatomist and physiologist (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>"}]}, {"year": "1818", "text": "<PERSON>, German chess player (d. 1879)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chess player (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chess player (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, Swedish publisher, writer, and women's rights activist (d. 1895)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish publisher, writer, and women's rights activist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish publisher, writer, and women's rights activist (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, Duke of Schleswig-Holstein (d. 1880)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Schleswig-Holstein\" title=\"<PERSON>, Duke of Schleswig-Holstein\"><PERSON>, Duke of Schleswig-Holstein</a> (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Schleswig-Holstein\" title=\"<PERSON>, Duke of Schleswig-Holstein\"><PERSON>, Duke of Schleswig-Holstein</a> (d. 1880)", "links": [{"title": "<PERSON>, Duke of Schleswig-Holstein", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Schleswig-Holstein"}]}, {"year": "1831", "text": "<PERSON>, American lawyer and politician, 8th Governor of Oregon (d. 1902)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1832", "text": "<PERSON> of Mexico (d. 1867)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Mexico\" title=\"Maximilian I of Mexico\"><PERSON> of Mexico</a> (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Mexico\" title=\"<PERSON> I of Mexico\"><PERSON> of Mexico</a> (d. 1867)", "links": [{"title": "<PERSON> I of Mexico", "link": "https://wikipedia.org/wiki/Maximilian_I_of_Mexico"}]}, {"year": "1837", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian orientalist and scholar (d. 1925)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/R._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian orientalist and scholar (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R._<PERSON><PERSON>_<PERSON>\" title=\"R. <PERSON><PERSON>hand<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian orientalist and scholar (d. 1925)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian philologist and scholar (d. 1923)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian philologist and scholar (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian philologist and scholar (d. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vatroslav_Jagi%C4%87"}]}, {"year": "1840", "text": "<PERSON>, Mexican painter and academic (d. 1912)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Velasco_G%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Mexican painter and academic (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Velasco_G%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Mexican painter and academic (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Velasco_G%C3%B3mez"}]}, {"year": "1843", "text": "<PERSON>, Australian politician, 16th Premier of South Australia (d. 1915)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1846", "text": "<PERSON><PERSON>, Mexican opera singer (d. 1883)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican opera singer (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican opera singer (d. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Jr., American lawyer and businessman (d. 1928)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and businessman (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and businessman (d. 1928)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1858", "text": "<PERSON>, Irish-Australian politician, 21st Premier of Victoria (d. 1943)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Irish-Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Irish-Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1943)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Swiss composer and educator (d. 1950)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss composer and educator (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss composer and educator (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Jaque<PERSON>-<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1868", "text": "Princess <PERSON> of the United Kingdom (d. 1935)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (d. 1935)", "links": [{"title": "Princess <PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_the_United_Kingdom"}]}, {"year": "1873", "text": "<PERSON><PERSON>, Greek banker and politician, 140th Prime Minister of Greece (d. 1955)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek banker and politician, 140th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek banker and politician, 140th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dimitrios_Maximos"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, French golfer (d. 1950)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French golfer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French golfer (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Finnish poet and journalist (d. 1926)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish poet and journalist (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish poet and journalist (d. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Prime Minister of the Federation of Rhodesia and Nyasaland (d. 1971)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Federation_of_Rhodesia_and_Nyasaland\" title=\"Prime Minister of the Federation of Rhodesia and Nyasaland\">Prime Minister of the Federation of Rhodesia and Nyasaland</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Federation_of_Rhodesia_and_Nyasaland\" title=\"Prime Minister of the Federation of Rhodesia and Nyasaland\">Prime Minister of the Federation of Rhodesia and Nyasaland</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Federation of Rhodesia and Nyasaland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Federation_of_Rhodesia_and_Nyasaland"}]}, {"year": "1884", "text": "<PERSON>, American businessman and sailor (d. 1970)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and sailor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and sailor (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, German field marshal (d. 1945)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(field_marshal)\" title=\"<PERSON> (field marshal)\"><PERSON></a>, German field marshal (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(field_marshal)\" title=\"<PERSON> (field marshal)\"><PERSON></a>, German field marshal (d. 1945)", "links": [{"title": "<PERSON> (field marshal)", "link": "https://wikipedia.org/wiki/<PERSON>(field_marshal)"}]}, {"year": "1886", "text": "<PERSON>, French historian and academic (d. 1944)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and academic (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and academic (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Belarusian-French painter and poet (d. 1985)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-French painter and poet (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-French painter and poet (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Australian swimmer and actress (d. 1975)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer and actress (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer and actress (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, Indian-American author and scholar (d. 1936)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-American author and scholar (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-American author and scholar (d. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American  engineer, created <PERSON><PERSON><PERSON><PERSON> strut  (d. 1960)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, created <a href=\"https://wikipedia.org/wiki/MacPherson_strut\" title=\"MacPherson strut\">MacPherson strut</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mac<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, created <a href=\"https://wikipedia.org/wiki/MacPherson_strut\" title=\"MacPherson strut\">MacPherson strut</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Earle_<PERSON><PERSON>_<PERSON>hers<PERSON>"}, {"title": "Mac<PERSON><PERSON><PERSON> strut", "link": "https://wikipedia.org/wiki/MacPherson_strut"}]}, {"year": "1892", "text": "<PERSON>, American author and illustrator (d. 1942)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American author and illustrator (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American author and illustrator (d. 1942)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1897", "text": "<PERSON>, German-American historian and scholar (d. 1994)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian and scholar (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian and scholar (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, German-Austrian soldier and composer (d. 1962)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian soldier and composer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian soldier and composer (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American supercentarian (d. 2016)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Supercentarian\" class=\"mw-redirect\" title=\"Supercentarian\">supercentarian</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Supercentarian\" class=\"mw-redirect\" title=\"Supercentarian\">supercentarian</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Supercentarian", "link": "https://wikipedia.org/wiki/Supercentarian"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American author and screenwriter (d. 2012)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and screenwriter (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, German Olympic runner (d. 1941)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Wever\"><PERSON><PERSON><PERSON></a>, German Olympic runner (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Wever\"><PERSON><PERSON><PERSON></a>, German Olympic runner (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elfriede_Wever"}]}, {"year": "1903", "text": "<PERSON>, Swedish biochemist and academic, Nobel Prize laureate (d. 1982)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1904", "text": "<PERSON>, American conductor and composer (d. 1986)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, American conductor and composer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, American conductor and composer (d. 1986)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(conductor)"}]}, {"year": "1904", "text": "<PERSON>, Swedish 9th General of The Salvation Army (d. 1996)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish 9th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish 9th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1905", "text": "<PERSON>, Mexican painter and architect (d. 1982)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Gorman\" title=\"<PERSON>\"><PERSON></a>, Mexican painter and architect (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Gorman\" title=\"<PERSON>\"><PERSON></a>, Mexican painter and architect (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_<PERSON>%27Gorman"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Mexican painter and educator (d. 1954)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican painter and educator (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican painter and educator (d. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1907", "text": "<PERSON>, Canadian soldier, historian, and author, designed the flag of Canada (d. 2002)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, historian, and author, designed the <a href=\"https://wikipedia.org/wiki/Flag_of_Canada\" title=\"Flag of Canada\">flag of Canada</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, historian, and author, designed the <a href=\"https://wikipedia.org/wiki/Flag_of_Canada\" title=\"Flag of Canada\">flag of Canada</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Flag of Canada", "link": "https://wikipedia.org/wiki/Flag_of_Canada"}]}, {"year": "1908", "text": "<PERSON>, Sri Lankan general and diplomat (d. 2001)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan general and diplomat (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan general and diplomat (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Australian politician, 32nd Premier of Tasmania (d. 1999)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1910", "text": "<PERSON>, French cyclist (d. 1946)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Le_Gr%C3%A8ves\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Le_Gr%C3%A8ves\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Le_Gr%C3%A8ves"}]}, {"year": "1911", "text": "<PERSON>, American actress (d. 1996)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/June_Gale\" title=\"June Gale\">June Gale</a>, American actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_Gale\" title=\"June Gale\">June Gale</a>, American actress (d. 1996)", "links": [{"title": "June Gale", "link": "https://wikipedia.org/wiki/June_Gale"}]}, {"year": "1912", "text": "<PERSON>, Austrian geographer and mountaineer (d. 2006)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geographer and mountaineer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geographer and mountaineer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American feminist (d. 2005)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Molly_Yard\" title=\"Molly Yard\"><PERSON></a>, American feminist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Molly_Yard\" title=\"Molly Yard\"><PERSON></a>, American feminist (d. 2005)", "links": [{"title": "Molly Yard", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American journalist and author (d. 2021)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American wrestling promoter, founded WWE (d. 1984)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Sr.\"><PERSON> Sr.</a>, American wrestling promoter, founded <a href=\"https://wikipedia.org/wiki/WWE\" title=\"WWE\">WWE</a> (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Sr.\"><PERSON>.</a>, American wrestling promoter, founded <a href=\"https://wikipedia.org/wiki/WWE\" title=\"WWE\">WWE</a> (d. 1984)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "WWE", "link": "https://wikipedia.org/wiki/WWE"}]}, {"year": "1914", "text": "<PERSON>, American chemist and metallurgist (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and metallurgist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and metallurgist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Royal Canadian Air Force pilot (d. 2004)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Royal Canadian Air Force pilot (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Royal Canadian Air Force pilot (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American poet and author (d. 2009)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Harold_<PERSON>\" title=\"Harold Norse\"><PERSON></a>, American poet and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harold_Norse\" title=\"Harold Norse\"><PERSON></a>, American poet and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Harold_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American animator, cartoonist, illustrator, writer and inventor (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, cartoonist, illustrator, writer and inventor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, cartoonist, illustrator, writer and inventor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, New Zealand runner and coach (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner and coach (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner and coach (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English-Canadian actor (d. 1977)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-Canadian actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-Canadian actor (d. 1977)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1918", "text": "<PERSON><PERSON>, American professional basketball player (d. 2010)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American professional basketball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American professional basketball player (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Dominican-American ballet dancer, charter member of the New York City Ballet (d. 1995)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American ballet dancer, charter member of the <a href=\"https://wikipedia.org/wiki/New_York_City_Ballet\" title=\"New York City Ballet\">New York City Ballet</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Francisco Moncion\"><PERSON></a>, Dominican-American ballet dancer, charter member of the <a href=\"https://wikipedia.org/wiki/New_York_City_Ballet\" title=\"New York City Ballet\">New York City Ballet</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Moncion"}, {"title": "New York City Ballet", "link": "https://wikipedia.org/wiki/New_York_City_Ballet"}]}, {"year": "1919", "text": "<PERSON>, Swiss tenor and educator (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tenor and educator (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tenor and educator (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Australian Second World War recipient of the Victoria Cross (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian Second World War recipient of the <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian Second World War recipient of the <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1919", "text": "<PERSON>, New Zealand cricketer (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Canadian economist and politician, Deputy Prime Minister of Canada (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada"}]}, {"year": "1921", "text": "<PERSON>, American actor (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American actor (d. 2006)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor (d. 2007)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American actor (d. 2007)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actress and activist, 42nd First Lady of the United States (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and activist, 42nd <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and activist, 42nd <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of First Ladies of the United States", "link": "https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States"}]}, {"year": "1922", "text": "<PERSON>, American actor; president (1979-81) of the Screen Actors Guild (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor; president (1979-81) of the <a href=\"https://wikipedia.org/wiki/Screen_Actors_Guild\" title=\"Screen Actors Guild\">Screen Actors Guild</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor; president (1979-81) of the <a href=\"https://wikipedia.org/wiki/Screen_Actors_Guild\" title=\"Screen Actors Guild\">Screen Actors Guild</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Screen Actors Guild", "link": "https://wikipedia.org/wiki/Screen_Actors_Guild"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish general and politician, 1st President of Poland (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>zel<PERSON>\" title=\"Wojcie<PERSON>aruzel<PERSON>\">Woj<PERSON><PERSON></a>, Polish general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>\" title=\"Wojcie<PERSON>aru<PERSON>\">Wo<PERSON><PERSON><PERSON></a>, Polish general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wojcie<PERSON>_<PERSON>"}, {"title": "President of Poland", "link": "https://wikipedia.org/wiki/President_of_Poland"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Indian writer and educationist, recipients of the Padma Shri, India's fourth highest civilian honour (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian writer and educationist, recipients of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Padma <PERSON>\">Padma <PERSON></a>, India's fourth highest civilian honour (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian writer and educationist, recipients of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Padma <PERSON>\">Padma <PERSON></a>, India's fourth highest civilian honour (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Padma_Shri"}]}, {"year": "1924", "text": "<PERSON>, American drummer, composer, and bandleader (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, composer, and bandleader (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, composer, and bandleader (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Australian actress (d. 2002)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, American actor, singer, and producer, created Wheel of Fortune and Jeopardy! (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>r<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, singer, and producer, created <i><a href=\"https://wikipedia.org/wiki/Wheel_of_Fortune_(U.S._game_show)\" class=\"mw-redirect\" title=\"Wheel of Fortune (U.S. game show)\">Wheel of Fortune</a></i> and <i><a href=\"https://wikipedia.org/wiki/Jeopardy!\" title=\"Jeopardy!\">Jeopardy!</a></i> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>r<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, singer, and producer, created <i><a href=\"https://wikipedia.org/wiki/Wheel_of_Fortune_(U.S._game_show)\" class=\"mw-redirect\" title=\"Wheel of Fortune (U.S. game show)\">Wheel of Fortune</a></i> and <i><a href=\"https://wikipedia.org/wiki/Jeopardy!\" title=\"Jeopardy!\">Jeopardy!</a></i> (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Merv_<PERSON>"}, {"title": "Wheel of Fortune (U.S. game show)", "link": "https://wikipedia.org/wiki/Wheel_of_Fortune_(U.S._game_show)"}, {"title": "Jeopardy!", "link": "https://wikipedia.org/wiki/Jeopardy!"}]}, {"year": "1925", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1981)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1981)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Turkish neurosurgeon and academic", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Gazi_Ya%C5%9Fargil\" title=\"Gazi Yaşargil\">Gazi Yaşargil</a>, Turkish neurosurgeon and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gazi_Ya%C5%9Fargil\" title=\"Gazi Yaşargil\">Gazi Yaşargil</a>, Turkish neurosurgeon and academic", "links": [{"title": "Gazi Yaşargil", "link": "https://wikipedia.org/wiki/Gazi_Ya%C5%9Fargil"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Estonian historian and academic (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian historian and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian historian and academic (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Canadian sociologist (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sociologist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sociologist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Dutch chess player and journalist (d. 1988)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chess player and journalist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chess player and journalist (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actress and author (d. 2004)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, French mathematician (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, French politician historian (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Carr%C3%A8re_d%27Encausse\" title=\"<PERSON><PERSON><PERSON><PERSON>En<PERSON>usse\"><PERSON><PERSON><PERSON><PERSON></a>, French politician historian (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Carr%C3%A8re_d%27Encausse\" title=\"<PERSON><PERSON><PERSON><PERSON>En<PERSON>usse\"><PERSON><PERSON><PERSON><PERSON></a>, French politician historian (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Carr%C3%A8re_d%27Encausse"}]}, {"year": "1930", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2021)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1930", "text": "<PERSON>, English racing driver (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actress and singer (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian runner and coach (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_T%C3%A1bori\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> T<PERSON>bor<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian runner and coach (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_T%C3%A1bori\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> T<PERSON>bor<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian runner and coach (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_T%C3%A1bori"}]}, {"year": "1932", "text": "<PERSON>, Dutch architect and academic", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch architect and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch architect and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American model, dancer, and actress (d. 2005)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Barr\" title=\"Candy Barr\"><PERSON></a>, American model, dancer, and actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Candy_Barr\" title=\"Candy Barr\"><PERSON></a>, American model, dancer, and actress (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Barr"}]}, {"year": "1935", "text": "<PERSON><PERSON>, 14th Dal<PERSON>", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Tenzin_Gyatso\" class=\"mw-redirect\" title=\"Tenzin Gyatso\"><PERSON><PERSON> Gyatso</a>, <a href=\"https://wikipedia.org/wiki/14th_<PERSON><PERSON>_Lama\" title=\"14th Dalai Lama\">14th Dalai Lama</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ten<PERSON>_Gyatso\" class=\"mw-redirect\" title=\"Tenzin Gyatso\"><PERSON><PERSON> Gyatso</a>, <a href=\"https://wikipedia.org/wiki/14th_<PERSON><PERSON>_Lama\" title=\"14th Dalai Lama\">14th Dalai Lama</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ten<PERSON>_<PERSON>o"}, {"title": "14th Dalai Lama", "link": "https://wikipedia.org/wiki/14th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Irish comedian, actor, and screenwriter (d. 2005)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Irish comedian, actor, and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Irish comedian, actor, and screenwriter (d. 2005)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)"}]}, {"year": "1937", "text": "<PERSON>, Russian-Icelandic pianist and conductor", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Vladimir_Ashkenazy\" title=\"Vladimir <PERSON>\"><PERSON></a>, Russian-Icelandic pianist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladimir_Ash<PERSON>azy\" title=\"Vladimir <PERSON>\"><PERSON></a>, Russian-Icelandic pianist and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_Ashkenazy"}]}, {"year": "1937", "text": "<PERSON>, American actor (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter and producer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Botswanan writer (d. 1986)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Head\" title=\"<PERSON><PERSON> Head\"><PERSON><PERSON></a>, Botswanan writer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Head\"><PERSON><PERSON></a>, Botswanan writer (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Head"}]}, {"year": "1937", "text": "<PERSON>, Zambian police officer and politician, 5th President of Zambia (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian police officer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian police officer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Zambia", "link": "https://wikipedia.org/wiki/President_of_Zambia"}]}, {"year": "1939", "text": "<PERSON>, English bass player (d. 2011)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English-Irish pentathlete and shot putter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English-Irish pentathlete and shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English-Irish pentathlete and shot putter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1939", "text": "<PERSON>, American swimmer (d. 2018)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer (d. 2018)", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1939", "text": "<PERSON><PERSON>, French sports executive, president of AJ Auxerre (2011-2013) and (Ligue de Football Professionnel) (d. 2025)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French sports executive, president of <a href=\"https://wikipedia.org/wiki/<PERSON>_Auxerre\" title=\"<PERSON>rre\"><PERSON>rre</a> (2011-2013) and (<a href=\"https://wikipedia.org/wiki/Ligue_de_Football_Professionnel\" title=\"Ligue de Football Professionnel\">Ligue de Football Professionnel</a>) (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>urg<PERSON>n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French sports executive, president of <a href=\"https://wikipedia.org/wiki/AJ_Auxerre\" title=\"<PERSON>xerre\"><PERSON></a> (2011-2013) and (<a href=\"https://wikipedia.org/wiki/Ligue_de_Football_Professionnel\" title=\"Ligue de Football Professionnel\">Ligue de Football Professionnel</a>) (d. 2025)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>n"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/AJ_Auxerre"}, {"title": "Ligue de Football Professionnel", "link": "https://wikipedia.org/wiki/Ligue_de_Football_Professionnel"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Kazakh politician, 1st President of Kazakhstan", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Kazakh politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Kazakhstan\" title=\"President of Kazakhstan\">President of Kazakhstan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Kazakh politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Kazakhstan\" title=\"President of Kazakhstan\">President of Kazakhstan</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Kazakhstan", "link": "https://wikipedia.org/wiki/President_of_Kazakhstan"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Grammy Award-winning country music singer-songwriter and Grand Ole <PERSON> member", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Grammy Award-winning <a href=\"https://wikipedia.org/wiki/Country_music\" title=\"Country music\">country music</a> singer-songwriter and <a href=\"https://wikipedia.org/wiki/Grand_Ole_Opry\" title=\"Grand Ole Opry\">Grand Ole Opry</a> member", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Grammy Award-winning <a href=\"https://wikipedia.org/wiki/Country_music\" title=\"Country music\">country music</a> singer-songwriter and <a href=\"https://wikipedia.org/wiki/Grand_Ole_Opry\" title=\"Grand Ole Opry\">Grand Ole Opry</a> member", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Country music", "link": "https://wikipedia.org/wiki/Country_music"}, {"title": "Grand Ole Opry", "link": "https://wikipedia.org/wiki/<PERSON>_Ole_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Malaysian lawyer and judge", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian lawyer and judge", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Siti_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, British linguist, author, and academic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British linguist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British linguist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Indonesian-Australian journalist and television host", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian-Australian journalist and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian-Australian journalist and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Russian soprano", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, German runner", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Burt_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American businessman and politician, 43rd President of the United States", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 43rd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 43rd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1946", "text": "<PERSON>, American football player and actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian philosopher and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sylvester_<PERSON>ne"}]}, {"year": "1947", "text": "<PERSON>, Filipino diplomat and politician (d. 2016)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1eres\" title=\"<PERSON>\"><PERSON></a>, Filipino diplomat and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1eres\" title=\"<PERSON>\"><PERSON></a>, Filipino diplomat and politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1eres"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, French actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Canadian academic and politician, 26th Canadian Minister of Veterans Affairs", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian academic and politician, 26th <a href=\"https://wikipedia.org/wiki/Minister_of_Veterans_Affairs_(Canada)\" title=\"Minister of Veterans Affairs (Canada)\">Canadian Minister of Veterans Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian academic and politician, 26th <a href=\"https://wikipedia.org/wiki/Minister_of_Veterans_Affairs_(Canada)\" title=\"Minister of Veterans Affairs (Canada)\">Canadian Minister of Veterans Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Minister of Veterans Affairs (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Veterans_Affairs_(Canada)"}]}, {"year": "1948", "text": "<PERSON>, Canadian-American ice hockey player and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Brad Park\"><PERSON></a>, Canadian-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brad_<PERSON>\" title=\"Brad Park\"><PERSON></a>, Canadian-American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brad_Park"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Filipino journalist and politician, 14th Vice President of the Philippines", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist and politician, 14th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Philippines\" title=\"Vice President of the Philippines\">Vice President of the Philippines</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist and politician, 14th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Philippines\" title=\"Vice President of the Philippines\">Vice President of the Philippines</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the Philippines", "link": "https://wikipedia.org/wiki/Vice_President_of_the_Philippines"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and actress (d. 1995)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American composer, drummer, and percussionist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, drummer, and percussionist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, drummer, and percussionist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English-American author and illustrator", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, English-American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, English-American author and illustrator", "links": [{"title": "<PERSON> (comics)", "link": "https://wikipedia.org/wiki/<PERSON>_(comics)"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Former First Lady of Jamaica", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Former First Lady of Jamaica", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Former First Lady of Jamaica", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian actor and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Rush\"><PERSON></a>, Australian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American illustrator and concept designer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and concept designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and concept designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English author and critic (d. 2022)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 2021)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Griffith"}]}, {"year": "1953", "text": "<PERSON>, Zambian footballer and manager (d. 2014)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kaiser <PERSON>\"><PERSON></a>, Zambian footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kaiser <PERSON>\"><PERSON></a>, Zambian footballer and manager (d. 2014)", "links": [{"title": "Kaiser Kalambo", "link": "https://wikipedia.org/wiki/Kaiser_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, French politician and former journalist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nard\" title=\"<PERSON>\"><PERSON></a>, French politician and former journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nard\" title=\"<PERSON>\"><PERSON></a>, French politician and former journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_M%C3%A9nard"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American baseball player and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English actress, comedian and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, comedian and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, comedian and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French basketball player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Polish businesswoman and politician, Polish Minister of Infrastructure and Development", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish businesswoman and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Infrastructure_and_Development_(Poland)\" title=\"Ministry of Infrastructure and Development (Poland)\">Polish Minister of Infrastructure and Development</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish businesswoman and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Infrastructure_and_Development_(Poland)\" title=\"Ministry of Infrastructure and Development (Poland)\">Polish Minister of Infrastructure and Development</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Infrastructure and Development (Poland)", "link": "https://wikipedia.org/wiki/Ministry_of_Infrastructure_and_Development_(Poland)"}]}, {"year": "1962", "text": "<PERSON>, English runner and coach (d. 2013)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American author, screenwriter, and director[citation needed]", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, screenwriter, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, screenwriter, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Thi<PERSON>_<PERSON>\" title=\"Thi<PERSON> Warm<PERSON>\"><PERSON><PERSON><PERSON></a>, politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hi<PERSON>_<PERSON>\" title=\"Thierry Warm<PERSON>\"><PERSON><PERSON><PERSON></a>, politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Bermudian singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Heather Nova\"><PERSON></a>, Bermudian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Heather Nova\"><PERSON></a>, Bermudian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Heather_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American rapper and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Inspectah_Deck\" title=\"Inspectah Deck\">Inspectah Deck</a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inspectah_Deck\" title=\"Inspectah Deck\">Inspectah Deck</a>, American rapper and producer", "links": [{"title": "Inspectah Deck", "link": "https://wikipedia.org/wiki/Inspectah_Deck"}]}, {"year": "1972", "text": "<PERSON>, Australian politician, 48th Premier of Victoria", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 48th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 48th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1972", "text": "<PERSON>, French author and playwright", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1972", "text": "<PERSON>, American baseball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1972", "text": "<PERSON><PERSON>-Block, Ukrainian sprinter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Block\" title=\"<PERSON><PERSON>-Block\"><PERSON><PERSON>-<PERSON></a>, Ukrainian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Block\" title=\"<PERSON><PERSON>-Block\"><PERSON><PERSON>-<PERSON></a>, Ukrainian sprinter", "links": [{"title": "<PERSON><PERSON>-Block", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-Block"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Japanese professional wrestler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese professional wrestler", "links": [{"title": "Harashima", "link": "https://wikipedia.org/wiki/Harashima"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Z%C3%A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Z%C3%A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Z%C3%A9_<PERSON>"}]}, {"year": "1975", "text": "50 Cent, American rapper and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/50_Cent\" title=\"50 Cent\">50 Cent</a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/50_Cent\" title=\"50 Cent\">50 Cent</a>, American rapper and actor", "links": [{"title": "50 Cent", "link": "https://wikipedia.org/wiki/50_Cent"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentine-Mexican actor and model", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine-Mexican actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se<PERSON>ti%C3%A1n_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine-Mexican actor and model", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_<PERSON><PERSON>i"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Iranian journalist and activist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian journalist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian journalist and activist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Australian rugby league player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oolf\" title=\"<PERSON><PERSON>ool<PERSON>\"><PERSON><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oolf\" title=\"<PERSON><PERSON>ool<PERSON>\"><PERSON><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oolf"}]}, {"year": "1976", "text": "<PERSON>, English-Irish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Romanian-American mathematician and academic", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-American mathematician and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Belarusian tennis player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, South African cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1978", "text": "<PERSON>, American actor, director, and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American actress and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Mo<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American actress and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, New Zealand rugby player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Australian singer-songwriter and guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ster"}]}, {"year": "1979", "text": "<PERSON>, American comedian, actor, producer, and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Spanish basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Pau_<PERSON>\" title=\"Pau Gasol\"><PERSON><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>u_<PERSON>\" title=\"Pau Gasol\"><PERSON><PERSON></a>, Spanish basketball player", "links": [{"title": "Pau <PERSON>ol", "link": "https://wikipedia.org/wiki/Pau_Gasol"}]}, {"year": "1980", "text": "<PERSON>, French actress and model", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Green\"><PERSON></a>, French actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Green\"><PERSON></a>, French actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_Green"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American rapper", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>nam<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nam<PERSON>_<PERSON>a"}]}, {"year": "1981", "text": "<PERSON>, Russian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shi<PERSON>ov\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actress (d. 2014)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian actor, director, and producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor, director, and producer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1984", "text": "<PERSON>, Chinese figure skater", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, Chinese figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, Chinese figure skater", "links": [{"title": "<PERSON> (figure skater)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian film actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian film actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian film actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>n<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American businessman, founded Tumblr", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Tumblr\" title=\"Tumblr\">Tumblr</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Tumblr\" title=\"Tumblr\">Tumblr</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Tumblr", "link": "https://wikipedia.org/wiki/Tumblr"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American runner", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English singer-songwriter, guitarist, and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Brazilian model", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Swiss footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gueye\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Magaye Gueye\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "Ma<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-lae\" title=\"<PERSON> Na-lae\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-lae\" title=\"<PERSON> Na-lae\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-lae"}]}, {"year": "1992", "text": "<PERSON>, Dominican-American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American YouTuber and live streamer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber and live streamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber and live streamer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Comethazine\" title=\"Comethazine\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Comethazine\" title=\"Comethazine\"><PERSON>ha<PERSON></a>, American rapper", "links": [{"title": "Comethazine", "link": "https://wikipedia.org/wiki/Comethazine"}]}, {"year": "2000", "text": "<PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Williamson"}]}], "Deaths": [{"year": "371 BC", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Spartan king", "html": "371 BC - 371 BC - <a href=\"https://wikipedia.org/wiki/Cleombrotus_I\" title=\"Cleombrotus I\">Cleombrot<PERSON> I</a>, Spartan king", "no_year_html": "371 BC - <a href=\"https://wikipedia.org/wiki/Cleombrotus_I\" title=\"Cleombrotus I\">Cleombrotus I</a>, Spartan king", "links": [{"title": "Cleombrotus I", "link": "https://wikipedia.org/wiki/C<PERSON>mbrotus_I"}]}, {"year": "649", "text": "<PERSON><PERSON> of Aquitaine, French bishop", "html": "649 - <a href=\"https://wikipedia.org/wiki/Goar_of_Aquitaine\" title=\"<PERSON><PERSON> of Aquitaine\"><PERSON><PERSON> of Aquitaine</a>, French bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Goar_of_Aquitaine\" title=\"<PERSON><PERSON> of Aquitaine\"><PERSON><PERSON> of Aquitaine</a>, French bishop", "links": [{"title": "Goar of Aquitaine", "link": "https://wikipedia.org/wiki/Goar_of_Aquitaine"}]}, {"year": "887", "text": "<PERSON>, Chinese warlord", "html": "887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese warlord", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese warlord", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wang_<PERSON>"}]}, {"year": "918", "text": "<PERSON>, duke of Aquitaine (b. 875)", "html": "918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON></a>, duke of Aquitaine (b. 875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON></a>, duke of Aquitaine (b. 875)", "links": [{"title": "<PERSON>, Duke of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine"}]}, {"year": "1017", "text": "<PERSON><PERSON>, Japanese scholar (b. 942)", "html": "1017 - <a href=\"https://wikipedia.org/wiki/<PERSON>shin\" title=\"Gen<PERSON>\"><PERSON><PERSON></a>, Japanese scholar (b. 942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Genshin\" title=\"Genshin\"><PERSON><PERSON></a>, Japanese scholar (b. 942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>shin"}]}, {"year": "1070", "text": "<PERSON><PERSON><PERSON>, Flemish saint (b. 1049)", "html": "1070 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish saint (b. 1049)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish saint (b. 1049)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1189", "text": "<PERSON>, king of England (b. 1133)", "html": "1189 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"<PERSON> II of England\"><PERSON> II</a>, king of England (b. 1133)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> II</a>, king of England (b. 1133)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_England"}]}, {"year": "1218", "text": "<PERSON><PERSON> <PERSON>, duke of Burgundy (b. 1166)", "html": "1218 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON><PERSON> <PERSON>, Duke of Burgundy\"><PERSON><PERSON> <PERSON></a>, duke of Burgundy (b. 1166)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON><PERSON> <PERSON>, Duke of Burgundy\"><PERSON><PERSON> <PERSON></a>, duke of Burgundy (b. 1166)", "links": [{"title": "<PERSON><PERSON> <PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1249", "text": "<PERSON>, king of Scotland (b. 1198)", "html": "1249 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> II</a>, king of Scotland (b. 1198)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> II</a>, king of Scotland (b. 1198)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1415", "text": "<PERSON>, Czech priest, philosopher, and reformer (b. 1369)", "html": "1415 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech priest, philosopher, and reformer (b. 1369)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech priest, philosopher, and reformer (b. 1369)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1476", "text": "<PERSON><PERSON><PERSON><PERSON>, German mathematician and astrologer (b. 1436)", "html": "1476 - <a href=\"https://wikipedia.org/wiki/Regiomontanus\" title=\"Regiomontanus\"><PERSON><PERSON><PERSON><PERSON></a>, German mathematician and astrologer (b. 1436)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Regiomontanus\" title=\"Regiomontanus\"><PERSON><PERSON><PERSON><PERSON></a>, German mathematician and astrologer (b. 1436)", "links": [{"title": "Regiomontanus", "link": "https://wikipedia.org/wiki/Regiomontanus"}]}, {"year": "1480", "text": "<PERSON>, Italian composer (b. 1416)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1416)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1416)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1533", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian poet and playwright (b. 1474)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet and playwright (b. 1474)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet and playwright (b. 1474)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1535", "text": "<PERSON>, English lawyer and politician, Chancellor of the Duchy of Lancaster (b. 1478)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1478)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1478)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1553", "text": "<PERSON>, king of England and Ireland (b. 1537)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> VI of England\"><PERSON> VI</a>, king of England and Ireland (b. 1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_England\" class=\"mw-redirect\" title=\"<PERSON> VI of England\"><PERSON> VI</a>, king of England and Ireland (b. 1537)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1583", "text": "<PERSON>, English archbishop (b. 1519)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1519)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1585", "text": "<PERSON>, English priest and martyr (b. 1552)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and martyr (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and martyr (b. 1552)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1614", "text": "<PERSON>, Rajput Raja of Amer (b. 1550)", "html": "1614 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Man Singh I\"><PERSON> I</a>, <PERSON><PERSON> of Amer (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Man Singh I\"><PERSON> I</a>, <PERSON><PERSON> of Amer (b. 1550)", "links": [{"title": "<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1684", "text": "<PERSON>, English bishop (b. 1614)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1614)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, 3rd Viscount <PERSON>, English general and politician (b. 1725)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Viscount_<PERSON>\" title=\"<PERSON>, 3rd Viscount <PERSON>\"><PERSON>, 3rd Viscount <PERSON></a>, English general and politician (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Viscount_<PERSON>\" title=\"<PERSON>, 3rd Viscount <PERSON>\"><PERSON>, 3rd Viscount <PERSON></a>, English general and politician (b. 1725)", "links": [{"title": "<PERSON>, 3rd Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Viscount_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, German-American religious leader (b. 1690)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American religious leader (b. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American religious leader (b. 1690)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, American general and politician (b. 1736)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (b. 1736)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, French general (b. 1775)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON><PERSON>, English activist (b. 1735)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Granville_Sharp\" title=\"Granville Sharp\"><PERSON><PERSON></a>, English activist (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Granville_Sharp\" title=\"Granville Sharp\"><PERSON><PERSON> Sharp</a>, English activist (b. 1735)", "links": [{"title": "Granville Sharp", "link": "https://wikipedia.org/wiki/Granville_Sharp"}]}, {"year": "1815", "text": "<PERSON>, English politician (b. 1764)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1764%E2%80%931815)\" title=\"<PERSON> (1764-1815)\"><PERSON></a>, English politician (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1764%E2%80%931815)\" title=\"<PERSON> (1764-1815)\"><PERSON></a>, English politician (b. 1764)", "links": [{"title": "<PERSON> (1764-1815)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1764%E2%80%931815)"}]}, {"year": "1835", "text": "<PERSON>, American captain and politician, 4th United States Secretary of State (b. 1755)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1755)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1854", "text": "<PERSON>, German physicist and mathematician (b. 1789)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and mathematician (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and mathematician (b. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON>, Japanese captain (b. 1840)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Harada_Sanosuke\" title=\"Harada Sanosuke\"><PERSON><PERSON></a>, Japanese captain (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harada_Sanosuke\" title=\"Harada Sanosuke\"><PERSON><PERSON></a>, Japanese captain (b. 1840)", "links": [{"title": "<PERSON><PERSON> Sanosuke", "link": "https://wikipedia.org/wiki/Harada_Sanosuke"}]}, {"year": "1893", "text": "<PERSON>, French short story writer, novelist, and poet (b. 1850)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French short story writer, novelist, and poet (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French short story writer, novelist, and poet (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, German prince and chancellor (b. 1819)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Prince_of_Hohenlohe-Schillingsf%C3%BCrst\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prince of Hohenlohe-Schillingsfürst\"><PERSON><PERSON><PERSON><PERSON></a>, German prince and chancellor (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Prince_of_Hohenlohe-Schillingsf%C3%BCrst\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prince of Hohenlohe-Schillingsfürst\"><PERSON><PERSON><PERSON><PERSON></a>, German prince and chancellor (b. 1819)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Prince of Hohenlohe-Schillingsfürst", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Prince_of_Hohenlohe-Schillingsf%C3%BCrst"}]}, {"year": "1902", "text": "<PERSON>, Italian martyr and saint (b. 1890)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian martyr and saint (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian martyr and saint (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Kazakh poet and philosopher (b. 1845)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>ly\" title=\"A<PERSON><PERSON> Q<PERSON>n<PERSON>iuly\"><PERSON><PERSON><PERSON></a>, Kazakh poet and philosopher (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ly\"><PERSON><PERSON><PERSON></a>, Kazakh poet and philosopher (b. 1845)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>i_Qunan<PERSON>iuly"}]}, {"year": "1907", "text": "<PERSON> <PERSON>, German linguist and theologian (b. 1826)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"August <PERSON>\">August <PERSON></a>, German linguist and theologian (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"August <PERSON>\">August <PERSON></a>, German linguist and theologian (b. 1826)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, French aviator (b. 1882) ", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French aviator (b. 1882) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French aviator (b. 1882) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, French painter and illustrator (b. 1840)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and illustrator (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and illustrator (b. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>on"}]}, {"year": "1918", "text": "<PERSON>, German diplomat (b. 1871)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German diplomat (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German diplomat (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Polish-Austrian nun and missionary (b. 1863)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Maria_Teres<PERSON>_Led%C3%B3chowska\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Polish-Austrian nun and missionary (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_Teres<PERSON>_Led%C3%B3chowska\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Polish-Austrian nun and missionary (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Teresia_Led%C3%B3chowska"}]}, {"year": "1932", "text": "<PERSON>, Scottish-English author (b. 1859)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English author (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English author (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American painter (b. 1888)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Swiss-American painter (b. 1862)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BCller-Ury\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-American painter (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BCller-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-American painter (b. 1862)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolfo_M%C3%BCller-Ury"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 14th Premier of Quebec (b. 1867)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Indian lawyer, social reformer and writer (b. 1866)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Cornelia_Sorabji\" title=\"Cornelia Sorabji\">Corne<PERSON>ji</a>, Indian lawyer, social reformer and writer (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornelia_Sorabji\" title=\"Cornelia Sorabji\">Co<PERSON><PERSON>ji</a>, Indian lawyer, social reformer and writer (b. 1866)", "links": [{"title": "Cornelia <PERSON>rabji", "link": "https://wikipedia.org/wiki/Cornelia_Sorabji"}]}, {"year": "1959", "text": "<PERSON>, German painter and illustrator (b. 1893)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Welsh-English politician, Secretary of State for Health (b. 1897)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh-English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh-English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Secretary of State for Health", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Health"}]}, {"year": "1961", "text": "<PERSON>, American bassist (b. 1936)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American lawyer and politician, Mayor of Dallas (b. 1890)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Dallas\" title=\"Mayor of Dallas\">Mayor of Dallas</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Dallas\" title=\"Mayor of Dallas\">Mayor of Dallas</a> (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Dallas", "link": "https://wikipedia.org/wiki/Mayor_of_Dallas"}]}, {"year": "1962", "text": "<PERSON>, Maltese soldier and politician, 5th Prime Minister of Malta (b. 1890)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese soldier and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese soldier and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1962", "text": "<PERSON>, American novelist and short story writer, Nobel Prize laureate (b. 1897)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1962", "text": "<PERSON>, archduke of Austria (b. 1872)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_August_of_Austria\" title=\"Archduke <PERSON> August of Austria\"><PERSON> August</a>, archduke of Austria (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_August_of_Austria\" title=\"Archduke <PERSON> August of Austria\"><PERSON> August</a>, archduke of Austria (b. 1872)", "links": [{"title": "Archdu<PERSON> <PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_August_of_Austria"}]}, {"year": "1963", "text": "<PERSON>, duke of Mecklenburg (b. 1899)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON></a>, duke of Mecklenburg (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON></a>, duke of Mecklenburg (b. 1899)", "links": [{"title": "<PERSON>, Duke of Mecklenburg", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mecklenburg"}]}, {"year": "1964", "text": "<PERSON>, American admiral (b. 1906)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player and manager (b. 1892)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Estonian architect and educator (b. 1902)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect and educator (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect and educator (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer and trumpet player (b. 1901)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and trumpet player (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and trumpet player (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, German-American conductor and composer (b. 1885)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American conductor and composer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American conductor and composer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Turkish historian, scholar, and poet (b. 1905)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Re%C5%9Fat_Ekrem_Ko%C3%A7u\" class=\"mw-redirect\" title=\"Reşat Ekrem Koçu\"><PERSON><PERSON><PERSON> Ekrem <PERSON></a>, Turkish historian, scholar, and poet (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Re%C5%9Fat_Ekrem_Ko%C3%A7u\" class=\"mw-redirect\" title=\"Reşat Ekrem Koçu\"><PERSON><PERSON><PERSON> Ekrem <PERSON>u</a>, Turkish historian, scholar, and poet (b. 1905)", "links": [{"title": "Reşat Ekrem Koçu", "link": "https://wikipedia.org/wiki/Re%C5%9Fat_Ekrem_Ko%C3%A7u"}]}, {"year": "1976", "text": "<PERSON>, Chinese general and politician, Chairman of the Standing Committee of the National People's Congress (b. 1886)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, <a href=\"https://wikipedia.org/wiki/Chairman_of_the_Standing_Committee_of_the_National_People%27s_Congress\" title=\"Chairman of the Standing Committee of the National People's Congress\">Chairman of the Standing Committee of the National People's Congress</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, <a href=\"https://wikipedia.org/wiki/Chairman_of_the_Standing_Committee_of_the_National_People%27s_Congress\" title=\"Chairman of the Standing Committee of the National People's Congress\">Chairman of the Standing Committee of the National People's Congress</a> (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chairman of the Standing Committee of the National People's Congress", "link": "https://wikipedia.org/wiki/Chairman_of_the_Standing_Committee_of_the_National_People%27s_Congress"}]}, {"year": "1976", "text": "<PERSON>, German geneticist and physician (b. 1887)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geneticist and physician (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geneticist and physician (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-Israeli viola player and composer (b. 1907)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6n_P%C3%A1rtos\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-Israeli viola player and composer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6n_P%C3%A1rtos\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-Israeli viola player and composer (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96d%C3%B6n_P%C3%A1rtos"}]}, {"year": "1978", "text": "<PERSON>, American socialite and fashion style icon (b. 1915)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite and fashion style icon (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite and fashion style icon (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter and producer (b. 1940)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 4th Deputy Prime Minister of India (b. 1908)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India\" title=\"Deputy Prime Minister of India\">Deputy Prime Minister of India</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India\" title=\"Deputy Prime Minister of India\">Deputy Prime Minister of India</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jag<PERSON>van_Ram"}, {"title": "Deputy Prime Minister of India", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Finnish politician (b. 1903)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Hungarian mechanic and politician, Hungarian Minister of the Interior (b. 1912)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian mechanic and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Hungary)\" title=\"Ministry of Interior (Hungary)\">Hungarian Minister of the Interior</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian mechanic and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Hungary)\" title=\"Ministry of Interior (Hungary)\">Hungarian Minister of the Interior</a> (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r"}, {"title": "Ministry of Interior (Hungary)", "link": "https://wikipedia.org/wiki/Ministry_of_Interior_(Hungary)"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian footballer (b. 1954)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>al\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian footballer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian footballer (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>al"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American drag queen performer and activist (b. 1945)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Drag_queen\" title=\"Drag queen\">drag queen</a> performer and activist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Drag_queen\" title=\"Drag queen\">drag queen</a> performer and activist (b. 1945)", "links": [{"title": "Marsh<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Drag queen", "link": "https://wikipedia.org/wiki/Drag_queen"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Kosovan activist (b. 1932)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Ahmet_Haxhiu\" title=\"<PERSON>met Haxhiu\"><PERSON><PERSON></a>, Kosovan activist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ah<PERSON>_Haxhiu\" title=\"<PERSON><PERSON> Haxhiu\"><PERSON><PERSON></a>, Kosovan activist (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ah<PERSON>_<PERSON>hiu"}]}, {"year": "1995", "text": "<PERSON>, Turkish author and poet (b. 1915)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish author and poet (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish author and poet (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Indian director, producer, and screenwriter (b. 1921)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(director)\" title=\"<PERSON><PERSON><PERSON> (director)\"><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(director)\" title=\"<PERSON><PERSON><PERSON> (director)\"><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(director)"}]}, {"year": "1998", "text": "<PERSON>, American cowboy, actor, and singer (b. 1911)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cowboy, actor, and singer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cowboy, actor, and singer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish pianist and composer (b. 1901)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish pianist and composer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaqu%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish pianist and composer (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_Rodrigo"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish pianist and composer (b. 1911)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist and composer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist and composer (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian businessman, founded Reliance Industries (b. 1932)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Dhirubhai_Ambani\" title=\"Dhirubhai Ambani\"><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman, founded <a href=\"https://wikipedia.org/wiki/Reliance_Industries\" title=\"Reliance Industries\">Reliance Industries</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dhirub<PERSON>_Ambani\" title=\"Dhir<PERSON><PERSON> Amban<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman, founded <a href=\"https://wikipedia.org/wiki/Reliance_Industries\" title=\"Reliance Industries\">Reliance Industries</a> (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>bani"}, {"title": "Reliance Industries", "link": "https://wikipedia.org/wiki/Reliance_Industries"}]}, {"year": "2002", "text": "<PERSON>, American director, producer, and screenwriter (b. 1930)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor, singer, and dancer (b. 1908)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Turkish lawyer, historical preservationist, writer and poet (b. 1930)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/%C3%87elik_G%C3%BClersoy\" title=\"<PERSON><PERSON><PERSON> Gülersoy\"><PERSON><PERSON><PERSON></a>, Turkish lawyer, historical preservationist, writer and poet (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%87elik_G%C3%BClersoy\" title=\"<PERSON><PERSON><PERSON>oy\"><PERSON><PERSON><PERSON></a>, Turkish lawyer, historical preservationist, writer and poet (b. 1930)", "links": [{"title": "Çelik G<PERSON>oy", "link": "https://wikipedia.org/wiki/%C3%87elik_G%C3%BClersoy"}]}, {"year": "2004", "text": "<PERSON>, Austrian politician, 10th President of Austria (b. 1932)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Federal_Presidents_of_Austria\" class=\"mw-redirect\" title=\"List of Federal Presidents of Austria\">President of Austria</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Federal_Presidents_of_Austria\" class=\"mw-redirect\" title=\"List of Federal Presidents of Austria\">President of Austria</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Federal Presidents of Austria", "link": "https://wikipedia.org/wiki/List_of_Federal_Presidents_of_Austria"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter (b. 1946)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wright"}]}, {"year": "2005", "text": "<PERSON>, American author and screenwriter (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Malagasy-French novelist and critic, Nobel Prize laureate (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malagasy-French novelist and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malagasy-French novelist and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American actress (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American author (b. 1939)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>iwiss"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Russian author and academic (b. 1932)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and academic (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American businessman and politician, 8th United States Secretary of Defense (b. 1916)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "2010", "text": "<PERSON>, American singer-songwriter and producer (b. 1929)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Australian road racing cyclist (b. 1985)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian road racing cyclist (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian road racing cyclist (b. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Palestinian engineer and politician (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian engineer and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian engineer and politician (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Burmese businessman, co-founded Asia World (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burmese businessman, co-founded <a href=\"https://wikipedia.org/wiki/Asia_World\" title=\"Asia World\">Asia World</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burmese businessman, co-founded <a href=\"https://wikipedia.org/wiki/Asia_World\" title=\"Asia World\">Asia World</a> (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Asia World", "link": "https://wikipedia.org/wiki/Asia_World"}]}, {"year": "2014", "text": "<PERSON>, American soldier, lawyer, and politician, 34th Illinois Secretary of State (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 34th <a href=\"https://wikipedia.org/wiki/Illinois_Secretary_of_State\" title=\"Illinois Secretary of State\">Illinois Secretary of State</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 34th <a href=\"https://wikipedia.org/wiki/Illinois_Secretary_of_State\" title=\"Illinois Secretary of State\">Illinois Secretary of State</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Illinois Secretary of State", "link": "https://wikipedia.org/wiki/Illinois_Secretary_of_State"}]}, {"year": "2015", "text": "<PERSON>, American film producer, and talent agent (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer, and talent agent (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer, and talent agent (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, founder of Japanese cult group <PERSON><PERSON> (b. 1955)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, founder of Japanese cult group <a href=\"https://wikipedia.org/wiki/Aum_Shinrikyo\" title=\"Aum Shinrikyo\">Aum Shinrikyo</a> (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, founder of Japanese cult group <a href=\"https://wikipedia.org/wiki/Aum_Shinrikyo\" title=\"Aum Shinrikyo\">Aum Shinrikyo</a> (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aum_Shinrikyo"}]}, {"year": "2019", "text": "<PERSON>, Brazilian singer-songwriter and guitarist, pioneer of bossa nova music style (b. 1931)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter and guitarist, pioneer of <a href=\"https://wikipedia.org/wiki/Bossa_nova\" title=\"Bossa nova\">bossa nova</a> music style (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter and guitarist, pioneer of <a href=\"https://wikipedia.org/wiki/Bossa_nova\" title=\"Bossa nova\">bossa nova</a> music style (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>o"}, {"title": "Bossa nova", "link": "https://wikipedia.org/wiki/Boss<PERSON>_nova"}]}, {"year": "2020", "text": "<PERSON>, American singer-songwriter, fiddle-player and guitarist (b. 1936)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, fiddle-player and guitarist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, fiddle-player and guitarist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American child rapist (b. 1962)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American child rapist (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American child rapist (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Italian composer, orchestrator, conductor, and trumpet player (b. 1928)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer, orchestrator, conductor, and trumpet player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer, orchestrator, conductor, and trumpet player (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ennio_Morricone"}]}, {"year": "2022", "text": "<PERSON>, American actor (b. 1940)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Italian former professional road racing cyclist (b. 1935)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Arnaldo_Pambianco\" title=\"Arnaldo Pambianco\"><PERSON><PERSON><PERSON></a>, Italian former professional road racing cyclist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arnaldo_Pambianco\" title=\"Arnaldo Pambianco\"><PERSON><PERSON><PERSON></a>, Italian former professional road racing cyclist (b. 1935)", "links": [{"title": "<PERSON><PERSON>do <PERSON>", "link": "https://wikipedia.org/wiki/Arnaldo_Pambianco"}]}, {"year": "2022", "text": "<PERSON><PERSON>, American writer (b. 1968)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer (b. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, American football player (b. 1999)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player (b. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player (b. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}