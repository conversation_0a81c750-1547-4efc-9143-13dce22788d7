{"date": "March 22", "url": "https://wikipedia.org/wiki/March_22", "data": {"Events": [{"year": "106", "text": "Start of the Bostran era, the calendar of the province of Arabia Petraea.", "html": "106 - Start of the <a href=\"https://wikipedia.org/wiki/Bostran_era\" title=\"Bostran era\">Bostran era</a>, the calendar of the province of <a href=\"https://wikipedia.org/wiki/Arabia_Petraea\" title=\"Arabia Petraea\">Arabia Petraea</a>.", "no_year_html": "Start of the <a href=\"https://wikipedia.org/wiki/Bostran_era\" title=\"Bostran era\">Bostran era</a>, the calendar of the province of <a href=\"https://wikipedia.org/wiki/Arabia_Petraea\" title=\"Arabia Petraea\">Arabia Petraea</a>.", "links": [{"title": "Bostran era", "link": "https://wikipedia.org/wiki/Bostran_era"}, {"title": "Arabia Petraea", "link": "https://wikipedia.org/wiki/Arabia_Petraea"}]}, {"year": "235", "text": "Roman emperor <PERSON><PERSON><PERSON> is murdered, marking the start of the Crisis of the Third Century.", "html": "235 - Roman emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Alexander\" title=\"<PERSON><PERSON><PERSON> Alexander\"><PERSON><PERSON><PERSON></a> is murdered, marking the start of the <a href=\"https://wikipedia.org/wiki/Crisis_of_the_Third_Century\" title=\"Crisis of the Third Century\">Crisis of the Third Century</a>.", "no_year_html": "Roman emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Alexander\" title=\"<PERSON><PERSON><PERSON> Alexander\"><PERSON><PERSON><PERSON></a> is murdered, marking the start of the <a href=\"https://wikipedia.org/wiki/Crisis_of_the_Third_Century\" title=\"Crisis of the Third Century\">Crisis of the Third Century</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Crisis of the Third Century", "link": "https://wikipedia.org/wiki/Crisis_of_the_Third_Century"}]}, {"year": "871", "text": "<PERSON><PERSON><PERSON><PERSON> of Wessex is defeated by a Danish invasion army at the Battle of Marton.", "html": "871 - <a href=\"https://wikipedia.org/wiki/%C3%86thelred_of_Wessex\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> of Wessex\"><PERSON><PERSON><PERSON><PERSON> of Wessex</a> is defeated by a <a href=\"https://wikipedia.org/wiki/Great_Heathen_Army\" title=\"Great Heathen Army\">Danish invasion</a> army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Marton\" class=\"mw-redirect\" title=\"Battle of Marton\">Battle of Marton</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86thelred_of_Wessex\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> of Wessex\"><PERSON><PERSON><PERSON><PERSON> of Wessex</a> is defeated by a <a href=\"https://wikipedia.org/wiki/Great_Heathen_Army\" title=\"Great Heathen Army\">Danish invasion</a> army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Marton\" class=\"mw-redirect\" title=\"Battle of Marton\">Battle of Marton</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Wessex", "link": "https://wikipedia.org/wiki/%C3%86thelred_of_Wessex"}, {"title": "Great Heathen Army", "link": "https://wikipedia.org/wiki/Great_Heathen_Army"}, {"title": "Battle of Marton", "link": "https://wikipedia.org/wiki/Battle_of_Marton"}]}, {"year": "1185", "text": "Battle of Yashima: the Japanese forces of the <PERSON>ra clan are defeated by the Minamoto clan.", "html": "1185 - <a href=\"https://wikipedia.org/wiki/Battle_of_Yashima\" title=\"Battle of Yashima\">Battle of Yashima</a>: the Japanese forces of the <a href=\"https://wikipedia.org/wiki/Taira_clan\" title=\"Taira clan\">Taira clan</a> are defeated by the <a href=\"https://wikipedia.org/wiki/Minamoto_clan\" title=\"Minamoto clan\">Minamoto clan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Yashima\" title=\"Battle of Yashima\">Battle of Yashima</a>: the Japanese forces of the <a href=\"https://wikipedia.org/wiki/Taira_clan\" title=\"Taira clan\">Taira clan</a> are defeated by the <a href=\"https://wikipedia.org/wiki/Minamoto_clan\" title=\"Minamoto clan\"><PERSON><PERSON> clan</a>.", "links": [{"title": "Battle of Yashima", "link": "https://wikipedia.org/wiki/Battle_of_Yashima"}, {"title": "Taira clan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_clan"}, {"title": "Minamoto clan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_clan"}]}, {"year": "1312", "text": "Vox in excelso: Pope <PERSON> dissolves the Order of the Knights Templar.", "html": "1312 - <i><a href=\"https://wikipedia.org/wiki/Vox_in_excelso\" title=\"Vox in excelso\">Vox in excelso</a></i>: <a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_V\" title=\"Pope Clement V\">Pope <PERSON> V</a> dissolves the <a href=\"https://wikipedia.org/wiki/Order_of_the_Knights_Templar\" class=\"mw-redirect\" title=\"Order of the Knights Templar\">Order of the Knights Templar</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Vox_in_excelso\" title=\"Vox in excelso\">Vox in excelso</a></i>: <a href=\"https://wikipedia.org/wiki/Pope_Clement_V\" title=\"Pope Clement V\">Pope <PERSON> V</a> dissolves the <a href=\"https://wikipedia.org/wiki/Order_of_the_Knights_Templar\" class=\"mw-redirect\" title=\"Order of the Knights Templar\">Order of the Knights Templar</a>.", "links": [{"title": "Vox in excelso", "link": "https://wikipedia.org/wiki/Vox_in_excelso"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Order of the Knights Templar", "link": "https://wikipedia.org/wiki/Order_of_the_Knights_Templar"}]}, {"year": "1508", "text": "<PERSON> II of Aragon commissions <PERSON><PERSON><PERSON> chief navigator of the Spanish Empire.", "html": "1508 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> commissions <a href=\"https://wikipedia.org/wiki/Ameri<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> chief navigator of the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> commissions <a href=\"https://wikipedia.org/wiki/Amerigo_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> chief navigator of the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a>.", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amerigo_<PERSON>"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}]}, {"year": "1621", "text": "The Pilgrims of Plymouth Colony, lead by governor <PERSON>, sign a peace treaty with Massasoit, sachem of the Wampanoags; <PERSON><PERSON><PERSON> serves as an interpreter between the two sides.", "html": "1621 - The <a href=\"https://wikipedia.org/wiki/Pilgrim_Fathers\" class=\"mw-redirect\" title=\"Pilgrim Fathers\">Pilgrims</a> of <a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">Plymouth Colony</a>, lead by governor <a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, sign a <a href=\"https://wikipedia.org/wiki/Wampanoag_treaty\" title=\"Wampanoag treaty\">peace treaty</a> with <a href=\"https://wikipedia.org/wiki/Massasoit\" title=\"Massasoit\">Massasoit</a>, <a href=\"https://wikipedia.org/wiki/Sachem\" title=\"Sachem\">sachem</a> of the <a href=\"https://wikipedia.org/wiki/Wampanoag_people\" class=\"mw-redirect\" title=\"Wampanoag people\">Wampanoags</a>; <a href=\"https://wikipedia.org/wiki/Squanto\" title=\"Squanto\"><PERSON><PERSON><PERSON></a> serves as an interpreter between the two sides.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pilgrim_Fathers\" class=\"mw-redirect\" title=\"Pilgrim Fathers\">Pilgrims</a> of <a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">Plymouth Colony</a>, lead by governor <a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, sign a <a href=\"https://wikipedia.org/wiki/Wampanoag_treaty\" title=\"Wampanoag treaty\">peace treaty</a> with <a href=\"https://wikipedia.org/wiki/Massasoit\" title=\"Massasoit\">Massasoit</a>, <a href=\"https://wikipedia.org/wiki/Sachem\" title=\"Sachem\">sachem</a> of the <a href=\"https://wikipedia.org/wiki/Wampanoag_people\" class=\"mw-redirect\" title=\"Wampanoag people\">Wampanoags</a>; <a href=\"https://wikipedia.org/wiki/Squanto\" title=\"Squanto\"><PERSON><PERSON><PERSON></a> serves as an interpreter between the two sides.", "links": [{"title": "Pilgrim Fathers", "link": "https://wikipedia.org/wiki/Pilgrim_Fathers"}, {"title": "Plymouth Colony", "link": "https://wikipedia.org/wiki/Plymouth_Colony"}, {"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_(governor)"}, {"title": "Wampanoag treaty", "link": "https://wikipedia.org/wiki/Wampanoag_treaty"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Massasoit"}, {"title": "Sachem", "link": "https://wikipedia.org/wiki/Sachem"}, {"title": "Wampanoag people", "link": "https://wikipedia.org/wiki/Wampanoag_people"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Squanto"}]}, {"year": "1622", "text": "Jamestown massacre: Algonquians kill 347 English settlers around Jamestown, Virginia, a third of the colony's population, during the Second Anglo-Powhatan War.", "html": "1622 - <a href=\"https://wikipedia.org/wiki/Indian_massacre_of_1622\" title=\"Indian massacre of 1622\">Jamestown massacre</a>: <a href=\"https://wikipedia.org/wiki/Algonquian_peoples\" title=\"Algonquian peoples\">Algonquians</a> kill 347 English settlers around <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">Jamestown, Virginia</a>, a third of the colony's population, during the <a href=\"https://wikipedia.org/wiki/Anglo-Powhatan_Wars\" title=\"Anglo-Powhatan Wars\">Second Anglo-Powhatan War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indian_massacre_of_1622\" title=\"Indian massacre of 1622\">Jamestown massacre</a>: <a href=\"https://wikipedia.org/wiki/Algonquian_peoples\" title=\"Algonquian peoples\">Algonquians</a> kill 347 English settlers around <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">Jamestown, Virginia</a>, a third of the colony's population, during the <a href=\"https://wikipedia.org/wiki/Anglo-Powhatan_Wars\" title=\"Anglo-Powhatan Wars\">Second Anglo-Powhatan War</a>.", "links": [{"title": "Indian massacre of 1622", "link": "https://wikipedia.org/wiki/Indian_massacre_of_1622"}, {"title": "Algonquian peoples", "link": "https://wikipedia.org/wiki/Algonquian_peoples"}, {"title": "Jamestown, Virginia", "link": "https://wikipedia.org/wiki/Jamestown,_Virginia"}, {"title": "Anglo-Powhatan Wars", "link": "https://wikipedia.org/wiki/Anglo-Powhatan_Wars"}]}, {"year": "1631", "text": "The Massachusetts Bay Colony outlaws the possession of cards, dice, and gaming tables.", "html": "1631 - The <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> outlaws the possession of <a href=\"https://wikipedia.org/wiki/Playing_card\" title=\"Playing card\">cards</a>, <a href=\"https://wikipedia.org/wiki/Dice\" title=\"Dice\">dice</a>, and gaming tables.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> outlaws the possession of <a href=\"https://wikipedia.org/wiki/Playing_card\" title=\"Playing card\">cards</a>, <a href=\"https://wikipedia.org/wiki/Dice\" title=\"Dice\">dice</a>, and gaming tables.", "links": [{"title": "Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Bay_Colony"}, {"title": "Playing card", "link": "https://wikipedia.org/wiki/Playing_card"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dice"}]}, {"year": "1638", "text": "<PERSON> is expelled from Massachusetts Bay Colony for religious dissent.", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is expelled from <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> for religious dissent.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is expelled from <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> for religious dissent.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Bay_Colony"}]}, {"year": "1668", "text": "Notable Privateer <PERSON> lands in Cuba to raid and plunder the inland town of Puerto del Príncipe during the latter stages of the Anglo-Spanish War (1654-1660).", "html": "1668 - Notable <a href=\"https://wikipedia.org/wiki/Privateer\" title=\"Privateer\">Privateer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> to raid and plunder the inland town of <a href=\"https://wikipedia.org/wiki/Puerto_del_Pr%C3%ADncipe\" class=\"mw-redirect\" title=\"Puerto del Príncipe\">Puerto del Príncipe</a> during the latter stages of the <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%931660)\" title=\"Anglo-Spanish War (1654-1660)\">Anglo-Spanish War (1654-1660)</a>.", "no_year_html": "Notable <a href=\"https://wikipedia.org/wiki/Privateer\" title=\"Privateer\">Privateer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> to raid and plunder the inland town of <a href=\"https://wikipedia.org/wiki/Puerto_del_Pr%C3%ADncipe\" class=\"mw-redirect\" title=\"Puerto del Príncipe\">Puerto del Príncipe</a> during the latter stages of the <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%931660)\" title=\"Anglo-Spanish War (1654-1660)\">Anglo-Spanish War (1654-1660)</a>.", "links": [{"title": "Privateer", "link": "https://wikipedia.org/wiki/Privateer"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Puerto del Príncipe", "link": "https://wikipedia.org/wiki/Puerto_del_Pr%C3%ADncipe"}, {"title": "Anglo-Spanish War (1654-1660)", "link": "https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%931660)"}]}, {"year": "1739", "text": "<PERSON><PERSON> occupies Delhi in India and sacks the city, stealing the jewels of the Peacock Throne.", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> occupies <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a> in India and sacks the city, stealing the jewels of the <a href=\"https://wikipedia.org/wiki/Peacock_Throne\" title=\"Peacock Throne\">Peacock Throne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> occupies <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a> in India and sacks the city, stealing the jewels of the <a href=\"https://wikipedia.org/wiki/Peacock_Throne\" title=\"Peacock Throne\"><PERSON> Throne</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Delhi", "link": "https://wikipedia.org/wiki/Delhi"}, {"title": "<PERSON> Throne", "link": "https://wikipedia.org/wiki/Peacock_Throne"}]}, {"year": "1765", "text": "The British Parliament passes the Stamp Act that introduces a tax to be levied directly on its American colonies.", "html": "1765 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_Great_Britain\" title=\"Parliament of Great Britain\">British Parliament</a> passes the <a href=\"https://wikipedia.org/wiki/Stamp_Act_1765\" title=\"Stamp Act 1765\">Stamp Act</a> that introduces a tax to be levied directly on its <a href=\"https://wikipedia.org/wiki/British_colonization_of_the_Americas\" title=\"British colonization of the Americas\">American colonies</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_Great_Britain\" title=\"Parliament of Great Britain\">British Parliament</a> passes the <a href=\"https://wikipedia.org/wiki/Stamp_Act_1765\" title=\"Stamp Act 1765\">Stamp Act</a> that introduces a tax to be levied directly on its <a href=\"https://wikipedia.org/wiki/British_colonization_of_the_Americas\" title=\"British colonization of the Americas\">American colonies</a>.", "links": [{"title": "Parliament of Great Britain", "link": "https://wikipedia.org/wiki/Parliament_of_Great_Britain"}, {"title": "Stamp Act 1765", "link": "https://wikipedia.org/wiki/Stamp_Act_1765"}, {"title": "British colonization of the Americas", "link": "https://wikipedia.org/wiki/British_colonization_of_the_Americas"}]}, {"year": "1784", "text": "The Emerald Buddha is moved with great ceremony to its current location in Wat Phra Kaew, Thailand.", "html": "1784 - The <a href=\"https://wikipedia.org/wiki/Emerald_Buddha\" title=\"Emerald Buddha\">Emerald Buddha</a> is moved with great ceremony to its current location in <a href=\"https://wikipedia.org/wiki/Wat_Phra_Kaew\" title=\"Wat Phra Kaew\">Wat Phra Kaew</a>, Thailand.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Emerald_Buddha\" title=\"Emerald Buddha\">Emerald Buddha</a> is moved with great ceremony to its current location in <a href=\"https://wikipedia.org/wiki/Wat_<PERSON>ra_Kaew\" title=\"Wat Phra Kaew\">Wat Phra Kaew</a>, Thailand.", "links": [{"title": "Emerald Buddha", "link": "https://wikipedia.org/wiki/Emerald_Buddha"}, {"title": "Wat Phra Kaew", "link": "https://wikipedia.org/wiki/Wat_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1792", "text": "Battle of Croix-des-Bouquets: Black slave insurgents gain a victory in the first major battle of the Haitian Revolution.", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Battle_of_Croix-des-Bouquets\" title=\"Battle of Croix-des-Bouquets\">Battle of Croix-des-Bouquets</a>: Black slave insurgents gain a victory in the first major battle of the <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Croix-des-Bouquets\" title=\"Battle of Croix-des-Bouquets\">Battle of Croix-des-Bouquets</a>: Black slave insurgents gain a victory in the first major battle of the <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>.", "links": [{"title": "Battle of Croix-des-Bouquets", "link": "https://wikipedia.org/wiki/Battle_of_Croix-des-Bouquets"}, {"title": "Haitian Revolution", "link": "https://wikipedia.org/wiki/Haitian_Revolution"}]}, {"year": "1794", "text": "The Slave Trade Act of 1794 bans the export of slaves from the United States, and prohibits American citizens from outfitting a ship for the purpose of importing slaves.", "html": "1794 - The <a href=\"https://wikipedia.org/wiki/Slave_Trade_Act_of_1794\" title=\"Slave Trade Act of 1794\">Slave Trade Act of 1794</a> bans the export of slaves from the United States, and prohibits American citizens from outfitting a <a href=\"https://wikipedia.org/wiki/Slave_ship\" title=\"Slave ship\">ship for the purpose of importing slaves</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Slave_Trade_Act_of_1794\" title=\"Slave Trade Act of 1794\">Slave Trade Act of 1794</a> bans the export of slaves from the United States, and prohibits American citizens from outfitting a <a href=\"https://wikipedia.org/wiki/Slave_ship\" title=\"Slave ship\">ship for the purpose of importing slaves</a>.", "links": [{"title": "Slave Trade Act of 1794", "link": "https://wikipedia.org/wiki/Slave_Trade_Act_of_1794"}, {"title": "Slave ship", "link": "https://wikipedia.org/wiki/Slave_ship"}]}, {"year": "1829", "text": "In the London Protocol, the three protecting powers (United Kingdom, France and Russia) establish the borders of Greece.", "html": "1829 - In the <a href=\"https://wikipedia.org/wiki/London_Protocol_(1829)\" title=\"London Protocol (1829)\">London Protocol</a>, the three protecting powers (<a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom</a>, <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> and <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a>) establish the borders of <a href=\"https://wikipedia.org/wiki/First_Hellenic_Republic\" title=\"First Hellenic Republic\">Greece</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/London_Protocol_(1829)\" title=\"London Protocol (1829)\">London Protocol</a>, the three protecting powers (<a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom</a>, <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> and <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a>) establish the borders of <a href=\"https://wikipedia.org/wiki/First_Hellenic_Republic\" title=\"First Hellenic Republic\">Greece</a>.", "links": [{"title": "London Protocol (1829)", "link": "https://wikipedia.org/wiki/London_Protocol_(1829)"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "First Hellenic Republic", "link": "https://wikipedia.org/wiki/First_Hellenic_Republic"}]}, {"year": "1849", "text": "The Austrians defeat the Piedmontese at the Battle of Novara.", "html": "1849 - The <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrians</a> defeat the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sardinia\" title=\"Kingdom of Sardinia\">Piedmontese</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Novara_(1849)\" title=\"Battle of Novara (1849)\">Battle of Novara</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrians</a> defeat the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sardinia\" title=\"Kingdom of Sardinia\">Piedmontese</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Novara_(1849)\" title=\"Battle of Novara (1849)\">Battle of Novara</a>.", "links": [{"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "Kingdom of Sardinia", "link": "https://wikipedia.org/wiki/Kingdom_of_Sardinia"}, {"title": "Battle of Novara (1849)", "link": "https://wikipedia.org/wiki/Battle_of_Novara_(1849)"}]}, {"year": "1871", "text": "In North Carolina, <PERSON> becomes the first governor of a U.S. state to be removed from office by impeachment.", "html": "1871 - In <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Governor_(United_States)\" title=\"Governor (United States)\">governor</a> of a U.S. state to be removed from office by <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Governor_(United_States)\" title=\"Governor (United States)\">governor</a> of a U.S. state to be removed from office by <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a>.", "links": [{"title": "North Carolina", "link": "https://wikipedia.org/wiki/North_Carolina"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor (United States)", "link": "https://wikipedia.org/wiki/Governor_(United_States)"}, {"title": "Impeachment", "link": "https://wikipedia.org/wiki/Impeachment"}]}, {"year": "1873", "text": "The Spanish National Assembly abolishes slavery in Puerto Rico.", "html": "1873 - The <a href=\"https://wikipedia.org/wiki/Cortes_Generales\" title=\"Cortes Generales\">Spanish National Assembly</a> abolishes <a href=\"https://wikipedia.org/wiki/Slavery_in_the_Spanish_New_World_colonies\" class=\"mw-redirect\" title=\"Slavery in the Spanish New World colonies\">slavery in Puerto Rico</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cortes_Generales\" title=\"Cortes Generales\">Spanish National Assembly</a> abolishes <a href=\"https://wikipedia.org/wiki/Slavery_in_the_Spanish_New_World_colonies\" class=\"mw-redirect\" title=\"Slavery in the Spanish New World colonies\">slavery in Puerto Rico</a>.", "links": [{"title": "Cortes Generales", "link": "https://wikipedia.org/wiki/Cortes_Generales"}, {"title": "Slavery in the Spanish New World colonies", "link": "https://wikipedia.org/wiki/Slavery_in_the_Spanish_New_World_colonies"}]}, {"year": "1894", "text": "The Stanley Cup ice hockey competition is held for the first time, in Montreal, Canada.", "html": "1894 - The <a href=\"https://wikipedia.org/wiki/Stanley_Cup\" title=\"Stanley Cup\">Stanley Cup</a> ice hockey competition is held for the first time, in Montreal, Canada.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Stanley_Cup\" title=\"Stanley Cup\">Stanley Cup</a> ice hockey competition is held for the first time, in Montreal, Canada.", "links": [{"title": "Stanley Cup", "link": "https://wikipedia.org/wiki/Stanley_Cup"}]}, {"year": "1895", "text": "Before the Société pour L'Encouragement à l'Industrie, brothers <PERSON> and <PERSON> demonstrate movie film technology publicly for the first time.", "html": "1895 - Before the Société pour L'Encouragement à l'Industrie, brothers <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> demonstrate movie <a href=\"https://wikipedia.org/wiki/Film\" title=\"Film\">film</a> technology publicly for the first time.", "no_year_html": "Before the Société pour L'Encouragement à l'Industrie, brothers <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> demonstrate movie <a href=\"https://wikipedia.org/wiki/Film\" title=\"Film\">film</a> technology publicly for the first time.", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re"}, {"title": "Film", "link": "https://wikipedia.org/wiki/Film"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON><PERSON> wins the first modern Olympic marathon race with a time of three hours and 18 minutes.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON><PERSON>_Vasilak<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Vasilak<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> wins the first modern Olympic <a href=\"https://wikipedia.org/wiki/Marathon\" title=\"Marathon\">marathon</a> race with a time of three hours and 18 minutes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON><PERSON>_Vasi<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> V<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> wins the first modern Olympic <a href=\"https://wikipedia.org/wiki/Marathon\" title=\"Marathon\">marathon</a> race with a time of three hours and 18 minutes.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Marathon", "link": "https://wikipedia.org/wiki/Marathon"}]}, {"year": "1906", "text": "The first England vs France rugby union match is played at Parc des Princes in Paris.", "html": "1906 - The first <a href=\"https://wikipedia.org/wiki/England_national_rugby_union_team\" title=\"England national rugby union team\">England</a> vs <a href=\"https://wikipedia.org/wiki/France_national_rugby_union_team\" title=\"France national rugby union team\">France</a> rugby union match is played at <a href=\"https://wikipedia.org/wiki/Parc_des_Princes\" title=\"Parc des Princes\">Parc des Princes</a> in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/England_national_rugby_union_team\" title=\"England national rugby union team\">England</a> vs <a href=\"https://wikipedia.org/wiki/France_national_rugby_union_team\" title=\"France national rugby union team\">France</a> rugby union match is played at <a href=\"https://wikipedia.org/wiki/Parc_des_Princes\" title=\"Parc des Princes\">Parc des Princes</a> in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "links": [{"title": "England national rugby union team", "link": "https://wikipedia.org/wiki/England_national_rugby_union_team"}, {"title": "France national rugby union team", "link": "https://wikipedia.org/wiki/France_national_rugby_union_team"}, {"title": "Parc des Princes", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_des_<PERSON>"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "1913", "text": "Mystic <PERSON><PERSON>, the self-proclaimed Emperor of Vietnam, is arrested for organising a revolt against the colonial rule of French Indochina, which was nevertheless carried out by his supporters the following day.", "html": "1913 - Mystic <a href=\"https://wikipedia.org/wiki/Phan_X%C3%<PERSON><PERSON>_Long\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the self-proclaimed <a href=\"https://wikipedia.org/wiki/Emperor_of_Vietnam\" class=\"mw-redirect\" title=\"Emperor of Vietnam\">Emperor of Vietnam</a>, is arrested for organising a revolt against the colonial rule of <a href=\"https://wikipedia.org/wiki/French_Indochina\" title=\"French Indochina\">French Indochina</a>, which was nevertheless carried out by his supporters the following day.", "no_year_html": "Mystic <a href=\"https://wikipedia.org/wiki/Phan_X%C3%AD<PERSON>_Long\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the self-proclaimed <a href=\"https://wikipedia.org/wiki/Emperor_of_Vietnam\" class=\"mw-redirect\" title=\"Emperor of Vietnam\">Emperor of Vietnam</a>, is arrested for organising a revolt against the colonial rule of <a href=\"https://wikipedia.org/wiki/French_Indochina\" title=\"French Indochina\">French Indochina</a>, which was nevertheless carried out by his supporters the following day.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Phan_X%C3%ADch_Long"}, {"title": "Emperor of Vietnam", "link": "https://wikipedia.org/wiki/Emperor_of_Vietnam"}, {"title": "French Indochina", "link": "https://wikipedia.org/wiki/French_Indochina"}]}, {"year": "1916", "text": "<PERSON> abdicates as Emperor of China, restoring the Republic and returning to the Presidency.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Yuan_<PERSON>\" title=\"Yuan Shikai\"><PERSON></a> abdicates as <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Emperor of China</a>, restoring the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic</a> and returning to the <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">Presidency</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yuan_Shi<PERSON>\" title=\"Yuan Shikai\"><PERSON></a> abdicates as <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Emperor of China</a>, restoring the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic</a> and returning to the <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">Presidency</a>.", "links": [{"title": "Yuan Shikai", "link": "https://wikipedia.org/wiki/Yuan_Shikai"}, {"title": "Emperor of China", "link": "https://wikipedia.org/wiki/Emperor_of_China"}, {"title": "Republic of China (1912-1949)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)"}, {"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}]}, {"year": "1920", "text": "Azeri and Turkish army soldiers with participation of Kurdish gangs attack the Armenian inhabitants of Shushi (Nagorno Karabakh).", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Azerbaijan_Democratic_Republic\" title=\"Azerbaijan Democratic Republic\">Azeri</a> and <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkish</a> army soldiers with participation of <a href=\"https://wikipedia.org/wiki/Kurds\" title=\"Kurds\">Kurdish</a> gangs <a href=\"https://wikipedia.org/wiki/Shusha_massacre\" title=\"Shusha massacre\">attack</a> the <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenian</a> inhabitants of <a href=\"https://wikipedia.org/wiki/Shusha\" title=\"Shusha\">Shushi</a> (<a href=\"https://wikipedia.org/wiki/Nagorno-Karabakh\" title=\"Nagorno-Karabakh\">Nagorno Karabakh</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Azerbaijan_Democratic_Republic\" title=\"Azerbaijan Democratic Republic\">Azeri</a> and <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkish</a> army soldiers with participation of <a href=\"https://wikipedia.org/wiki/Kurds\" title=\"Kurds\">Kurdish</a> gangs <a href=\"https://wikipedia.org/wiki/Shusha_massacre\" title=\"Shusha massacre\">attack</a> the <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenian</a> inhabitants of <a href=\"https://wikipedia.org/wiki/Shusha\" title=\"Shusha\">Shushi</a> (<a href=\"https://wikipedia.org/wiki/Nagorno-Karabakh\" title=\"Nagorno-Karabakh\">Nagorno Karabakh</a>).", "links": [{"title": "Azerbaijan Democratic Republic", "link": "https://wikipedia.org/wiki/Azerbaijan_Democratic_Republic"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Kurds", "link": "https://wikipedia.org/wiki/Kurds"}, {"title": "Shusha massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_massacre"}, {"title": "Armenians", "link": "https://wikipedia.org/wiki/Armenians"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Nagorno-Karabakh", "link": "https://wikipedia.org/wiki/Nagorno-Karabakh"}]}, {"year": "1933", "text": "Cullen-Harrison Act: President <PERSON> signs an amendment to the Volstead Act, legalizing the manufacture and sale of \"3.2 beer\" (3.2% alcohol by weight, approximately 4% alcohol by volume) and light wines.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Cullen%E2%80%93Harrison_Act\" title=\"Cullen-Harrison Act\">Cullen-Harrison Act</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs an amendment to the <a href=\"https://wikipedia.org/wiki/Volstead_Act\" title=\"Volstead Act\">Volstead Act</a>, legalizing the manufacture and sale of \"3.2 beer\" (3.2% alcohol by weight, approximately 4% alcohol by volume) and light wines.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cullen%E2%80%93Harrison_Act\" title=\"Cullen-Harrison Act\">Cullen-Harrison Act</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs an amendment to the <a href=\"https://wikipedia.org/wiki/Volstead_Act\" title=\"Volstead Act\">Volstead Act</a>, legalizing the manufacture and sale of \"3.2 beer\" (3.2% alcohol by weight, approximately 4% alcohol by volume) and light wines.", "links": [{"title": "Cullen-Harrison Act", "link": "https://wikipedia.org/wiki/Cullen%E2%80%93Harrison_Act"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Volstead Act", "link": "https://wikipedia.org/wiki/Volstead_Act"}]}, {"year": "1933", "text": "Nazi Germany opens its first concentration camp, Dachau.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> opens its first <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camp</a>, <a href=\"https://wikipedia.org/wiki/Dachau_concentration_camp\" title=\"Dachau concentration camp\">Dachau</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> opens its first <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camp</a>, <a href=\"https://wikipedia.org/wiki/Dachau_concentration_camp\" title=\"Dachau concentration camp\">Dachau</a>.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Nazi concentration camps", "link": "https://wikipedia.org/wiki/Nazi_concentration_camps"}, {"title": "Dachau concentration camp", "link": "https://wikipedia.org/wiki/Dachau_concentration_camp"}]}, {"year": "1934", "text": "The first Masters Tournament is held at Augusta National Golf Club in Georgia.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/1934_Masters_Tournament\" title=\"1934 Masters Tournament\">first Masters Tournament</a> is held at <a href=\"https://wikipedia.org/wiki/Augusta_National_Golf_Club\" title=\"Augusta National Golf Club\">Augusta National Golf Club</a> in <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1934_Masters_Tournament\" title=\"1934 Masters Tournament\">first Masters Tournament</a> is held at <a href=\"https://wikipedia.org/wiki/Augusta_National_Golf_Club\" title=\"Augusta National Golf Club\">Augusta National Golf Club</a> in <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a>.", "links": [{"title": "1934 Masters Tournament", "link": "https://wikipedia.org/wiki/1934_Masters_Tournament"}, {"title": "Augusta National Golf Club", "link": "https://wikipedia.org/wiki/Augusta_National_Golf_Club"}, {"title": "Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S._state)"}]}, {"year": "1939", "text": "Germany takes <PERSON><PERSON> from Lithuania.", "html": "1939 - Germany <a href=\"https://wikipedia.org/wiki/1939_German_ultimatum_to_Lithuania\" title=\"1939 German ultimatum to Lithuania\">takes <PERSON><PERSON> from Lithuania</a>.", "no_year_html": "Germany <a href=\"https://wikipedia.org/wiki/1939_German_ultimatum_to_Lithuania\" title=\"1939 German ultimatum to Lithuania\">takes <PERSON><PERSON> from Lithuania</a>.", "links": [{"title": "1939 German ultimatum to Lithuania", "link": "https://wikipedia.org/wiki/1939_German_ultimatum_to_Lithuania"}]}, {"year": "1942", "text": "World War II: In the Mediterranean Sea, the Royal Navy confronts Italy's Regia Marina in the Second Battle of Sirte.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In the Mediterranean Sea, the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> confronts Italy's <a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Regia Marina</a> in the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Sirte\" title=\"Second Battle of Sirte\">Second Battle of Sirte</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In the Mediterranean Sea, the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> confronts Italy's <a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Regia Marina</a> in the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Sirte\" title=\"Second Battle of Sirte\">Second Battle of Sirte</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Regia Marina", "link": "https://wikipedia.org/wiki/Regia_Marina"}, {"title": "Second Battle of Sirte", "link": "https://wikipedia.org/wiki/Second_Battle_of_Sirte"}]}, {"year": "1943", "text": "World War II: The entire village of Khatyn (in present-day Republic of Belarus) is burnt alive by Schutzmannschaft Battalion 118.", "html": "1943 - World War II: The entire village of <a href=\"https://wikipedia.org/wiki/Khatyn_massacre\" title=\"Khatyn massacre\">Khatyn</a> (in present-day <a href=\"https://wikipedia.org/wiki/Republic_of_Belarus\" class=\"mw-redirect\" title=\"Republic of Belarus\">Republic of Belarus</a>) is burnt alive by <a href=\"https://wikipedia.org/wiki/Schutzmannschaft_Battalion_118\" title=\"Schutzmannschaft Battalion 118\">Schutzmannschaft Battalion 118</a>.", "no_year_html": "World War II: The entire village of <a href=\"https://wikipedia.org/wiki/Khatyn_massacre\" title=\"Khatyn massacre\">Khatyn</a> (in present-day <a href=\"https://wikipedia.org/wiki/Republic_of_Belarus\" class=\"mw-redirect\" title=\"Republic of Belarus\">Republic of Belarus</a>) is burnt alive by <a href=\"https://wikipedia.org/wiki/Schutzmannschaft_Battalion_118\" title=\"Schutzmannschaft Battalion 118\">Schutzmannschaft Battalion 118</a>.", "links": [{"title": "K<PERSON>yn massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}, {"title": "Republic of Belarus", "link": "https://wikipedia.org/wiki/Republic_of_Belarus"}, {"title": "Schutzmannschaft Battalion 118", "link": "https://wikipedia.org/wiki/Schutzmannschaft_Battalion_118"}]}, {"year": "1945", "text": "World War II: The city of Hildesheim, Germany, is heavily damaged in a British air raid, though it had little military significance and Germany was on the verge of final defeat.", "html": "1945 - World War II: The city of <a href=\"https://wikipedia.org/wiki/Hildesheim\" title=\"Hildesheim\">Hildesheim</a>, Germany, is heavily damaged in a <a href=\"https://wikipedia.org/wiki/Bombing_of_Hildesheim_in_World_War_II\" title=\"Bombing of Hildesheim in World War II\">British air raid</a>, though it had little military significance and Germany was on the verge of final defeat.", "no_year_html": "World War II: The city of <a href=\"https://wikipedia.org/wiki/Hildesheim\" title=\"Hildesheim\">Hildesheim</a>, Germany, is heavily damaged in a <a href=\"https://wikipedia.org/wiki/Bombing_of_Hildesheim_in_World_War_II\" title=\"Bombing of Hildesheim in World War II\">British air raid</a>, though it had little military significance and Germany was on the verge of final defeat.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>sheim"}, {"title": "Bombing of Hildesheim in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Hildesheim_in_World_War_II"}]}, {"year": "1945", "text": "The Arab League is founded when a charter is adopted in Cairo, Egypt.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Arab_League\" title=\"Arab League\">Arab League</a> is founded when a charter is adopted in <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, Egypt.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arab_League\" title=\"Arab League\">Arab League</a> is founded when a charter is adopted in <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, Egypt.", "links": [{"title": "Arab League", "link": "https://wikipedia.org/wiki/Arab_League"}, {"title": "Cairo", "link": "https://wikipedia.org/wiki/Cairo"}]}, {"year": "1946", "text": "The United Kingdom grants full independence to Transjordan.", "html": "1946 - The United Kingdom grants full independence to <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Transjordan</a>.", "no_year_html": "The United Kingdom grants full independence to <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Transjordan</a>.", "links": [{"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}]}, {"year": "1955", "text": "A United States Navy Douglas R6D-1 Liftmaster crashes into Hawaii's Waiʻanae Range, killing 66.", "html": "1955 - A <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> <a href=\"https://wikipedia.org/wiki/Douglas_DC-6\" title=\"Douglas DC-6\">Douglas R6D-1 Liftmaster</a> <a href=\"https://wikipedia.org/wiki/1955_Hawaii_R6D-1_crash\" title=\"1955 Hawaii R6D-1 crash\">crashes</a> into Hawaii's <a href=\"https://wikipedia.org/wiki/Wai%CA%BBanae_Range\" title=\"Waiʻanae Range\">Waiʻanae Range</a>, killing 66.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> <a href=\"https://wikipedia.org/wiki/Douglas_DC-6\" title=\"Douglas DC-6\">Douglas R6D-1 Liftmaster</a> <a href=\"https://wikipedia.org/wiki/1955_Hawaii_R6D-1_crash\" title=\"1955 Hawaii R6D-1 crash\">crashes</a> into Hawaii's <a href=\"https://wikipedia.org/wiki/Wai%CA%BBanae_Range\" title=\"Waiʻanae Range\">Waiʻanae Range</a>, killing 66.", "links": [{"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "Douglas DC-6", "link": "https://wikipedia.org/wiki/Douglas_DC-6"}, {"title": "1955 Hawaii R6D-1 crash", "link": "https://wikipedia.org/wiki/1955_Hawaii_R6D-1_crash"}, {"title": "Waiʻanae Range", "link": "https://wikipedia.org/wiki/Wai%CA%BBanae_Range"}]}, {"year": "1960", "text": "<PERSON> and <PERSON> receive the first patent for a laser.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Charles <PERSON> Townes\"><PERSON></a> receive the first <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for a <a href=\"https://wikipedia.org/wiki/Laser\" title=\"Laser\">laser</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> receive the first <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for a <a href=\"https://wikipedia.org/wiki/Laser\" title=\"Laser\">laser</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_Hard_Townes"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Laser", "link": "https://wikipedia.org/wiki/Laser"}]}, {"year": "1963", "text": "The Beatles release their debut album Please Please Me.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> release their debut album <i><a href=\"https://wikipedia.org/wiki/Please_Please_Me\" title=\"Please Please Me\">Please Please Me</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> release their debut album <i><a href=\"https://wikipedia.org/wiki/Please_Please_Me\" title=\"Please Please Me\">Please Please Me</a></i>.", "links": [{"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}, {"title": "Please Please Me", "link": "https://wikipedia.org/wiki/Please_Please_Me"}]}, {"year": "1970", "text": "Chicano residents in San Diego, California occupy a site under the Coronado Bridge, leading to the creation of Chicano Park.", "html": "1970 - Chicano residents in <a href=\"https://wikipedia.org/wiki/San_Diego\" title=\"San Diego\">San Diego, California</a> occupy a site under the <a href=\"https://wikipedia.org/wiki/San_Diego%E2%80%93Coronado_Bridge\" title=\"San Diego-Coronado Bridge\">Coronado Bridge</a>, leading to the creation of <a href=\"https://wikipedia.org/wiki/Chicano_Park\" title=\"Chicano Park\">Chicano Park</a>.", "no_year_html": "Chicano residents in <a href=\"https://wikipedia.org/wiki/San_Diego\" title=\"San Diego\">San Diego, California</a> occupy a site under the <a href=\"https://wikipedia.org/wiki/San_Diego%E2%80%93Coronado_Bridge\" title=\"San Diego-Coronado Bridge\">Coronado Bridge</a>, leading to the creation of <a href=\"https://wikipedia.org/wiki/Chicano_Park\" title=\"Chicano Park\">Chicano Park</a>.", "links": [{"title": "San Diego", "link": "https://wikipedia.org/wiki/San_Diego"}, {"title": "San Diego-Coronado Bridge", "link": "https://wikipedia.org/wiki/San_Diego%E2%80%93Coronado_Bridge"}, {"title": "Chicano Park", "link": "https://wikipedia.org/wiki/Chicano_Park"}]}, {"year": "1972", "text": "The United States Congress sends the Equal Rights Amendment to the states for ratification.", "html": "1972 - The United States Congress sends the <a href=\"https://wikipedia.org/wiki/Equal_Rights_Amendment\" title=\"Equal Rights Amendment\">Equal Rights Amendment</a> to the states for ratification.", "no_year_html": "The United States Congress sends the <a href=\"https://wikipedia.org/wiki/Equal_Rights_Amendment\" title=\"Equal Rights Amendment\">Equal Rights Amendment</a> to the states for ratification.", "links": [{"title": "Equal Rights Amendment", "link": "https://wikipedia.org/wiki/Equal_Rights_Amendment"}]}, {"year": "1972", "text": "In <PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> <PERSON>, the United States Supreme Court decides that unmarried persons have the right to possess contraceptives.", "html": "1972 - In <i><a href=\"https://wikipedia.org/wiki/<PERSON>isenstad<PERSON>_v._<PERSON>\" title=\"<PERSON>isenstad<PERSON> v<PERSON> Baird\"><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> Baird</a></i>, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> decides that unmarried persons have the right to possess <a href=\"https://wikipedia.org/wiki/Birth_control_in_the_United_States\" title=\"Birth control in the United States\">contraceptives</a>.", "no_year_html": "In <i><a href=\"https://wikipedia.org/wiki/<PERSON>ise<PERSON>ad<PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON>ise<PERSON>ad<PERSON> v<PERSON> Baird\"><PERSON>isenstad<PERSON> v<PERSON> Baird</a></i>, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> decides that unmarried persons have the right to possess <a href=\"https://wikipedia.org/wiki/Birth_control_in_the_United_States\" title=\"Birth control in the United States\">contraceptives</a>.", "links": [{"title": "Eisenstadt v. Baird", "link": "https://wikipedia.org/wiki/Eisenstadt_v._<PERSON>"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Birth control in the United States", "link": "https://wikipedia.org/wiki/Birth_control_in_the_United_States"}]}, {"year": "1975", "text": "A fire at the Browns Ferry Nuclear Power Plant in Decatur, Alabama, causes a dangerous reduction in cooling water levels.", "html": "1975 - A fire at the <a href=\"https://wikipedia.org/wiki/Browns_Ferry_Nuclear_Power_Plant\" class=\"mw-redirect\" title=\"Browns Ferry Nuclear Power Plant\">Browns Ferry Nuclear Power Plant</a> in <a href=\"https://wikipedia.org/wiki/Decatur,_Alabama\" title=\"Decatur, Alabama\">Decatur, Alabama</a>, causes a dangerous reduction in cooling water levels.", "no_year_html": "A fire at the <a href=\"https://wikipedia.org/wiki/Browns_Ferry_Nuclear_Power_Plant\" class=\"mw-redirect\" title=\"Browns Ferry Nuclear Power Plant\">Browns Ferry Nuclear Power Plant</a> in <a href=\"https://wikipedia.org/wiki/Decatur,_Alabama\" title=\"Decatur, Alabama\">Decatur, Alabama</a>, causes a dangerous reduction in cooling water levels.", "links": [{"title": "Browns Ferry Nuclear Power Plant", "link": "https://wikipedia.org/wiki/Browns_Ferry_Nuclear_Power_Plant"}, {"title": "Decatur, Alabama", "link": "https://wikipedia.org/wiki/Decatur,_Alabama"}]}, {"year": "1978", "text": "<PERSON> of The Flying Wallendas dies after falling off a tight-rope suspended between two hotels in San Juan, Puerto Rico.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/The_Flying_Wallendas\" title=\"The Flying Wallendas\">The Flying Wallendas</a> dies after falling off a tight-rope suspended between two hotels in <a href=\"https://wikipedia.org/wiki/San_Juan,_Puerto_Rico\" title=\"San Juan, Puerto Rico\">San Juan, Puerto Rico</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/The_Flying_Wallendas\" title=\"The Flying Wallendas\">The Flying Wallendas</a> dies after falling off a tight-rope suspended between two hotels in <a href=\"https://wikipedia.org/wiki/San_Juan,_Puerto_Rico\" title=\"San Juan, Puerto Rico\">San Juan, Puerto Rico</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Flying Wallendas", "link": "https://wikipedia.org/wiki/The_Flying_Wallendas"}, {"title": "San Juan, Puerto Rico", "link": "https://wikipedia.org/wiki/San_Juan,_Puerto_Rico"}]}, {"year": "1982", "text": "NASA's Space Shuttle Columbia is launched from the Kennedy Space Center on its third mission, STS-3.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> on its third mission, <a href=\"https://wikipedia.org/wiki/STS-3\" title=\"STS-3\">STS-3</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> on its third mission, <a href=\"https://wikipedia.org/wiki/STS-3\" title=\"STS-3\">STS-3</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}, {"title": "STS-3", "link": "https://wikipedia.org/wiki/STS-3"}]}, {"year": "1988", "text": "The United States Congress votes to override President <PERSON>'s veto of the Civil Rights Restoration Act of 1987.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> votes to override President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s veto of the <a href=\"https://wikipedia.org/wiki/Civil_Rights_Restoration_Act_of_1987\" title=\"Civil Rights Restoration Act of 1987\">Civil Rights Restoration Act of 1987</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> votes to override President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s veto of the <a href=\"https://wikipedia.org/wiki/Civil_Rights_Restoration_Act_of_1987\" title=\"Civil Rights Restoration Act of 1987\">Civil Rights Restoration Act of 1987</a>.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Civil Rights Restoration Act of 1987", "link": "https://wikipedia.org/wiki/Civil_Rights_Restoration_Act_of_1987"}]}, {"year": "1992", "text": "USAir Flight 405 crashes shortly after takeoff from New York City's LaGuardia Airport, leading to a number of studies into the effect that ice has on aircraft.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/USAir_Flight_405\" title=\"USAir Flight 405\">USAir Flight 405</a> crashes shortly after takeoff from New York City's <a href=\"https://wikipedia.org/wiki/LaGuardia_Airport\" title=\"LaGuardia Airport\">LaGuardia Airport</a>, leading to a number of studies into the effect that ice has on aircraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USAir_Flight_405\" title=\"USAir Flight 405\">USAir Flight 405</a> crashes shortly after takeoff from New York City's <a href=\"https://wikipedia.org/wiki/LaGuardia_Airport\" title=\"LaGuardia Airport\">LaGuardia Airport</a>, leading to a number of studies into the effect that ice has on aircraft.", "links": [{"title": "USAir Flight 405", "link": "https://wikipedia.org/wiki/USAir_Flight_405"}, {"title": "LaGuardia Airport", "link": "https://wikipedia.org/wiki/LaGuardia_Airport"}]}, {"year": "1992", "text": "Fall of communism in Albania: The Democratic Party of Albania wins a decisive majority in the parliamentary election.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Fall_of_communism_in_Albania\" title=\"Fall of communism in Albania\">Fall of communism in Albania</a>: The <a href=\"https://wikipedia.org/wiki/Democratic_Party_of_Albania\" title=\"Democratic Party of Albania\">Democratic Party of Albania</a> wins a decisive majority in the <a href=\"https://wikipedia.org/wiki/1992_Albanian_parliamentary_election\" title=\"1992 Albanian parliamentary election\">parliamentary election</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fall_of_communism_in_Albania\" title=\"Fall of communism in Albania\">Fall of communism in Albania</a>: The <a href=\"https://wikipedia.org/wiki/Democratic_Party_of_Albania\" title=\"Democratic Party of Albania\">Democratic Party of Albania</a> wins a decisive majority in the <a href=\"https://wikipedia.org/wiki/1992_Albanian_parliamentary_election\" title=\"1992 Albanian parliamentary election\">parliamentary election</a>.", "links": [{"title": "Fall of communism in Albania", "link": "https://wikipedia.org/wiki/Fall_of_communism_in_Albania"}, {"title": "Democratic Party of Albania", "link": "https://wikipedia.org/wiki/Democratic_Party_of_Albania"}, {"title": "1992 Albanian parliamentary election", "link": "https://wikipedia.org/wiki/1992_Albanian_parliamentary_election"}]}, {"year": "1993", "text": "The Intel Corporation ships the first Pentium chips (80586), featuring a 60 MHz clock speed, 100+ MIPS, and a 64 bit data path.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Intel_Corporation\" class=\"mw-redirect\" title=\"Intel Corporation\">Intel Corporation</a> ships the first <a href=\"https://wikipedia.org/wiki/Pentium\" title=\"Pentium\">Pentium</a> chips (80586), featuring a 60 <a href=\"https://wikipedia.org/wiki/Hertz\" title=\"Hertz\">MHz</a> clock speed, 100+ <a href=\"https://wikipedia.org/wiki/Instructions_per_second\" title=\"Instructions per second\">MIPS</a>, and a 64 <a href=\"https://wikipedia.org/wiki/Bit\" title=\"Bit\">bit</a> data path.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Intel_Corporation\" class=\"mw-redirect\" title=\"Intel Corporation\">Intel Corporation</a> ships the first <a href=\"https://wikipedia.org/wiki/Pentium\" title=\"Pentium\">Pentium</a> chips (80586), featuring a 60 <a href=\"https://wikipedia.org/wiki/Hertz\" title=\"Hertz\">MHz</a> clock speed, 100+ <a href=\"https://wikipedia.org/wiki/Instructions_per_second\" title=\"Instructions per second\">MIPS</a>, and a 64 <a href=\"https://wikipedia.org/wiki/Bit\" title=\"Bit\">bit</a> data path.", "links": [{"title": "Intel Corporation", "link": "https://wikipedia.org/wiki/Intel_Corporation"}, {"title": "Pentium", "link": "https://wikipedia.org/wiki/Pentium"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hertz"}, {"title": "Instructions per second", "link": "https://wikipedia.org/wiki/Instructions_per_second"}, {"title": "Bit", "link": "https://wikipedia.org/wiki/Bit"}]}, {"year": "1995", "text": "Cosmonaut <PERSON><PERSON> returns to earth after setting a record of 438 days in space.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Cosmonaut\" class=\"mw-redirect\" title=\"Cosmonaut\">Cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> returns to earth after setting a record of 438 days in <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">space</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cosmonaut\" class=\"mw-redirect\" title=\"Cosmonaut\">Cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> returns to earth after setting a record of 438 days in <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">space</a>.", "links": [{"title": "Cosmonaut", "link": "https://wikipedia.org/wiki/Cosmonaut"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Outer space", "link": "https://wikipedia.org/wiki/Outer_space"}]}, {"year": "1996", "text": "NASA's Space Shuttle Atlantis is launched on its 16th mission, STS-76.", "html": "1996 - NASA's <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on its 16th mission, <a href=\"https://wikipedia.org/wiki/STS-76\" title=\"STS-76\">STS-76</a>.", "no_year_html": "NASA's <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on its 16th mission, <a href=\"https://wikipedia.org/wiki/STS-76\" title=\"STS-76\">STS-76</a>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-76", "link": "https://wikipedia.org/wiki/STS-76"}]}, {"year": "1997", "text": "<PERSON>, aged 14 years and nine months, becomes the youngest women's World Figure Skating Champion.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, aged 14 years and nine months, becomes the youngest women's <a href=\"https://wikipedia.org/wiki/World_Figure_Skating_Championships\" title=\"World Figure Skating Championships\">World Figure Skating Champion</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, aged 14 years and nine months, becomes the youngest women's <a href=\"https://wikipedia.org/wiki/World_Figure_Skating_Championships\" title=\"World Figure Skating Championships\">World Figure Skating Champion</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ski"}, {"title": "World Figure Skating Championships", "link": "https://wikipedia.org/wiki/World_Figure_Skating_Championships"}]}, {"year": "1997", "text": "Comet Hale-Bo<PERSON> reaches its closest approach to Earth at 1.315 AU.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp\" title=\"Comet Hale-Bopp\">Comet Hale-Bopp</a> reaches its closest approach to Earth at 1.315 AU.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp\" title=\"Comet Hale-Bopp\">Comet Hale-Bopp</a> reaches its closest approach to Earth at 1.315 AU.", "links": [{"title": "Comet Hale-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp"}]}, {"year": "2004", "text": "<PERSON>, co-founder and leader of the Palestinian Sunni Islamist group Hamas, two bodyguards, and nine civilian bystanders are killed in the Gaza Strip when hit by Israeli Air Force Hellfire missiles.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, co-founder and leader of the <a href=\"https://wikipedia.org/wiki/Palestinian_people\" class=\"mw-redirect\" title=\"Palestinian people\">Palestinian</a> <a href=\"https://wikipedia.org/wiki/Sunni\" class=\"mw-redirect\" title=\"Sunni\">Sunni</a> Islamist group <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a>, two bodyguards, and nine civilian bystanders are killed in the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> when hit by <a href=\"https://wikipedia.org/wiki/Israeli_Air_Force\" title=\"Israeli Air Force\">Israeli Air Force</a> <a href=\"https://wikipedia.org/wiki/AGM-114_Hellfire\" title=\"AGM-114 Hellfire\">Hellfire missiles</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, co-founder and leader of the <a href=\"https://wikipedia.org/wiki/Palestinian_people\" class=\"mw-redirect\" title=\"Palestinian people\">Palestinian</a> <a href=\"https://wikipedia.org/wiki/Sunni\" class=\"mw-redirect\" title=\"Sunni\">Sunni</a> Islamist group <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a>, two bodyguards, and nine civilian bystanders are killed in the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> when hit by <a href=\"https://wikipedia.org/wiki/Israeli_Air_Force\" title=\"Israeli Air Force\">Israeli Air Force</a> <a href=\"https://wikipedia.org/wiki/AGM-114_Hellfire\" title=\"AGM-114 Hellfire\">Hellfire missiles</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Palestinian people", "link": "https://wikipedia.org/wiki/Palestinian_people"}, {"title": "Sunni", "link": "https://wikipedia.org/wiki/Sunni"}, {"title": "Hamas", "link": "https://wikipedia.org/wiki/Hamas"}, {"title": "Gaza Strip", "link": "https://wikipedia.org/wiki/Gaza_Strip"}, {"title": "Israeli Air Force", "link": "https://wikipedia.org/wiki/Israeli_Air_Force"}, {"title": "AGM-114 Hellfire", "link": "https://wikipedia.org/wiki/AGM-114_Hellfire"}]}, {"year": "2006", "text": "Three Christian Peacemaker Team (CPT) hostages are freed by British forces in Baghdad after 118 days of captivity and the murder of their colleague from the U.S., <PERSON>.", "html": "2006 - Three <a href=\"https://wikipedia.org/wiki/Christian_Peacemaker_Team\" class=\"mw-redirect\" title=\"Christian Peacemaker Team\">Christian Peacemaker Team</a> (CPT) hostages are freed by <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">British</a> forces in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a> after 118 days of captivity and the murder of their colleague from the U.S., <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quaker)\" title=\"<PERSON> (Quaker)\"><PERSON></a>.", "no_year_html": "Three <a href=\"https://wikipedia.org/wiki/Christian_Peacemaker_Team\" class=\"mw-redirect\" title=\"Christian Peacemaker Team\">Christian Peacemaker Team</a> (CPT) hostages are freed by <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">British</a> forces in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a> after 118 days of captivity and the murder of their colleague from the U.S., <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quaker)\" title=\"<PERSON> (Quaker)\"><PERSON></a>.", "links": [{"title": "Christian Peacemaker Team", "link": "https://wikipedia.org/wiki/Christian_Peacemaker_Team"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Baghdad", "link": "https://wikipedia.org/wiki/Baghdad"}, {"title": "<PERSON> (Quaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quaker)"}]}, {"year": "2013", "text": "At least 37 people are killed and 200 are injured after a fire destroys a camp containing Burmese refugees near Ban Mae, Thailand.", "html": "2013 - At least 37 people are killed and 200 are injured after a <a href=\"https://wikipedia.org/wiki/2013_Thailand_refugee_camp_fire\" title=\"2013 Thailand refugee camp fire\">fire destroys</a> a camp containing <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burmese</a> refugees near <a href=\"https://wikipedia.org/wiki/Ban_Mae\" title=\"Ban Mae\">Ban <PERSON></a>, Thailand.", "no_year_html": "At least 37 people are killed and 200 are injured after a <a href=\"https://wikipedia.org/wiki/2013_Thailand_refugee_camp_fire\" title=\"2013 Thailand refugee camp fire\">fire destroys</a> a camp containing <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burmese</a> refugees near <a href=\"https://wikipedia.org/wiki/Ban_Mae\" title=\"Ban Mae\">Ban <PERSON></a>, Thailand.", "links": [{"title": "2013 Thailand refugee camp fire", "link": "https://wikipedia.org/wiki/2013_Thailand_refugee_camp_fire"}, {"title": "Burma", "link": "https://wikipedia.org/wiki/Burma"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ban_Mae"}]}, {"year": "2016", "text": "Three suicide bombers kill 32 people and injure 316 in the 2016 Brussels bombings at the airport and at the Maelbeek/Maalbeek metro station.", "html": "2016 - Three suicide bombers kill 32 people and injure 316 in the <a href=\"https://wikipedia.org/wiki/2016_Brussels_bombings\" title=\"2016 Brussels bombings\">2016 Brussels bombings</a> at the <a href=\"https://wikipedia.org/wiki/Brussels_Airport\" title=\"Brussels Airport\">airport</a> and at the <a href=\"https://wikipedia.org/wiki/Maelbeek/Maalbeek_metro_station\" class=\"mw-redirect\" title=\"Maelbeek/Maalbeek metro station\">Maelbeek/Maalbeek metro station</a>.", "no_year_html": "Three suicide bombers kill 32 people and injure 316 in the <a href=\"https://wikipedia.org/wiki/2016_Brussels_bombings\" title=\"2016 Brussels bombings\">2016 Brussels bombings</a> at the <a href=\"https://wikipedia.org/wiki/Brussels_Airport\" title=\"Brussels Airport\">airport</a> and at the <a href=\"https://wikipedia.org/wiki/Maelbeek/Maalbeek_metro_station\" class=\"mw-redirect\" title=\"Maelbeek/Maalbeek metro station\">Maelbeek/Maalbeek metro station</a>.", "links": [{"title": "2016 Brussels bombings", "link": "https://wikipedia.org/wiki/2016_Brussels_bombings"}, {"title": "Brussels Airport", "link": "https://wikipedia.org/wiki/Brussels_Airport"}, {"title": "Maelbeek/Maalbeek metro station", "link": "https://wikipedia.org/wiki/Maelbeek/Maalbeek_metro_station"}]}, {"year": "2017", "text": "A terrorist attack in London near the Houses of Parliament leaves four people dead and at least 20 injured.", "html": "2017 - A <a href=\"https://wikipedia.org/wiki/2017_Westminster_attack\" title=\"2017 Westminster attack\">terrorist attack</a> in London near the <a href=\"https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom\" title=\"Parliament of the United Kingdom\">Houses of Parliament</a> leaves four people dead and at least 20 injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2017_Westminster_attack\" title=\"2017 Westminster attack\">terrorist attack</a> in London near the <a href=\"https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom\" title=\"Parliament of the United Kingdom\">Houses of Parliament</a> leaves four people dead and at least 20 injured.", "links": [{"title": "2017 Westminster attack", "link": "https://wikipedia.org/wiki/2017_Westminster_attack"}, {"title": "Parliament of the United Kingdom", "link": "https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom"}]}, {"year": "2017", "text": "Syrian civil war: Five hundred members of the Syrian Democratic Forces (SDF) are airlifted south of the Euphrates by United States Air Force helicopters, beginning the Battle of Tabqa.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: Five hundred members of the <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) are airlifted south of the <a href=\"https://wikipedia.org/wiki/Euphrates\" title=\"Euphrates\">Euphrates</a> by <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> helicopters, beginning the <a href=\"https://wikipedia.org/wiki/Battle_of_Tabqa\" title=\"Battle of Tabqa\">Battle of Tabqa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: Five hundred members of the <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) are airlifted south of the <a href=\"https://wikipedia.org/wiki/Euphrates\" title=\"Euphrates\">Euphrates</a> by <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> helicopters, beginning the <a href=\"https://wikipedia.org/wiki/Battle_of_Tabqa\" title=\"Battle of Tabqa\">Battle of Tabqa</a>.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Syrian Democratic Forces", "link": "https://wikipedia.org/wiki/Syrian_Democratic_Forces"}, {"title": "Euphrates", "link": "https://wikipedia.org/wiki/Euphrates"}, {"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Battle of Tabqa", "link": "https://wikipedia.org/wiki/Battle_of_Tabqa"}]}, {"year": "2019", "text": "The Special Counsel investigation on the 2016 United States presidential election concludes when <PERSON> submits his report to the United States Attorney General.", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_special_counsel_investigation\" title=\"<PERSON><PERSON> special counsel investigation\">Special Counsel investigation</a> on the <a href=\"https://wikipedia.org/wiki/2016_United_States_presidential_election\" title=\"2016 United States presidential election\">2016 United States presidential election</a> concludes when <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> submits <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_report\" title=\"<PERSON><PERSON> report\">his report</a> to the United States Attorney General.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_special_counsel_investigation\" title=\"<PERSON><PERSON> special counsel investigation\">Special Counsel investigation</a> on the <a href=\"https://wikipedia.org/wiki/2016_United_States_presidential_election\" title=\"2016 United States presidential election\">2016 United States presidential election</a> concludes when <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> submits <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_report\" title=\"<PERSON><PERSON> report\">his report</a> to the United States Attorney General.", "links": [{"title": "<PERSON><PERSON> special counsel investigation", "link": "https://wikipedia.org/wiki/Mu<PERSON>_special_counsel_investigation"}, {"title": "2016 United States presidential election", "link": "https://wikipedia.org/wiki/2016_United_States_presidential_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON> report", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_report"}]}, {"year": "2019", "text": "Two buses crashed in Kitampo, a town north of Ghana's capital Accra, killing at least 50 people.", "html": "2019 - Two buses crashed in Kitampo, a town north of <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a>'s capital <a href=\"https://wikipedia.org/wiki/Accra\" title=\"Accra\">Accra</a>, killing at least 50 people.", "no_year_html": "Two buses crashed in Kitampo, a town north of <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a>'s capital <a href=\"https://wikipedia.org/wiki/Accra\" title=\"Accra\">Accra</a>, killing at least 50 people.", "links": [{"title": "Ghana", "link": "https://wikipedia.org/wiki/Ghana"}, {"title": "Accra", "link": "https://wikipedia.org/wiki/Accra"}]}, {"year": "2020", "text": "Indian Prime Minister <PERSON><PERSON><PERSON> announces the country's largest ever self-imposed curfew, in an effort to fight the spread of COVID-19.", "html": "2020 - Indian Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> announces the country's largest ever self-imposed curfew, in an effort to fight the spread of <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_India\" title=\"COVID-19 pandemic in India\">COVID-19</a>.", "no_year_html": "Indian Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> announces the country's largest ever self-imposed curfew, in an effort to fight the spread of <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_India\" title=\"COVID-19 pandemic in India\">COVID-19</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "COVID-19 pandemic in India", "link": "https://wikipedia.org/wiki/COVID-19_pandemic_in_India"}]}, {"year": "2020", "text": "Greek Prime Minister <PERSON><PERSON><PERSON><PERSON> announces a national lockdown and the country's first ever self-imposed curfew, in an effort to fight the spread of COVID-19.", "html": "2020 - Greek Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>yria<PERSON>_Mi<PERSON>kis\" title=\"<PERSON>yria<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> announces a national lockdown and the country's first ever self-imposed curfew, in an effort to fight the spread of <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_Greece\" title=\"COVID-19 pandemic in Greece\">COVID-19</a>.", "no_year_html": "Greek Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>yria<PERSON>_<PERSON>\" title=\"<PERSON>yr<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> announces a national lockdown and the country's first ever self-imposed curfew, in an effort to fight the spread of <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_Greece\" title=\"COVID-19 pandemic in Greece\">COVID-19</a>.", "links": [{"title": "Kyriakos <PERSON>", "link": "https://wikipedia.org/wiki/Kyriakos_Mitsotakis"}, {"title": "COVID-19 pandemic in Greece", "link": "https://wikipedia.org/wiki/COVID-19_pandemic_in_Greece"}]}, {"year": "2021", "text": "Ten people are killed in a mass shooting in Boulder, Colorado.", "html": "2021 - Ten people are killed in a <a href=\"https://wikipedia.org/wiki/2021_Boulder_shooting\" title=\"2021 Boulder shooting\">mass shooting</a> in <a href=\"https://wikipedia.org/wiki/Boulder,_Colorado\" title=\"Boulder, Colorado\">Boulder, Colorado</a>.", "no_year_html": "Ten people are killed in a <a href=\"https://wikipedia.org/wiki/2021_Boulder_shooting\" title=\"2021 Boulder shooting\">mass shooting</a> in <a href=\"https://wikipedia.org/wiki/Boulder,_Colorado\" title=\"Boulder, Colorado\">Boulder, Colorado</a>.", "links": [{"title": "2021 Boulder shooting", "link": "https://wikipedia.org/wiki/2021_Boulder_shooting"}, {"title": "Boulder, Colorado", "link": "https://wikipedia.org/wiki/Boulder,_Colorado"}]}, {"year": "2024", "text": "At least 145 people are killed and 551 injured in a bombing and mass shooting at the Crocus City Hall in Krasnogorsk, Russia.", "html": "2024 - At least 145 people are killed and 551 injured in a <a href=\"https://wikipedia.org/wiki/Crocus_City_Hall_attack\" title=\"Crocus City Hall attack\">bombing and mass shooting</a> at the <a href=\"https://wikipedia.org/wiki/Crocus_City_Hall\" title=\"Crocus City Hall\">Crocus City Hall</a> in <a href=\"https://wikipedia.org/wiki/Krasnogorsk,_Moscow_Oblast\" title=\"Krasnogorsk, Moscow Oblast\">Krasnogorsk</a>, Russia.", "no_year_html": "At least 145 people are killed and 551 injured in a <a href=\"https://wikipedia.org/wiki/Crocus_City_Hall_attack\" title=\"Crocus City Hall attack\">bombing and mass shooting</a> at the <a href=\"https://wikipedia.org/wiki/Crocus_City_Hall\" title=\"Crocus City Hall\">Crocus City Hall</a> in <a href=\"https://wikipedia.org/wiki/Krasnogorsk,_Moscow_Oblast\" title=\"Krasnogorsk, Moscow Oblast\">Krasnogorsk</a>, Russia.", "links": [{"title": "Crocus City Hall attack", "link": "https://wikipedia.org/wiki/Crocus_City_Hall_attack"}, {"title": "Crocus City Hall", "link": "https://wikipedia.org/wiki/Crocus_City_Hall"}, {"title": "Krasnogorsk, Moscow Oblast", "link": "https://wikipedia.org/wiki/Krasnogorsk,_Moscow_Oblast"}]}], "Births": [{"year": "841", "text": "<PERSON>, Frankish son of <PERSON> of Septimania (d. 885)", "html": "841 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Frankish son of <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Septimania\" title=\"<PERSON> of Septimania\"><PERSON> of Septimania</a> (d. 885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Frankish son of <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Septimania\" title=\"<PERSON> of Septimania\"><PERSON> of Septimania</a> (d. 885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernard_Plantapilosa"}, {"title": "<PERSON> of Septimania", "link": "https://wikipedia.org/wiki/<PERSON>_of_Septimania"}]}, {"year": "875", "text": "<PERSON>, Duke of Aquitaine (d. 918)", "html": "875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON>, Duke of Aquitaine</a> (d. 918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON>, Duke of Aquitaine</a> (d. 918)", "links": [{"title": "<PERSON>, Duke of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine"}]}, {"year": "1212", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (d. 1235)", "html": "1212 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Ho<PERSON>\" title=\"Emperor <PERSON><PERSON>Ho<PERSON>wa\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1235)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Ho<PERSON>wa\" title=\"Emperor <PERSON><PERSON>Ho<PERSON>wa\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1235)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>"}]}, {"year": "1367", "text": "<PERSON>, 1st Duke of Norfolk, English politician, Earl Marshal of the United Kingdom (probable; d. 1399)", "html": "1367 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Norfolk\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Norfolk\"><PERSON>, 1st Duke of Norfolk</a>, English politician, <a href=\"https://wikipedia.org/wiki/Earl_Marshal\" title=\"Earl Marshal\">Earl Marshal of the United Kingdom</a> (probable; d. 1399)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Norfolk\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Norfolk\"><PERSON>, 1st Duke of Norfolk</a>, English politician, <a href=\"https://wikipedia.org/wiki/Earl_Marshal\" title=\"Earl Marshal\">Earl Marshal of the United Kingdom</a> (probable; d. 1399)", "links": [{"title": "<PERSON>, 1st Duke of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Norfolk"}, {"title": "Earl Marshal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1394", "text": "<PERSON><PERSON><PERSON>, Persian astronomer and mathematician (d. 1449)", "html": "1394 - <a href=\"https://wikipedia.org/wiki/Ulugh_Beg\" title=\"Ulugh Beg\"><PERSON><PERSON><PERSON></a>, Persian astronomer and mathematician (d. 1449)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ulugh_Beg\" title=\"Ulugh Beg\"><PERSON><PERSON><PERSON></a>, Persian astronomer and mathematician (d. 1449)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ulug<PERSON>_Beg"}]}, {"year": "1459", "text": "<PERSON>, Holy Roman Emperor (d. 1519)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> I, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1519)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1499", "text": "<PERSON>, German astrologer and chronicler (d. 1537)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astrologer and chronicler (d. 1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astrologer and chronicler (d. 1537)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1503", "text": "<PERSON>, Italian author and educator (d. 1583)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and educator (d. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and educator (d. 1583)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1517", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer (d. 1590)", "html": "1517 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (d. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (d. 1590)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>ef<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON>, Duchess of Suffolk, English noblewoman (d. 1580)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Suffolk\" class=\"mw-redirect\" title=\"<PERSON>, Duchess of Suffolk\"><PERSON>, Duchess of Suffolk</a>, English noblewoman (d. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Suffolk\" class=\"mw-redirect\" title=\"<PERSON>, Duchess of Suffolk\"><PERSON>, Duchess of Suffolk</a>, English noblewoman (d. 1580)", "links": [{"title": "<PERSON>, Duchess of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Suffolk"}]}, {"year": "1582", "text": "<PERSON>, Archbishop of York (d. 1650)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_York)\" title=\"<PERSON> (archbishop of York)\"><PERSON></a>, Archbishop of York (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_York)\" title=\"<PERSON> (archbishop of York)\"><PERSON></a>, Archbishop of York (d. 1650)", "links": [{"title": "<PERSON> (archbishop of York)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_York)"}]}, {"year": "1599", "text": "<PERSON>, Flemish-English painter and etcher (d. 1641)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish-English painter and etcher (d. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish-English painter and etcher (d. 1641)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1609", "text": "<PERSON>, Polish king (d. 1672)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II <PERSON>\"><PERSON></a>, Polish king (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish king (d. 1672)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1615", "text": "<PERSON>, Viscountess <PERSON>, British scientist (d. 1691)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscountess_<PERSON><PERSON>\" title=\"<PERSON>, Viscountess Ra<PERSON>\"><PERSON>, Viscountess <PERSON></a>, British scientist (d. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscountess_<PERSON>\" title=\"<PERSON>, Viscountess <PERSON>\"><PERSON>, Viscountess <PERSON></a>, British scientist (d. 1691)", "links": [{"title": "<PERSON>, Viscountess <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscountess_<PERSON>"}]}, {"year": "1663", "text": "<PERSON> <PERSON>, German clergyman, philanthropist, and scholar (d. 1727)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German clergyman, philanthropist, and scholar (d. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German clergyman, philanthropist, and scholar (d. 1727)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>"}]}, {"year": "1684", "text": "<PERSON>, 1st Earl of Bath, English politician, Secretary at War (d. 1764)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Bath\" title=\"<PERSON>, 1st Earl of Bath\"><PERSON>, 1st Earl of Bath</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_at_War\" title=\"Secretary at War\">Secretary at War</a> (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Bath\" title=\"<PERSON>, 1st Earl <PERSON> Bath\"><PERSON>, 1st Earl of Bath</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_at_War\" title=\"Secretary at War\">Secretary at War</a> (d. 1764)", "links": [{"title": "<PERSON>, 1st Earl <PERSON> Bath", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Bath"}, {"title": "Secretary at War", "link": "https://wikipedia.org/wiki/Secretary_at_War"}]}, {"year": "1712", "text": "<PERSON>, English poet and playwright (d. 1757)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)\" title=\"<PERSON> (dramatist)\"><PERSON></a>, English poet and playwright (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)\" title=\"<PERSON> (dramatist)\"><PERSON></a>, English poet and playwright (d. 1757)", "links": [{"title": "<PERSON> (dramatist)", "link": "https://wikipedia.org/wiki/<PERSON>_(dramatist)"}]}, {"year": "1720", "text": "<PERSON><PERSON><PERSON>, French architect, designed the Yellow Palace and Bernstorff Palace (d. 1799)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French architect, designed the <a href=\"https://wikipedia.org/wiki/Yellow_Palace,_Copenhagen\" title=\"Yellow Palace, Copenhagen\">Yellow Palace</a> and <a href=\"https://wikipedia.org/wiki/Bernstorff_Palace\" title=\"Bernstorff Palace\">Bernstorff Palace</a> (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French architect, designed the <a href=\"https://wikipedia.org/wiki/Yellow_Palace,_Copenhagen\" title=\"Yellow Palace, Copenhagen\">Yellow Palace</a> and <a href=\"https://wikipedia.org/wiki/Bernstorff_Palace\" title=\"Bernstorff Palace\">Bernstorff Palace</a> (d. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Yellow Palace, Copenhagen", "link": "https://wikipedia.org/wiki/Yellow_Palace,_Copenhagen"}, {"title": "Bernstorff Palace", "link": "https://wikipedia.org/wiki/Bernstorff_Palace"}]}, {"year": "1723", "text": "<PERSON>, American lawyer and politician (d. 1783)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>(barrister)\" title=\"<PERSON> (barrister)\"><PERSON></a>, American lawyer and politician (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(barrister)\" title=\"<PERSON> (barrister)\"><PERSON></a>, American lawyer and politician (d. 1783)", "links": [{"title": "<PERSON> (barrister)", "link": "https://wikipedia.org/wiki/<PERSON>(barrister)"}]}, {"year": "1728", "text": "<PERSON>, German painter and theorist (d. 1779)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and theorist (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and theorist (d. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, English scientist (d. 1873)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adam_<PERSON>wick"}]}, {"year": "1797", "text": "<PERSON>, German Emperor (d. 1888)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor\" title=\"<PERSON>, German Emperor\"><PERSON>, German Emperor</a> (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor\" title=\"<PERSON>, German Emperor\"><PERSON>, German Emperor</a> (d. 1888)", "links": [{"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}]}, {"year": "1808", "text": "<PERSON>, English feminist, social reformer, and author (d. 1877)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English feminist, social reformer, and author (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English feminist, social reformer, and author (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, American physician and lawyer (d. 1873)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physician and lawyer (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physician and lawyer (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, American author and activist (d. 1886)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, American sculptor, designed the Statue of Freedom (d. 1857)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, American sculptor, designed the <a href=\"https://wikipedia.org/wiki/Statue_of_Freedom\" title=\"Statue of Freedom\">Statue of Freedom</a> (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, American sculptor, designed the <a href=\"https://wikipedia.org/wiki/Statue_of_Freedom\" title=\"Statue of Freedom\">Statue of Freedom</a> (d. 1857)", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)"}, {"title": "Statue of Freedom", "link": "https://wikipedia.org/wiki/Statue_of_Freedom"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON>, American general (d. 1876)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Braxton Bragg\"><PERSON><PERSON><PERSON></a>, American general (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Brax<PERSON> Bragg\"><PERSON><PERSON><PERSON></a>, American general (d. 1876)", "links": [{"title": "Braxton Bragg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, English-Australian explorer, founded Penwortham (d. 1846)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian explorer, founded <a href=\"https://wikipedia.org/wiki/Penwortham,_South_Australia\" title=\"Penwortham, South Australia\">Penwortham</a> (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ho<PERSON>\"><PERSON></a>, English-Australian explorer, founded <a href=\"https://wikipedia.org/wiki/Penwortham,_South_Australia\" title=\"Penwortham, South Australia\">Penwortham</a> (d. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Penwortham, South Australia", "link": "https://wikipedia.org/wiki/Penwortham,_South_Australia"}]}, {"year": "1822", "text": "<PERSON>, Ottoman sociologist, historian, scholar, statesman and jurist (d. 1895)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman sociologist, historian, scholar, statesman and jurist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman sociologist, historian, scholar, statesman and jurist (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek scientist (d. 1906)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek scientist (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek scientist (d. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ssi<PERSON>_<PERSON>omanos"}]}, {"year": "1842", "text": "<PERSON><PERSON><PERSON>, Ukrainian pianist, composer, and conductor (d. 1912)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian pianist, composer, and conductor (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian pianist, composer, and conductor (d. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, English illustrator and painter (d. 1886)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator and painter (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator and painter (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, American lieutenant, police officer, and farmer (d. 1891)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, police officer, and farmer (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, police officer, and farmer (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON>, Czech violinist and educator (d. 1934)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0ev%C4%8D%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech violinist and educator (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0ev%C4%8D%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech violinist and educator (d. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Otakar_%C5%A0ev%C4%8D%C3%ADk"}]}, {"year": "1852", "text": "<PERSON>, French cardinal (d. 1916)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/Hector_S%C3%A9vin\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hector_S%C3%A9vin\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hector_S%C3%A9vin"}]}, {"year": "1855", "text": "<PERSON>, British painter (d. 1926)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, French mathematician, journalist, and politician, 14th President of France (d. 1932)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, journalist, and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, journalist, and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1866", "text": "<PERSON>, American baseball player and umpire (d. 1913)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, American colonel and physicist, Nobel Prize laureate (d. 1953)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1869", "text": "<PERSON>, Filipino general and politician, 1st President of the Philippines (d. 1964)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1869", "text": "<PERSON>, Scottish-English footballer (d. 1939)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1869)\" title=\"<PERSON> (footballer, born 1869)\"><PERSON></a>, Scottish-English footballer (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1869)\" title=\"<PERSON> (footballer, born 1869)\"><PERSON></a>, Scottish-English footballer (d. 1939)", "links": [{"title": "<PERSON> (footballer, born 1869)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1869)"}]}, {"year": "1873", "text": "<PERSON>, Canadian-American painter (d. 1939)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Canadian-American football player and coach (d. 1960)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American football player and coach (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American football player and coach (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American journalist and politician (d. 1951)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Italian actress (d. 1959)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (d. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Polish-Lithuanian rabbi and educator (d. 1969)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Lithuanian rabbi and educator (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Lithuanian rabbi and educator (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Estonian lawyer and politician, Head of State of Estonia (d. 1963)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/August_Re<PERSON>\" title=\"August <PERSON><PERSON>\">August <PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Head_of_State_of_Estonia\" class=\"mw-redirect\" title=\"Head of State of Estonia\">Head of State of Estonia</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Re<PERSON>\" title=\"August Re<PERSON>\">August <PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Head_of_State_of_Estonia\" class=\"mw-redirect\" title=\"Head of State of Estonia\">Head of State of Estonia</a> (d. 1963)", "links": [{"title": "August Rei", "link": "https://wikipedia.org/wiki/August_Rei"}, {"title": "Head of State of Estonia", "link": "https://wikipedia.org/wiki/Head_of_State_of_Estonia"}]}, {"year": "1887", "text": "<PERSON>, American actor (d. 1961)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American race car driver (d. 1978)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 1978)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1892", "text": "<PERSON>, American country banjo player (d. 1931)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country banjo player (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country banjo player (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Estonian poet and scholar (d. 1970)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet and scholar (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet and scholar (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Chinese general and politician, 1st Vice Premier of the People's Republic of China (d. 1969)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"He <PERSON>\">He <PERSON></a>, Chinese general and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\">He <PERSON></a>, Chinese general and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice Premier of the People's Republic of China", "link": "https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China"}]}, {"year": "1896", "text": "<PERSON>, Austrian-American actor (d. 1964)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American actor (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American actor (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American ballerina and choreographer (d. 1991)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ballerina)\" title=\"<PERSON> (ballerina)\"><PERSON></a>, American ballerina and choreographer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ballerina)\" title=\"<PERSON> (ballerina)\"><PERSON></a>, American ballerina and choreographer (d. 1991)", "links": [{"title": "<PERSON> (ballerina)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ballerina)"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Austrian-American painter (d. 1991)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American painter (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American painter (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Dutch architect, designed the Van Nelle Factory (d. 1949)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch architect, designed the <a href=\"https://wikipedia.org/wiki/Van_Nelle_Factory\" title=\"Van Nelle Factory\">Van Nelle Factory</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch architect, designed the <a href=\"https://wikipedia.org/wiki/Van_Nelle_Factory\" title=\"Van Nelle Factory\">Van Nelle Factory</a> (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Van Nelle Factory", "link": "https://wikipedia.org/wiki/Van_Nelle_Factory"}]}, {"year": "1902", "text": "<PERSON>, French actress and composer (d. 2008)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and composer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and composer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American cartoonist (d. 1987)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (d. 1987)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)"}]}, {"year": "1907", "text": "<PERSON>, American general and diplomat, United States Ambassador to France (d. 1990)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to France", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_France"}]}, {"year": "1908", "text": "<PERSON>, Australian tennis player (d. 1991)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player (d. 1991)", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1908", "text": "<PERSON>, American novelist and short story writer (d. 1988)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Louis_L%27Amour\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_L%27Amour\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_L%27Amour"}]}, {"year": "1909", "text": "<PERSON>, Canadian author and educator (d. 1983)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and educator (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and educator (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English sailor and author (d. 1979)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and author (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and author (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish actor and performer (d. 1985)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, Irish actor and performer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, Irish actor and performer (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English race car driver (d. 1959)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 1959)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1912", "text": "<PERSON>, American actor (d. 2009)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Canadian-American painter and educator (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter and educator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter and educator (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American journalist and politician, 30th Governor of Oregon (d. 1983)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American businessman and talent agent (d. 2002)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and talent agent (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and talent agent (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actor (d. 1971)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American author and illustrator (d. 1993)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American author and illustrator (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American author and illustrator (d. 1993)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cartoonist)"}]}, {"year": "1914", "text": "<PERSON>, <PERSON>, English businessman (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English businessman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, <PERSON></a>, English businessman (d. 2008)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actress (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Virginia_Grey\" title=\"Virginia Grey\"><PERSON></a>, American actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Grey\" title=\"Virginia Grey\"><PERSON></a>, American actress (d. 2004)", "links": [{"title": "Virginia Grey", "link": "https://wikipedia.org/wiki/Virginia_Grey"}]}, {"year": "1917", "text": "<PERSON>, Canadian-American mathematician and academic (d. 2006)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American mathematician and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American mathematician and academic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English actor (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2013)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Guyanese politician, 4th President of Guyana (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guyanese politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guyanese politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a> (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "President of Guyana", "link": "https://wikipedia.org/wiki/President_of_Guyana"}]}, {"year": "1919", "text": "<PERSON>, American illustrator (d. 1990)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor and singer (d. 1992)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (d. 1992)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1920", "text": "<PERSON>, German-American actor (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Canadian businessman and politician, 23rd Lieutenant Governor of Prince Edward Island (d. 1995)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 23rd <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Prince_Edward_Island\" title=\"Lieutenant Governor of Prince Edward Island\">Lieutenant Governor of Prince Edward Island</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 23rd <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Prince_Edward_Island\" title=\"Lieutenant Governor of Prince Edward Island\">Lieutenant Governor of Prince Edward Island</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Prince Edward Island", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Prince_Edward_Island"}]}, {"year": "1920", "text": "<PERSON>, American actor (d. 1981)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Japanese geochemist (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese geochemist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese geochemist (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English pianist and educator, founded the Leeds International Pianoforte Competition (d. 2020)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and educator, founded the <a href=\"https://wikipedia.org/wiki/Leeds_International_Pianoforte_Competition\" class=\"mw-redirect\" title=\"Leeds International Pianoforte Competition\">Leeds International Pianoforte Competition</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Waterman\"><PERSON></a>, English pianist and educator, founded the <a href=\"https://wikipedia.org/wiki/Leeds_International_Pianoforte_Competition\" class=\"mw-redirect\" title=\"Leeds International Pianoforte Competition\">Leeds International Pianoforte Competition</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Leeds International Pianoforte Competition", "link": "https://wikipedia.org/wiki/Leeds_International_Pianoforte_Competition"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Italian actor, director, and screenwriter (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor, director, and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor, director, and screenwriter (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1922", "text": "<PERSON>, American politician, 62nd Governor of Ohio (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Ohio", "link": "https://wikipedia.org/wiki/Governor_of_Ohio"}]}, {"year": "1922", "text": "<PERSON>, American screenwriter (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French mime and actor (d. 2007)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Mime\" class=\"mw-redirect\" title=\"Mime\">mime</a> and actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Mime\" class=\"mw-redirect\" title=\"Mime\">mime</a> and actor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mime"}]}, {"year": "1924", "text": "<PERSON>, American journalist and author, founded USA Today (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author, founded <i><a href=\"https://wikipedia.org/wiki/USA_Today\" title=\"USA Today\">USA Today</a></i> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author, founded <i><a href=\"https://wikipedia.org/wiki/USA_Today\" title=\"USA Today\">USA Today</a></i> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_<PERSON>eu<PERSON>h"}, {"title": "USA Today", "link": "https://wikipedia.org/wiki/USA_Today"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Russian test pilot, participant in the launch of the first artificial Earth satellite (d. 1960)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian test pilot, participant in the <a href=\"https://wikipedia.org/wiki/Rocket_launch\" class=\"mw-redirect\" title=\"Rocket launch\">launch</a> of the first artificial <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> <a href=\"https://wikipedia.org/wiki/Sputnik_1\" title=\"Sputnik 1\">satellite</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian test pilot, participant in the <a href=\"https://wikipedia.org/wiki/Rocket_launch\" class=\"mw-redirect\" title=\"Rocket launch\">launch</a> of the first artificial <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> <a href=\"https://wikipedia.org/wiki/Sputnik_1\" title=\"Sputnik 1\">satellite</a> (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Rocket launch", "link": "https://wikipedia.org/wiki/Rocket_launch"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}, {"title": "Sputnik 1", "link": "https://wikipedia.org/wiki/Sputnik_1"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Turkish director, producer, and screenwriter (d. 1998)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>._<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish director, producer, and screenwriter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sman_<PERSON>_<PERSON>\" title=\"<PERSON>sman <PERSON>\"><PERSON><PERSON></a>, Turkish director, producer, and screenwriter (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American television announcer (d. 1999)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television announcer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television announcer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American basketball player and manager (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Russian photographer (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian photographer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian photographer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American journalist (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author, critic, and academic", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author, critic, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author, critic, and academic", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American basketball player, coach, and priest (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and priest (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and priest (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Japanese artist", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese artist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Malaysian actor, director, singer, songwriter, composer, and producer (d. 1973)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian actor, director, singer, songwriter, composer, and producer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian actor, director, singer, songwriter, composer, and producer (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American lawyer and academic", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American minister and broadcaster, founded the Christian Broadcasting Network (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and broadcaster, founded the <a href=\"https://wikipedia.org/wiki/Christian_Broadcasting_Network\" title=\"Christian Broadcasting Network\">Christian Broadcasting Network</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and broadcaster, founded the <a href=\"https://wikipedia.org/wiki/Christian_Broadcasting_Network\" title=\"Christian Broadcasting Network\">Christian Broadcasting Network</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Christian Broadcasting Network", "link": "https://wikipedia.org/wiki/Christian_Broadcasting_Network"}]}, {"year": "1930", "text": "<PERSON>, American composer and songwriter (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1931", "text": "<PERSON>, Canadian actor", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Welsh journalist and author (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh journalist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh journalist and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Dutch physician and politician, Deputy Prime Minister of the Netherlands (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands\" title=\"Deputy Prime Minister of the Netherlands\">Deputy Prime Minister of the Netherlands</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands\" title=\"Deputy Prime Minister of the Netherlands\">Deputy Prime Minister of the Netherlands</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rst"}, {"title": "Deputy Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands"}]}, {"year": "1932", "text": "<PERSON>, American chess player and journalist (d. 2010)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_grandmaster)\" class=\"mw-redirect\" title=\"<PERSON> (chess grandmaster)\"><PERSON></a>, American chess player and journalist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_grandmaster)\" class=\"mw-redirect\" title=\"<PERSON> (chess grandmaster)\"><PERSON></a>, American chess player and journalist (d. 2010)", "links": [{"title": "<PERSON> (chess grandmaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_grandmaster)"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian economist and politician, 1st President of Iran (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian economist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Iran\" title=\"President of Iran\">President of Iran</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian economist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Iran\" title=\"President of Iran\">President of Iran</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Iran", "link": "https://wikipedia.org/wiki/President_of_Iran"}]}, {"year": "1934", "text": "<PERSON>, Swedish actress", "html": "1934 - <a href=\"https://wikipedia.org/wiki/May_Britt\" title=\"May Britt\"><PERSON></a>, Swedish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_Britt\" title=\"May Britt\">May <PERSON></a>, Swedish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/May_<PERSON><PERSON>t"}]}, {"year": "1934", "text": "<PERSON>, English lawyer and judge", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" class=\"mw-redirect\" title=\"<PERSON> (lawyer)\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" class=\"mw-redirect\" title=\"<PERSON> (lawyer)\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON> (lawyer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(lawyer)"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American lawyer and politician (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Russian-born Soviet test pilot and aerobatics champion (d. 2004)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_Korchuganova\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>rch<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-born Soviet test pilot and <a href=\"https://wikipedia.org/wiki/Aerobatics\" title=\"Aerobatics\">aerobatics</a> champion (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Korchuganova\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-born Soviet test pilot and <a href=\"https://wikipedia.org/wiki/Aerobatics\" title=\"Aerobatics\">aerobatics</a> champion (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_Korch<PERSON>nova"}, {"title": "Aerobatics", "link": "https://wikipedia.org/wiki/Aerobatics"}]}, {"year": "1935", "text": "<PERSON>, Italian tennis player and journalist (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>li\" title=\"<PERSON> Pericoli\"><PERSON></a>, Italian tennis player and journalist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pericoli\"><PERSON></a>, Italian tennis player and journalist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lea_Pericoli"}]}, {"year": "1935", "text": "<PERSON>, American baseball player and umpire (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American actor (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Turkish singer-songwriter, pop music composer, and actor (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Erol_B%C3%BCy%C3%BCkbur%C3%A7\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter, pop music composer, and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erol_B%C3%BCy%C3%BCkbur%C3%A7\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter, pop music composer, and actor (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erol_B%C3%BCy%C3%BCkbur%C3%A7"}]}, {"year": "1936", "text": "<PERSON>, American trade union leader (d. 2008)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(labor_leader)\" title=\"<PERSON> (labor leader)\"><PERSON></a>, American trade union leader (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(labor_leader)\" title=\"<PERSON> (labor leader)\"><PERSON></a>, American trade union leader (d. 2008)", "links": [{"title": "<PERSON> (labor leader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(labor_leader)"}]}, {"year": "1936", "text": "<PERSON>, Kenyan-English singer-songwriter and guitarist (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English singer-songwriter and guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English singer-songwriter and guitarist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American pianist and composer (d. 2022)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Angelo_Badalamenti"}]}, {"year": "1937", "text": "<PERSON><PERSON>, German sprinter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American trumpet player and composer (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, British drag queen (d. 2003)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Foo_Foo_Lammar\" title=\"Foo Foo Lammar\"><PERSON>oo <PERSON><PERSON></a>, British drag queen (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Foo_Foo_Lammar\" title=\"Foo Foo Lammar\"><PERSON><PERSON> <PERSON><PERSON></a>, British drag queen (d. 2003)", "links": [{"title": "<PERSON>oo <PERSON>oo <PERSON>", "link": "https://wikipedia.org/wiki/Foo_Foo_Lammar"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Estonian chess player (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>in_<PERSON><PERSON>uk"}]}, {"year": "1940", "text": "<PERSON>, Jr., American physicist and inventor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American physicist and inventor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American physicist and inventor", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1940", "text": "<PERSON>, Canadian ice hockey player", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON> <PERSON>, Cambodian-American physician and author (d. 1996)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Haing_<PERSON>_<PERSON>\" title=\"Haing <PERSON> Ng<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Cambodian-American physician and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haing_<PERSON><PERSON>_<PERSON>\" title=\"Haing S. Ngor\"><PERSON><PERSON> <PERSON><PERSON></a>, Cambodian-American physician and author (d. 1996)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American poet", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Swiss actor (d. 2019)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bruno_<PERSON>z"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Mauritian politician, 2nd President of Mauritius", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mauritian politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Mauritius\" class=\"mw-redirect\" title=\"List of Presidents of Mauritius\">President of Mauritius</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mauritian politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Mauritius\" class=\"mw-redirect\" title=\"List of Presidents of Mauritius\">President of Mauritius</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>m"}, {"title": "List of Presidents of Mauritius", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Mauritius"}]}, {"year": "1942", "text": "<PERSON>, Brazilian singer-songwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian lawyer and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Iranian footballer and manager (d. 2013)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Nazem_<PERSON>an<PERSON>pour\" title=\"Naze<PERSON> Ganjapour\"><PERSON><PERSON><PERSON></a>, Iranian footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>an<PERSON>pour\" title=\"Naze<PERSON> Ganjapour\"><PERSON><PERSON><PERSON></a>, Iranian footballer and manager (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>m_<PERSON>ur"}]}, {"year": "1943", "text": "<PERSON>, English singer-songwriter, guitarist, and producer (d. 1976)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American screenwriter and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American basketball player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Israeli viola player and composer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli viola player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>lani\"><PERSON><PERSON><PERSON></a>, Israeli viola player and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iv<PERSON>_Golani"}]}, {"year": "1946", "text": "<PERSON>, American mathematician, computer scientist, and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, computer scientist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, computer scientist, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Dutch-Australian singer-songwriter, guitarist, and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Australian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Australian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English architect and politician, 1st Mayor of Bristol", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Mayor_of_Bristol)\" class=\"mw-redirect\" title=\"<PERSON> (Mayor of Bristol)\"><PERSON></a>, English architect and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Bristol\" title=\"Mayor of Bristol\">Mayor of Bristol</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Mayor_of_Bristol)\" class=\"mw-redirect\" title=\"<PERSON> (Mayor of Bristol)\"><PERSON></a>, English architect and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Bristol\" title=\"Mayor of Bristol\">Mayor of Bristol</a>", "links": [{"title": "<PERSON> (Mayor of Bristol)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Mayor_of_Bristol)"}, {"title": "Mayor of Bristol", "link": "https://wikipedia.org/wiki/Mayor_of_Bristol"}]}, {"year": "1947", "text": "<PERSON>, American author and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Dutch basketball player and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American journalist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wolf Blitzer\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wolf Blitzer\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1948", "text": "<PERSON>, English composer and director", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, French actress, director, and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Fanny_Ardant\" title=\"Fanny Ardant\"><PERSON></a>, French actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ardant\" title=\"Fanny Ardant\"><PERSON></a>, French actress, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fanny_Ardant"}]}, {"year": "1949", "text": "<PERSON>, English journalist (d. 2010)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Scottish lawyer and politician, Secretary of State for Scotland", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Scotland"}]}, {"year": "1952", "text": "<PERSON>, American sportscaster", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American economist and chess grandmaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and chess grandmaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and chess grandmaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Swedish actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Latvian physician and politician, 7th President of Latvia", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>atlers\" title=\"<PERSON><PERSON> Zatlers\"><PERSON><PERSON></a>, Latvian physician and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Zatlers\" title=\"<PERSON><PERSON> Zatlers\"><PERSON><PERSON></a>, Latvian physician and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a>", "links": [{"title": "Valdis Zatlers", "link": "https://wikipedia.org/wiki/Valdis_Zatlers"}, {"title": "President of Latvia", "link": "https://wikipedia.org/wiki/President_of_Latvia"}]}, {"year": "1956", "text": "<PERSON>, Grand Duchess of Luxembourg", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duchess_of_Luxembourg\" title=\"<PERSON>, Grand Duchess of Luxembourg\"><PERSON>, Grand Duchess of Luxembourg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duchess_of_Luxembourg\" title=\"<PERSON>, Grand Duchess of Luxembourg\"><PERSON>, Grand Duchess of Luxembourg</a>", "links": [{"title": "<PERSON>, Grand Duchess of Luxembourg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duchess_of_Luxembourg"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_Bucher\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_Bucher\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON>rgen_Bucher"}]}, {"year": "1957", "text": "<PERSON>, American actress and singer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American football player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, British comic book writer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British comic book writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British comic book writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Greek footballer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ko<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nikos_Kourbanas"}]}, {"year": "1963", "text": "<PERSON>, English ballerina", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballerina", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballerina", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English pop singer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pop singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Finnish ice hockey player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Virta\" title=\"Hannu Virta\"><PERSON><PERSON></a>, Finnish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Virta\" title=\"Hannu Virta\"><PERSON><PERSON></a>, Finnish ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Virta"}]}, {"year": "1963", "text": "<PERSON>, Peruvian engineer and politician, 67th President of Peru", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Vizcarra\" title=\"<PERSON>\"><PERSON></a>, Peruvian engineer and politician, 67th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Vizcarra\" title=\"<PERSON>\"><PERSON></a>, Peruvian engineer and politician, 67th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Vizcarra"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1965", "text": " <PERSON>, British rapper", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_MC_(rapper)\" title=\"<PERSON> MC (rapper)\"> <PERSON> MC</a>, British rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_MC_(rapper)\" title=\"<PERSON> (rapper)\"> <PERSON> MC</a>, British rapper", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rapper)"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2015)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Latvian academic and politician, 11th Minister for Defence of Latvia", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Artis_Pabriks\" title=\"Artis Pa<PERSON>ks\"><PERSON><PERSON></a>, Latvian academic and politician, 11th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_of_Latvia\" class=\"mw-redirect\" title=\"Minister for Defence of Latvia\">Minister for Defence of Latvia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artis_Pa<PERSON>ks\" title=\"Artis <PERSON>\"><PERSON><PERSON></a>, Latvian academic and politician, 11th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_of_Latvia\" class=\"mw-redirect\" title=\"Minister for Defence of Latvia\">Minister for Defence of Latvia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artis_Pabriks"}, {"title": "Minister for Defence of Latvia", "link": "https://wikipedia.org/wiki/Minister_for_Defence_of_Latvia"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese runner", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Ant%C3%<PERSON><PERSON><PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (athlete)", "link": "https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_<PERSON><PERSON>_(athlete)"}]}, {"year": "1966", "text": "<PERSON>, American basketball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1967", "text": "<PERSON>, Italian cyclist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Scottish-English footballer (d. 2011)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English footballer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English footballer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American football player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Russell_<PERSON>\" title=\"Russell Maryland\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russell_Maryland\" title=\"Russell Maryland\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Russell_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Swedish singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Dutch cyclist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, South Korean runner", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-cho\" title=\"<PERSON><PERSON>-cho\"><PERSON><PERSON>-cho</a>, South Korean runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-cho\" title=\"<PERSON><PERSON>-cho\"><PERSON><PERSON>-cho</a>, South Korean runner", "links": [{"title": "<PERSON><PERSON>cho", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-cho"}]}, {"year": "1971", "text": "<PERSON><PERSON>-<PERSON>, American actor, comedian, and writer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, American actor, comedian, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, American actor, comedian, and writer", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor and martial artist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, German-American basketball player, coach, and actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American basketball player, coach, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American basketball player, coach, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player (d. 2006)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian figure skater and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Knight\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Knight\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON> Knight", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Belgian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Greek handball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lia\" title=\"<PERSON><PERSON>oria <PERSON>lia\"><PERSON><PERSON><PERSON></a>, Greek handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek handball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Golia"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>s_Gr%C3%B6nman\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>s_Gr%C3%B6nman\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tu<PERSON>s_Gr%C3%B6nman"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Mexican producer and singer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican producer and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican producer and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Geo_<PERSON>eses"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_D%C3%<PERSON><PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guillermo_D%C3%<PERSON><PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/Guillermo_D%C3%<PERSON><PERSON>_(actor)"}]}, {"year": "1975", "text": "<PERSON>, American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Czech-Monegasque tennis player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-Monegasque tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-Monegasque tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Nov%C3%A1k"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Dutch field hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch field hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American musician and songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American musician and songwriter", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)"}]}, {"year": "1977", "text": "<PERSON>, American football player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Venezuelan film director and screenwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rodr%C3%ADguez_R%C3%ADos\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan film director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rodr%C3%ADguez_R%C3%ADos\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan film director and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anabel_Rodr%C3%ADguez_R%C3%ADos"}]}, {"year": "1978", "text": "<PERSON>,  American football player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Greek hip hop singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek hip hop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek hip hop singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Aaron North\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Aaron North\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Dominican baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, German runner", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American rapper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1982", "text": "<PERSON><PERSON>, South Sudanese basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Deng_<PERSON>\" title=\"Deng <PERSON>\"><PERSON><PERSON></a>, South Sudanese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deng_<PERSON>\" title=\"Deng <PERSON>\"><PERSON><PERSON></a>, South Sudanese basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Deng_<PERSON>ai"}]}, {"year": "1982", "text": "<PERSON>, Italian cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Enrico_<PERSON>o"}]}, {"year": "1982", "text": "<PERSON>, Canadian skier", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Pi%C3%<PERSON>_(footballer,_born_1982)\" title=\"<PERSON><PERSON> (footballer, born 1982)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pi%C3%<PERSON>_(footballer,_born_1982)\" title=\"<PERSON><PERSON> (footballer, born 1982)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1982)", "link": "https://wikipedia.org/wiki/Pi%C3%A1_(footballer,_born_1982)"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1982)\" title=\"<PERSON> (ice hockey, born 1982)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1982)\" title=\"<PERSON> (ice hockey, born 1982)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1982)"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON>.</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American football player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Biboko\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Biboko\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Danish cyclist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Australian swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, British rallycross driver", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British rallycross driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British rallycross driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Costa Rican environmentalist (d. 2013)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican environmentalist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican environmentalist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Romanian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Barbadian cricketer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Roston_Chase\" title=\"Roston Chase\"><PERSON><PERSON><PERSON> <PERSON></a>, Barbadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roston_Chase\" title=\"Roston Chase\"><PERSON><PERSON><PERSON> Chase</a>, Barbadian cricketer", "links": [{"title": "Roston Chase", "link": "https://wikipedia.org/wiki/Roston_Chase"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Cape Verdean basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cape Verdean basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cape Verdean basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Puerto Rican baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_D%C3%ADaz\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADaz\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edwin_D%C3%ADaz"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Taurean_Prince\" title=\"Taurean Prince\">Taurean <PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taurean_Prince\" title=\"Taurean Prince\">Taurean Prince</a>, American basketball player", "links": [{"title": "Taurean Prince", "link": "https://wikipedia.org/wiki/Taurean_Prince"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Belarusian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woon\" title=\"<PERSON>-woon\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woon\" title=\"<PERSON>woon\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-woon"}]}, {"year": "1995", "text": "<PERSON>, American actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (American actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)"}]}, {"year": "1997", "text": "<PERSON>, Italian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Nigerian-American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Metu\" title=\"<PERSON><PERSON><PERSON> Metu\"><PERSON><PERSON><PERSON></a>, Nigerian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Metu\" title=\"Chimezie Metu\"><PERSON><PERSON><PERSON></a>, Nigerian-American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Greek footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Latvian ice hockey player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Art%C5%ABrs_%C5%A0ilovs\" title=\"<PERSON><PERSON><PERSON>ilov<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art%C5%ABrs_%C5%A0ilovs\" title=\"<PERSON><PERSON><PERSON>v<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Art%C5%ABrs_%C5%A0ilovs"}]}], "Deaths": [{"year": "235", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 208)", "html": "235 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Alexander\" title=\"<PERSON><PERSON><PERSON> Alexander\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 208)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Alexander\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 208)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "880", "text": "<PERSON><PERSON> of Bavaria, Frankish king", "html": "880 - <a href=\"https://wikipedia.org/wiki/Carloman_of_Bavaria\" title=\"<PERSON><PERSON> of Bavaria\"><PERSON><PERSON> of Bavaria</a>, Frankish king", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carloman_of_Bavaria\" title=\"Carlo<PERSON> of Bavaria\"><PERSON><PERSON> of Bavaria</a>, Frankish king", "links": [{"title": "<PERSON><PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Carloman_of_Bavaria"}]}, {"year": "1144", "text": "<PERSON> of Norwich, child murder victim", "html": "1144 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Norwich\" title=\"<PERSON> of Norwich\"><PERSON> of Norwich</a>, child murder victim", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Norwich\" title=\"<PERSON> of Norwich\"><PERSON> of Norwich</a>, child murder victim", "links": [{"title": "<PERSON> of Norwich", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Norwich"}]}, {"year": "1322", "text": "<PERSON>, 2nd Earl of Lancaster, English politician, Lord <PERSON> of England (b. 1278)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Lancaster\" title=\"<PERSON>, 2nd Earl of Lancaster\"><PERSON>, 2nd Earl of Lancaster</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_Steward\" title=\"Lord <PERSON> Steward\">Lord <PERSON> Steward of England</a> (b. 1278)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Lancaster\" title=\"<PERSON>, 2nd Earl of Lancaster\"><PERSON>, 2nd Earl of Lancaster</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_Steward\" title=\"Lord <PERSON> Steward\">Lord <PERSON> Steward of England</a> (b. 1278)", "links": [{"title": "<PERSON>, 2nd Earl of Lancaster", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Lancaster"}, {"title": "Lord High Steward", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Steward"}]}, {"year": "1418", "text": "<PERSON> of Nieheim, German bishop and historian (b. 1345)", "html": "1418 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Nieheim\" title=\"<PERSON> of Nieheim\"><PERSON> of Nieheim</a>, German bishop and historian (b. 1345)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nieheim\" title=\"<PERSON> of Nieheim\"><PERSON> of Nieheim</a>, German bishop and historian (b. 1345)", "links": [{"title": "<PERSON> of Nieheim", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>eim"}]}, {"year": "1421", "text": "<PERSON> of Lancaster, 1st Duke of Clarence, English soldier and politician, Lord <PERSON> St<PERSON>ard of England (b. 1388)", "html": "1421 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_<PERSON>_Clarence\" class=\"mw-redirect\" title=\"<PERSON> of Lancaster, 1st Duke of Clarence\"><PERSON> Lancaster, 1st Duke of Clarence</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_Steward\" title=\"Lord <PERSON> Steward\">Lord <PERSON> Steward of England</a> (b. 1388)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Clarence\" class=\"mw-redirect\" title=\"<PERSON> of Lancaster, 1st Duke of Clarence\"><PERSON> Lancaster, 1st Duke of Clarence</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_Steward\" title=\"Lord <PERSON> Steward\">Lord <PERSON> Steward of England</a> (b. 1388)", "links": [{"title": "<PERSON> Lancaster, 1st Duke of Clarence", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_<PERSON>_Clarence"}, {"title": "Lord High Steward", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Steward"}]}, {"year": "1454", "text": "<PERSON>, Archbishop of Canterbury", "html": "1454 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}]}, {"year": "1471", "text": "<PERSON> of Poděbrady (b. 1420)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pod%C4%9Bbrady\" title=\"<PERSON> of Poděbrady\"><PERSON> of Poděbrady</a> (b. 1420)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pod%C4%9Bbrady\" title=\"<PERSON> of Poděbrady\"><PERSON> of Poděbrady</a> (b. 1420)", "links": [{"title": "<PERSON> of Poděbrady", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pod%C4%9Bbrady"}]}, {"year": "1544", "text": "<PERSON>, Swedish archbishop and theologian (b. 1488)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish archbishop and theologian (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish archbishop and theologian (b. 1488)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1602", "text": "<PERSON><PERSON><PERSON>, Italian painter and educator (b. 1557)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and educator (b. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and educator (b. 1557)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ci"}]}, {"year": "1685", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1638)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Sai\" title=\"Emperor <PERSON><PERSON>Sai\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Sai\" title=\"Emperor <PERSON><PERSON>Sai\">Emperor <PERSON><PERSON>Sai</a> of Japan (b. 1638)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1687", "text": "<PERSON><PERSON><PERSON>, Italian-French composer and conductor (b. 1632)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French composer and conductor (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French composer and conductor (b. 1632)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, English minister, theologian, and philosopher (b. 1703)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English minister, theologian, and philosopher (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English minister, theologian, and philosopher (b. 1703)", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)"}]}, {"year": "1820", "text": "<PERSON>, American commander (b. 1779)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander (b. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, German novelist, poet, playwright, and diplomat (b. 1749)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German novelist, poet, playwright, and diplomat (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German novelist, poet, playwright, and diplomat (b. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, French mathematician and academic (b. 1798)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_<PERSON>illier"}]}, {"year": "1864", "text": "<PERSON><PERSON><PERSON><PERSON>, writer, journalist, lawyer and revolutionary (b. 1838)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, writer, journalist, lawyer and revolutionary (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, writer, journalist, lawyer and revolutionary (b. 1838)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, English businessman (b. 1793)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(industrialist)\" title=\"<PERSON> (industrialist)\"><PERSON></a>, English businessman (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(industrialist)\" title=\"<PERSON> (industrialist)\"><PERSON></a>, English businessman (b. 1793)", "links": [{"title": "<PERSON> (industrialist)", "link": "https://wikipedia.org/wiki/<PERSON>_(industrialist)"}]}, {"year": "1896", "text": "<PERSON>, English lawyer and politician (b. 1822)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Chinese educator and politician (b. 1882)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Song_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese educator and politician (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Song_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese educator and politician (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Song_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON><PERSON>,  Italian physiologist and anatomist (b. 1864)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian physiologist and anatomist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ug<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian physiologist and anatomist (b. 1864)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rugger<PERSON>_<PERSON>i"}]}, {"year": "1924", "text": "<PERSON>, Scottish surgeon and neuroscientist (b. 1848)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and neuroscientist (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and neuroscientist (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, 1st Baron <PERSON>, Irish lawyer and politician (b. 1851)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron Glenavy\"><PERSON>, 1st Baron <PERSON></a>, Irish lawyer and politician (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>avy\"><PERSON>, 1st Baron <PERSON></a>, Irish lawyer and politician (b. 1851)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English cricketer (b. 1875)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1875)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1942", "text": "<PERSON>, English captain and cricketer (b. 1875)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English captain and cricketer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English captain and cricketer (b. 1875)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1942", "text": "<PERSON>, Uruguayan journalist and activist (b. 1884)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan journalist and activist (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan journalist and activist (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_Collazo"}]}, {"year": "1952", "text": "<PERSON><PERSON> <PERSON><PERSON>, 1st Prime Minister of Sri Lanka (b. 1883)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/D._S._Senanayake\" title=\"D. S. Senanayake\">D<PERSON> S. Senanayake</a>, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka\" title=\"Prime Minister of Sri Lanka\">Prime Minister of Sri Lanka</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D._S._Senanayake\" title=\"D. S. Senanayake\"><PERSON><PERSON> S<PERSON>anayake</a>, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka\" title=\"Prime Minister of Sri Lanka\">Prime Minister of Sri Lanka</a> (b. 1883)", "links": [{"title": "D. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D._S._Senanayake"}, {"title": "Prime Minister of Sri Lanka", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka"}]}, {"year": "1955", "text": "<PERSON>, Croatian lawyer and politician, 23rd Prime Minister of Yugoslavia (b. 1892)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0uba%C5%A1i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0uba%C5%A1i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ivan_%C5%A0uba%C5%A1i%C4%87"}, {"title": "Prime Minister of Yugoslavia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia"}]}, {"year": "1958", "text": "<PERSON>, American film producer (b. 1909)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Spanish lawyer and politician, 1st President of the Basque Country (b. 1904)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Spanish lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Basque_Country\" class=\"mw-redirect\" title=\"President of the Basque Country\">President of the Basque Country</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>%C3%A<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Spanish lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Basque_Country\" class=\"mw-redirect\" title=\"President of the Basque Country\">President of the Basque Country</a> (b. 1904)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>_(politician)"}, {"title": "President of the Basque Country", "link": "https://wikipedia.org/wiki/President_of_the_Basque_Country"}]}, {"year": "1966", "text": "<PERSON>, American mountaineer and pilot (b. 1935)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer and pilot (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer and pilot (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Estonian-American runner (b. 1893)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-American runner (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-American runner (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress and vaudevillian (b. 1886)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and vaudevillian (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and vaudevillian (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American race car driver (b. 1939)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Italian automobile designer (b. 1910)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Puliga\" title=\"<PERSON><PERSON><PERSON> Puli<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian automobile designer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Puliga\" title=\"<PERSON><PERSON><PERSON> Puliga\"><PERSON><PERSON><PERSON></a>, Italian automobile designer (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>uliga"}]}, {"year": "1976", "text": "<PERSON>, American painter (b. 1898)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter (b. 1898)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1977", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian educator and politician (b. 1904)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian educator and politician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian educator and politician (b. 1904)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, German-American acrobat and tightrope walker, founded The Flying Wallendas (b. 1905)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American acrobat and tightrope walker, founded <a href=\"https://wikipedia.org/wiki/The_Flying_Wallendas\" title=\"The Flying Wallendas\">The Flying Wallendas</a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American acrobat and tightrope walker, founded <a href=\"https://wikipedia.org/wiki/The_Flying_Wallendas\" title=\"The Flying Wallendas\">The Flying Wallendas</a> (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Flying Wallendas", "link": "https://wikipedia.org/wiki/The_Flying_Wallendas"}]}, {"year": "1979", "text": "<PERSON>, American actor and studio executive (b. 1901)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and studio executive (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and studio executive (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American runner and coach (b. 1915)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(coach)\" title=\"<PERSON><PERSON> (coach)\"><PERSON></a>, American runner and coach (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(coach)\" title=\"<PERSON><PERSON> (coach)\"><PERSON></a>, American runner and coach (b. 1915)", "links": [{"title": "<PERSON><PERSON> (coach)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(coach)"}]}, {"year": "1981", "text": "<PERSON>,  Filipino businessman and politician, 13th President of the Senate of the Philippines (b. 1907)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino businessman and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino businessman and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Senate of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines"}]}, {"year": "1985", "text": "<PERSON>, French painter, sculptor, photographer, and engraver (b. 1910)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter, sculptor, photographer, and engraver (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter, sculptor, photographer, and engraver (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Greek painter, printmaker, illustrator, and stage designer (b. 1903)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter, printmaker, illustrator, and stage designer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter, printmaker, illustrator, and stage designer (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress (b. 1918)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Deering\"><PERSON></a>, American actress (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Deering"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek general and politician (b. 1912)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON><PERSON>_<PERSON>is\" title=\"O<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek general and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"O<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek general and politician (b. 1912)", "links": [{"title": "O<PERSON>sseas <PERSON>", "link": "https://wikipedia.org/wiki/Odysseas_Angelis"}]}, {"year": "1989", "text": "<PERSON><PERSON>, English cricketer (b. 1912)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian engineer and academic (b. 1928)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Bull\"><PERSON></a>, Canadian engineer and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian lawyer and politician, 19th Solicitor General of Canada (b. 1917)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Canada\" title=\"Solicitor General of Canada\">Solicitor General of Canada</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Canada\" title=\"Solicitor General of Canada\">Solicitor General of Canada</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Ba<PERSON>cer"}, {"title": "Solicitor General of Canada", "link": "https://wikipedia.org/wiki/Solicitor_General_of_Canada"}]}, {"year": "1991", "text": "<PERSON>, American novelist, poet, playwright, and critic (b. 1908)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, playwright, and critic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, playwright, and critic (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American singer-songwriter and guitarist  (b. 1934)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Guard\"><PERSON></a>, American singer-songwriter and guitarist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English-American actress (b. 1908)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American baseball player (b. 1965)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American singer-songwriter, and producer (b. 1950)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, and producer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, and producer (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American animator, director, and producer (b. 1899)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American drummer (b. 1945)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer (b. 1945)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)"}]}, {"year": "1996", "text": "<PERSON>, American colonel, pilot, and astronaut (b. 1936)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American guitarist (b. 1925)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American guitarist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American guitarist (b. 1925)", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>(guitarist)"}]}, {"year": "1999", "text": "<PERSON>, <PERSON>, English historian and academic (b. 1913)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English historian and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English historian and academic (b. 1913)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actor (b. 1969)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Italian footballer and manager (b. 1921)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Lithuanian basketball player and coach (b. 1925)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player and coach (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player and coach (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>as"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Turkish soldier and pilot (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Sabiha_G%C3%B6k%C3%A7en\" title=\"Sabiha Gökçen\"><PERSON><PERSON><PERSON></a>, Turkish soldier and pilot (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sabiha_G%C3%B6k%C3%A7en\" title=\"Sabiha Gökçen\"><PERSON><PERSON><PERSON></a>, Turkish soldier and pilot (b. 1913)", "links": [{"title": "Sabiha Gökçen", "link": "https://wikipedia.org/wiki/Sabiha_G%C3%B6k%C3%A7en"}]}, {"year": "2001", "text": "<PERSON>, American animator, director, producer, and voice actor, co-founded <PERSON><PERSON><PERSON><PERSON> (b. 1910)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, producer, and voice actor, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>-Barber<PERSON>\" title=\"Hanna-Barbera\"><PERSON><PERSON></a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Hanna\"><PERSON></a>, American animator, director, producer, and voice actor, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>-Barber<PERSON>\" title=\"<PERSON>-Barbera\"><PERSON><PERSON></a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Canadian businessman, academic, and civil servant (b. 1910)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, academic, and civil servant (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, academic, and civil servant (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Swiss violinist and conductor (b. 1917)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss violinist and conductor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss violinist and conductor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English journalist (b. 1952)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Turkish-American astronomer and academic (b. 1943)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-American astronomer and academic (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-American astronomer and academic (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%<PERSON><PERSON>_<PERSON>ei"}]}, {"year": "2004", "text": "<PERSON>, Co-founded Hamas (b. 1937)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Co-founded <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Co-founded <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hamas", "link": "https://wikipedia.org/wiki/Hamas"}]}, {"year": "2004", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and civil rights activist (b. 1909)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/V._<PERSON>._Tarkunde\" title=\"V. M. Tarkunde\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and civil rights activist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V._<PERSON>._Tarkunde\" title=\"V. M. Tarkunde\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and civil rights activist (b. 1909)", "links": [{"title": "V. M. Tark<PERSON>e", "link": "https://wikipedia.org/wiki/V._M._Tarkunde"}]}, {"year": "2005", "text": "<PERSON>, English guitarist and songwriter (b. 1947)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, English guitarist and songwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, English guitarist and songwriter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Price"}]}, {"year": "2005", "text": "<PERSON>, Indian film actor (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ganesan\" title=\"Gemini Ganesan\"><PERSON></a>, Indian film actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ganesan\" title=\"Gemini Ganesan\"><PERSON></a>, Indian film actor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Japanese architect, designed the Yoyogi National Gymnasium and Hiroshima Peace Memorial Museum (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Kenz%C5%8D_Tange\" title=\"Kenz<PERSON> Tange\"><PERSON><PERSON><PERSON></a>, Japanese architect, designed the <a href=\"https://wikipedia.org/wiki/Yoyogi_National_Gymnasium\" title=\"Yoyogi National Gymnasium\">Yoyogi National Gymnasium</a> and <a href=\"https://wikipedia.org/wiki/Hiroshima_Peace_Memorial_Museum\" title=\"Hiroshima Peace Memorial Museum\">Hiroshima Peace Memorial Museum</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenz%C5%8D_Tange\" title=\"Kenz<PERSON> Tange\"><PERSON><PERSON><PERSON></a>, Japanese architect, designed the <a href=\"https://wikipedia.org/wiki/Yoyogi_National_Gymnasium\" title=\"Yoyogi National Gymnasium\">Yoyogi National Gymnasium</a> and <a href=\"https://wikipedia.org/wiki/Hiroshima_Peace_Memorial_Museum\" title=\"Hiroshima Peace Memorial Museum\">Hiroshima Peace Memorial Museum</a> (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kenz%C5%8D_Tange"}, {"title": "Yoyogi National Gymnasium", "link": "https://wikipedia.org/wiki/Yoyogi_National_Gymnasium"}, {"title": "Hiroshima Peace Memorial Museum", "link": "https://wikipedia.org/wiki/Hiroshima_Peace_Memorial_Museum"}]}, {"year": "2006", "text": "<PERSON>, French soldier, pilot, and politician (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier, pilot, and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier, pilot, and politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Cuban singer and author (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/P%C3%ADo_Leyva\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban singer and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%ADo_Leyva\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban singer and author (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%ADo_Leyva"}]}, {"year": "2006", "text": "<PERSON>, Austrian-Australian journalist and author (b. 1937)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Australian journalist and author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Australian journalist and author (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian-Italian philosopher and educator (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/U._G._<PERSON>rt<PERSON>\" title=\"U. G. Krishna<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian-Italian philosopher and educator (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U._G<PERSON>_<PERSON>\" title=\"U. G. Krishna<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian-Italian philosopher and educator (b. 1918)", "links": [{"title": "U<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U._G._<PERSON>i"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Cuban-American bassist and composer (b. 1918)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Cachao_L%C3%B3pez\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban-American bassist and composer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cachao_L%C3%B3pez\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban-American bassist and composer (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cachao_L%C3%B3pez"}]}, {"year": "2010", "text": "<PERSON>, Scottish biologist and pharmacologist, Nobel Prize laureate (b. 1924)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pharmacologist)\" title=\"<PERSON> (pharmacologist)\"><PERSON></a>, Scottish biologist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pharmacologist)\" title=\"<PERSON> (pharmacologist)\"><PERSON></a>, Scottish biologist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1924)", "links": [{"title": "<PERSON> (pharmacologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pharmacologist)"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Turkish basketball player and businessman (b. 1943)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/%C3%96zhan_Canayd%C4%B1n\" title=\"<PERSON><PERSON><PERSON>aydın\"><PERSON><PERSON><PERSON></a>, Turkish basketball player and businessman (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96zhan_Canayd%C4%B1n\" title=\"<PERSON><PERSON><PERSON>aydın\"><PERSON><PERSON><PERSON></a>, Turkish basketball player and businessman (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96zhan_Canayd%C4%B1n"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Portuguese journalist (b. 1920)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese journalist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese journalist (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Canadian pianist and composer (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American football player and wrestler (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American computer scientist and academic (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English anthropologist and author (b. 1956)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anthropologist and author (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anthropologist and author (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech actor and politician (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_%C4%8Cech\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech actor and politician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_%C4%8Cech\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech actor and politician (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladim%C3%ADr_%C4%8Cech"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Cuban-Swedish pianist and composer (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Bebo_Vald%C3%A9s\" title=\"<PERSON><PERSON> Valdés\"><PERSON><PERSON></a>, Cuban-Swedish pianist and composer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vald%C3%A9s\" title=\"<PERSON><PERSON> Valdés\"><PERSON><PERSON></a>, Cuban-Swedish pianist and composer (b. 1918)", "links": [{"title": "Bebo Valdés", "link": "https://wikipedia.org/wiki/Bebo_Vald%C3%A9s"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>ttal\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ttal\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON>wan<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>ttal"}]}, {"year": "2014", "text": "<PERSON>, Polish-English boxer and manager (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English boxer and manager (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English boxer and manager (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Norwegian soldier and politician (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian soldier and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian soldier and politician (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>au"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Cypriot politician, Cypriot Minister of Defence (b. 1965)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Defence_of_Cyprus\" class=\"mw-redirect\" title=\"List of Ministers of Defence of Cyprus\">Cypriot Minister of Defence</a> (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Defence_of_Cyprus\" class=\"mw-redirect\" title=\"List of Ministers of Defence of Cyprus\">Cypriot Minister of Defence</a> (b. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Ministers of Defence of Cyprus", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Defence_of_Cyprus"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Ukrainian-Russian actor and playwright (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Ark<PERSON>_<PERSON>v\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian actor and playwright (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ark<PERSON>_<PERSON>v\" title=\"Ark<PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian actor and playwright (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}]}, {"year": "2015", "text": "<PERSON><PERSON>, German footballer and manager (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American pianist, composer, and conductor (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Canadian businessman and politician, 64th Mayor of Toronto (b. 1969)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ford\" title=\"Rob Ford\"><PERSON></a>, Canadian businessman and politician, 64th <a href=\"https://wikipedia.org/wiki/Mayor_of_Toronto\" title=\"Mayor of Toronto\">Mayor of Toronto</a> (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ford\" title=\"Rob Ford\"><PERSON></a>, Canadian businessman and politician, 64th <a href=\"https://wikipedia.org/wiki/Mayor_of_Toronto\" title=\"Mayor of Toronto\">Mayor of Toronto</a> (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Toronto", "link": "https://wikipedia.org/wiki/Mayor_of_Toronto"}]}, {"year": "2016", "text": "<PERSON>, American actress (b. 1927)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Dutch politician, academic and author, Ya<PERSON> recipient (b. 1911)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician, academic and author, <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"Ya<PERSON>\">Ya<PERSON></a> recipient (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician, academic and author, <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"Ya<PERSON>\">Ya<PERSON></a> recipient (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yad_<PERSON><PERSON><PERSON>"}]}, {"year": "2019", "text": "<PERSON>, British-American singer-songwriter (b. 1943)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British-American singer-songwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British-American singer-songwriter (b. 1943)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "2024", "text": "<PERSON>, French author and illustrator (b. 1925)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}