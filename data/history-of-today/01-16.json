{"date": "January 16", "url": "https://wikipedia.org/wiki/January_16", "data": {"Events": [{"year": "1458 BC", "text": "<PERSON><PERSON><PERSON><PERSON> dies at the age of 50 and is buried in the Valley of the Kings.", "html": "1458 BC - 1458 BC - <a href=\"https://wikipedia.org/wiki/Hatshepsut\" title=\"Hatshepsut\">Hatshepsut</a> dies at the age of 50 and is buried in the <a href=\"https://wikipedia.org/wiki/Valley_of_the_Kings\" title=\"Valley of the Kings\">Valley of the Kings</a>.", "no_year_html": "1458 BC - <a href=\"https://wikipedia.org/wiki/Hatshepsut\" title=\"Hatshepsut\">Hatshepsut</a> dies at the age of 50 and is buried in the <a href=\"https://wikipedia.org/wiki/Valley_of_the_Kings\" title=\"Valley of the Kings\">Valley of the Kings</a>.", "links": [{"title": "Hatshepsut", "link": "https://wikipedia.org/wiki/Hatshepsut"}, {"title": "Valley of the Kings", "link": "https://wikipedia.org/wiki/Valley_of_the_Kings"}]}, {"year": "27 BC", "text": "Gaius <PERSON> is granted the title Augustus by the Roman Senate, marking the beginning of the Roman Empire.", "html": "27 BC - 27 BC - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Gaius Julius Caesar <PERSON>avianus</a> is granted the title <a href=\"https://wikipedia.org/wiki/Augustus_(honorific)\" class=\"mw-redirect\" title=\"Augustus (honorific)\">Augustus</a> by the <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a>, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a>.", "no_year_html": "27 BC - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Gaius Julius Caesar <PERSON>avianus</a> is granted the title <a href=\"https://wikipedia.org/wiki/Augustus_(honorific)\" class=\"mw-redirect\" title=\"Augustus (honorific)\">Augustus</a> by the <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a>, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}, {"title": "<PERSON> (honorific)", "link": "https://wikipedia.org/wiki/<PERSON>_(honorific)"}, {"title": "Roman Senate", "link": "https://wikipedia.org/wiki/Roman_Senate"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}]}, {"year": "378", "text": "General <PERSON><PERSON><PERSON>' conquers Tikal, enlarging the domain of King <PERSON><PERSON><PERSON><PERSON><PERSON> of Teotihuacán.", "html": "378 - General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>j_<PERSON>%27ak%27\" class=\"mw-redirect\" title=\"<PERSON>ya<PERSON>'ak'\"><PERSON><PERSON><PERSON>'ak'</a> conquers <a href=\"https://wikipedia.org/wiki/Tikal\" title=\"Tikal\">Tikal</a>, enlarging the domain of King <a href=\"https://wikipedia.org/wiki/Spearthrower_Owl\" title=\"Spearthrower Owl\">Spearthrower Owl</a> of <a href=\"https://wikipedia.org/wiki/Teotihuac%C3%A1n\" class=\"mw-redirect\" title=\"Teotihuacán\">Teotihuacán</a>.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27ak%27\" class=\"mw-redirect\" title=\"<PERSON>ya<PERSON>'ak'\"><PERSON><PERSON><PERSON>'ak'</a> conquers <a href=\"https://wikipedia.org/wiki/Tikal\" title=\"Tikal\">Tikal</a>, enlarging the domain of King <a href=\"https://wikipedia.org/wiki/Spearthrower_Owl\" title=\"Spearthrower Owl\">Spearthrower Owl</a> of <a href=\"https://wikipedia.org/wiki/Teotihuac%C3%A1n\" class=\"mw-redirect\" title=\"Teotihuacán\">Teotihuacán</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>'", "link": "https://wikipedia.org/wiki/Siyaj_K%27ak%27"}, {"title": "Tikal", "link": "https://wikipedia.org/wiki/Tikal"}, {"title": "Spearthrower Owl", "link": "https://wikipedia.org/wiki/Spearthrower_Owl"}, {"title": "Teotihuacán", "link": "https://wikipedia.org/wiki/Teotihuac%C3%A1n"}]}, {"year": "550", "text": "Gothic War: The Ostrogoths, under King <PERSON><PERSON><PERSON>, conquer Rome after a long siege, by bribing the Isaurian garrison.", "html": "550 - <a href=\"https://wikipedia.org/wiki/Gothic_War_(535%E2%80%93554)\" title=\"Gothic War (535-554)\">Gothic War</a>: The <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a>, under King <a href=\"https://wikipedia.org/wiki/Totila\" title=\"Totila\">Totila</a>, conquer Rome after a long siege, by bribing the <a href=\"https://wikipedia.org/wiki/Isauria\" title=\"Isauria\">Isaurian</a> garrison.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gothic_War_(535%E2%80%93554)\" title=\"Gothic War (535-554)\">Gothic War</a>: The <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a>, under King <a href=\"https://wikipedia.org/wiki/To<PERSON>a\" title=\"Totil<PERSON>\">Totila</a>, conquer Rome after a long siege, by bribing the <a href=\"https://wikipedia.org/wiki/Isauria\" title=\"Isauria\">Isaurian</a> garrison.", "links": [{"title": "Gothic War (535-554)", "link": "https://wikipedia.org/wiki/Gothic_War_(535%E2%80%93554)"}, {"title": "Ostrogoths", "link": "https://wikipedia.org/wiki/Ostrogoths"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}, {"title": "Isauria", "link": "https://wikipedia.org/wiki/Isauria"}]}, {"year": "929", "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III establishes the Caliphate of Córdoba.", "html": "929 - <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\"><PERSON>ir</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_III\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III</a> establishes the <a href=\"https://wikipedia.org/wiki/Caliphate_of_C%C3%B3rdoba\" class=\"mw-redirect\" title=\"Caliphate of Córdoba\">Caliphate of Córdoba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\"><PERSON>ir</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_III\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III</a> establishes the <a href=\"https://wikipedia.org/wiki/Caliphate_of_C%C3%B3rdoba\" class=\"mw-redirect\" title=\"Caliphate of Córdoba\">Caliphate of Córdoba</a>.", "links": [{"title": "<PERSON>ir", "link": "https://wikipedia.org/wiki/Emir"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Caliphate of Córdoba", "link": "https://wikipedia.org/wiki/Caliphate_of_C%C3%B3rdoba"}]}, {"year": "1120", "text": "Crusades: The Council of Nablus is held, establishing the earliest surviving written laws of the Crusader Kingdom of Jerusalem.", "html": "1120 - <a href=\"https://wikipedia.org/wiki/Crusades\" title=\"Crusades\">Crusades</a>: The <a href=\"https://wikipedia.org/wiki/Council_of_Nablus\" title=\"Council of Nablus\">Council of Nablus</a> is held, establishing the earliest surviving written laws of the <a href=\"https://wikipedia.org/wiki/Crusades\" title=\"Crusades\">Crusader</a> <a href=\"https://wikipedia.org/wiki/Kingdom_of_Jerusalem\" title=\"Kingdom of Jerusalem\">Kingdom of Jerusalem</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crusades\" title=\"Crusades\">Crusades</a>: The <a href=\"https://wikipedia.org/wiki/Council_of_Nablus\" title=\"Council of Nablus\">Council of Nablus</a> is held, establishing the earliest surviving written laws of the <a href=\"https://wikipedia.org/wiki/Crusades\" title=\"Crusades\">Crusader</a> <a href=\"https://wikipedia.org/wiki/Kingdom_of_Jerusalem\" title=\"Kingdom of Jerusalem\">Kingdom of Jerusalem</a>.", "links": [{"title": "Crusades", "link": "https://wikipedia.org/wiki/Crusades"}, {"title": "Council of Nablus", "link": "https://wikipedia.org/wiki/Council_of_Nablus"}, {"title": "Crusades", "link": "https://wikipedia.org/wiki/Crusades"}, {"title": "Kingdom of Jerusalem", "link": "https://wikipedia.org/wiki/Kingdom_of_Jerusalem"}]}, {"year": "1275", "text": "<PERSON> permits his mother <PERSON> of Provence to expel the Jews from the towns Worcester, Marlborough, Cambridge and Gloucester.", "html": "1275 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> I</a> permits his mother <a href=\"https://wikipedia.org/wiki/Eleanor_of_Provence\" title=\"<PERSON> of Provence\"><PERSON> of Provence</a> to expel the Jews from the towns <a href=\"https://wikipedia.org/wiki/Worcester,_England\" title=\"Worcester, England\">Worcester</a>, <a href=\"https://wikipedia.org/wiki/Marlborough,_Wiltshire\" title=\"Marlborough, Wiltshire\">Marlborough</a>, <a href=\"https://wikipedia.org/wiki/Cambridge\" title=\"Cambridge\">Cambridge</a> and <a href=\"https://wikipedia.org/wiki/Gloucester\" title=\"Gloucester\">Gloucester</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> I</a> permits his mother <a href=\"https://wikipedia.org/wiki/Eleanor_of_Provence\" title=\"<PERSON> of Provence\"><PERSON> of Provence</a> to expel the Jews from the towns <a href=\"https://wikipedia.org/wiki/Worcester,_England\" title=\"Worcester, England\">Worcester</a>, <a href=\"https://wikipedia.org/wiki/Marlborough,_Wiltshire\" title=\"Marlborough, Wiltshire\">Marlborough</a>, <a href=\"https://wikipedia.org/wiki/Cambridge\" title=\"Cambridge\">Cambridge</a> and <a href=\"https://wikipedia.org/wiki/Gloucester\" title=\"Gloucester\">Gloucester</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England"}, {"title": "<PERSON> of Provence", "link": "https://wikipedia.org/wiki/Eleanor_of_Provence"}, {"title": "Worcester, England", "link": "https://wikipedia.org/wiki/Worcester,_England"}, {"title": "Marlborough, Wiltshire", "link": "https://wikipedia.org/wiki/Marlborough,_Wiltshire"}, {"title": "Cambridge", "link": "https://wikipedia.org/wiki/Cambridge"}, {"title": "Gloucester", "link": "https://wikipedia.org/wiki/Gloucester"}]}, {"year": "1362", "text": "Saint <PERSON>'s flood kills at least 25,000 people on the shores of the North Sea.", "html": "1362 - <a href=\"https://wikipedia.org/wiki/Saint_Marcellus%27s_flood\" title=\"Saint Marcellus's flood\">Saint Marcellus's flood</a> kills at least 25,000 people on the shores of the <a href=\"https://wikipedia.org/wiki/North_Sea\" title=\"North Sea\">North Sea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Marcellus%27s_flood\" title=\"Saint Marcellus's flood\">Saint Marcellus's flood</a> kills at least 25,000 people on the shores of the <a href=\"https://wikipedia.org/wiki/North_Sea\" title=\"North Sea\">North Sea</a>.", "links": [{"title": "<PERSON>'s flood", "link": "https://wikipedia.org/wiki/Saint_Marcellus%27s_flood"}, {"title": "North Sea", "link": "https://wikipedia.org/wiki/North_Sea"}]}, {"year": "1537", "text": "<PERSON><PERSON>'s Rebellion, an armed insurrection attempting to resist the English Reformation, begins.", "html": "1537 - <a href=\"https://wikipedia.org/wiki/Bigod%27s_rebellion\" title=\"<PERSON><PERSON>'s rebellion\"><PERSON><PERSON>'s Rebellion</a>, an armed insurrection attempting to resist the <a href=\"https://wikipedia.org/wiki/English_Reformation\" title=\"English Reformation\">English Reformation</a>, begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bigod%27s_rebellion\" title=\"<PERSON><PERSON>'s rebellion\"><PERSON><PERSON>'s Rebellion</a>, an armed insurrection attempting to resist the <a href=\"https://wikipedia.org/wiki/English_Reformation\" title=\"English Reformation\">English Reformation</a>, begins.", "links": [{"title": "<PERSON><PERSON>'s rebellion", "link": "https://wikipedia.org/wiki/Bigod%27s_rebellion"}, {"title": "English Reformation", "link": "https://wikipedia.org/wiki/English_Reformation"}]}, {"year": "1547", "text": "Grand Duke <PERSON> of Muscovy becomes the first Tsar of Russia, replacing the 264-year-old Grand Duchy of Moscow with the Tsardom of Russia.", "html": "1547 - Grand Duke <a href=\"https://wikipedia.org/wiki/Ivan_IV\" class=\"mw-redirect\" title=\"Ivan IV\"><PERSON> IV</a> of <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Moscow\" class=\"mw-redirect\" title=\"Grand Duchy of Moscow\">Muscovy</a> becomes the first <a href=\"https://wikipedia.org/wiki/Tsar_of_Russia\" class=\"mw-redirect\" title=\"Tsar of Russia\">Tsar of Russia</a>, replacing the 264-year-old <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Moscow\" class=\"mw-redirect\" title=\"Grand Duchy of Moscow\">Grand Duchy of Moscow</a> with the <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Tsardom of Russia</a>.", "no_year_html": "Grand Duke <a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" class=\"mw-redirect\" title=\"<PERSON> IV\"><PERSON> IV</a> of <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Moscow\" class=\"mw-redirect\" title=\"Grand Duchy of Moscow\">Muscovy</a> becomes the first <a href=\"https://wikipedia.org/wiki/Tsar_of_Russia\" class=\"mw-redirect\" title=\"Tsar of Russia\">Tsar of Russia</a>, replacing the 264-year-old <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Moscow\" class=\"mw-redirect\" title=\"Grand Duchy of Moscow\">Grand Duchy of Moscow</a> with the <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Tsardom of Russia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_IV"}, {"title": "Grand Duchy of Moscow", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Moscow"}, {"title": "Tsar of Russia", "link": "https://wikipedia.org/wiki/Tsar_of_Russia"}, {"title": "Grand Duchy of Moscow", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Moscow"}, {"title": "Tsardom of Russia", "link": "https://wikipedia.org/wiki/Tsardom_of_Russia"}]}, {"year": "1556", "text": "<PERSON> becomes King of Spain.", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> II</a> becomes King of Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> II</a> becomes King of Spain.", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1572", "text": "<PERSON>, 4th Duke of Norfolk is tried and found guilty of treason for his part in the <PERSON><PERSON><PERSON><PERSON> plot to restore Catholicism in England.", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Norfolk\" title=\"<PERSON>, 4th Duke of Norfolk\"><PERSON>, 4th Duke of Norfolk</a> is tried and found guilty of treason for his part in the <a href=\"https://wikipedia.org/wiki/Ridolfi_plot\" title=\"Ridolfi plot\">Rido<PERSON><PERSON> plot</a> to restore <a href=\"https://wikipedia.org/wiki/Catholicism\" class=\"mw-redirect\" title=\"Catholicism\">Catholicism</a> in England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Norfolk\" title=\"<PERSON>, 4th Duke of Norfolk\"><PERSON>, 4th Duke of Norfolk</a> is tried and found guilty of treason for his part in the <a href=\"https://wikipedia.org/wiki/Ridolfi_plot\" title=\"Ridolfi plot\">Rido<PERSON><PERSON> plot</a> to restore <a href=\"https://wikipedia.org/wiki/Catholicism\" class=\"mw-redirect\" title=\"Catholicism\">Catholicism</a> in England.", "links": [{"title": "<PERSON>, 4th Duke of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Norfolk"}, {"title": "<PERSON><PERSON><PERSON><PERSON> plot", "link": "https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_plot"}, {"title": "Catholicism", "link": "https://wikipedia.org/wiki/Catholicism"}]}, {"year": "1605", "text": "The first edition of El ingenioso hidalgo Don Quijote de la Mancha (Book One of Don Quixote) by <PERSON> is published in Madrid, Spain.", "html": "1605 - The first edition of <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Quixote\">El ingenioso hidalgo Don <PERSON> de la Mancha</a></i> (Book One of <i><PERSON></i>) by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Cervantes\"><PERSON></a> is published in <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid, Spain</a>.", "no_year_html": "The first edition of <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Quixote\">El ingenioso hidalgo Don <PERSON> la Mancha</a></i> (Book One of <i><PERSON></i>) by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> de Cervantes\"><PERSON>ntes</a> is published in <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid, Spain</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Madrid", "link": "https://wikipedia.org/wiki/Madrid"}]}, {"year": "1707", "text": "The Scottish Parliament ratifies the Act of Union, paving the way for the creation of Great Britain.", "html": "1707 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_Scotland\" title=\"Parliament of Scotland\">Scottish Parliament</a> ratifies the <a href=\"https://wikipedia.org/wiki/Acts_of_Union_1707\" title=\"Acts of Union 1707\">Act of Union</a>, paving the way for the creation of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_Scotland\" title=\"Parliament of Scotland\">Scottish Parliament</a> ratifies the <a href=\"https://wikipedia.org/wiki/Acts_of_Union_1707\" title=\"Acts of Union 1707\">Act of Union</a>, paving the way for the creation of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a>.", "links": [{"title": "Parliament of Scotland", "link": "https://wikipedia.org/wiki/Parliament_of_Scotland"}, {"title": "Acts of Union 1707", "link": "https://wikipedia.org/wiki/Acts_of_Union_1707"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}]}, {"year": "1716", "text": "King <PERSON> of Spain promulgates the Nueva Planta decree of the Principality of Catalonia, abolishing the Catalan institutions and its legal system, being replaced by those of the Castile, thus putting an end to Catalonia as separate state and becoming a province of the new French-style Kingdom of Spain.", "html": "1716 - King <a href=\"https://wikipedia.org/wiki/Philip_V_of_Spain\" title=\"Philip V of Spain\"><PERSON> of Spain</a> promulgates the <a href=\"https://wikipedia.org/wiki/Nueva_Planta_decrees\" title=\"Nueva Planta decrees\">Nueva Planta decree</a> of the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a>, abolishing the Catalan institutions and its <a href=\"https://wikipedia.org/wiki/Catalan_constitutions\" title=\"Catalan constitutions\">legal system</a>, being replaced by those of the <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castile</a>, thus putting an end to <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a> as separate state and becoming a province of the new French-style <a href=\"https://wikipedia.org/wiki/History_of_Spain_(1700%E2%80%931808)\" title=\"History of Spain (1700-1808)\">Kingdom of Spain</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Philip_<PERSON>_of_Spain\" title=\"<PERSON> V of Spain\"><PERSON> of Spain</a> promulgates the <a href=\"https://wikipedia.org/wiki/Nueva_Planta_decrees\" title=\"Nueva Planta decrees\">Nueva Planta decree</a> of the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a>, abolishing the Catalan institutions and its <a href=\"https://wikipedia.org/wiki/Catalan_constitutions\" title=\"Catalan constitutions\">legal system</a>, being replaced by those of the <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castile</a>, thus putting an end to <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a> as separate state and becoming a province of the new French-style <a href=\"https://wikipedia.org/wiki/History_of_Spain_(1700%E2%80%931808)\" title=\"History of Spain (1700-1808)\">Kingdom of Spain</a>.", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Philip_V_of_Spain"}, {"title": "Nueva Planta decrees", "link": "https://wikipedia.org/wiki/Nueva_Planta_decrees"}, {"title": "Principality of Catalonia", "link": "https://wikipedia.org/wiki/Principality_of_Catalonia"}, {"title": "Catalan constitutions", "link": "https://wikipedia.org/wiki/Catalan_constitutions"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}, {"title": "Catalonia", "link": "https://wikipedia.org/wiki/Catalonia"}, {"title": "History of Spain (1700-1808)", "link": "https://wikipedia.org/wiki/History_of_Spain_(1700%E2%80%931808)"}]}, {"year": "1757", "text": "Forces of the Maratha Empire defeat a 5,000-strong army of the Durrani Empire in the Battle of Narela.", "html": "1757 - Forces of the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> defeat a 5,000-strong army of the <a href=\"https://wikipedia.org/wiki/Durrani_Empire\" title=\"Durrani Empire\">Durrani Empire</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Narela\" title=\"Battle of Narela\">Battle of Narela</a>.", "no_year_html": "Forces of the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> defeat a 5,000-strong army of the <a href=\"https://wikipedia.org/wiki/Durrani_Empire\" title=\"Durrani Empire\">Durrani Empire</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Narela\" title=\"Battle of Narela\">Battle of Narela</a>.", "links": [{"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}, {"title": "Durrani Empire", "link": "https://wikipedia.org/wiki/Du<PERSON>ni_Empire"}, {"title": "Battle of Narela", "link": "https://wikipedia.org/wiki/Battle_of_Narela"}]}, {"year": "1780", "text": "American Revolutionary War: Battle of Cape St. Vincent.", "html": "1780 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cape_St._Vincent_(1780)\" title=\"Battle of Cape St. Vincent (1780)\">Battle of Cape St. Vincent</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cape_St._Vincent_(1780)\" title=\"Battle of Cape St. Vincent (1780)\">Battle of Cape St. Vincent</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Cape St. Vincent (1780)", "link": "https://wikipedia.org/wiki/Battle_of_Cape_St._Vincent_(1780)"}]}, {"year": "1786", "text": "Virginia enacts the Statute for Religious Freedom authored by <PERSON>.", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> enacts the <a href=\"https://wikipedia.org/wiki/Virginia_Statute_for_Religious_Freedom\" title=\"Virginia Statute for Religious Freedom\">Statute for Religious Freedom</a> authored by <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas <PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> enacts the <a href=\"https://wikipedia.org/wiki/Virginia_Statute_for_Religious_Freedom\" title=\"Virginia Statute for Religious Freedom\">Statute for Religious Freedom</a> authored by <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Jefferson\"><PERSON></a>.", "links": [{"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "Virginia Statute for Religious Freedom", "link": "https://wikipedia.org/wiki/Virginia_Statute_for_Religious_Freedom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "Peninsular War: The British defeat the French at the Battle of La Coruña.", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: The British defeat the French at the <a href=\"https://wikipedia.org/wiki/Battle_of_Corunna\" title=\"Battle of Corunna\">Battle of La Coruña</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: The British defeat the French at the <a href=\"https://wikipedia.org/wiki/Battle_of_Corunna\" title=\"Battle of Corunna\">Battle of La Coruña</a>.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "Battle of Corunna", "link": "https://wikipedia.org/wiki/Battle_of_Corunna"}]}, {"year": "1847", "text": "Westward expansion of the United States: <PERSON> is appointed Governor of the new California Territory.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Westward_expansion_of_the_United_States\" class=\"mw-redirect\" title=\"Westward expansion of the United States\">Westward expansion of the United States</a>: <a href=\"https://wikipedia.org/wiki/John_<PERSON>._Fr%C3%A9mont\" title=\"<PERSON>\"><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor</a> of the new <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> Territory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Westward_expansion_of_the_United_States\" class=\"mw-redirect\" title=\"Westward expansion of the United States\">Westward expansion of the United States</a>: <a href=\"https://wikipedia.org/wiki/John_<PERSON>._Fr%C3%A9mont\" title=\"<PERSON>\"><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor</a> of the new <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> Territory.", "links": [{"title": "Westward expansion of the United States", "link": "https://wikipedia.org/wiki/Westward_expansion_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>._Fr%C3%A9mont"}, {"title": "Governor of California", "link": "https://wikipedia.org/wiki/Governor_of_California"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "1862", "text": "Hartley Colliery disaster: Two hundred and four men and boys killed in a mining disaster, prompting a change in UK law which henceforth required all collieries to have at least two independent means of escape.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Hartley_Colliery_disaster\" title=\"Hartley Colliery disaster\">Hartley Colliery disaster</a>: Two hundred and four men and boys killed in a mining disaster, prompting a change in UK law which henceforth required all collieries to have at least two independent means of escape.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hartley_Colliery_disaster\" title=\"Hartley Colliery disaster\">Hartley Colliery disaster</a>: Two hundred and four men and boys killed in a mining disaster, prompting a change in UK law which henceforth required all collieries to have at least two independent means of escape.", "links": [{"title": "Hartley Colliery disaster", "link": "https://wikipedia.org/wiki/Hartley_Colliery_disaster"}]}, {"year": "1878", "text": "Russo-Turkish War (1877-78): Battle of Philippopolis: Captain <PERSON> with a squadron of Russian Imperial army dragoons liberates Plovdiv from Ottoman rule.", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1877%E2%80%9378)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1877-78)\">Russo-Turkish War (1877-78)</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Philippopolis_(1878)\" class=\"mw-redirect\" title=\"Battle of Philippopolis (1878)\">Battle of Philippopolis</a>: Captain <a href=\"https://wikipedia.org/wiki/Aleksandr_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a> with a <a href=\"https://wikipedia.org/wiki/Squadron_(army)\" title=\"Squadron (army)\">squadron</a> of <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Imperial</a> army <a href=\"https://wikipedia.org/wiki/Dragoon\" title=\"Dragoon\">dragoons</a> <a href=\"https://wikipedia.org/wiki/Liberation_of_Bulgaria\" title=\"Liberation of Bulgaria\">liberates Plovdiv</a> from <a href=\"https://wikipedia.org/wiki/Ottoman_Bulgaria\" title=\"Ottoman Bulgaria\">Ottoman rule</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1877%E2%80%9378)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1877-78)\">Russo-Turkish War (1877-78)</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Philippopolis_(1878)\" class=\"mw-redirect\" title=\"Battle of Philippopolis (1878)\">Battle of Philippopolis</a>: Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a> with a <a href=\"https://wikipedia.org/wiki/Squadron_(army)\" title=\"Squadron (army)\">squadron</a> of <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Imperial</a> army <a href=\"https://wikipedia.org/wiki/Dragoon\" title=\"Dragoon\">dragoons</a> <a href=\"https://wikipedia.org/wiki/Liberation_of_Bulgaria\" title=\"Liberation of Bulgaria\">liberates <PERSON>lovdiv</a> from <a href=\"https://wikipedia.org/wiki/Ottoman_Bulgaria\" title=\"Ottoman Bulgaria\">Ottoman rule</a>.", "links": [{"title": "Russo-Turkish War (1877-78)", "link": "https://wikipedia.org/wiki/Russo-Turkish_War_(1877%E2%80%9378)"}, {"title": "Battle of Philippopolis (1878)", "link": "https://wikipedia.org/wiki/Battle_of_Philippopolis_(1878)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Squadron (army)", "link": "https://wikipedia.org/wiki/Squadron_(army)"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Dragoon", "link": "https://wikipedia.org/wiki/Dragoon"}, {"title": "Liberation of Bulgaria", "link": "https://wikipedia.org/wiki/Liberation_of_Bulgaria"}, {"title": "Ottoman Bulgaria", "link": "https://wikipedia.org/wiki/Ottoman_Bulgaria"}]}, {"year": "1883", "text": "The Pendleton Civil Service Reform Act, establishing the United States Civil Service, is enacted by Congress.", "html": "1883 - The <a href=\"https://wikipedia.org/wiki/Pendleton_Civil_Service_Reform_Act\" title=\"Pendleton Civil Service Reform Act\">Pendleton Civil Service Reform Act</a>, establishing the <a href=\"https://wikipedia.org/wiki/United_States_Civil_Service_Commission\" title=\"United States Civil Service Commission\">United States Civil Service</a>, is enacted by Congress.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pendleton_Civil_Service_Reform_Act\" title=\"Pendleton Civil Service Reform Act\">Pendleton Civil Service Reform Act</a>, establishing the <a href=\"https://wikipedia.org/wiki/United_States_Civil_Service_Commission\" title=\"United States Civil Service Commission\">United States Civil Service</a>, is enacted by Congress.", "links": [{"title": "Pendleton Civil Service Reform Act", "link": "https://wikipedia.org/wiki/Pendleton_Civil_Service_Reform_Act"}, {"title": "United States Civil Service Commission", "link": "https://wikipedia.org/wiki/United_States_Civil_Service_Commission"}]}, {"year": "1900", "text": "The United States Senate accepts the Anglo-German treaty of 1899 in which the United Kingdom renounces its claims to the Samoan islands.", "html": "1900 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> accepts the <a href=\"https://wikipedia.org/wiki/Anglo-German_treaty_of_1899\" class=\"mw-redirect\" title=\"Anglo-German treaty of 1899\">Anglo-German treaty of 1899</a> in which the United Kingdom renounces its claims to the <a href=\"https://wikipedia.org/wiki/American_Samoa\" title=\"American Samoa\">Samoan islands</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> accepts the <a href=\"https://wikipedia.org/wiki/Anglo-German_treaty_of_1899\" class=\"mw-redirect\" title=\"Anglo-German treaty of 1899\">Anglo-German treaty of 1899</a> in which the United Kingdom renounces its claims to the <a href=\"https://wikipedia.org/wiki/American_Samoa\" title=\"American Samoa\">Samoan islands</a>.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "Anglo-German treaty of 1899", "link": "https://wikipedia.org/wiki/Anglo-German_treaty_of_1899"}, {"title": "American Samoa", "link": "https://wikipedia.org/wiki/American_Samoa"}]}, {"year": "1909", "text": "<PERSON>'s expedition finds the magnetic South Pole.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s expedition finds the magnetic <a href=\"https://wikipedia.org/wiki/South_Pole\" title=\"South Pole\">South Pole</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s expedition finds the magnetic <a href=\"https://wikipedia.org/wiki/South_Pole\" title=\"South Pole\">South Pole</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "South Pole", "link": "https://wikipedia.org/wiki/South_Pole"}]}, {"year": "1913", "text": "Indian mathematician <PERSON><PERSON><PERSON><PERSON> writes his first letter to <PERSON><PERSON> <PERSON><PERSON> at Cambridge, stating without proof various formulae involving integrals, infinite series, and continued fractions, beginning a long correspondence between the two as well as widespread recognition of <PERSON><PERSON><PERSON>'s results.", "html": "1913 - Indian mathematician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> writes his first letter to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/University_of_Cambridge\" title=\"University of Cambridge\">Cambridge</a>, stating without proof various formulae involving <a href=\"https://wikipedia.org/wiki/Integral\" title=\"Integral\">integrals</a>, <a href=\"https://wikipedia.org/wiki/Infinite_series\" class=\"mw-redirect\" title=\"Infinite series\">infinite series</a>, and <a href=\"https://wikipedia.org/wiki/Continued_fraction\" title=\"Continued fraction\">continued fractions</a>, beginning a long correspondence between the two as well as widespread recognition of <PERSON><PERSON><PERSON>'s results.", "no_year_html": "Indian mathematician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> writes his first letter to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/University_of_Cambridge\" title=\"University of Cambridge\">Cambridge</a>, stating without proof various formulae involving <a href=\"https://wikipedia.org/wiki/Integral\" title=\"Integral\">integrals</a>, <a href=\"https://wikipedia.org/wiki/Infinite_series\" class=\"mw-redirect\" title=\"Infinite series\">infinite series</a>, and <a href=\"https://wikipedia.org/wiki/Continued_fraction\" title=\"Continued fraction\">continued fractions</a>, beginning a long correspondence between the two as well as widespread recognition of <PERSON><PERSON><PERSON>'s results.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "University of Cambridge", "link": "https://wikipedia.org/wiki/University_of_Cambridge"}, {"title": "Integral", "link": "https://wikipedia.org/wiki/Integral"}, {"title": "Infinite series", "link": "https://wikipedia.org/wiki/Infinite_series"}, {"title": "Continued fraction", "link": "https://wikipedia.org/wiki/Continued_fraction"}]}, {"year": "1919", "text": "Nebraska becomes the 36th state to approve the Eighteenth Amendment to the United States Constitution. With the necessary three-quarters of the states approving the amendment, Prohibition is constitutionally mandated in the United States one year later.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a> becomes the 36th state to approve the <a href=\"https://wikipedia.org/wiki/Eighteenth_Amendment_to_the_United_States_Constitution\" title=\"Eighteenth Amendment to the United States Constitution\">Eighteenth Amendment to the United States Constitution</a>. With the necessary three-quarters of the states approving the amendment, <a href=\"https://wikipedia.org/wiki/Prohibition_in_the_United_States\" title=\"Prohibition in the United States\">Prohibition</a> is constitutionally mandated in the United States one year later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a> becomes the 36th state to approve the <a href=\"https://wikipedia.org/wiki/Eighteenth_Amendment_to_the_United_States_Constitution\" title=\"Eighteenth Amendment to the United States Constitution\">Eighteenth Amendment to the United States Constitution</a>. With the necessary three-quarters of the states approving the amendment, <a href=\"https://wikipedia.org/wiki/Prohibition_in_the_United_States\" title=\"Prohibition in the United States\">Prohibition</a> is constitutionally mandated in the United States one year later.", "links": [{"title": "Nebraska", "link": "https://wikipedia.org/wiki/Nebraska"}, {"title": "Eighteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Eighteenth_Amendment_to_the_United_States_Constitution"}, {"title": "Prohibition in the United States", "link": "https://wikipedia.org/wiki/Prohibition_in_the_United_States"}]}, {"year": "1920", "text": "The League of Nations holds its first council meeting in Paris, France.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> holds its first council meeting in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris, France</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> holds its first council meeting in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris, France</a>.", "links": [{"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "1921", "text": "The Marxist Left in Slovakia and the Transcarpathian Ukraine holds its founding congress in Ľubochňa.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Marxist_Left_in_Slovakia_and_the_Transcarpathian_Ukraine\" title=\"Marxist Left in Slovakia and the Transcarpathian Ukraine\">Marxist Left in Slovakia and the Transcarpathian Ukraine</a> holds its founding congress in <a href=\"https://wikipedia.org/wiki/%C4%BDuboch%C5%88a\" title=\"Ľubochňa\"><PERSON>ubochňa</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Marxist_Left_in_Slovakia_and_the_Transcarpathian_Ukraine\" title=\"Marxist Left in Slovakia and the Transcarpathian Ukraine\">Marxist Left in Slovakia and the Transcarpathian Ukraine</a> holds its founding congress in <a href=\"https://wikipedia.org/wiki/%C4%BDuboch%C5%88a\" title=\"Ľubochňa\"><PERSON><PERSON><PERSON>ňa</a>.", "links": [{"title": "Marxist Left in Slovakia and the Transcarpathian Ukraine", "link": "https://wikipedia.org/wiki/Marxist_Left_in_Slovakia_and_the_Transcarpathian_Ukraine"}, {"title": "Ľubochňa", "link": "https://wikipedia.org/wiki/%C4%BDuboch%C5%88a"}]}, {"year": "1942", "text": "The Holocaust: Nazi Germany begins deporting Jews from the Łódź Ghetto to Chełmno extermination camp.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: Nazi Germany begins deporting Jews from the <a href=\"https://wikipedia.org/wiki/%C5%81%C3%B3d%C5%BA_Ghetto\" title=\"Łódź Ghetto\">Łódź Ghetto</a> to <a href=\"https://wikipedia.org/wiki/Che%C5%82mno_extermination_camp\" title=\"Chełmno extermination camp\">Chełmno extermination camp</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: Nazi Germany begins deporting Jews from the <a href=\"https://wikipedia.org/wiki/%C5%81%C3%B3d%C5%BA_Ghetto\" title=\"Łódź Ghetto\">Łódź Ghetto</a> to <a href=\"https://wikipedia.org/wiki/Che%C5%82mno_extermination_camp\" title=\"Chełmno extermination camp\">Chełmno extermination camp</a>.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Łódź Ghetto", "link": "https://wikipedia.org/wiki/%C5%81%C3%B3d%C5%BA_Ghetto"}, {"title": "Chełmno extermination camp", "link": "https://wikipedia.org/wiki/Che%C5%82mno_extermination_camp"}]}, {"year": "1942", "text": "Crash of TWA Flight 3, killing all 22 aboard, including film star <PERSON>.", "html": "1942 - Crash of <a href=\"https://wikipedia.org/wiki/TWA_Flight_3\" title=\"TWA Flight 3\">TWA Flight 3</a>, killing all 22 aboard, including film star <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Crash of <a href=\"https://wikipedia.org/wiki/TWA_Flight_3\" title=\"TWA Flight 3\">TWA Flight 3</a>, killing all 22 aboard, including film star <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "TWA Flight 3", "link": "https://wikipedia.org/wiki/TWA_Flight_3"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "World War II: <PERSON> moves into his underground bunker, the so-called Führerbunker.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> moves into his underground bunker, the so-called <a href=\"https://wikipedia.org/wiki/F%C3%BChrerbunker\" title=\"Führerbunker\">Führerbunker</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hitler\"><PERSON></a> moves into his underground bunker, the so-called <a href=\"https://wikipedia.org/wiki/F%C3%BChrerbunker\" title=\"Führerbunker\">Führerbunker</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Führerbunker", "link": "https://wikipedia.org/wiki/F%C3%BChrerbunker"}]}, {"year": "1959", "text": "Austral Líneas Aéreas Flight 205 crashes into the Atlantic Ocean near Astor Piazzolla International Airport in Mar del Plata, Argentina, killing 51.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Austral_L%C3%ADneas_A%C3%A9reas_Flight_205\" title=\"Austral Líneas Aéreas Flight 205\">Austral Líneas Aéreas Flight 205</a> crashes into the Atlantic Ocean near <a href=\"https://wikipedia.org/wiki/Astor_Piazzolla_International_Airport\" title=\"Astor Piazzolla International Airport\">Astor Piazzolla International Airport</a> in <a href=\"https://wikipedia.org/wiki/Mar_del_Plata\" title=\"Mar del Plata\">Mar del Plata</a>, Argentina, killing 51.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austral_L%C3%ADneas_A%C3%A9reas_Flight_205\" title=\"Austral Líneas Aéreas Flight 205\">Austral Líneas Aéreas Flight 205</a> crashes into the Atlantic Ocean near <a href=\"https://wikipedia.org/wiki/Astor_Piazzolla_International_Airport\" title=\"Astor Piazzolla International Airport\">Astor Piazzolla International Airport</a> in <a href=\"https://wikipedia.org/wiki/Mar_del_Plata\" title=\"Mar del Plata\">Mar del Plata</a>, Argentina, killing 51.", "links": [{"title": "Austral Líneas Aéreas Flight 205", "link": "https://wikipedia.org/wiki/Austral_L%C3%ADneas_A%C3%A9reas_Flight_205"}, {"title": "Astor Piazzolla International Airport", "link": "https://wikipedia.org/wiki/Astor_Piazzolla_International_Airport"}, {"title": "Mar del Plata", "link": "https://wikipedia.org/wiki/Mar_del_Plata"}]}, {"year": "1969", "text": "Czech student <PERSON> commits suicide by self-immolation in Prague, Czechoslovakia, in protest against the Soviets' crushing of the Prague Spring the year before.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Czechs\" title=\"Czechs\">Czech</a> student <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> commits suicide by <a href=\"https://wikipedia.org/wiki/Self-immolation\" title=\"Self-immolation\">self-immolation</a> in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague, Czechoslovakia</a>, in protest against the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviets</a>' crushing of the <a href=\"https://wikipedia.org/wiki/Prague_Spring\" title=\"Prague Spring\">Prague Spring</a> the year before.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Czechs\" title=\"Czechs\">Czech</a> student <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> commits suicide by <a href=\"https://wikipedia.org/wiki/Self-immolation\" title=\"Self-immolation\">self-immolation</a> in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague, Czechoslovakia</a>, in protest against the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviets</a>' crushing of the <a href=\"https://wikipedia.org/wiki/Prague_Spring\" title=\"Prague Spring\">Prague Spring</a> the year before.", "links": [{"title": "Czechs", "link": "https://wikipedia.org/wiki/Czechs"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Self-immolation", "link": "https://wikipedia.org/wiki/Self-immolation"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Prague Spring", "link": "https://wikipedia.org/wiki/Prague_Spring"}]}, {"year": "1969", "text": "Space Race: Soviet spacecraft Soyuz 4 and Soyuz 5 perform the first-ever docking of crewed spacecraft in orbit, the first-ever transfer of crew from one space vehicle to another, and the only time such a transfer was accomplished with a space walk.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Space_Race\" title=\"Space Race\">Space Race</a>: Soviet spacecraft <i><a href=\"https://wikipedia.org/wiki/Soyuz_4\" title=\"Soyuz 4\">Soyuz 4</a></i> and <i><a href=\"https://wikipedia.org/wiki/Soyuz_5\" title=\"Soyuz 5\">Soyuz 5</a></i> perform the first-ever docking of crewed spacecraft in <a href=\"https://wikipedia.org/wiki/Low_Earth_orbit\" title=\"Low Earth orbit\">orbit</a>, the first-ever transfer of crew from one space vehicle to another, and the only time such a transfer was accomplished with a <a href=\"https://wikipedia.org/wiki/Extravehicular_activity\" title=\"Extravehicular activity\">space walk</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Race\" title=\"Space Race\">Space Race</a>: Soviet spacecraft <i><a href=\"https://wikipedia.org/wiki/Soyuz_4\" title=\"Soyuz 4\">Soyuz 4</a></i> and <i><a href=\"https://wikipedia.org/wiki/Soyuz_5\" title=\"Soyuz 5\">Soyuz 5</a></i> perform the first-ever docking of crewed spacecraft in <a href=\"https://wikipedia.org/wiki/Low_Earth_orbit\" title=\"Low Earth orbit\">orbit</a>, the first-ever transfer of crew from one space vehicle to another, and the only time such a transfer was accomplished with a <a href=\"https://wikipedia.org/wiki/Extravehicular_activity\" title=\"Extravehicular activity\">space walk</a>.", "links": [{"title": "Space Race", "link": "https://wikipedia.org/wiki/Space_Race"}, {"title": "Soyuz 4", "link": "https://wikipedia.org/wiki/Soyuz_4"}, {"title": "Soyuz 5", "link": "https://wikipedia.org/wiki/Soyuz_5"}, {"title": "Low Earth orbit", "link": "https://wikipedia.org/wiki/Low_Earth_orbit"}, {"title": "Extravehicular activity", "link": "https://wikipedia.org/wiki/Extravehicular_activity"}]}, {"year": "1979", "text": "Iranian Revolution: The last Iranian Shah flees Iran with his family for good and relocates to Egypt.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Iranian_Revolution\" class=\"mw-redirect\" title=\"Iranian Revolution\">Iranian Revolution</a>: The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">last Iranian Shah</a> flees <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> with his family for good and relocates to <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iranian_Revolution\" class=\"mw-redirect\" title=\"Iranian Revolution\">Iranian Revolution</a>: The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">last Iranian Shah</a> flees <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> with his family for good and relocates to <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "Iranian Revolution", "link": "https://wikipedia.org/wiki/Iranian_Revolution"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1983", "text": "Turkish Airlines Flight 158 crashes at Ankara Esenboğa Airport in Ankara, Turkey, killing 47 and injuring 20.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_158\" title=\"Turkish Airlines Flight 158\">Turkish Airlines Flight 158</a> crashes at <a href=\"https://wikipedia.org/wiki/Ankara_Esenbo%C4%9Fa_Airport\" title=\"Ankara Esenboğa Airport\">Ankara Esenboğa Airport</a> in <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a>, Turkey, killing 47 and injuring 20.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_158\" title=\"Turkish Airlines Flight 158\">Turkish Airlines Flight 158</a> crashes at <a href=\"https://wikipedia.org/wiki/Ankara_Esenbo%C4%9Fa_Airport\" title=\"Ankara Esenboğa Airport\">Ankara Esenboğa Airport</a> in <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a>, Turkey, killing 47 and injuring 20.", "links": [{"title": "Turkish Airlines Flight 158", "link": "https://wikipedia.org/wiki/Turkish_Airlines_Flight_158"}, {"title": "Ankara Esenboğa Airport", "link": "https://wikipedia.org/wiki/Ankara_Esenbo%C4%9Fa_Airport"}, {"title": "Ankara", "link": "https://wikipedia.org/wiki/Ankara"}]}, {"year": "1991", "text": "Coalition Forces go to war with Iraq, beginning the Gulf War.", "html": "1991 - Coalition Forces go to war with <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, beginning the <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>.", "no_year_html": "Coalition Forces go to war with <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, beginning the <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>.", "links": [{"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Gulf War", "link": "https://wikipedia.org/wiki/Gulf_War"}]}, {"year": "1992", "text": "El Salvador officials and rebel leaders sign the Chapultepec Peace Accords in Mexico City, Mexico ending the 12-year Salvadoran Civil War that claimed at least 75,000 lives.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a> officials and rebel leaders sign the <a href=\"https://wikipedia.org/wiki/Chapultepec_Peace_Accords\" title=\"Chapultepec Peace Accords\">Chapultepec Peace Accords</a> in <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City, Mexico</a> ending the 12-year <a href=\"https://wikipedia.org/wiki/Salvadoran_Civil_War\" title=\"Salvadoran Civil War\">Salvadoran Civil War</a> that claimed at least 75,000 lives.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a> officials and rebel leaders sign the <a href=\"https://wikipedia.org/wiki/Chapultepec_Peace_Accords\" title=\"Chapultepec Peace Accords\">Chapultepec Peace Accords</a> in <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City, Mexico</a> ending the 12-year <a href=\"https://wikipedia.org/wiki/Salvadoran_Civil_War\" title=\"Salvadoran Civil War\">Salvadoran Civil War</a> that claimed at least 75,000 lives.", "links": [{"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}, {"title": "Chapultepec Peace Accords", "link": "https://wikipedia.org/wiki/Chapultepec_Peace_Accords"}, {"title": "Mexico City", "link": "https://wikipedia.org/wiki/Mexico_City"}, {"title": "Salvadoran Civil War", "link": "https://wikipedia.org/wiki/Salvadoran_Civil_War"}]}, {"year": "1995", "text": "An avalanche hits the Icelandic village Súðavík, destroying 25 homes and burying 26 people, 14 of whom died.", "html": "1995 - An <a href=\"https://wikipedia.org/wiki/1995_S%C3%BA%C3%B0av%C3%ADk_avalanche\" title=\"1995 Súðavík avalanche\">avalanche</a> hits the Icelandic village <a href=\"https://wikipedia.org/wiki/S%C3%BA%C3%B0av%C3%ADk\" title=\"Súðavík\">Súðavík</a>, destroying 25 homes and burying 26 people, 14 of whom died.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1995_S%C3%BA%C3%B0av%C3%ADk_avalanche\" title=\"1995 Súðavík avalanche\">avalanche</a> hits the Icelandic village <a href=\"https://wikipedia.org/wiki/S%C3%BA%C3%B0av%C3%ADk\" title=\"Súðavík\">Súðavík</a>, destroying 25 homes and burying 26 people, 14 of whom died.", "links": [{"title": "1995 Súðavík avalanche", "link": "https://wikipedia.org/wiki/1995_S%C3%BA%C3%B0av%C3%ADk_avalanche"}, {"title": "Súðavík", "link": "https://wikipedia.org/wiki/S%C3%BA%C3%B0av%C3%ADk"}]}, {"year": "2001", "text": "Second Congo War: Congolese President <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is assassinated by one of his own bodyguards in Kinshasa.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Second_Congo_War\" title=\"Second Congo War\">Second Congo War</a>: <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Congolese</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9sir%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>-<PERSON>%C3%A9sir%C3%A<PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\">assassinated by one of his own bodyguards</a> in Kinshasa.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Congo_War\" title=\"Second Congo War\">Second Congo War</a>: <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Congolese</a> President <a href=\"https://wikipedia.org/wiki/Laurent-D%C3%A9sir%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>-<PERSON>%C3%A9sir%C3%A<PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\">assassinated by one of his own bodyguards</a> in Kinshasa.", "links": [{"title": "Second Congo War", "link": "https://wikipedia.org/wiki/Second_Congo_War"}, {"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laurent-D%C3%A9sir%C3%A9_<PERSON><PERSON>a"}, {"title": "Assassination of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>-D%C3%A9sir%C3%A9_<PERSON><PERSON>a"}]}, {"year": "2001", "text": "US President <PERSON> awards former President <PERSON> a posthumous Medal of Honor for his service in the Spanish-American War.", "html": "2001 - US President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> awards former President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> a posthumous <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> for his service in the <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>.", "no_year_html": "US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> awards former President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> a posthumous <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> for his service in the <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}, {"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}]}, {"year": "2002", "text": "War in Afghanistan: The UN Security Council unanimously establishes an arms embargo and the freezing of assets of <PERSON><PERSON><PERSON> bin <PERSON>, al-Qaeda, and the remaining members of the Taliban.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)\" title=\"War in Afghanistan (2001-2021)\">War in Afghanistan</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">UN Security Council</a> unanimously establishes an arms embargo and the freezing of assets of <a href=\"https://wikipedia.org/wiki/Osama_bin_Laden\" title=\"<PERSON><PERSON><PERSON> bin Laden\"><PERSON><PERSON><PERSON> bin <PERSON>den</a>, <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a>, and the remaining members of the <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)\" title=\"War in Afghanistan (2001-2021)\">War in Afghanistan</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">UN Security Council</a> unanimously establishes an arms embargo and the freezing of assets of <a href=\"https://wikipedia.org/wiki/Osama_bin_Laden\" title=\"Osa<PERSON> bin Laden\"><PERSON><PERSON><PERSON> bin <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a>, and the remaining members of the <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a>.", "links": [{"title": "War in Afghanistan (2001-2021)", "link": "https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)"}, {"title": "United Nations Security Council", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>den"}, {"title": "Al-Qaeda", "link": "https://wikipedia.org/wiki/Al-Qaeda"}, {"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}]}, {"year": "2003", "text": "The Space Shuttle Columbia takes off for mission STS-107 which would be its final one. Columbia disintegrated 16 days later on re-entry.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> takes off for mission <a href=\"https://wikipedia.org/wiki/STS-107\" title=\"STS-107\">STS-107</a> which would be its final one. <i>Columbia</i> <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia_disaster\" title=\"Space Shuttle Columbia disaster\">disintegrated 16 days later on re-entry</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> takes off for mission <a href=\"https://wikipedia.org/wiki/STS-107\" title=\"STS-107\">STS-107</a> which would be its final one. <i>Columbia</i> <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia_disaster\" title=\"Space Shuttle Columbia disaster\">disintegrated 16 days later on re-entry</a>.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-107", "link": "https://wikipedia.org/wiki/STS-107"}, {"title": "Space Shuttle Columbia disaster", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia_disaster"}]}, {"year": "2006", "text": "<PERSON> is sworn in as Liberia's new president. She becomes Africa's first female elected head of state.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as <a href=\"https://wikipedia.org/wiki/Liberia\" title=\"Liberia\">Liberia</a>'s new <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">president</a>. She becomes Africa's first female elected head of state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as <a href=\"https://wikipedia.org/wiki/Liberia\" title=\"Liberia\">Liberia</a>'s new <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">president</a>. She becomes Africa's first female elected head of state.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Liberia", "link": "https://wikipedia.org/wiki/Liberia"}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}]}, {"year": "2011", "text": "Syrian civil war: The Movement for a Democratic Society (TEV-DEM) is established with the stated goal of re-organizing Syria along the lines of democratic confederalism.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Movement_for_a_Democratic_Society\" title=\"Movement for a Democratic Society\">Movement for a Democratic Society</a> (TEV-DEM) is established with the stated goal of re-organizing <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> along the lines of <a href=\"https://wikipedia.org/wiki/Democratic_confederalism\" title=\"Democratic confederalism\">democratic confederalism</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Movement_for_a_Democratic_Society\" title=\"Movement for a Democratic Society\">Movement for a Democratic Society</a> (TEV-DEM) is established with the stated goal of re-organizing <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> along the lines of <a href=\"https://wikipedia.org/wiki/Democratic_confederalism\" title=\"Democratic confederalism\">democratic confederalism</a>.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Movement for a Democratic Society", "link": "https://wikipedia.org/wiki/Movement_for_a_Democratic_Society"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "Democratic confederalism", "link": "https://wikipedia.org/wiki/Democratic_confederalism"}]}, {"year": "2012", "text": "The Mali War begins when Tuareg militias start fighting the Malian government for independence.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/Mali_War\" title=\"Mali War\">Mali War</a> begins when <a href=\"https://wikipedia.org/wiki/National_Movement_for_the_Liberation_of_Azawad\" title=\"National Movement for the Liberation of Azawad\">Tuareg militias</a> start fighting the Malian government for independence.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mali_War\" title=\"Mali War\">Mali War</a> begins when <a href=\"https://wikipedia.org/wiki/National_Movement_for_the_Liberation_of_Azawad\" title=\"National Movement for the Liberation of Azawad\">Tuareg militias</a> start fighting the Malian government for independence.", "links": [{"title": "Mali War", "link": "https://wikipedia.org/wiki/Mali_War"}, {"title": "National Movement for the Liberation of Azawad", "link": "https://wikipedia.org/wiki/National_Movement_for_the_Liberation_of_Azawad"}]}, {"year": "2016", "text": "Thirty-three out of 126 freed hostages are injured and 23 killed in terrorist attacks in Ouagadougou, Burkina Faso on a hotel and a nearby restaurant.", "html": "2016 - Thirty-three out of 126 freed hostages are injured and 23 killed in terrorist <a href=\"https://wikipedia.org/wiki/2016_Ouagadougou_attack\" class=\"mw-redirect\" title=\"2016 Ouagadougou attack\">attacks in Ouagadougou, Burkina Faso</a> on a hotel and a nearby restaurant.", "no_year_html": "Thirty-three out of 126 freed hostages are injured and 23 killed in terrorist <a href=\"https://wikipedia.org/wiki/2016_Ouagadougou_attack\" class=\"mw-redirect\" title=\"2016 Ouagadougou attack\">attacks in Ouagadougou, Burkina Faso</a> on a hotel and a nearby restaurant.", "links": [{"title": "2016 Ouagadougou attack", "link": "https://wikipedia.org/wiki/2016_Ouagadougou_attack"}]}, {"year": "2017", "text": "Turkish Airlines Flight 6491 crashes into a residential area near Manas International Airport in Kyrgyzstan, killing 39 people.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_6491\" title=\"Turkish Airlines Flight 6491\">Turkish Airlines Flight 6491</a> crashes into a residential area near <a href=\"https://wikipedia.org/wiki/Manas_International_Airport\" title=\"Manas International Airport\">Manas International Airport</a> in Kyrgyzstan, killing 39 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_6491\" title=\"Turkish Airlines Flight 6491\">Turkish Airlines Flight 6491</a> crashes into a residential area near <a href=\"https://wikipedia.org/wiki/Manas_International_Airport\" title=\"Manas International Airport\">Manas International Airport</a> in Kyrgyzstan, killing 39 people.", "links": [{"title": "Turkish Airlines Flight 6491", "link": "https://wikipedia.org/wiki/Turkish_Airlines_Flight_6491"}, {"title": "Manas International Airport", "link": "https://wikipedia.org/wiki/Manas_International_Airport"}]}, {"year": "2018", "text": "Myanmar police open fire on a group of ethnic Rakhine protesters, killing seven and wounding twelve.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a> <a href=\"https://wikipedia.org/wiki/Myanmar_Police_Force\" title=\"Myanmar Police Force\">police</a> <a href=\"https://wikipedia.org/wiki/Mrauk_U_riot\" title=\"Mrauk U riot\">open fire</a> on a group of ethnic <a href=\"https://wikipedia.org/wiki/Rakhine_people\" title=\"Rakhine people\">Rakhine</a> protesters, killing seven and wounding twelve.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a> <a href=\"https://wikipedia.org/wiki/Myanmar_Police_Force\" title=\"Myanmar Police Force\">police</a> <a href=\"https://wikipedia.org/wiki/Mrauk_U_riot\" title=\"Mrauk U riot\">open fire</a> on a group of ethnic <a href=\"https://wikipedia.org/wiki/Rakhine_people\" title=\"Rakhine people\">Rakhine</a> protesters, killing seven and wounding twelve.", "links": [{"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}, {"title": "Myanmar Police Force", "link": "https://wikipedia.org/wiki/Myanmar_Police_Force"}, {"title": "Mrauk U riot", "link": "https://wikipedia.org/wiki/Mrauk_U_riot"}, {"title": "Rakhine people", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_people"}]}, {"year": "2020", "text": "The first impeachment of <PERSON> formally moves into its trial phase in the United States Senate.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/First_impeachment_of_<PERSON>_<PERSON>\" title=\"First impeachment of <PERSON>\">first impeachment of <PERSON></a> formally moves into its trial phase in the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_impeachment_of_<PERSON>_<PERSON>\" title=\"First impeachment of <PERSON>\">first impeachment of <PERSON></a> formally moves into its trial phase in the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a>.", "links": [{"title": "First impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/First_impeachment_of_<PERSON>_<PERSON>"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "2020", "text": "The United States Senate ratifies the United States-Mexico-Canada Agreement as a replacement for NAFTA.", "html": "2020 - The United States Senate ratifies the <a href=\"https://wikipedia.org/wiki/United_States%E2%80%93Mexico%E2%80%93Canada_Agreement\" title=\"United States-Mexico-Canada Agreement\">United States-Mexico-Canada Agreement</a> as a replacement for <a href=\"https://wikipedia.org/wiki/North_American_Free_Trade_Agreement\" title=\"North American Free Trade Agreement\">NAFTA</a>.", "no_year_html": "The United States Senate ratifies the <a href=\"https://wikipedia.org/wiki/United_States%E2%80%93Mexico%E2%80%93Canada_Agreement\" title=\"United States-Mexico-Canada Agreement\">United States-Mexico-Canada Agreement</a> as a replacement for <a href=\"https://wikipedia.org/wiki/North_American_Free_Trade_Agreement\" title=\"North American Free Trade Agreement\">NAFTA</a>.", "links": [{"title": "United States-Mexico-Canada Agreement", "link": "https://wikipedia.org/wiki/United_States%E2%80%93Mexico%E2%80%93Canada_Agreement"}, {"title": "North American Free Trade Agreement", "link": "https://wikipedia.org/wiki/North_American_Free_Trade_Agreement"}]}], "Births": [{"year": "972", "text": "<PERSON><PERSON>, emperor of the Liao Dynasty (d. 1031)", "html": "972 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON><PERSON> of Liao\"><PERSON><PERSON></a>, emperor of the Liao Dynasty (d. 1031)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON><PERSON> of Liao\"><PERSON><PERSON></a>, emperor of the Liao Dynasty (d. 1031)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Liao", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao"}]}, {"year": "1093", "text": "<PERSON>, son of Byzantine emperor <PERSON><PERSON> (d. 1152)", "html": "1093 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(son_of_<PERSON><PERSON>_<PERSON>)\" title=\"<PERSON> (son of <PERSON><PERSON>)\"><PERSON></a>, son of Byzantine emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I_Komnen<PERSON>\" title=\"<PERSON><PERSON>nen<PERSON>\"><PERSON><PERSON></a> (d. 1152)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(son_of_<PERSON><PERSON>_<PERSON>)\" title=\"<PERSON> (son of <PERSON><PERSON>)\"><PERSON></a>, son of Byzantine emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1152)", "links": [{"title": "<PERSON> (son of <PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(son_of_<PERSON><PERSON>_<PERSON>)"}, {"title": "Alexios I Komnenos", "link": "https://wikipedia.org/wiki/Alexios_I_Komnenos"}]}, {"year": "1245", "text": "<PERSON>, English politician, Lord Warden of the Cinque Ports (d. 1296)", "html": "1245 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (d. 1296)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (d. 1296)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1362", "text": "<PERSON>, duke of Ireland (d. 1392)", "html": "1362 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Ireland\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Ireland\"><PERSON></a>, duke of Ireland (d. 1392)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Ireland\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Ireland\"><PERSON></a>, duke of Ireland (d. 1392)", "links": [{"title": "<PERSON>, Duke of Ireland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Ireland"}]}, {"year": "1409", "text": "<PERSON> of Anjou, king of Naples (d. 1480)", "html": "1409 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a>, king of Naples (d. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a>, king of Naples (d. 1480)", "links": [{"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/Ren%C3%A9_of_Anjou"}]}, {"year": "1477", "text": "<PERSON>, German astronomer and cartographer (d. 1547)", "html": "1477 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ner\" title=\"<PERSON>\"><PERSON></a>, German astronomer and cartographer (d. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ner\" title=\"<PERSON>\"><PERSON></a>, German astronomer and cartographer (d. 1547)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Johannes_Sch%C3%B6ner"}]}, {"year": "1501", "text": "<PERSON>, confidant of <PERSON> of England (d. 1559)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, confidant of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\">Henry VIII of England</a> (d. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, confidant of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\">Henry VIII of England</a> (d. 1559)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1516", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Burma (d. 1581)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/Bayinnaung\" title=\"Bayinnaung\"><PERSON><PERSON><PERSON><PERSON></a>, king of Burma (d. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bayinnaung\" title=\"Bayinnaung\"><PERSON><PERSON><PERSON><PERSON></a>, king of Burma (d. 1581)", "links": [{"title": "Bayinnaung", "link": "https://wikipedia.org/wiki/Bayinnaung"}]}, {"year": "1558", "text": "<PERSON><PERSON> of Baden, <PERSON><PERSON><PERSON><PERSON> of Baden by birth, Duchess of Jülich-Cleves-Berg by marriage (d. 1597)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Baden\" title=\"<PERSON><PERSON> of Baden\"><PERSON><PERSON> of Baden</a>, <PERSON><PERSON><PERSON><PERSON> of Baden by birth, Duchess of Jülich-Cleves-Berg by marriage (d. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Baden\" title=\"<PERSON><PERSON> of Baden\"><PERSON><PERSON> of Baden</a>, <PERSON><PERSON><PERSON><PERSON> of Baden by birth, Duchess of Jülich-Cleves-Berg by marriage (d. 1597)", "links": [{"title": "<PERSON><PERSON> of Baden", "link": "https://wikipedia.org/wiki/Jakobea_of_Baden"}]}, {"year": "1616", "text": "<PERSON>, duke of Beaufort (d. 1669)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_de_Vend%C3%B4me,_Duke_<PERSON>_Beaufort\" class=\"mw-redirect\" title=\"<PERSON>, Duke <PERSON> Beaufort\"><PERSON></a>, duke of Beaufort (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_de_Vend%C3%B4me,_Duke_<PERSON>_Beaufort\" class=\"mw-redirect\" title=\"<PERSON>, Duke <PERSON>\"><PERSON></a>, duke of Beaufort (d. 1669)", "links": [{"title": "<PERSON>, Duke of Beaufort", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_de_Vend%C3%B4me,_<PERSON>_of_Beaufort"}]}, {"year": "1626", "text": "<PERSON>, Belgian painter and educator (d. 1699)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter and educator (d. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter and educator (d. 1699)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1630", "text": "<PERSON>, Sikh Guru (d. 1661)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>, Sikh Guru (d. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>, Sikh Guru (d. 1661)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1634", "text": "<PERSON><PERSON><PERSON>, Norwegian author and poet (d. 1716)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian author and poet (d. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian author and poet (d. 1716)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1653", "text": "<PERSON>, Swiss anatomist (d. 1727)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss anatomist (d. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss anatomist (d. 1727)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1675", "text": "<PERSON>, duc de <PERSON>, French soldier and diplomat (d. 1755)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_duc_de_<PERSON>\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc de <PERSON></a>, French soldier and diplomat (d. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_duc_de_<PERSON>\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc de <PERSON></a>, French soldier and diplomat (d. 1755)", "links": [{"title": "<PERSON>, duc de <PERSON>-Simon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>"}]}, {"year": "1691", "text": "<PERSON>, Belgian sculptor and educator (d. 1781)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian sculptor and educator (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian sculptor and educator (d. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1728", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer and educator (d. 1800)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Piccinni\" title=\"<PERSON><PERSON><PERSON><PERSON>cci<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and educator (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Piccinni\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and educator (d. 1800)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_Piccinni"}]}, {"year": "1749", "text": "<PERSON><PERSON><PERSON>, Italian poet and playwright (d. 1803)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and playwright (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>itt<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and playwright (d. 1803)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, English admiral and politician, third Commodore-Governor of Newfoundland (d. 1834)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, third <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore-Governor of Newfoundland</a> (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, third <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore-Governor of Newfoundland</a> (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of lieutenant governors of Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador"}]}, {"year": "1807", "text": "<PERSON>, American admiral (d. 1877)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, American lawyer, general, and scholar (d. 1872)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, general, and scholar (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, general, and scholar (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, American general and politician, 14th Vice President of the United States (d. 1875)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 14th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 14th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1834", "text": "<PERSON>, American lawyer and politician, 13th United States Assistant Secretary of State (d. 1906)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Assistant Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State"}]}, {"year": "1836", "text": "<PERSON> of the Two Sicilies (d. 1894)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies\" title=\"<PERSON> II of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies\" title=\"<PERSON> II of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (d. 1894)", "links": [{"title": "<PERSON> of the Two Sicilies", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies"}]}, {"year": "1838", "text": "<PERSON>, German philosopher and psychologist (d. 1917)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and psychologist (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and psychologist (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, Albanian politician and statesman, first prime minister of Albania (d. 1919)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian politician and statesman, first prime minister of Albania (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian politician and statesman, first prime minister of Albania (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, English-New Zealand politician, 16th Prime Minister of New Zealand (d. 1936)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1853", "text": "<PERSON>-<PERSON>, English actor and manager (d. 1937)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and manager (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and manager (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, Greek-English general (d. 1947)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek-English general (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek-English general (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, French businessman, co-founded the Michelin Tyre Company (d. 1931)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Michelin\" title=\"<PERSON>\"><PERSON></a>, French businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Michelin\" title=\"<PERSON>in\">Michelin Tyre Company</a> (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Michelin\" title=\"<PERSON>\"><PERSON></a>, French businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Michelin\" title=\"<PERSON>in\">Michelin Tyre Company</a> (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>in"}, {"title": "<PERSON>in", "link": "https://wikipedia.org/wiki/<PERSON>in"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON>, Estonian businessman and politician, State Elder of Estonia (d. 1942)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian businessman and politician, <a href=\"https://wikipedia.org/wiki/State_Elder_of_Estonia\" title=\"State Elder of Estonia\">State Elder of Estonia</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian businessman and politician, <a href=\"https://wikipedia.org/wiki/State_Elder_of_Estonia\" title=\"State Elder of Estonia\">State Elder of Estonia</a> (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "State Elder of Estonia", "link": "https://wikipedia.org/wiki/State_Elder_of_Estonia"}]}, {"year": "1872", "text": "<PERSON>, French organist, composer, and conductor (d. 1973)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON>sser\" title=\"<PERSON>\"><PERSON></a>, French organist, composer, and conductor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist, composer, and conductor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_B%C3%BCsser"}]}, {"year": "1874", "text": "<PERSON>, English-Canadian poet and author (d. 1958)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian poet and author (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian poet and author (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_W._Service"}]}, {"year": "1875", "text": "<PERSON><PERSON>, German biochemist and physician (d. 1949)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German biochemist and physician (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German biochemist and physician (d. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, English cricketer and footballer (d. 1937)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 1947)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1947)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1880", "text": "<PERSON>, American high jumper (d. 1954)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American high jumper (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American high jumper (d. 1954)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1882", "text": "<PERSON>, American author (d. 1973)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American author (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American author (d. 1973)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_(novelist)"}]}, {"year": "1885", "text": "<PERSON>, Chinese author and translator (d. 1967)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese author and translator (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese author and translator (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Russian avant garde writer and literary critic (d. 1945)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian avant garde writer and literary critic (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian avant garde writer and literary critic (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American chemist (d. 1949)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Australian-English violinist (d. 1981)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English violinist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English violinist (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American publisher (d. 1985)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Irving Mills\"><PERSON></a>, American publisher (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Irving Mills\"><PERSON></a>, American publisher (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Mills"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Greek soldier and politician (d. 1947)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Evrip<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>rip<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek soldier and politician (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Evrip<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>vrip<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek soldier and politician (d. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evripid<PERSON>_<PERSON>s"}]}, {"year": "1895", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer and politician (d. 1966)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. M. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician (d. 1966)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>nam"}]}, {"year": "1895", "text": "<PERSON>, American lawyer, chemist, and author (d. 1955)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, chemist, and author (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, chemist, and author (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Mexican poet and academic (d. 1977)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and academic (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and academic (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American producer and editor (d. 2002)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and editor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and editor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American film director and producer (d. 1999)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Irving Rapper\"><PERSON></a>, American film director and producer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Japanese author and translator (d. 1978)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and translator (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and translator (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German-Dutch mother of <PERSON> (d. 1945)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch mother of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch mother of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, Cuban colonel and politician, ninth President of Cuba (d. 1973)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>io_Batista\" title=\"<PERSON><PERSON><PERSON>io Batista\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban colonel and politician, ninth <a href=\"https://wikipedia.org/wiki/President_of_Cuba\" title=\"President of Cuba\">President of Cuba</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Batista\" title=\"<PERSON><PERSON><PERSON>io Batista\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban colonel and politician, ninth <a href=\"https://wikipedia.org/wiki/President_of_Cuba\" title=\"President of Cuba\">President of Cuba</a> (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fulgencio_Batista"}, {"title": "President of Cuba", "link": "https://wikipedia.org/wiki/President_of_Cuba"}]}, {"year": "1901", "text": "<PERSON>, American businessman and inventor (d. 1988)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and inventor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and inventor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Scottish runner, rugby player, and missionary (d. 1945)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish runner, rugby player, and missionary (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish runner, rugby player, and missionary (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, English-French racing driver (d. 1945)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French racing driver (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French racing driver (d. 1945)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Spanish composer and conductor (d. 1989)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish composer and conductor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish composer and conductor (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Estonian footballer and pilot (d. 1975)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer and pilot (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer and pilot (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English actress (d. 1964)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Canadian-English actor and screenwriter (d. 1995)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English actor and screenwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English actor and screenwriter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American banker and politician, tenth United States Secretary of the Navy (d. 2004)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, tenth <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, tenth <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Navy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy"}]}, {"year": "1908", "text": "<PERSON>, English footballer (d. 1981)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1908", "text": "<PERSON>, American actress and singer (d. 1984)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, German captain (d. 1941)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON>nt<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German captain (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German captain (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCnther_Prien"}]}, {"year": "1909", "text": "<PERSON>, American art critic (d. 1994)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art critic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art critic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, American baseball player and sportscaster (d. 1974)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Dizzy <PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Dizzy <PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster (d. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Jamaican cricketer (d. 1979)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Chilean lawyer and politician, 28th President of Chile (d. 1982)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1911", "text": "<PERSON>, French cyclist (d. 1996)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bie\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bie\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bie"}]}, {"year": "1914", "text": "<PERSON>, French-American conductor and educator (d. 1992)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American conductor and educator (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American conductor and educator (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American director, producer, and screenwriter (d. 2016)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English-Australian minister and politician (d. 1996)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian minister and politician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian minister and politician (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American businessman, founded <PERSON>'s Jr. (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Jr.\" title=\"<PERSON>'s Jr.\"><PERSON>'s Jr.</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Jr.\" title=\"<PERSON>'s Jr.\"><PERSON>'s Jr.</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>%27s_Jr."}]}, {"year": "1918", "text": "<PERSON><PERSON>, Dutch poet and educator (d. 2005)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch poet and educator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch poet and educator (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>el_Benschop"}]}, {"year": "1918", "text": "<PERSON>, Swedish director, producer, and production manager (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director, producer, and production manager (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director, producer, and production manager (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Australian surveyor and politician, eighth Lord Mayor of Brisbane (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian surveyor and politician, eighth <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Brisbane\" title=\"Lord Mayor of Brisbane\">Lord Mayor of Brisbane</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian surveyor and politician, eighth <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Brisbane\" title=\"Lord Mayor of Brisbane\">Lord Mayor of Brisbane</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Lord Mayor of Brisbane", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_Brisbane"}]}, {"year": "1918", "text": "<PERSON>, American screenwriter and producer (d. 1996)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Silliphant\" title=\"<PERSON> Silliphant\"><PERSON></a>, American screenwriter and producer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Silliphant\" title=\"<PERSON> Silliphant\"><PERSON></a>, American screenwriter and producer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lliphant"}]}, {"year": "1919", "text": "<PERSON>, American chemist and academic (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor and screenwriter (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American photographer (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American director and playwright, co-founded the Roundabout Theatre Company (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and playwright, co-founded the <a href=\"https://wikipedia.org/wiki/Roundabout_Theatre_Company\" title=\"Roundabout Theatre Company\">Roundabout Theatre Company</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fe<PERSON>\"><PERSON></a>, American director and playwright, co-founded the <a href=\"https://wikipedia.org/wiki/Roundabout_Theatre_Company\" title=\"Roundabout Theatre Company\">Roundabout Theatre Company</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gene_Feist"}, {"title": "Roundabout Theatre Company", "link": "https://wikipedia.org/wiki/Roundabout_Theatre_Company"}]}, {"year": "1923", "text": "<PERSON>, American poet (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Mexican actress (d. 2002)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor, writer, playwright, and magician (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, writer, playwright, and magician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Swift\"><PERSON></a>, American actor, writer, playwright, and magician (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, German-English metallurgist and academic", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(metallurgist)\" title=\"<PERSON> (metallurgist)\"><PERSON></a>, German-English metallurgist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(metallurgist)\" title=\"<PERSON> (metallurgist)\"><PERSON></a>, German-English metallurgist and academic", "links": [{"title": "<PERSON> (metallurgist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(metallurgist)"}]}, {"year": "1925", "text": "<PERSON>, American general and pilot (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Risner\"><PERSON></a>, American general and pilot (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Risner\"><PERSON></a>, American general and pilot (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American novelist and journalist", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American novelist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American novelist and journalist", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Spanish soprano and actress (d. 1996)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish soprano and actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish soprano and actress (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gar"}]}, {"year": "1929", "text": "<PERSON>, Sri Lankan anthropologist and academic (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan anthropologist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan anthropologist and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American lawyer and judge (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American journalist and author", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English actress (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English physicist and academic (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American physicist and academic (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, German journalist and politician, eighth Federal President of Germany (d. 2006)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, eighth <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">Federal President of Germany</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, eighth <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">Federal President of Germany</a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of German presidents", "link": "https://wikipedia.org/wiki/List_of_German_presidents"}]}, {"year": "1932", "text": "<PERSON>, Romanian chess player (d. 1983)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A2lt<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian chess player (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A2ltea\" title=\"<PERSON>\"><PERSON></a>, Romanian chess player (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victor_Cioc%C3%A2ltea"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American zoologist and anthropologist (d. 1985)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American zoologist and anthropologist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American zoologist and anthropologist (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American novelist, essayist, and critic (d. 2004)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and critic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and critic (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American rock guitarist and bass player (d. 2009)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock guitarist and bass player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock guitarist and bass player (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American soprano and actress", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON> <PERSON><PERSON>, American race car driver", "html": "1935 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American race car driver", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, German footballer, manager, and sportscaster (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Udo_<PERSON>ttek\" title=\"Udo <PERSON>ttek\"><PERSON><PERSON></a>, German footballer, manager, and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Udo_<PERSON>ttek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer, manager, and sportscaster (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Udo_<PERSON>k"}]}, {"year": "1936", "text": "<PERSON>, Scottish actor and producer (d. 2016)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, Scottish actor and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, Scottish actor and producer (d. 2016)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(producer)"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Brazilian racing driver (d. 2011)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian racing driver (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian racing driver (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ueno"}]}, {"year": "1937", "text": "<PERSON>, American cardinal (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American journalist and critic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Marina_Vai<PERSON>y\" title=\"<PERSON> V<PERSON>\"><PERSON></a>, American journalist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marina_Vai<PERSON>y\" title=\"Marina Vaizey\"><PERSON></a>, American journalist and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Vaizey"}]}, {"year": "1939", "text": "<PERSON>, American photographer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English actress and comedian (d. 2015)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and comedian (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and comedian (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English tennis player and sportscaster", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian singer and manager (d. 2016)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Ang%C3%A9lil\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and manager (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Ang%C3%A9lil\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and manager (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Ang%C3%A9lil"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English actor (d. 2006)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English bassist and composer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bassist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bassist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, British composer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer and pianist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Swiss-German keyboard player and producer (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-German keyboard player and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-German keyboard player and producer (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter and actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American astronomer and biologist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and biologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and biologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American journalist and politician (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Dutch footballer and manager (d. 2020)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Wim_<PERSON>urbier\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON>bier\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON><PERSON><PERSON>r"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Indian actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Italian soprano and actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soprano and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON>, <PERSON>, English academic and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Proctor\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Proctor\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American physiologist, talk show host, and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physiologist, talk show host, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physiologist, talk show host, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American director, producer, screenwriter, and composer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Estonian general", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Ants_<PERSON>ots\" title=\"Ants Laaneots\"><PERSON><PERSON></a>, Estonian general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ants_<PERSON>ots\" title=\"Ants Laaneots\"><PERSON><PERSON></a>, Estonian general", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ants_Laaneots"}]}, {"year": "1948", "text": "<PERSON>, American journalist and critic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian snooker player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American businesswoman, founded Auntie Anne's", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, founded <a href=\"https://wikipedia.org/wiki/Aunt<PERSON>_<PERSON>%27s\" title=\"Aunt<PERSON>'s\"><PERSON><PERSON>'s</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, founded <a href=\"https://wikipedia.org/wiki/Aunt<PERSON>_<PERSON>%27s\" title=\"Aunt<PERSON>\"><PERSON><PERSON>'s</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Auntie <PERSON>'s", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27s"}]}, {"year": "1949", "text": "<PERSON><PERSON> <PERSON><PERSON>, Irish historian and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(historian)\" title=\"<PERSON><PERSON> <PERSON><PERSON> (historian)\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(historian)\" title=\"<PERSON><PERSON> <PERSON><PERSON> (historian)\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish historian and academic", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(historian)"}]}, {"year": "1949", "text": "<PERSON>, Australian physician and politician, 13th Deputy Premier of New South Wales", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physician and politician, 13th <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_New_South_Wales\" title=\"Deputy Premier of New South Wales\">Deputy Premier of New South Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physician and politician, 13th <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_New_South_Wales\" title=\"Deputy Premier of New South Wales\">Deputy Premier of New South Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Premier of New South Wales", "link": "https://wikipedia.org/wiki/Deputy_Premier_of_New_South_Wales"}]}, {"year": "1950", "text": "<PERSON>, American actress, dancer, and choreographer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American comedian, actor, and producer (d. 2010)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON> <PERSON>, King of Egypt", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Fuad_II\" class=\"mw-redirect\" title=\"Fuad II\">Fuad II</a>, King of Egypt", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fuad_II\" class=\"mw-redirect\" title=\"Fuad II\"><PERSON><PERSON> II</a>, King of Egypt", "links": [{"title": "Fuad II", "link": "https://wikipedia.org/wiki/Fuad_II"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Italian racing driver and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian racing driver and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian racing driver and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zani"}]}, {"year": "1953", "text": "<PERSON>, American militant, founded The Order (d. 1984)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American militant, founded <a href=\"https://wikipedia.org/wiki/The_Order_(white_supremacist_group)\" title=\"The Order (white supremacist group)\">The Order</a> (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American militant, founded <a href=\"https://wikipedia.org/wiki/The_Order_(white_supremacist_group)\" title=\"The Order (white supremacist group)\">The Order</a> (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "The Order (white supremacist group)", "link": "https://wikipedia.org/wiki/The_Order_(white_supremacist_group)"}]}, {"year": "1954", "text": "<PERSON>, Welsh actor (d. 2010)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, German discus thrower", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, German discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, German discus thrower", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Russian footballer and coach (d. 2015)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and coach (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American captain, physician, and astronaut", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, physician, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, physician, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Barbadian cricketer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American basketball player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Dutch footballer and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Australian singer-songwriter and keyboardist (d. 2019)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and keyboardist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and keyboardist (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Argentinian actor, director, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_Dar%C3%ADn"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Russian mountaineer and explorer (d. 1997)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mountaineer and explorer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mountaineer and explorer (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v"}]}, {"year": "1958", "text": "<PERSON>, Swedish lawyer and politician, ninth Swedish Minister for the Environment", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician, ninth <a href=\"https://wikipedia.org/wiki/Minister_for_the_Environment_(Sweden)\" title=\"Minister for the Environment (Sweden)\">Swedish Minister for the Environment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician, ninth <a href=\"https://wikipedia.org/wiki/Minister_for_the_Environment_(Sweden)\" title=\"Minister for the Environment (Sweden)\">Swedish Minister for the Environment</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>k"}, {"title": "Minister for the Environment (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_the_Environment_(Sweden)"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Latvian businessman and politician, fourth Prime Minister of Latvia", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Andris_%C5%A0%C4%B7%C4%93le\" title=\"<PERSON><PERSON> Šķēle\"><PERSON><PERSON> Šķēle</a>, Latvian businessman and politician, fourth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andris_%C5%A0%C4%B7%C4%93le\" title=\"<PERSON><PERSON> Šķēle\"><PERSON><PERSON> Šķēle</a>, Latvian businessman and politician, fourth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a>", "links": [{"title": "<PERSON><PERSON> Šķēle", "link": "https://wikipedia.org/wiki/Andris_%C5%A0%C4%B7%C4%93le"}, {"title": "Prime Minister of Latvia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Latvia"}]}, {"year": "1959", "text": "<PERSON>, Canadian painter and educator", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Nigerian-English singer-songwriter and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Nigerian-English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Nigerian-English singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1961", "text": "<PERSON>, Norwegian guitarist and composer (d. 2006)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Norwegian guitarist and composer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Norwegian guitarist and composer (d. 2006)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1962", "text": "<PERSON>, Australian electrician and politician, 51st Australian Minister of Defence", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian electrician and politician, 51st <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian electrician and politician, 51st <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister of Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American R&B singer-songwriter and actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American R&amp;B singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American R&amp;B singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English journalist and television presenter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1964", "text": "<PERSON>, Canadian golfer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, German footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1969", "text": "<PERSON>, Scottish guitarist and songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American boxer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American boxer", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1970", "text": "<PERSON>, American basketball player and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Spanish tennis player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American film producer, screenwriter and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" title=\"<PERSON> (film producer)\"><PERSON></a>, American film producer, screenwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" title=\"<PERSON> (film producer)\"><PERSON></a>, American film producer, screenwriter and actor", "links": [{"title": "<PERSON> (film producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Danish footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bagger\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Russian footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Vincentian footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vincentian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vincentian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English model and fashion designer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American basketball player and sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Russian racing driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Russian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Russian racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1976", "text": "<PERSON>, Slovak swimmer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ravcov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Slovak swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ravcov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Slovak swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martina_Moravcov%C3%A1"}]}, {"year": "1977", "text": "<PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1978", "text": "<PERSON>, Mexican baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9zaga\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9zaga\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfredo_Am%C3%A9zaga"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American singer and actress (d. 2001)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Aali<PERSON>\" title=\"Aali<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ali<PERSON>\" title=\"Aali<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>yah"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American actor, playwright, and composer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, playwright, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, playwright, and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Dominican-American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Malian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ita"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1981", "text": "<PERSON>, American musician and songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English singer-songwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Danish actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Birgitte_Hjort_S%C3%B<PERSON><PERSON><PERSON>\" title=\"Birgitte H<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Birgitte_Hjort_S%C3%B<PERSON><PERSON><PERSON>\" title=\"Birgitte H<PERSON>rt <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birgitte_Hjort_S%C3%B8<PERSON>sen"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1983", "text": "<PERSON>, Austrian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Swiss footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Serbian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Radovi%C4%87"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Australian cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian-Lithuanian pianist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Gintaras_Janu%C5%A1evi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian-Lithuanian pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gintaras_Janu%C5%A1evi%C4%8Dius\" title=\"G<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian-Lithuanian pianist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gintaras_Janu%C5%A1evi%C4%8Dius"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Indian actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hotra"}]}, {"year": "1985", "text": "<PERSON>, Danish-Gambian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Gambian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Gambian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Danish-Gambian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Gambian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Gambian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, German footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English swimmer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Venezuelan basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Greivis_V%C3%A1squez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greivis_V%C3%A1squez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Greivis_V%C3%A1squez"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Danish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Mexican footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, English singer-songwriter and actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/FKA_Twigs\" title=\"FKA Twigs\">FK<PERSON> Twigs</a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/FKA_Twigs\" title=\"FKA Twigs\">FKA Twigs</a>, English singer-songwriter and actress", "links": [{"title": "FKA Twigs", "link": "https://wikipedia.org/wiki/FKA_Twigs"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1991", "text": "<PERSON>, Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Anier"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, French tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Amandine_Hesse\" title=\"Amandine <PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amandine_Hesse\" title=\"Amandine <PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "Amandine <PERSON>", "link": "https://wikipedia.org/wiki/Amandine_Hesse"}]}, {"year": "1993", "text": "<PERSON><PERSON>, South Korean musician", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Sungjin\" title=\"Sung<PERSON>\"><PERSON><PERSON></a>, South Korean musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sungjin\" title=\"Sung<PERSON>\"><PERSON><PERSON></a>, South Korean musician", "links": [{"title": "Sungjin", "link": "https://wikipedia.org/wiki/Sung<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(ice_hockey,_born_1994)\" title=\"<PERSON><PERSON><PERSON> (ice hockey, born 1994)\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(ice_hockey,_born_1994)\" title=\"<PERSON><PERSON><PERSON> (ice hockey, born 1994)\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON> (ice hockey, born 1994)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(ice_hockey,_born_1994)"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Australian-Canadian cricketer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-Canadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-Canadian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Tre%27Davious_White\" title=\"Tre'<PERSON><PERSON>\">T<PERSON>'<PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tre%27Davious_White\" title=\"Tre'<PERSON><PERSON>\">Tre'<PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tre%27Davious_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, South Korean singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1996", "text": "<PERSON>, Chinese basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, South Korean singer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>wan\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>wan\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-kwan"}]}, {"year": "2000", "text": "<PERSON>, Canadian basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "654", "text": "<PERSON>, Chinese politician and chancellor (b. 596)", "html": "654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician and chancellor (b. 596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician and chancellor (b. 596)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>fu"}]}, {"year": "957", "text": "<PERSON> ibn <PERSON>, <PERSON><PERSON><PERSON><PERSON> vizier (b. 871)", "html": "957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>Madhara%27i\" title=\"<PERSON> ibn <PERSON>Madhara'<PERSON>\"><PERSON> ibn <PERSON></a>, <PERSON><PERSON><PERSON><PERSON> vizier (b. 871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>Madhara%27i\" title=\"<PERSON> Ba<PERSON> ibn <PERSON>Madhara'<PERSON>\"><PERSON> ibn <PERSON>hara<PERSON>i</a>, <PERSON><PERSON><PERSON><PERSON> vizier (b. 871)", "links": [{"title": "<PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_ibn_<PERSON>_<PERSON>Madhara%27i"}]}, {"year": "970", "text": "<PERSON><PERSON><PERSON><PERSON> of Constantinople, Byzantine patriarch (b. 956)", "html": "970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON><PERSON><PERSON> of Constantinople\"><PERSON><PERSON><PERSON><PERSON> of Constantinople</a>, Byzantine patriarch (b. 956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON><PERSON><PERSON> of Constantinople\"><PERSON><PERSON><PERSON><PERSON> of Constantinople</a>, Byzantine patriarch (b. 956)", "links": [{"title": "Pol<PERSON><PERSON>tus of Constantinople", "link": "https://wikipedia.org/wiki/Polyeuctus_of_Constantinople"}]}, {"year": "1263", "text": "<PERSON><PERSON>, Japanese founder of the Jodo Shinshu branch of Pure Land Buddhism (b. 1173)", "html": "1263 - <a href=\"https://wikipedia.org/wiki/Shinran\" title=\"Shinran\"><PERSON><PERSON></a>, Japanese founder of the Jodo Shinshu branch of Pure Land Buddhism (b. 1173)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shinran\" title=\"Shinran\"><PERSON><PERSON></a>, Japanese founder of the Jodo Shinshu branch of Pure Land Buddhism (b. 1173)", "links": [{"title": "Shinran", "link": "https://wikipedia.org/wiki/Shinran"}]}, {"year": "1289", "text": "<PERSON><PERSON><PERSON>, Mongol minister", "html": "1289 - <a href=\"https://wikipedia.org/wiki/Buqa\" title=\"Buqa\"><PERSON><PERSON><PERSON></a>, Mongol minister", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buqa\" title=\"Buqa\"><PERSON><PERSON><PERSON></a>, Mongol minister", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Buqa"}]}, {"year": "1327", "text": "<PERSON><PERSON><PERSON><PERSON>, Byzantine monk, scholar, and politician (b. 1250)", "html": "1327 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine monk, scholar, and politician (b. 1250)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine monk, scholar, and politician (b. 1250)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ph<PERSON><PERSON>_<PERSON>nos"}]}, {"year": "1354", "text": "<PERSON> of Châtillon, duchess of Athens (b. c.1285)", "html": "1354 - <a href=\"https://wikipedia.org/wiki/Joanna_of_Ch%C3%A2til<PERSON>\" title=\"<PERSON> of Châtillon\"><PERSON> of Ch<PERSON>lon</a>, duchess of Athens (b. c.1285)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joanna_of_Ch%C3%A2tillon\" title=\"<PERSON> of Châtillon\"><PERSON> of <PERSON>lon</a>, duchess of Athens (b. c.1285)", "links": [{"title": "<PERSON> of Châtillon", "link": "https://wikipedia.org/wiki/Joanna_of_Ch%C3%A2tillon"}]}, {"year": "1373", "text": "<PERSON>, 7th Earl of Hereford (b. 1342)", "html": "1373 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_7th_Earl_of_Hereford\" title=\"<PERSON>, 7th Earl of Hereford\"><PERSON>, 7th Earl of Hereford</a> (b. 1342)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_7th_Earl_of_Hereford\" title=\"<PERSON>, 7th Earl of Hereford\"><PERSON>, 7th Earl of Hereford</a> (b. 1342)", "links": [{"title": "<PERSON>, 7th Earl of Hereford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_7th_Earl_of_Hereford"}]}, {"year": "1391", "text": "<PERSON><PERSON><PERSON> of Granada, Na<PERSON>rid emir (b. 1338)", "html": "1391 - <a href=\"https://wikipedia.org/wiki/Muhammed_V_of_Granada\" class=\"mw-redirect\" title=\"Muhammed V of Granada\"><PERSON><PERSON><PERSON> V of Granada</a>, <PERSON><PERSON><PERSON> emir (b. 1338)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Muhammed_V_of_Granada\" class=\"mw-redirect\" title=\"Muhammed V of Granada\"><PERSON><PERSON>med V of Granada</a>, <PERSON><PERSON><PERSON> emir (b. 1338)", "links": [{"title": "Muhammed V of Granada", "link": "https://wikipedia.org/wiki/Muhammed_V_of_Granada"}]}, {"year": "1400", "text": "<PERSON>, 1st Duke of Exeter, English politician, Lord Great <PERSON> (b. 1352)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Exeter\" title=\"<PERSON>, 1st Duke of Exeter\"><PERSON>, 1st Duke of Exeter</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Great_Chamberlain\" title=\"Lord Great Chamberlain\">Lord Great Chamberlain</a> (b. 1352)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Exeter\" title=\"<PERSON>, 1st Duke of Exeter\"><PERSON>, 1st Duke of Exeter</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Great_Chamberlain\" title=\"Lord Great Chamberlain\">Lord Great <PERSON></a> (b. 1352)", "links": [{"title": "<PERSON>, 1st Duke of Exeter", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Exeter"}, {"title": "Lord Great <PERSON>", "link": "https://wikipedia.org/wiki/Lord_Great_<PERSON>"}]}, {"year": "1443", "text": "<PERSON><PERSON> of Narni, Italian mercenary (b. 1370)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Narni\" title=\"<PERSON><PERSON> of Narni\"><PERSON><PERSON> of Narni</a>, Italian mercenary (b. 1370)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Narni\" title=\"<PERSON><PERSON> of Narni\"><PERSON><PERSON> of Narni</a>, Italian mercenary (b. 1370)", "links": [{"title": "<PERSON><PERSON> of Narni", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Narni"}]}, {"year": "1545", "text": "<PERSON>, German priest and reformer (b. 1484)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and reformer (b. 1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and reformer (b. 1484)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1547", "text": "<PERSON>, German astronomer and cartographer (b. 1477)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ner\" title=\"<PERSON>\"><PERSON></a>, German astronomer and cartographer (b. 1477)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ner\" title=\"<PERSON>\"><PERSON></a>, German astronomer and cartographer (b. 1477)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Johannes_Sch%C3%B6ner"}]}, {"year": "1554", "text": "<PERSON><PERSON>, Danish publisher and scholar (b. 1480)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish publisher and scholar (b. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish publisher and scholar (b. 1480)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1585", "text": "<PERSON>, 1st Earl of Lincoln, English admiral and politician (b. 1512)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Lincoln\" title=\"<PERSON>, 1st Earl of Lincoln\"><PERSON>, 1st Earl of Lincoln</a>, English admiral and politician (b. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Lincoln\" title=\"<PERSON>, 1st Earl of Lincoln\"><PERSON>, 1st Earl of Lincoln</a>, English admiral and politician (b. 1512)", "links": [{"title": "<PERSON>, 1st Earl of Lincoln", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Lincoln"}]}, {"year": "1595", "text": "<PERSON><PERSON>, Ottoman sultan (b. 1546)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/Murad_III\" title=\"Murad III\"><PERSON><PERSON> III</a>, Ottoman sultan (b. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murad_III\" title=\"Murad III\"><PERSON><PERSON></a>, Ottoman sultan (b. 1546)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Murad_III"}]}, {"year": "1635", "text": "<PERSON>, Spanish nun and mystic (b. 1563)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/Mariana_de_Jes%C3%<PERSON><PERSON>_<PERSON>\" title=\"Mariana de <PERSON>\"><PERSON></a>, Spanish nun and mystic (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mariana_de_Jes%C3%BA<PERSON>_<PERSON>\" title=\"Mariana de <PERSON>\"><PERSON></a>, Spanish nun and mystic (b. 1563)", "links": [{"title": "Mariana de Je<PERSON>ús <PERSON>", "link": "https://wikipedia.org/wiki/Mariana_de_Jes%C3%<PERSON><PERSON>_Torres"}]}, {"year": "1659", "text": "<PERSON>, French lawyer (b. 1580)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer (b. 1580)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese emperor (b. 1675)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 1675)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>"}]}, {"year": "1711", "text": "<PERSON>, Indian-Sri Lankan priest and saint (b. 1651)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Sri Lankan priest and saint (b. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Sri Lankan priest and saint (b. 1651)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "<PERSON><PERSON>, German poet and playwright (b. 1680)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and playwright (b. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and playwright (b. 1680)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON>, Dutch lawyer and scholar (b. 1684)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and scholar (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and scholar (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, Russian field marshal and politician (b. 1667)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian field marshal and politician (b. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian field marshal and politician (b. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, English historian and author (b. 1705)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1705)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, English historian and politician (b. 1737)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and politician (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and politician (b. 1737)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, Scottish general and politician (b. 1761)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Scottish general and politician (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Scottish general and politician (b. 1761)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)"}]}, {"year": "1817", "text": "<PERSON>, Jamaican-American lawyer and politician, sixth United States Secretary of the Treasury (b. 1759)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(statesman)\" title=\"<PERSON> (statesman)\"><PERSON></a>, Jamaican-American lawyer and politician, sixth <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(statesman)\" title=\"<PERSON> (statesman)\"><PERSON></a>, Jamaican-American lawyer and politician, sixth <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (b. 1759)", "links": [{"title": "<PERSON> (statesman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(statesman)"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1834", "text": "<PERSON>, French mathematician and academic (b. 1769)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON><PERSON>, American entomologist and botanist (b. 1795)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Thad<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Thad<PERSON><PERSON>\">Thad<PERSON><PERSON></a>, American entomologist and botanist (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thad<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Thad<PERSON><PERSON>\">Thad<PERSON><PERSON></a>, American entomologist and botanist (b. 1795)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thad<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Austrian secretary and author (b. 1795)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian secretary and author (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian secretary and author (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, French journalist and author (b. 1828)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_About\" title=\"<PERSON>\"><PERSON> About</a>, French journalist and author (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_About\" title=\"<PERSON>\"><PERSON> About</a>, French journalist and author (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON>_About"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Canadian-French poet and bookseller (b. 1827)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Octave_Cr%C3%A9mazie\" title=\"Octave Crémazie\">Octave <PERSON></a>, Canadian-French poet and bookseller (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octave_Cr%C3%A9mazie\" title=\"Octave Crémazie\">Octave <PERSON><PERSON><PERSON></a>, Canadian-French poet and bookseller (b. 1827)", "links": [{"title": "Octave <PERSON>", "link": "https://wikipedia.org/wiki/Octave_Cr%C3%A9mazie"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, Italian composer and academic (b. 1834)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Amil<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and academic (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>il<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and academic (b. 1834)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amilcare_<PERSON>ielli"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, French pianist and composer (b. 1836)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/L%C3%A9o_Delibes\" title=\"Léo Delibes\"><PERSON><PERSON><PERSON></a>, French pianist and composer (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9o_Delibes\" title=\"Léo Delibes\"><PERSON><PERSON><PERSON></a>, French pianist and composer (b. 1836)", "links": [{"title": "Léo <PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9o_Delibes"}]}, {"year": "1898", "text": "<PERSON>, English lawyer and politician (b. 1802)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, French poet and playwright (b. 1825)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and playwright (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and playwright (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Swiss painter and academic (b. 1827)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and academic (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and academic (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arnold_B%C3%B6<PERSON><PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, American soldier, minister, and politician (b. 1822)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Revels\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Revels\"><PERSON><PERSON></a>, American soldier, minister, and politician (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Revels\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Revels\"><PERSON><PERSON></a>, American soldier, minister, and politician (b. 1822)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Revels"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Indian scholar, social reformer, judge and author (b. 1842)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian scholar, social reformer, judge and author (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian scholar, social reformer, judge and author (b. 1842)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American businessman and philanthropist, founded Marshall Field's (b. 1834)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Marshall_Field\" title=\"Marshall Field\">Marshall Field</a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Marshall_Field%27s\" title=\"Marshall Field's\">Marshall Field's</a> (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marshall_Field\" title=\"Marshall Field\">Marshall Field</a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Marshall_Field%27s\" title=\"Marshall Field's\">Marshall Field's</a> (b. 1834)", "links": [{"title": "Marshall Field", "link": "https://wikipedia.org/wiki/Marshall_Field"}, {"title": "Marshall Field's", "link": "https://wikipedia.org/wiki/Marshall_Field%27s"}]}, {"year": "1917", "text": "<PERSON>, American admiral (b. 1837)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Brazilian lawyer and politician, fifth President of Brazil (b. 1848)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, fifth <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a>, Brazilian lawyer and politician, fifth <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1848)", "links": [{"title": "<PERSON> de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Turkish politician (b. 1867)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish politician (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish politician (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American serial killer, rapist and cannibal (b. 1870)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fish\"><PERSON></a>, American serial killer, rapist and cannibal (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert Fish\"><PERSON></a>, American serial killer, rapist and cannibal (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON> Indian author and playwright (b. 1876)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>y\" title=\"<PERSON><PERSON>hya<PERSON>\"><PERSON><PERSON></a> Indian author and playwright (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Indian author and playwright (b. 1876)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Duke of Connaught and Strathearn (b. 1850)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Connaught_and_Strathearn\" title=\"Prince <PERSON>, Duke of Connaught and Strathearn\">Prince <PERSON>, Duke of Connaught and Strathearn</a> (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Connaught_and_Strathearn\" title=\"Prince <PERSON>, Duke of Connaught and Strathearn\">Prince <PERSON>, Duke of Connaught and Strathearn</a> (b. 1850)", "links": [{"title": "<PERSON>, Duke of Connaught and Strathearn", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_<PERSON>_Connaught_and_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Estonian poet and linguist (b. 1885)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Villem_Gr%C3%BCnt<PERSON>-Ridal<PERSON>\" title=\"<PERSON><PERSON>rünthal-Ridal<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet and linguist (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Villem_Gr%C3%BCnthal-Ridala\" title=\"<PERSON><PERSON>nthal-Ridal<PERSON>\"><PERSON><PERSON></a>, Estonian poet and linguist (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Villem_Gr%C3%BCnthal-Ridala"}]}, {"year": "1942", "text": "<PERSON>, American actress and comedian (b. 1908)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, German lawyer and politician, Mayor of Marburg (b. 1899)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of mayors of Marburg", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Marburg"}]}, {"year": "1957", "text": "<PERSON>, 1st Earl of Athlone, English general and politician, 16th Governor General of Canada (b. 1874)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Athlone\" title=\"<PERSON>, 1st Earl of Athlone\"><PERSON>, 1st Earl of Athlone</a>, English general and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Athlone\" title=\"<PERSON>, 1st Earl of Athlone\"><PERSON>, 1st Earl of Athlone</a>, English general and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1874)", "links": [{"title": "<PERSON>, 1st Earl of Athlone", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Athlone"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1957", "text": "<PERSON>, Italian cellist and conductor (b. 1867)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and conductor (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and conductor (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Vietnamese journalist and author (b. 1887)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Ph<PERSON>_<PERSON>h%C3%B4i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese journalist and author (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ph<PERSON>_<PERSON>h%C3%B4i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese journalist and author (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Phan_Kh%C3%B4i"}]}, {"year": "1961", "text": "<PERSON>, German swimmer (b. 1880)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%B6ne\" title=\"<PERSON>\"><PERSON></a>, German swimmer (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%B6ne\" title=\"<PERSON>\"><PERSON></a>, German swimmer (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Max_Sch%C3%B6ne"}]}, {"year": "1962", "text": "<PERSON>, Australian photographer, director, producer, and cinematographer (b. 1885)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian photographer, director, producer, and cinematographer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian photographer, director, producer, and cinematographer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Croatian sculptor and architect, designed the Monument to the Unknown Hero (b. 1883)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1trovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian sculptor and architect, designed the <a href=\"https://wikipedia.org/wiki/Monument_to_the_Unknown_Hero\" title=\"Monument to the Unknown Hero\">Monument to the Unknown Hero</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1trovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian sculptor and architect, designed the <a href=\"https://wikipedia.org/wiki/Monument_to_the_Unknown_Hero\" title=\"Monument to the Unknown Hero\">Monument to the Unknown Hero</a> (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ivan_Me%C5%A1trovi%C4%87"}, {"title": "Monument to the Unknown Hero", "link": "https://wikipedia.org/wiki/Monument_to_the_Unknown_Hero"}]}, {"year": "1967", "text": "<PERSON>, American physicist and academic (b. 1901)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American evangelist, founded Bob <PERSON> University (b. 1883)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American evangelist, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Bob <PERSON> University\"><PERSON></a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American evangelist, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Bob <PERSON> University\"><PERSON></a> (b. 1883)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek archaeologist and judge (b. 1881)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek archaeologist and judge (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek archaeologist and judge (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panagiot<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Russian-American composer and songwriter (b. 1903)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American composer and songwriter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American composer and songwriter (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Duke"}]}, {"year": "1971", "text": "<PERSON>, Belgian cyclist (b. 1890)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Belgian cyclist (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Belgian cyclist (b. 1890)", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American soldier and politician, 28th Governor of Colorado (b. 1895)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and politician, 28th <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor of Colorado</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and politician, 28th <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor of Colorado</a> (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Colorado", "link": "https://wikipedia.org/wiki/Governor_of_Colorado"}]}, {"year": "1972", "text": "<PERSON>, Sr., American singer-songwriter, pianist, producer, and actor, created <PERSON> and the Chipmunks (b. 1919)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American singer-songwriter, pianist, producer, and actor, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_and_the_Chipmunks\" title=\"<PERSON> and the Chipmunks\"><PERSON> and the Chipmunks</a></i> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American singer-songwriter, pianist, producer, and actor, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_and_the_Chipmunks\" title=\"<PERSON> and the Chipmunks\"><PERSON> and the Chipmunks</a></i> (b. 1919)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>."}, {"title": "<PERSON> and the Chipmunks", "link": "https://wikipedia.org/wiki/<PERSON>_and_the_Chipmunks"}]}, {"year": "1973", "text": "<PERSON>, American musician and composer (b. 1907)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and composer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and composer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Russian-American painter (b. 1888)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Israel_<PERSON>\" title=\"Israel Abramofsky\"><PERSON></a>, Russian-American painter (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_<PERSON>\" title=\"Israel Abramofsky\">Israel <PERSON></a>, Russian-American painter (b. 1888)", "links": [{"title": "Israel A<PERSON>ky", "link": "https://wikipedia.org/wiki/Israel_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan journalist, lawyer, and politician (b. 1890)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan journalist, lawyer, and politician (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan journalist, lawyer, and politician (b. 1890)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English actor (b. 1908)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American musician and dancer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Virginia_Mauret\" title=\"Virginia Mauret\"><PERSON></a>, American musician and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Mauret\" title=\"Virginia Mauret\"><PERSON></a>, American musician and dancer", "links": [{"title": "<PERSON> Ma<PERSON>t", "link": "https://wikipedia.org/wiki/Virginia_Ma<PERSON>t"}]}, {"year": "1986", "text": "<PERSON>, American evangelist, author, and publisher (b. 1892)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist, author, and publisher (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist, author, and publisher (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Australian physician and activist (b. 1928)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian physician and activist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian physician and activist (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aine<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Croatian politician, war criminal, and Porajmos perpetrator, first Minister of Interior of the Independent State of Croatia (b. 1899)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ukovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician, war criminal, and <a href=\"https://wikipedia.org/wiki/Porajmos\" class=\"mw-redirect\" title=\"Porajmos\">Po<PERSON><PERSON> perpetrator</a>, first <a href=\"https://wikipedia.org/wiki/Minister_of_Interior_of_the_Independent_State_of_Croatia\" class=\"mw-redirect\" title=\"Minister of Interior of the Independent State of Croatia\">Minister of Interior of the Independent State of Croatia</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician, war criminal, and <a href=\"https://wikipedia.org/wiki/Porajmos\" class=\"mw-redirect\" title=\"Porajmos\"><PERSON><PERSON><PERSON> perpetrator</a>, first <a href=\"https://wikipedia.org/wiki/Minister_of_Interior_of_the_Independent_State_of_Croatia\" class=\"mw-redirect\" title=\"Minister of Interior of the Independent State of Croatia\">Minister of Interior of the Independent State of Croatia</a> (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ja_Artukovi%C4%87"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Porajmos"}, {"title": "Minister of Interior of the Independent State of Croatia", "link": "https://wikipedia.org/wiki/Minister_of_Interior_of_the_Independent_State_of_Croatia"}]}, {"year": "1990", "text": "<PERSON> <PERSON>, British farmer, educator, and founding figure in the organic movement (b. 1898)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, British farmer, educator, and founding figure in the organic movement (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, British farmer, educator, and founding figure in the organic movement (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English poet and critic (b. 1924)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American author and critic (b. 1903)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English journalist and publisher (b. 1914)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and publisher (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and publisher (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Australian lawyer, jurist, and politician, 12th Minister for Industry and Science (b. 1915)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer, jurist, and politician, 12th <a href=\"https://wikipedia.org/wiki/Minister_for_Industry_and_Science\" title=\"Minister for Industry and Science\">Minister for Industry and Science</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer, jurist, and politician, 12th <a href=\"https://wikipedia.org/wiki/Minister_for_Industry_and_Science\" title=\"Minister for Industry and Science\">Minister for Industry and Science</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Industry and Science", "link": "https://wikipedia.org/wiki/Minister_for_Industry_and_Science"}]}, {"year": "2000", "text": "<PERSON>, American physicist and academic (b. 1914)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, English author and journalist (b. 1939)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Waugh\" title=\"<PERSON>ber<PERSON> Waugh\"><PERSON><PERSON><PERSON></a>, English author and journalist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Waugh\" title=\"<PERSON>ber<PERSON> Waugh\"><PERSON><PERSON><PERSON></a>, English author and journalist (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English astronomer and physicist (b. 1916)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and physicist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and physicist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English politician (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician (b. 1918)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Finnish politician 34th Prime Minister of Finland (b. 1930)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician 34th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician 34th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sorsa"}, {"title": "Prime Minister of Finland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Finland"}]}, {"year": "2005", "text": "<PERSON>, American journalist and author (b. 1958)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American soldier and physician (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and physician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and physician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American boxer and runner (b. 1930)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_boxer)\" title=\"<PERSON> (American boxer)\"><PERSON></a>, American boxer and runner (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_boxer)\" title=\"<PERSON> (American boxer)\"><PERSON></a>, American boxer and runner (b. 1930)", "links": [{"title": "<PERSON> (American boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_boxer)"}]}, {"year": "2009", "text": "<PERSON>, English lawyer and author (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and author (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American painter (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American businessman, founded Taco Bell (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bell\" title=\"Glen Bell\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Taco_Bell\" title=\"Taco Bell\">Ta<PERSON> Bell</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bell\" title=\"Glen Bell\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Taco_Bell\" title=\"Taco Bell\">Ta<PERSON> Bell</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glen_Bell"}, {"title": "Taco Bell", "link": "https://wikipedia.org/wiki/Taco_Bell"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Japanese author and translator (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and translator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and translator (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "2012", "text": "<PERSON>, Jamaican-English boxer (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English boxer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English boxer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter and saxophonist (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and saxophonist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and saxophonist (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Icelandic footballer and manager (b. 1968)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Sigursteinn_G%C3%ADslason\" title=\"Sigursteinn Gíslason\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer and manager (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sigursteinn_G%C3%ADslason\" title=\"Sigursteinn Gíslason\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer and manager (b. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigursteinn_G%C3%ADslason"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American journalist and politician (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and politician (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Dutch pianist, conductor, and musicologist (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pianist, conductor, and musicologist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pianist, conductor, and musicologist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and coach (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, French technician and toy maker, created the Etch A Sketch (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French technician and toy maker, created the <a href=\"https://wikipedia.org/wiki/Etch_A_Sketch\" title=\"Etch A Sketch\"><PERSON><PERSON> A <PERSON>ch</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French technician and toy maker, created the <a href=\"https://wikipedia.org/wiki/Etch_A_Sketch\" title=\"<PERSON>tch A Sketch\"><PERSON><PERSON> A Sketch</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}, {"title": "Etch A Sketch", "link": "https://wikipedia.org/wiki/Etch_A_Sketch"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American tennis player and sportscaster (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player and sportscaster (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player and sportscaster (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American journalist and radio host, created <PERSON> (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and radio host, created <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dear <PERSON>\">Dear <PERSON></a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and radio host, created <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dear <PERSON>\">Dear <PERSON></a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dear Abby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American businessman, founded Scientific Atlanta (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Robinson\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Scientific_Atlanta\" title=\"Scientific Atlanta\">Scientific Atlanta</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Robinson\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Scientific_Atlanta\" title=\"Scientific Atlanta\">Scientific Atlanta</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Scientific Atlanta", "link": "https://wikipedia.org/wiki/Scientific_Atlanta"}]}, {"year": "2014", "text": "<PERSON>, American author and illustrator (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gary <PERSON>\"><PERSON></a>, American author and illustrator (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gary_<PERSON>\" title=\"Gary Arlington\"><PERSON></a>, American author and illustrator (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actress (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian-American actor (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Japanese lieutenant (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Hiroo Onoda\"><PERSON><PERSON></a>, Japanese lieutenant (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Onoda\"><PERSON><PERSON></a>, Japanese lieutenant (b. 1922)", "links": [{"title": "Hiroo Onoda", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oda"}]}, {"year": "2015", "text": "<PERSON>, Polish-Israeli author and translator (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli author and translator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli author and translator (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Chinese singer (b. 1981)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yao Beina\"><PERSON></a>, Chinese singer (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yao Beina\"><PERSON></a>, Chinese singer (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Yao_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Greek sculptor (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek sculptor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek sculptor (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>is"}]}, {"year": "2016", "text": "<PERSON>, American football player and coach (b. 1931)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American captain, pilot, and astronaut (b. 1934)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, British radio presenter (b. 1941)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British radio presenter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British radio presenter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Kosovo Serb politician (b. 1953)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Kosovo Serb politician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Kosovo Serb politician (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oliver_Ivan<PERSON>i%C4%87"}]}, {"year": "2019", "text": "<PERSON>, American businessman, investor, and philanthropist (b. 1929)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, investor, and philanthropist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, investor, and philanthropist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American musician (b. 1958)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lorna Doom\"><PERSON><PERSON></a>, American musician (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lorna Doom\"><PERSON><PERSON></a>, American musician (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Australian musician (b. 1956)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_musician)\" title=\"<PERSON> (Australian musician)\"><PERSON></a>, Australian musician (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_musician)\" title=\"<PERSON> (Australian musician)\"><PERSON></a>, Australian musician (b. 1956)", "links": [{"title": "<PERSON> (Australian musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_musician)"}]}, {"year": "2020", "text": "<PERSON>, British academic and editor (b. 1924)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and editor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and editor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, German-born Venezuelan zoologist (b. 1929)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born Venezuelan zoologist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born Venezuelan zoologist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, British journalist (b. 1948)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American record producer, songwriter (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, songwriter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, songwriter (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Former Malian President (b. 1945)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%AFta\" title=\"<PERSON>\"><PERSON></a>, Former Malian President (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%AFta\" title=\"<PERSON>\"><PERSON></a>, Former Malian President (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%AFta"}]}, {"year": "2025", "text": "<PERSON> <PERSON>, English actress (b. 1929)", "html": "2025 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1929)", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American professional baseball catcher and sportscaster (b. 1934)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional baseball catcher and sportscaster (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional baseball catcher and sportscaster (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}