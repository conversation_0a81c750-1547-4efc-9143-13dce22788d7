{"date": "June 24", "url": "https://wikipedia.org/wiki/June_24", "data": {"Events": [{"year": "1312 BC", "text": "<PERSON><PERSON><PERSON> II launches a campaign against the Kingdom of Azzi-Hayasa.", "html": "1312 BC - 1312 BC - <a href=\"https://wikipedia.org/wiki/Mursili_II\" class=\"mw-redirect\" title=\"Mursili II\"><PERSON>rsili II</a> <a href=\"https://wikipedia.org/wiki/Mursili%27s_eclipse\" title=\"<PERSON><PERSON><PERSON>'s eclipse\">launches a campaign</a> against the Kingdom of <a href=\"https://wikipedia.org/wiki/A<PERSON>-<PERSON>asa\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>.", "no_year_html": "1312 BC - <a href=\"https://wikipedia.org/wiki/Mursili_II\" class=\"mw-redirect\" title=\"Mursili II\">Mursili II</a> <a href=\"https://wikipedia.org/wiki/Mursili%27s_eclipse\" title=\"<PERSON><PERSON><PERSON>'s eclipse\">launches a campaign</a> against the Kingdom of <a href=\"https://wikipedia.org/wiki/A<PERSON>-Hayasa\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>.", "links": [{"title": "Mursili II", "link": "https://wikipedia.org/wiki/Mu<PERSON><PERSON>_II"}, {"title": "<PERSON><PERSON><PERSON>'s eclipse", "link": "https://wikipedia.org/wiki/Mursili%27s_eclipse"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "109", "text": "Roman emperor <PERSON><PERSON><PERSON> inaugurates the Aqua Traiana, an aqueduct that channels water from Lake Bracciano, 40 kilometres (25 miles) northwest of Rome.", "html": "109 - Roman emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> inaugurates the <a href=\"https://wikipedia.org/wiki/Aqua_Traiana\" title=\"Aqua Traiana\">Aqua Traiana</a>, an <a href=\"https://wikipedia.org/wiki/Roman_aqueduct\" title=\"Roman aqueduct\">aqueduct</a> that channels water from <a href=\"https://wikipedia.org/wiki/Lake_Bracciano\" title=\"Lake Bracciano\">Lake Bracciano</a>, 40 kilometres (25 miles) northwest of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>.", "no_year_html": "Roman emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> inaugurates the <a href=\"https://wikipedia.org/wiki/Aqua_Traiana\" title=\"Aqua Traiana\">Aqua Traiana</a>, an <a href=\"https://wikipedia.org/wiki/Roman_aqueduct\" title=\"Roman aqueduct\">aqueduct</a> that channels water from <a href=\"https://wikipedia.org/wiki/Lake_Bracciano\" title=\"Lake Bracciano\">Lake Bracciano</a>, 40 kilometres (25 miles) northwest of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Aqua <PERSON>", "link": "https://wikipedia.org/wiki/Aqua_Traiana"}, {"title": "Roman aqueduct", "link": "https://wikipedia.org/wiki/Roman_aqueduct"}, {"title": "Lake Bracciano", "link": "https://wikipedia.org/wiki/Lake_Bracciano"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}]}, {"year": "474", "text": "<PERSON> forces Roman usurper <PERSON><PERSON><PERSON><PERSON> to abdicate the throne and proclaims himself Emperor of the Western Roman Empire.", "html": "474 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> forces Roman usurper <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> to abdicate the throne and proclaims himself Emperor of the <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> forces Roman usurper <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> to abdicate the throne and proclaims himself Emperor of the <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julius_Nepos"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Glycerius"}, {"title": "Western Roman Empire", "link": "https://wikipedia.org/wiki/Western_Roman_Empire"}]}, {"year": "637", "text": "The Battle of Moira is fought between the High King of Ireland and the Kings of Ulster and Dál Riata. It is claimed to be the largest battle in the history of Ireland.", "html": "637 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Moira\" title=\"Battle of Moira\">Battle of Moira</a> is fought between the <a href=\"https://wikipedia.org/wiki/High_King_of_Ireland\" title=\"High King of Ireland\">High King of Ireland</a> and the Kings of <a href=\"https://wikipedia.org/wiki/Ulster\" title=\"Ulster\">Ulster</a> and <a href=\"https://wikipedia.org/wiki/D%C3%A1l_Riata\" title=\"Dál Riata\">Dál Riata</a>. It is claimed to be the largest battle in the history of Ireland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Moira\" title=\"Battle of Moira\">Battle of Moira</a> is fought between the <a href=\"https://wikipedia.org/wiki/High_King_of_Ireland\" title=\"High King of Ireland\">High King of Ireland</a> and the Kings of <a href=\"https://wikipedia.org/wiki/Ulster\" title=\"Ulster\">Ulster</a> and <a href=\"https://wikipedia.org/wiki/D%C3%A1l_Riata\" title=\"Dál Riata\">Dál Riata</a>. It is claimed to be the largest battle in the history of Ireland.", "links": [{"title": "Battle of Moira", "link": "https://wikipedia.org/wiki/Battle_of_Moira"}, {"title": "High King of Ireland", "link": "https://wikipedia.org/wiki/High_King_of_Ireland"}, {"title": "Ulster", "link": "https://wikipedia.org/wiki/Ulster"}, {"title": "<PERSON>ál <PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A1l_Riata"}]}, {"year": "843", "text": "The Vikings sack the French city of Nantes.", "html": "843 - The Vikings sack the French city of <a href=\"https://wikipedia.org/wiki/Viking_Brittany\" class=\"mw-redirect\" title=\"Viking Brittany\">Nantes</a>.", "no_year_html": "The Vikings sack the French city of <a href=\"https://wikipedia.org/wiki/Viking_Brittany\" class=\"mw-redirect\" title=\"Viking Brittany\">Nantes</a>.", "links": [{"title": "Viking Brittany", "link": "https://wikipedia.org/wiki/Viking_Brittany"}]}, {"year": "972", "text": "Battle of Cedynia, the first documented victory of Polish forces, takes place.", "html": "972 - <a href=\"https://wikipedia.org/wiki/Battle_of_Cedynia\" title=\"Battle of Cedynia\">Battle of Cedynia</a>, the first documented victory of Polish forces, takes place.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Cedynia\" title=\"Battle of Cedynia\">Battle of Cedynia</a>, the first documented victory of Polish forces, takes place.", "links": [{"title": "Battle of Cedynia", "link": "https://wikipedia.org/wiki/Battle_of_Cedynia"}]}, {"year": "1128", "text": "Battle of São Mamede, near Guimarães: Forces led by <PERSON><PERSON><PERSON> defeat forces led by his mother <PERSON> and her lover <PERSON>.", "html": "1128 - <a href=\"https://wikipedia.org/wiki/Battle_of_S%C3%A3o_Mamede\" title=\"Battle of São Mamede\">Battle of São Mamede</a>, near <a href=\"https://wikipedia.org/wiki/Guimar%C3%A3es\" title=\"Guimarães\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>: Forces led by <a href=\"https://wikipedia.org/wiki/Afonso_I_of_Portugal\" title=\"<PERSON><PERSON><PERSON> of Portugal\"><PERSON><PERSON><PERSON> I</a> defeat forces led by his mother <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Portugal\" title=\"<PERSON>, Countess of Portugal\"><PERSON></a> and her lover <a href=\"https://wikipedia.org/wiki/Fernando_P%C3%A9rez_de_Traba\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_S%C3%A3o_Mamede\" title=\"Battle of São Mamede\">Battle of São Mamede</a>, near <a href=\"https://wikipedia.org/wiki/Guimar%C3%A3es\" title=\"Guimarães\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>: Forces led by <a href=\"https://wikipedia.org/wiki/Afo<PERSON>_I_of_Portugal\" title=\"<PERSON><PERSON><PERSON> of Portugal\"><PERSON><PERSON><PERSON> I</a> defeat forces led by his mother <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Portugal\" title=\"<PERSON>, Countess of Portugal\"><PERSON></a> and her lover <a href=\"https://wikipedia.org/wiki/Fernando_P%C3%A9rez_de_Traba\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Battle of São Mamede", "link": "https://wikipedia.org/wiki/Battle_of_S%C3%A3o_Mamede"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Guimar%C3%A3es"}, {"title": "<PERSON><PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Portugal"}, {"title": "<PERSON>, Countess of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_of_Portugal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_P%C3%A9<PERSON>_<PERSON>_<PERSON>a"}]}, {"year": "1230", "text": "The Siege of Jaén begins, in the context of the Spanish Reconquista.", "html": "1230 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Ja%C3%A9n_(1230)\" title=\"Siege of Jaén (1230)\">Siege of Jaén</a> begins, in the context of the Spanish Reconquista.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Ja%C3%A9n_(1230)\" title=\"Siege of Jaén (1230)\">Siege of Jaén</a> begins, in the context of the Spanish Reconquista.", "links": [{"title": "Siege of Jaén (1230)", "link": "https://wikipedia.org/wiki/Siege_of_Ja%C3%A9n_(1230)"}]}, {"year": "1314", "text": "First War of Scottish Independence: The Battle of Bannockburn concludes with a decisive victory by Scottish forces led by <PERSON>.", "html": "1314 - <a href=\"https://wikipedia.org/wiki/First_War_of_Scottish_Independence\" title=\"First War of Scottish Independence\">First War of Scottish Independence</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Bannockburn\" title=\"Battle of Bannockburn\">Battle of Bannockburn</a> concludes with a decisive victory by Scottish forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_War_of_Scottish_Independence\" title=\"First War of Scottish Independence\">First War of Scottish Independence</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Bannockburn\" title=\"Battle of Bannockburn\">Battle of Bannockburn</a> concludes with a decisive victory by Scottish forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>.", "links": [{"title": "First War of Scottish Independence", "link": "https://wikipedia.org/wiki/First_War_of_Scottish_Independence"}, {"title": "Battle of Bannockburn", "link": "https://wikipedia.org/wiki/Battle_of_Bannockburn"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1340", "text": "Hundred Years' War: Battle of Sluys: The French fleet is almost completely destroyed by the English fleet commanded in person by King <PERSON>.", "html": "1340 - <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Sluys\" title=\"Battle of Sluys\">Battle of Sluys</a>: The French fleet is almost completely destroyed by the English fleet commanded in person by King <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_England\" title=\"<PERSON> III of England\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Sluys\" title=\"Battle of Sluys\">Battle of Sluys</a>: The French fleet is almost completely destroyed by the English fleet commanded in person by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_England\" title=\"<PERSON> III of England\"><PERSON></a>.", "links": [{"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}, {"title": "Battle of Sluys", "link": "https://wikipedia.org/wiki/Battle_of_Sluys"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1374", "text": "A sudden outbreak of St. John's Dance causes people in the streets of Aachen, Germany, to experience hallucinations and begin to jump and twitch uncontrollably until they collapse from exhaustion.", "html": "1374 - A sudden outbreak of <a href=\"https://wikipedia.org/wiki/Dancing_mania\" title=\"Dancing mania\"><PERSON>. John's Dance</a> causes people in the streets of <a href=\"https://wikipedia.org/wiki/Aachen\" title=\"Aachen\">Aachen</a>, Germany, to experience <a href=\"https://wikipedia.org/wiki/Hallucination\" title=\"Hallucination\">hallucinations</a> and begin to jump and twitch uncontrollably until they collapse from exhaustion.", "no_year_html": "A sudden outbreak of <a href=\"https://wikipedia.org/wiki/Dancing_mania\" title=\"Dancing mania\">St. John's Dance</a> causes people in the streets of <a href=\"https://wikipedia.org/wiki/Aachen\" title=\"Aachen\">Aachen</a>, Germany, to experience <a href=\"https://wikipedia.org/wiki/Hallucination\" title=\"Hallucination\">hallucinations</a> and begin to jump and twitch uncontrollably until they collapse from exhaustion.", "links": [{"title": "Dancing mania", "link": "https://wikipedia.org/wiki/Dancing_mania"}, {"title": "Aachen", "link": "https://wikipedia.org/wiki/Aachen"}, {"title": "Hallucination", "link": "https://wikipedia.org/wiki/Hallucination"}]}, {"year": "1497", "text": "<PERSON> lands in North America at Newfoundland leading the first European exploration of the region since the Vikings.", "html": "1497 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in North America at <a href=\"https://wikipedia.org/wiki/Newfoundland_(island)\" title=\"Newfoundland (island)\">Newfoundland</a> leading the first European exploration of the region since the <a href=\"https://wikipedia.org/wiki/Vikings\" title=\"Vikings\">Vikings</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in North America at <a href=\"https://wikipedia.org/wiki/Newfoundland_(island)\" title=\"Newfoundland (island)\">Newfoundland</a> leading the first European exploration of the region since the <a href=\"https://wikipedia.org/wiki/Vikings\" title=\"Vikings\">Vikings</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Newfoundland (island)", "link": "https://wikipedia.org/wiki/Newfoundland_(island)"}, {"title": "Vikings", "link": "https://wikipedia.org/wiki/Vikings"}]}, {"year": "1509", "text": "<PERSON> and <PERSON> of <PERSON> are crowned King and Queen of England.", "html": "1509 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> Aragon</a> are crowned King and Queen of England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> are crowned King and Queen of England.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1535", "text": "The Anabaptist state of Münster is conquered and disbanded.", "html": "1535 - The <a href=\"https://wikipedia.org/wiki/Anabaptist\" class=\"mw-redirect\" title=\"Anabaptist\">Anabaptist</a> state of <a href=\"https://wikipedia.org/wiki/M%C3%BCnster_Rebellion\" class=\"mw-redirect\" title=\"Münster Rebellion\">Münster</a> is conquered and disbanded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anabaptist\" class=\"mw-redirect\" title=\"Anabaptist\">Anabaptist</a> state of <a href=\"https://wikipedia.org/wiki/M%C3%BCnster_Rebellion\" class=\"mw-redirect\" title=\"Münster Rebellion\">Münster</a> is conquered and disbanded.", "links": [{"title": "Anabaptist", "link": "https://wikipedia.org/wiki/Anabaptist"}, {"title": "Münster Rebellion", "link": "https://wikipedia.org/wiki/M%C3%BCnster_Rebellion"}]}, {"year": "1540", "text": "English King <PERSON> commands his fourth wife, <PERSON> of Cleves, to leave the court.", "html": "1540 - English King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII\" title=\"<PERSON> VIII\"><PERSON></a> commands his fourth wife, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Cleves\"><PERSON></a>, to leave the court.", "no_year_html": "English King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VIII\"><PERSON></a> commands his fourth wife, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cleves\"><PERSON>es</a>, to leave the court.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Cleves", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1571", "text": "<PERSON> conquers Manila for Spain, modern day capital of the Philippines.", "html": "1571 - <a href=\"https://wikipedia.org/wiki/Miguel_L%C3%B3pez_de_Legazpi\" title=\"<PERSON>\"><PERSON></a> conquers <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a> for Spain, modern day capital of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miguel_L%C3%B3pez_de_Legazpi\" title=\"<PERSON>\"><PERSON></a> conquers <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a> for Spain, modern day capital of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_L%C3%B3pez_de_Legazpi"}, {"title": "Manila", "link": "https://wikipedia.org/wiki/Manila"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1593", "text": "The Dutch city of Geertruidenberg held by the Spanish, capitulates to a besieging Dutch and English army led by <PERSON> Nassau.", "html": "1593 - The Dutch city of <a href=\"https://wikipedia.org/wiki/Geertruidenberg\" title=\"Geertruidenberg\">Geertruidenberg</a> held by the Spanish, <a href=\"https://wikipedia.org/wiki/Siege_of_Geertruidenberg_(1593)\" title=\"Siege of Geertruidenberg (1593)\">capitulates to a besieging Dutch and English</a> army led by <a href=\"https://wikipedia.org/wiki/Maurice_<PERSON>_Nassau\" class=\"mw-redirect\" title=\"<PERSON> of Nassau\"><PERSON> of Nassau</a>.", "no_year_html": "The Dutch city of <a href=\"https://wikipedia.org/wiki/Geertruidenberg\" title=\"Geertruidenberg\">Geertruidenberg</a> held by the Spanish, <a href=\"https://wikipedia.org/wiki/Siege_of_Geertruidenberg_(1593)\" title=\"Siege of Geertruidenberg (1593)\">capitulates to a besieging Dutch and English</a> army led by <a href=\"https://wikipedia.org/wiki/Maurice_<PERSON>_Nassau\" class=\"mw-redirect\" title=\"<PERSON> of Nassau\"><PERSON> of Nassau</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "Siege of Geertruidenberg (1593)", "link": "https://wikipedia.org/wiki/Siege_of_<PERSON><PERSON><PERSON>idenberg_(1593)"}, {"title": "<PERSON> of Nassau", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nassau"}]}, {"year": "1604", "text": "<PERSON> encounters the mouth of the Saint John River, site of Reversing Falls and the present-day city of Saint John, New Brunswick, Canada.", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> encounters the mouth of the <a href=\"https://wikipedia.org/wiki/Saint_John_River_(Bay_of_Fundy)\" title=\"Saint John River (Bay of Fundy)\">Saint John River</a>, site of <a href=\"https://wikipedia.org/wiki/Reversing_Falls\" title=\"Reversing Falls\">Reversing Falls</a> and the present-day city of <a href=\"https://wikipedia.org/wiki/Saint_John,_New_Brunswick\" title=\"Saint John, New Brunswick\">Saint John, New Brunswick</a>, Canada.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> encounters the mouth of the <a href=\"https://wikipedia.org/wiki/Saint_John_River_(Bay_of_Fundy)\" title=\"Saint John River (Bay of Fundy)\">Saint John River</a>, site of <a href=\"https://wikipedia.org/wiki/Reversing_Falls\" title=\"Reversing Falls\">Reversing Falls</a> and the present-day city of <a href=\"https://wikipedia.org/wiki/Saint_John,_New_Brunswick\" title=\"Saint John, New Brunswick\">Saint John, New Brunswick</a>, Canada.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Saint John River (Bay of Fundy)", "link": "https://wikipedia.org/wiki/Saint_John_River_(Bay_of_Fundy)"}, {"title": "Reversing Falls", "link": "https://wikipedia.org/wiki/Reversing_Falls"}, {"title": "Saint John, New Brunswick", "link": "https://wikipedia.org/wiki/<PERSON>_John,_New_Brunswick"}]}, {"year": "1622", "text": "Battle of Macau: The Dutch make a failed attempt to capture Macau.", "html": "1622 - <a href=\"https://wikipedia.org/wiki/Battle_of_Macau\" title=\"Battle of Macau\">Battle of Macau</a>: The Dutch make a failed attempt to capture <a href=\"https://wikipedia.org/wiki/Macau\" title=\"Macau\">Macau</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Macau\" title=\"Battle of Macau\">Battle of Macau</a>: The Dutch make a failed attempt to capture <a href=\"https://wikipedia.org/wiki/Macau\" title=\"Macau\">Macau</a>.", "links": [{"title": "Battle of Macau", "link": "https://wikipedia.org/wiki/Battle_of_Macau"}, {"title": "Macau", "link": "https://wikipedia.org/wiki/Macau"}]}, {"year": "1663", "text": "The Spanish garrison of Évora capitulates, following the Portuguese victory at the Battle of Ameixial.", "html": "1663 - The Spanish garrison of <a href=\"https://wikipedia.org/wiki/%C3%89vora\" title=\"Évora\">Évora</a> capitulates, following the Portuguese victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ameixial\" title=\"Battle of Ameixial\">Battle of Ameixial</a>.", "no_year_html": "The Spanish garrison of <a href=\"https://wikipedia.org/wiki/%C3%89vora\" title=\"Évora\">Évora</a> capitulates, following the Portuguese victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ameixial\" title=\"Battle of Ameixial\">Battle of Ameixial</a>.", "links": [{"title": "É<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89vora"}, {"title": "Battle of Ameixial", "link": "https://wikipedia.org/wiki/Battle_of_Ameixial"}]}, {"year": "1717", "text": "The Premier Grand Lodge of England is founded in London, the first Masonic Grand Lodge in the world (now the United Grand Lodge of England).", "html": "1717 - The <a href=\"https://wikipedia.org/wiki/Premier_Grand_Lodge_of_England\" title=\"Premier Grand Lodge of England\">Premier Grand Lodge of England</a> is founded in London, the first <a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">Masonic</a> <a href=\"https://wikipedia.org/wiki/Grand_Lodge\" title=\"Grand Lodge\">Grand Lodge</a> in the world (now the <a href=\"https://wikipedia.org/wiki/United_Grand_Lodge_of_England\" title=\"United Grand Lodge of England\">United Grand Lodge of England</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Premier_Grand_Lodge_of_England\" title=\"Premier Grand Lodge of England\">Premier Grand Lodge of England</a> is founded in London, the first <a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">Masonic</a> <a href=\"https://wikipedia.org/wiki/Grand_Lodge\" title=\"Grand Lodge\">Grand Lodge</a> in the world (now the <a href=\"https://wikipedia.org/wiki/United_Grand_Lodge_of_England\" title=\"United Grand Lodge of England\">United Grand Lodge of England</a>).", "links": [{"title": "Premier Grand Lodge of England", "link": "https://wikipedia.org/wiki/Premier_Grand_Lodge_of_England"}, {"title": "Freemasonry", "link": "https://wikipedia.org/wiki/Freemasonry"}, {"title": "Grand Lodge", "link": "https://wikipedia.org/wiki/Grand_Lodge"}, {"title": "United Grand Lodge of England", "link": "https://wikipedia.org/wiki/United_Grand_Lodge_of_England"}]}, {"year": "1724", "text": "On the Feast of <PERSON><PERSON> John the Baptist, <PERSON> leads the first performance of his Christ unser Herr zum Jordan kam, BWV 7, the third cantata of his chorale cantata cycle.", "html": "1724 - On the <a href=\"https://wikipedia.org/wiki/Nativity_of_St_John_the_Baptist\" class=\"mw-redirect\" title=\"Nativity of St John the Baptist\">Feast of St. John the Baptist</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Bach</a> leads the first performance of his <i><span title=\"German-language text\"><i lang=\"de\"><a href=\"https://wikipedia.org/wiki/Christ_unser_Herr_zum_Jordan_kam,_BWV_7\" title=\"Christ unser Herr zum Jordan kam, BWV 7\"><PERSON> unser Herr zum Jordan kam</a></i></span></i>, BWV<span class=\"nowrap\"> </span>7, the third cantata of his <a href=\"https://wikipedia.org/wiki/Chorale_cantata_cycle\" title=\"Chorale cantata cycle\">chorale cantata cycle</a>.", "no_year_html": "On the <a href=\"https://wikipedia.org/wiki/Nativity_of_<PERSON>_John_the_Baptist\" class=\"mw-redirect\" title=\"Nativity of St John the Baptist\">Feast of St. John the Baptist</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of his <i><span title=\"German-language text\"><i lang=\"de\"><a href=\"https://wikipedia.org/wiki/Christ_unser_Herr_zum_Jordan_kam,_BWV_7\" title=\"<PERSON> unser Herr zum Jordan kam, BWV 7\"><PERSON> unser Herr zum Jordan kam</a></i></span></i>, BWV<span class=\"nowrap\"> </span>7, the third cantata of his <a href=\"https://wikipedia.org/wiki/Chorale_cantata_cycle\" title=\"Chorale cantata cycle\">chorale cantata cycle</a>.", "links": [{"title": "Nativity of St John the Baptist", "link": "https://wikipedia.org/wiki/Nativity_of_St_John_the_Baptist"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> unser Herr zum Jordan kam, BWV 7", "link": "https://wikipedia.org/wiki/<PERSON>_un<PERSON>_<PERSON>_<PERSON>_<PERSON>_kam,_<PERSON>_7"}, {"title": "Chorale cantata cycle", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_cantata_cycle"}]}, {"year": "1762", "text": "Battle of Wilhelmsthal: The British-Hanoverian army of Ferdinand of Brunswick defeats French forces in Westphalia.", "html": "1762 - <a href=\"https://wikipedia.org/wiki/Battle_of_Wilhelmsthal\" title=\"Battle of Wilhelmsthal\">Battle of Wilhelmsthal</a>: The British-Hanoverian army of Ferdinand of Brunswick defeats French forces in Westphalia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Wilhelmsthal\" title=\"Battle of Wilhelmsthal\">Battle of Wilhelmsthal</a>: The British-Hanoverian army of <PERSON> of Brunswick defeats French forces in Westphalia.", "links": [{"title": "Battle of Wilhelmsthal", "link": "https://wikipedia.org/wiki/Battle_of_Wilhelmsthal"}]}, {"year": "1779", "text": "American Revolutionary War: The Great Siege of Gibraltar begins.", "html": "1779 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Gibraltar\" title=\"Great Siege of Gibraltar\">Great Siege of Gibraltar</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Gibraltar\" title=\"Great Siege of Gibraltar\">Great Siege of Gibraltar</a> begins.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Great Siege of Gibraltar", "link": "https://wikipedia.org/wiki/Great_Siege_of_Gibraltar"}]}, {"year": "1793", "text": "The first Republican constitution in France is adopted.", "html": "1793 - The <a href=\"https://wikipedia.org/wiki/French_Constitution_of_1793\" title=\"French Constitution of 1793\">first Republican constitution</a> in France is adopted.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_Constitution_of_1793\" title=\"French Constitution of 1793\">first Republican constitution</a> in France is adopted.", "links": [{"title": "French Constitution of 1793", "link": "https://wikipedia.org/wiki/French_Constitution_of_1793"}]}, {"year": "1812", "text": "Napoleonic Wars: Napoleon's Grande Armée crosses the Neman river beginning the invasion of Russia.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: Napoleon's <a href=\"https://wikipedia.org/wiki/Grande_Arm%C3%A9e\" title=\"Grande Armée\">Grande Armée</a> crosses the <a href=\"https://wikipedia.org/wiki/Neman_(river)\" class=\"mw-redirect\" title=\"Neman (river)\">Neman</a> river beginning the <a href=\"https://wikipedia.org/wiki/French_invasion_of_Russia\" title=\"French invasion of Russia\">invasion</a> of Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: Napoleon's <a href=\"https://wikipedia.org/wiki/Grande_Arm%C3%A9e\" title=\"Grande Armée\">Grande Armée</a> crosses the <a href=\"https://wikipedia.org/wiki/Neman_(river)\" class=\"mw-redirect\" title=\"Neman (river)\">Neman</a> river beginning the <a href=\"https://wikipedia.org/wiki/French_invasion_of_Russia\" title=\"French invasion of Russia\">invasion</a> of Russia.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Grande Armée", "link": "https://wikipedia.org/wiki/Grande_Arm%C3%A9e"}, {"title": "Neman (river)", "link": "https://wikipedia.org/wiki/Neman_(river)"}, {"title": "French invasion of Russia", "link": "https://wikipedia.org/wiki/French_invasion_of_Russia"}]}, {"year": "1813", "text": "Battle of Beaver Dams: A British and Indian combined force defeats the United States Army.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Battle_of_Beaver_Dams\" title=\"Battle of Beaver Dams\">Battle of Beaver Dams</a>: A British and Indian combined force defeats the United States Army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Beaver_Dams\" title=\"Battle of Beaver Dams\">Battle of Beaver Dams</a>: A British and Indian combined force defeats the United States Army.", "links": [{"title": "Battle of Beaver Dams", "link": "https://wikipedia.org/wiki/Battle_of_Beaver_Dams"}]}, {"year": "1821", "text": "Battle of Carabobo: Decisive battle in the war of independence of Venezuela from Spain.", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Battle_of_Carabobo\" title=\"Battle of Carabobo\">Battle of Carabobo</a>: Decisive battle in the war of independence of <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a> from Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Carabobo\" title=\"Battle of Carabobo\">Battle of Carabobo</a>: Decisive battle in the war of independence of <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a> from Spain.", "links": [{"title": "Battle of Carabobo", "link": "https://wikipedia.org/wiki/Battle_of_Carabobo"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}]}, {"year": "1859", "text": "Battle of Solferino (Battle of the Three Sovereigns): Sardinia and France defeat Austria in Solferino, northern Italy.", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Battle_of_Solferino\" title=\"Battle of Solferino\">Battle of Solferino</a> (Battle of the Three Sovereigns): <a href=\"https://wikipedia.org/wiki/Sardinia\" title=\"Sardinia\">Sardinia</a> and France defeat Austria in <a href=\"https://wikipedia.org/wiki/Solferino\" title=\"Solferino\">Solferino</a>, northern Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Solferino\" title=\"Battle of Solferino\">Battle of Solferino</a> (Battle of the Three Sovereigns): <a href=\"https://wikipedia.org/wiki/Sardinia\" title=\"Sardinia\">Sardinia</a> and France defeat Austria in <a href=\"https://wikipedia.org/wiki/Solferino\" title=\"Solferino\">Solferino</a>, northern Italy.", "links": [{"title": "Battle of Solferino", "link": "https://wikipedia.org/wiki/Battle_of_Solferino"}, {"title": "Sardinia", "link": "https://wikipedia.org/wiki/Sardinia"}, {"title": "Solfer<PERSON>", "link": "https://wikipedia.org/wiki/Solferino"}]}, {"year": "1866", "text": "Battle of Custoza: An Austrian army defeats the Italian army during the Austro-Prussian War.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Battle_of_Custoza_(1866)\" title=\"Battle of Custoza (1866)\">Battle of Custoza</a>: An <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> army defeats the Italian army during the <a href=\"https://wikipedia.org/wiki/Austro-Prussian_War\" title=\"Austro-Prussian War\">Austro-Prussian War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Custoza_(1866)\" title=\"Battle of Custoza (1866)\">Battle of Custoza</a>: An <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> army defeats the Italian army during the <a href=\"https://wikipedia.org/wiki/Austro-Prussian_War\" title=\"Austro-Prussian War\">Austro-Prussian War</a>.", "links": [{"title": "Battle of Custoza (1866)", "link": "https://wikipedia.org/wiki/Battle_of_Custoza_(1866)"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "Austro-Prussian War", "link": "https://wikipedia.org/wiki/Austro-Prussian_War"}]}, {"year": "1880", "text": "First performance of O Canada at the Congrès national des Canadiens-Français. The song would later become the national anthem of Canada.", "html": "1880 - First performance of <i><a href=\"https://wikipedia.org/wiki/O_Canada\" title=\"O Canada\">O Canada</a></i> at the Congrès national des Canadiens-Français. The song would later become the <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a> of Canada.", "no_year_html": "First performance of <i><a href=\"https://wikipedia.org/wiki/O_Canada\" title=\"O Canada\">O Canada</a></i> at the Congrès national des Canadiens-Français. The song would later become the <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a> of Canada.", "links": [{"title": "O Canada", "link": "https://wikipedia.org/wiki/O_Canada"}, {"title": "National anthem", "link": "https://wikipedia.org/wiki/National_anthem"}]}, {"year": "1894", "text": "<PERSON>, President of France, is assassinated by <PERSON><PERSON>.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, President of France, is assassinated by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, President of France, is assassinated by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>not"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "Greece and Serbia annul their alliance with Bulgaria.", "html": "1913 - Greece and <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a> annul their alliance with <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>.", "no_year_html": "Greece and <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a> annul their alliance with <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>.", "links": [{"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}]}, {"year": "1916", "text": "<PERSON> becomes the first female film star to sign a million-dollar contract.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female film star to sign a million-dollar contract.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female film star to sign a million-dollar contract.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "First airmail service in Canada from Montreal to Toronto.", "html": "1918 - First <a href=\"https://wikipedia.org/wiki/Airmail\" title=\"Airmail\">airmail</a> service in Canada from <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a> to <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a>.", "no_year_html": "First <a href=\"https://wikipedia.org/wiki/Airmail\" title=\"Airmail\">airmail</a> service in Canada from <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a> to <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a>.", "links": [{"title": "Airmail", "link": "https://wikipedia.org/wiki/Airmail"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}, {"title": "Toronto", "link": "https://wikipedia.org/wiki/Toronto"}]}, {"year": "1922", "text": "The American Professional Football Association is renamed the National Football League.", "html": "1922 - The American Professional Football Association is renamed the <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a>.", "no_year_html": "The American Professional Football Association is renamed the <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a>.", "links": [{"title": "National Football League", "link": "https://wikipedia.org/wiki/National_Football_League"}]}, {"year": "1932", "text": "A bloodless revolution instigated by the People's Party ends the absolute power of King <PERSON><PERSON><PERSON><PERSON><PERSON> of Siam (now Thailand).", "html": "1932 - A bloodless <a href=\"https://wikipedia.org/wiki/Siamese_revolution_of_1932\" title=\"Siamese revolution of 1932\">revolution</a> instigated by the <a href=\"https://wikipedia.org/wiki/Khana_Ratsadon\" title=\"Khana Ratsadon\">People's Party</a> ends the absolute power of King <a href=\"https://wikipedia.org/wiki/Prajadhipok\" title=\"Prajadhipok\">Prajadhipok</a> of <a href=\"https://wikipedia.org/wiki/Rattanakosin_Kingdom\" class=\"mw-redirect\" title=\"Rattanakosin Kingdom\">Siam</a> (now <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>).", "no_year_html": "A bloodless <a href=\"https://wikipedia.org/wiki/Siamese_revolution_of_1932\" title=\"Siamese revolution of 1932\">revolution</a> instigated by the <a href=\"https://wikipedia.org/wiki/Khana_Ratsadon\" title=\"Khana Ratsadon\">People's Party</a> ends the absolute power of King <a href=\"https://wikipedia.org/wiki/Praj<PERSON>hipok\" title=\"Prajadhipok\">Prajadhipok</a> of <a href=\"https://wikipedia.org/wiki/Rattanakosin_Kingdom\" class=\"mw-redirect\" title=\"Rattanakosin Kingdom\">Siam</a> (now <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>).", "links": [{"title": "Siamese revolution of 1932", "link": "https://wikipedia.org/wiki/Siamese_revolution_of_1932"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Praj<PERSON>hipok"}, {"title": "Rattanakosin Kingdom", "link": "https://wikipedia.org/wiki/Rattanakosin_Kingdom"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}]}, {"year": "1938", "text": "Pieces of a meteorite land near Chicora, Pennsylvania. The meteorite is estimated to have weighed 450 metric tons when it hit the Earth's atmosphere and exploded.", "html": "1938 - Pieces of a <a href=\"https://wikipedia.org/wiki/Meteorite\" title=\"Meteorite\">meteorite</a> land near <a href=\"https://wikipedia.org/wiki/Chicora,_Pennsylvania\" title=\"Chicora, Pennsylvania\">Chicora, Pennsylvania</a>. The meteorite is estimated to have weighed 450 <a href=\"https://wikipedia.org/wiki/Metric_ton\" class=\"mw-redirect\" title=\"Metric ton\">metric tons</a> when it hit the Earth's atmosphere and exploded.", "no_year_html": "Pieces of a <a href=\"https://wikipedia.org/wiki/Meteorite\" title=\"Meteorite\">meteorite</a> land near <a href=\"https://wikipedia.org/wiki/Chicora,_Pennsylvania\" title=\"Chicora, Pennsylvania\">Chicora, Pennsylvania</a>. The meteorite is estimated to have weighed 450 <a href=\"https://wikipedia.org/wiki/Metric_ton\" class=\"mw-redirect\" title=\"Metric ton\">metric tons</a> when it hit the Earth's atmosphere and exploded.", "links": [{"title": "Meteorite", "link": "https://wikipedia.org/wiki/Meteorite"}, {"title": "Chicora, Pennsylvania", "link": "https://wikipedia.org/wiki/Chicora,_Pennsylvania"}, {"title": "Metric ton", "link": "https://wikipedia.org/wiki/Metric_ton"}]}, {"year": "1939", "text": "Siam is renamed Thailand by <PERSON><PERSON><PERSON>, the country's third prime minister.", "html": "1939 - Siam is renamed Thailand by <a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON>khram\" title=\"<PERSON><PERSON><PERSON>khram\"><PERSON><PERSON><PERSON></a>, the country's third <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">prime minister</a>.", "no_year_html": "Siam is renamed Thailand by <a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON>khram\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the country's third <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">prime minister</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ram"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1940", "text": "World War II: Operation Collar, the first British Commando raid on occupied France, by No 11 Independent Company.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Collar_(commando_raid)\" title=\"Operation Collar (commando raid)\">Operation Collar</a>, the first <a href=\"https://wikipedia.org/wiki/British_Commandos\" class=\"mw-redirect\" title=\"British Commandos\">British Commando</a> raid on occupied France, by No 11 <a href=\"https://wikipedia.org/wiki/Independent_Company\" class=\"mw-redirect\" title=\"Independent Company\">Independent Company</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Collar_(commando_raid)\" title=\"Operation Collar (commando raid)\">Operation Collar</a>, the first <a href=\"https://wikipedia.org/wiki/British_Commandos\" class=\"mw-redirect\" title=\"British Commandos\">British Commando</a> raid on occupied France, by No 11 <a href=\"https://wikipedia.org/wiki/Independent_Company\" class=\"mw-redirect\" title=\"Independent Company\">Independent Company</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Collar (commando raid)", "link": "https://wikipedia.org/wiki/Operation_Collar_(commando_raid)"}, {"title": "British Commandos", "link": "https://wikipedia.org/wiki/British_Commandos"}, {"title": "Independent Company", "link": "https://wikipedia.org/wiki/Independent_Company"}]}, {"year": "1943", "text": "US military police attempt to arrest a black soldier in Bamber Bridge, England, sparking the Battle of Bamber Bridge mutiny that leaves one dead and seven wounded.", "html": "1943 - US military police attempt to arrest a black soldier in <a href=\"https://wikipedia.org/wiki/Bamber_Bridge\" title=\"Bamber Bridge\">Bamber Bridge</a>, England, sparking the <a href=\"https://wikipedia.org/wiki/Battle_of_Bamber_Bridge\" title=\"Battle of Bamber Bridge\">Battle of Bamber Bridge</a> mutiny that leaves one dead and seven wounded.", "no_year_html": "US military police attempt to arrest a black soldier in <a href=\"https://wikipedia.org/wiki/Bamber_Bridge\" title=\"Bamber Bridge\">Bamber Bridge</a>, England, sparking the <a href=\"https://wikipedia.org/wiki/Battle_of_Bamber_Bridge\" title=\"Battle of Bamber Bridge\">Battle of Bamber Bridge</a> mutiny that leaves one dead and seven wounded.", "links": [{"title": "Bamber Bridge", "link": "https://wikipedia.org/wiki/Bamber_Bridge"}, {"title": "Battle of Bamber Bridge", "link": "https://wikipedia.org/wiki/Battle_of_Bamber_Bridge"}]}, {"year": "1945", "text": "The first Victory Day Parade takes place on Red Square in Moscow, Soviet Union, symbolizing the victory of the Soviet Union over Nazi Germany.", "html": "1945 - The first <a href=\"https://wikipedia.org/wiki/Victory_Day_Parade\" class=\"mw-redirect\" title=\"Victory Day Parade\">Victory Day Parade</a> takes place on <a href=\"https://wikipedia.org/wiki/Red_Square\" title=\"Red Square\">Red Square</a> in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, symbolizing the victory of the Soviet Union over <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Victory_Day_Parade\" class=\"mw-redirect\" title=\"Victory Day Parade\">Victory Day Parade</a> takes place on <a href=\"https://wikipedia.org/wiki/Red_Square\" title=\"Red Square\">Red Square</a> in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, symbolizing the victory of the Soviet Union over <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>.", "links": [{"title": "Victory Day Parade", "link": "https://wikipedia.org/wiki/Victory_Day_Parade"}, {"title": "Red Square", "link": "https://wikipedia.org/wiki/Red_Square"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1947", "text": "<PERSON> makes the first widely reported UFO sighting near Mount Rainier, Washington.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes the first widely reported <a href=\"https://wikipedia.org/wiki/<PERSON>_UFO_sighting\" title=\"<PERSON> UFO sighting\">UFO sighting</a> near <a href=\"https://wikipedia.org/wiki/Mount_Rainier\" title=\"Mount Rainier\">Mount Rainier</a>, <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes the first widely reported <a href=\"https://wikipedia.org/wiki/<PERSON>_UFO_sighting\" title=\"<PERSON> UFO sighting\">UFO sighting</a> near <a href=\"https://wikipedia.org/wiki/Mount_Rainier\" title=\"Mount Rainier\">Mount Rainier</a>, <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON> sighting", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_sighting"}, {"title": "Mount Rainier", "link": "https://wikipedia.org/wiki/Mount_Rainier"}, {"title": "Washington (state)", "link": "https://wikipedia.org/wiki/Washington_(state)"}]}, {"year": "1948", "text": "Cold War: Start of the Berlin Blockade: The Soviet Union makes overland travel between West Germany and West Berlin impossible.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Start of the <a href=\"https://wikipedia.org/wiki/Berlin_Blockade\" title=\"Berlin Blockade\">Berlin Blockade</a>: The Soviet Union makes overland travel between West Germany and <a href=\"https://wikipedia.org/wiki/West_Berlin\" title=\"West Berlin\">West Berlin</a> impossible.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Start of the <a href=\"https://wikipedia.org/wiki/Berlin_Blockade\" title=\"Berlin Blockade\">Berlin Blockade</a>: The Soviet Union makes overland travel between West Germany and <a href=\"https://wikipedia.org/wiki/West_Berlin\" title=\"West Berlin\">West Berlin</a> impossible.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Berlin Blockade", "link": "https://wikipedia.org/wiki/Berlin_Blockade"}, {"title": "West Berlin", "link": "https://wikipedia.org/wiki/West_Berlin"}]}, {"year": "1949", "text": "The first television western, <PERSON><PERSON><PERSON>, starring <PERSON>, is aired on NBC.", "html": "1949 - The first <a href=\"https://wikipedia.org/wiki/Westerns_on_television\" title=\"Westerns on television\">television western</a>, <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cassidy\" title=\"Hopalo<PERSON> Cassidy\"><PERSON><PERSON><PERSON></a></i>, starring <a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, is aired on <a href=\"https://wikipedia.org/wiki/NBC\" title=\"NBC\">NBC</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Westerns_on_television\" title=\"Westerns on television\">television western</a>, <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cassidy\" title=\"Hopalo<PERSON> Cassidy\"><PERSON><PERSON><PERSON></a></i>, starring <a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, is aired on <a href=\"https://wikipedia.org/wiki/NBC\" title=\"NBC\">NBC</a>.", "links": [{"title": "Westerns on television", "link": "https://wikipedia.org/wiki/Westerns_on_television"}, {"title": "Hopalong Cassidy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}, {"title": "NBC", "link": "https://wikipedia.org/wiki/NBC"}]}, {"year": "1950", "text": "Apartheid: In South Africa, the Group Areas Act is passed, formally segregating races.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a>: In South Africa, the <a href=\"https://wikipedia.org/wiki/Group_Areas_Act\" title=\"Group Areas Act\">Group Areas Act</a> is passed, formally segregating races.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a>: In South Africa, the <a href=\"https://wikipedia.org/wiki/Group_Areas_Act\" title=\"Group Areas Act\">Group Areas Act</a> is passed, formally segregating races.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}, {"title": "Group Areas Act", "link": "https://wikipedia.org/wiki/Group_Areas_Act"}]}, {"year": "1954", "text": "First Indochina War: Battle of Mang Yang Pass: Viet Minh troops belonging to the 803rd Regiment ambush G.M. 100 of France in An Khê.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Mang_Yang_Pass\" title=\"Battle of Mang Yang Pass\">Battle of Mang Yang Pass</a>: <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\"><PERSON><PERSON> Minh</a> troops belonging to the 803rd Regiment ambush G.M. 100 of France in <a href=\"https://wikipedia.org/wiki/An_Kh%C3%AA\" title=\"An Khê\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Mang_Yang_Pass\" title=\"Battle of Mang Yang Pass\">Battle of Mang Yang Pass</a>: <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\">Viet Minh</a> troops belonging to the 803rd Regiment ambush G.M. 100 of France in <a href=\"https://wikipedia.org/wiki/An_Kh%C3%AA\" title=\"An Khê\"><PERSON></a>.", "links": [{"title": "First Indochina War", "link": "https://wikipedia.org/wiki/First_Indochina_War"}, {"title": "Battle of Mang Yang Pass", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>g_<PERSON>_Pass"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viet_Minh"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/An_Kh%C3%AA"}]}, {"year": "1957", "text": "In Roth v. United States, the U.S. Supreme Court rules that obscenity is not protected by the First Amendment.", "html": "1957 - In <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v._United_States\" title=\"Roth v. United States\"><PERSON> v. United States</a></i>, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules that obscenity is not protected by the <a href=\"https://wikipedia.org/wiki/First_Amendment_to_the_United_States_Constitution\" title=\"First Amendment to the United States Constitution\">First Amendment</a>.", "no_year_html": "In <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v._United_States\" title=\"Roth v. United States\"><PERSON> v. United States</a></i>, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules that obscenity is not protected by the <a href=\"https://wikipedia.org/wiki/First_Amendment_to_the_United_States_Constitution\" title=\"First Amendment to the United States Constitution\">First Amendment</a>.", "links": [{"title": "<PERSON> v. United States", "link": "https://wikipedia.org/wiki/Roth_v._United_States"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "First Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/First_Amendment_to_the_United_States_Constitution"}]}, {"year": "1960", "text": "Venezuelan President <PERSON><PERSON><PERSON><PERSON> is injured in an assassination attempt.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuelan</a> President <a href=\"https://wikipedia.org/wiki/R%C3%B3mu<PERSON>_Betancourt\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is injured in an <a href=\"https://wikipedia.org/wiki/Assassination_attempt_of_R%C3%B3mulo_Betancourt\" class=\"mw-redirect\" title=\"Assassination attempt of <PERSON><PERSON><PERSON><PERSON>\">assassination attempt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuelan</a> President <a href=\"https://wikipedia.org/wiki/R%C3%B3mulo_Betancourt\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is injured in an <a href=\"https://wikipedia.org/wiki/Assassination_attempt_of_R%C3%B3mulo_Betancourt\" class=\"mw-redirect\" title=\"Assassination attempt of <PERSON><PERSON><PERSON><PERSON>\">assassination attempt</a>.", "links": [{"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%B3mu<PERSON>_Betancourt"}, {"title": "Assassination attempt of <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assassination_attempt_of_R%C3%B3mulo_Betancourt"}]}, {"year": "1963", "text": "The United Kingdom grants Zanzibar internal self-government.", "html": "1963 - The United Kingdom grants <a href=\"https://wikipedia.org/wiki/Zanzibar\" title=\"Zanzibar\">Zanzibar</a> internal self-government.", "no_year_html": "The United Kingdom grants <a href=\"https://wikipedia.org/wiki/Zanzibar\" title=\"Zanzibar\">Zanzibar</a> internal self-government.", "links": [{"title": "Zanzibar", "link": "https://wikipedia.org/wiki/Zanzibar"}]}, {"year": "1973", "text": "The UpStairs Lounge arson attack takes place at a gay bar located on the second floor of the three-story building at 141 Chartres Street in the French Quarter of New Orleans, Louisiana, US. Thirty-two people die as a result of fire or smoke inhalation.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/UpStairs_Lounge_arson_attack\" title=\"UpStairs Lounge arson attack\">UpStairs Lounge arson attack</a> takes place at a gay bar located on the second floor of the three-story building at 141 Chartres Street in the French Quarter of New Orleans, Louisiana, US. Thirty-two people die as a result of fire or smoke inhalation.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/UpStairs_Lounge_arson_attack\" title=\"UpStairs Lounge arson attack\">UpStairs Lounge arson attack</a> takes place at a gay bar located on the second floor of the three-story building at 141 Chartres Street in the French Quarter of New Orleans, Louisiana, US. Thirty-two people die as a result of fire or smoke inhalation.", "links": [{"title": "UpStairs Lounge arson attack", "link": "https://wikipedia.org/wiki/UpStairs_Lounge_arson_attack"}]}, {"year": "1975", "text": "Eastern Air Lines Flight 66 encounters severe wind shear and crashes on final approach to New York's JFK Airport killing 113 of the 124 passengers on board, making it the deadliest U.S. plane crash at the time. This accident led to decades of research into downburst and microburst phenomena and their effects on aircraft.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_66\" title=\"Eastern Air Lines Flight 66\">Eastern Air Lines Flight 66</a> encounters severe wind shear and crashes on final approach to New York's <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">JFK Airport</a> killing 113 of the 124 passengers on board, making it the deadliest U.S. plane crash at the time. This accident led to decades of research into downburst and microburst phenomena and their effects on aircraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_66\" title=\"Eastern Air Lines Flight 66\">Eastern Air Lines Flight 66</a> encounters severe wind shear and crashes on final approach to New York's <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_International_Airport\" title=\"John F. Kennedy International Airport\">JFK Airport</a> killing 113 of the 124 passengers on board, making it the deadliest U.S. plane crash at the time. This accident led to decades of research into downburst and microburst phenomena and their effects on aircraft.", "links": [{"title": "Eastern Air Lines Flight 66", "link": "https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_66"}, {"title": "<PERSON> International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Kennedy_International_Airport"}]}, {"year": "1981", "text": "The Humber Bridge opens to traffic, connecting Yorkshire and Lincolnshire. It remained the world's longest bridge span for 17 years.", "html": "1981 - The <a href=\"https://wikipedia.org/wiki/Humber_Bridge\" title=\"Humber Bridge\">Humber Bridge</a> opens to traffic, connecting <a href=\"https://wikipedia.org/wiki/Yorkshire\" title=\"Yorkshire\">Yorkshire</a> and <a href=\"https://wikipedia.org/wiki/Lincolnshire\" title=\"Lincolnshire\">Lincolnshire</a>. It remained the world's longest bridge span for 17 years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Humber_Bridge\" title=\"Humber Bridge\">Humber Bridge</a> opens to traffic, connecting <a href=\"https://wikipedia.org/wiki/Yorkshire\" title=\"Yorkshire\">Yorkshire</a> and <a href=\"https://wikipedia.org/wiki/Lincolnshire\" title=\"Lincolnshire\">Lincolnshire</a>. It remained the world's longest bridge span for 17 years.", "links": [{"title": "Humber Bridge", "link": "https://wikipedia.org/wiki/Humber_Bridge"}, {"title": "Yorkshire", "link": "https://wikipedia.org/wiki/Yorkshire"}, {"title": "Lincolnshire", "link": "https://wikipedia.org/wiki/Lincolnshire"}]}, {"year": "1982", "text": "\"The Jakarta Incident\": British Airways Flight 009 flies into a cloud of volcanic ash thrown up by the eruption of Mount Galunggung, resulting in the failure of all four engines.", "html": "1982 - \"The Jakarta Incident\": <a href=\"https://wikipedia.org/wiki/British_Airways_Flight_009\" title=\"British Airways Flight 009\">British Airways Flight 009</a> flies into a cloud of volcanic ash thrown up by the eruption of <a href=\"https://wikipedia.org/wiki/Mount_Galunggung\" class=\"mw-redirect\" title=\"Mount Galunggung\">Mount Galunggung</a>, resulting in the failure of all four engines.", "no_year_html": "\"The Jakarta Incident\": <a href=\"https://wikipedia.org/wiki/British_Airways_Flight_009\" title=\"British Airways Flight 009\">British Airways Flight 009</a> flies into a cloud of volcanic ash thrown up by the eruption of <a href=\"https://wikipedia.org/wiki/Mount_Galunggung\" class=\"mw-redirect\" title=\"Mount Galunggung\">Mount Galunggung</a>, resulting in the failure of all four engines.", "links": [{"title": "British Airways Flight 009", "link": "https://wikipedia.org/wiki/British_Airways_Flight_009"}, {"title": "Mount Galunggung", "link": "https://wikipedia.org/wiki/Mount_Galunggung"}]}, {"year": "1989", "text": "<PERSON> succeeds <PERSON> to become the General Secretary of the Chinese Communist Party after the 1989 Tiananmen Square protests and massacre.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to become the <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> after the <a href=\"https://wikipedia.org/wiki/1989_Tiananmen_Square_protests_and_massacre\" title=\"1989 Tiananmen Square protests and massacre\">1989 Tiananmen Square protests and massacre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to become the <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> after the <a href=\"https://wikipedia.org/wiki/1989_Tiananmen_Square_protests_and_massacre\" title=\"1989 Tiananmen Square protests and massacre\">1989 Tiananmen Square protests and massacre</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General Secretary of the Chinese Communist Party", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party"}, {"title": "1989 Tiananmen Square protests and massacre", "link": "https://wikipedia.org/wiki/1989_Tiananmen_Square_protests_and_massacre"}]}, {"year": "1994", "text": "A Boeing B-52 Stratofortress crashes at Fairchild Air Force Base near Spokane, Washington, killing four.", "html": "1994 - A <a href=\"https://wikipedia.org/wiki/Boeing_B-52_Stratofortress\" title=\"Boeing B-52 Stratofortress\">Boeing B-52 Stratofortress</a> <a href=\"https://wikipedia.org/wiki/1994_Fairchild_Air_Force_Base_B-52_crash\" title=\"1994 Fairchild Air Force Base B-52 crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Fairchild_Air_Force_Base\" title=\"Fairchild Air Force Base\">Fairchild Air Force Base</a> near <a href=\"https://wikipedia.org/wiki/Spokane,_Washington\" title=\"Spokane, Washington\">Spokane, Washington</a>, killing four.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Boeing_B-52_Stratofortress\" title=\"Boeing B-52 Stratofortress\">Boeing B-52 Stratofortress</a> <a href=\"https://wikipedia.org/wiki/1994_Fairchild_Air_Force_Base_B-52_crash\" title=\"1994 Fairchild Air Force Base B-52 crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Fairchild_Air_Force_Base\" title=\"Fairchild Air Force Base\">Fairchild Air Force Base</a> near <a href=\"https://wikipedia.org/wiki/Spokane,_Washington\" title=\"Spokane, Washington\">Spokane, Washington</a>, killing four.", "links": [{"title": "Boeing B-52 Stratofortress", "link": "https://wikipedia.org/wiki/Boeing_B-52_Stratofortress"}, {"title": "1994 Fairchild Air Force Base B-52 crash", "link": "https://wikipedia.org/wiki/1994_Fairchild_Air_Force_Base_B-52_crash"}, {"title": "Fairchild Air Force Base", "link": "https://wikipedia.org/wiki/Fairchild_Air_Force_Base"}, {"title": "Spokane, Washington", "link": "https://wikipedia.org/wiki/Spokane,_Washington"}]}, {"year": "1995", "text": "Rugby World Cup: South Africa defeats New Zealand and <PERSON> presents <PERSON><PERSON> with the Webb Ellis Cup in an iconic post-apartheid moment.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/1995_Rugby_World_Cup\" title=\"1995 Rugby World Cup\">Rugby World Cup</a>: <a href=\"https://wikipedia.org/wiki/South_Africa_national_rugby_union_team\" title=\"South Africa national rugby union team\">South Africa</a> defeats <a href=\"https://wikipedia.org/wiki/New_Zealand_national_rugby_union_team\" title=\"New Zealand national rugby union team\">New Zealand</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> with the <a href=\"https://wikipedia.org/wiki/Webb_Ellis_Cup\" title=\"Webb Ellis Cup\">Webb Ellis Cup</a> in an iconic post-<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a> moment.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1995_Rugby_World_Cup\" title=\"1995 Rugby World Cup\">Rugby World Cup</a>: <a href=\"https://wikipedia.org/wiki/South_Africa_national_rugby_union_team\" title=\"South Africa national rugby union team\">South Africa</a> defeats <a href=\"https://wikipedia.org/wiki/New_Zealand_national_rugby_union_team\" title=\"New Zealand national rugby union team\">New Zealand</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> with the <a href=\"https://wikipedia.org/wiki/Webb_Ellis_Cup\" title=\"Webb Ellis Cup\">Webb Ellis Cup</a> in an iconic post-<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a> moment.", "links": [{"title": "1995 Rugby World Cup", "link": "https://wikipedia.org/wiki/1995_Rugby_World_Cup"}, {"title": "South Africa national rugby union team", "link": "https://wikipedia.org/wiki/South_Africa_national_rugby_union_team"}, {"title": "New Zealand national rugby union team", "link": "https://wikipedia.org/wiki/New_Zealand_national_rugby_union_team"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Mandela"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> Ellis Cup", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}]}, {"year": "2002", "text": "The Igandu train disaster in Tanzania kills 281, the worst train accident in African history.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/Igandu_train_disaster\" class=\"mw-redirect\" title=\"Igandu train disaster\">Igandu train disaster</a> in <a href=\"https://wikipedia.org/wiki/Tanzania\" title=\"Tanzania\">Tanzania</a> kills 281, the worst train accident in <a href=\"https://wikipedia.org/wiki/Africa\" title=\"Africa\">African</a> history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Igandu_train_disaster\" class=\"mw-redirect\" title=\"Igandu train disaster\">Igandu train disaster</a> in <a href=\"https://wikipedia.org/wiki/Tanzania\" title=\"Tanzania\">Tanzania</a> kills 281, the worst train accident in <a href=\"https://wikipedia.org/wiki/Africa\" title=\"Africa\">African</a> history.", "links": [{"title": "Igandu train disaster", "link": "https://wikipedia.org/wiki/Igandu_train_disaster"}, {"title": "Tanzania", "link": "https://wikipedia.org/wiki/Tanzania"}, {"title": "Africa", "link": "https://wikipedia.org/wiki/Africa"}]}, {"year": "2004", "text": "In New York, capital punishment is declared unconstitutional.", "html": "2004 - In New York, <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">capital punishment</a> is declared unconstitutional.", "no_year_html": "In New York, <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">capital punishment</a> is declared unconstitutional.", "links": [{"title": "Capital punishment", "link": "https://wikipedia.org/wiki/Capital_punishment"}]}, {"year": "2010", "text": "At Wimbledon, <PERSON> of the United States defeats <PERSON> of France, in the longest match in professional tennis history.", "html": "2010 - At <a href=\"https://wikipedia.org/wiki/The_Championships,_Wimbledon\" class=\"mw-redirect\" title=\"The Championships, Wimbledon\">Wimbledon</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> of the United States defeats <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of France, in <a href=\"https://wikipedia.org/wiki/Isner%E2%80%93Mahut_match_at_the_2010_Wimbledon_Championships\" title=\"<PERSON><PERSON>-<PERSON><PERSON><PERSON> match at the 2010 Wimbledon Championships\">the longest match in professional tennis history</a>.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/The_Championships,_Wimbledon\" class=\"mw-redirect\" title=\"The Championships, Wimbledon\">Wimbledon</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> of the United States defeats <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of France, in <a href=\"https://wikipedia.org/wiki/Isner%E2%80%93Mahut_match_at_the_2010_Wimbledon_Championships\" title=\"<PERSON><PERSON>-<PERSON><PERSON><PERSON> match at the 2010 Wimbledon Championships\">the longest match in professional tennis history</a>.", "links": [{"title": "The Championships, Wimbledon", "link": "https://wikipedia.org/wiki/The_Championships,_Wimbledon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> match at the 2010 Wimbledon Championships", "link": "https://wikipedia.org/wiki/Isner%E2%80%93Mahut_match_at_the_2010_Wimbledon_Championships"}]}, {"year": "2010", "text": "<PERSON> assumes office as the first female Prime Minister of Australia.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> assumes office as the first female <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> assumes office as the first female <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "2012", "text": "Death of Lone<PERSON>, the last known individual of Chelonoidis nigra a<PERSON>ii, a subspecies of the Galápagos tortoise.", "html": "2012 - Death of <a href=\"https://wikipedia.org/wiki/Lonesome_George\" title=\"Lonesome George\">Lonesome <PERSON></a>, the last known individual of <i><a href=\"https://wikipedia.org/wiki/Chelonoidis_nigra_abingdonii\" class=\"mw-redirect\" title=\"Chelonoidis nigra abingdonii\">Chelonoidis nigra abingdonii</a></i>, a subspecies of the <a href=\"https://wikipedia.org/wiki/Gal%C3%A1pagos_tortoise\" title=\"Galápagos tortoise\">Galápagos tortoise</a>.", "no_year_html": "Death of <a href=\"https://wikipedia.org/wiki/Lone<PERSON>_George\" title=\"Lonesome George\">Lonesome <PERSON></a>, the last known individual of <i><a href=\"https://wikipedia.org/wiki/Chelonoidis_nigra_abingdonii\" class=\"mw-redirect\" title=\"Chelonoidis nigra abingdonii\">Chelonoidis nigra abingdonii</a></i>, a subspecies of the <a href=\"https://wikipedia.org/wiki/Gal%C3%A1pagos_tortoise\" title=\"Galápagos tortoise\">Galápagos tortoise</a>.", "links": [{"title": "Lonesome <PERSON>", "link": "https://wikipedia.org/wiki/Lone<PERSON>_<PERSON>"}, {"title": "Chelonoidis nigra a<PERSON>ii", "link": "https://wikipedia.org/wiki/Chelonoidis_nigra_abingdonii"}, {"title": "Galápagos tortoise", "link": "https://wikipedia.org/wiki/Gal%C3%A1pagos_tortoise"}]}, {"year": "2013", "text": "Former Italian Prime Minister <PERSON><PERSON><PERSON> is found guilty of abusing his power and engaging in sex with an underage prostitute, and is sentenced to seven years in prison.", "html": "2013 - Former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Italian Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_prostitute_trial\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> prostitute trial\">found guilty</a> of abusing his power and engaging in sex with an underage prostitute, and is sentenced to seven years in prison.", "no_year_html": "Former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Italian Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_prostitute_trial\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> prostitute trial\">found guilty</a> of abusing his power and engaging in sex with an underage prostitute, and is sentenced to seven years in prison.", "links": [{"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> prostitute trial", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_prostitute_trial"}]}, {"year": "2021", "text": "The Champlain Towers South condominium in Surfside, Florida suffers a sudden partial collapse, killing 98 people inside.", "html": "2021 - The Champlain Towers South condominium in <a href=\"https://wikipedia.org/wiki/Surfside,_Florida\" title=\"Surfside, Florida\">Surfside, Florida</a> suffers a <a href=\"https://wikipedia.org/wiki/Surfside_condominium_collapse\" title=\"Surfside condominium collapse\">sudden partial collapse</a>, killing 98 people inside.", "no_year_html": "The Champlain Towers South condominium in <a href=\"https://wikipedia.org/wiki/Surfside,_Florida\" title=\"Surfside, Florida\">Surfside, Florida</a> suffers a <a href=\"https://wikipedia.org/wiki/Surfside_condominium_collapse\" title=\"Surfside condominium collapse\">sudden partial collapse</a>, killing 98 people inside.", "links": [{"title": "Surfside, Florida", "link": "https://wikipedia.org/wiki/Surfside,_Florida"}, {"title": "Surfside condominium collapse", "link": "https://wikipedia.org/wiki/Surfside_condominium_collapse"}]}, {"year": "2022", "text": "In <PERSON><PERSON> v. Jackson Women's Health Organization, the U.S. Supreme Court rules that the U.S. Constitution does not assign the authority to regulate abortions to the federal government, thereby returning such authority to the individual states. This overturns the prior decisions in <PERSON> v<PERSON> (1973) and Planned Parenthood v. <PERSON> (1992).", "html": "2022 - In <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v._Jackson_Women%27s_Health_Organization\" title=\"<PERSON><PERSON> v. Jackson Women's Health Organization\"><PERSON><PERSON> v. Jackson Women's Health Organization</a></i>, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules that the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_United_States\" title=\"Constitution of the United States\">U.S. Constitution</a> does not assign the authority to regulate <a href=\"https://wikipedia.org/wiki/Abortion\" title=\"Abortion\">abortions</a> to the federal government, thereby returning such authority to the individual states. This overturns the prior decisions in <i><a href=\"https://wikipedia.org/wiki/Roe_v._Wade\" title=\"Roe v. Wade\"><PERSON> v<PERSON></a></i> (1973) and <i><a href=\"https://wikipedia.org/wiki/Planned_Parenthood_v._Casey\" title=\"Planned Parenthood v. Casey\">Planned Parenthood v. <PERSON></a></i> (1992).", "no_year_html": "In <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v._Jackson_Women%27s_Health_Organization\" title=\"<PERSON><PERSON> v. Jackson Women's Health Organization\"><PERSON><PERSON> v. Jackson Women's Health Organization</a></i>, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules that the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_United_States\" title=\"Constitution of the United States\">U.S. Constitution</a> does not assign the authority to regulate <a href=\"https://wikipedia.org/wiki/Abortion\" title=\"Abortion\">abortions</a> to the federal government, thereby returning such authority to the individual states. This overturns the prior decisions in <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v._Wade\" title=\"Roe v. Wade\"><PERSON> v. Wade</a></i> (1973) and <i><a href=\"https://wikipedia.org/wiki/Planned_Parenthood_v._Casey\" title=\"Planned Parenthood v<PERSON> Casey\">Planned Parenthood v. <PERSON></a></i> (1992).", "links": [{"title": "<PERSON><PERSON> v. Jackson Women's Health Organization", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_v._Jackson_Women%27s_Health_Organization"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Constitution of the United States", "link": "https://wikipedia.org/wiki/Constitution_of_the_United_States"}, {"title": "Abortion", "link": "https://wikipedia.org/wiki/Abortion"}, {"title": "Roe v. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_v._<PERSON>"}, {"title": "Planned Parenthood v. Casey", "link": "https://wikipedia.org/wiki/Planned_Parenthood_v._<PERSON>"}]}, {"year": "2023", "text": "The Wagner Group led by <PERSON><PERSON><PERSON> launches an insurrection against the Russian government.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/Wagner_Group\" title=\"Wagner Group\">Wagner Group</a> led by <a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Wagner_Group_rebellion\" title=\"Wagner Group rebellion\">launches an insurrection</a> against the <a href=\"https://wikipedia.org/wiki/Government_of_Russia\" title=\"Government of Russia\">Russian government.</a>", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wagner_Group\" title=\"Wagner Group\">Wagner Group</a> led by <a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Wagner_Group_rebellion\" title=\"Wagner Group rebellion\">launches an insurrection</a> against the <a href=\"https://wikipedia.org/wiki/Government_of_Russia\" title=\"Government of Russia\">Russian government.</a>", "links": [{"title": "Wagner Group", "link": "https://wikipedia.org/wiki/Wagner_Group"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>"}, {"title": "Wagner Group rebellion", "link": "https://wikipedia.org/wiki/Wagner_Group_rebellion"}, {"title": "Government of Russia", "link": "https://wikipedia.org/wiki/Government_of_Russia"}]}], "Births": [{"year": "1210", "text": "Count <PERSON><PERSON><PERSON> of Holland (d. 1234)", "html": "1210 - Count <a href=\"https://wikipedia.org/wiki/Flor<PERSON>_IV,_Count_of_Holland\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Holland\">Flor<PERSON> IV</a> of Holland (d. 1234)", "no_year_html": "Count <a href=\"https://wikipedia.org/wiki/<PERSON>lor<PERSON>_IV,_Count_of_Holland\" title=\"<PERSON><PERSON><PERSON> IV, Count of Holland\">Floris IV</a> of Holland (d. 1234)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Holland", "link": "https://wikipedia.org/wiki/Flor<PERSON>_IV,_Count_of_Holland"}]}, {"year": "1244", "text": "<PERSON>, Landgrave of Hesse (d. 1308)", "html": "1244 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse\" title=\"<PERSON>, Landgrave of Hesse\"><PERSON>, Landgrave of Hesse</a> (d. 1308)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse\" title=\"<PERSON>, Landgrave of Hesse\"><PERSON>, Landgrave of Hesse</a> (d. 1308)", "links": [{"title": "<PERSON>, Landgrave of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse"}]}, {"year": "1254", "text": "<PERSON><PERSON><PERSON>, Count of Holland (d. 1296)", "html": "1254 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Holland\"><PERSON><PERSON><PERSON>, Count of Holland</a> (d. 1296)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Holland\"><PERSON><PERSON><PERSON>, Count of Holland</a> (d. 1296)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Holland", "link": "https://wikipedia.org/wiki/<PERSON>lor<PERSON>_V,_Count_of_Holland"}]}, {"year": "1257", "text": "<PERSON>, 6th Earl of Oxford, English nobleman (probable; d. 1331)", "html": "1257 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Oxford\" title=\"<PERSON>, 6th Earl of Oxford\"><PERSON>, 6th Earl of Oxford</a>, English nobleman (probable; d. 1331)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_6th_Earl_of_Oxford\" title=\"<PERSON>, 6th Earl of Oxford\"><PERSON>, 6th Earl of Oxford</a>, English nobleman (probable; d. 1331)", "links": [{"title": "<PERSON>, 6th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Oxford"}]}, {"year": "1314", "text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> Queen of England (d. 1369)", "html": "1314 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hainault\" title=\"<PERSON><PERSON> of Hainault\"><PERSON><PERSON> of Hainault</a> Queen of England (d. 1369)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> of Hainault\"><PERSON><PERSON> of Hainault</a> Queen of England (d. 1369)", "links": [{"title": "<PERSON><PERSON> of Hainault", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1322", "text": "<PERSON>, Duchess of Brabant (d. 1406)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/Joanna,_Duchess_of_Brabant\" title=\"<PERSON>, Duchess of Brabant\"><PERSON>, Duchess of Brabant</a> (d. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joanna,_Duchess_of_Brabant\" title=\"<PERSON>, Duchess of Brabant\"><PERSON>, Duchess of Brabant</a> (d. 1406)", "links": [{"title": "<PERSON>, Duchess of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>,_Duchess_<PERSON>_<PERSON>"}]}, {"year": "1343", "text": "<PERSON> Valois, Queen of Navarre (d. 1373)", "html": "1343 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Val<PERSON>,_Queen_of_Navarre\" title=\"<PERSON> Valois, Queen of Navarre\"><PERSON> of Valois, Queen of Navarre</a> (d. 1373)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Navarre\" title=\"<PERSON> of Valois, Queen of Navarre\"><PERSON> Valois, Queen of Navarre</a> (d. 1373)", "links": [{"title": "<PERSON> Valois, Queen of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Navarre"}]}, {"year": "1360", "text": "<PERSON><PERSON>, Portuguese general", "html": "1360 - <a href=\"https://wikipedia.org/wiki/Nuno_%C3%81<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuno_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese general", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nuno_%C3%81l<PERSON><PERSON>_<PERSON>"}]}, {"year": "1386", "text": "<PERSON> of Capistrano, Italian priest and saint (d. 1456)", "html": "1386 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Capistrano\" title=\"<PERSON> of Capistrano\"><PERSON> of Capistrano</a>, Italian priest and saint (d. 1456)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Capist<PERSON>\" title=\"<PERSON> of Capistrano\"><PERSON> of Capistrano</a>, Italian priest and saint (d. 1456)", "links": [{"title": "<PERSON> of Capistrano", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1465", "text": "<PERSON>, Queen Consort of Naples (d. 1533)", "html": "1465 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Queen Consort of Naples (d. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Queen Consort of Naples (d. 1533)", "links": [{"title": "<PERSON> Balzo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1485", "text": "<PERSON>, Polish-German priest and reformer (d. 1558)", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German priest and reformer (d. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German priest and reformer (d. 1558)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1485", "text": "<PERSON> of Denmark, El<PERSON>tress of Brandenburg (d. 1555)", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Denmark,_Electress_of_Brandenburg\" title=\"<PERSON> of Denmark, Electress of Brandenburg\"><PERSON> of Denmark, <PERSON><PERSON>tress of Brandenburg</a> (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Denmark,_Electress_of_Brandenburg\" title=\"<PERSON> of Denmark, <PERSON><PERSON>tress of Brandenburg\"><PERSON> of Denmark, <PERSON><PERSON>tress of Brandenburg</a> (d. 1555)", "links": [{"title": "<PERSON> of Denmark, El<PERSON>tress of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_of_Denmark,_<PERSON><PERSON>tress_of_Brandenburg"}]}, {"year": "1499", "text": "<PERSON>, German theologian and the Protestant Reformer (d. 1570)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and the Protestant Reformer (d. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and the Protestant Reformer (d. 1570)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON>, French theologian and scholar (d. 1605)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theologian and scholar (d. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theologian and scholar (d. 1605)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1532", "text": "<PERSON>, 1st Earl of Leicester, English politician (d. 1588)", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Leicester\" title=\"<PERSON>, 1st Earl of Leicester\"><PERSON>, 1st Earl of Leicester</a>, English politician (d. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Leicester\" title=\"<PERSON>, 1st Earl of Leicester\"><PERSON>, 1st Earl of Leicester</a>, English politician (d. 1588)", "links": [{"title": "<PERSON>, 1st Earl of Leicester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Leicester"}]}, {"year": "1532", "text": "<PERSON>, Landgrave of Hesse-Kassel (d. 1573)", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Kassel\" title=\"<PERSON>, Landgrave of Hesse-Kassel\"><PERSON>, Landgrave of Hesse-Kassel</a> (d. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Kassel\" title=\"<PERSON>, Landgrave of Hesse-Kassel\"><PERSON>, Landgrave of Hesse-Kassel</a> (d. 1573)", "links": [{"title": "<PERSON>, Landgrave of Hesse-Kassel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse-Kassel"}]}, {"year": "1535", "text": "<PERSON> of Austria, Princess of Portugal (d. 1573)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Princess_of_Portugal\" title=\"<PERSON> of Austria, Princess of Portugal\"><PERSON> of Austria, Princess of Portugal</a> (d. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joanna_of_Austria,_Princess_of_Portugal\" title=\"<PERSON> of Austria, Princess of Portugal\"><PERSON> of Austria, Princess of Portugal</a> (d. 1573)", "links": [{"title": "<PERSON> of Austria, Princess of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_of_Austria,_Princess_of_Portugal"}]}, {"year": "1546", "text": "<PERSON>, English Jesuit priest, insurrectionist, and author (d. 1610)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Jesuit priest, insurrectionist, and author (d. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Jesuit priest, insurrectionist, and author (d. 1610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1587", "text": "<PERSON>, English-American settler (d. 1675)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(settler)\" title=\"<PERSON> (settler)\"><PERSON></a>, English-American settler (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(settler)\" title=\"<PERSON> (settler)\"><PERSON></a>, English-American settler (d. 1675)", "links": [{"title": "<PERSON> (settler)", "link": "https://wikipedia.org/wiki/<PERSON>(settler)"}]}, {"year": "1614", "text": "<PERSON>, 1st Baron <PERSON>", "html": "1614 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1616", "text": "<PERSON>, Dutch painter, etcher and draftsman, student of <PERSON><PERSON><PERSON><PERSON> (d. 1680)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter, etcher and draftsman, student of <PERSON><PERSON><PERSON><PERSON> (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter, etcher and draftsman, student of <PERSON><PERSON><PERSON><PERSON> (d. 1680)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1661", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese daimyō (d. 1730)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ri\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1730)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1663", "text": "<PERSON>, French bishop (d. 1742)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bishop (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bishop (d. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1687", "text": "<PERSON>, German-Lutheran clergyman and scholar (d. 1757)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Lutheran clergyman and scholar (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Lutheran clergyman and scholar (d. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON><PERSON><PERSON>, Swiss author and theorist (d. 1748)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and theorist (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and theorist (d. 1748)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1704", "text": "<PERSON><PERSON><PERSON>, <PERSON>, French philosopher and author (d. 1771)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Marquis_d%27Argens\" title=\"<PERSON><PERSON><PERSON>, Marquis <PERSON>\"><PERSON><PERSON><PERSON>, <PERSON></a>, French philosopher and author (d. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Marquis_<PERSON>%27Argens\" title=\"<PERSON><PERSON><PERSON>, Marquis <PERSON>\"><PERSON><PERSON><PERSON>, <PERSON></a>, French philosopher and author (d. 1771)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_<PERSON>_d%27A<PERSON>s"}]}, {"year": "1753", "text": "<PERSON>, American general and politician, 1st Governor of Michigan Territory (d. 1825)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan_Territory\" class=\"mw-redirect\" title=\"Governor of Michigan Territory\">Governor of Michigan Territory</a> (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan_Territory\" class=\"mw-redirect\" title=\"Governor of Michigan Territory\">Governor of Michigan Territory</a> (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Michigan Territory", "link": "https://wikipedia.org/wiki/Governor_of_Michigan_Territory"}]}, {"year": "1755", "text": "<PERSON><PERSON><PERSON><PERSON>, Prussian-French activist (d. 1794)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/Anacharsis_Cloots\" title=\"Anacharsis Cloots\"><PERSON><PERSON><PERSON><PERSON></a>, Prussian-French activist (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anacharsis_Cloots\" title=\"Anacharsis Cloots\"><PERSON><PERSON><PERSON><PERSON></a>, Prussian-French activist (d. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anacharsis_Cloots"}]}, {"year": "1767", "text": "<PERSON><PERSON><PERSON>, French geographer and author (d. 1846)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AEt_Eyri%C3%A8s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French geographer and author (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AEt_Eyri%C3%A8s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French geographer and author (d. 1846)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AEt_Eyri%C3%A8s"}]}, {"year": "1771", "text": "Éleuthère Irénée <PERSON>, French chemist and businessman, founded DuPont (d. 1834)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/%C3%89leuth%C3%A8re_Ir%C3%A9n%C3%A9e_du_Pont\" title=\"Éleuthère Irénée du Pont\">Éleuthère Irén<PERSON></a>, French chemist and businessman, founded <a href=\"https://wikipedia.org/wiki/Du<PERSON><PERSON>_(1802%E2%80%932017)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (1802-2017)\"><PERSON><PERSON><PERSON></a> (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89leuth%C3%A8re_Ir%C3%A9n%C3%A9e_du_Pont\" title=\"Éleuthère Irénée du Pont\">Éleuthère Irén<PERSON></a>, French chemist and businessman, founded <a href=\"https://wikipedia.org/wiki/Du<PERSON><PERSON>_(1802%E2%80%932017)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (1802-2017)\"><PERSON><PERSON><PERSON></a> (d. 1834)", "links": [{"title": "Éleuthère Irénée <PERSON>", "link": "https://wikipedia.org/wiki/%C3%89leuth%C3%A8re_Ir%C3%A9n%C3%A9e_du_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> (1802-2017)", "link": "https://wikipedia.org/wiki/Du<PERSON><PERSON>_(1802%E2%80%932017)"}]}, {"year": "1774", "text": "<PERSON>, Argentinian commander and politician, 5th Supreme Director of the United Provinces of the Río de la Plata (d. 1819)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_de_Balcarce\" title=\"<PERSON>\"><PERSON></a>, Argentinian commander and politician, 5th <a href=\"https://wikipedia.org/wiki/Supreme_Director_of_the_United_Provinces_of_the_R%C3%ADo_de_la_Plata\" title=\"Supreme Director of the United Provinces of the Río de la Plata\">Supreme Director of the United Provinces of the Río de la Plata</a> (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_de_Balcarce\" title=\"<PERSON>\"><PERSON></a>, Argentinian commander and politician, 5th <a href=\"https://wikipedia.org/wiki/Supreme_Director_of_the_United_Provinces_of_the_R%C3%ADo_de_la_Plata\" title=\"Supreme Director of the United Provinces of the Río de la Plata\">Supreme Director of the United Provinces of the Río de la Plata</a> (d. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nz%C3%<PERSON><PERSON><PERSON>_de_Balcarce"}, {"title": "Supreme Director of the United Provinces of the Río de la Plata", "link": "https://wikipedia.org/wiki/Supreme_Director_of_the_United_Provinces_of_the_R%C3%ADo_de_la_Plata"}]}, {"year": "1774", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French general and engineer (d. 1838)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>-<PERSON>%C3%AEt_Haxo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and engineer (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>-<PERSON>%C3%AEt_Haxo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French general and engineer (d. 1838)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>-<PERSON>%C3%AEt_Haxo"}]}, {"year": "1777", "text": "<PERSON>, Scottish commander and explorer (d. 1856)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Arctic_explorer)\" class=\"mw-redirect\" title=\"<PERSON> (Arctic explorer)\"><PERSON></a>, Scottish commander and explorer (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Arctic_explorer)\" class=\"mw-redirect\" title=\"<PERSON> (Arctic explorer)\"><PERSON></a>, Scottish commander and explorer (d. 1856)", "links": [{"title": "<PERSON> (Arctic explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Arctic_explorer)"}]}, {"year": "1782", "text": "<PERSON>, Argentinian captain and politician (d. 1847)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Argentinian captain and politician (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Argentinian captain and politician (d. 1847)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1783", "text": "<PERSON>, German economist and geographer (d. 1850)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>h%C3%BCnen\" title=\"<PERSON>\"><PERSON></a>, German economist and geographer (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>h%C3%BCnen\" title=\"<PERSON>\"><PERSON></a>, German economist and geographer (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Th%C3%BCnen"}]}, {"year": "1784", "text": "<PERSON>, Uruguayan general and politician, President of Uruguay (d. 1853)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Uruguay\" class=\"mw-redirect\" title=\"List of Presidents of Uruguay\">President of Uruguay</a> (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Uruguay\" class=\"mw-redirect\" title=\"List of Presidents of Uruguay\">President of Uruguay</a> (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of Uruguay", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Uruguay"}]}, {"year": "1788", "text": "<PERSON>, American inventor (d. 1864)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American inventor (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American inventor (d. 1864)", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>_(inventor)"}]}, {"year": "1795", "text": "<PERSON>, German physician and psychologist (d. 1878)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and psychologist (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and psychologist (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, Irish-American archbishop (d. 1864)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_New_York)\" class=\"mw-redirect\" title=\"<PERSON> (archbishop of New York)\"><PERSON></a>, Irish-American archbishop (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_New_York)\" class=\"mw-redirect\" title=\"<PERSON> (archbishop of New York)\"><PERSON></a>, Irish-American archbishop (d. 1864)", "links": [{"title": "<PERSON> (archbishop of New York)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_New_York)"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON>, Polish geologist and explorer (d. 1873)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Polish geologist and explorer (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish geologist and explorer (d. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>,  Austrian botanist, numismatist, and sinologist (d. 1849)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian botanist, numismatist, and sinologist (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian botanist, numismatist, and sinologist (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, American religious leader (d. 1854)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (d. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, American lawyer and jurist (d. 1889)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, American minister and reformer (d. 1887)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and reformer (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and reformer (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, American composer (d. 1904)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer (d. 1904)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1821", "text": "<PERSON>, Argentinian physician and politician (d. 1890)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian physician and politician (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian physician and politician (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, English-Australian surveyor (d. 1898)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian surveyor (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian surveyor (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, German chemist and academic (d. 1902)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, Polish painter (d. 1893)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish painter (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish painter (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON>, American businessman (d. 1903)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman (d. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, American short story writer, essayist, and journalist (d. 1914)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, essayist, and journalist (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, essayist, and journalist (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Nigerian priest and historian (d. 1901)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Nigerian_historian)\" title=\"<PERSON> (Nigerian historian)\"><PERSON></a>, Nigerian priest and historian (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Nigerian_historian)\" title=\"<PERSON> (Nigerian historian)\"><PERSON></a>, Nigerian priest and historian (d. 1901)", "links": [{"title": "<PERSON> (Nigerian historian)", "link": "https://wikipedia.org/wiki/<PERSON>_(Nigerian_historian)"}]}, {"year": "1850", "text": "<PERSON>, 1st <PERSON>, Irish field marshal and politician, Governor-General of Sudan (d. 1916)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, Irish field marshal and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_pre-independence_Sudan\" title=\"List of governors of pre-independence Sudan\">Governor-General of Sudan</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, Irish field marshal and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_pre-independence_Sudan\" title=\"List of governors of pre-independence Sudan\">Governor-General of Sudan</a> (d. 1916)", "links": [{"title": "<PERSON>, 1st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>"}, {"title": "List of governors of pre-independence Sudan", "link": "https://wikipedia.org/wiki/List_of_governors_of_pre-independence_Sudan"}]}, {"year": "1852", "text": "<PERSON>, German bacteriologist and academic (d. 1915)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bacteriologist and academic (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bacteriologist and academic (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, American painter (d. 1923)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, American archaeologist and author (d. 1930)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and author (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and author (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, English historian, philosopher, and theologian (d. 1924)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, philosopher, and theologian (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, philosopher, and theologian (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1865", "text": "<PERSON>, American painter and educator (d. 1929)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American educator and activist (d. 1944)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Edstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, American educator and activist (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Edstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, American educator and activist (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Edstr%C3%B6m"}]}, {"year": "1869", "text": "<PERSON> of Greece and Denmark (d. 1957)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Greece_and_Denmark\" title=\"Prince <PERSON> of Greece and Denmark\">Prince <PERSON> of Greece and Denmark</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Greece_and_Denmark\" title=\"Prince <PERSON> of Greece and Denmark\">Prince <PERSON> of Greece and Denmark</a> (d. 1957)", "links": [{"title": "<PERSON> of Greece and Denmark", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_of_Greece_and_Denmark"}]}, {"year": "1872", "text": "<PERSON>, American journalist and art and theatre critic (d. 1947)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and art and theatre critic (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and art and theatre critic (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>inshield"}]}, {"year": "1875", "text": "<PERSON>, Irish novelist, literary critic and translator (d. 1947)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, literary critic and translator (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, literary critic and translator (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American mathematician and academic (g. 1960)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (g. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (g. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Brazilian revolutionary and sailor (d. 1969)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_C%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian revolutionary and sailor (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_C%C3%<PERSON>ndi<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian revolutionary and sailor (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_C%C3%A2ndido_Felisberto"}]}, {"year": "1881", "text": "<PERSON>, Irish-Canadian author, poet, and playwright (d. 1949)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian author, poet, and playwright (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian author, poet, and playwright (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician (d. 1953)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, German businessman (d. 1962)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Austrian-American physicist and academic, Nobel Prize laureate (d. 1964)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1883", "text": "<PERSON>-<PERSON><PERSON>, Austrian librettist, lyricist and writer (d. 1942)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>-<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Austrian librettist, lyricist and writer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>-<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Austrian librettist, lyricist and writer (d. 1942)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fritz_L%C3%B6<PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "1883", "text": "<PERSON>, French artist (d. 1956)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American runner (d. 1956)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American runner (d. 1966)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American runner (d. 1941)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner (d. 1941)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "1885", "text": "<PERSON>, Norwegian geologist (d. 1975)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian geologist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian geologist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Dutch architect, designed the Rietveld Schröder House (d. 1964)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_R<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch architect, designed the <a href=\"https://wikipedia.org/wiki/Rietveld_Schr%C3%B6der_House\" title=\"Rietveld Schröder House\">Rietveld Schröder House</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch architect, designed the <a href=\"https://wikipedia.org/wiki/Rietveld_Schr%C3%B6der_House\" title=\"Rietveld Schröder House\">Rietveld Schröder House</a> (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>"}, {"title": "Rietveld Schröder House", "link": "https://wikipedia.org/wiki/Rietveld_Schr%C3%B6der_House"}]}, {"year": "1893", "text": "<PERSON>, American businessman, co-founded The Walt Disney Company (d. 1971)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "The Walt Disney Company", "link": "https://wikipedia.org/wiki/The_Walt_Disney_Company"}]}, {"year": "1895", "text": "<PERSON>, American boxer and soldier (d. 1983)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and soldier (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and soldier (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Estonian-Australian paleontologist and geologist (d. 1983)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Armin_%C3%96pik\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-Australian paleontologist and geologist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armin_%C3%96pik\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-Australian paleontologist and geologist (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Armin_%C3%96pik"}]}, {"year": "1898", "text": "<PERSON>, Estonian politician, 14th Minister of Foreign Affairs of Estonia (d. 1958)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, 14th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Estonia\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs of Estonia\">Minister of Foreign Affairs of Estonia</a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, 14th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Estonia\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs of Estonia\">Minister of Foreign Affairs of Estonia</a> (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs of Estonia", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Estonia"}]}, {"year": "1900", "text": "<PERSON>, German mathematician and engineer (d. 1945)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and engineer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and engineer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, French saxophonist (d. 2001)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French saxophonist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French saxophonist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American composer and theorist (d. 1974)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and theorist (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and theorist (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American basketball player and salesman (d. 1969)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(salesman)\" title=\"<PERSON> (salesman)\"><PERSON></a>, American basketball player and salesman (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(salesman)\" title=\"<PERSON> (salesman)\"><PERSON></a>, American basketball player and salesman (d. 1969)", "links": [{"title": "<PERSON> (salesman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(salesman)"}]}, {"year": "1904", "text": "<PERSON>, American singer-songwriter and actor (d. 1995)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American sprinter (d. 1998)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, French cellist and educator (d. 1986)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cellist and educator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cellist and educator (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American poet and educator (d. 1971)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Russian poet and translator (d. 1989)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and translator (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and translator (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, German organist, composer, and conductor (d. 1942)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and conductor (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and conductor (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Estonian colonel (d. 1976)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Alfons_<PERSON>bane\" title=\"Alfons Re<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian colonel (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfons_<PERSON>bane\" title=\"Alfons Rebane\"><PERSON><PERSON><PERSON></a>, Estonian colonel (d. 1976)", "links": [{"title": "Alfons <PERSON>", "link": "https://wikipedia.org/wiki/Alfons_Rebane"}]}, {"year": "1909", "text": "<PERSON>, Canadian violinist, composer, and conductor (d. 1978)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, composer, and conductor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, composer, and conductor (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Baron <PERSON>, English mathematician and physicist (d. 1991)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English mathematician and physicist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English mathematician and physicist (d. 1991)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American author (d. 2001)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Argentinian race car driver (d. 1995)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Argentinian physicist and academic (d. 2011)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian physicist and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian physicist and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernesto_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Canadian opera singer (d. 1968)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Portia_White\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian opera singer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Portia_White\" title=\"<PERSON><PERSON> White\"><PERSON><PERSON></a>, Canadian opera singer (d. 1968)", "links": [{"title": "Portia White", "link": "https://wikipedia.org/wiki/Portia_White"}]}, {"year": "1912", "text": "<PERSON>, English sportscaster and author (d. 1994)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster and author (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English author (d. 2002)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist and soldier (d. 2002)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>oor\"><PERSON><PERSON><PERSON></a>, Belgian cyclist and soldier (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist and soldier (d. 2002)", "links": [{"title": "Gustaaf <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Norwegian singer and revue actress (d. 1987)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer and revue actress (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer and revue actress (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Polish-American activist and academic (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American activist and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American activist and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, French secret agent (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French secret agent (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French secret agent (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English astronomer and author (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American soldier, lawyer, and politician, 70th United States Attorney General (d. 2010)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 70th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 70th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Lebanese painter and sculptor (d. 2017)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese painter and sculptor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese painter and sculptor (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ir"}]}, {"year": "1917", "text": "<PERSON>, Canadian-American political scientist and academic (d. 2014)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American political scientist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American political scientist and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American television producer (d. 2020)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American television producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American television producer (d. 2020)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "1917", "text": "Ramblin' <PERSON>, American singer and guitarist (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27_<PERSON>_<PERSON>\" title=\"Ramblin' <PERSON>\"><PERSON><PERSON>' <PERSON></a>, American singer and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27_<PERSON>_<PERSON>\" title=\"Ramblin' <PERSON>\"><PERSON><PERSON>' <PERSON></a>, American singer and guitarist (d. 2013)", "links": [{"title": "Ramblin' <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>%27_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English cryptanalyst and numismatist (d. 1996)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cryptanalyst and numismatist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cryptanalyst and numismatist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, American journalist and author (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Singaporean businessman and politician, Singaporean Minister for Education (d. 2012)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean businessman and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Education_(Singapore)\" class=\"mw-redirect\" title=\"Minister for Education (Singapore)\">Singaporean Minister for Education</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean businessman and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Education_(Singapore)\" class=\"mw-redirect\" title=\"Minister for Education (Singapore)\">Singaporean Minister for Education</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister for Education (Singapore)", "link": "https://wikipedia.org/wiki/Minister_for_Education_(Singapore)"}]}, {"year": "1919", "text": "<PERSON>, American actor (d. 2015)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "links": [{"title": "Al <PERSON>ro", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ro"}]}, {"year": "1921", "text": "<PERSON>, German soldier (d. 2019)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actor and comedian (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor and comedian (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor and comedian (d. 2015)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1922", "text": "<PERSON>, English microbiologist, author, and academic (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(microbiologist)\" title=\"<PERSON> (microbiologist)\"><PERSON></a>, English microbiologist, author, and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(microbiologist)\" title=\"<PERSON> (microbiologist)\"><PERSON></a>, English microbiologist, author, and academic (d. 2014)", "links": [{"title": "<PERSON> (microbiologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(microbiologist)"}]}, {"year": "1922", "text": "<PERSON>, American economist (d. 2020)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Australian painter and philanthropist (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and philanthropist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and philanthropist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Swiss politician, 70th President of the Swiss Confederation (d. 2008)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician, 70th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician, 70th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1924", "text": "<PERSON>, Scottish astronomer and academic (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish astronomer and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish astronomer and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, American politician (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American politician (d. 2019)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Canadian sociologist, philosopher, and poet (d. 1997)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian sociologist, philosopher, and poet (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian sociologist, philosopher, and poet (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American dentist, soldier, and politician, 3rd United States Secretary of Energy (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist, soldier, and politician, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Energy\" title=\"United States Secretary of Energy\">United States Secretary of Energy</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist, soldier, and politician, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Energy\" title=\"United States Secretary of Energy\">United States Secretary of Energy</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of Energy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Energy"}]}, {"year": "1927", "text": "<PERSON>, American physicist and engineer, Nobel Prize laureate (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1929", "text": "<PERSON>, American astronomer (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, French actor, director, producer, and screenwriter (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, South African businessman and philanthropist (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_African_businessman)\" title=\"<PERSON> (South African businessman)\"><PERSON></a>, South African businessman and philanthropist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_African_businessman)\" title=\"<PERSON> (South African businessman)\"><PERSON></a>, South African businessman and philanthropist (d. 2019)", "links": [{"title": "<PERSON> (South African businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_(South_African_businessman)"}]}, {"year": "1930", "text": "<PERSON>, Jr., American publisher (d. 2006)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American publisher (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American publisher (d. 2006)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1931", "text": "<PERSON>, American golfer (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian-Italian environmentalist (d. 2001)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Italian environmentalist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Italian environmentalist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Canadian sports announcer (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, Canadian sports announcer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, Canadian sports announcer (d. 2024)", "links": [{"title": "<PERSON> (sportscaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)"}]}, {"year": "1933", "text": "<PERSON>, American basketball player and coach (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1933)\" title=\"<PERSON> (basketball, born 1933)\"><PERSON></a>, American basketball player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1933)\" title=\"<PERSON> (basketball, born 1933)\"><PERSON></a>, American basketball player and coach (d. 2021)", "links": [{"title": "<PERSON> (basketball, born 1933)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1933)"}]}, {"year": "1933", "text": "<PERSON><PERSON>, 1st First Lady of Kenya", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Ngina_Kenyatta\" title=\"Ngina Kenyatta\"><PERSON><PERSON></a>, 1st First Lady of Kenya", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ngina_Kenyatta\" title=\"Ngina Kenyatta\"><PERSON><PERSON></a>, 1st First Lady of Kenya", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ngina_Kenyatta"}]}, {"year": "1934", "text": "<PERSON>, German footballer and referee (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and referee (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and referee (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Canadian singer-songwriter (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Italian singer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gloria Christian\"><PERSON></a>, Italian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Christian\" title=\"Gloria Christian\"><PERSON></a>, Italian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American composer and educator", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, French racing cyclist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American baseball player", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor and director (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American actor and director (d. 2021)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1937", "text": "<PERSON>, Indian-American author and academic", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Lawrence_Block\" title=\"Lawrence Block\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lawrence_Block\" title=\"Lawrence Block\"><PERSON></a>, American author", "links": [{"title": "Lawrence Block", "link": "https://wikipedia.org/wiki/Lawrence_Block"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Azerbaijani politician, 1st democratically elected Azerbaijani President (d. 2000)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani politician, 1st democratically elected Azerbaijani President (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani politician, 1st democratically elected Azerbaijani President (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lfaz_Elchibey"}]}, {"year": "1938", "text": "<PERSON>, New Zealand rugby player (d. 1992)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 1992)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, French singer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ontaine\"><PERSON><PERSON><PERSON></a>, French singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American politician and educator", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian newsreader (d. 2014)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newsreader)\" title=\"<PERSON> (newsreader)\"><PERSON></a>, Australian newsreader (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newsreader)\" title=\"<PERSON> (newsreader)\"><PERSON></a>, Australian newsreader (d. 2014)", "links": [{"title": "<PERSON> (newsreader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newsreader)"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Italian cinematographer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>\" title=\"<PERSON>itt<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cinematographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Turkish singer-songwriter and guitarist (d. 2023)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter and guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter and guitarist (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Bulgarian-French psychoanalyst and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-French psychoanalyst and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-French psychoanalyst and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Australian cricketer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English rock singer-songwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English rock singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English rock singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1942", "text": "<PERSON>, American actress and singer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Chilean engineer and politician, 32nd President of Chile", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean engineer and politician, 32nd <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean engineer and politician, 32nd <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1942", "text": "<PERSON>, Australian academician and educator  (d. 2017)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian academician and educator (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian academician and educator (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Danish economist and academic (d. 2004)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Birgit_Grodal\" title=\"Birgit Grodal\"><PERSON><PERSON><PERSON> Grodal</a>, Danish economist and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Birgit_Grodal\" title=\"Birgit Grodal\"><PERSON><PERSON><PERSON> Grodal</a>, Danish economist and academic (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birgit_Grodal"}]}, {"year": "1944", "text": "<PERSON>, English guitarist and songwriter (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English saxophonist (d. 1983)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rock_musician)\" title=\"<PERSON> (rock musician)\"><PERSON></a>, English saxophonist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rock_musician)\" title=\"<PERSON> (rock musician)\"><PERSON></a>, English saxophonist (d. 1983)", "links": [{"title": "<PERSON> (rock musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(rock_musician)"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American lawyer and politician, 53rd Governor of New York", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 53rd <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 53rd <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1945", "text": "<PERSON>, Dutch tennis player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Betty_<PERSON>%C3%B6ve\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Betty_<PERSON>%C3%B6ve\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Betty_St%C3%B6ve"}]}, {"year": "1946", "text": "<PERSON>, Canadian civil servant and politician, 32nd Canadian Minister of National Defence", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian civil servant and politician, 32nd <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian civil servant and politician, 32nd <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of National Defence (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)"}]}, {"year": "1946", "text": "<PERSON>, American engineer, and astronaut (d. 1986)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> On<PERSON>\"><PERSON></a>, American engineer, and astronaut (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Onizuka\"><PERSON></a>, American engineer, and astronaut (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American economist and politician, 22nd United States Secretary of Labor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Labor", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Labor"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, English chef, author, and television personality (d. 2014)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English chef, author, and television personality (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English chef, author, and television personality (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English-American drummer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor and director", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Swiss keyboard player and songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss keyboard player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter, bass player, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English fashion designer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1950", "text": "<PERSON>, Irish-born English photographer (d. 2006)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born English photographer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born English photographer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Polish businessman (d. 2015)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish businessman (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish businessman (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lackey\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lackey\"><PERSON></a>, American author", "links": [{"title": "Mercedes Lackey", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Australian sprinter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, English diplomat, British High Commissioner to Tanzania", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_Tanzania\" class=\"mw-redirect\" title=\"List of High Commissioners of the United Kingdom to Tanzania\">British High Commissioner to Tanzania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_Tanzania\" class=\"mw-redirect\" title=\"List of High Commissioners of the United Kingdom to Tanzania\">British High Commissioner to Tanzania</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of High Commissioners of the United Kingdom to Tanzania", "link": "https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_Tanzania"}]}, {"year": "1952", "text": "<PERSON>, English lawyer and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American chemist and physicist, Nobel Prize laureate", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1953", "text": "<PERSON>, Australian footballer and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English geneticist and academic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)\" title=\"<PERSON> (academic)\"><PERSON></a>, English geneticist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(academic)\" title=\"<PERSON> (academic)\"><PERSON></a>, English geneticist and academic", "links": [{"title": "<PERSON> (academic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)"}]}, {"year": "1955", "text": "<PERSON>, German footballer and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American golfer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English politician, Secretary of State for Northern Ireland", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland\" title=\"Secretary of State for Northern Ireland\">Secretary of State for Northern Ireland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland\" title=\"Secretary of State for Northern Ireland\">Secretary of State for Northern Ireland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Northern Ireland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland"}]}, {"year": "1957", "text": "<PERSON>, American lawyer and politician, 45th Governor of Kansas", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Kansas_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Kansas politician)\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Kansas\" class=\"mw-redirect\" title=\"Governor of Kansas\">Governor of Kansas</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Kansas_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Kansas politician)\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Kansas\" class=\"mw-redirect\" title=\"Governor of Kansas\">Governor of Kansas</a>", "links": [{"title": "<PERSON> (Kansas politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Kansas_politician)"}, {"title": "Governor of Kansas", "link": "https://wikipedia.org/wiki/Governor_of_Kansas"}]}, {"year": "1958", "text": "<PERSON>, Canadian lawyer and politician, 5th Deputy Prime Minister of Canada", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Italian mountaineer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mountaineer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, governor of Pohnpei State, Micronesia", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, governor of <a href=\"https://wikipedia.org/wiki/Pohnpei_State\" title=\"Pohnpei State\">Pohnpei State</a>, Micronesia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Oliver\"><PERSON></a>, governor of <a href=\"https://wikipedia.org/wiki/Pohnpei_State\" title=\"Pohnpei State\">Pohnpei State</a>, Micronesia", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Oliver"}, {"title": "Pohnpei State", "link": "https://wikipedia.org/wiki/Pohnpei_State"}]}, {"year": "1958", "text": "<PERSON>, American ice hockey player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter, bass player, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Scottish lawyer, judge, and politician, Solicitor General for Scotland", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_Scotland\" title=\"Solicitor General for Scotland\">Solicitor General for Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_Scotland\" title=\"Solicitor General for Scotland\">Solicitor General for Scotland</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Solicitor General for Scotland", "link": "https://wikipedia.org/wiki/Solicitor_General_for_Scotland"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and pianist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Swedish accountant and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ter\" title=\"<PERSON>\"><PERSON></a>, Swedish accountant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ter\" title=\"<PERSON>\"><PERSON></a>, Swedish accountant and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A4ter"}]}, {"year": "1960", "text": "<PERSON>, Norwegian director, cinematographer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian director, cinematographer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian director, cinematographer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer and guitarist (d. 2000)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Scottish actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Iain Glen\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iain_Glen\" title=\"Iain Glen\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Jr., American journalist and activist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American journalist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American journalist and activist", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1961", "text": "<PERSON><PERSON>, English singer-songwriter, guitarist, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Indian industrialist and billionaire", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian industrialist and billionaire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian industrialist and billionaire", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Mexican politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Russian guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Serbian-American soccer player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Pre<PERSON>\" title=\"Pre<PERSON>\"><PERSON><PERSON></a>, Serbian-American soccer player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pre<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-American soccer player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Preki"}]}, {"year": "1963", "text": "<PERSON>, American author and illustrator (d. 2007)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, French television host and producer (d. 2012)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French television host and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French television host and producer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON>, <PERSON>, English politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American ice hockey player and scout", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and scout", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian race car driver", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, German ice hockey player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uwe_K<PERSON>p"}]}, {"year": "1965", "text": "<PERSON>, English actor, writer, composer and musician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, writer, composer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, writer, composer and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and musician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sandoval"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American actress, director, and screenwriter (d. 2006)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, director, and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, director, and screenwriter (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Slovenian director and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian director and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1967", "text": "<PERSON>, Canadian soccer player and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Egyptian-American basketball player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>by\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>by"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Portuguese pianist, composer, and educator (d. 2012)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese pianist, composer, and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese pianist, composer, and educator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian cyclist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Slovenian rower", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%BDvegelj\" title=\"<PERSON>\"><PERSON></a>, Slovenian rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%BDvegelj\" title=\"<PERSON>\"><PERSON></a>, Slovenian rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Denis_%C5%BDvegelj"}]}, {"year": "1973", "text": "<PERSON>, French chef", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English sailor, rower, and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor, rower, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor, rower, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player and umpire", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)\" title=\"<PERSON> (umpire)\"><PERSON></a>, American baseball player and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(umpire)\" title=\"<PERSON> (umpire)\"><PERSON></a>, American baseball player and umpire", "links": [{"title": "<PERSON> (umpire)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mal%C3%ADk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marek_Mal%C3%ADk"}]}, {"year": "1975", "text": "<PERSON>, Argentinian-Italian rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American football player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Greek basketball player and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Di<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1978", "text": "<PERSON>, Spanish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(footballer,_born_1978)\" title=\"<PERSON> (footballer, born 1978)\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(footballer,_born_1978)\" title=\"<PERSON> (footballer, born 1978)\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON> (footballer, born 1978)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(footballer,_born_1978)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ariel Pink\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Pink\" title=\"Ariel Pink\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Argentinian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A1n_Riquelme\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A1n_Riquelme\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Rom%C3%A1n_Riquelme"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Finnish guitarist and songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Emppu_Vuorinen\" title=\"<PERSON><PERSON><PERSON> Vuorinen\"><PERSON><PERSON><PERSON></a>, Finnish guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emppu_Vuorinen\" title=\"Em<PERSON>u Vuorinen\"><PERSON><PERSON><PERSON></a>, Finnish guitarist and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emppu_Vuorinen"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American actress and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>y_Kaling"}]}, {"year": "1979", "text": "<PERSON>, Czech model and philanthropist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Petra_N%C4%9Bmcov%C3%A1\" title=\"<PERSON> Němcová\"><PERSON></a>, Czech model and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petra_N%C4%9Bmcov%C3%A1\" title=\"<PERSON>ěmcová\"><PERSON></a>, Czech model and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Petra_N%C4%9Bmcov%C3%A1"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Cicinho\" title=\"Cicinh<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cicinho\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "Cici<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cicinho"}]}, {"year": "1980", "text": "<PERSON>, German tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_D%C3%BCbbers\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_D%C3%BCbbers\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nina_D%C3%BCbbers"}]}, {"year": "1980", "text": "<PERSON>, Australian race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Filipino actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Norwegian drummer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian drummer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>J_Redick"}]}, {"year": "1984", "text": "<PERSON>, Swedish-born German wheelchair basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-born German <a href=\"https://wikipedia.org/wiki/Wheelchair_basketball\" title=\"Wheelchair basketball\">wheelchair basketball</a> player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-born German <a href=\"https://wikipedia.org/wiki/Wheelchair_basketball\" title=\"Wheelchair basketball\">wheelchair basketball</a> player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wheelchair basketball", "link": "https://wikipedia.org/wiki/Wheelchair_basketball"}]}, {"year": "1985", "text": "<PERSON>, Brazilian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Al<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, South African cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Japanese model", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Shi<PERSON>\" title=\"Yukin<PERSON> Shirakawa\"><PERSON><PERSON><PERSON></a>, Japanese model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yu<PERSON><PERSON>_Shi<PERSON>\" title=\"Yukin<PERSON> Shirakawa\"><PERSON><PERSON><PERSON></a>, Japanese model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yukina_Shirakawa"}]}, {"year": "1986", "text": "<PERSON>, English cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Czech tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dobr%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dobr%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Simona_Dobr%C3%A1"}]}, {"year": "1987", "text": "<PERSON>, Argentinian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, French snowboarder", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French snowboarder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Thai-American singer and actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Thai-American singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Thai-American singer and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ch<PERSON>un"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Eritrean runner", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Te<PERSON><PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Eritrean runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Te<PERSON><PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Eritrean runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Te<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, German footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"Ya<PERSON><PERSON>\">Ya<PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Ya<PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Austrian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Argentinian rapper", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, Argentinian rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, Argentinian rapper", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1996", "text": "<PERSON>, Guadeloupean footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guadeloupean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guadeloupean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Uruguayan footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Darwin_N%C3%BA%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Darwin_N%C3%BA%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Darwin_N%C3%BA%C3%B1ez"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Russian tennis player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_2004)\" title=\"<PERSON> (footballer, born 2004)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_2004)\" title=\"<PERSON> (footballer, born 2004)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 2004)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_2004)"}]}], "Deaths": [{"year": "1046", "text": "<PERSON><PERSON><PERSON>, Korean ruler (b. 1018)", "html": "1046 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_10th_Monarch_of_Goryeo\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, 10th Monarch of Goryeo\"><PERSON><PERSON><PERSON> II</a>, Korean ruler (b. 1018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_10th_Monarch_of_Goryeo\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, 10th Monarch of Goryeo\"><PERSON><PERSON><PERSON> II</a>, Korean ruler (b. 1018)", "links": [{"title": "<PERSON><PERSON><PERSON>, 10th Monarch of Goryeo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_10th_Monarch_of_Goryeo"}]}, {"year": "1088", "text": "<PERSON>, 1st Earl of Surrey, Norman nobleman", "html": "1088 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Surrey\" title=\"<PERSON>, 1st Earl of Surrey\"><PERSON>, 1st Earl of Surrey</a>, Norman nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Surrey\" title=\"<PERSON>, 1st Earl of Surrey\"><PERSON>, 1st Earl of Surrey</a>, Norman nobleman", "links": [{"title": "<PERSON>, 1st Earl of Surrey", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Surrey"}]}, {"year": "1314", "text": "<PERSON>, 8th Earl of Gloucester, English commander (b. 1291)", "html": "1314 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_8th_Earl_of_Gloucester\" title=\"<PERSON>, 8th Earl of Gloucester\"><PERSON>, 8th Earl of Gloucester</a>, English commander (b. 1291)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_8th_Earl_of_Gloucester\" title=\"<PERSON>, 8th Earl of Gloucester\"><PERSON>, 8th Earl of Gloucester</a>, English commander (b. 1291)", "links": [{"title": "<PERSON>, 8th Earl of Gloucester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_8th_Earl_of_Gloucester"}]}, {"year": "1314", "text": "<PERSON>, 1st Baron <PERSON>, English soldier and politician, Lord Warden of the Marches (b. 1274)", "html": "1314 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Marches\" title=\"Lord Warden of the Marches\">Lord Warden of the Marches</a> (b. 1274)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron de <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Marches\" title=\"Lord Warden of the Marches\">Lord Warden of the Marches</a> (b. 1274)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_<PERSON>"}, {"title": "Lord Warden of the Marches", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Marches"}]}, {"year": "1398", "text": "<PERSON><PERSON>, Chinese emperor (b. 1328)", "html": "1398 - <a href=\"https://wikipedia.org/wiki/Hongwu_Emperor\" title=\"Hongwu Emperor\"><PERSON><PERSON></a>, Chinese emperor (b. 1328)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hongwu_Emperor\" title=\"Hongwu Emperor\"><PERSON><PERSON></a>, Chinese emperor (b. 1328)", "links": [{"title": "Hongwu Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}]}, {"year": "1439", "text": "<PERSON>, duke of Austria (b. 1382)", "html": "1439 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON></a>, duke of Austria (b. 1382)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON></a>, duke of Austria (b. 1382)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}]}, {"year": "1503", "text": "<PERSON>, English architect and politician, Chancellor of the Duchy of Lancaster (b. 1440)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1440)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1440)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1519", "text": "<PERSON><PERSON><PERSON>, Italian wife of <PERSON>, Duke of Ferrara (b. 1480)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bo<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27<PERSON><PERSON>,_Duke_<PERSON>_Fe<PERSON>ra\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Ferrara\"><PERSON>, Duke of Ferrara</a> (b. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27<PERSON><PERSON>,_Duke_<PERSON>_Ferrara\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Ferrara\"><PERSON>, Duke of Ferrara</a> (b. 1480)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucrez<PERSON>_Borgia"}, {"title": "<PERSON>, Duke of Ferrara", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Este,_<PERSON>_<PERSON>_Ferrara"}]}, {"year": "1520", "text": "<PERSON><PERSON><PERSON>, Japanese commander (b. 1489)", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sumimoto\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sumimoto\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1489)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1604", "text": "<PERSON>, 17th Earl of Oxford, English courtier, Lord Great Chamberlain (b. 1550)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_17th_Earl_of_Oxford\" title=\"<PERSON>, 17th Earl of Oxford\"><PERSON>, 17th Earl of Oxford</a>, English courtier, <a href=\"https://wikipedia.org/wiki/Lord_Great_Chamberlain\" title=\"Lord Great Chamberlain\">Lord Great <PERSON></a> (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_17th_Earl_of_Oxford\" title=\"<PERSON>, 17th Earl of Oxford\"><PERSON>, 17th Earl of Oxford</a>, English courtier, <a href=\"https://wikipedia.org/wiki/Lord_Great_Chamberlain\" title=\"Lord Great Chamberlain\">Lord Great <PERSON></a> (b. 1550)", "links": [{"title": "<PERSON>, 17th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_17th_Earl_of_Oxford"}, {"title": "Lord Great <PERSON>", "link": "https://wikipedia.org/wiki/Lord_Great_<PERSON>"}]}, {"year": "1637", "text": "<PERSON><PERSON><PERSON>, French astronomer and historian (b. 1580)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and historian (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and historian (b. 1580)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON>, English politician (b. 1595)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1595)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1766", "text": "<PERSON><PERSON>, French soldier and politician, French Minister of Foreign Affairs (b. 1678)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French soldier and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a> (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French soldier and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a> (b. 1678)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (France)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)"}]}, {"year": "1778", "text": "<PERSON> the Younger, Dutch philologist and academic (b. 1714)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Dutch philologist and academic (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Dutch philologist and academic (b. 1714)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, Irish-American judge and politician (b. 1714)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American judge and politician (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American judge and politician (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, American lawyer and politician, 2nd Governor of Pennsylvania (b. 1734)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (b. 1734)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (b. 1734)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Pennsylvania", "link": "https://wikipedia.org/wiki/Governor_of_Pennsylvania"}]}, {"year": "1835", "text": "<PERSON>, Greek admiral and politician (b. 1769)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek admiral and politician (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek admiral and politician (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Australian politician, 2nd Premier of Western Australia (b. 1856)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American lawyer and politician, 22nd and 24th  President of the United States (b. 1837)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a>, American lawyer and politician, 22nd and 24th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a>, American lawyer and politician, 22nd and 24th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1837)", "links": [{"title": "Grover <PERSON>", "link": "https://wikipedia.org/wiki/Grover_Cleveland"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1909", "text": "<PERSON>, American novelist, short story writer, and poet (b. 1849)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and poet (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and poet (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, German businessman and politician, 7th German Minister for Foreign Affairs (b. 1867)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)\" title=\"Minister for Foreign Affairs (Germany)\">German Minister for Foreign Affairs</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)\" title=\"Minister for Foreign Affairs (Germany)\">German Minister for Foreign Affairs</a> (b. 1867)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Germany)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)"}]}, {"year": "1923", "text": "<PERSON>, Swedish-Finnish poet (b. 1892)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6dergran\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Swedish-speaking_population_of_Finland\" title=\"Swedish-speaking population of Finland\">Swedish-Finnish</a> poet (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%B6dergran\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Swedish-speaking_population_of_Finland\" title=\"Swedish-speaking population of Finland\">Swedish-Finnish</a> poet (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edith_S%C3%B6dergran"}, {"title": "Swedish-speaking population of Finland", "link": "https://wikipedia.org/wiki/Swedish-speaking_population_of_Finland"}]}, {"year": "1931", "text": "<PERSON>, Russian-American businessman (b. 1840)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American businessman (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American businessman (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Chinese politician, 2nd General Secretary of the Chinese Communist Party (b. 1880)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician, 2nd <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician, 2nd <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "General Secretary of the Chinese Communist Party", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party"}]}, {"year": "1932", "text": "<PERSON>, Estonian general (b. 1879)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B5dder\" title=\"<PERSON>\"><PERSON></a>, Estonian general (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B5dder\" title=\"<PERSON>\"><PERSON></a>, Estonian general (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_P%C3%B5dder"}]}, {"year": "1943", "text": "<PERSON>, Canadian priest and critic (b. 1870)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(literary_critic)\" title=\"<PERSON> (literary critic)\"><PERSON></a>, Canadian priest and critic (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(literary_critic)\" title=\"<PERSON> (literary critic)\"><PERSON></a>, Canadian priest and critic (b. 1870)", "links": [{"title": "<PERSON> (literary critic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(literary_critic)"}]}, {"year": "1946", "text": "<PERSON>, American philanthropist (b. 1857)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American politician, Mayor of Milwaukee (b. 1864)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Milwaukee\" class=\"mw-redirect\" title=\"Mayor of Milwaukee\">Mayor of Milwaukee</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Milwaukee\" class=\"mw-redirect\" title=\"Mayor of Milwaukee\">Mayor of Milwaukee</a> (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Milwaukee", "link": "https://wikipedia.org/wiki/Mayor_of_Milwaukee"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian composer, pianist and music critic (b. 1906)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Volfgangs_D%C4%81rzi%C5%86%C5%A1\" title=\"Volfgangs Dārziņš\">Volfgangs <PERSON></a>, Latvian composer, pianist and music critic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Volfgangs_D%C4%81rzi%C5%86%C5%A1\" title=\"Volfgangs Dārziņš\">Volfgangs <PERSON></a>, Latvian composer, pianist and music critic (b. 1906)", "links": [{"title": "Volfgangs Dārziņš", "link": "https://wikipedia.org/wiki/Volfgangs_D%C4%81rzi%C5%86%C5%A1"}]}, {"year": "1964", "text": "<PERSON>, American painter and academic (b. 1892)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter and academic (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter and academic (b. 1892)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1969", "text": "<PERSON>, American cartoonist (b. 1883)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (b. 1883)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)"}]}, {"year": "1969", "text": "<PERSON>, German-American historian and author (b. 1906)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian and author (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian and author (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Professional Basketball Player in the ABA", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Professional Basketball Player in the ABA", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Professional Basketball Player in the ABA", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American photographer, critic, and academic (b. 1908)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Minor_White\" title=\"Minor White\"><PERSON></a>, American photographer, critic, and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Minor_White\" title=\"Minor White\"><PERSON></a>, American photographer, critic, and academic (b. 1908)", "links": [{"title": "Minor White", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, French author and critic (b. 1909)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, 4th President of India (b. 1894)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/V._V._Giri\" title=\"V. V. Giri\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V._V._Giri\" title=\"V. V. Giri\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1894)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V._V._Giri"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1984", "text": "<PERSON>, Canadian businessman (b. 1905)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor, comedian, and producer (b. 1916)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and producer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and producer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Hungarian race car driver (b. 1962)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Csaba_Kesj%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian race car driver (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Csaba_Kesj%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian race car driver (b. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Csaba_Kesj%C3%A1r"}]}, {"year": "1991", "text": "<PERSON>, Australian-American author and playwright (b. 1917)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American author and playwright (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American author and playwright (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Mexican painter and illustrator (b. 1899)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican painter and illustrator (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican painter and illustrator (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Canadian violinist, composer, and conductor (b. 1915)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, composer, and conductor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, composer, and conductor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American politician and attorney <PERSON><PERSON><PERSON><PERSON> v. United States (b. 1903)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and attorney <i><a href=\"https://wikipedia.org/wiki/Morissette_v._United_States\" title=\"Morissette v. United States\">Morissette v. United States</a></i> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and attorney <i><a href=\"https://wikipedia.org/wiki/Morissette_v._United_States\" title=\"Morissette v. United States\">Morissette v. United States</a></i> (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ue"}, {"title": "Morissette v. United States", "link": "https://wikipedia.org/wiki/Morissette_v._United_States"}]}, {"year": "1997", "text": "<PERSON>, American actor (b. 1921)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, British intelligence officer (b. 1908)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British intelligence officer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British intelligence officer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English actor and comedian (b. 1917)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Argentine cuarteto singer (b. 1973)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, Argentine cuarteto singer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentine cuarteto singer (b. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, the second head of the world's first cosmodrome — \"Baikonur\" (1958-1961).", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the second head of the world's first cosmodrome — \"Baikonur\" (1958-1961).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the second head of the world's first cosmodrome — \"Baikonur\" (1958-1961).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Luxembourgish banker and politician, 21st Prime Minister of Luxembourg (b. 1913)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgish banker and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg\" class=\"mw-redirect\" title=\"Prime Minister of Luxembourg\">Prime Minister of Luxembourg</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgish banker and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg\" class=\"mw-redirect\" title=\"Prime Minister of Luxembourg\">Prime Minister of Luxembourg</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Luxembourg", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek songwriter and author (b. 1957)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Ifigeneia_Giannopoulou\" title=\"Ifigeneia Giannopoulou\">Ifige<PERSON><PERSON></a>, Greek songwriter and author (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ifigeneia_Giannopoulou\" title=\"Ifigeneia Giannopoulou\">Ifige<PERSON><PERSON></a>, Greek songwriter and author (b. 1957)", "links": [{"title": "Ifigeneia <PERSON>ulou", "link": "https://wikipedia.org/wiki/Ifigeneia_Gian<PERSON>u"}]}, {"year": "2005", "text": "<PERSON>, American actor, voice artist, and ventriloquist (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, and ventriloquist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, and ventriloquist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Danish rapper and reggae singer (b. 1974)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Natas<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish rapper and reggae singer (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Nat<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish rapper and reggae singer (b. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ad"}]}, {"year": "2007", "text": "<PERSON>, Canadian wrestler (b. 1967)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Northern Irish footballer and manager (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Austrian mathematician and academic (b. 1919)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician and academic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian journalist and politician, 25th Governor General of Canada (b. 1927)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Rom%C3%A9o_LeBlanc\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian journalist and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rom%C3%A9o_LeBlanc\" title=\"Rom<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian journalist and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rom%C3%A9o_LeBlanc"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "2010", "text": "<PERSON>, American jazz tenor saxophonist (b. 1929)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American jazz tenor saxophonist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American jazz tenor saxophonist (b. 1929)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Croatian football coach and manager (b. 1933)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian football coach and manager (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian football coach and manager (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American baseball player and coach (b. 1962)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, German author and educator (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, German author and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, German author and educator (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Chinese mathematician and academic (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese mathematician and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese mathematician and academic (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Spanish footballer (b. 1988)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Roqu%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oqu%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer (b. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON>_Roqu%C3%A9"}]}, {"year": "2012", "text": "<PERSON>, American lawyer, educator, and activist (b. 1952)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Ann_C._Scales\" title=\"Ann <PERSON> Scales\"><PERSON></a>, American lawyer, educator, and activist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ann_C._Scales\" title=\"Ann <PERSON> Scales\"><PERSON> <PERSON></a>, American lawyer, educator, and activist (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ann_C._Scales"}]}, {"year": "2012", "text": "Lonesome <PERSON>, last known Pinta Island tortoise (h. c. 1910)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Lonesome_George\" title=\"Lonesome George\">Lonesome George</a>, <a href=\"https://wikipedia.org/wiki/Endling\" title=\"<PERSON><PERSON>\">last known</a> <a href=\"https://wikipedia.org/wiki/Pinta_Island_tortoise\" title=\"Pinta Island tortoise\">Pinta Island tortoise</a> (h. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1910</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lone<PERSON>_George\" title=\"Lonesome George\">Lonesome George</a>, <a href=\"https://wikipedia.org/wiki/Endling\" title=\"Endling\">last known</a> <a href=\"https://wikipedia.org/wiki/Pinta_Island_tortoise\" title=\"Pinta Island tortoise\">Pinta Island tortoise</a> (h. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1910</span>)", "links": [{"title": "Lonesome <PERSON>", "link": "https://wikipedia.org/wiki/Lone<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Endling"}, {"title": "Pinta Island tortoise", "link": "https://wikipedia.org/wiki/Pinta_Island_tortoise"}]}, {"year": "2013", "text": "<PERSON>, English archaeologist and academic (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mick Aston\"><PERSON></a>, English archaeologist and academic (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mick_Aston\" title=\"Mick Aston\"><PERSON></a>, English archaeologist and academic (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Italian politician, 40th Prime Minister of Italy (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Dutch bishop (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch bishop (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch bishop (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English-Bermudian computer scientist and author (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English-Bermudian computer scientist and author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English-Bermudian computer scientist and author (b. 1933)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "2013", "text": "<PERSON>, American drummer  (b. 1955)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer (b. 1955)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>(drummer)"}]}, {"year": "2014", "text": "<PERSON>, Canadian lawyer and politician (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ontario_politician)\" title=\"<PERSON> (Ontario politician)\"><PERSON></a>, Canadian lawyer and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ontario_politician)\" title=\"<PERSON> (Ontario politician)\"><PERSON></a>, Canadian lawyer and politician (b. 1928)", "links": [{"title": "<PERSON> (Ontario politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Ontario_politician)"}]}, {"year": "2014", "text": "<PERSON>, Canadian runner and softball player (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian runner and softball player (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian runner and softball player (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Venezuelan journalist, lawyer, and politician, President of Venezuela (b. 1916)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Jos%C3%A9_Vel%C3%A1squez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan journalist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Jo<PERSON>%C3%A9_Vel%C3%A1squez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan journalist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Jos%C3%A9_Vel%C3%A1squez"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "2014", "text": "<PERSON>, American actor (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Brazilian singer-songwriter (b. 1986)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/C<PERSON><PERSON>_<PERSON>%C3%BAjo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C<PERSON><PERSON>_<PERSON>%C3%BAjo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter (b. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cristiano_Ara%C3%BAjo"}]}, {"year": "2015", "text": "<PERSON>, American police officer, politician and criminal (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer, politician and criminal (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer, politician and criminal (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American author and educator (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and educator (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and educator (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American lieutenant (b. 1915)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, 15th President of the Philippines (b. 1960)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> III</a>, 15th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 15th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON><PERSON>, 7th Prime Minister of South Vietnam and army officer (b. 1925)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Thi%E1%BB%87n_Khi%C3%AAm\" title=\"Trần Thi<PERSON> Khiêm\">Tr<PERSON><PERSON></a>, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Vietnam\" class=\"mw-redirect\" title=\"Prime Minister of the Republic of Vietnam\">Prime Minister of South Vietnam</a> and army officer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Thi%E1%BB%87n_Khi%C3%AAm\" title=\"Trần Thiện Khiêm\">Tr<PERSON><PERSON></a>, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Vietnam\" class=\"mw-redirect\" title=\"Prime Minister of the Republic of Vietnam\">Prime Minister of South Vietnam</a> and army officer (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_Thi%E1%BB%87n_Khi%C3%AAm"}, {"title": "Prime Minister of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Vietnam"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American vocalist (b. 1974)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Shifty_<PERSON>shock\" title=\"Shifty Shellshock\"><PERSON><PERSON></a>, American vocalist (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shi<PERSON>_<PERSON>shock\" title=\"Shifty Shellshock\"><PERSON><PERSON></a>, American vocalist (b. 1974)", "links": [{"title": "Shifty Shellshock", "link": "https://wikipedia.org/wiki/Shi<PERSON>_Shellshock"}]}]}}