{"date": "July 9", "url": "https://wikipedia.org/wiki/July_9", "data": {"Events": [{"year": "118", "text": "<PERSON><PERSON>, who became emperor a year previously on <PERSON><PERSON><PERSON>'s death, makes his entry into Rome.", "html": "118 - <a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"Hadrian\"><PERSON><PERSON></a>, who became emperor a year previously on <PERSON><PERSON><PERSON>'s death, makes his entry into Rome.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, who became emperor a year previously on <PERSON><PERSON><PERSON>'s death, makes his entry into Rome.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "381", "text": "The end of the First Council of Christian bishops convened in Constantinople by the Roman emperor <PERSON><PERSON><PERSON>.", "html": "381 - The end of the <a href=\"https://wikipedia.org/wiki/First_Council_of_Constantinople\" title=\"First Council of Constantinople\">First Council</a> of Christian bishops convened in Constantinople by the Roman emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON>ius I\"><PERSON><PERSON><PERSON> I</a>.", "no_year_html": "The end of the <a href=\"https://wikipedia.org/wiki/First_Council_of_Constantinople\" title=\"First Council of Constantinople\">First Council</a> of Christian bishops convened in Constantinople by the Roman emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON>ius I\"><PERSON><PERSON><PERSON> I</a>.", "links": [{"title": "First Council of Constantinople", "link": "https://wikipedia.org/wiki/First_Council_of_Constantinople"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}]}, {"year": "491", "text": "<PERSON><PERSON><PERSON><PERSON> makes a night assault with his Heruli guardsmen, engaging <PERSON><PERSON><PERSON> the Great in Ad Pinetam. Both sides suffer heavy losses, but in the end <PERSON><PERSON> forces <PERSON><PERSON><PERSON><PERSON> back into Ravenna.", "html": "491 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>r\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> makes a night assault with his <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> guardsmen, engaging <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a> in Ad Pinetam. Both sides suffer heavy losses, but in the end <PERSON><PERSON> forces <PERSON><PERSON><PERSON><PERSON> back into <a href=\"https://wikipedia.org/wiki/Ravenna\" title=\"Ravenna\">Ravenna</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> makes a night assault with his <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> guardsmen, engaging <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a> in Ad Pinetam. Both sides suffer heavy losses, but in the end <PERSON><PERSON> forces <PERSON><PERSON><PERSON><PERSON> back into <a href=\"https://wikipedia.org/wiki/Ravenna\" title=\"Ravenna\">Ravenna</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odoacer"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Theo<PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/Theoder<PERSON>_the_Great"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ravenna"}]}, {"year": "551", "text": "A major earthquake strikes Beirut, triggering a devastating tsunami that affects the coastal towns of Byzantine Phoenicia, causing thousands of deaths.", "html": "551 - A <a href=\"https://wikipedia.org/wiki/551_Beirut_earthquake\" title=\"551 Beirut earthquake\">major earthquake</a> strikes Beirut, triggering a devastating tsunami that affects the coastal towns of <a href=\"https://wikipedia.org/wiki/Phoenicia_under_Roman_rule\" title=\"Phoenicia under Roman rule\">Byzantine Phoenicia</a>, causing thousands of deaths.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/551_Beirut_earthquake\" title=\"551 Beirut earthquake\">major earthquake</a> strikes Beirut, triggering a devastating tsunami that affects the coastal towns of <a href=\"https://wikipedia.org/wiki/Phoenicia_under_Roman_rule\" title=\"Phoenicia under Roman rule\">Byzantine Phoenicia</a>, causing thousands of deaths.", "links": [{"title": "551 Beirut earthquake", "link": "https://wikipedia.org/wiki/551_Beirut_earthquake"}, {"title": "Phoenicia under Roman rule", "link": "https://wikipedia.org/wiki/Phoenicia_under_Roman_rule"}]}, {"year": "660", "text": "Korean forces under general <PERSON> of Silla defeat the army of Baekje in the Battle of Hwangsanbeol.", "html": "660 - Korean forces under general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Silla defeat the army of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hwangsanbeol\" title=\"Battle of Hwangsanbeol\">Battle of Hwangsanbeol</a>.", "no_year_html": "Korean forces under general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Silla defeat the army of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hwangsanbeol\" title=\"Battle of Hwangsanbeol\">Battle of Hwangsanbeol</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "<PERSON>ek<PERSON>", "link": "https://wikipedia.org/wiki/Baekje"}, {"title": "Battle of Hwangsanbeol", "link": "https://wikipedia.org/wiki/Battle_of_Hwangsanbeol"}]}, {"year": "869", "text": "The 8.4-9.0 Mw Sanriku earthquake strikes the area around Sendai in northern Honshu, Japan. Inundation from the tsunami extended several kilometers inland.", "html": "869 - The 8.4-9.0 M<sub>w</sub> <a href=\"https://wikipedia.org/wiki/869_Sanriku_earthquake\" class=\"mw-redirect\" title=\"869 Sanriku earthquake\">Sanriku earthquake</a> strikes the area around <a href=\"https://wikipedia.org/wiki/Sendai\" title=\"Sendai\">Sendai</a> in northern <a href=\"https://wikipedia.org/wiki/Honshu\" title=\"Honshu\">Honshu</a>, Japan. Inundation from the tsunami extended several kilometers inland.", "no_year_html": "The 8.4-9.0 M<sub>w</sub> <a href=\"https://wikipedia.org/wiki/869_Sanriku_earthquake\" class=\"mw-redirect\" title=\"869 Sanriku earthquake\">Sanriku earthquake</a> strikes the area around <a href=\"https://wikipedia.org/wiki/Sendai\" title=\"Sendai\">Sendai</a> in northern <a href=\"https://wikipedia.org/wiki/Honshu\" title=\"Honshu\">Honshu</a>, Japan. Inundation from the tsunami extended several kilometers inland.", "links": [{"title": "869 Sanriku earthquake", "link": "https://wikipedia.org/wiki/869_Sanriku_earthquake"}, {"title": "Sendai", "link": "https://wikipedia.org/wiki/Sendai"}, {"title": "Honshu", "link": "https://wikipedia.org/wiki/Honshu"}]}, {"year": "969", "text": "The Fatimid general <PERSON><PERSON><PERSON> leads the Friday prayer in Fustat in the name of <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, thereby symbolically completing the Fatimid conquest of Egypt.", "html": "969 - The <a href=\"https://wikipedia.org/wiki/Fatimid\" class=\"mw-redirect\" title=\"Fatimid\">Fatimid</a> general <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(general)\" title=\"<PERSON><PERSON><PERSON> (general)\"><PERSON><PERSON><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Friday_prayer\" title=\"Friday prayer\">Friday prayer</a> in <a href=\"https://wikipedia.org/wiki/Fustat\" title=\"Fustat\">Fustat</a> in the name of Caliph <a href=\"https://wikipedia.org/wiki/Al-Mu%27izz_li-<PERSON>_Allah\" title=\"Al-Mu'izz li-<PERSON> Allah\">al-<PERSON><PERSON>izz li-<PERSON></a>, thereby symbolically completing the <a href=\"https://wikipedia.org/wiki/Fatimid_conquest_of_Egypt\" title=\"Fatimid conquest of Egypt\">Fatimid conquest of Egypt</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fatimid\" class=\"mw-redirect\" title=\"Fatimid\">Fatimid</a> general <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(general)\" title=\"<PERSON><PERSON><PERSON> (general)\"><PERSON><PERSON><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Friday_prayer\" title=\"Friday prayer\">Friday prayer</a> in <a href=\"https://wikipedia.org/wiki/Fustat\" title=\"Fustat\">Fustat</a> in the name of Caliph <a href=\"https://wikipedia.org/wiki/Al-Mu%27izz_li-<PERSON>_Allah\" title=\"Al-Mu'izz li-<PERSON> Allah\">al-<PERSON><PERSON>izz li-<PERSON></a>, thereby symbolically completing the <a href=\"https://wikipedia.org/wiki/Fatimid_conquest_of_Egypt\" title=\"Fatimid conquest of Egypt\">Fatimid conquest of Egypt</a>.", "links": [{"title": "Fatimid", "link": "https://wikipedia.org/wiki/Fatimid"}, {"title": "<PERSON><PERSON><PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(general)"}, {"title": "Friday prayer", "link": "https://wikipedia.org/wiki/Friday_prayer"}, {"title": "Fustat", "link": "https://wikipedia.org/wiki/Fustat"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Mu%27izz_li-<PERSON>_<PERSON>"}, {"title": "Fatimid conquest of Egypt", "link": "https://wikipedia.org/wiki/Fatimid_conquest_of_Egypt"}]}, {"year": "1357", "text": "Emperor <PERSON> assists in laying the foundation stone of Charles Bridge in Prague.", "html": "1357 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\">Emperor <PERSON> IV</a> assists in laying the <a href=\"https://wikipedia.org/wiki/Cornerstone\" title=\"Cornerstone\">foundation stone</a> of <a href=\"https://wikipedia.org/wiki/Charles_Bridge\" title=\"Charles Bridge\">Charles Bridge</a> in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\">Emperor <PERSON></a> assists in laying the <a href=\"https://wikipedia.org/wiki/Cornerstone\" title=\"Cornerstone\">foundation stone</a> of <a href=\"https://wikipedia.org/wiki/Charles_Bridge\" title=\"Charles Bridge\">Charles Bridge</a> in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Cornerstone", "link": "https://wikipedia.org/wiki/Cornerstone"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}]}, {"year": "1386", "text": "The Old Swiss Confederacy makes great strides in establishing control over its territory by soundly defeating the Duchy of Austria in the Battle of Sempach.", "html": "1386 - The <a href=\"https://wikipedia.org/wiki/Old_Swiss_Confederacy\" title=\"Old Swiss Confederacy\">Old Swiss Confederacy</a> makes great strides in <a href=\"https://wikipedia.org/wiki/Growth_of_the_Old_Swiss_Confederacy\" title=\"Growth of the Old Swiss Confederacy\">establishing control over its territory</a> by soundly defeating the <a href=\"https://wikipedia.org/wiki/Duchy_of_Austria\" title=\"Duchy of Austria\">Duchy of Austria</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Sempach\" title=\"Battle of Sempach\">Battle of Sempach</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Old_Swiss_Confederacy\" title=\"Old Swiss Confederacy\">Old Swiss Confederacy</a> makes great strides in <a href=\"https://wikipedia.org/wiki/Growth_of_the_Old_Swiss_Confederacy\" title=\"Growth of the Old Swiss Confederacy\">establishing control over its territory</a> by soundly defeating the <a href=\"https://wikipedia.org/wiki/Duchy_of_Austria\" title=\"Duchy of Austria\">Duchy of Austria</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Sempach\" title=\"Battle of Sempach\">Battle of Sempach</a>.", "links": [{"title": "Old Swiss Confederacy", "link": "https://wikipedia.org/wiki/Old_Swiss_Confederacy"}, {"title": "Growth of the Old Swiss Confederacy", "link": "https://wikipedia.org/wiki/Growth_of_the_Old_Swiss_Confederacy"}, {"title": "Duchy of Austria", "link": "https://wikipedia.org/wiki/Duchy_of_Austria"}, {"title": "Battle of Sempach", "link": "https://wikipedia.org/wiki/Battle_of_Sempach"}]}, {"year": "1401", "text": "<PERSON><PERSON> attacks the Jalairid Sultanate and destroys Baghdad.", "html": "1401 - <a href=\"https://wikipedia.org/wiki/<PERSON>ur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> attacks the <a href=\"https://wikipedia.org/wiki/Jalairid_Sultanate\" class=\"mw-redirect\" title=\"Jalairid Sultanate\">Jalairid Sultanate</a> and destroys Baghdad.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> attacks the <a href=\"https://wikipedia.org/wiki/Jalairid_Sultanate\" class=\"mw-redirect\" title=\"Jalairid Sultanate\">Jalairid Sultanate</a> and destroys Baghdad.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ur"}, {"title": "Jalairid Sultanate", "link": "https://wikipedia.org/wiki/Jalairid_Sultanate"}]}, {"year": "1540", "text": "King <PERSON> of England annuls his marriage to his fourth wife, <PERSON> of Cleves.", "html": "1540 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII\" title=\"<PERSON> VIII\"><PERSON></a> of England annuls his marriage to his fourth wife, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Cleves\"><PERSON> Cleves</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VIII\"><PERSON></a> of England annuls his marriage to his fourth wife, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Cleves\"><PERSON>es</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Cleves", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1572", "text": "Nineteen Catholics suffer martyrdom for their beliefs, in the Dutch town of Gorkum.", "html": "1572 - <a href=\"https://wikipedia.org/wiki/Martyrs_of_Gorkum\" title=\"Martyrs of Gorkum\">Nineteen Catholics</a> suffer martyrdom for their beliefs, in the Dutch town of <a href=\"https://wikipedia.org/wiki/Gorinchem\" title=\"Gorinchem\">Gorkum</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martyrs_of_Gorkum\" title=\"Martyrs of Gorkum\">Nineteen Catholics</a> suffer martyrdom for their beliefs, in the Dutch town of <a href=\"https://wikipedia.org/wiki/Gorinchem\" title=\"Gorinchem\">Gorkum</a>.", "links": [{"title": "Martyrs of Gorkum", "link": "https://wikipedia.org/wiki/Martyrs_of_Gorkum"}, {"title": "Gorinchem", "link": "https://wikipedia.org/wiki/Gorinchem"}]}, {"year": "1609", "text": "Bohemia is granted freedom of religion through the Letter of Majesty by the Holy Roman Emperor, <PERSON>.", "html": "1609 - <a href=\"https://wikipedia.org/wiki/Bohemia\" title=\"Bohemia\">Bohemia</a> is granted <a href=\"https://wikipedia.org/wiki/Freedom_of_religion\" title=\"Freedom of religion\">freedom of religion</a> through the <a href=\"https://wikipedia.org/wiki/Letter_of_Majesty\" title=\"Letter of Majesty\">Letter of Majesty</a> by the Holy Roman Emperor, <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" class=\"mw-redirect\" title=\"<PERSON> II\"><PERSON> II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bohemia\" title=\"Bohemia\">Bohemia</a> is granted <a href=\"https://wikipedia.org/wiki/Freedom_of_religion\" title=\"Freedom of religion\">freedom of religion</a> through the <a href=\"https://wikipedia.org/wiki/Letter_of_Majesty\" title=\"Letter of Majesty\">Letter of Majesty</a> by the Holy Roman Emperor, <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" class=\"mw-redirect\" title=\"<PERSON> II\"><PERSON> II</a>.", "links": [{"title": "Bohemia", "link": "https://wikipedia.org/wiki/Bohemia"}, {"title": "Freedom of religion", "link": "https://wikipedia.org/wiki/Freedom_of_religion"}, {"title": "Letter of Majesty", "link": "https://wikipedia.org/wiki/Letter_of_Majesty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1701", "text": "A Bourbon force under <PERSON> withdraws from a smaller Habsburg force under Prince <PERSON> of Savoy in the Battle of Carpi.", "html": "1701 - A <a href=\"https://wikipedia.org/wiki/House_of_Bourbon\" title=\"House of Bourbon\">Bourbon</a> force under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> withdraws from a smaller <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Habsburg</a> force under <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Savoy\" title=\"Prince <PERSON> of Savoy\">Prince <PERSON> of Savoy</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Carpi\" title=\"Battle of Carpi\">Battle of Carpi</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/House_of_Bourbon\" title=\"House of Bourbon\">Bourbon</a> force under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> withdraws from a smaller <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Habsburg</a> force under <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Savoy\" title=\"Prince <PERSON> of Savoy\">Prince <PERSON> of Savoy</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Carpi\" title=\"Battle of Carpi\">Battle of Carpi</a>.", "links": [{"title": "House of Bourbon", "link": "https://wikipedia.org/wiki/House_of_Bourbon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Habsburg monarchy", "link": "https://wikipedia.org/wiki/Habsburg_monarchy"}, {"title": "Prince <PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Savoy"}, {"title": "Battle of Carpi", "link": "https://wikipedia.org/wiki/Battle_of_Carpi"}]}, {"year": "1745", "text": "French victory in the Battle of Melle allows them to capture Ghent in the days after.", "html": "1745 - French victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Melle\" title=\"Battle of Melle\">Battle of Melle</a> allows them to capture <a href=\"https://wikipedia.org/wiki/Ghent\" title=\"Ghent\">Ghent</a> in the days after.", "no_year_html": "French victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Melle\" title=\"Battle of Melle\">Battle of Melle</a> allows them to capture <a href=\"https://wikipedia.org/wiki/Ghent\" title=\"Ghent\">Ghent</a> in the days after.", "links": [{"title": "Battle of Melle", "link": "https://wikipedia.org/wiki/Battle_of_Melle"}, {"title": "Ghent", "link": "https://wikipedia.org/wiki/Ghent"}]}, {"year": "1755", "text": "The Braddock Expedition is soundly defeated by a smaller French and Native American force in its attempt to capture Fort Duquesne in what is now downtown Pittsburgh.", "html": "1755 - The <a href=\"https://wikipedia.org/wiki/Braddock_Expedition\" title=\"Braddock Expedition\">Braddock Expedition</a> is <a href=\"https://wikipedia.org/wiki/Battle_of_the_Monongahela\" title=\"Battle of the Monongahela\">soundly defeated</a> by a smaller French and <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native American</a> force in its attempt to capture <a href=\"https://wikipedia.org/wiki/Fort_Duquesne\" title=\"Fort Duquesne\">Fort Duquesne</a> in what is now <a href=\"https://wikipedia.org/wiki/Downtown_Pittsburgh\" title=\"Downtown Pittsburgh\">downtown Pittsburgh</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Braddock_Expedition\" title=\"Braddock Expedition\">Braddock Expedition</a> is <a href=\"https://wikipedia.org/wiki/Battle_of_the_Monongahela\" title=\"Battle of the Monongahela\">soundly defeated</a> by a smaller French and <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native American</a> force in its attempt to capture <a href=\"https://wikipedia.org/wiki/Fort_Duquesne\" title=\"Fort Duquesne\">Fort Duquesne</a> in what is now <a href=\"https://wikipedia.org/wiki/Downtown_Pittsburgh\" title=\"Downtown Pittsburgh\">downtown Pittsburgh</a>.", "links": [{"title": "Braddock Expedition", "link": "https://wikipedia.org/wiki/Braddock_Expedition"}, {"title": "Battle of the Monongahela", "link": "https://wikipedia.org/wiki/Battle_of_the_Monongahela"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}, {"title": "Fort Duquesne", "link": "https://wikipedia.org/wiki/Fort_Duquesne"}, {"title": "Downtown Pittsburgh", "link": "https://wikipedia.org/wiki/Downtown_Pittsburgh"}]}, {"year": "1762", "text": "<PERSON> the Great becomes Empress of Russia following the coup against her husband, <PERSON>.", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> becomes Empress of Russia following the coup against her husband, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> becomes Empress of Russia following the coup against her husband, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON></a>.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1763", "text": "The <PERSON> family grand tour of Europe began, lifting the profile of son <PERSON>.", "html": "1763 - The <a href=\"https://wikipedia.org/wiki/Mozart_family_grand_tour\" title=\"Mozart family grand tour\">Mozart family grand tour</a> of Europe began, lifting the profile of son <a href=\"https://wikipedia.org/wiki/<PERSON>_Amade<PERSON>_<PERSON>\" title=\"Wolfgang Amadeus Mozart\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mozart_family_grand_tour\" title=\"Mozart family grand tour\">Mozart family grand tour</a> of Europe began, lifting the profile of son <a href=\"https://wikipedia.org/wiki/<PERSON>_Amade<PERSON>_Mozart\" title=\"Wolfgang Amadeus Mozart\"><PERSON></a>.", "links": [{"title": "Mozart family grand tour", "link": "https://wikipedia.org/wiki/Mozart_family_grand_tour"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON> orders the Declaration of Independence to be read out to members of the Continental Army in Manhattan, while thousands of British troops on Staten Island prepare for the Battle of Long Island.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/United_States_Declaration_of_Independence\" title=\"United States Declaration of Independence\">Declaration of Independence</a> to be read out to members of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> in <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a>, while thousands of British troops on <a href=\"https://wikipedia.org/wiki/Staten_Island\" title=\"Staten Island\">Staten Island</a> prepare for the <a href=\"https://wikipedia.org/wiki/Battle_of_Long_Island\" title=\"Battle of Long Island\">Battle of Long Island</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/United_States_Declaration_of_Independence\" title=\"United States Declaration of Independence\">Declaration of Independence</a> to be read out to members of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> in <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a>, while thousands of British troops on <a href=\"https://wikipedia.org/wiki/Staten_Island\" title=\"Staten Island\">Staten Island</a> prepare for the <a href=\"https://wikipedia.org/wiki/Battle_of_Long_Island\" title=\"Battle of Long Island\">Battle of Long Island</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "United States Declaration of Independence", "link": "https://wikipedia.org/wiki/United_States_Declaration_of_Independence"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "Manhattan", "link": "https://wikipedia.org/wiki/Manhattan"}, {"title": "Staten Island", "link": "https://wikipedia.org/wiki/Staten_Island"}, {"title": "Battle of Long Island", "link": "https://wikipedia.org/wiki/Battle_of_Long_Island"}]}, {"year": "1789", "text": "In Versailles, the National Assembly reconstitutes itself as the National Constituent Assembly and begins preparations for a French constitution.", "html": "1789 - In <a href=\"https://wikipedia.org/wiki/Versailles,_Yvelines\" title=\"Versailles, Yvelines\">Versailles</a>, the <a href=\"https://wikipedia.org/wiki/National_Assembly_(French_Revolution)\" title=\"National Assembly (French Revolution)\">National Assembly</a> reconstitutes itself as the <a href=\"https://wikipedia.org/wiki/National_Constituent_Assembly_(France)\" title=\"National Constituent Assembly (France)\">National Constituent Assembly</a> and begins preparations for a <a href=\"https://wikipedia.org/wiki/Constitution_of_France\" title=\"Constitution of France\">French constitution</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Versailles,_Yvelines\" title=\"Versailles, Yvelines\">Versailles</a>, the <a href=\"https://wikipedia.org/wiki/National_Assembly_(French_Revolution)\" title=\"National Assembly (French Revolution)\">National Assembly</a> reconstitutes itself as the <a href=\"https://wikipedia.org/wiki/National_Constituent_Assembly_(France)\" title=\"National Constituent Assembly (France)\">National Constituent Assembly</a> and begins preparations for a <a href=\"https://wikipedia.org/wiki/Constitution_of_France\" title=\"Constitution of France\">French constitution</a>.", "links": [{"title": "Versailles, Yvelines", "link": "https://wikipedia.org/wiki/Versailles,_Yvelines"}, {"title": "National Assembly (French Revolution)", "link": "https://wikipedia.org/wiki/National_Assembly_(French_Revolution)"}, {"title": "National Constituent Assembly (France)", "link": "https://wikipedia.org/wiki/National_Constituent_Assembly_(France)"}, {"title": "Constitution of France", "link": "https://wikipedia.org/wiki/Constitution_of_France"}]}, {"year": "1790", "text": "The Swedish Navy captures one third of the Russian Baltic fleet.", "html": "1790 - The <a href=\"https://wikipedia.org/wiki/Swedish_Navy\" title=\"Swedish Navy\">Swedish Navy</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Svensksund\" title=\"Battle of Svensksund\">captures one third</a> of the <a href=\"https://wikipedia.org/wiki/Russian_Navy\" title=\"Russian Navy\">Russian Baltic fleet</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Swedish_Navy\" title=\"Swedish Navy\">Swedish Navy</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Svensksund\" title=\"Battle of Svensksund\">captures one third</a> of the <a href=\"https://wikipedia.org/wiki/Russian_Navy\" title=\"Russian Navy\">Russian Baltic fleet</a>.", "links": [{"title": "Swedish Navy", "link": "https://wikipedia.org/wiki/Swedish_Navy"}, {"title": "Battle of Svensksund", "link": "https://wikipedia.org/wiki/Battle_of_Svensksund"}, {"title": "Russian Navy", "link": "https://wikipedia.org/wiki/Russian_Navy"}]}, {"year": "1793", "text": "The Act Against Slavery in Upper Canada bans the importation of slaves and will free those who are born into slavery after the passage of the Act at 25 years of age.", "html": "1793 - The <a href=\"https://wikipedia.org/wiki/Act_Against_Slavery\" title=\"Act Against Slavery\">Act Against Slavery</a> in <a href=\"https://wikipedia.org/wiki/Upper_Canada\" title=\"Upper Canada\">Upper Canada</a> bans the importation of slaves and will free those who are born into slavery after the passage of the Act at 25 years of age.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Act_Against_Slavery\" title=\"Act Against Slavery\">Act Against Slavery</a> in <a href=\"https://wikipedia.org/wiki/Upper_Canada\" title=\"Upper Canada\">Upper Canada</a> bans the importation of slaves and will free those who are born into slavery after the passage of the Act at 25 years of age.", "links": [{"title": "Act Against Slavery", "link": "https://wikipedia.org/wiki/Act_Against_Slavery"}, {"title": "Upper Canada", "link": "https://wikipedia.org/wiki/Upper_Canada"}]}, {"year": "1795", "text": "Financier <PERSON> pays off the $2,024,899 US national debt that had been accrued during the American Revolution.", "html": "1795 - Financier <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)\" title=\"<PERSON> (financier)\"><PERSON></a> pays off the $2,024,899 US national debt that had been accrued during the <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>.", "no_year_html": "Financier <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)\" title=\"<PERSON> (financier)\"><PERSON></a> pays off the $2,024,899 US national debt that had been accrued during the <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>.", "links": [{"title": "<PERSON> (financier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)"}, {"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}]}, {"year": "1807", "text": "The second Treaty of Tilsit is signed between France and Prussia, ending the War of the Fourth Coalition.", "html": "1807 - The second <a href=\"https://wikipedia.org/wiki/Treaties_of_Tilsit\" title=\"Treaties of Tilsit\">Treaty of Tilsit</a> is signed between <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">France</a> and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>, ending the <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a>.", "no_year_html": "The second <a href=\"https://wikipedia.org/wiki/Treaties_of_Tilsit\" title=\"Treaties of Tilsit\">Treaty of Tilsit</a> is signed between <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">France</a> and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>, ending the <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a>.", "links": [{"title": "Treaties of Tilsit", "link": "https://wikipedia.org/wiki/Treaties_of_Tilsit"}, {"title": "First French Empire", "link": "https://wikipedia.org/wiki/First_French_Empire"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "War of the Fourth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Fourth_Coalition"}]}, {"year": "1810", "text": "Napoleon annexes the Kingdom of Holland as part of the First French Empire.", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> annexes the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Holland\" title=\"Kingdom of Holland\">Kingdom of Holland</a> as part of the First French Empire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> annexes the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Holland\" title=\"Kingdom of Holland\">Kingdom of Holland</a> as part of the First French Empire.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Kingdom of Holland", "link": "https://wikipedia.org/wiki/Kingdom_of_Holland"}]}, {"year": "1811", "text": "Explorer <PERSON> posts a sign near what is now Sacajawea State Park in Washington state, claiming the Columbia District for the United Kingdom.", "html": "1811 - Explorer <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> posts a sign near what is now <a href=\"https://wikipedia.org/wiki/Sacajawea_State_Park\" title=\"Sacajawea State Park\">Sacajawea State Park</a> in <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington state</a>, claiming the <a href=\"https://wikipedia.org/wiki/Columbia_District\" title=\"Columbia District\">Columbia District</a> for the United Kingdom.", "no_year_html": "Explorer <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> posts a sign near what is now <a href=\"https://wikipedia.org/wiki/Sacajawea_State_Park\" title=\"Sacajawea State Park\">Sacajawea State Park</a> in <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington state</a>, claiming the <a href=\"https://wikipedia.org/wiki/Columbia_District\" title=\"Columbia District\">Columbia District</a> for the United Kingdom.", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_(explorer)"}, {"title": "Sacajawea State Park", "link": "https://wikipedia.org/wiki/Sacajawea_State_Park"}, {"title": "Washington (state)", "link": "https://wikipedia.org/wiki/Washington_(state)"}, {"title": "Columbia District", "link": "https://wikipedia.org/wiki/Columbia_District"}]}, {"year": "1815", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> becomes the first Prime Minister of France.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-P%C3%A9rigord\" title=\"<PERSON>-Périgord\"><PERSON>-<PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-P%C3%A9rigord\" title=\"<PERSON>-<PERSON>érigord\"><PERSON>-<PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>-P%C3%A9<PERSON><PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1816", "text": "Argentina declares independence from Spain.", "html": "1816 - <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a> <a href=\"https://wikipedia.org/wiki/Argentine_Declaration_of_Independence\" title=\"Argentine Declaration of Independence\">declares independence</a> from Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a> <a href=\"https://wikipedia.org/wiki/Argentine_Declaration_of_Independence\" title=\"Argentine Declaration of Independence\">declares independence</a> from Spain.", "links": [{"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "Argentine Declaration of Independence", "link": "https://wikipedia.org/wiki/Argentine_Declaration_of_Independence"}]}, {"year": "1821", "text": "Four hundred and seventy prominent Cypriots including Archbishop <PERSON><PERSON><PERSON><PERSON><PERSON> are executed in response to Cypriot aid to the Greek War of Independence.", "html": "1821 - Four hundred and seventy prominent <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cypriots</a> including Archbishop <a href=\"https://wikipedia.org/wiki/Kyprianos\" class=\"mw-redirect\" title=\"<PERSON>yprian<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> are executed in response to Cypriot aid to the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>.", "no_year_html": "Four hundred and seventy prominent <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cypriots</a> including Archbishop <a href=\"https://wikipedia.org/wiki/Kyprianos\" class=\"mw-redirect\" title=\"<PERSON>yprian<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> are executed in response to Cypriot aid to the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>.", "links": [{"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kyprianos"}, {"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}]}, {"year": "1850", "text": "U.S. President <PERSON> dies after eating raw fruit and iced milk; he is succeeded in office by Vice President <PERSON><PERSON>.", "html": "1850 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies after eating raw fruit and iced milk; he is succeeded in office by Vice President <a href=\"https://wikipedia.org/wiki/Millard_Fillmore\" title=\"Millard Fillmore\"><PERSON><PERSON></a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies after eating raw fruit and iced milk; he is succeeded in office by Vice President <a href=\"https://wikipedia.org/wiki/Millard_Fillmore\" title=\"Millard Fillmore\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Millard_Fillmore"}]}, {"year": "1850", "text": "Persian prophet <PERSON><PERSON><PERSON> is executed in Tabriz, Persia.", "html": "1850 - Persian prophet <a href=\"https://wikipedia.org/wiki/B%C3%A1b\" title=\"Báb\"><PERSON><PERSON><PERSON></a> is executed in <a href=\"https://wikipedia.org/wiki/Tabriz\" title=\"Tabriz\">Tabriz</a>, <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Persia</a>.", "no_year_html": "Persian prophet <a href=\"https://wikipedia.org/wiki/B%C3%A1b\" title=\"Báb\"><PERSON><PERSON><PERSON></a> is executed in <a href=\"https://wikipedia.org/wiki/Tabriz\" title=\"Tabriz\">Tabriz</a>, <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Persia</a>.", "links": [{"title": "B<PERSON>b", "link": "https://wikipedia.org/wiki/B%C3%A1b"}, {"title": "Tabriz", "link": "https://wikipedia.org/wiki/Tabriz"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "1863", "text": "American Civil War: The Siege of Port Hudson ends in a Union victory and, along with the fall of Vicksburg five days earlier, gives the Union complete control of the Mississippi River.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Port_Hudson\" title=\"Siege of Port Hudson\">Siege of Port Hudson</a> ends in a <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> victory and, along with the <a href=\"https://wikipedia.org/wiki/Siege_of_Vicksburg\" title=\"Siege of Vicksburg\">fall of Vicksburg</a> five days earlier, gives the Union complete control of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Port_Hudson\" title=\"Siege of Port Hudson\">Siege of Port Hudson</a> ends in a <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> victory and, along with the <a href=\"https://wikipedia.org/wiki/Siege_of_Vicksburg\" title=\"Siege of Vicksburg\">fall of Vicksburg</a> five days earlier, gives the Union complete control of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Siege of Port Hudson", "link": "https://wikipedia.org/wiki/Siege_of_Port_Hudson"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Siege of Vicksburg", "link": "https://wikipedia.org/wiki/Siege_of_Vicksburg"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}]}, {"year": "1868", "text": "The 14th Amendment to the United States Constitution is ratified, guaranteeing African Americans full citizenship and all persons in the United States due process of law.", "html": "1868 - The <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">14th Amendment to the United States Constitution</a> is ratified, guaranteeing <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African Americans</a> full citizenship and all persons in the United States <a href=\"https://wikipedia.org/wiki/Due_process\" title=\"Due process\">due process</a> of law.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">14th Amendment to the United States Constitution</a> is ratified, guaranteeing <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African Americans</a> full citizenship and all persons in the United States <a href=\"https://wikipedia.org/wiki/Due_process\" title=\"Due process\">due process</a> of law.", "links": [{"title": "Fourteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution"}, {"title": "African Americans", "link": "https://wikipedia.org/wiki/African_Americans"}, {"title": "Due process", "link": "https://wikipedia.org/wiki/Due_process"}]}, {"year": "1875", "text": "The Herzegovina Uprising against Ottoman rule begins, which would last until 1878 and have far-reaching implications throughout the Balkans.", "html": "1875 - The <a href=\"https://wikipedia.org/wiki/Herzegovina_Uprising_(1875%E2%80%9377)\" class=\"mw-redirect\" title=\"Herzegovina Uprising (1875-77)\">Herzegovina Uprising</a> against <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> rule begins, which would last until 1878 and have far-reaching implications throughout the Balkans.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Herzegovina_Uprising_(1875%E2%80%9377)\" class=\"mw-redirect\" title=\"Herzegovina Uprising (1875-77)\">Herzegovina Uprising</a> against <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> rule begins, which would last until 1878 and have far-reaching implications throughout the Balkans.", "links": [{"title": "Herzegovina Uprising (1875-77)", "link": "https://wikipedia.org/wiki/Herzegovina_Uprising_(1875%E2%80%9377)"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1877", "text": "The inaugural Wimbledon Championships begins.", "html": "1877 - The <a href=\"https://wikipedia.org/wiki/1877_Wimbledon_Championship\" title=\"1877 Wimbledon Championship\">inaugural</a> <a href=\"https://wikipedia.org/wiki/The_Championships,_Wimbledon\" class=\"mw-redirect\" title=\"The Championships, Wimbledon\">Wimbledon Championships</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1877_Wimbledon_Championship\" title=\"1877 Wimbledon Championship\">inaugural</a> <a href=\"https://wikipedia.org/wiki/The_Championships,_Wimbledon\" class=\"mw-redirect\" title=\"The Championships, Wimbledon\">Wimbledon Championships</a> begins.", "links": [{"title": "1877 Wimbledon Championship", "link": "https://wikipedia.org/wiki/1877_Wimbledon_Championship"}, {"title": "The Championships, Wimbledon", "link": "https://wikipedia.org/wiki/The_Championships,_Wimbledon"}]}, {"year": "1893", "text": "<PERSON>, American heart surgeon, performs the first successful open-heart surgery in United States without anesthesia.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American heart surgeon, performs the first successful open-heart surgery in United States without anesthesia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American heart surgeon, performs the first successful open-heart surgery in United States without anesthesia.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON> delivers his Cross of Gold speech advocating bimetallism at the 1896 Democratic National Convention in Chicago.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his <a href=\"https://wikipedia.org/wiki/Cross_of_Gold_speech\" title=\"Cross of Gold speech\">Cross of Gold speech</a> advocating <a href=\"https://wikipedia.org/wiki/Bimetallism\" title=\"Bimetallism\">bimetallism</a> at the <a href=\"https://wikipedia.org/wiki/1896_Democratic_National_Convention\" title=\"1896 Democratic National Convention\">1896 Democratic National Convention</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his <a href=\"https://wikipedia.org/wiki/Cross_of_Gold_speech\" title=\"Cross of Gold speech\">Cross of Gold speech</a> advocating <a href=\"https://wikipedia.org/wiki/Bimetallism\" title=\"Bimetallism\">bimetallism</a> at the <a href=\"https://wikipedia.org/wiki/1896_Democratic_National_Convention\" title=\"1896 Democratic National Convention\">1896 Democratic National Convention</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cross of Gold speech", "link": "https://wikipedia.org/wiki/Cross_of_Gold_speech"}, {"title": "Bimetallism", "link": "https://wikipedia.org/wiki/Bimetallism"}, {"title": "1896 Democratic National Convention", "link": "https://wikipedia.org/wiki/1896_Democratic_National_Convention"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}]}, {"year": "1900", "text": "The Federation of Australia is given royal assent.", "html": "1900 - The <a href=\"https://wikipedia.org/wiki/Federation_of_Australia\" title=\"Federation of Australia\">Federation of Australia</a> is given <a href=\"https://wikipedia.org/wiki/Royal_assent\" title=\"Royal assent\">royal assent</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federation_of_Australia\" title=\"Federation of Australia\">Federation of Australia</a> is given <a href=\"https://wikipedia.org/wiki/Royal_assent\" title=\"Royal assent\">royal assent</a>.", "links": [{"title": "Federation of Australia", "link": "https://wikipedia.org/wiki/Federation_of_Australia"}, {"title": "Royal assent", "link": "https://wikipedia.org/wiki/Royal_assent"}]}, {"year": "1900", "text": "The Governor of Shanxi province in North China orders the execution of 45 foreign Christian missionaries and local church members, including children.", "html": "1900 - The Governor of <a href=\"https://wikipedia.org/wiki/Shanxi\" title=\"Shanxi\">Shanxi</a> province in <a href=\"https://wikipedia.org/wiki/North_China\" title=\"North China\">North China</a> orders the <a href=\"https://wikipedia.org/wiki/Taiyuan_massacre\" title=\"Taiyuan massacre\">execution</a> of 45 foreign Christian missionaries and local church members, including children.", "no_year_html": "The Governor of <a href=\"https://wikipedia.org/wiki/Shanxi\" title=\"Shanxi\">Shanxi</a> province in <a href=\"https://wikipedia.org/wiki/North_China\" title=\"North China\">North China</a> orders the <a href=\"https://wikipedia.org/wiki/Taiyuan_massacre\" title=\"Taiyuan massacre\">execution</a> of 45 foreign Christian missionaries and local church members, including children.", "links": [{"title": "Shanxi", "link": "https://wikipedia.org/wiki/Shanxi"}, {"title": "North China", "link": "https://wikipedia.org/wiki/North_China"}, {"title": "Taiyuan massacre", "link": "https://wikipedia.org/wiki/Taiyuan_massacre"}]}, {"year": "1918", "text": "In Nashville, Tennessee, an inbound local train collides with an outbound express, killing 101 and injuring 171 people, making it the deadliest rail accident in United States history.", "html": "1918 - In <a href=\"https://wikipedia.org/wiki/Nashville,_Tennessee\" title=\"Nashville, Tennessee\">Nashville, Tennessee</a>, an inbound local train <a href=\"https://wikipedia.org/wiki/Great_Train_Wreck_of_1918\" title=\"Great Train Wreck of 1918\">collides with an outbound express</a>, killing 101 and injuring 171 people, making it the deadliest rail accident in United States history.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Nashville,_Tennessee\" title=\"Nashville, Tennessee\">Nashville, Tennessee</a>, an inbound local train <a href=\"https://wikipedia.org/wiki/Great_Train_Wreck_of_1918\" title=\"Great Train Wreck of 1918\">collides with an outbound express</a>, killing 101 and injuring 171 people, making it the deadliest rail accident in United States history.", "links": [{"title": "Nashville, Tennessee", "link": "https://wikipedia.org/wiki/Nashville,_Tennessee"}, {"title": "Great Train Wreck of 1918", "link": "https://wikipedia.org/wiki/Great_Train_Wreck_of_1918"}]}, {"year": "1922", "text": "<PERSON> swims the 100 meters freestyle in 58.6 seconds breaking the world swimming record and the 'minute barrier'.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> swims the <a href=\"https://wikipedia.org/wiki/Freestyle_swimming\" title=\"Freestyle swimming\">100 meters freestyle</a> in 58.6 seconds breaking the world <a href=\"https://wikipedia.org/wiki/Swimming_(sport)\" title=\"Swimming (sport)\">swimming</a> record and the 'minute barrier'.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> swims the <a href=\"https://wikipedia.org/wiki/Freestyle_swimming\" title=\"Freestyle swimming\">100 meters freestyle</a> in 58.6 seconds breaking the world <a href=\"https://wikipedia.org/wiki/Swimming_(sport)\" title=\"Swimming (sport)\">swimming</a> record and the 'minute barrier'.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Freestyle swimming", "link": "https://wikipedia.org/wiki/Freestyle_swimming"}, {"title": "Swimming (sport)", "link": "https://wikipedia.org/wiki/Swimming_(sport)"}]}, {"year": "1926", "text": "<PERSON> accepts the post of commander-in-chief of the National Revolutionary Army, marking the beginning of the Northern Expedition to unite China under the rule of the Nationalist government.", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> accepts the post of commander-in-chief of the <a href=\"https://wikipedia.org/wiki/National_Revolutionary_Army\" title=\"National Revolutionary Army\">National Revolutionary Army</a>, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Northern_Expedition\" title=\"Northern Expedition\">Northern Expedition</a> to unite <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">China</a> under the rule of the <a href=\"https://wikipedia.org/wiki/Nationalist_government\" title=\"Nationalist government\">Nationalist government</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> accepts the post of commander-in-chief of the <a href=\"https://wikipedia.org/wiki/National_Revolutionary_Army\" title=\"National Revolutionary Army\">National Revolutionary Army</a>, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Northern_Expedition\" title=\"Northern Expedition\">Northern Expedition</a> to unite <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">China</a> under the rule of the <a href=\"https://wikipedia.org/wiki/Nationalist_government\" title=\"Nationalist government\">Nationalist government</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "National Revolutionary Army", "link": "https://wikipedia.org/wiki/National_Revolutionary_Army"}, {"title": "Northern Expedition", "link": "https://wikipedia.org/wiki/Northern_Expedition"}, {"title": "Republic of China (1912-1949)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)"}, {"title": "Nationalist government", "link": "https://wikipedia.org/wiki/Nationalist_government"}]}, {"year": "1932", "text": "The state of São Paulo revolts against the Brazilian Federal Government, starting the Constitutionalist Revolution.", "html": "1932 - The state of <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo_(state)\" title=\"São Paulo (state)\">São Paulo</a> revolts against the <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazilian</a> Federal Government, starting the <a href=\"https://wikipedia.org/wiki/Constitutionalist_Revolution\" title=\"Constitutionalist Revolution\">Constitutionalist Revolution</a>.", "no_year_html": "The state of <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo_(state)\" title=\"São Paulo (state)\">São Paulo</a> revolts against the <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazilian</a> Federal Government, starting the <a href=\"https://wikipedia.org/wiki/Constitutionalist_Revolution\" title=\"Constitutionalist Revolution\">Constitutionalist Revolution</a>.", "links": [{"title": "São Paulo (state)", "link": "https://wikipedia.org/wiki/S%C3%A3o_Paulo_(state)"}, {"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "Constitutionalist Revolution", "link": "https://wikipedia.org/wiki/Constitutionalist_Revolution"}]}, {"year": "1937", "text": "The silent film archives of Fox Film Corporation are destroyed by the 1937 Fox vault fire.", "html": "1937 - The silent film archives of <a href=\"https://wikipedia.org/wiki/Fox_Film_Corporation\" class=\"mw-redirect\" title=\"Fox Film Corporation\">Fox Film Corporation</a> are destroyed by the <a href=\"https://wikipedia.org/wiki/1937_Fox_vault_fire\" title=\"1937 Fox vault fire\">1937 Fox vault fire</a>.", "no_year_html": "The silent film archives of <a href=\"https://wikipedia.org/wiki/Fox_Film_Corporation\" class=\"mw-redirect\" title=\"Fox Film Corporation\">Fox Film Corporation</a> are destroyed by the <a href=\"https://wikipedia.org/wiki/1937_Fox_vault_fire\" title=\"1937 Fox vault fire\">1937 Fox vault fire</a>.", "links": [{"title": "Fox Film Corporation", "link": "https://wikipedia.org/wiki/Fox_Film_Corporation"}, {"title": "1937 Fox vault fire", "link": "https://wikipedia.org/wiki/1937_Fox_vault_fire"}]}, {"year": "1943", "text": "World War II: The Allied invasion of Sicily begins, leading to the downfall of <PERSON> and forcing <PERSON> to break off the Battle of Kursk.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Allied_invasion_of_Sicily\" title=\"Allied invasion of Sicily\">Allied invasion of Sicily</a> begins, leading to the <a href=\"https://wikipedia.org/wiki/25_Luglio\" class=\"mw-redirect\" title=\"25 Luglio\">downfall of Mussolini</a> and forcing <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> to break off the <a href=\"https://wikipedia.org/wiki/Battle_of_Kursk\" title=\"Battle of Kursk\">Battle of Kursk</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Allied_invasion_of_Sicily\" title=\"Allied invasion of Sicily\">Allied invasion of Sicily</a> begins, leading to the <a href=\"https://wikipedia.org/wiki/25_Luglio\" class=\"mw-redirect\" title=\"25 Luglio\">downfall of Mussolini</a> and forcing <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> to break off the <a href=\"https://wikipedia.org/wiki/Battle_of_Kursk\" title=\"Battle of Kursk\">Battle of Kursk</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Allied invasion of Sicily", "link": "https://wikipedia.org/wiki/Allied_invasion_of_Sicily"}, {"title": "25 Luglio", "link": "https://wikipedia.org/wiki/25_Lu<PERSON>o"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Kursk", "link": "https://wikipedia.org/wiki/Battle_of_Kursk"}]}, {"year": "1944", "text": "World War II: American forces take Saipan, bringing the Japanese archipelago within range of B-29 raids, and causing the downfall of the Tojo government.", "html": "1944 - World War II: American forces <a href=\"https://wikipedia.org/wiki/Battle_of_Saipan\" title=\"Battle of Saipan\">take Saipan</a>, bringing the <a href=\"https://wikipedia.org/wiki/Japanese_archipelago\" title=\"Japanese archipelago\">Japanese archipelago</a> within range of <a href=\"https://wikipedia.org/wiki/Boeing_B-29_Superfortress\" title=\"Boeing B-29 Superfortress\">B-29</a> raids, and causing the downfall of the <a href=\"https://wikipedia.org/wiki/Hideki_Tojo\" title=\"Hideki Tojo\">Tojo government</a>.", "no_year_html": "World War II: American forces <a href=\"https://wikipedia.org/wiki/Battle_of_Saipan\" title=\"Battle of Saipan\">take Saipan</a>, bringing the <a href=\"https://wikipedia.org/wiki/Japanese_archipelago\" title=\"Japanese archipelago\">Japanese archipelago</a> within range of <a href=\"https://wikipedia.org/wiki/Boeing_B-29_Superfortress\" title=\"Boeing B-29 Superfortress\">B-29</a> raids, and causing the downfall of the <a href=\"https://wikipedia.org/wiki/Hideki_Tojo\" title=\"Hideki Tojo\">Tojo government</a>.", "links": [{"title": "Battle of Saipan", "link": "https://wikipedia.org/wiki/Battle_of_Saipan"}, {"title": "Japanese archipelago", "link": "https://wikipedia.org/wiki/Japanese_archipelago"}, {"title": "Boeing B-29 Superfortress", "link": "https://wikipedia.org/wiki/Boeing_B-29_Superfortress"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1944", "text": "World War II: Continuation War: Finland wins the Battle of Tali-Ihantala, the largest battle ever fought in northern Europe. The Red Army withdraws its troops from Ihantala and digs into a defensive position, thus ending the Vyborg-Petrozavodsk Offensive.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Continuation_War\" title=\"Continuation War\">Continuation War</a>: Finland wins the <a href=\"https://wikipedia.org/wiki/Battle_of_Tali%E2%80%93Ihantala\" title=\"Battle of Tali-Ihantala\">Battle of Tali-Ihantala</a>, the largest battle ever fought in <a href=\"https://wikipedia.org/wiki/Northern_Europe\" title=\"Northern Europe\">northern Europe</a>. The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> withdraws its troops from Ihantala and digs into a defensive position, thus ending the <a href=\"https://wikipedia.org/wiki/Vyborg%E2%80%93Petrozavodsk_Offensive\" class=\"mw-redirect\" title=\"Vyborg-Petrozavodsk Offensive\">Vyborg-Petrozavodsk Offensive</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Continuation_War\" title=\"Continuation War\">Continuation War</a>: Finland wins the <a href=\"https://wikipedia.org/wiki/Battle_of_Tali%E2%80%93Ihantala\" title=\"Battle of Tali-Ihantala\">Battle of Tali-Ihantala</a>, the largest battle ever fought in <a href=\"https://wikipedia.org/wiki/Northern_Europe\" title=\"Northern Europe\">northern Europe</a>. The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> withdraws its troops from Ihantala and digs into a defensive position, thus ending the <a href=\"https://wikipedia.org/wiki/Vyborg%E2%80%93Petrozavodsk_Offensive\" class=\"mw-redirect\" title=\"Vyborg-Petrozavodsk Offensive\">Vyborg-Petrozavodsk Offensive</a>.", "links": [{"title": "Continuation War", "link": "https://wikipedia.org/wiki/Continuation_War"}, {"title": "Battle of Tali-Ihantala", "link": "https://wikipedia.org/wiki/Battle_of_Tali%E2%80%93Ihantala"}, {"title": "Northern Europe", "link": "https://wikipedia.org/wiki/Northern_Europe"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Vyborg-Petrozavodsk Offensive", "link": "https://wikipedia.org/wiki/Vyborg%E2%80%93Petrozavodsk_Offensive"}]}, {"year": "1955", "text": "The Russell-<PERSON> calls for a reduction of the risk of nuclear warfare.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Russell%E2%80%93<PERSON><PERSON><PERSON>_Manifesto\" title=\"Russell-Einstein Manifesto\">Russell<PERSON><PERSON></a> calls for a reduction of the risk of <a href=\"https://wikipedia.org/wiki/Nuclear_warfare\" title=\"Nuclear warfare\">nuclear warfare</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Russell%E2%80%93<PERSON><PERSON><PERSON>_Manifesto\" title=\"Russell-Einstein Manifesto\">Russell<PERSON><PERSON></a> calls for a reduction of the risk of <a href=\"https://wikipedia.org/wiki/Nuclear_warfare\" title=\"Nuclear warfare\">nuclear warfare</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Russell%E2%80%93<PERSON><PERSON><PERSON>_<PERSON>ifesto"}, {"title": "Nuclear warfare", "link": "https://wikipedia.org/wiki/Nuclear_warfare"}]}, {"year": "1956", "text": "The 7.7 Mw  Amorgos earthquake shakes the Cyclades island group in the Aegean Sea with a maximum Mercalli intensity of IX (Violent). The shaking and the destructive tsunami that followed left fifty-three people dead. A damaging M7.2 aftershock occurred minutes after the mainshock.", "html": "1956 - The 7.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1956_Amorgos_earthquake\" title=\"1956 Amorgos earthquake\">Amorgos earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Cyclades\" title=\"Cyclades\">Cyclades</a> island group in the <a href=\"https://wikipedia.org/wiki/Aegean_Sea\" title=\"Aegean Sea\">Aegean Sea</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>). The shaking and the destructive tsunami that followed left fifty-three people dead. A damaging M7.2 aftershock occurred minutes after the mainshock.", "no_year_html": "The 7.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1956_Amorgos_earthquake\" title=\"1956 Amorgos earthquake\">Amorgos earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Cyclades\" title=\"Cyclades\">Cyclades</a> island group in the <a href=\"https://wikipedia.org/wiki/Aegean_Sea\" title=\"Aegean Sea\">Aegean Sea</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>). The shaking and the destructive tsunami that followed left fifty-three people dead. A damaging M7.2 aftershock occurred minutes after the mainshock.", "links": [{"title": "1956 Amorgos earthquake", "link": "https://wikipedia.org/wiki/1956_Amorgos_earthquake"}, {"title": "Cyclades", "link": "https://wikipedia.org/wiki/Cyclades"}, {"title": "Aegean Sea", "link": "https://wikipedia.org/wiki/Aegean_Sea"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1958", "text": "A 7.8 Mw  strike-slip earthquake in Alaska causes a landslide that produces a megatsunami. The runup from the waves reached 525 m (1,722 ft) on the rim of Lituya Bay; five people were killed.", "html": "1958 - A 7.8 M<sub>w</sub>  strike-slip <a href=\"https://wikipedia.org/wiki/1958_Lituya_Bay,_Alaska_earthquake_and_megatsunami\" class=\"mw-redirect\" title=\"1958 Lituya Bay, Alaska earthquake and megatsunami\">earthquake in Alaska</a> causes a <a href=\"https://wikipedia.org/wiki/Landslide\" title=\"Landslide\">landslide</a> that produces a <a href=\"https://wikipedia.org/wiki/Megatsunami\" title=\"Megatsunami\">megatsunami</a>. The runup from the waves reached 525 m (1,722 ft) on the rim of <a href=\"https://wikipedia.org/wiki/Lituya_Bay\" title=\"Lituya Bay\">Lituya Bay</a>; five people were killed.", "no_year_html": "A 7.8 M<sub>w</sub>  strike-slip <a href=\"https://wikipedia.org/wiki/1958_Lituya_Bay,_Alaska_earthquake_and_megatsunami\" class=\"mw-redirect\" title=\"1958 Lituya Bay, Alaska earthquake and megatsunami\">earthquake in Alaska</a> causes a <a href=\"https://wikipedia.org/wiki/Landslide\" title=\"Landslide\">landslide</a> that produces a <a href=\"https://wikipedia.org/wiki/Megatsunami\" title=\"Megatsunami\">megatsunami</a>. The runup from the waves reached 525 m (1,722 ft) on the rim of <a href=\"https://wikipedia.org/wiki/Lituya_Bay\" title=\"Lituya Bay\">Lituya Bay</a>; five people were killed.", "links": [{"title": "1958 Lituya Bay, Alaska earthquake and megatsunami", "link": "https://wikipedia.org/wiki/1958_Lituya_Bay,_Alaska_earthquake_and_megatsunami"}, {"title": "Landslide", "link": "https://wikipedia.org/wiki/Landslide"}, {"title": "Megatsunami", "link": "https://wikipedia.org/wiki/Megatsunami"}, {"title": "Lituya Bay", "link": "https://wikipedia.org/wiki/Lituya_Bay"}]}, {"year": "1961", "text": "Greece becomes the first member state to join the European Economic Community by signing the Athens Agreement, which was suspended in 1967 during the Greek junta.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> becomes the first member state to join the <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a> by signing the Athens Agreement, which was suspended in 1967 during the <a href=\"https://wikipedia.org/wiki/Greek_junta\" title=\"Greek junta\">Greek junta</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> becomes the first member state to join the <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a> by signing the Athens Agreement, which was suspended in 1967 during the <a href=\"https://wikipedia.org/wiki/Greek_junta\" title=\"Greek junta\">Greek junta</a>.", "links": [{"title": "Greece", "link": "https://wikipedia.org/wiki/Greece"}, {"title": "European Economic Community", "link": "https://wikipedia.org/wiki/European_Economic_Community"}, {"title": "Greek junta", "link": "https://wikipedia.org/wiki/Greek_junta"}]}, {"year": "1962", "text": "Starfish Prime tests the effects of a nuclear test at orbital altitudes.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Starfish_Prime\" title=\"Starfish Prime\">Starfish Prime</a> tests the effects of a <a href=\"https://wikipedia.org/wiki/High-altitude_nuclear_explosion\" title=\"High-altitude nuclear explosion\">nuclear test at orbital altitudes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Starfish_Prime\" title=\"Starfish Prime\">Starfish Prime</a> tests the effects of a <a href=\"https://wikipedia.org/wiki/High-altitude_nuclear_explosion\" title=\"High-altitude nuclear explosion\">nuclear test at orbital altitudes</a>.", "links": [{"title": "Starfish Prime", "link": "https://wikipedia.org/wiki/Starfish_Prime"}, {"title": "High-altitude nuclear explosion", "link": "https://wikipedia.org/wiki/High-altitude_nuclear_explosion"}]}, {"year": "1977", "text": "The Pinochet dictatorship in Chile organises the youth event of Acto de Chacarillas, a ritualised act reminiscent of Francoist Spain.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/Military_dictatorship_of_Chile_(1973%E2%80%931990)\" class=\"mw-redirect\" title=\"Military dictatorship of Chile (1973-1990)\">Pinochet dictatorship</a> in Chile organises the youth event of <a href=\"https://wikipedia.org/wiki/Acto_de_Chacarillas\" title=\"Acto de Chacarillas\">Acto de Chacarillas</a>, a ritualised act reminiscent of <a href=\"https://wikipedia.org/wiki/Francoist_Spain\" title=\"Francoist Spain\">Francoist Spain</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Military_dictatorship_of_Chile_(1973%E2%80%931990)\" class=\"mw-redirect\" title=\"Military dictatorship of Chile (1973-1990)\">Pinochet dictatorship</a> in Chile organises the youth event of <a href=\"https://wikipedia.org/wiki/Acto_de_Chacarillas\" title=\"Acto de Chacarillas\">Acto de Chacarillas</a>, a ritualised act reminiscent of <a href=\"https://wikipedia.org/wiki/Francoist_Spain\" title=\"Francoist Spain\">Francoist Spain</a>.", "links": [{"title": "Military dictatorship of Chile (1973-1990)", "link": "https://wikipedia.org/wiki/Military_dictatorship_of_Chile_(1973%E2%80%931990)"}, {"title": "Acto de Chacarillas", "link": "https://wikipedia.org/wiki/Act<PERSON>_de_Chacarillas"}, {"title": "Francoist Spain", "link": "https://wikipedia.org/wiki/Francoist_Spain"}]}, {"year": "1979", "text": "A car bomb destroys a Renault motor car owned by \"Nazi hunters\" <PERSON> and <PERSON><PERSON> outside their home in France in an unsuccessful assassination attempt.", "html": "1979 - A car bomb destroys a <a href=\"https://wikipedia.org/wiki/Renault\" title=\"Renault\">Renault</a> motor car owned by \"<a href=\"https://wikipedia.org/wiki/Nazi_hunter\" title=\"Nazi hunter\">Nazi hunters</a>\" <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> outside their home in France in an unsuccessful assassination attempt.", "no_year_html": "A car bomb destroys a <a href=\"https://wikipedia.org/wiki/Renault\" title=\"Renault\">Renault</a> motor car owned by \"<a href=\"https://wikipedia.org/wiki/Nazi_hunter\" title=\"Nazi hunter\">Nazi hunters</a>\" <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> outside their home in France in an unsuccessful assassination attempt.", "links": [{"title": "Renault", "link": "https://wikipedia.org/wiki/Renault"}, {"title": "Nazi hunter", "link": "https://wikipedia.org/wiki/Nazi_hunter"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "Pan Am Flight 759 crashes in Kenner, Louisiana, killing all 145 people on board and eight others on the ground.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_759\" title=\"Pan Am Flight 759\">Pan Am Flight 759</a> crashes in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Louisiana\" title=\"Kenner, Louisiana\">Kenner, Louisiana</a>, killing all 145 people on board and eight others on the ground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_759\" title=\"Pan Am Flight 759\">Pan Am Flight 759</a> crashes in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Louisiana\" title=\"Kenner, Louisiana\">Kenner, Louisiana</a>, killing all 145 people on board and eight others on the ground.", "links": [{"title": "Pan Am Flight 759", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_759"}, {"title": "<PERSON>ner, Louisiana", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Louisiana"}]}, {"year": "1986", "text": "The New Zealand Parliament passes the Homosexual Law Reform Act legalising homosexuality in New Zealand.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/New_Zealand_Parliament\" title=\"New Zealand Parliament\">New Zealand Parliament</a> passes the <a href=\"https://wikipedia.org/wiki/Homosexual_Law_Reform_Act_1986\" title=\"Homosexual Law Reform Act 1986\">Homosexual Law Reform Act</a> <a href=\"https://wikipedia.org/wiki/LGBT_rights_in_New_Zealand\" class=\"mw-redirect\" title=\"LGBT rights in New Zealand\">legalising homosexuality in New Zealand</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_Zealand_Parliament\" title=\"New Zealand Parliament\">New Zealand Parliament</a> passes the <a href=\"https://wikipedia.org/wiki/Homosexual_Law_Reform_Act_1986\" title=\"Homosexual Law Reform Act 1986\">Homosexual Law Reform Act</a> <a href=\"https://wikipedia.org/wiki/LGBT_rights_in_New_Zealand\" class=\"mw-redirect\" title=\"LGBT rights in New Zealand\">legalising homosexuality in New Zealand</a>.", "links": [{"title": "New Zealand Parliament", "link": "https://wikipedia.org/wiki/New_Zealand_Parliament"}, {"title": "Homosexual Law Reform Act 1986", "link": "https://wikipedia.org/wiki/Homosexual_Law_Reform_Act_1986"}, {"title": "LGBT rights in New Zealand", "link": "https://wikipedia.org/wiki/LGBT_rights_in_New_Zealand"}]}, {"year": "1993", "text": "The Parliament of Canada passes the Nunavut Act leading to the 1999 creation of Nunavut, dividing the Northwest Territories into arctic (Inuit) and sub-arctic (Dene) lands based on a plebiscite.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_Canada\" title=\"Parliament of Canada\">Parliament of Canada</a> passes the Nunavut Act leading to the 1999 creation of <a href=\"https://wikipedia.org/wiki/Nunavut\" title=\"Nunavut\">Nunavut</a>, dividing the <a href=\"https://wikipedia.org/wiki/Northwest_Territories\" title=\"Northwest Territories\">Northwest Territories</a> into arctic (<a href=\"https://wikipedia.org/wiki/Inuit\" title=\"Inuit\">Inuit</a>) and sub-arctic (<a href=\"https://wikipedia.org/wiki/Dene\" title=\"Dene\">Dene</a>) lands based on a <a href=\"https://wikipedia.org/wiki/1982_Northwest_Territories_division_plebiscite\" title=\"1982 Northwest Territories division plebiscite\">plebiscite</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_Canada\" title=\"Parliament of Canada\">Parliament of Canada</a> passes the Nunavut Act leading to the 1999 creation of <a href=\"https://wikipedia.org/wiki/Nunavut\" title=\"Nunavut\">Nunavut</a>, dividing the <a href=\"https://wikipedia.org/wiki/Northwest_Territories\" title=\"Northwest Territories\">Northwest Territories</a> into arctic (<a href=\"https://wikipedia.org/wiki/Inuit\" title=\"Inuit\">Inuit</a>) and sub-arctic (<a href=\"https://wikipedia.org/wiki/Dene\" title=\"Dene\">Dene</a>) lands based on a <a href=\"https://wikipedia.org/wiki/1982_Northwest_Territories_division_plebiscite\" title=\"1982 Northwest Territories division plebiscite\">plebiscite</a>.", "links": [{"title": "Parliament of Canada", "link": "https://wikipedia.org/wiki/Parliament_of_Canada"}, {"title": "Nunavut", "link": "https://wikipedia.org/wiki/Nunavut"}, {"title": "Northwest Territories", "link": "https://wikipedia.org/wiki/Northwest_Territories"}, {"title": "Inuit", "link": "https://wikipedia.org/wiki/Inuit"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dene"}, {"title": "1982 Northwest Territories division plebiscite", "link": "https://wikipedia.org/wiki/1982_Northwest_Territories_division_plebiscite"}]}, {"year": "1995", "text": "The Navaly church bombing is carried out by the Sri Lanka Air Force killing 125 Tamil civilian refugees.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/Navaly_church_bombing\" title=\"Navaly church bombing\">Navaly church bombing</a> is carried out by the <a href=\"https://wikipedia.org/wiki/Sri_Lanka_Air_Force\" title=\"Sri Lanka Air Force\">Sri Lanka Air Force</a> killing 125 <a href=\"https://wikipedia.org/wiki/Tamil_people\" class=\"mw-redirect\" title=\"Tamil people\">Tamil</a> civilian refugees.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Navaly_church_bombing\" title=\"Navaly church bombing\">Navaly church bombing</a> is carried out by the <a href=\"https://wikipedia.org/wiki/Sri_Lanka_Air_Force\" title=\"Sri Lanka Air Force\">Sri Lanka Air Force</a> killing 125 <a href=\"https://wikipedia.org/wiki/Tamil_people\" class=\"mw-redirect\" title=\"Tamil people\">Tamil</a> civilian refugees.", "links": [{"title": "Navaly church bombing", "link": "https://wikipedia.org/wiki/Navaly_church_bombing"}, {"title": "Sri Lanka Air Force", "link": "https://wikipedia.org/wiki/Sri_Lanka_Air_Force"}, {"title": "Tamil people", "link": "https://wikipedia.org/wiki/Tamil_people"}]}, {"year": "1997", "text": "A Fokker 100 from the Brazilian airline TAM launches engineer <PERSON> into 2,400 meters of free fall after an explosion that depressurized the aircraft.", "html": "1997 - A <a href=\"https://wikipedia.org/wiki/Fokker_100\" title=\"Fokker 100\">Fokker 100</a> from the Brazilian airline <a href=\"https://wikipedia.org/wiki/LATAM_Brasil\" class=\"mw-redirect\" title=\"LATAM Brasil\">TAM</a> launches engineer <a href=\"https://wikipedia.org/wiki/LATAM_Brasil#Incidents_and_accidents\" class=\"mw-redirect\" title=\"LATAM Brasil\"><PERSON></a> into 2,400 meters of free fall after an explosion that depressurized the aircraft.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Fokker_100\" title=\"Fokker 100\">Fokker 100</a> from the Brazilian airline <a href=\"https://wikipedia.org/wiki/LATAM_Brasil\" class=\"mw-redirect\" title=\"LATAM Brasil\">TAM</a> launches engineer <a href=\"https://wikipedia.org/wiki/LATAM_Brasil#Incidents_and_accidents\" class=\"mw-redirect\" title=\"LATAM Brasil\"><PERSON>s</a> into 2,400 meters of free fall after an explosion that depressurized the aircraft.", "links": [{"title": "Fokker 100", "link": "https://wikipedia.org/wiki/Fokker_100"}, {"title": "LATAM Brasil", "link": "https://wikipedia.org/wiki/LATAM_Brasil"}, {"title": "LATAM Brasil", "link": "https://wikipedia.org/wiki/LATAM_Brasil#Incidents_and_accidents"}]}, {"year": "1999", "text": "Days of student protests begin after Iranian police and hardliners attack a student dormitory at the University of Tehran.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Iran_student_protests,_July_1999\" class=\"mw-redirect\" title=\"Iran student protests, July 1999\">Days of student protests</a> begin after <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> police and hardliners attack a student dormitory at the <a href=\"https://wikipedia.org/wiki/University_of_Tehran\" title=\"University of Tehran\">University of Tehran</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran_student_protests,_July_1999\" class=\"mw-redirect\" title=\"Iran student protests, July 1999\">Days of student protests</a> begin after <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> police and hardliners attack a student dormitory at the <a href=\"https://wikipedia.org/wiki/University_of_Tehran\" title=\"University of Tehran\">University of Tehran</a>.", "links": [{"title": "Iran student protests, July 1999", "link": "https://wikipedia.org/wiki/Iran_student_protests,_July_1999"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "University of Tehran", "link": "https://wikipedia.org/wiki/University_of_Tehran"}]}, {"year": "2002", "text": "The African Union is established in Addis Ababa, Ethiopia, replacing the Organisation of African Unity (OAU). The organization's first chairman is <PERSON><PERSON><PERSON>, President of South Africa.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/African_Union\" title=\"African Union\">African Union</a> is established in Addis Ababa, <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>, replacing the <a href=\"https://wikipedia.org/wiki/Organisation_of_African_Unity\" title=\"Organisation of African Unity\">Organisation of African Unity</a> (OAU). The organization's first chairman is <a href=\"https://wikipedia.org/wiki/Thabo_Mbeki\" title=\"Thabo Mbeki\"><PERSON><PERSON><PERSON></a>, President of <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/African_Union\" title=\"African Union\">African Union</a> is established in Addis Ababa, <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>, replacing the <a href=\"https://wikipedia.org/wiki/Organisation_of_African_Unity\" title=\"Organisation of African Unity\">Organisation of African Unity</a> (OAU). The organization's first chairman is <a href=\"https://wikipedia.org/wiki/Thabo_Mbeki\" title=\"Thabo Mbeki\"><PERSON><PERSON><PERSON></a>, President of <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a>.", "links": [{"title": "African Union", "link": "https://wikipedia.org/wiki/African_Union"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "Organisation of African Unity", "link": "https://wikipedia.org/wiki/Organisation_of_African_Unity"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thab<PERSON>_<PERSON><PERSON>i"}, {"title": "South Africa", "link": "https://wikipedia.org/wiki/South_Africa"}]}, {"year": "2004", "text": "The Senate Report on Iraqi WMD Intelligence is released by the United States Senate Select Committee on Intelligence, casting doubt on the rationale for the Iraq War.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Senate_Report_on_Iraqi_WMD_Intelligence\" title=\"Senate Report on Iraqi WMD Intelligence\">Senate Report on Iraqi WMD Intelligence</a> is released by the <a href=\"https://wikipedia.org/wiki/United_States_Senate_Select_Committee_on_Intelligence\" title=\"United States Senate Select Committee on Intelligence\">United States Senate Select Committee on Intelligence</a>, casting doubt on the <a href=\"https://wikipedia.org/wiki/Rationale_for_the_Iraq_War\" title=\"Rationale for the Iraq War\">rationale for the Iraq War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Senate_Report_on_Iraqi_WMD_Intelligence\" title=\"Senate Report on Iraqi WMD Intelligence\">Senate Report on Iraqi WMD Intelligence</a> is released by the <a href=\"https://wikipedia.org/wiki/United_States_Senate_Select_Committee_on_Intelligence\" title=\"United States Senate Select Committee on Intelligence\">United States Senate Select Committee on Intelligence</a>, casting doubt on the <a href=\"https://wikipedia.org/wiki/Rationale_for_the_Iraq_War\" title=\"Rationale for the Iraq War\">rationale for the Iraq War</a>.", "links": [{"title": "Senate Report on Iraqi WMD Intelligence", "link": "https://wikipedia.org/wiki/Senate_Report_on_Iraqi_WMD_Intelligence"}, {"title": "United States Senate Select Committee on Intelligence", "link": "https://wikipedia.org/wiki/United_States_Senate_Select_Committee_on_Intelligence"}, {"title": "Rationale for the Iraq War", "link": "https://wikipedia.org/wiki/Rationale_for_the_Iraq_War"}]}, {"year": "2006", "text": "One hundred and twenty-five people are killed when S7 Airlines Flight 778, an Airbus A310 passenger jet, veers off the runway while landing in wet conditions at Irkutsk Airport in Siberia.", "html": "2006 - One hundred and twenty-five people are killed when <a href=\"https://wikipedia.org/wiki/S7_Airlines_Flight_778\" title=\"S7 Airlines Flight 778\">S7 Airlines Flight 778</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A310\" title=\"Airbus A310\">Airbus A310</a> passenger jet, veers off the runway while landing in wet conditions at <a href=\"https://wikipedia.org/wiki/International_Airport_Irkutsk\" title=\"International Airport Irkutsk\">Irkutsk Airport</a> in <a href=\"https://wikipedia.org/wiki/Siberia\" title=\"Siberia\">Siberia</a>.", "no_year_html": "One hundred and twenty-five people are killed when <a href=\"https://wikipedia.org/wiki/S7_Airlines_Flight_778\" title=\"S7 Airlines Flight 778\">S7 Airlines Flight 778</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A310\" title=\"Airbus A310\">Airbus A310</a> passenger jet, veers off the runway while landing in wet conditions at <a href=\"https://wikipedia.org/wiki/International_Airport_Irkutsk\" title=\"International Airport Irkutsk\">Irkutsk Airport</a> in <a href=\"https://wikipedia.org/wiki/Siberia\" title=\"Siberia\">Siberia</a>.", "links": [{"title": "S7 Airlines Flight 778", "link": "https://wikipedia.org/wiki/S7_Airlines_Flight_778"}, {"title": "Airbus A310", "link": "https://wikipedia.org/wiki/Airbus_A310"}, {"title": "International Airport Irkutsk", "link": "https://wikipedia.org/wiki/International_Airport_Irkutsk"}, {"title": "Siberia", "link": "https://wikipedia.org/wiki/Siberia"}]}, {"year": "2011", "text": "South Sudan gains independence and secedes from Sudan.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/South_Sudan\" title=\"South Sudan\">South Sudan</a> gains independence and secedes from <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_Sudan\" title=\"South Sudan\">South Sudan</a> gains independence and secedes from <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a>.", "links": [{"title": "South Sudan", "link": "https://wikipedia.org/wiki/South_Sudan"}, {"title": "Sudan", "link": "https://wikipedia.org/wiki/Sudan"}]}, {"year": "2011", "text": "A rally takes place in Kuala Lumpur, Malaysia to call for fairer elections in the country.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Bersih_2.0_rally\" title=\"Bersih 2.0 rally\">A rally takes place</a> in Kuala Lumpur, Malaysia to call for fairer elections in the country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bersih_2.0_rally\" title=\"Bersih 2.0 rally\">A rally takes place</a> in Kuala Lumpur, Malaysia to call for fairer elections in the country.", "links": [{"title": "Bersih 2.0 rally", "link": "https://wikipedia.org/wiki/Bersih_2.0_rally"}]}], "Births": [{"year": "1249", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 1305)", "html": "1249 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1305)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1305)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>"}]}, {"year": "1455", "text": "<PERSON> of Baden, Dutch bishop (d. 1517)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Baden\" title=\"<PERSON> of Baden\"><PERSON> of Baden</a>, Dutch bishop (d. 1517)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Baden\" title=\"<PERSON> of Baden\"><PERSON> of Baden</a>, Dutch bishop (d. 1517)", "links": [{"title": "<PERSON> of Baden", "link": "https://wikipedia.org/wiki/Frederick_IV_of_Baden"}]}, {"year": "1511", "text": "<PERSON> of Saxe-Lauenburg, queen consort of Denmark and Norway (d. 1571)", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg\" title=\"Dorothea of Saxe-Lauenburg\"><PERSON> of Saxe-Lauenburg</a>, queen consort of Denmark and Norway (d. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg\" title=\"Dorothea of Saxe-Lauenburg\"><PERSON> of Saxe-Lauenburg</a>, queen consort of Denmark and Norway (d. 1571)", "links": [{"title": "Dorothea of Saxe-Lauenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxe-Lauenburg"}]}, {"year": "1526", "text": "<PERSON> of Austria, Polish noble (d. 1545)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Austria_(1526%E2%80%931545)\" title=\"<PERSON> of Austria (1526-1545)\"><PERSON> of Austria</a>, Polish noble (d. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_of_Austria_(1526%E2%80%931545)\" title=\"<PERSON> of Austria (1526-1545)\"><PERSON> of Austria</a>, Polish noble (d. 1545)", "links": [{"title": "<PERSON> of Austria (1526-1545)", "link": "https://wikipedia.org/wiki/Elizabeth_of_Austria_(1526%E2%80%931545)"}]}, {"year": "1577", "text": "<PERSON>, 3rd Baron <PERSON>, English-American soldier and politician, Colonial Governor of Virginia (d. 1618)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English-American soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Colonial Governor of Virginia</a> (d. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English-American soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Colonial Governor of Virginia</a> (d. 1618)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Virginia", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia"}]}, {"year": "1578", "text": "<PERSON>, Holy Roman Emperor (d. 1637)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1637)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1654", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 1732)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>igen\" title=\"Emperor <PERSON>ige<PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>n\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1732)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>igen"}]}, {"year": "1686", "text": "<PERSON>, American merchant and politician (d. 1749)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1686%E2%80%931749)\" title=\"<PERSON> (1686-1749)\"><PERSON></a>, American merchant and politician (d. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1686%E2%80%931749)\" title=\"<PERSON> (1686-1749)\"><PERSON></a>, American merchant and politician (d. 1749)", "links": [{"title": "<PERSON> (1686-1749)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1686%E2%80%931749)"}]}, {"year": "1689", "text": "<PERSON>, French epigrammatist and playwright (d. 1773)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French epigrammatist and playwright (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French epigrammatist and playwright (d. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, German poet and author (d. 1781)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6tz\" title=\"<PERSON>\"><PERSON></a>, German poet and author (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6tz\" title=\"<PERSON>\"><PERSON></a>, German poet and author (d. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6tz"}]}, {"year": "1753", "text": "<PERSON>, 1st Baron <PERSON>, English admiral and politician, 34th Lieutenant Governor of Newfoundland (d. 1825)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron Ra<PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English admiral and politician, 34th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Newfoundland\" class=\"mw-redirect\" title=\"Lieutenant Governor of Newfoundland\">Lieutenant Governor of Newfoundland</a> (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English admiral and politician, 34th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Newfoundland\" class=\"mw-redirect\" title=\"Lieutenant Governor of Newfoundland\">Lieutenant Governor of Newfoundland</a> (d. 1825)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Lieutenant Governor of Newfoundland", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Newfoundland"}]}, {"year": "1764", "text": "<PERSON>, English author and poet (d. 1823)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON>, English author and playwright (d. 1818)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and playwright (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and playwright (d. 1818)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1777", "text": "<PERSON><PERSON><PERSON>, Finnish farmer and lay preacher (d. 1852)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish farmer and lay preacher (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish farmer and lay preacher (d. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1800", "text": "<PERSON>, German physician, pathologist, and anatomist (d. 1885)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, pathologist, and anatomist (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, pathologist, and anatomist (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, American lawyer and colonel (d. 1887)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and colonel (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and colonel (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, American inventor, invented the sewing machine (d. 1867)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, invented the <a href=\"https://wikipedia.org/wiki/Sewing_machine\" title=\"Sewing machine\">sewing machine</a> (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, invented the <a href=\"https://wikipedia.org/wiki/Sewing_machine\" title=\"Sewing machine\">sewing machine</a> (d. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sewing machine", "link": "https://wikipedia.org/wiki/Sewing_machine"}]}, {"year": "1825", "text": "<PERSON><PERSON> <PERSON><PERSON>, American lawyer and politician, 2nd Governor of Oregon (d. 1886)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1886)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1828", "text": "<PERSON>, Italian cardinal (d. 1913)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Stefano\"><PERSON></a>, Italian cardinal (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Luigi <PERSON> Stefano\"><PERSON> Stefano</a>, Italian cardinal (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Santo_Stefano"}]}, {"year": "1834", "text": "<PERSON>, Czech journalist and poet (d. 1891)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech journalist and poet (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech journalist and poet (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON> of Renesse-Breidbach (d. 1904)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Renesse-Breidbach\" title=\"<PERSON> of Renesse-Breidbach\"><PERSON> of Renesse-Breidbach</a> (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Renesse-Breidbach\" title=\"<PERSON> of Renesse-Breidbach\"><PERSON> of Renesse-Breidbach</a> (d. 1904)", "links": [{"title": "Camille of Renesse-Breidbach", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rene<PERSON>-Breidbach"}]}, {"year": "1848", "text": "<PERSON>, Duke of Parma (d. 1907)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (d. 1907)", "links": [{"title": "<PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma"}]}, {"year": "1853", "text": "<PERSON>, American painter (d. 1929)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, English-Australian politician, 26th Premier of South Australia (d. 1932)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1858", "text": "<PERSON>, German-American anthropologist and linguist (d. 1942)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American anthropologist and linguist (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American anthropologist and linguist (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, French author and playwright (d. 1958)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Brazilian physician and parasitologist (d. 1934)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian physician and parasitologist (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian physician and parasitologist (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Italian composer and conductor (d. 1936)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and conductor (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and conductor (d. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1887", "text": "<PERSON>, American-Canadian painter and illustrator (d. 1975)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian painter and illustrator (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian painter and illustrator (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Mexican painter (d. 1918)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican painter (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican painter (d. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saturnino_Herr%C3%A1n"}]}, {"year": "1887", "text": "<PERSON>, American admiral and historian (d. 1976)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and historian (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and historian (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, American-Canadian ice hockey player, coach, and referee (d. 1964)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/L%C3%A9o_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Canadian ice hockey player, coach, and referee (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9o_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Canadian ice hockey player, coach, and referee (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9o_<PERSON><PERSON>"}]}, {"year": "1893", "text": "<PERSON>, English cricketer and coach (d. 1981)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, English author (d. 2000)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English soldier (d. 1993)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Canadian ice hockey player and referee (d. 1984)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and referee (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and referee (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American singer-songwriter (d. 1999)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 1999)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Pakistani philosopher and scholar (d. 1973)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Turabi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani philosopher and scholar (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Turabi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani philosopher and scholar (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1908", "text": "<PERSON>, American photographer, critic, and educator (d. 1976)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Minor_White\" title=\"Minor White\"><PERSON></a>, American photographer, critic, and educator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Minor_White\" title=\"Minor White\"><PERSON></a>, American photographer, critic, and educator (d. 1976)", "links": [{"title": "Minor White", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American author and illustrator (d. 1978)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Basil_Wolverton"}]}, {"year": "1910", "text": "<PERSON><PERSON>, South African anti-apartheid and ANC leader and activist (d. 2001)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African anti-apartheid and <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">ANC</a> leader and activist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African anti-apartheid and <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">ANC</a> leader and activist (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}, {"title": "African National Congress", "link": "https://wikipedia.org/wiki/African_National_Congress"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, English author and illustrator (d. 1968)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author and illustrator (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author and illustrator (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American physicist and author (d. 2008)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and author (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, German engineer and politician, 4th Prime Minister of East Germany (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German engineer and politician, 4th <a href=\"https://wikipedia.org/wiki/Leadership_of_East_Germany\" class=\"mw-redirect\" title=\"Leadership of East Germany\">Prime Minister of East Germany</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German engineer and politician, 4th <a href=\"https://wikipedia.org/wiki/Leadership_of_East_Germany\" class=\"mw-redirect\" title=\"Leadership of East Germany\">Prime Minister of East Germany</a> (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>h"}, {"title": "Leadership of East Germany", "link": "https://wikipedia.org/wiki/Leadership_of_East_Germany"}]}, {"year": "1914", "text": "<PERSON>, Australian rules footballer (d. 2017)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1914)\" title=\"<PERSON> (footballer, born 1914)\"><PERSON></a>, Australian rules footballer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1914)\" title=\"<PERSON> (footballer, born 1914)\"><PERSON></a>, Australian rules footballer (d. 2017)", "links": [{"title": "<PERSON> (footballer, born 1914)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1914)"}]}, {"year": "1915", "text": "<PERSON>, American composer and educator (d. 2005)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and educator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and educator (d. 2005)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)"}]}, {"year": "1915", "text": "<PERSON>, American sergeant and photographer (d. 2008)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and photographer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and photographer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, New Zealand composer (d. 1984)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand composer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand composer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English colonel and politician; Prime Minister of the United Kingdom, 1970-74 (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel and politician; Prime Minister of the United Kingdom, 1970-74 (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Edward <PERSON>\"><PERSON></a>, English colonel and politician; Prime Minister of the United Kingdom, 1970-74 (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish orphan, survivor of Holocaust (d. 2019)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Krystyna_Da%C5%84ko\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish orphan, survivor of Holocaust (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Krystyna_Da%C5%84ko\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish orphan, survivor of Holocaust (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Krystyna_Da%C5%84ko"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Dutch mathematician and academic (d. 2012)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and academic (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Finnish 12th General of The Salvation Army (d. 1999)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Jar<PERSON>_Wahlstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish 12th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jar<PERSON>_Wahlstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish 12th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jarl_Wahlstr%C3%B6m"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1921", "text": "<PERSON>, American general (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, British computer scientist and mathematician (d. 2022)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British computer scientist and mathematician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British computer scientist and mathematician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Spanish-Mexican actress (d. 1994)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Fern%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish-Mexican actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Fern%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish-Mexican actress (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Angelines_Fern%C3%A1ndez"}]}, {"year": "1922", "text": "<PERSON>, American basketball player and coach (d. 1993)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, French organist and composer (d. 1984)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Indian actor, director, and producer (d. 1964)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor, director, and producer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor, director, and producer (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American engineer, author, and academic (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>icks\" title=\"<PERSON>\"><PERSON></a>, American engineer, author, and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, author, and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wicks"}]}, {"year": "1925", "text": "<PERSON>, American ambassador (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ambassador (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ambassador (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American illustrator (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American-Danish physicist and academic, Nobel Prize laureate (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Danish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Danish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1926", "text": "<PERSON>, Argentine football defender and coach (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine football defender and coach (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine football defender and coach (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Italian-American medical researcher and health educator (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American medical researcher and health educator (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American medical researcher and health educator (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American singer and actor (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Canadian ice hockey player, coach, and politician (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and politician (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Spanish cyclist (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Federico_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor, singer, and director (d. 1996)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer-songwriter and producer (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer and mandolin player (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and mandolin player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and mandolin player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Chinese general", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ian\"><PERSON></a>, Chinese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ian"}]}, {"year": "1929", "text": "<PERSON> of Morocco (d. 1999)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Hassan_II_of_Morocco\" title=\"<PERSON> II of Morocco\"><PERSON> of Morocco</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Morocco\" title=\"<PERSON> II of Morocco\"><PERSON> of Morocco</a> (d. 1999)", "links": [{"title": "<PERSON> II of Morocco", "link": "https://wikipedia.org/wiki/Hassan_II_of_Morocco"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Indian actor, director, producer, and screenwriter (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, producer, and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, producer, and screenwriter (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American composer and conductor (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American computer scientist and graphic artist", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and graphic artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and graphic artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Norwegian actress (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian actress (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian actress (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American publicist", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publicist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, South African cricketer and rugby player (d. 2007)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and rugby player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and rugby player (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American journalist and author (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American judge (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American captain and politician, 13th United States Secretary of Defense (d. 2021)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Israeli sprinter and long jumper (d. 1972)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Amitz<PERSON>_<PERSON>\" title=\"Amitz<PERSON> S<PERSON>pira\"><PERSON><PERSON><PERSON></a>, Israeli sprinter and long jumper (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am<PERSON><PERSON>_<PERSON>\" title=\"Amitz<PERSON> S<PERSON>pira\"><PERSON><PERSON><PERSON></a>, Israeli sprinter and long jumper (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ra"}]}, {"year": "1933", "text": "<PERSON>, English-American neurologist, author, and academic (d. 2015)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American neurologist, author, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American neurologist, author, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American architect, designed the Portland Building and the Humana Building (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Portland_Building\" title=\"Portland Building\">Portland Building</a> and the <a href=\"https://wikipedia.org/wiki/Humana_Building\" title=\"Humana Building\">Humana Building</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Portland_Building\" title=\"Portland Building\">Portland Building</a> and the <a href=\"https://wikipedia.org/wiki/Humana_Building\" title=\"Humana Building\">Humana Building</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Portland Building", "link": "https://wikipedia.org/wiki/Portland_Building"}, {"title": "Humana Building", "link": "https://wikipedia.org/wiki/Humana_Building"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Dutch economist and politician, Dutch Minister of Finance (d. 2005)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Netherlands)\" title=\"Ministry of Finance (Netherlands)\">Dutch Minister of Finance</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Netherlands)\" title=\"Ministry of Finance (Netherlands)\">Dutch Minister of Finance</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON>"}, {"title": "Ministry of Finance (Netherlands)", "link": "https://wikipedia.org/wiki/Ministry_of_Finance_(Netherlands)"}]}, {"year": "1935", "text": "<PERSON>, Argentine singer and activist (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Mercedes_Sosa\" title=\"Mercedes Sosa\"><PERSON></a>, Argentine singer and activist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mercedes_Sosa\" title=\"Mercedes Sosa\"><PERSON></a>, Argentine singer and activist (d. 2009)", "links": [{"title": "Mercedes Sosa", "link": "https://wikipedia.org/wiki/Mercedes_Sosa"}]}, {"year": "1935", "text": "<PERSON>, English actor (d. 2001)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2001)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1936", "text": "<PERSON>, American poet and educator (d. 2002)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/June_Jordan\" title=\"June <PERSON>\">June <PERSON></a>, American poet and educator (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_Jordan\" title=\"June Jordan\">June Jordan</a>, American poet and educator (d. 2002)", "links": [{"title": "June Jordan", "link": "https://wikipedia.org/wiki/June_Jordan"}]}, {"year": "1936", "text": "<PERSON>, American violinist and conductor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English painter and photographer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actor (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Indian film actor (d. 1985)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actor (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American lawyer and politician, 12th Oregon Attorney General (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Oregon_Attorney_General\" title=\"Oregon Attorney General\">Oregon Attorney General</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Oregon_Attorney_General\" title=\"Oregon Attorney General\">Oregon Attorney General</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Oregon Attorney General", "link": "https://wikipedia.org/wiki/Oregon_Attorney_General"}]}, {"year": "1940", "text": "<PERSON>, American psychoanalyst and theorist (d. 2010)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychoanalyst and theorist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychoanalyst and theorist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English musician (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Baron <PERSON>, English engineer and politician (d. 2022)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English engineer and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English engineer and politician (d. 2022)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American colonel, pilot, and astronaut", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Indian-English historian and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American ice hockey player and coach (d. 2002)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actress and talk show host (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Tabassum\" title=\"Tabassu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress and talk show host (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tabassum\" title=\"Ta<PERSON>su<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress and talk show host (d. 2022)", "links": [{"title": "Tabassum", "link": "https://wikipedia.org/wiki/Tabassum"}]}, {"year": "1945", "text": "<PERSON>, American author and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON> Boy <PERSON>, American singer-songwriter and guitarist (d. 1993)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Root_Boy_Slim\" title=\"Root Boy Slim\">Root Boy Slim</a>, American singer-songwriter and guitarist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Root_Boy_Slim\" title=\"Root Boy Slim\">Root Boy Slim</a>, American singer-songwriter and guitarist (d. 1993)", "links": [{"title": "Root Boy Slim", "link": "https://wikipedia.org/wiki/Root_Boy_Slim"}]}, {"year": "1946", "text": "<PERSON>, Scottish-Australian singer-songwriter (d. 1980)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer-songwriter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Scott\"><PERSON></a>, Scottish-Australian singer-songwriter (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter, bass player, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1947", "text": "<PERSON>, English drummer (d. 2008)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player and actor (d. 2024)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player and actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player and actor (d. 2024)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English historian (d. 2004)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Indonesian lawyer and politician, 15th Indonesian Minister of Foreign Affairs", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Indonesia)\" title=\"Ministry of Foreign Affairs (Indonesia)\">Indonesian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Indonesia)\" title=\"Ministry of Foreign Affairs (Indonesia)\">Indonesian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Indonesia)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Indonesia)"}]}, {"year": "1949", "text": "<PERSON>, Haitian military officer and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9dras\" title=\"<PERSON>\"><PERSON></a>, Haitian military officer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9dras\" title=\"<PERSON>\"><PERSON></a>, Haitian military officer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Raoul_C%C3%A9dras"}]}, {"year": "1950", "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, Moroccan physician and neurosurgeon", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ibn_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON><PERSON>\"><PERSON><PERSON> ibn <PERSON><PERSON><PERSON></a>, Moroccan physician and neurosurgeon", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ibn_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON><PERSON>\"><PERSON><PERSON> ibn <PERSON><PERSON><PERSON></a>, Moroccan physician and neurosurgeon", "links": [{"title": "<PERSON><PERSON> ibn <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Italian tennis player and sailor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian tennis player and sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian tennis player and sailor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adrian<PERSON>_<PERSON>atta"}]}, {"year": "1950", "text": "<PERSON>, Ukrainian engineer and politician, 4th President of Ukraine", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian engineer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President of Ukraine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian engineer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President of Ukraine</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Ukraine", "link": "https://wikipedia.org/wiki/President_of_Ukraine"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Honduran singer-songwriter (d. 2024)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Mois%C3%A9s_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Honduran singer-songwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mois%C3%A9s_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Honduran singer-songwriter (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mois%C3%A9s_<PERSON>elo"}]}, {"year": "1951", "text": "<PERSON>, American actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Latvian politician, businessman, and former Prime Minister of Latvia", "html": "1951 - <a href=\"https://wikipedia.org/wiki/M%C4%81<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian politician, businessman, and former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C4%81<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian politician, businessman, and former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C4%81<PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of Latvia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Latvia"}]}, {"year": "1952", "text": "<PERSON>, American pianist, composer, and radio and television host", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Canadian dancer and choreographer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian dancer and choreographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Cameroonian footballer and politician (d. 2012)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9ophile_<PERSON>\" title=\"Théophile <PERSON>\">Th<PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9ophile_<PERSON>\" title=\"Théophile <PERSON>\">Thé<PERSON><PERSON><PERSON></a>, Cameroonian footballer and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9ophile_Abega"}]}, {"year": "1954", "text": "<PERSON>, Canadian journalist and businessman", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary"}]}, {"year": "1955", "text": "<PERSON>, English footballer and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1955", "text": "<PERSON>, American baseball player and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1956", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hank<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American author, poet, and playwright", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English singer-songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American screenwriter and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English comedian, actor, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Malaysian politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Malaysian football coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Malaysia_football_coach)\" class=\"mw-redirect\" title=\"<PERSON> (Malaysia football coach)\"><PERSON></a>, Malaysian football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Malaysia_football_coach)\" class=\"mw-redirect\" title=\"<PERSON> (Malaysia football coach)\"><PERSON></a>, Malaysian football coach", "links": [{"title": "<PERSON> (Malaysia football coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Malaysia_football_coach)"}]}, {"year": "1959", "text": "<PERSON>, Scottish singer-songwriter and keyboard player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American professional wrestler and actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English lawyer and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese actress and singer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian rugby league player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>-<PERSON>, Argentine journalist, photographer, and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine journalist, photographer, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine journalist, photographer, and author", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, German footballer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter, guitarist, and actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Love\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Love\" title=\"<PERSON> Love\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer and coach (d. 2023)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and coach (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and coach (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American bass player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German director and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American sculptor (d. 2006)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actress and voice artist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Chinese-American soprano and actress (d. 2013)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American soprano and actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American soprano and actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American television writer and producer (d. 2016)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television writer and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television writer and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American screenwriter and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Swedish politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Gunnar_Ax%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gunnar_Ax%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gunnar_Ax%C3%A9n"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Bulgarian footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American football player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Welsh lawn bowler", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawn_bowler)\" class=\"mw-redirect\" title=\"<PERSON> (lawn bowler)\"><PERSON></a>, Welsh lawn bowler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawn_bowler)\" class=\"mw-redirect\" title=\"<PERSON> (lawn bowler)\"><PERSON></a>, Welsh lawn bowler", "links": [{"title": "<PERSON> (lawn bowler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawn_bowler)"}]}, {"year": "1968", "text": "<PERSON>, Italian footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paolo_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Swedish singer-songwriter and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian footballer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American football player and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Trent_Green\" title=\"Trent Green\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trent_Green\" title=\"Trent Green\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "Trent Green", "link": "https://wikipedia.org/wiki/Trent_Green"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Japanese author and illustrator", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American software developer, co-founded Netscape", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American software developer, co-founded <a href=\"https://wikipedia.org/wiki/Netscape\" title=\"Netscape\">Netscape</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American software developer, co-founded <a href=\"https://wikipedia.org/wiki/Netscape\" title=\"Netscape\">Netscape</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Netscape", "link": "https://wikipedia.org/wiki/Netscape"}]}, {"year": "1972", "text": "<PERSON>, American drummer and songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American football player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, English environmentalist and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Si%C3%A2n_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English environmentalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Si%C3%A2n_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English environmentalist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Si%C3%A2n_Berry"}]}, {"year": "1974", "text": "<PERSON>, Barbadian cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Irish footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1974)\" title=\"<PERSON> (footballer, born 1974)\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1974)\" title=\"<PERSON> (footballer, born 1974)\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON> (footballer, born 1974)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1974)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian ice hockey player (d. 2011)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/K%C4%81rlis_Skrasti%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian ice hockey player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C4%81rlis_Skrasti%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian ice hockey player (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C4%81rlis_Skrasti%C5%86%C5%A1"}]}, {"year": "1974", "text": "<PERSON>, Swedish singer-songwriter and bass player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0ar%C4%8Devi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0ar%C4%8Devi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikola_%C5%A0ar%C4%8Devi%C4%87"}]}, {"year": "1975", "text": "<PERSON>, American wrestler", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Benjamin\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Benjamin\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1975", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)"}]}, {"year": "1975", "text": "<PERSON>, Welsh rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Polish-German footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor, director, and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Savage\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Fijian-Australian rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Fijian-Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Fijian-Australian rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Kara_Goucher\" title=\"<PERSON> Goucher\"><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Goucher\" title=\"<PERSON> Goucher\"><PERSON></a>, American runner", "links": [{"title": "Kara <PERSON>uche<PERSON>", "link": "https://wikipedia.org/wiki/Kara_Goucher"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1978)\" title=\"<PERSON><PERSON> (footballer, born 1978)\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1978)\" title=\"<PERSON><PERSON> (footballer, born 1978)\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1978)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1978)"}]}, {"year": "1979", "text": "<PERSON>, Malaysian Chinese singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian Chinese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian Chinese singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South Korean footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>o", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>o"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American author and performance artist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Pet<PERSON>\"><PERSON><PERSON><PERSON></a>, American author and performance artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Pet<PERSON>\"><PERSON><PERSON><PERSON></a>, American author and performance artist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American soccer player and manager", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Japanese race car driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Estonian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Ave_Pajo\" title=\"Ave Pajo\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ave_Pajo\" title=\"Ave Pajo\"><PERSON></a>, Estonian footballer", "links": [{"title": "Ave <PERSON>", "link": "https://wikipedia.org/wiki/Ave_Pajo"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Finnish tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>en"}]}, {"year": "1984", "text": "<PERSON>, Filipino basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/LA_Tenorio\" title=\"LA Tenorio\"><PERSON> Tenorio</a>, Filipino basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/LA_Tenorio\" title=\"LA Tenorio\">LA Tenorio</a>, Filipino basketball player", "links": [{"title": "LA Tenorio", "link": "https://wikipedia.org/wiki/LA_Tenorio"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Polish swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Cameroonian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American skier", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American singer-songwriter and dancer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Estonian cyclist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B5e%C3%A4%C3%A4r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B5e%C3%A4%C3%A4r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gert_J%C3%B5e%C3%A4%C3%A4r"}]}, {"year": "1987", "text": "<PERSON>, American animator, composer, and screenwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, composer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sugar\"><PERSON></a>, American animator, composer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Romanian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, New Zealand race car driver", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/F%C3%<PERSON><PERSON>_(footballer,_born_1990)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1990)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%<PERSON><PERSON>_(footballer,_born_1990)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1990)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1990)", "link": "https://wikipedia.org/wiki/F%C3%<PERSON><PERSON>_(footballer,_born_1990)"}]}, {"year": "1990", "text": "<PERSON>, Brazilian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1990)\" title=\"<PERSON> (footballer, born 1990)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1990)\" title=\"<PERSON> (footballer, born 1990)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1990)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1990)"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American actor and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Australian swimmer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, American footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/DeAnd<PERSON>_<PERSON>\" title=\"DeAnd<PERSON>dl<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DeAnd<PERSON>_<PERSON>\" title=\"DeAnd<PERSON>dl<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>in"}]}, {"year": "1999", "text": "<PERSON>, American voice actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Northern Irish footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "230", "text": "Empress <PERSON><PERSON><PERSON>, <PERSON>'s wife (b. 159)", "html": "230 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>\" title=\"Empress <PERSON><PERSON>\">Empress <PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s wife (b. 159)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>\" title=\"Empress <PERSON><PERSON>\">Empress <PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s wife (b. 159)", "links": [{"title": "Empress <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "518", "text": "<PERSON><PERSON><PERSON>, Byzantine emperor (b. 430)", "html": "518 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine emperor (b. 430)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine emperor (b. 430)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>sius_I_Dicorus"}]}, {"year": "715", "text": "<PERSON><PERSON>, Japanese prince (b.c 637)", "html": "715 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>\" title=\"Prince <PERSON>\"><PERSON><PERSON></a>, Japanese prince (b.c 637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>\" title=\"Prince <PERSON>\"><PERSON><PERSON></a>, Japanese prince (b.c 637)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ga"}]}, {"year": "880", "text": "<PERSON><PERSON>, Japanese poet (b. 825)", "html": "880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Narihira\" title=\"<PERSON><PERSON> no Narihira\"><PERSON><PERSON> no Na<PERSON>hir<PERSON></a>, Japanese poet (b. 825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Narihira\" title=\"<PERSON><PERSON> no Narihira\"><PERSON><PERSON> no Na<PERSON>hir<PERSON></a>, Japanese poet (b. 825)", "links": [{"title": "<PERSON><PERSON> no Narihira", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>a"}]}, {"year": "981", "text": "<PERSON><PERSON>, king of Viguera", "html": "981 - <a href=\"https://wikipedia.org/wiki/Ramiro_Garc%C3%A9s_of_Viguera\" title=\"<PERSON><PERSON>arcés of Viguera\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Viguera\" title=\"Kingdom of Viguera\">Viguera</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ramiro_Garc%C3%A9s_of_Viguera\" title=\"<PERSON><PERSON> Garcés of Viguera\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Viguera\" title=\"Kingdom of Viguera\">Viguera</a>", "links": [{"title": "<PERSON><PERSON> of Viguera", "link": "https://wikipedia.org/wiki/Ramiro_Garc%C3%A9s_of_Viguera"}, {"title": "Kingdom of Viguera", "link": "https://wikipedia.org/wiki/Kingdom_of_Viguera"}]}, {"year": "1169", "text": "<PERSON> of Ravenna, Italian cartographer, entomologist and historian", "html": "1169 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pisa\" title=\"<PERSON> of Pisa\"><PERSON> of Ravenna</a>, Italian cartographer, entomologist and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pi<PERSON>\" title=\"<PERSON> of Pisa\"><PERSON> of Ravenna</a>, Italian cartographer, entomologist and historian", "links": [{"title": "<PERSON> of Pisa", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pisa"}]}, {"year": "1228", "text": "<PERSON>, English cardinal and theologian (b. 1150)", "html": "1228 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal and theologian (b. 1150)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal and theologian (b. 1150)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1270", "text": "<PERSON>, Hungarian cardinal (b. c. 1205)", "html": "1270 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian cardinal (b. c. 1205)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nc<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian cardinal (b. c. 1205)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_B%C3%A1ncsa"}]}, {"year": "1386", "text": "<PERSON>, Duke of Austria (b. 1351)", "html": "1386 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1351)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1351)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}]}, {"year": "1441", "text": "<PERSON>, Dutch painter (b. 1359)", "html": "1441 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1359)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1359)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1546", "text": "<PERSON>, 5th Lord <PERSON>, Scottish statesman (b. c. 1493)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Lord_<PERSON>\" title=\"<PERSON>, 5th Lord <PERSON>\"><PERSON>, 5th Lord <PERSON></a>, Scottish statesman (b. c. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Lord_<PERSON>\" title=\"<PERSON>, 5th Lord <PERSON>\"><PERSON>, 5th Lord <PERSON></a>, Scottish statesman (b. c. 1493)", "links": [{"title": "<PERSON>, 5th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Lord_<PERSON>"}]}, {"year": "1553", "text": "<PERSON>, Elector of Saxony (b. 1521)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1521)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony"}]}, {"year": "1654", "text": "<PERSON>, King of the Romans (b. 1633)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_the_Romans\" title=\"<PERSON> IV, King of the Romans\"><PERSON>, King of the Romans</a> (b. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_King_of_the_Romans\" title=\"<PERSON> IV, King of the Romans\"><PERSON> IV, King of the Romans</a> (b. 1633)", "links": [{"title": "<PERSON>, King of the Romans", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_the_Romans"}]}, {"year": "1706", "text": "<PERSON>ber<PERSON>, Canadian captain and explorer (b. 1661)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27Iberville\" title=\"<PERSON> d'Iberville\"><PERSON>ber<PERSON></a>, Canadian captain and explorer (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27Iberville\" title=\"<PERSON> d'Iberville\"><PERSON>Iberville</a>, Canadian captain and explorer (b. 1661)", "links": [{"title": "<PERSON>Iber<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27Iberville"}]}, {"year": "1737", "text": "<PERSON><PERSON>, Grand Duke of Tuscany (b. 1671)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON> <PERSON><PERSON>, Grand Duke of Tuscany</a> (b. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON> <PERSON><PERSON>, Grand Duke of Tuscany</a> (b. 1671)", "links": [{"title": "<PERSON><PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1742", "text": "<PERSON>, English historian, poet, and playwright (b. 1673)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, poet, and playwright (b. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, poet, and playwright (b. 1673)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1746", "text": "<PERSON> of Spain (b. 1683)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/Philip_V_of_Spain\" title=\"<PERSON> V of Spain\"><PERSON> of Spain</a> (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philip_<PERSON>_of_Spain\" title=\"<PERSON> V of Spain\"><PERSON> of Spain</a> (b. 1683)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Philip_V_of_Spain"}]}, {"year": "1747", "text": "<PERSON>, Italian cellist and composer (b. 1670)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (b. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, American minister (b. 1720)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON>, Spanish anatomist (b. 1714)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish anatomist (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish anatomist (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON>, English general and politician, Secretary of State for the Northern Department (b. 1721)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for the Northern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department"}]}, {"year": "1797", "text": "<PERSON>, Irish-English philosopher, academic, and politician (b. 1729)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English philosopher, academic, and politician (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English philosopher, academic, and politician (b. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON><PERSON><PERSON>, German operatic singer and actress (b. 1789)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German operatic singer and actress (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German operatic singer and actress (b. 1789)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>r"}]}, {"year": "1850", "text": "<PERSON><PERSON><PERSON>, Persian religious leader, founded Bábism (b. 1819)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/B%C3%A1b\" title=\"Báb\"><PERSON><PERSON><PERSON></a>, Persian religious leader, founded <a href=\"https://wikipedia.org/wiki/B%C3%A1bism\" title=\"Bábism\">Bábism</a> (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A1b\" title=\"Báb\"><PERSON><PERSON><PERSON></a>, Persian religious leader, founded <a href=\"https://wikipedia.org/wiki/B%C3%A1bism\" title=\"Bábism\">Bábism</a> (b. 1819)", "links": [{"title": "B<PERSON>b", "link": "https://wikipedia.org/wiki/B%C3%A1b"}, {"title": "Bábism", "link": "https://wikipedia.org/wiki/B%C3%A1bism"}]}, {"year": "1850", "text": "<PERSON>, American general and politician, 12th President of the United States (b. 1784)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1852", "text": "<PERSON>, American lawyer and politician, 2nd United States Secretary of the Interior (b. 1794)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON>, Italian chemist and academic (b. 1776)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian chemist and academic (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian chemist and academic (b. 1776)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1856", "text": "<PERSON>, American religious leader and politician (b. 1813)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and politician (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and politician (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, French physician and anatomist (b. 1824)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and anatomist (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and anatomist (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Chilean captain (b. 1848)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean captain (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean captain (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Belgian geologist and photographer (b. 1842)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Alphon<PERSON>\">Alphon<PERSON></a>, Belgian geologist and photographer (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Alphon<PERSON>\">Alphon<PERSON></a>, Belgian geologist and photographer (b. 1842)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C3%A<PERSON>ois_Renard"}]}, {"year": "1927", "text": "<PERSON>, Jr., American actor (b. 1853)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor (b. 1853)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1932", "text": "<PERSON>, American businessman, founded the Gillette Company (b. 1855)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/King_Camp_Gillette\" class=\"mw-redirect\" title=\"King Camp Gillette\">King Camp Gillette</a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Gillette_Company\" class=\"mw-redirect\" title=\"Gillette Company\">Gillette Company</a> (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/King_Camp_Gillette\" class=\"mw-redirect\" title=\"King Camp Gillette\">King Camp Gillette</a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Gillette_Company\" class=\"mw-redirect\" title=\"Gillette Company\">Gillette Company</a> (b. 1855)", "links": [{"title": "King Camp Gillette", "link": "https://wikipedia.org/wiki/King_Camp_Gillette"}, {"title": "Gillette Company", "link": "https://wikipedia.org/wiki/Gillette_Company"}]}, {"year": "1935", "text": "<PERSON>, 16th president of Liberia (b. 1861)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 16th president of Liberia (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 16th president of Liberia (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American commander (b. 1899)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Oliver Law\"><PERSON></a>, American commander (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Oliver Law\"><PERSON></a>, American commander (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and jurist (b. 1870)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Polish-Lithuanian general and politician (b. 1865)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%B<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Lithuanian general and politician (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%B<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Lithuanian general and politician (b. 1865)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English-Australian composer and conductor (b. 1874)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian composer and conductor (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian composer and conductor (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American baseball player and sportscaster (b. 1894)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English race car driver (b. 1928)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Mexican politician and provisional president, 1920 (b. 1881)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican politician and provisional president, 1920 (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican politician and provisional president, 1920 (b. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Slovene journalist and painter (b. 1883)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Ferenc_Tal%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene journalist and painter (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferenc_Tal%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene journalist and painter (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_Tal%C3%A1nyi"}]}, {"year": "1962", "text": "<PERSON>, French philosopher, novelist, and poet (b. 1897)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher, novelist, and poet (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher, novelist, and poet (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, American spy and witness in <PERSON><PERSON> case(b. 1901)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American spy and witness in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s\" title=\"<PERSON><PERSON> Hiss\">Hiss</a> case(b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American spy and witness in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s\" title=\"<PERSON><PERSON> Hiss\">Hiss</a> case(b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hiss"}]}, {"year": "1967", "text": "<PERSON><PERSON>, German physician and academic (b. 1874)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician and academic (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician and academic (b. 1874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Pakistani dentist and politician (b. 1893)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani dentist and politician (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani dentist and politician (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fat<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Swedish actress (b. 1899)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actress (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actress (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Estonian author and politician (b. 1886)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian author and politician (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian author and politician (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American opera singer (b. 1903)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American opera singer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American opera singer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American jurist and politician, 14th Chief Justice of the United States (b. 1891)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, 14th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, 14th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1977", "text": "<PERSON>, American activist (b. 1885)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American actress and author (b. 1899)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and author (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Co<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and author (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Brazilian poet, playwright, and composer (b. 1913)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian poet, playwright, and composer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian poet, playwright, and composer (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>s"}]}, {"year": "1984", "text": "<PERSON>, American mathematician (b. 1902)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Grand Duchess of Luxembourg (b. 1896)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Duchess_of_Luxembourg\" title=\"<PERSON>, Grand Duchess of Luxembourg\"><PERSON>, Grand Duchess of Luxembourg</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Duchess_of_Luxembourg\" title=\"<PERSON>, Grand Duchess of Luxembourg\"><PERSON>, Grand Duchess of Luxembourg</a> (b. 1896)", "links": [{"title": "<PERSON>, Grand Duchess of Luxembourg", "link": "https://wikipedia.org/wiki/<PERSON>,_Grand_Duchess_of_Luxembourg"}]}, {"year": "1985", "text": "<PERSON>, Scottish-American activist, founded Narcotics Anonymous (b. 1911)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American activist, founded <a href=\"https://wikipedia.org/wiki/Narcotics_Anonymous\" title=\"Narcotics Anonymous\">Narcotics Anonymous</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American activist, founded <a href=\"https://wikipedia.org/wiki/Narcotics_Anonymous\" title=\"Narcotics Anonymous\">Narcotics Anonymous</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Narcotics Anonymous", "link": "https://wikipedia.org/wiki/Narcotics_Anonymous"}]}, {"year": "1986", "text": "Patriarch <PERSON> of Alexandria (b. 1915)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON>_VI_of_Alexandria\" title=\"Patriarch <PERSON> of Alexandria\">Patriarch <PERSON> of Alexandria</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON>_VI_of_Alexandria\" title=\"Patriarch <PERSON> VI of Alexandria\">Patriarch <PERSON> of Alexandria</a> (b. 1915)", "links": [{"title": "Patriarch <PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/Patriarch_<PERSON>_<PERSON>_of_Alexandria"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Australian ballet dancer (b. 1946)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian ballet dancer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian ballet dancer (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1992", "text": "<PERSON>, American journalist (b. 1912)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Turkish poet and educator (b. 1940)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Metin_Alt%C4%B1ok\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish poet and educator (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Metin_Alt%C4%B1ok\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish poet and educator (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Metin_Alt%C4%B1ok"}]}, {"year": "1994", "text": "<PERSON>, Canadian ice hockey player (b. 1921)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American lawyer (b. 1907)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian politician, 56th Secretary of State for Canada (b. 1944)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 56th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 56th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Secretary of State for Canada", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Canada"}]}, {"year": "2000", "text": "<PERSON>, English actor (b. 1941)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1941)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2002", "text": "<PERSON>, American bodybuilder (b. 1914)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>an"}]}, {"year": "2002", "text": "<PERSON>, American actor (b. 1925)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Steiger"}]}, {"year": "2004", "text": "<PERSON>, American journalist and historian (b. 1963)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and historian (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and historian (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American actress (b. 1917)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian engineer and politician (b. 1948)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and politician (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Russian speed skater (b. 1931)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>_(speed_skater)\" title=\"<PERSON><PERSON><PERSON> (speed skater)\"><PERSON><PERSON><PERSON></a>, Russian speed skater (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>_(speed_skater)\" title=\"<PERSON><PERSON><PERSON> (speed skater)\"><PERSON><PERSON><PERSON></a>, Russian speed skater (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON> (speed skater)", "link": "https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>_(speed_skater)"}]}, {"year": "2005", "text": "<PERSON>, Canadian ice hockey player (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American keyboard player and producer (b. 1948)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and producer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Williams\"><PERSON></a>, American keyboard player and producer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Williams"}]}, {"year": "2007", "text": "<PERSON>, American actor (b. 1905)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1905)\" title=\"<PERSON> (actor, born 1905)\"><PERSON></a>, American actor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1905)\" title=\"<PERSON> (actor, born 1905)\"><PERSON></a>, American actor (b. 1905)", "links": [{"title": "<PERSON> (actor, born 1905)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor,_born_1905)"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish accountant and politician, Minister for Transport, Tourism and Sport (b. 1948)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish accountant and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Transport,_Tourism_and_Sport\" class=\"mw-redirect\" title=\"Minister for Transport, Tourism and Sport\">Minister for Transport, Tourism and Sport</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish accountant and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Transport,_Tourism_and_Sport\" class=\"mw-redirect\" title=\"Minister for Transport, Tourism and Sport\">Minister for Transport, Tourism and Sport</a> (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9amus_<PERSON>"}, {"title": "Minister for Transport, Tourism and Sport", "link": "https://wikipedia.org/wiki/Minister_for_Transport,_Tourism_and_Sport"}]}, {"year": "2010", "text": "<PERSON>, Australian author and playwright (b. 1916)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Australian author and playwright (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Australian author and playwright (b. 1916)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2011", "text": "<PERSON>, American basketball player (b. 1930)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentine singer-songwriter (b. 1937)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Facundo_Cabral\" title=\"Facundo Cabral\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine singer-songwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Facundo_Cabral\" title=\"Facundo Cabral\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine singer-songwriter (b. 1937)", "links": [{"title": "Facundo Cabral", "link": "https://wikipedia.org/wiki/Facundo_Cabral"}]}, {"year": "2012", "text": "<PERSON>, South Korean-American martial artist (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-chul\" title=\"<PERSON> J<PERSON>-chul\"><PERSON></a>, South Korean-American martial artist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-chul\" title=\"<PERSON>-chul\"><PERSON>-<PERSON>ul</a>, South Korean-American martial artist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-chul"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American baseball player (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Chick_King\" title=\"Chick King\"><PERSON><PERSON></a>, American baseball player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chick_King\" title=\"Chick King\"><PERSON><PERSON></a>, American baseball player (b. 1930)", "links": [{"title": "<PERSON>ck King", "link": "https://wikipedia.org/wiki/Chi<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Cook Islander physician and politician, 6th Prime Minister of the Cook Islands (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tere<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cook Islander physician and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Cook_Islands\" title=\"Prime Minister of the Cook Islands\">Prime Minister of the Cook Islands</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tere<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cook Islander physician and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Cook_Islands\" title=\"Prime Minister of the Cook Islands\">Prime Minister of the Cook Islands</a> (b. 1934)", "links": [{"title": "Terepai Mao<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>re<PERSON>i_<PERSON>ate"}, {"title": "Prime Minister of the Cook Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Cook_Islands"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian cardinal (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Eug%C3%AAnio_Sales\" title=\"Eugênio Sales\">Eugênio <PERSON></a>, Brazilian cardinal (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%AAnio_Sales\" title=\"Eugênio Sales\">Eugênio <PERSON></a>, Brazilian cardinal (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%AAnio_Sales"}]}, {"year": "2013", "text": "<PERSON>, Liechtensteiner politician, 9th Prime Minister of Liechtenstein (b. 1959)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liechtensteiner politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Liechtenstein\" class=\"mw-redirect\" title=\"Prime Minister of Liechtenstein\">Prime Minister of Liechtenstein</a> (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liechtensteiner politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Liechtenstein\" class=\"mw-redirect\" title=\"Prime Minister of Liechtenstein\">Prime Minister of Liechtenstein</a> (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Markus_B%C3%BCchel"}, {"title": "Prime Minister of Liechtenstein", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Liechtenstein"}]}, {"year": "2013", "text": "<PERSON>, Solomon lawyer and politician (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Solomon lawyer and politician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Solomon lawyer and politician (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON> of Varna, Bulgarian metropolitan (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Varna\" title=\"<PERSON><PERSON> of Varna\"><PERSON><PERSON> of Varna</a>, Bulgarian metropolitan (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Varna\" title=\"<PERSON><PERSON> of Varna\"><PERSON><PERSON> of Varna</a>, Bulgarian metropolitan (b. 1954)", "links": [{"title": "<PERSON><PERSON> of Varna", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Varna"}]}, {"year": "2013", "text": "<PERSON>, American author and poet (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and poet (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and poet (b. 1927)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American activist, co-founded the Clearwater Festival (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Seeger\" title=\"<PERSON>shi Seeger\"><PERSON><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Clearwater_Festival\" title=\"Clearwater Festival\">Clearwater Festival</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Seeger\" title=\"<PERSON><PERSON> Seeger\"><PERSON><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Clearwater_Festival\" title=\"Clearwater Festival\">Clearwater Festival</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Seeger"}, {"title": "Clearwater Festival", "link": "https://wikipedia.org/wiki/Clearwater_Festival"}]}, {"year": "2014", "text": "<PERSON>, Paraguayan violinist and composer (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>_Florent%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Paraguayan violinist and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>_Florent%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Paraguayan violinist and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lorenzo_%C3%81l<PERSON><PERSON>_Florent%C3%ADn"}]}, {"year": "2014", "text": "<PERSON>, Polish-Canadian businessman and philanthropist (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian businessman and philanthropist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian businessman and philanthropist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American businesswoman, co-founded Ford Models (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ford\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Ford_Models\" title=\"Ford Models\">Ford Models</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Ford\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Ford_Models\" title=\"Ford Models\">Ford Models</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ford Models", "link": "https://wikipedia.org/wiki/Ford_Models"}]}, {"year": "2014", "text": "<PERSON>, English guitarist and songwriter (b. 1953)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist and songwriter (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist and songwriter (b. 1953)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2015", "text": "<PERSON>, French fashion designer (b. 1958)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Christian_Audigier\" title=\"Christian Audigier\"><PERSON></a>, French fashion designer (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_Audigier\" title=\"Christian Audigier\"><PERSON></a>, French fashion designer (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Audigier"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON>, Saudi Arabian economist and politician, Saudi Arabian Minister of Foreign Affairs (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_bin_<PERSON><PERSON><PERSON>_bin_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ud\" class=\"mw-redirect\" title=\"<PERSON><PERSON> bin F<PERSON>al bin <PERSON><PERSON><PERSON>\"><PERSON><PERSON> bin <PERSON> bin <PERSON><PERSON><PERSON></a>, Saudi Arabian economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Saudi_Arabia)\" title=\"Ministry of Foreign Affairs (Saudi Arabia)\">Saudi Arabian Minister of Foreign Affairs</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_bin_<PERSON><PERSON><PERSON>_bin_<PERSON><PERSON><PERSON>_<PERSON>_Saud\" class=\"mw-redirect\" title=\"<PERSON><PERSON> bin <PERSON><PERSON>al bin <PERSON><PERSON><PERSON>\"><PERSON><PERSON> bin <PERSON> bin <PERSON><PERSON><PERSON></a>, Saudi Arabian economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Saudi_Arabia)\" title=\"Ministry of Foreign Affairs (Saudi Arabia)\">Saudi Arabian Minister of Foreign Affairs</a> (b. 1940)", "links": [{"title": "<PERSON><PERSON> bin <PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_bin_<PERSON><PERSON><PERSON>_bin_<PERSON><PERSON><PERSON>_<PERSON>_Saud"}, {"title": "Ministry of Foreign Affairs (Saudi Arabia)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Saudi_Arabia)"}]}, {"year": "2019", "text": "<PERSON>, American politician (b. 1929)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American businessman and politician (b. 1930)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, 43rd President of Argentina (b. 1937)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BAa\" title=\"<PERSON> Rú<PERSON>\"><PERSON></a>, 43rd <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BAa\" title=\"<PERSON> Rú<PERSON>\"><PERSON></a>, 43rd <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_R%C3%BAa"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American actor (b. 1931)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Rip_Torn\" title=\"Rip Torn\"><PERSON><PERSON></a>, American actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rip_Torn\" title=\"Rip Torn\"><PERSON><PERSON></a>, American actor (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rip_Torn"}]}, {"year": "2019", "text": "<PERSON>, English actor (b. 1927)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, English reporter and commentator (b. 1945)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/2022\" title=\"2022\">2022</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(commentator)\" title=\"<PERSON> (commentator)\"><PERSON></a>, English reporter and commentator (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2022\" title=\"2022\">2022</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(commentator)\" title=\"<PERSON> (commentator)\"><PERSON></a>, English reporter and commentator (b. 1945)", "links": [{"title": "2022", "link": "https://wikipedia.org/wiki/2022"}, {"title": "<PERSON> (commentator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(commentator)"}]}, {"year": "2024", "text": "<PERSON>, American country/gospel singer (b. 1948)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country/gospel singer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country/gospel singer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, New Zealand biochemist (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, New Zealand biochemist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, New Zealand biochemist (b. 1943)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)"}]}, {"year": "2024", "text": "<PERSON>, American politician (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American biologist (b. 1931)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Polish actor", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}