{"date": "May 31", "url": "https://wikipedia.org/wiki/May_31", "data": {"Events": [{"year": "455", "text": "Emperor <PERSON><PERSON><PERSON> is stoned to death by an angry mob while fleeing Rome.", "html": "455 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Maximus\" title=\"<PERSON><PERSON><PERSON> Maximus\"><PERSON><PERSON><PERSON> Maximus</a> is <a href=\"https://wikipedia.org/wiki/Stoning\" title=\"Stoning\">stoned to death</a> by an <a href=\"https://wikipedia.org/wiki/Ochlocracy\" class=\"mw-redirect\" title=\"Ochlocracy\">angry mob</a> while fleeing <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Maximus\" title=\"<PERSON><PERSON><PERSON> Maximus\"><PERSON><PERSON><PERSON> Maximus</a> is <a href=\"https://wikipedia.org/wiki/Stoning\" title=\"Stoning\">stoned to death</a> by an <a href=\"https://wikipedia.org/wiki/Ochlocracy\" class=\"mw-redirect\" title=\"Ochlocracy\">angry mob</a> while fleeing <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Maximus"}, {"title": "Stoning", "link": "https://wikipedia.org/wiki/Stoning"}, {"title": "Ochlocracy", "link": "https://wikipedia.org/wiki/Ochlocracy"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}]}, {"year": "1215", "text": "Zhongdu (now Beijing), then under the control of the Jurchen ruler Emperor <PERSON><PERSON><PERSON> of Jin, is captured by the Mongols under <PERSON><PERSON><PERSON>, ending the Battle of Zhongdu.", "html": "1215 - <a href=\"https://wikipedia.org/wiki/Zhongdu\" title=\"Zhongdu\">Zhongdu</a> (now Beijing), then under the control of the <a href=\"https://wikipedia.org/wiki/Jurchen_people\" title=\"Jurchen people\">Jurchen</a> ruler <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON> of Jin\">Emperor <PERSON><PERSON><PERSON> of Jin</a>, is captured by the <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongols</a> under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, ending the <a href=\"https://wikipedia.org/wiki/Battle_of_Zhongdu\" class=\"mw-redirect\" title=\"Battle of Zhongdu\">Battle of Zhongdu</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zhongdu\" title=\"Zhongdu\">Zhongdu</a> (now Beijing), then under the control of the <a href=\"https://wikipedia.org/wiki/Jurchen_people\" title=\"Jurchen people\">Jurchen</a> ruler <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON> of Jin\">Emperor <PERSON><PERSON><PERSON> of Jin</a>, is captured by the <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongols</a> under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, ending the <a href=\"https://wikipedia.org/wiki/Battle_of_Zhongdu\" class=\"mw-redirect\" title=\"Battle of Zhongdu\">Battle of Zhongdu</a>.", "links": [{"title": "<PERSON>hong<PERSON>", "link": "https://wikipedia.org/wiki/Zhong<PERSON>"}, {"title": "Jurchen people", "link": "https://wikipedia.org/wiki/Jurchen_people"}, {"title": "Emperor <PERSON><PERSON><PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Jin"}, {"title": "Mongols", "link": "https://wikipedia.org/wiki/Mongols"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Zhongdu", "link": "https://wikipedia.org/wiki/Battle_of_Zhongdu"}]}, {"year": "1223", "text": "Mongol invasion of the Cumans: Battle of the Kalka River: Mongol armies of <PERSON><PERSON><PERSON> led by <PERSON><PERSON><PERSON> defeat <PERSON>an <PERSON> and Cumans.", "html": "1223 - Mongol invasion of the Cumans: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Kalka_River\" title=\"Battle of the Kalka River\">Battle of the Kalka River</a>: <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol</a> armies of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Khan\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a> led by <a href=\"https://wikipedia.org/wiki/Subutai\" title=\"Subutai\">Subutai</a> defeat <a href=\"https://wikipedia.org/wiki/Kievan_Rus%27\" title=\"Kievan Rus'\"><PERSON>an <PERSON>'</a> and <a href=\"https://wikipedia.org/wiki/Cumans\" title=\"Cumans\">Cumans</a>.", "no_year_html": "Mongol invasion of the Cumans: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Kalka_River\" title=\"Battle of the Kalka River\">Battle of the Kalka River</a>: <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol</a> armies of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Khan\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a> led by <a href=\"https://wikipedia.org/wiki/Subutai\" title=\"Subutai\">Subutai</a> defeat <a href=\"https://wikipedia.org/wiki/Kievan_Rus%27\" title=\"Kievan Rus'\"><PERSON>an <PERSON>'</a> and <a href=\"https://wikipedia.org/wiki/Cumans\" title=\"Cumans\">Cumans</a>.", "links": [{"title": "Battle of the Kalka River", "link": "https://wikipedia.org/wiki/Battle_of_the_Kalka_River"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Subutai", "link": "https://wikipedia.org/wiki/Subutai"}, {"title": "<PERSON><PERSON>'", "link": "https://wikipedia.org/wiki/Kievan_Rus%27"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>s"}]}, {"year": "1293", "text": "Mongol invasion of Java was a punitive expedition against King <PERSON><PERSON><PERSON><PERSON> of Singhasari, who had refused to pay tribute to the Yuan and maimed one of its ministers. However, it ended with failure for the Mongols. Regarded as establish City of Surabaya", "html": "1293 - <a href=\"https://wikipedia.org/wiki/Mongol_invasion_of_Java\" title=\"Mongol invasion of Java\">Mongol invasion of Java</a> was a <a href=\"https://wikipedia.org/wiki/Punitive_expedition\" title=\"Punitive expedition\">punitive expedition</a> against King <a href=\"https://wikipedia.org/wiki/Kertanegara\" class=\"mw-redirect\" title=\"Kertanegara\">Kertanegara</a> of <a href=\"https://wikipedia.org/wiki/Singhasari\" title=\"Singhasa<PERSON>\"><PERSON><PERSON><PERSON></a>, who had refused to pay tribute to the Yuan and maimed one of its ministers. However, it ended with failure for the <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongols</a>. Regarded as establish City of <a href=\"https://wikipedia.org/wiki/Surabaya\" title=\"Surabaya\">Surabaya</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongol_invasion_of_Java\" title=\"Mongol invasion of Java\">Mongol invasion of Java</a> was a <a href=\"https://wikipedia.org/wiki/Punitive_expedition\" title=\"Punitive expedition\">punitive expedition</a> against <PERSON> <a href=\"https://wikipedia.org/wiki/Kertanegara\" class=\"mw-redirect\" title=\"Kertanegara\">Kertanegara</a> of <a href=\"https://wikipedia.org/wiki/Singhasari\" title=\"Singhasa<PERSON>\"><PERSON><PERSON><PERSON></a>, who had refused to pay tribute to the Yuan and maimed one of its ministers. However, it ended with failure for the <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongols</a>. Regarded as establish City of <a href=\"https://wikipedia.org/wiki/Surabaya\" title=\"Surabaya\">Surabaya</a>", "links": [{"title": "Mongol invasion of Java", "link": "https://wikipedia.org/wiki/Mongol_invasion_of_Java"}, {"title": "Punitive expedition", "link": "https://wikipedia.org/wiki/Punitive_expedition"}, {"title": "Kertanegara", "link": "https://wikipedia.org/wiki/Kertanegara"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Mongols", "link": "https://wikipedia.org/wiki/Mongols"}, {"title": "Surabaya", "link": "https://wikipedia.org/wiki/Surabaya"}]}, {"year": "1578", "text": "King <PERSON> lays the first stone of the Pont Neuf (New Bridge), the oldest bridge of Paris, France.", "html": "1578 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> III of France\"><PERSON></a> lays the first stone of the <a href=\"https://wikipedia.org/wiki/Pont_Neuf\" title=\"Pont Neuf\"><PERSON></a> (<i>New Bridge</i>), the oldest bridge of <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris, France</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> III of France\"><PERSON> III</a> lays the first stone of the <a href=\"https://wikipedia.org/wiki/<PERSON>_Neuf\" title=\"Pont Neuf\"><PERSON></a> (<i>New Bridge</i>), the oldest bridge of <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris, France</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Neuf"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "1610", "text": "The pageant London's Love to Prince <PERSON> on the River Thames celebrates the creation of Prince <PERSON> as Prince of Wales.", "html": "1610 - The pageant <i><a href=\"https://wikipedia.org/wiki/London%27s_Love_to_Prince_<PERSON>\" title=\"London's Love to <PERSON>\">London's Love to <PERSON></a></i> on the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a> celebrates the creation of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\">Prince <PERSON></a> as <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a>.", "no_year_html": "The pageant <i><a href=\"https://wikipedia.org/wiki/London%27s_Love_to_Prince_<PERSON>\" title=\"London's Love to <PERSON>\">London's Love to <PERSON></a></i> on the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a> celebrates the creation of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\">Prince <PERSON></a> as <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a>.", "links": [{"title": "London's Love to Prince Henry", "link": "https://wikipedia.org/wiki/London%27s_Love_to_<PERSON>_<PERSON>"}, {"title": "River Thames", "link": "https://wikipedia.org/wiki/River_Thames"}, {"title": "<PERSON>, Prince of Wales", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Wales"}, {"title": "Prince of Wales", "link": "https://wikipedia.org/wiki/Prince_of_Wales"}]}, {"year": "1669", "text": "Citing poor eyesight as a reason, <PERSON> records the last event in his diary.", "html": "1669 - Citing poor eyesight as a reason, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> records the last event in <a href=\"https://wikipedia.org/wiki/<PERSON>#The_diary\" title=\"<PERSON>\">his diary</a>.", "no_year_html": "Citing poor eyesight as a reason, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> records the last event in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#The_diary\" title=\"<PERSON>\">his diary</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>#The_diary"}]}, {"year": "1775", "text": "American Revolution: The Mecklenburg Resolves are adopted in the Province of North Carolina.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Mecklenburg_Resolves\" title=\"Mecklenburg Resolves\">Mecklenburg Resolves</a> are adopted in the <a href=\"https://wikipedia.org/wiki/Province_of_North_Carolina\" title=\"Province of North Carolina\">Province of North Carolina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Mecklenburg_Resolves\" title=\"Mecklenburg Resolves\">Mecklenburg Resolves</a> are adopted in the <a href=\"https://wikipedia.org/wiki/Province_of_North_Carolina\" title=\"Province of North Carolina\">Province of North Carolina</a>.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "Mecklenburg Resolves", "link": "https://wikipedia.org/wiki/Mecklenburg_Resolves"}, {"title": "Province of North Carolina", "link": "https://wikipedia.org/wiki/Province_of_North_Carolina"}]}, {"year": "1790", "text": "<PERSON> explores the Strait of Juan de Fuca.", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> explores the <a href=\"https://wikipedia.org/wiki/Strait_of_Juan_de_Fuca\" title=\"Strait of Juan de Fuca\">Strait of Juan de Fuca</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> explores the <a href=\"https://wikipedia.org/wiki/Strait_of_Juan_de_Fuca\" title=\"Strait of Juan de Fuca\">Strait of Juan de Fuca</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Strait of Juan de Fuca", "link": "https://wikipedia.org/wiki/Strait_of_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "The United States enacts its first copyright statute, the Copyright Act of 1790.", "html": "1790 - The United States enacts its first <a href=\"https://wikipedia.org/wiki/Copyright_law_of_the_United_States\" title=\"Copyright law of the United States\">copyright statute</a>, the <a href=\"https://wikipedia.org/wiki/Copyright_Act_of_1790\" title=\"Copyright Act of 1790\">Copyright Act of 1790</a>.", "no_year_html": "The United States enacts its first <a href=\"https://wikipedia.org/wiki/Copyright_law_of_the_United_States\" title=\"Copyright law of the United States\">copyright statute</a>, the <a href=\"https://wikipedia.org/wiki/Copyright_Act_of_1790\" title=\"Copyright Act of 1790\">Copyright Act of 1790</a>.", "links": [{"title": "Copyright law of the United States", "link": "https://wikipedia.org/wiki/Copyright_law_of_the_United_States"}, {"title": "Copyright Act of 1790", "link": "https://wikipedia.org/wiki/Copyright_Act_of_1790"}]}, {"year": "1795", "text": "French Revolution: The Revolutionary Tribunal is suppressed.", "html": "1795 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Revolutionary_Tribunal\" title=\"Revolutionary Tribunal\">Revolutionary Tribunal</a> is suppressed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Revolutionary_Tribunal\" title=\"Revolutionary Tribunal\">Revolutionary Tribunal</a> is suppressed.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "Revolutionary Tribunal", "link": "https://wikipedia.org/wiki/Revolutionary_Tribunal"}]}, {"year": "1805", "text": "French and Spanish forces begin the assault against British forces occupying Diamond Rock, Martinique.", "html": "1805 - French and Spanish forces begin the <a href=\"https://wikipedia.org/wiki/Battle_of_Diamond_Rock\" title=\"Battle of Diamond Rock\">assault</a> against British forces occupying <a href=\"https://wikipedia.org/wiki/Diamond_Rock\" title=\"Diamond Rock\">Diamond Rock</a>, <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"Martinique\"><PERSON><PERSON></a>.", "no_year_html": "French and Spanish forces begin the <a href=\"https://wikipedia.org/wiki/Battle_of_Diamond_Rock\" title=\"Battle of Diamond Rock\">assault</a> against British forces occupying <a href=\"https://wikipedia.org/wiki/Diamond_Rock\" title=\"Diamond Rock\">Diamond Rock</a>, <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"<PERSON>ique\"><PERSON><PERSON></a>.", "links": [{"title": "Battle of Diamond Rock", "link": "https://wikipedia.org/wiki/Battle_of_Diamond_Rock"}, {"title": "Diamond Rock", "link": "https://wikipedia.org/wiki/Diamond_Rock"}, {"title": "Martinique", "link": "https://wikipedia.org/wiki/<PERSON>ique"}]}, {"year": "1813", "text": "In Australia, <PERSON>, <PERSON> and <PERSON> reach Mount Blaxland, effectively marking the end of a route across the Blue Mountains.", "html": "1813 - In Australia, <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reach <a href=\"https://wikipedia.org/wiki/Mount_Blaxland,_New_South_Wales\" class=\"mw-redirect\" title=\"Mount Blaxland, New South Wales\">Mount Blaxland</a>, effectively marking the end of a route across the <a href=\"https://wikipedia.org/wiki/Blue_Mountains_(New_South_Wales)\" title=\"Blue Mountains (New South Wales)\">Blue Mountains</a>.", "no_year_html": "In Australia, <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reach <a href=\"https://wikipedia.org/wiki/Mount_Blaxland,_New_South_Wales\" class=\"mw-redirect\" title=\"Mount Blaxland, New South Wales\">Mount Blaxland</a>, effectively marking the end of a route across the <a href=\"https://wikipedia.org/wiki/Blue_Mountains_(New_South_Wales)\" title=\"Blue Mountains (New South Wales)\">Blue Mountains</a>.", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_(explorer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mount Blaxland, New South Wales", "link": "https://wikipedia.org/wiki/Mount_Blaxland,_New_South_Wales"}, {"title": "Blue Mountains (New South Wales)", "link": "https://wikipedia.org/wiki/Blue_Mountains_(New_South_Wales)"}]}, {"year": "1859", "text": "The clock tower at the Houses of Parliament, which houses Big Ben, starts keeping time.", "html": "1859 - The clock tower at the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">Houses of Parliament</a>, which houses <a href=\"https://wikipedia.org/wiki/<PERSON>_Ben\" title=\"Big Ben\"><PERSON> Ben</a>, starts keeping time.", "no_year_html": "The clock tower at the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">Houses of Parliament</a>, which houses <a href=\"https://wikipedia.org/wiki/<PERSON>_Ben\" title=\"Big Ben\"><PERSON> Ben</a>, starts keeping time.", "links": [{"title": "Palace of Westminster", "link": "https://wikipedia.org/wiki/Palace_of_Westminster"}, {"title": "Big Ben", "link": "https://wikipedia.org/wiki/Big_Ben"}]}, {"year": "1862", "text": "American Civil War: Peninsula Campaign: Confederate forces under <PERSON> and <PERSON><PERSON><PERSON><PERSON> engage Union forces under <PERSON> outside the Confederate capital of Richmond, Virginia.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Peninsula_Campaign\" class=\"mw-redirect\" title=\"Peninsula Campaign\">Peninsula Campaign</a>: Confederate forces under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Seven_Pines\" title=\"Battle of Seven Pines\">engage</a> Union forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> outside the Confederate capital of <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Peninsula_Campaign\" class=\"mw-redirect\" title=\"Peninsula Campaign\">Peninsula Campaign</a>: Confederate forces under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Seven_Pines\" title=\"Battle of Seven Pines\">engage</a> Union forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> outside the Confederate capital of <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Peninsula Campaign", "link": "https://wikipedia.org/wiki/Peninsula_Campaign"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Seven Pines", "link": "https://wikipedia.org/wiki/Battle_of_Seven_Pines"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Richmond, Virginia", "link": "https://wikipedia.org/wiki/Richmond,_Virginia"}]}, {"year": "1864", "text": "American Civil War: Overland Campaign: Battle of Cold Harbor: The Army of Northern Virginia engages the Army of the Potomac.", "html": "1864 - American Civil War: <a href=\"https://wikipedia.org/wiki/Overland_Campaign\" title=\"Overland Campaign\">Overland Campaign</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cold_Harbor\" title=\"Battle of Cold Harbor\">Battle of Cold Harbor</a>: The <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> engages the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Overland_Campaign\" title=\"Overland Campaign\">Overland Campaign</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cold_Harbor\" title=\"Battle of Cold Harbor\">Battle of Cold Harbor</a>: The <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> engages the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a>.", "links": [{"title": "Overland Campaign", "link": "https://wikipedia.org/wiki/Overland_Campaign"}, {"title": "Battle of Cold Harbor", "link": "https://wikipedia.org/wiki/Battle_of_Cold_Harbor"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}, {"title": "Army of the Potomac", "link": "https://wikipedia.org/wiki/Army_of_the_Potomac"}]}, {"year": "1879", "text": "<PERSON>'s Garden in New York City is renamed Madison Square Garden by <PERSON> and is opened to the public at 26th Street and Madison Avenue.", "html": "1879 - <PERSON>'s Garden in New York City is renamed <a href=\"https://wikipedia.org/wiki/Madison_Square_Garden_(1879)\" title=\"Madison Square Garden (1879)\">Madison Square Garden</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and is opened to the public at 26th Street and <a href=\"https://wikipedia.org/wiki/Madison_Avenue\" title=\"Madison Avenue\">Madison Avenue</a>.", "no_year_html": "<PERSON>'s Garden in New York City is renamed <a href=\"https://wikipedia.org/wiki/Madison_Square_Garden_(1879)\" title=\"Madison Square Garden (1879)\">Madison Square Garden</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and is opened to the public at 26th Street and <a href=\"https://wikipedia.org/wiki/Madison_Avenue\" title=\"Madison Avenue\">Madison Avenue</a>.", "links": [{"title": "Madison Square Garden (1879)", "link": "https://wikipedia.org/wiki/Madison_Square_Garden_(1879)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Madison Avenue", "link": "https://wikipedia.org/wiki/Madison_Avenue"}]}, {"year": "1884", "text": "The arrival at Plymouth of Tāwhiao, King of Maoris, to claim the protection of Queen <PERSON>.", "html": "1884 - The arrival at <a href=\"https://wikipedia.org/wiki/Plymouth\" title=\"Plymouth\">Plymouth</a> of <a href=\"https://wikipedia.org/wiki/T%C4%81whiao\" title=\"Tāwhiao\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, King of <a href=\"https://wikipedia.org/wiki/M%C4%81ori_people\" title=\"Māori people\"><PERSON><PERSON></a>, to claim the protection of <a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen Victoria\">Queen <PERSON></a>.", "no_year_html": "The arrival at <a href=\"https://wikipedia.org/wiki/Plymouth\" title=\"Plymouth\">Plymouth</a> of <a href=\"https://wikipedia.org/wiki/T%C4%81whiao\" title=\"Tāwhiao\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, King of <a href=\"https://wikipedia.org/wiki/M%C4%81ori_people\" title=\"Māori people\"><PERSON><PERSON></a>, to claim the protection of <a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen <PERSON>\">Queen <PERSON></a>.", "links": [{"title": "Plymouth", "link": "https://wikipedia.org/wiki/Plymouth"}, {"title": "Tāwhia<PERSON>", "link": "https://wikipedia.org/wiki/T%C4%81whiao"}, {"title": "Māori people", "link": "https://wikipedia.org/wiki/M%C4%81ori_people"}, {"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}]}, {"year": "1889", "text": "Johnstown Flood: Over 2,200 people die after a dam fails and sends a 60-foot (18-meter) wall of water over the town of Johnstown, Pennsylvania.", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Johnstown_Flood\" title=\"Johnstown Flood\">Johnstown Flood</a>: Over 2,200 people die after a dam fails and sends a 60-foot (18-meter) wall of water over the town of <a href=\"https://wikipedia.org/wiki/Johnstown,_Pennsylvania\" title=\"Johnstown, Pennsylvania\">Johnstown, Pennsylvania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Johnstown_Flood\" title=\"Johnstown Flood\">Johnstown Flood</a>: Over 2,200 people die after a dam fails and sends a 60-foot (18-meter) wall of water over the town of <a href=\"https://wikipedia.org/wiki/Johnstown,_Pennsylvania\" title=\"Johnstown, Pennsylvania\">Johnstown, Pennsylvania</a>.", "links": [{"title": "Johnstown Flood", "link": "https://wikipedia.org/wiki/Johnstown_Flood"}, {"title": "Johnstown, Pennsylvania", "link": "https://wikipedia.org/wiki/Johnstown,_Pennsylvania"}]}, {"year": "1902", "text": "Second Boer War: The Treaty of Vereeniging ends the war and ensures British control of South Africa.", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: The <a href=\"https://wikipedia.org/wiki/Treaty_of_Vereeniging\" title=\"Treaty of Vereeniging\">Treaty of Vereeniging</a> ends the war and ensures British control of South Africa.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: The <a href=\"https://wikipedia.org/wiki/Treaty_of_Vereeniging\" title=\"Treaty of Vereeniging\">Treaty of Vereeniging</a> ends the war and ensures British control of South Africa.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Treaty of Vereeniging", "link": "https://wikipedia.org/wiki/Treaty_of_Vereeniging"}]}, {"year": "1906", "text": "The attempted regicide of Spanish King <PERSON> and Queen <PERSON> on their wedding day instead kills 24", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Morral_affair\" title=\"Morral affair\">The attempted regicide</a> of Spanish King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alfonso XIII\"><PERSON></a> and Queen <a href=\"https://wikipedia.org/wiki/Victoria_Eugenie\" class=\"mw-redirect\" title=\"Victoria Eugenie\"><PERSON> Eugenie</a> on <a href=\"https://wikipedia.org/wiki/Wedding_of_King_<PERSON>_<PERSON>_and_Princess_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Wedding of King <PERSON> and Princess <PERSON>\">their wedding</a> day instead kills 24", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morral_affair\" title=\"Morral affair\">The attempted regicide</a> of Spanish King <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> XIII\"><PERSON></a> and Queen <a href=\"https://wikipedia.org/wiki/Victoria_Eugenie\" class=\"mw-redirect\" title=\"Victoria Eugenie\"><PERSON> Eugenie</a> on <a href=\"https://wikipedia.org/wiki/Wedding_of_King_<PERSON>_<PERSON>_and_Princess_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Wedding of King <PERSON> and Princess <PERSON>\">their wedding</a> day instead kills 24", "links": [{"title": "Morral affair", "link": "https://wikipedia.org/wiki/Morral_affair"}, {"title": "Alfonso XIII", "link": "https://wikipedia.org/wiki/Alfonso_XIII"}, {"title": "Victoria Eugenie", "link": "https://wikipedia.org/wiki/Victoria_E<PERSON>ie"}, {"title": "Wedding of King <PERSON> and Princess <PERSON>", "link": "https://wikipedia.org/wiki/Wedding_of_King_<PERSON>_<PERSON>_and_Princess_<PERSON>_<PERSON><PERSON>ie"}]}, {"year": "1909", "text": "The National Negro Committee, forerunner to the National Association for the Advancement of Colored People (NAACP), convenes for the first time.", "html": "1909 - The <a href=\"https://wikipedia.org/wiki/National_Negro_Committee\" title=\"National Negro Committee\">National Negro Committee</a>, forerunner to the <a href=\"https://wikipedia.org/wiki/National_Association_for_the_Advancement_of_Colored_People\" class=\"mw-redirect\" title=\"National Association for the Advancement of Colored People\">National Association for the Advancement of Colored People</a> (NAACP), convenes for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Negro_Committee\" title=\"National Negro Committee\">National Negro Committee</a>, forerunner to the <a href=\"https://wikipedia.org/wiki/National_Association_for_the_Advancement_of_Colored_People\" class=\"mw-redirect\" title=\"National Association for the Advancement of Colored People\">National Association for the Advancement of Colored People</a> (NAACP), convenes for the first time.", "links": [{"title": "National Negro Committee", "link": "https://wikipedia.org/wiki/National_Negro_Committee"}, {"title": "National Association for the Advancement of Colored People", "link": "https://wikipedia.org/wiki/National_Association_for_the_Advancement_of_Colored_People"}]}, {"year": "1910", "text": "The South Africa Act comes into force, establishing the Union of South Africa.", "html": "1910 - The <a href=\"https://wikipedia.org/wiki/South_Africa_Act_1909\" title=\"South Africa Act 1909\">South Africa Act</a> comes into force, establishing the <a href=\"https://wikipedia.org/wiki/Union_of_South_Africa\" title=\"Union of South Africa\">Union of South Africa</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/South_Africa_Act_1909\" title=\"South Africa Act 1909\">South Africa Act</a> comes into force, establishing the <a href=\"https://wikipedia.org/wiki/Union_of_South_Africa\" title=\"Union of South Africa\">Union of South Africa</a>.", "links": [{"title": "South Africa Act 1909", "link": "https://wikipedia.org/wiki/South_Africa_Act_1909"}, {"title": "Union of South Africa", "link": "https://wikipedia.org/wiki/Union_of_South_Africa"}]}, {"year": "1911", "text": "The RMS Titanic is launched in Belfast, Northern Ireland.", "html": "1911 - The <a href=\"https://wikipedia.org/wiki/RMS_Titanic\" class=\"mw-redirect\" title=\"RMS Titanic\">RMS <i>Titanic</i></a> is launched in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/RMS_Titanic\" class=\"mw-redirect\" title=\"RMS Titanic\">RMS <i>Titanic</i></a> is launched in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "links": [{"title": "RMS Titanic", "link": "https://wikipedia.org/wiki/RMS_Titanic"}, {"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "1911", "text": "The President of Mexico <PERSON><PERSON><PERSON><PERSON> flees the country during the Mexican Revolution.", "html": "1911 - The <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> <a href=\"https://wikipedia.org/wiki/Porfirio_D%C3%ADaz\" title=\"Porfiri<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> flees the country during the <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> <a href=\"https://wikipedia.org/wiki/Porfirio_D%C3%ADaz\" title=\"Po<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> flees the country during the <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>.", "links": [{"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Porfirio_D%C3%ADaz"}, {"title": "Mexican Revolution", "link": "https://wikipedia.org/wiki/Mexican_Revolution"}]}, {"year": "1916", "text": "World War I: Battle of Jutland: The British Grand Fleet engages the High Seas Fleet in the largest naval battle of the war, which proves indecisive.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Jutland\" title=\"Battle of Jutland\">Battle of Jutland</a>: The British <a href=\"https://wikipedia.org/wiki/Grand_Fleet\" title=\"Grand Fleet\">Grand Fleet</a> engages the <a href=\"https://wikipedia.org/wiki/High_Seas_Fleet\" title=\"High Seas Fleet\">High Seas Fleet</a> in the largest naval battle of the war, which proves indecisive.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Jutland\" title=\"Battle of Jutland\">Battle of Jutland</a>: The British <a href=\"https://wikipedia.org/wiki/Grand_Fleet\" title=\"Grand Fleet\">Grand Fleet</a> engages the <a href=\"https://wikipedia.org/wiki/High_Seas_Fleet\" title=\"High Seas Fleet\">High Seas Fleet</a> in the largest naval battle of the war, which proves indecisive.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Jutland", "link": "https://wikipedia.org/wiki/Battle_of_Jutland"}, {"title": "Grand Fleet", "link": "https://wikipedia.org/wiki/Grand_Fleet"}, {"title": "High Seas Fleet", "link": "https://wikipedia.org/wiki/High_Seas_Fleet"}]}, {"year": "1921", "text": "The Tulsa race massacre kills at least 39, but other estimates of black fatalities vary from 55 to about 300.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Tulsa_race_massacre\" title=\"Tulsa race massacre\">Tulsa race massacre</a> kills at least 39, but other estimates of black fatalities vary from 55 to about 300.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tulsa_race_massacre\" title=\"Tulsa race massacre\">Tulsa race massacre</a> kills at least 39, but other estimates of black fatalities vary from 55 to about 300.", "links": [{"title": "Tulsa race massacre", "link": "https://wikipedia.org/wiki/Tulsa_race_massacre"}]}, {"year": "1924", "text": "Hope Development School fire kills 24 people, mostly disabled children.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Hope_Development_School_fire\" title=\"Hope Development School fire\">Hope Development School fire</a> kills 24 people, mostly disabled children.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hope_Development_School_fire\" title=\"Hope Development School fire\">Hope Development School fire</a> kills 24 people, mostly disabled children.", "links": [{"title": "Hope Development School fire", "link": "https://wikipedia.org/wiki/Hope_Development_School_fire"}]}, {"year": "1935", "text": "A 7.7 Mw  earthquake destroys Quetta in modern-day Pakistan killing 40,000.", "html": "1935 - A 7.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1935_Quetta_earthquake\" title=\"1935 Quetta earthquake\">earthquake</a> destroys <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta</a> in modern-day <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> killing 40,000.", "no_year_html": "A 7.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1935_Quetta_earthquake\" title=\"1935 Quetta earthquake\">earthquake</a> destroys <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta</a> in modern-day <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> killing 40,000.", "links": [{"title": "1935 Quetta earthquake", "link": "https://wikipedia.org/wiki/1935_Quetta_earthquake"}, {"title": "Quetta", "link": "https://wikipedia.org/wiki/Quetta"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "1941", "text": "Anglo-Iraqi War: The United Kingdom completes the re-occupation of Iraq and returns '<PERSON> to power as regent for Faisal II.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Anglo-Iraqi_War\" title=\"Anglo-Iraqi War\">Anglo-Iraqi War</a>: The United Kingdom completes the re-occupation of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> and returns <a href=\"https://wikipedia.org/wiki/%27Abd_al-Ilah\" class=\"mw-redirect\" title=\"'<PERSON>\">'<PERSON></a> to power as regent for <a href=\"https://wikipedia.org/wiki/Faisal_II_of_Iraq\" class=\"mw-redirect\" title=\"Faisal II of Iraq\">Faisal II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Iraqi_War\" title=\"Anglo-Iraqi War\">Anglo-Iraqi War</a>: The United Kingdom completes the re-occupation of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> and returns <a href=\"https://wikipedia.org/wiki/%27Abd_al-Ilah\" class=\"mw-redirect\" title=\"'<PERSON>\">'<PERSON></a> to power as regent for <a href=\"https://wikipedia.org/wiki/Faisal_II_of_Iraq\" class=\"mw-redirect\" title=\"Faisal II of Iraq\">Faisal II</a>.", "links": [{"title": "Anglo-Iraqi War", "link": "https://wikipedia.org/wiki/Anglo-Iraqi_War"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%27Abd_al-<PERSON>"}, {"title": "Faisal II of Iraq", "link": "https://wikipedia.org/wiki/Faisal_II_of_Iraq"}]}, {"year": "1942", "text": "World War II: Imperial Japanese Navy midget submarines begin a series of attacks on Sydney, Australia.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> <a href=\"https://wikipedia.org/wiki/Midget_submarine\" title=\"Midget submarine\">midget submarines</a> begin a series of <a href=\"https://wikipedia.org/wiki/Attack_on_Sydney_Harbour\" title=\"Attack on Sydney Harbour\">attacks on Sydney</a>, Australia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> <a href=\"https://wikipedia.org/wiki/Midget_submarine\" title=\"Midget submarine\">midget submarines</a> begin a series of <a href=\"https://wikipedia.org/wiki/Attack_on_Sydney_Harbour\" title=\"Attack on Sydney Harbour\">attacks on Sydney</a>, Australia.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Imperial Japanese Navy", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Navy"}, {"title": "Midget submarine", "link": "https://wikipedia.org/wiki/Midget_submarine"}, {"title": "Attack on Sydney Harbour", "link": "https://wikipedia.org/wiki/Attack_on_Sydney_Harbour"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, the democratically elected Prime Minister of Hungary, resigns from office after blackmail from the Hungarian Communist Party accusing him of being part of a plot against the state. This grants the Communists effective control of the Hungarian government.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the democratically elected <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a>, resigns from office after blackmail from the <a href=\"https://wikipedia.org/wiki/Hungarian_Communist_Party\" title=\"Hungarian Communist Party\">Hungarian Communist Party</a> accusing him of being part of a plot against the state. This grants the Communists effective control of the Hungarian government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the democratically elected <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a>, resigns from office after blackmail from the <a href=\"https://wikipedia.org/wiki/Hungarian_Communist_Party\" title=\"Hungarian Communist Party\">Hungarian Communist Party</a> accusing him of being part of a plot against the state. This grants the Communists effective control of the Hungarian government.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>renc_<PERSON>gy"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}, {"title": "Hungarian Communist Party", "link": "https://wikipedia.org/wiki/Hungarian_Communist_Party"}]}, {"year": "1951", "text": "The Uniform Code of Military Justice takes effect as the legal system of the United States Armed Forces.", "html": "1951 - The <a href=\"https://wikipedia.org/wiki/Uniform_Code_of_Military_Justice\" title=\"Uniform Code of Military Justice\">Uniform Code of Military Justice</a> takes effect as the legal system of the <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">United States Armed Forces</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Uniform_Code_of_Military_Justice\" title=\"Uniform Code of Military Justice\">Uniform Code of Military Justice</a> takes effect as the legal system of the <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">United States Armed Forces</a>.", "links": [{"title": "Uniform Code of Military Justice", "link": "https://wikipedia.org/wiki/Uniform_Code_of_Military_Justice"}, {"title": "United States Armed Forces", "link": "https://wikipedia.org/wiki/United_States_Armed_Forces"}]}, {"year": "1955", "text": "The U.S. Supreme Court expands on its Brown v. Board of Education decision by ordering district courts and school districts to enforce educational desegregation \"at all deliberate speed.\"", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> expands on its <a href=\"https://wikipedia.org/wiki/Brown_II\" class=\"mw-redirect\" title=\"Brown II\"><i><PERSON> v. Board of Education</i></a> decision by ordering <a href=\"https://wikipedia.org/wiki/United_States_district_court\" title=\"United States district court\">district courts</a> and <a href=\"https://wikipedia.org/wiki/School_district\" title=\"School district\">school districts</a> to enforce <a href=\"https://wikipedia.org/wiki/School_integration_in_the_United_States\" title=\"School integration in the United States\">educational desegregation</a> \"at all deliberate speed.\"", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> expands on its <a href=\"https://wikipedia.org/wiki/Brown_II\" class=\"mw-redirect\" title=\"Brown II\"><i>Brown v. Board of Education</i></a> decision by ordering <a href=\"https://wikipedia.org/wiki/United_States_district_court\" title=\"United States district court\">district courts</a> and <a href=\"https://wikipedia.org/wiki/School_district\" title=\"School district\">school districts</a> to enforce <a href=\"https://wikipedia.org/wiki/School_integration_in_the_United_States\" title=\"School integration in the United States\">educational desegregation</a> \"at all deliberate speed.\"", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Brown II", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "United States district court", "link": "https://wikipedia.org/wiki/United_States_district_court"}, {"title": "School district", "link": "https://wikipedia.org/wiki/School_district"}, {"title": "School integration in the United States", "link": "https://wikipedia.org/wiki/School_integration_in_the_United_States"}]}, {"year": "1961", "text": "The South African Constitution of 1961 becomes effective, thus creating the Republic of South Africa, which remains outside the Commonwealth of Nations until 1 June 1994, when South Africa is returned to Commonwealth membership.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/South_African_Constitution_of_1961\" title=\"South African Constitution of 1961\">South African Constitution of 1961</a> becomes effective, thus creating the <a href=\"https://wikipedia.org/wiki/Republic_of_South_Africa\" class=\"mw-redirect\" title=\"Republic of South Africa\">Republic of South Africa</a>, which remains outside the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a> until 1 June 1994, when South Africa is returned to Commonwealth membership.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/South_African_Constitution_of_1961\" title=\"South African Constitution of 1961\">South African Constitution of 1961</a> becomes effective, thus creating the <a href=\"https://wikipedia.org/wiki/Republic_of_South_Africa\" class=\"mw-redirect\" title=\"Republic of South Africa\">Republic of South Africa</a>, which remains outside the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a> until 1 June 1994, when South Africa is returned to Commonwealth membership.", "links": [{"title": "South African Constitution of 1961", "link": "https://wikipedia.org/wiki/South_African_Constitution_of_1961"}, {"title": "Republic of South Africa", "link": "https://wikipedia.org/wiki/Republic_of_South_Africa"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}]}, {"year": "1961", "text": "In Moscow City Court, the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> show trial begins, despite the Khrushchev Thaw to reverse Stalinist elements in Soviet society.", "html": "1961 - In <a href=\"https://wikipedia.org/wiki/Moscow_City_Court\" title=\"Moscow City Court\">Moscow City Court</a>, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%E2%80%93<PERSON><PERSON><PERSON><PERSON>_case\" title=\"<PERSON><PERSON><PERSON><PERSON> case\"><PERSON><PERSON><PERSON>-<PERSON></a> <a href=\"https://wikipedia.org/wiki/Show_trial\" title=\"Show trial\">show trial</a> begins, despite the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Thaw\" title=\"Khrushchev Thaw\"><PERSON><PERSON><PERSON><PERSON> Thaw</a> to <a href=\"https://wikipedia.org/wiki/De-Stalinization\" title=\"De-Stalinization\">reverse Stalinist elements in Soviet society</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Moscow_City_Court\" title=\"Moscow City Court\">Moscow City Court</a>, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%E2%80%93<PERSON><PERSON><PERSON><PERSON>_case\" title=\"<PERSON><PERSON><PERSON><PERSON> case\"><PERSON><PERSON><PERSON>-<PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Show_trial\" title=\"Show trial\">show trial</a> begins, despite the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Thaw\" title=\"Khr<PERSON>chev Thaw\"><PERSON><PERSON><PERSON><PERSON> Thaw</a> to <a href=\"https://wikipedia.org/wiki/De-Stalinization\" title=\"De-Stalinization\">reverse Stalinist elements in Soviet society</a>.", "links": [{"title": "Moscow City Court", "link": "https://wikipedia.org/wiki/Moscow_City_Court"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> case", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%E2%80%93<PERSON><PERSON><PERSON><PERSON>_case"}, {"title": "Show trial", "link": "https://wikipedia.org/wiki/Show_trial"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>haw"}, {"title": "De-Stalinization", "link": "https://wikipedia.org/wiki/De-Stalinization"}]}, {"year": "1962", "text": "The West Indies Federation dissolves.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/West_Indies_Federation\" title=\"West Indies Federation\">West Indies Federation</a> dissolves.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/West_Indies_Federation\" title=\"West Indies Federation\">West Indies Federation</a> dissolves.", "links": [{"title": "West Indies Federation", "link": "https://wikipedia.org/wiki/West_Indies_Federation"}]}, {"year": "1970", "text": "The 7.9 Mw  Ancash earthquake shakes Peru with a maximum Mercalli intensity of VIII (Severe) and a landslide buries the town of Yungay, Peru. Between 66,794 and 70,000 were killed and 50,000 were injured.", "html": "1970 - The 7.9 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1970_Ancash_earthquake\" title=\"1970 Ancash earthquake\">Ancash earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>) and a <a href=\"https://wikipedia.org/wiki/Landslide\" title=\"Landslide\">landslide</a> buries the town of <a href=\"https://wikipedia.org/wiki/Yungay,_Peru\" title=\"Yungay, Peru\">Yungay, Peru</a>. Between 66,794 and 70,000 were killed and 50,000 were injured.", "no_year_html": "The 7.9 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1970_Ancash_earthquake\" title=\"1970 Ancash earthquake\">Ancash earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>) and a <a href=\"https://wikipedia.org/wiki/Landslide\" title=\"Landslide\">landslide</a> buries the town of <a href=\"https://wikipedia.org/wiki/Yungay,_Peru\" title=\"Yungay, Peru\">Yungay, Peru</a>. Between 66,794 and 70,000 were killed and 50,000 were injured.", "links": [{"title": "1970 Ancash earthquake", "link": "https://wikipedia.org/wiki/1970_Ancash_earthquake"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}, {"title": "Landslide", "link": "https://wikipedia.org/wiki/Landslide"}, {"title": "Yungay, Peru", "link": "https://wikipedia.org/wiki/Yungay,_Peru"}]}, {"year": "1971", "text": "In accordance with the Uniform Monday Holiday Act passed by the U.S. Congress in 1968, observation of Memorial Day occurs on the last Monday in May for the first time, rather than on the traditional Memorial Day of May 30.", "html": "1971 - In accordance with the <a href=\"https://wikipedia.org/wiki/Uniform_Monday_Holiday_Act\" title=\"Uniform Monday Holiday Act\">Uniform Monday Holiday Act</a> passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> in <a href=\"https://wikipedia.org/wiki/1968\" title=\"1968\">1968</a>, observation of <a href=\"https://wikipedia.org/wiki/Memorial_Day\" title=\"Memorial Day\">Memorial Day</a> occurs on the last Monday in May for the first time, rather than on the traditional <a href=\"https://wikipedia.org/wiki/Memorial_Day\" title=\"Memorial Day\">Memorial Day</a> of <a href=\"https://wikipedia.org/wiki/May_30\" title=\"May 30\">May 30</a>.", "no_year_html": "In accordance with the <a href=\"https://wikipedia.org/wiki/Uniform_Monday_Holiday_Act\" title=\"Uniform Monday Holiday Act\">Uniform Monday Holiday Act</a> passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> in <a href=\"https://wikipedia.org/wiki/1968\" title=\"1968\">1968</a>, observation of <a href=\"https://wikipedia.org/wiki/Memorial_Day\" title=\"Memorial Day\">Memorial Day</a> occurs on the last Monday in May for the first time, rather than on the traditional <a href=\"https://wikipedia.org/wiki/Memorial_Day\" title=\"Memorial Day\">Memorial Day</a> of <a href=\"https://wikipedia.org/wiki/May_30\" title=\"May 30\">May 30</a>.", "links": [{"title": "Uniform Monday Holiday Act", "link": "https://wikipedia.org/wiki/Uniform_Monday_Holiday_Act"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "1968", "link": "https://wikipedia.org/wiki/1968"}, {"title": "Memorial Day", "link": "https://wikipedia.org/wiki/Memorial_Day"}, {"title": "Memorial Day", "link": "https://wikipedia.org/wiki/Memorial_Day"}, {"title": "May 30", "link": "https://wikipedia.org/wiki/May_30"}]}, {"year": "1973", "text": "The United States Senate votes to cut off funding for the bombing of Khmer Rouge targets within Cambodia, hastening the end of the Cambodian Civil War.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> votes to cut off funding for the bombing of <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> targets within <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>, hastening the end of the <a href=\"https://wikipedia.org/wiki/Cambodian_Civil_War\" title=\"Cambodian Civil War\">Cambodian Civil War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> votes to cut off funding for the bombing of <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> targets within <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>, hastening the end of the <a href=\"https://wikipedia.org/wiki/Cambodian_Civil_War\" title=\"Cambodian Civil War\">Cambodian Civil War</a>.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "Khmer Rouge", "link": "https://wikipedia.org/wiki/Khmer_Rouge"}, {"title": "Cambodia", "link": "https://wikipedia.org/wiki/Cambodia"}, {"title": "Cambodian Civil War", "link": "https://wikipedia.org/wiki/Cambodian_Civil_War"}]}, {"year": "1973", "text": "Indian Airlines Flight 440 crashes near Palam Airport in Delhi, killing 48.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Indian_Airlines_Flight_440\" title=\"Indian Airlines Flight 440\">Indian Airlines Flight 440</a> crashes near <a href=\"https://wikipedia.org/wiki/Indira_Gandhi_International_Airport\" title=\"Indira Gandhi International Airport\">Palam Airport</a> in <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a>, killing 48.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indian_Airlines_Flight_440\" title=\"Indian Airlines Flight 440\">Indian Airlines Flight 440</a> crashes near <a href=\"https://wikipedia.org/wiki/Indira_Gandhi_International_Airport\" title=\"Indira Gandhi International Airport\">Palam Airport</a> in <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a>, killing 48.", "links": [{"title": "Indian Airlines Flight 440", "link": "https://wikipedia.org/wiki/Indian_Airlines_Flight_440"}, {"title": "Indira Gandhi International Airport", "link": "https://wikipedia.org/wiki/Indira_Gandhi_International_Airport"}, {"title": "Delhi", "link": "https://wikipedia.org/wiki/Delhi"}]}, {"year": "1977", "text": "The Trans-Alaska Pipeline System is completed.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/Trans-Alaska_Pipeline_System\" title=\"Trans-Alaska Pipeline System\">Trans-Alaska Pipeline System</a> is completed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Trans-Alaska_Pipeline_System\" title=\"Trans-Alaska Pipeline System\">Trans-Alaska Pipeline System</a> is completed.", "links": [{"title": "Trans-Alaska Pipeline System", "link": "https://wikipedia.org/wiki/Trans-Alaska_Pipeline_System"}]}, {"year": "1985", "text": "United States-Canada tornado outbreak: Forty-one tornadoes hit Ohio, Pennsylvania, New York, and Ontario, leaving 76 dead.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/1985_United_States%E2%80%93Canada_tornado_outbreak\" title=\"1985 United States-Canada tornado outbreak\">United States-Canada tornado outbreak</a>: Forty-one tornadoes hit <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a>, <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a>, <a href=\"https://wikipedia.org/wiki/New_York_(state)\" title=\"New York (state)\">New York</a>, and <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a>, leaving 76 dead.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1985_United_States%E2%80%93Canada_tornado_outbreak\" title=\"1985 United States-Canada tornado outbreak\">United States-Canada tornado outbreak</a>: Forty-one tornadoes hit <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a>, <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a>, <a href=\"https://wikipedia.org/wiki/New_York_(state)\" title=\"New York (state)\">New York</a>, and <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a>, leaving 76 dead.", "links": [{"title": "1985 United States-Canada tornado outbreak", "link": "https://wikipedia.org/wiki/1985_United_States%E2%80%93Canada_tornado_outbreak"}, {"title": "Ohio", "link": "https://wikipedia.org/wiki/Ohio"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}, {"title": "New York (state)", "link": "https://wikipedia.org/wiki/New_York_(state)"}, {"title": "Ontario", "link": "https://wikipedia.org/wiki/Ontario"}]}, {"year": "1991", "text": "Bicesse Accords in Angola lay out a transition to multi-party democracy under the supervision of the United Nations' UNAVEM II peacekeeping mission.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Bicesse_Accords\" title=\"Bicesse Accords\">Bicesse Accords</a> in <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a> lay out a transition to multi-party <a href=\"https://wikipedia.org/wiki/Democracy\" title=\"Democracy\">democracy</a> under the supervision of the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>' <a href=\"https://wikipedia.org/wiki/United_Nations_Angola_Verification_Mission_II\" title=\"United Nations Angola Verification Mission II\">UNAVEM II</a> peacekeeping mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bicesse_Accords\" title=\"Bicesse Accords\">Bicesse Accords</a> in <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a> lay out a transition to multi-party <a href=\"https://wikipedia.org/wiki/Democracy\" title=\"Democracy\">democracy</a> under the supervision of the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>' <a href=\"https://wikipedia.org/wiki/United_Nations_Angola_Verification_Mission_II\" title=\"United Nations Angola Verification Mission II\">UNAVEM II</a> peacekeeping mission.", "links": [{"title": "Bicesse Accords", "link": "https://wikipedia.org/wiki/Bicesse_Accords"}, {"title": "Angola", "link": "https://wikipedia.org/wiki/Angola"}, {"title": "Democracy", "link": "https://wikipedia.org/wiki/Democracy"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "United Nations Angola Verification Mission II", "link": "https://wikipedia.org/wiki/United_Nations_Angola_Verification_Mission_II"}]}, {"year": "2003", "text": "Air France retires its fleet of Concorde aircraft.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Air_France\" title=\"Air France\">Air France</a> retires its fleet of <a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> aircraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_France\" title=\"Air France\">Air France</a> retires its fleet of <a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> aircraft.", "links": [{"title": "Air France", "link": "https://wikipedia.org/wiki/Air_France"}, {"title": "Concorde", "link": "https://wikipedia.org/wiki/Concorde"}]}, {"year": "2005", "text": "Vanity Fair reveals that <PERSON> was \"Deep Throat\".", "html": "2005 - <i><a href=\"https://wikipedia.org/wiki/Vanity_Fair_(magazine)\" title=\"Vanity Fair (magazine)\">Vanity Fair</a></i> reveals that <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> was \"<a href=\"https://wikipedia.org/wiki/Deep_Throat_(Watergate)\" title=\"Deep Throat (Watergate)\">Deep Throat</a>\".", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Vanity_Fair_(magazine)\" title=\"Vanity Fair (magazine)\">Vanity Fair</a></i> reveals that <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> was \"<a href=\"https://wikipedia.org/wiki/Deep_Throat_(Watergate)\" title=\"Deep Throat (Watergate)\">Deep Throat</a>\".", "links": [{"title": "Vanity Fair (magazine)", "link": "https://wikipedia.org/wiki/Vanity_Fair_(magazine)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deep Throat (Watergate)", "link": "https://wikipedia.org/wiki/Deep_Throat_(Watergate)"}]}, {"year": "2008", "text": "<PERSON><PERSON> breaks the world record in the 100m sprint, with a wind-legal (+1.7 m/s) 9.72 seconds", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Usain_Bolt\" title=\"Usain Bolt\"><PERSON><PERSON></a> breaks the world record in the 100m sprint, with a wind-legal (+1.7 m/s) 9.72 seconds", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ain_Bolt\" title=\"Usain Bolt\"><PERSON><PERSON></a> breaks the world record in the 100m sprint, with a wind-legal (+1.7 m/s) 9.72 seconds", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Usain_Bolt"}]}, {"year": "2010", "text": "Israeli Shayetet 13 commandos boarded the Gaza Freedom Flotilla while still in international waters trying to break the ongoing blockade of the Gaza Strip; nine Turkish citizens on the flotilla were killed in the ensuing violent affray.", "html": "2010 - Israeli <a href=\"https://wikipedia.org/wiki/Shayetet_13\" title=\"Shayetet 13\">Shayetet 13</a> commandos <a href=\"https://wikipedia.org/wiki/Gaza_flotilla_raid\" title=\"Gaza flotilla raid\">boarded</a> the <a href=\"https://wikipedia.org/wiki/Gaza_Freedom_Flotilla\" title=\"Gaza Freedom Flotilla\">Gaza Freedom Flotilla</a> while still in international waters trying to break the <a href=\"https://wikipedia.org/wiki/Blockade_of_the_Gaza_Strip\" title=\"Blockade of the Gaza Strip\">ongoing blockade</a> of the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a>; nine Turkish citizens on the flotilla were killed in the ensuing violent affray.", "no_year_html": "Israeli <a href=\"https://wikipedia.org/wiki/Shayetet_13\" title=\"Shayetet 13\">Shayetet 13</a> commandos <a href=\"https://wikipedia.org/wiki/Gaza_flotilla_raid\" title=\"Gaza flotilla raid\">boarded</a> the <a href=\"https://wikipedia.org/wiki/Gaza_Freedom_Flotilla\" title=\"Gaza Freedom Flotilla\">Gaza Freedom Flotilla</a> while still in international waters trying to break the <a href=\"https://wikipedia.org/wiki/Blockade_of_the_Gaza_Strip\" title=\"Blockade of the Gaza Strip\">ongoing blockade</a> of the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a>; nine Turkish citizens on the flotilla were killed in the ensuing violent affray.", "links": [{"title": "Shayetet 13", "link": "https://wikipedia.org/wiki/Shayetet_13"}, {"title": "Gaza flotilla raid", "link": "https://wikipedia.org/wiki/Gaza_flotilla_raid"}, {"title": "Gaza Freedom Flotilla", "link": "https://wikipedia.org/wiki/Gaza_Freedom_Flotilla"}, {"title": "Blockade of the Gaza Strip", "link": "https://wikipedia.org/wiki/Blockade_of_the_Gaza_Strip"}, {"title": "Gaza Strip", "link": "https://wikipedia.org/wiki/Gaza_Strip"}]}, {"year": "2013", "text": "The asteroid 1998 QE<PERSON> and its moon make their closest approach to Earth for the next two centuries.", "html": "2013 - The <a href=\"https://wikipedia.org/wiki/Asteroid\" title=\"Asteroid\">asteroid</a> <a href=\"https://wikipedia.org/wiki/(285263)_1998_QE2\" title=\"(285263) 1998 QE2\">1998 QE2</a> and its <a href=\"https://wikipedia.org/wiki/Natural_satellite\" title=\"Natural satellite\">moon</a> make their <a href=\"https://wikipedia.org/wiki/List_of_asteroid_close_approaches_to_Earth\" title=\"List of asteroid close approaches to Earth\">closest approach to Earth</a> for the next two centuries.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Asteroid\" title=\"Asteroid\">asteroid</a> <a href=\"https://wikipedia.org/wiki/(285263)_1998_QE2\" title=\"(285263) 1998 QE2\">1998 QE2</a> and its <a href=\"https://wikipedia.org/wiki/Natural_satellite\" title=\"Natural satellite\">moon</a> make their <a href=\"https://wikipedia.org/wiki/List_of_asteroid_close_approaches_to_Earth\" title=\"List of asteroid close approaches to Earth\">closest approach to Earth</a> for the next two centuries.", "links": [{"title": "Asteroid", "link": "https://wikipedia.org/wiki/Asteroid"}, {"title": "(285263) 1998 QE2", "link": "https://wikipedia.org/wiki/(285263)_1998_QE2"}, {"title": "Natural satellite", "link": "https://wikipedia.org/wiki/Natural_satellite"}, {"title": "List of asteroid close approaches to Earth", "link": "https://wikipedia.org/wiki/List_of_asteroid_close_approaches_to_Earth"}]}, {"year": "2013", "text": "A record breaking 2.6 mile wide tornado strikes near El Reno, Oklahoma, United States, causing eight fatalities (including three storm chasers) and over 150 injuries.", "html": "2013 - A record breaking <a href=\"https://wikipedia.org/wiki/2013_El_Reno_tornado\" title=\"2013 El Reno tornado\">2.6 mile wide tornado</a> strikes near <a href=\"https://wikipedia.org/wiki/El_Reno,_Oklahoma\" title=\"El Reno, Oklahoma\">El Reno, Oklahoma</a>, United States, causing eight fatalities (including three storm chasers) and over 150 injuries.", "no_year_html": "A record breaking <a href=\"https://wikipedia.org/wiki/2013_El_Reno_tornado\" title=\"2013 El Reno tornado\">2.6 mile wide tornado</a> strikes near <a href=\"https://wikipedia.org/wiki/El_Reno,_Oklahoma\" title=\"El Reno, Oklahoma\">El Reno, Oklahoma</a>, United States, causing eight fatalities (including three storm chasers) and over 150 injuries.", "links": [{"title": "2013 El Reno tornado", "link": "https://wikipedia.org/wiki/2013_El_Reno_tornado"}, {"title": "El Reno, Oklahoma", "link": "https://wikipedia.org/wiki/El_Reno,_Oklahoma"}]}, {"year": "2016", "text": "Syrian civil war: The Syrian Democratic Forces (SDF) launch the Manbij offensive, in order to capture the city of Manbij from the Islamic State of Iraq and the Levant (ISIL).", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) launch the <a href=\"https://wikipedia.org/wiki/Manbij_offensive_(2016)\" title=\"Manbij offensive (2016)\">Manbij offensive</a>, in order to capture the city of <a href=\"https://wikipedia.org/wiki/Manbij\" title=\"Manbij\">Manbij</a> from the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> (ISIL).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) launch the <a href=\"https://wikipedia.org/wiki/Manbij_offensive_(2016)\" title=\"Manbij offensive (2016)\">Manbij offensive</a>, in order to capture the city of <a href=\"https://wikipedia.org/wiki/Manbij\" title=\"Manbij\">Manbij</a> from the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> (ISIL).", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Syrian Democratic Forces", "link": "https://wikipedia.org/wiki/Syrian_Democratic_Forces"}, {"title": "Manbij offensive (2016)", "link": "https://wikipedia.org/wiki/Manbij_offensive_(2016)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manbij"}, {"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}]}, {"year": "2017", "text": "A car bomb explodes in a crowded intersection in Kabul near the German embassy during rush hour, killing over 90 and injuring 463.", "html": "2017 - A <a href=\"https://wikipedia.org/wiki/May_2017_Kabul_attack\" class=\"mw-redirect\" title=\"May 2017 Kabul attack\">car bomb explodes</a> in a crowded intersection in <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a> near the <a href=\"https://wikipedia.org/wiki/Embassy_of_Germany,_Kabul\" title=\"Embassy of Germany, Kabul\">German embassy</a> during rush hour, killing over 90 and injuring 463.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/May_2017_Kabul_attack\" class=\"mw-redirect\" title=\"May 2017 Kabul attack\">car bomb explodes</a> in a crowded intersection in <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a> near the <a href=\"https://wikipedia.org/wiki/Embassy_of_Germany,_Kabul\" title=\"Embassy of Germany, Kabul\">German embassy</a> during rush hour, killing over 90 and injuring 463.", "links": [{"title": "May 2017 Kabul attack", "link": "https://wikipedia.org/wiki/May_2017_Kabul_attack"}, {"title": "Kabul", "link": "https://wikipedia.org/wiki/Kabul"}, {"title": "Embassy of Germany, Kabul", "link": "https://wikipedia.org/wiki/Embassy_of_Germany,_Kabul"}]}, {"year": "2019", "text": "A shooting occurs inside a municipal building at Virginia Beach, Virginia, leaving 13 people dead, including the shooter, and four others injured.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/2019_Virginia_Beach_shooting\" title=\"2019 Virginia Beach shooting\">A shooting</a> occurs inside a municipal building at <a href=\"https://wikipedia.org/wiki/Virginia_Beach\" class=\"mw-redirect\" title=\"Virginia Beach\">Virginia Beach</a>, <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>, leaving 13 people dead, including the shooter, and four others injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2019_Virginia_Beach_shooting\" title=\"2019 Virginia Beach shooting\">A shooting</a> occurs inside a municipal building at <a href=\"https://wikipedia.org/wiki/Virginia_Beach\" class=\"mw-redirect\" title=\"Virginia Beach\">Virginia Beach</a>, <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>, leaving 13 people dead, including the shooter, and four others injured.", "links": [{"title": "2019 Virginia Beach shooting", "link": "https://wikipedia.org/wiki/2019_Virginia_Beach_shooting"}, {"title": "Virginia Beach", "link": "https://wikipedia.org/wiki/Virginia_Beach"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}]}], "Births": [{"year": "1443 (or 1441)", "text": "<PERSON>, Countess of Richmond and Derby (d. 1509)", "html": "1443 (or 1441) - <a href=\"https://wikipedia.org/wiki/1443\" title=\"1443\">1443</a> (or <a href=\"https://wikipedia.org/wiki/1441\" title=\"1441\">1441</a>) - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Richmond_and_Derby\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Richmond and Derby\"><PERSON>, Countess of Richmond and Derby</a> (d. 1509)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1443\" title=\"1443\">1443</a> (or <a href=\"https://wikipedia.org/wiki/1441\" title=\"1441\">1441</a>) - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Richmond_and_Derby\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Richmond and Derby\"><PERSON>, Countess of Richmond and Derby</a> (d. 1509)", "links": [{"title": "1443", "link": "https://wikipedia.org/wiki/1443"}, {"title": "1441", "link": "https://wikipedia.org/wiki/1441"}, {"title": "<PERSON>, Countess of Richmond and Derby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Richmond_and_Derby"}]}, {"year": "1462", "text": "<PERSON>, Count of Hanau-Lichtenberg (d. 1504)", "html": "1462 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-Lichtenberg\" title=\"<PERSON>, Count of Hanau-Lichtenberg\"><PERSON>, Count of Hanau-Lichtenberg</a> (d. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-Lichtenberg\" title=\"<PERSON>, Count of Hanau-Lichtenberg\"><PERSON>, Count of Hanau-Lichtenberg</a> (d. 1504)", "links": [{"title": "<PERSON>, Count of Hanau-Lichtenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1469", "text": "<PERSON> of Portugal (d. 1521)", "html": "1469 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (d. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (d. 1521)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1535", "text": "<PERSON>, Italian painter (d. 1607)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1607)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1556", "text": "<PERSON><PERSON><PERSON>, Catholic cardinal (d. 1600)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_Radziwi%C5%82%C5%82_(1556%E2%80%931600)\" title=\"<PERSON><PERSON><PERSON> (1556-1600)\"><PERSON><PERSON><PERSON></a>, Catholic cardinal (d. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_Radziwi%C5%82%C5%82_(1556%E2%80%931600)\" title=\"<PERSON><PERSON><PERSON> (1556-1600)\"><PERSON><PERSON><PERSON></a>, Catholic cardinal (d. 1600)", "links": [{"title": "<PERSON><PERSON><PERSON> (1556-1600)", "link": "https://wikipedia.org/wiki/Jerzy_Radziwi%C5%82%C5%82_(1556%E2%80%931600)"}]}, {"year": "1577", "text": "<PERSON><PERSON>, Empress consort of the Mughal Empire (d. 1645)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Empress consort of the Mughal Empire (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Empress consort of the Mughal Empire (d. 1645)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1613", "text": "<PERSON>, Elector of Saxony (d. 1680)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1680)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1640", "text": "<PERSON><PERSON><PERSON>, King of Poland (d. 1673)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/Micha%C5%82_Korybut_Wi%C5%9Bniowiecki\" title=\"<PERSON><PERSON><PERSON> Wiśniowiecki\"><PERSON><PERSON><PERSON></a>, King of Poland (d. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mi<PERSON>%C5%82_Korybut_Wi%C5%9Bniowiecki\" title=\"<PERSON><PERSON><PERSON> Wiśniowiecki\"><PERSON><PERSON><PERSON></a>, King of Poland (d. 1673)", "links": [{"title": "<PERSON><PERSON><PERSON>śniowieck<PERSON>", "link": "https://wikipedia.org/wiki/Micha%C5%82_Korybut_Wi%C5%9Bniowiecki"}]}, {"year": "1641", "text": "Patriarch <PERSON><PERSON><PERSON><PERSON> of Jerusalem (d. 1707)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON><PERSON><PERSON>_II_of_Jerusalem\" class=\"mw-redirect\" title=\"Patriarch <PERSON><PERSON><PERSON><PERSON> II of Jerusalem\">Patriarch <PERSON><PERSON><PERSON><PERSON> II of Jerusalem</a> (d. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON><PERSON><PERSON>_II_of_Jerusalem\" class=\"mw-redirect\" title=\"Patriarch <PERSON><PERSON><PERSON><PERSON> of Jerusalem\">Patriarch <PERSON><PERSON><PERSON><PERSON> of Jerusalem</a> (d. 1707)", "links": [{"title": "Patriarch <PERSON><PERSON><PERSON><PERSON> of Jerusalem", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_II_of_Jerusalem"}]}, {"year": "1725", "text": "<PERSON><PERSON><PERSON><PERSON>, Queen of the Malwa Kingdom under the Maratha Empire (d. 1795)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/Ahily<PERSON><PERSON>_Holkar\" title=\"Ahilyabai Holkar\"><PERSON><PERSON><PERSON><PERSON></a>, Queen of the Malwa Kingdom under the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahily<PERSON><PERSON>_Holkar\" title=\"Ahilyabai Holkar\"><PERSON><PERSON><PERSON><PERSON></a>, Queen of the Malwa Kingdom under the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> (d. 1795)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ar"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}]}, {"year": "1732", "text": "Count <PERSON><PERSON><PERSON><PERSON>, Austrian archbishop (d. 1812)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON><PERSON>_von_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON><PERSON><PERSON><PERSON> von <PERSON>\">Count <PERSON><PERSON><PERSON><PERSON> von <PERSON></a>, Austrian archbishop (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON><PERSON>_von_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON><PERSON><PERSON><PERSON> von <PERSON>do\">Count <PERSON><PERSON><PERSON><PERSON></a>, Austrian archbishop (d. 1812)", "links": [{"title": "Count <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1753", "text": "<PERSON>, French lawyer and politician (d. 1793)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, Italian painter and educator (d. 1817)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and educator (d. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and educator (d. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, German poet, author, and critic (d. 1853)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, author, and critic (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, author, and critic (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, Swiss philologist and scholar (d. 1887)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss philologist and scholar (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss philologist and scholar (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, Irish-Australian politician, 3rd Premier of South Australia (d. 1884)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1815", "text": "<PERSON><PERSON>, English-Australian cricketer and politician, 15th Premier of Tasmania (d. 1906)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Australian cricketer and politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Australian cricketer and politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1818", "text": "<PERSON>, American lawyer and politician, 25th Governor of Massachusetts (d. 1867)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, American lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Andrew"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1819", "text": "<PERSON>, American poet, essayist, and journalist (d. 1892)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, essayist, and journalist (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, essayist, and journalist (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON><PERSON><PERSON>, first Japanese female doctor of Western medicine (d. 1903)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ine\" title=\"<PERSON><PERSON><PERSON> Ine\"><PERSON><PERSON><PERSON></a>, first Japanese female doctor of Western medicine (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ine\" title=\"<PERSON><PERSON><PERSON> Ine\"><PERSON><PERSON><PERSON></a>, first Japanese female doctor of Western medicine (d. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ine"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON>, Japanese commander (d. 1869)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Hi<PERSON><PERSON>_Toshiz%C5%8D\" title=\"<PERSON><PERSON><PERSON> Toshizō\"><PERSON><PERSON><PERSON></a>, Japanese commander (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hi<PERSON><PERSON>_Toshiz%C5%8D\" title=\"<PERSON><PERSON><PERSON> Toshizō\"><PERSON><PERSON><PERSON></a>, Japanese commander (d. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hijikata_Toshiz%C5%8D"}]}, {"year": "1838", "text": "<PERSON>, English economist and philosopher (d. 1900)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and philosopher (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and philosopher (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, Australian politician, 15th Premier of South Australia (d. 1894)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1847", "text": "<PERSON>, 1st Viscount <PERSON>, Canadian-Irish businessman and politician, Lord Mayor of Belfast (d. 1924)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Canadian-Irish businessman and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Belfast\" class=\"mw-redirect\" title=\"Lord Mayor of Belfast\">Lord Mayor of Belfast</a> (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Canadian-Irish businessman and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Belfast\" class=\"mw-redirect\" title=\"Lord Mayor of Belfast\">Lord Mayor of Belfast</a> (d. 1924)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Lord Mayor of Belfast", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_Belfast"}]}, {"year": "1852", "text": "<PERSON>, Argentinian explorer and academic (d. 1919)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian explorer and academic (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Francisco Moreno\"><PERSON></a>, Argentinian explorer and academic (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, German microbiologist, invented the <PERSON><PERSON> dish (d. 1921)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German microbiologist, invented the <a href=\"https://wikipedia.org/wiki/Petri_dish\" title=\"Petri dish\"><PERSON><PERSON> dish</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German microbiologist, invented the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_dish\" title=\"Petri dish\"><PERSON><PERSON> dish</a> (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Petri dish", "link": "https://wikipedia.org/wiki/Petri_dish"}]}, {"year": "1857", "text": "<PERSON> (d. 1939)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius XI\">Pope <PERSON></a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius <PERSON>\">Pope <PERSON></a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, English socialist, social psychologist, and educationalist (d. 1932)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English socialist, social psychologist, and educationalist (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English socialist, social psychologist, and educationalist (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, English painter (d. 1942)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Indian-English captain and explorer (d. 1942)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English captain and explorer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English captain and explorer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>band"}]}, {"year": "1866", "text": "<PERSON>, American entrepreneur; one of the founders of the Ringling Brothers Circus (d. 1936)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur; one of the founders of the <a href=\"https://wikipedia.org/wiki/<PERSON>ling_Brothers_Circus\" title=\"Ringling Brothers Circus\">Ringling Brothers Circus</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur; one of the founders of the <a href=\"https://wikipedia.org/wiki/<PERSON>ling_Brothers_Circus\" title=\"Ringling Brothers Circus\">Ringling Brothers Circus</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ringling Brothers Circus", "link": "https://wikipedia.org/wiki/Ringling_Brothers_Circus"}]}, {"year": "1875", "text": "<PERSON>, British suffragette and women's rights activist (d. 1953)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British suffragette and women's rights activist (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British suffragette and women's rights activist (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, New Zealand-Australian soprano (d. 1952)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian soprano (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian soprano (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frances_Alda"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Hungarian politician, Hungarian Minister of War (d. 1956)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Festetics\" title=\"Sándor Festetics\"><PERSON><PERSON><PERSON> Fest<PERSON></a>, Hungarian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)\" class=\"mw-redirect\" title=\"Ministry of Defence (Hungary)\">Hungarian Minister of War</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Festetics\" title=\"Sándor Festetics\"><PERSON><PERSON><PERSON> Fest<PERSON></a>, Hungarian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)\" class=\"mw-redirect\" title=\"Ministry of Defence (Hungary)\">Hungarian Minister of War</a> (d. 1956)", "links": [{"title": "S<PERSON>dor <PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_Festetics"}, {"title": "Ministry of Defence (Hungary)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Finnish politician, 2nd President of Finland (d. 1942)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1885", "text": "<PERSON>, Australian politician, 32nd Premier of South Australia (d. 1967)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1967)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, French poet and diplomat, Nobel Prize laureate (d. 1975)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Saint<PERSON><PERSON></a>, French poet and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1892", "text": "<PERSON>, Belarusian-French painter (d. 1968)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-French painter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-French painter (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, German lieutenant and politician (d. 1951)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German lieutenant and politician (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German lieutenant and politician (d. 1951)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1892", "text": "<PERSON>, Russian poet and author (d. 1968)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, German lieutenant and politician (d. 1934)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American comedian, radio host, game show panelist, and author (d. 1956)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, radio host, game show panelist, and author (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, radio host, game show panelist, and author (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American minister and author (d. 1993)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON></a>, American minister and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Norman <PERSON>\">Norman <PERSON></a>, American minister and author (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American athlete (d. 1981)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Luc<PERSON>_<PERSON>\" title=\"Lucile <PERSON>\"><PERSON><PERSON></a>, American athlete (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucile_<PERSON>d\" title=\"Lucile <PERSON>d\"><PERSON><PERSON></a>, American athlete (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucile_Godbold"}]}, {"year": "1901", "text": "<PERSON>, Italian-American conductor and composer (d. 1983)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American conductor and composer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American conductor and composer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American actor (d. 1993)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "links": [{"title": "Don <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Canadian-American ice hockey player (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Coulter"}]}, {"year": "1911", "text": "<PERSON>, French economist and physicist, Nobel Prize laureate (d. 2010)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Chinese-American experimental physicist (d. 1997)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American experimental physicist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"Chien-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American experimental physicist (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Japanese composer and educator (d. 2006)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese composer and educator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese composer and educator (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Dutch director, producer, and screenwriter (d. 1997)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director, producer, and screenwriter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director, producer, and screenwriter (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actor (d. 2001)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, African American chemist (d. 1982)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lloyd Quarterman\"><PERSON></a>, African American chemist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lloyd Quarterman\"><PERSON></a>, African American chemist (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>man"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American editor, novelist and critic (d. 1995)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American editor, novelist and critic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American editor, novelist and critic (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English actress (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9"}]}, {"year": "1921", "text": "<PERSON>, Anglo-Italian jewellery designer (d. 2007)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Italian jewellery designer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Italian jewellery designer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American radio and television announcer (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television announcer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television announcer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Austrian-Italian actress and singer (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Italian actress and singer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Italian actress and singer (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, English-Spanish actor (d. 1992)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Elliott\"><PERSON><PERSON></a>, English-Spanish actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Elliott\"><PERSON><PERSON></a>, English-Spanish actor (d. 1992)", "links": [{"title": "<PERSON><PERSON> Elliott", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, American painter and sculptor (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>ls<PERSON>_Kelly\" title=\"Ells<PERSON> Kelly\"><PERSON><PERSON><PERSON></a>, American painter and sculptor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ells<PERSON>_Kelly\" title=\"Ells<PERSON> Kelly\"><PERSON><PERSON><PERSON></a>, American painter and sculptor (d. 2015)", "links": [{"title": "Ellsworth Kelly", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kelly"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Prince of Monaco (d. 2005)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON><PERSON>, Prince of Monaco\"><PERSON><PERSON>, Prince of Monaco</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON><PERSON>, Prince of Monaco\"><PERSON><PERSON>, Prince of Monaco</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "1923", "text": "<PERSON>, Italian football player (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian football player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian football player (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor and director (d. 1986)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English admiral (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Baron <PERSON>, English lieutenant and banker (d. 2017)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lieutenant and banker (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lieutenant and banker (d. 2017)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Israeli director and producer (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Menahem_Golan\" title=\"Menahem Golan\"><PERSON><PERSON><PERSON></a>, Israeli director and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Menahem_Golan\" title=\"Menahem Golan\"><PERSON><PERSON><PERSON></a>, Israeli director and producer (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lan"}]}, {"year": "1930", "text": "<PERSON>, American actor, director, musician, and producer", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, musician, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Eastwood\"><PERSON></a>, American actor, director, musician, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1931", "text": "<PERSON>, American soprano and actress (d. 2010)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Brazilian pianist, bassist, and composer (d. 2012)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian pianist, bassist, and composer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian pianist, bassist, and composer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American computer scientist and engineer (d. 1994)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American religious leader, educator, and author", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, educator, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, educator, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor (d. 1979)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, New Zealand businessman and politician, 35th Prime Minister of New Zealand", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman and politician, 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman and politician, 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2003)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, British sailor and politician, Deputy Prime Minister of the United Kingdom (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British sailor and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom\" title=\"Deputy Prime Minister of the United Kingdom\">Deputy Prime Minister of the United Kingdom</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British sailor and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom\" title=\"Deputy Prime Minister of the United Kingdom\">Deputy Prime Minister of the United Kingdom</a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (d. 2025)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English humanitarian and author", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English humanitarian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English humanitarian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian hammer thrower and coach", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian hammer thrower and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian hammer thrower and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American musician and singer-songwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician and singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American illustrator", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Welsh nurse and educator", "html": "1941 - <a href=\"https://wikipedia.org/wiki/June_<PERSON>_(nurse)\" title=\"<PERSON> (nurse)\"><PERSON></a>, Welsh nurse and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON>(nurse)\" title=\"<PERSON> (nurse)\">June <PERSON></a>, Welsh nurse and educator", "links": [{"title": "<PERSON> (nurse)", "link": "https://wikipedia.org/wiki/June_<PERSON>_(nurse)"}]}, {"year": "1941", "text": "<PERSON>, American pharmacologist and academic, Nobel Prize laureate", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1941", "text": "<PERSON>, American economist and academic, Nobel Prize laureate", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1943", "text": "<PERSON>, American actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American football player, sportscaster, and actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, German actor, director, and screenwriter (d. 1982)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor, director, and screenwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor, director, and screenwriter (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Ivorian academic and politician, 4th President of Côte d'Ivoire", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian academic and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ivory_Coast\" title=\"List of heads of state of Ivory Coast\">President of Côte d'Ivoire</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian academic and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ivory_Coast\" title=\"List of heads of state of Ivory Coast\">President of Côte d'Ivoire</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Laurent_<PERSON>"}, {"title": "List of heads of state of Ivory Coast", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Ivory_Coast"}]}, {"year": "1945", "text": "<PERSON>, American journalist and author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American publisher and critic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Jamaican cricketer and umpire", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Estonian journalist, politician, and diplomat (d. 2009)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist, politician, and diplomat (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist, politician, and diplomat (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English model and businesswoman", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Scottish singer-songwriter, guitarist, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, German discus thrower", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German discus thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Belarusian journalist and author, Nobel Prize laureate", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1948", "text": "<PERSON>, English musician, songwriter and drummer (d. 1980)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician, songwriter and drummer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician, songwriter and drummer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English bass player, guitarist, and record producer (d. 1991)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player, guitarist, and record producer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player, guitarist, and record producer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American lieutenant, lawyer, and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor, film producer and television writer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, film producer and television writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, film producer and television writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, French director, producer, and screenwriter, founded DIC Entertainment", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter, founded <a href=\"https://wikipedia.org/wiki/DIC_Entertainment\" title=\"DIC Entertainment\">DIC Entertainment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter, founded <a href=\"https://wikipedia.org/wiki/DIC_Entertainment\" title=\"DIC Entertainment\">DIC Entertainment</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "DIC Entertainment", "link": "https://wikipedia.org/wiki/DIC_Entertainment"}]}, {"year": "1950", "text": "<PERSON>, American actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Estonian politician, Estonian Minister of the Interior (d. 2022)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of the Interior (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, German hammer thrower", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German hammer thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON>, French writer, photographer and actress (d. 2016)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French writer, photographer and actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French writer, photographer and actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, German singer-songwriter and keyboard player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "Pirkka<PERSON><PERSON><PERSON><PERSON>, Finnish actor and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rk<PERSON>-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish actor and screenwriter", "links": [{"title": "Pirkka<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rkka-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Greek footballer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American actress and singer (d. 2000)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actress, comedian, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German drummer and composer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German drummer and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German drummer and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English singer-songwriter and keyboard player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, English singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, English singer-songwriter and keyboard player", "links": [{"title": "<PERSON> (British musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(British_musician)"}]}, {"year": "1957", "text": "<PERSON>, American ice hockey player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1959", "text": "<PERSON>, Italian race car driver (d. 2014)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" class=\"mw-redirect\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" class=\"mw-redirect\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player and businessman", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1960)\" title=\"<PERSON> (ice hockey, born 1960)\"><PERSON></a>, Canadian ice hockey player and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1960)\" title=\"<PERSON> (ice hockey, born 1960)\"><PERSON></a>, Canadian ice hockey player and businessman", "links": [{"title": "<PERSON> (ice hockey, born 1960)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1960)"}]}, {"year": "1960", "text": "<PERSON>, American actor, comedian, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English rugby player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_Cote"}]}, {"year": "1961", "text": "<PERSON>, Australian footballer and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress, director, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Peruvian politician, 64th President of Peru", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian politician, 64th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian politician, 64th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1962", "text": "<PERSON>, Canadian singer-songwriter and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Canadian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Canadian singer-songwriter and producer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1963", "text": "<PERSON>, holder of the Sir <PERSON> Chair of Chemistry at the University of Manchester", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, holder of the <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_Chair_of_Chemistry\" class=\"mw-redirect\" title=\"Sir <PERSON> Chair of Chemistry\">Sir <PERSON> Chair of Chemistry</a> at the <a href=\"https://wikipedia.org/wiki/University_of_Manchester\" title=\"University of Manchester\">University of Manchester</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, holder of the <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>_Chair_of_Chemistry\" class=\"mw-redirect\" title=\"Sir <PERSON> Chair of Chemistry\">Sir <PERSON> Chair of Chemistry</a> at the <a href=\"https://wikipedia.org/wiki/University_of_Manchester\" title=\"University of Manchester\">University of Manchester</a>", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>(scientist)"}, {"title": "Sir <PERSON> Chair of Chemistry", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>_Chair_of_Chemistry"}, {"title": "University of Manchester", "link": "https://wikipedia.org/wiki/University_of_Manchester"}]}, {"year": "1963", "text": "<PERSON>, Hungarian politician, 38th Prime Minister of Hungary", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Hungarian politician, 38th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Hungarian politician, 38th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Viktor_Orb%C3%A1n"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter and keyboard player (d. 2003)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and keyboard player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and keyboard player (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian lawyer and businessman", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, French hurdler and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Caristan\" title=\"Stéphane Caristan\"><PERSON><PERSON><PERSON><PERSON></a>, French hurdler and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Caristan\" title=\"Stéphane Caristan\"><PERSON><PERSON><PERSON><PERSON></a>, French hurdler and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phane_<PERSON>istan"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Japanese politician, Japanese Minister for Foreign Affairs", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Japan)\" title=\"Minister for Foreign Affairs (Japan)\">Japanese Minister for Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Japan)\" title=\"Minister for Foreign Affairs (Japan)\">Japanese Minister for Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Japan)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Japan)"}]}, {"year": "1964", "text": "<PERSON> \"<PERSON><PERSON><PERSON><PERSON>C<PERSON>\" <PERSON><PERSON><PERSON><PERSON><PERSON>, American rapper and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> \"D.M.C.\" <PERSON><PERSON><PERSON><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> \"D.M.C.\" <PERSON><PERSON><PERSON><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American model, actress, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American-Australian singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American-Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American-Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Sri Lankan cricketer and referee", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer and referee", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON>, New Zealand television host and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand television host and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand television host and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player, coach, and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Indo-Anglo-American saxophonist, konnakol artist, composer, and arranger", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indo-Anglo-American saxophonist, <a href=\"https://wikipedia.org/wiki/Konnakol\" title=\"Konnakol\">konna<PERSON>l</a> artist, composer, and arranger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indo-Anglo-American saxophonist, <a href=\"https://wikipedia.org/wiki/Konnakol\" title=\"Konnako<PERSON>\">konna<PERSON>l</a> artist, composer, and arranger", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Konnakol", "link": "https://wikipedia.org/wiki/Konnakol"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Norwegian skier", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Frode_Estil\" title=\"Frode Estil\"><PERSON><PERSON></a>, Norwegian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frode_Estil\" title=\"Frode Estil\"><PERSON><PERSON>il</a>, Norwegian skier", "links": [{"title": "Frode Estil", "link": "https://wikipedia.org/wiki/Frode_Estil"}]}, {"year": "1972", "text": "<PERSON>, American bassist and record producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> McBride\"><PERSON></a>, American bassist and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Finnish international footballer and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Finnish international footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Finnish international footballer and coach", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(footballer)"}]}, {"year": "1972", "text": "<PERSON>, British actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" class=\"mw-redirect\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" class=\"mw-redirect\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (outfielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Japanese comedian and singer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese comedian and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese comedian and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yoshi"}]}, {"year": "1975", "text": "<PERSON>, Japanese baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Suzuki\"><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Irish actor ", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Italian swimmer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Zambian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Belgian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American musician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Swedish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Italian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Austrian skier", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d\" title=\"Mar<PERSON> Schild\"><PERSON><PERSON></a>, Austrian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d\" title=\"Mar<PERSON> Schild\"><PERSON><PERSON></a>, Austrian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d"}]}, {"year": "1982", "text": "<PERSON>, Australian rugby league player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Serbian swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%8Cavi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milo<PERSON>_%C4%8Cavi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milorad_%C4%8Cavi%C4%87"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American rapper", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Waka_Flocka_Flame\" title=\"Waka Flocka Flame\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Waka_Flocka_Flame\" title=\"Waka Flocka Flame\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waka_Flocka_Flame"}]}, {"year": "1986", "text": "<PERSON>, Dutch cyclist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Swedish ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and rapper", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Banks\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Banks\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and rapper", "links": [{"title": "Azealia Banks", "link": "https://wikipedia.org/wiki/A<PERSON>alia_Banks"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Micha%C3%ABl_Bournival\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Micha%C3%ABl_Bournival\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micha%C3%ABl_Bournival"}]}, {"year": "1992", "text": "<PERSON>, Latvian heptathlete", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian heptathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic politician", "html": "1992 - <a href=\"https://wikipedia.org/wiki/J%C3%B3hann_P%C3%A1ll_J%C3%B3<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3hann_P%C3%A1ll_J%C3%B3<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3hann_P%C3%A1ll_J%C3%B3han<PERSON>son"}]}, {"year": "1995", "text": "<PERSON>, American baseball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i"}]}, {"year": "1996", "text": "<PERSON>, New Zealand rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1997", "text": "<PERSON><PERSON>-young, South Korean singer and rapper", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Woo_<PERSON>-young\" title=\"Woo <PERSON>-young\"><PERSON><PERSON>-<PERSON></a>, South Korean singer and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woo_<PERSON>-young\" title=\"Woo <PERSON>-young\"><PERSON><PERSON>-<PERSON></a>, South Korean singer and rapper", "links": [{"title": "<PERSON><PERSON>-young", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>-young"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American race car driver", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American wrestler", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Bree<PERSON>_Hall\" title=\"Breece Hall\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bree<PERSON>_<PERSON>\" title=\"Breece Hall\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON>ce Hall", "link": "https://wikipedia.org/wiki/Breece_Hall"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Polish tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Iga_%C5%9Awi%C4%85tek\" title=\"Iga Świątek\"><PERSON><PERSON></a>, Polish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iga_%C5%9Awi%C4%85tek\" title=\"Iga Świątek\"><PERSON><PERSON></a>, Polish tennis player", "links": [{"title": "Iga <PERSON>wi<PERSON>tek", "link": "https://wikipedia.org/wiki/Iga_%C5%9Awi%C4%85tek"}]}], "Deaths": [{"year": "455", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 396)", "html": "455 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Maximus\" title=\"<PERSON><PERSON><PERSON> Maximus\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 396)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Maximus\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 396)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Maximus"}]}, {"year": "930", "text": "<PERSON>, princess of Southern Han (b. 896)", "html": "930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON>%27s_wife)\" title=\"<PERSON> (<PERSON>'s wife)\"><PERSON></a>, princess of Southern Han (b. 896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON>%27s_wife)\" title=\"<PERSON> (<PERSON>'s wife)\"><PERSON></a>, princess of Southern Han (b. 896)", "links": [{"title": "<PERSON> (<PERSON>'s wife)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(<PERSON>_<PERSON>%27s_wife)"}]}, {"year": "960", "text": "<PERSON><PERSON>, Japanese statesman (b. 909)", "html": "960 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Morosuke\" title=\"Fujiwara no Morosuke\"><PERSON><PERSON> no <PERSON>uke</a>, Japanese statesman (b. 909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Morosuke\" title=\"Fujiwara no Morosuke\"><PERSON><PERSON> no <PERSON>uke</a>, Japanese statesman (b. 909)", "links": [{"title": "<PERSON><PERSON> no <PERSON>uke", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1076", "text": "<PERSON><PERSON><PERSON>, Earl of Northumbria, English politician (b. 1050)", "html": "1076 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Earl_of_Northumbria\" title=\"<PERSON><PERSON><PERSON>, Earl of Northumbria\"><PERSON><PERSON><PERSON>, Earl of Northumbria</a>, English politician (b. 1050)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Earl_of_Northumbria\" title=\"<PERSON><PERSON><PERSON>, Earl of Northumbria\"><PERSON><PERSON><PERSON>, Earl of Northumbria</a>, English politician (b. 1050)", "links": [{"title": "<PERSON><PERSON><PERSON>, Earl of Northumbria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Earl_of_Northumbria"}]}, {"year": "1089", "text": "<PERSON><PERSON><PERSON>, archbishop of Cologne", "html": "1089 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, archbishop of Cologne", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON> von <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, archbishop of Cologne", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1162", "text": "<PERSON><PERSON><PERSON> <PERSON>, king of Hungary (b. 1130)", "html": "1162 - <a href=\"https://wikipedia.org/wiki/G%C3%A9za_II_of_Hungary\" title=\"Géza II of Hungary\"><PERSON><PERSON><PERSON> <PERSON></a>, king of Hungary (b. 1130)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9za_II_of_Hungary\" title=\"Géza II of Hungary\"><PERSON><PERSON><PERSON> <PERSON></a>, king of Hungary (b. 1130)", "links": [{"title": "Géza II of Hungary", "link": "https://wikipedia.org/wiki/G%C3%A9za_II_of_Hungary"}]}, {"year": "1321", "text": "<PERSON><PERSON><PERSON>, king of Sweden (b. 1280)", "html": "1321 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_King_of_Sweden\" title=\"<PERSON><PERSON><PERSON>, King of Sweden\"><PERSON><PERSON><PERSON></a>, king of Sweden (b. 1280)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_King_of_Sweden\" title=\"<PERSON><PERSON><PERSON>, King of Sweden\"><PERSON><PERSON><PERSON></a>, king of Sweden (b. 1280)", "links": [{"title": "<PERSON><PERSON><PERSON>, King of Sweden", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_King_of_Sweden"}]}, {"year": "1326", "text": "<PERSON>, 2nd Baron <PERSON> (b. 1271)", "html": "1326 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a> (b. 1271)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a> (b. 1271)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_<PERSON>_<PERSON>"}]}, {"year": "1329", "text": "<PERSON><PERSON>, Italian statesman and writer (b. 1261)", "html": "1329 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian statesman and writer (b. 1261)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian statesman and writer (b. 1261)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1349", "text": "<PERSON>, English politician (b. 1297)", "html": "1349 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>_of_Liddell\" title=\"<PERSON>, 2nd Baron <PERSON> of Liddell\"><PERSON></a>, English politician (b. 1297)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>_of_Liddell\" title=\"<PERSON>, 2nd Baron <PERSON> of Liddell\"><PERSON></a>, English politician (b. 1297)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON> of Liddell", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>_of_Liddell"}]}, {"year": "1370", "text": "<PERSON><PERSON> of Assisi, Italian hermit and monk (b. 1295)", "html": "1370 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Assisi\" title=\"<PERSON><PERSON> of Assisi\"><PERSON><PERSON> of Assisi</a>, Italian hermit and monk (b. 1295)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Assisi\" title=\"<PERSON><PERSON> of Assisi\"><PERSON><PERSON> of Assisi</a>, Italian hermit and monk (b. 1295)", "links": [{"title": "<PERSON><PERSON> of Assisi", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Assisi"}]}, {"year": "1408", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (b. 1358)", "html": "1408 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yo<PERSON>su\" title=\"Ash<PERSON><PERSON> Yoshimitsu\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1358)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yo<PERSON>su\" title=\"<PERSON><PERSON><PERSON> Yoshimitsu\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1358)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1410", "text": "<PERSON> of Aragon, Spanish king (b. 1356)", "html": "1410 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a>, Spanish king (b. 1356)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a>, Spanish king (b. 1356)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1504", "text": "<PERSON><PERSON><PERSON> of Nassau (b. 1451)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Nassau\" title=\"<PERSON><PERSON><PERSON> <PERSON> of Nassau\"><PERSON><PERSON><PERSON> II of Nassau</a> (b. 1451)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Nassau\" title=\"<PERSON><PERSON><PERSON> II of Nassau\"><PERSON><PERSON><PERSON> of Nassau</a> (b. 1451)", "links": [{"title": "<PERSON><PERSON><PERSON> II of Nassau", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Nassau"}]}, {"year": "1558", "text": "<PERSON>, English general and diplomat (b. 1505)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and diplomat (b. 1505)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and diplomat (b. 1505)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1567", "text": "<PERSON>, Belgian pastor and theologian (b. 1522)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian pastor and theologian (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian pastor and theologian (b. 1522)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1594", "text": "<PERSON><PERSON><PERSON>, Italian painter and educator (b. 1518)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and educator (b. 1518)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tin<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and educator (b. 1518)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1601", "text": "<PERSON><PERSON><PERSON> von <PERSON>, Archbishop-Elector of Cologne (b. 1547)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_von_Waldburg\" title=\"<PERSON><PERSON><PERSON> von Waldburg\"><PERSON><PERSON><PERSON> von Waldburg</a>, Archbishop<PERSON>Elector of Cologne (b. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_von_Waldburg\" title=\"<PERSON><PERSON><PERSON> von Waldburg\"><PERSON><PERSON><PERSON> von Waldburg</a>, Archbishop<PERSON>Elector of Cologne (b. 1547)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1640", "text": "<PERSON><PERSON><PERSON><PERSON>, Safavid princess (date of birth unknown)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Be<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Safavid princess (date of birth unknown)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Begum\"><PERSON><PERSON><PERSON><PERSON></a>, Safavid princess (date of birth unknown)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>b_Begum"}]}, {"year": "1665", "text": "<PERSON>, Dutch painter (b. 1597)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>am\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1597)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, German theologian and educator (b. 1650)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and educator (b. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and educator (b. 1650)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON> of Prussia (b. 1688)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (b. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (b. 1688)", "links": [{"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Prussia"}]}, {"year": "1747", "text": "<PERSON><PERSON>, German-Russian politician, Russian Minister of Foreign Affairs (b. 1686)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Russian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)\" title=\"Minister of Foreign Affairs (Russia)\">Russian Minister of Foreign Affairs</a> (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Russian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)\" title=\"Minister of Foreign Affairs (Russia)\">Russian Minister of Foreign Affairs</a> (b. 1686)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Russia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)"}]}, {"year": "1809", "text": "<PERSON>, Austrian pianist and composer (b. 1732)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (b. 1732)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, French general (b. 1769)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, English architect and engineer (b. 1757)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and engineer (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and engineer (b. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1832", "text": "<PERSON><PERSON><PERSON><PERSON>, French mathematician and theorist (b. 1811)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/%C3%89variste_<PERSON>is\" title=\"<PERSON>varist<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French mathematician and theorist (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89variste_<PERSON>is\" title=\"<PERSON>var<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French mathematician and theorist (b. 1811)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89variste_G<PERSON>is"}]}, {"year": "1837", "text": "<PERSON>, English actor, comedian and dancer, (b. 1779)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian and dancer, (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian and dancer, (b. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, German pastor and philosopher (b. 1780)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor and philosopher (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor and philosopher (b. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, Scottish minister and economist (b. 1780)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and economist (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and economist (b. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON><PERSON>, French author (b. 1805)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A9nie_de_Gu%C3%A9rin\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A9nie_de_Gu%C3%A9rin\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author (b. 1805)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A9nie_de_Gu%C3%A9rin"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Greek archaeologist, teacher and writer (b. 1818)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek archaeologist, teacher and writer (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek archaeologist, teacher and writer (b. 1818)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian author, poet, and politician (b. 1839)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_Fr%C3%A9chette\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian author, poet, and politician (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_Fr%C3%A9chette\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian author, poet, and politician (b. 1839)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Louis-Honor%C3%A9_Fr%C3%A9chette"}]}, {"year": "1909", "text": "<PERSON>, Welsh-Australian politician, 24th Premier of South Australia (b. 1852)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Australian_politician)\" title=\"<PERSON> (South Australian politician)\"><PERSON></a>, Welsh-Australian politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(South_Australian_politician)\" title=\"<PERSON> (South Australian politician)\"><PERSON></a>, Welsh-Australian politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1852)", "links": [{"title": "<PERSON> (South Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Australian_politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1910", "text": "<PERSON>, English-American physician and educator (b. 1821)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physician and educator (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physician and educator (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian cardinal (b. 1866)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1866)", "links": [{"title": "Felix<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, German author and illustrator (b. 1864)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6wer\" title=\"<PERSON>\"><PERSON></a>, German author and illustrator (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6wer\" title=\"<PERSON>\"><PERSON></a>, German author and illustrator (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Willy_St%C3%B6wer"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Italian-Austrian SS officer (b. 1904)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Odilo_Globocnik\" title=\"Odilo Globo<PERSON>nik\"><PERSON><PERSON><PERSON></a>, Italian-Austrian <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>lo_Globocnik\" title=\"Odilo Globocnik\"><PERSON><PERSON><PERSON></a>, Italian-Austrian <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odilo_Globocnik"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Greek art collector and philanthropist, founded the Benaki Museum (b. 1873)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek art collector and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Benaki_Museum\" title=\"Benaki Museum\">Benaki Museum</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek art collector and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Benaki_Museum\" title=\"Benaki Museum\">Benaki Museum</a> (b. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Benaki Museum", "link": "https://wikipedia.org/wiki/Benaki_Museum"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Greek general and politician (b. 1890)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Polish poet and academic (b. 1878)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Leopold_Staff\" title=\"Leopold Staff\">Leopold Staff</a>, Polish poet and academic (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leopold_Staff\" title=\"Leopold Staff\">Leopold Staff</a>, Polish poet and academic (b. 1878)", "links": [{"title": "Leopold Staff", "link": "https://wikipedia.org/wiki/Leopold_Staff"}]}, {"year": "1960", "text": "<PERSON>, Flemish author and poet (b. 1882)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish author and poet (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish author and poet (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Willem_Elsschot"}]}, {"year": "1960", "text": "<PERSON><PERSON>, German economist, journalist, and politician, German Minister of Economics (b. 1890)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German economist, journalist, and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_for_Economic_Affairs_and_Energy_(Germany)\" class=\"mw-redirect\" title=\"Federal Ministry for Economic Affairs and Energy (Germany)\">German Minister of Economics</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German economist, journalist, and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_for_Economic_Affairs_and_Energy_(Germany)\" class=\"mw-redirect\" title=\"Federal Ministry for Economic Affairs and Energy (Germany)\">German Minister of Economics</a> (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Federal Ministry for Economic Affairs and Energy (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_for_Economic_Affairs_and_Energy_(Germany)"}]}, {"year": "1962", "text": "<PERSON>, American lawyer and politician (b. 1874)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American pianist and composer (b. 1915)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian-American ice hockey player (b. 1929)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English sculptor and author (b. 1885)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and author (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and author (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, French biologist and geneticist, Nobel Prize laureate (b. 1910)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1977", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1914)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/William_Castle\" title=\"William Castle\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Castle\" title=\"William Castle\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1914)", "links": [{"title": "William Castle", "link": "https://wikipedia.org/wiki/William_Castle"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian footballer and manager (b. 1925)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, <PERSON> of Lodsworth, English economist and journalist (b. 1914)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Lodsworth\" title=\"<PERSON>, Baroness <PERSON> of Lodsworth\"><PERSON>, Baroness <PERSON> of Lodsworth</a>, English economist and journalist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Lodsworth\" title=\"<PERSON>, Baroness <PERSON> of Lodsworth\"><PERSON>, Baroness <PERSON> of Lodsworth</a>, English economist and journalist (b. 1914)", "links": [{"title": "<PERSON>, <PERSON> of Lodsworth", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Lodsworth"}]}, {"year": "1982", "text": "<PERSON>, Italian mountaineer and explorer (b. 1930)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mountaineer and explorer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mountaineer and explorer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American boxer and lieutenant (b. 1895)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and lieutenant (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and lieutenant (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, French mountaineer and author (b. 1921)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9buffat\" title=\"<PERSON>\"><PERSON></a>, French mountaineer and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gaston_R%C3%A9buffat\" title=\"<PERSON>\"><PERSON></a>, French mountaineer and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gaston_R%C3%A9buffat"}]}, {"year": "1986", "text": "<PERSON>, American painter and sculptor (b. 1918)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1917)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1987", "text": "<PERSON>, Indian director and screenwriter (b. 1937)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Indian director and screenwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Indian director and screenwriter (b. 1937)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>(director)"}]}, {"year": "1989", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Trinidadian journalist and historian (b. 1901)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Trinidadian journalist and historian (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C<PERSON> L<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Trinidadian journalist and historian (b. 1901)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American author and academic (b. 1900)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON> Tree Evil Eye, or, <PERSON><PERSON><PERSON>, Bud Light Bull Terrier mascot (b. 1983)", "html": "1993 - <PERSON> Tree Evil Eye, or, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Bud_Light\" class=\"mw-redirect\" title=\"<PERSON> Light\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Bull_Terrier\" title=\"Bull Terrier\">Bull Terrier</a> mascot (b. 1983)", "no_year_html": "<PERSON> Tree Evil Eye, or, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Bud_Light\" class=\"mw-redirect\" title=\"Bud Light\"><PERSON> Light</a> <a href=\"https://wikipedia.org/wiki/Bull_Terrier\" title=\"Bull Terrier\">Bull Terrier</a> mascot (b. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON> MacKenzie", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Bud Light", "link": "https://wikipedia.org/wiki/Bud_Light"}, {"title": "Bull Terrier", "link": "https://wikipedia.org/wiki/Bull_Terrier"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Turkish actor, producer, and composer (b. 1969)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>%C4%B1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor, producer, and composer (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%B1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor, producer, and composer (b. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uzay_Hepar%C4%B1"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Italian-American soprano (b. 1909)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American soprano (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American soprano (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1995", "text": "<PERSON>, American novelist, short story writer, and essayist (b. 1930)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American psychologist and author (b. 1920)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Belgian-American race car driver (b. 1912)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American race car driver (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American race car driver (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Bulgarian diplomat, 1st President of Bulgaria (b. 1936)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian diplomat, 1st <a href=\"https://wikipedia.org/wiki/President_of_Bulgaria\" title=\"President of Bulgaria\">President of Bulgaria</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian diplomat, 1st <a href=\"https://wikipedia.org/wiki/President_of_Bulgaria\" title=\"President of Bulgaria\">President of Bulgaria</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Bulgaria", "link": "https://wikipedia.org/wiki/President_of_Bulgaria"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Sri Lankan historian, author, and academic (b. 1928)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan historian, author, and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan historian, author, and academic (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American actress, talk show host, game show panelist, and television personality (b. 1907)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, talk show host, game show panelist, and television personality (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, talk show host, game show panelist, and television personality (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (b. 1929)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>has<PERSON>_Gupte\" title=\"<PERSON><PERSON><PERSON> Gupte\"><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gupte\"><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Subhash_<PERSON>upte"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Sri Lankan journalist (b. 1954)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Aiyathurai_Nadesan\" title=\"Aiyathurai Nadesan\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan journalist (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aiyathurai_Nadesan\" title=\"Aiyathurai Nadesan\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan journalist (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aiyathurai_Nadesan"}]}, {"year": "2004", "text": "<PERSON>, American guitarist (b. 1941)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>-<PERSON>, French screenwriter and composer (b. 1941)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French screenwriter and composer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French screenwriter and composer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_<PERSON><PERSON>-<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Spanish sculptor (b. 1933)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish sculptor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish sculptor (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Jr., American physicist and chemist, Nobel Prize laureate (b. 1914)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1914)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2009", "text": "<PERSON>, Irish-British drag queen performer and singer (b. 1927)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-British <a href=\"https://wikipedia.org/wiki/Drag_queen\" title=\"Drag queen\">drag queen</a> performer and singer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-British <a href=\"https://wikipedia.org/wiki/Drag_queen\" title=\"Drag queen\">drag queen</a> performer and singer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Drag queen", "link": "https://wikipedia.org/wiki/Drag_queen"}]}, {"year": "2009", "text": "<PERSON>, American physician (b. 1941)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, French-American sculptor and painter (b. 1911)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American sculptor and painter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American sculptor and painter (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English photographer and producer (b. 1933)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, English photographer and producer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, English photographer and producer (b. 1933)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(photographer)"}]}, {"year": "2010", "text": "<PERSON>, American director, producer, and cinematographer (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and cinematographer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and cinematographer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Argentinian singer-songwriter and bandoneón player (b. 1947)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Ju%C3%A1rez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian singer-songwriter and <a href=\"https://wikipedia.org/wiki/Bandone%C3%B3n\" class=\"mw-redirect\" title=\"Bandoneón\">bandoneón</a> player (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Ju%C3%A1rez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian singer-songwriter and <a href=\"https://wikipedia.org/wiki/Bandone%C3%B3n\" class=\"mw-redirect\" title=\"Bandoneón\">bandoneón</a> player (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Ju%C3%A1rez"}, {"title": "Bandoneón", "link": "https://wikipedia.org/wiki/Bandone%C3%B3n"}]}, {"year": "2010", "text": "<PERSON><PERSON>, New Zealand director, producer, and screenwriter (b. 1942)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ta\" title=\"Merata Mita\"><PERSON><PERSON></a>, New Zealand director, producer, and screenwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ta\" title=\"<PERSON><PERSON> Mita\"><PERSON><PERSON></a>, New Zealand director, producer, and screenwriter (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ta"}]}, {"year": "2011", "text": "<PERSON>, American tennis player (b. 1919)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American fashion designer, co-founded the Lifted Research Group (b. 1977)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, co-founded the <a href=\"https://wikipedia.org/wiki/Lifted_Research_Group\" title=\"Lifted Research Group\">Lifted Research Group</a> (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, co-founded the <a href=\"https://wikipedia.org/wiki/Lifted_Research_Group\" title=\"Lifted Research Group\">Lifted Research Group</a> (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lifted Research Group", "link": "https://wikipedia.org/wiki/Lifted_Research_Group"}]}, {"year": "2011", "text": "<PERSON>, Virgin Islander lawyer and politician, Lieutenant Governor of the United States Virgin Islands (b. 1941)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Virgin Islander lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_the_United_States_Virgin_Islands\" title=\"Lieutenant Governor of the United States Virgin Islands\">Lieutenant Governor of the United States Virgin Islands</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Virgin Islander lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_the_United_States_Virgin_Islands\" title=\"Lieutenant Governor of the United States Virgin Islands\">Lieutenant Governor of the United States Virgin Islands</a> (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of the United States Virgin Islands", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_the_United_States_Virgin_Islands"}]}, {"year": "2011", "text": "<PERSON>, German-Dutch psychoanalyst and author (b. 1909)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch psychoanalyst and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch psychoanalyst and author (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English admiral and politician, Lieutenant Governor of Guernsey (b. 1918)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Guernsey\" title=\"Lieutenant Governor of Guernsey\">Lieutenant Governor of Guernsey</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Guernsey\" title=\"Lieutenant Governor of Guernsey\">Lieutenant Governor of Guernsey</a> (b. 1918)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Royal_Navy_officer)"}, {"title": "Lieutenant Governor of Guernsey", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Guernsey"}]}, {"year": "2011", "text": "<PERSON>, American football player and manager (b. 1925)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and manager (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and manager (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English cinematographer (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cinematographer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cinematographer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and judge (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, German race car driver and publisher (b. 1911)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver and publisher (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver and publisher (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American basketball player and coach (b. 1959)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Orlando_Woolridge\" title=\"Orlando Woolridge\"><PERSON></a>, American basketball player and coach (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Woolridge\" title=\"Orlando Woolridge\"><PERSON></a>, American basketball player and coach (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Woolridge"}]}, {"year": "2013", "text": "<PERSON>, American physicist and academic (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Scottish author and educator (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and educator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and educator (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American author and poet (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_M%C3%A9ndez"}]}, {"year": "2013", "text": "<PERSON>, American engineer and storm chaser (b. 1957)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and storm chaser (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and storm chaser (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Costa Rican environmentalist (b. 1987)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican environmentalist (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican environmentalist (b. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>l"}]}, {"year": "2013", "text": "<PERSON>, American actress (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American journalist (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Brazilian footballer and coach (b. 1952)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and coach (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and coach (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marin<PERSON>_Chagas"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American race car driver (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American race car driver (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Hoss <PERSON>\"><PERSON><PERSON> <PERSON></a>, American race car driver (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hoss_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actress (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American businessman and philanthropist (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Baroness <PERSON>, English author (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baroness <PERSON></a>, English author (b. 1922)", "links": [{"title": "<PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian author and publisher (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, Canadian author and publisher (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, Canadian author and publisher (b. 1917)", "links": [{"title": "<PERSON> (publisher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher)"}]}, {"year": "2016", "text": "<PERSON>, President of the Sahrawi Arab Democratic Republic (1976-2016) (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Sahrawi_politician)\" title=\"<PERSON> (Sahrawi politician)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_the_Sahrawi_Arab_Democratic_Republic\" title=\"President of the Sahrawi Arab Democratic Republic\">President of the Sahrawi Arab Democratic Republic</a> (1976-2016) (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Sahrawi_politician)\" title=\"<PERSON> (Sahrawi politician)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_the_Sahrawi_Arab_Democratic_Republic\" title=\"President of the Sahrawi Arab Democratic Republic\">President of the Sahrawi Arab Democratic Republic</a> (1976-2016) (b. 1947)", "links": [{"title": "<PERSON> (Sahrawi politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_(Sahrawi_politician)"}, {"title": "President of the Sahrawi Arab Democratic Republic", "link": "https://wikipedia.org/wiki/President_of_the_Sahrawi_Arab_Democratic_Republic"}]}, {"year": "2016", "text": "<PERSON>, American televangelist, co-founder of the Trinity Broadcasting Network (b. 1938)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American televangelist, co-founder of the <a href=\"https://wikipedia.org/wiki/Trinity_Broadcasting_Network\" title=\"Trinity Broadcasting Network\">Trinity Broadcasting Network</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American televangelist, co-founder of the <a href=\"https://wikipedia.org/wiki/Trinity_Broadcasting_Network\" title=\"Trinity Broadcasting Network\">Trinity Broadcasting Network</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Trinity Broadcasting Network", "link": "https://wikipedia.org/wiki/Trinity_Broadcasting_Network"}]}, {"year": "2016", "text": "<PERSON>, English television writer (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television writer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television writer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, German journalist and humanitarian (b. 1939)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and humanitarian (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and humanitarian (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American concept artist and director (b.1932)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American concept artist and director (b.1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American concept artist and director (b.1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Indian singer (b. 1968)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer (b. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Colombian drug lord (b.1939)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Gilberto_Rodr%C3%ADguez_Orejuela\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian <a href=\"https://wikipedia.org/wiki/Drug_lord\" title=\"Drug lord\">drug lord</a> (b.1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gilbert<PERSON>_<PERSON>r%C3%ADguez_Orejuela\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian <a href=\"https://wikipedia.org/wiki/Drug_lord\" title=\"Drug lord\">drug lord</a> (b.1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gilberto_Rodr%C3%ADguez_Orejuela"}, {"title": "Drug lord", "link": "https://wikipedia.org/wiki/Drug_lord"}]}, {"year": "2022", "text": "<PERSON>, English cricketer (b. 1931)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1931)\" title=\"<PERSON> (cricketer, born 1931)\"><PERSON></a>, English cricketer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1931)\" title=\"<PERSON> (cricketer, born 1931)\"><PERSON></a>, English cricketer (b. 1931)", "links": [{"title": "<PERSON> (cricketer, born 1931)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1931)"}]}, {"year": "2024", "text": "<PERSON>, Canadian serial killer (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian serial killer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian serial killer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Mother of <PERSON> (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mother of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mother of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}]}}