{"date": "May 26", "url": "https://wikipedia.org/wiki/May_26", "data": {"Events": [{"year": "17", "text": "<PERSON><PERSON> celebrates a triumph in Rome for his victories over the Cherusci, Chatti, and other German tribes west of the Elbe.", "html": "17 - <a href=\"https://wikipedia.org/wiki/Germanicus\" title=\"Germanicus\">Germanicus</a> celebrates a <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumph</a> in Rome for his victories over the <a href=\"https://wikipedia.org/wiki/Cherusci\" title=\"Cherusci\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cha<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, and other <a href=\"https://wikipedia.org/wiki/Germanic_peoples\" title=\"Germanic peoples\">German tribes</a> west of the <a href=\"https://wikipedia.org/wiki/Elbe\" title=\"Elbe\">Elbe</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Germanicus\" title=\"Germanicus\">Germanicus</a> celebrates a <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumph</a> in Rome for his victories over the <a href=\"https://wikipedia.org/wiki/Cherusci\" title=\"Cherusci\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, and other <a href=\"https://wikipedia.org/wiki/Germanic_peoples\" title=\"Germanic peoples\">German tribes</a> west of the <a href=\"https://wikipedia.org/wiki/Elbe\" title=\"Elbe\">Elbe</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Germanicus"}, {"title": "Roman triumph", "link": "https://wikipedia.org/wiki/Roman_triumph"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Germanic peoples", "link": "https://wikipedia.org/wiki/Germanic_peoples"}, {"title": "Elbe", "link": "https://wikipedia.org/wiki/Elbe"}]}, {"year": "451", "text": "Battle of Avarayr between Armenian rebels and the Sasanian Empire takes place. The Sasanids defeat the Armenians militarily but guarantee them freedom to openly practice Christianity.", "html": "451 - <a href=\"https://wikipedia.org/wiki/Battle_of_Avarayr\" title=\"Battle of Avarayr\">Battle of Avarayr</a> between <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenian</a> rebels and the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian Empire</a> takes place. The Sasanids defeat the Armenians militarily but guarantee them freedom to openly practice <a href=\"https://wikipedia.org/wiki/Christianity\" title=\"Christianity\">Christianity</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Avarayr\" title=\"Battle of Avarayr\">Battle of Avarayr</a> between <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenian</a> rebels and the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian Empire</a> takes place. The Sasanids defeat the Armenians militarily but guarantee them freedom to openly practice <a href=\"https://wikipedia.org/wiki/Christianity\" title=\"Christianity\">Christianity</a>.", "links": [{"title": "Battle of Avarayr", "link": "https://wikipedia.org/wiki/Battle_of_Avarayr"}, {"title": "Armenians", "link": "https://wikipedia.org/wiki/Armenians"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}, {"title": "Christianity", "link": "https://wikipedia.org/wiki/Christianity"}]}, {"year": "946", "text": "England is left temporarily without a monarch after the death of King <PERSON> in a street fight, resulting in <PERSON>'s brother <PERSON><PERSON><PERSON> assuming the throne for the minority of <PERSON>'s two sons.", "html": "946 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> is left temporarily without a monarch after the death of King <a href=\"https://wikipedia.org/wiki/<PERSON>_I\" title=\"<PERSON>\"><PERSON> I</a> in a street fight, resulting in <PERSON>'s brother <a href=\"https://wikipedia.org/wiki/Eadred\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> assuming the throne for the minority of <PERSON>'s two sons.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> is left temporarily without a monarch after the death of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a> in a street fight, resulting in <PERSON>'s brother <a href=\"https://wikipedia.org/wiki/Eadred\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> assuming the throne for the minority of <PERSON>'s two sons.", "links": [{"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eadred"}]}, {"year": "961", "text": "King <PERSON> elects his six-year-old son <PERSON> as heir apparent and co-ruler of the East Frankish Kingdom. He is crowned at Aachen, and placed under the tutelage of his grandmother <PERSON>.", "html": "961 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON> I</a> elects his six-year-old son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> as <a href=\"https://wikipedia.org/wiki/Heir_apparent\" title=\"Heir apparent\">heir apparent</a> and co-ruler of the <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Frankish Kingdom</a>. He is crowned at <a href=\"https://wikipedia.org/wiki/Aachen\" title=\"Aachen\">A<PERSON><PERSON></a>, and placed under the tutelage of his grandmother <a href=\"https://wikipedia.org/wiki/Matilda_of_Ringelheim\" title=\"<PERSON> of Ringelheim\">Matilda</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON> I</a> elects his six-year-old son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> as <a href=\"https://wikipedia.org/wiki/Heir_apparent\" title=\"Heir apparent\">heir apparent</a> and co-ruler of the <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Frankish Kingdom</a>. He is crowned at <a href=\"https://wikipedia.org/wiki/Aachen\" title=\"Aachen\">A<PERSON><PERSON></a>, and placed under the tutelage of his grandmother <a href=\"https://wikipedia.org/wiki/Matilda_of_Ringelheim\" title=\"Matilda of Ringelheim\">Matilda</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Heir apparent", "link": "https://wikipedia.org/wiki/Heir_apparent"}, {"title": "East Francia", "link": "https://wikipedia.org/wiki/East_Francia"}, {"title": "Aachen", "link": "https://wikipedia.org/wiki/Aachen"}, {"title": "<PERSON> of Ringelheim", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ringelheim"}]}, {"year": "1135", "text": "<PERSON> of León and Castile is crowned in León Cathedral as Imperator to<PERSON> (Emperor of all of Spain).", "html": "1135 - <a href=\"https://wikipedia.org/wiki/Alfonso_VII_of_Le%C3%B3n_and_Castile\" title=\"Alfonso VII of León and Castile\"><PERSON> VII of León and Castile</a> is crowned in <a href=\"https://wikipedia.org/wiki/Le%C3%B3n_Cathedral\" title=\"León Cathedral\">León Cathedral</a> as <i><a href=\"https://wikipedia.org/wiki/Imperator_totius_Hispaniae\" title=\"Imperator totius Hispaniae\">Imperator totius Hispaniae</a></i> (<i>Emperor of all of Spain</i>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonso_VII_of_Le%C3%B3n_and_Castile\" title=\"Alfonso VII of León and Castile\"><PERSON> VII of León and Castile</a> is crowned in <a href=\"https://wikipedia.org/wiki/Le%C3%B3n_Cathedral\" title=\"León Cathedral\">León Cathedral</a> as <i><a href=\"https://wikipedia.org/wiki/Imperator_totius_Hispaniae\" title=\"Imperator totius Hispaniae\">Imperator totius Hispaniae</a></i> (<i>Emperor of all of Spain</i>).", "links": [{"title": "Alfonso VII of León and Castile", "link": "https://wikipedia.org/wiki/Alfonso_VII_of_Le%C3%B3n_and_Castile"}, {"title": "León Cathedral", "link": "https://wikipedia.org/wiki/Le%C3%B3n_Cathedral"}, {"title": "Imperator to<PERSON>", "link": "https://wikipedia.org/wiki/Imperator_totius_Hispaniae"}]}, {"year": "1293", "text": "An earthquake strikes Kamakura, Kanagawa, Japan, killing about 23,000.", "html": "1293 - An <a href=\"https://wikipedia.org/wiki/1293_Kamakura_earthquake\" title=\"1293 Kamakura earthquake\">earthquake strikes Kamakura, Kanagawa</a>, Japan, killing about 23,000.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1293_Kamakura_earthquake\" title=\"1293 Kamakura earthquake\">earthquake strikes Kamakura, Kanagawa</a>, Japan, killing about 23,000.", "links": [{"title": "1293 Kamakura earthquake", "link": "https://wikipedia.org/wiki/1293_Kamakura_earthquake"}]}, {"year": "1328", "text": "<PERSON> Ockham, the Franciscan Minister-General <PERSON> Cesena, and two other Franciscan leaders secretly leave Avignon, fearing a death sentence from <PERSON>.", "html": "1328 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ockham\"><PERSON> Ockham</a>, the <a href=\"https://wikipedia.org/wiki/Franciscan\" class=\"mw-redirect\" title=\"Franciscan\">Franciscan</a> Minister-General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cesena\"><PERSON> of Cesena</a>, and two other Franciscan leaders secretly leave <a href=\"https://wikipedia.org/wiki/Avignon\" title=\"Avignon\">Avignon</a>, fearing a death sentence from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John <PERSON>\">Pope <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ockham\"><PERSON>ham</a>, the <a href=\"https://wikipedia.org/wiki/Franciscan\" class=\"mw-redirect\" title=\"Franciscan\">Franciscan</a> Minister-General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cesena\"><PERSON> of Cesena</a>, and two other Franciscan leaders secretly leave <a href=\"https://wikipedia.org/wiki/Avignon\" title=\"Avignon\">Avignon</a>, fearing a death sentence from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a>.", "links": [{"title": "<PERSON> of Ockham", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Franciscan", "link": "https://wikipedia.org/wiki/Franciscan"}, {"title": "<PERSON> of Cesena", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Avignon", "link": "https://wikipedia.org/wiki/Avignon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1538", "text": "Geneva expels <PERSON> and his followers from the city. <PERSON> lives in exile in Strasbourg for the next three years.", "html": "1538 - <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a> expels <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his followers from the city. <PERSON> lives in exile in <a href=\"https://wikipedia.org/wiki/Strasbourg\" title=\"Strasbourg\">Strasbourg</a> for the next three years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a> expels <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his followers from the city. <PERSON> lives in exile in <a href=\"https://wikipedia.org/wiki/Strasbourg\" title=\"Strasbourg\">Strasbourg</a> for the next three years.", "links": [{"title": "Geneva", "link": "https://wikipedia.org/wiki/Geneva"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Strasbourg", "link": "https://wikipedia.org/wiki/Strasbourg"}]}, {"year": "1573", "text": "The Battle of Haarlemmermeer, a naval engagement in the Eighty Years' War.", "html": "1573 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Haarlemmermeer\" title=\"Battle of Haarlemmermeer\">Battle of Haarlemmermeer</a>, a naval engagement in the <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Haarlemmermeer\" title=\"Battle of Haarlemmermeer\">Battle of Haarlemmermeer</a>, a naval engagement in the <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>.", "links": [{"title": "Battle of Haarlemmermeer", "link": "https://wikipedia.org/wiki/Battle_of_Haarlemmermeer"}, {"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}]}, {"year": "1637", "text": "Pequot War: A combined English and Mohegan force under <PERSON> attacks a village in Connecticut, massacring approximately 500 Pequots.", "html": "1637 - <a href=\"https://wikipedia.org/wiki/Pequot_War\" title=\"Pequot War\">Pequot War</a>: A combined English and <a href=\"https://wikipedia.org/wiki/Mohegan\" title=\"Mohegan\"><PERSON><PERSON>gan</a> force under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(c._1600%E2%80%931672)\" class=\"mw-redirect\" title=\"<PERSON> (c. 1600-1672)\"><PERSON></a> attacks a village in <a href=\"https://wikipedia.org/wiki/Connecticut\" title=\"Connecticut\">Connecticut</a>, <a href=\"https://wikipedia.org/wiki/Mystic_massacre\" title=\"Mystic massacre\">massacring</a> approximately 500 <a href=\"https://wikipedia.org/wiki/Pequot\" class=\"mw-redirect\" title=\"Pequot\">Pequots</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pequot_War\" title=\"Pequot War\">Pequot War</a>: A combined English and <a href=\"https://wikipedia.org/wiki/Mohegan\" title=\"Mohegan\"><PERSON><PERSON>gan</a> force under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(c._1600%E2%80%931672)\" class=\"mw-redirect\" title=\"<PERSON> (c. 1600-1672)\"><PERSON></a> attacks a village in <a href=\"https://wikipedia.org/wiki/Connecticut\" title=\"Connecticut\">Connecticut</a>, <a href=\"https://wikipedia.org/wiki/Mystic_massacre\" title=\"Mystic massacre\">massacring</a> approximately 500 <a href=\"https://wikipedia.org/wiki/Pequot\" class=\"mw-redirect\" title=\"Pequot\">Pequots</a>.", "links": [{"title": "Pequot War", "link": "https://wikipedia.org/wiki/Pequot_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON> (c. 1600-1672)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(c._1600%E2%80%931672)"}, {"title": "Connecticut", "link": "https://wikipedia.org/wiki/Connecticut"}, {"title": "Mystic massacre", "link": "https://wikipedia.org/wiki/Mystic_massacre"}, {"title": "Pequot", "link": "https://wikipedia.org/wiki/Pequot"}]}, {"year": "1644", "text": "Portuguese Restoration War: Portuguese and Spanish forces both claim victory in the Battle of Montijo.", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Portuguese_Restoration_War\" title=\"Portuguese Restoration War\">Portuguese Restoration War</a>: Portuguese and Spanish forces both claim victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Montijo\" title=\"Battle of Montijo\">Battle of Montijo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Portuguese_Restoration_War\" title=\"Portuguese Restoration War\">Portuguese Restoration War</a>: Portuguese and Spanish forces both claim victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Montijo\" title=\"Battle of Montijo\">Battle of Montijo</a>.", "links": [{"title": "Portuguese Restoration War", "link": "https://wikipedia.org/wiki/Portuguese_Restoration_War"}, {"title": "Battle of Montijo", "link": "https://wikipedia.org/wiki/Battle_of_Montijo"}]}, {"year": "1736", "text": "The Battle of Ackia is fought near the present site of Tupelo, Mississippi. British and Chickasaw soldiers repel a French and Choctaw attack on the then-Chickasaw village of Ackia.", "html": "1736 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Ackia\" class=\"mw-redirect\" title=\"Battle of Ackia\">Battle of Ackia</a> is fought near the present site of <a href=\"https://wikipedia.org/wiki/Tupelo,_Mississippi\" title=\"Tupelo, Mississippi\">Tupelo, Mississippi</a>. British and <a href=\"https://wikipedia.org/wiki/Chickasaw\" title=\"Chickasaw\">Chickasaw</a> soldiers repel a French and <a href=\"https://wikipedia.org/wiki/Choctaw\" title=\"Choctaw\">Choctaw</a> attack on the then-Chickasaw village of Ackia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Ackia\" class=\"mw-redirect\" title=\"Battle of Ackia\">Battle of Ackia</a> is fought near the present site of <a href=\"https://wikipedia.org/wiki/Tupelo,_Mississippi\" title=\"Tupelo, Mississippi\">Tupelo, Mississippi</a>. British and <a href=\"https://wikipedia.org/wiki/Chickasaw\" title=\"Chickasaw\">Chickasaw</a> soldiers repel a French and <a href=\"https://wikipedia.org/wiki/Choctaw\" title=\"Choctaw\">Choctaw</a> attack on the then-Chickasaw village of Ackia.", "links": [{"title": "Battle of Ackia", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>ckia"}, {"title": "Tupelo, Mississippi", "link": "https://wikipedia.org/wiki/Tupelo,_Mississippi"}, {"title": "Chickasaw", "link": "https://wikipedia.org/wiki/Chickasaw"}, {"title": "Choctaw", "link": "https://wikipedia.org/wiki/Choctaw"}]}, {"year": "1783", "text": "A Great Jubilee Day held at North Stratford, Connecticut, celebrates the end of fighting in the American Revolutionary War.", "html": "1783 - <a href=\"https://wikipedia.org/wiki/A_Great_Jubilee_Day\" title=\"A Great Jubilee Day\">A Great Jubilee Day</a> held at <a href=\"https://wikipedia.org/wiki/Trumbull,_Connecticut\" title=\"Trumbull, Connecticut\">North Stratford, Connecticut</a>, celebrates the end of fighting in the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A_Great_Jubilee_Day\" title=\"A Great Jubilee Day\">A Great Jubilee Day</a> held at <a href=\"https://wikipedia.org/wiki/Trumbull,_Connecticut\" title=\"Trumbull, Connecticut\">North Stratford, Connecticut</a>, celebrates the end of fighting in the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>.", "links": [{"title": "A Great Jubilee Day", "link": "https://wikipedia.org/wiki/A_Great_Jubilee_Day"}, {"title": "Trumbull, Connecticut", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>,_Connecticut"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1805", "text": "<PERSON><PERSON><PERSON><PERSON> assumes the title of King of Italy and is crowned with the Iron Crown of Lombardy in Milan Cathedral, the gothic cathedral in Milan.", "html": "1805 - <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> assumes the title of <a href=\"https://wikipedia.org/wiki/King_of_Italy\" title=\"King of Italy\">King of Italy</a> and is crowned with the <a href=\"https://wikipedia.org/wiki/Iron_Crown_of_Lombardy\" class=\"mw-redirect\" title=\"Iron Crown of Lombardy\">Iron Crown of Lombardy</a> in <a href=\"https://wikipedia.org/wiki/Milan_Cathedral\" title=\"Milan Cathedral\">Milan Cathedral</a>, the <a href=\"https://wikipedia.org/wiki/Gothic_architecture\" title=\"Gothic architecture\">gothic cathedral</a> in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> assumes the title of <a href=\"https://wikipedia.org/wiki/King_of_Italy\" title=\"King of Italy\">King of Italy</a> and is crowned with the <a href=\"https://wikipedia.org/wiki/Iron_Crown_of_Lombardy\" class=\"mw-redirect\" title=\"Iron Crown of Lombardy\">Iron Crown of Lombardy</a> in <a href=\"https://wikipedia.org/wiki/Milan_Cathedral\" title=\"Milan Cathedral\">Milan Cathedral</a>, the <a href=\"https://wikipedia.org/wiki/Gothic_architecture\" title=\"Gothic architecture\">gothic cathedral</a> in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "King of Italy", "link": "https://wikipedia.org/wiki/King_of_Italy"}, {"title": "Iron Crown of Lombardy", "link": "https://wikipedia.org/wiki/Iron_Crown_of_Lombardy"}, {"title": "Milan Cathedral", "link": "https://wikipedia.org/wiki/Milan_Cathedral"}, {"title": "Gothic architecture", "link": "https://wikipedia.org/wiki/Gothic_architecture"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}]}, {"year": "1821", "text": "Establishment of the Peloponnesian Senate by the Greek rebels.", "html": "1821 - Establishment of the <a href=\"https://wikipedia.org/wiki/Peloponnesian_Senate\" title=\"Peloponnesian Senate\">Peloponnesian Senate</a> by the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek rebels</a>.", "no_year_html": "Establishment of the <a href=\"https://wikipedia.org/wiki/Peloponnesian_Senate\" title=\"Peloponnesian Senate\">Peloponnesian Senate</a> by the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek rebels</a>.", "links": [{"title": "Peloponnesian Senate", "link": "https://wikipedia.org/wiki/Peloponnesian_Senate"}, {"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}]}, {"year": "1822", "text": "At least 113 people die in the Grue Church fire, the biggest fire disaster in Norway's history.", "html": "1822 - At least 113 people die in the <a href=\"https://wikipedia.org/wiki/Grue_Church_fire\" title=\"Grue Church fire\">Grue Church fire</a>, the biggest fire disaster in Norway's history.", "no_year_html": "At least 113 people die in the <a href=\"https://wikipedia.org/wiki/Grue_Church_fire\" title=\"Grue Church fire\">Grue Church fire</a>, the biggest fire disaster in Norway's history.", "links": [{"title": "Grue Church fire", "link": "https://wikipedia.org/wiki/Grue_Church_fire"}]}, {"year": "1864", "text": "Montana is organized as a United States territory.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a> is organized as a <a href=\"https://wikipedia.org/wiki/United_States_territory\" class=\"mw-redirect\" title=\"United States territory\">United States territory</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a> is organized as a <a href=\"https://wikipedia.org/wiki/United_States_territory\" class=\"mw-redirect\" title=\"United States territory\">United States territory</a>.", "links": [{"title": "Montana", "link": "https://wikipedia.org/wiki/Montana"}, {"title": "United States territory", "link": "https://wikipedia.org/wiki/United_States_territory"}]}, {"year": "1865", "text": "Conclusion of the American Civil War: The Confederate General <PERSON>, commander of the Trans-Mississippi division, is the last full general of the Confederate Army to surrender, at Galveston, Texas.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Conclusion_of_the_American_Civil_War\" title=\"Conclusion of the American Civil War\">Conclusion of the American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, commander of the <a href=\"https://wikipedia.org/wiki/Trans-Mississippi\" title=\"Trans-Mississippi\">Trans-Mississippi</a> division, is the last full general of the <a href=\"https://wikipedia.org/wiki/Confederate_Army\" class=\"mw-redirect\" title=\"Confederate Army\">Confederate Army</a> to surrender, at <a href=\"https://wikipedia.org/wiki/Galveston,_Texas\" title=\"Galveston, Texas\">Galveston, Texas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Conclusion_of_the_American_Civil_War\" title=\"Conclusion of the American Civil War\">Conclusion of the American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, commander of the <a href=\"https://wikipedia.org/wiki/Trans-Mississippi\" title=\"Trans-Mississippi\">Trans-Mississippi</a> division, is the last full general of the <a href=\"https://wikipedia.org/wiki/Confederate_Army\" class=\"mw-redirect\" title=\"Confederate Army\">Confederate Army</a> to surrender, at <a href=\"https://wikipedia.org/wiki/Galveston,_Texas\" title=\"Galveston, Texas\">Galveston, Texas</a>.", "links": [{"title": "Conclusion of the American Civil War", "link": "https://wikipedia.org/wiki/Conclusion_of_the_American_Civil_War"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Trans-Mississippi", "link": "https://wikipedia.org/wiki/Trans-Mississippi"}, {"title": "Confederate Army", "link": "https://wikipedia.org/wiki/Confederate_Army"}, {"title": "Galveston, Texas", "link": "https://wikipedia.org/wiki/Galveston,_Texas"}]}, {"year": "1868", "text": "Impeachment of <PERSON>: President <PERSON> is acquitted by one vote in the United States Senate.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>\" title=\"Impeachment of <PERSON>\">Impeachment of <PERSON></a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is acquitted by one vote in the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>\" title=\"Impeachment of <PERSON>\">Impeachment of <PERSON></a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is acquitted by one vote in the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a>.", "links": [{"title": "Impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "1869", "text": "Boston University is chartered by the Commonwealth of Massachusetts.", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Boston_University\" title=\"Boston University\">Boston University</a> is chartered by the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Massachusetts\" class=\"mw-redirect\" title=\"Commonwealth of Massachusetts\">Commonwealth of Massachusetts</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boston_University\" title=\"Boston University\">Boston University</a> is chartered by the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Massachusetts\" class=\"mw-redirect\" title=\"Commonwealth of Massachusetts\">Commonwealth of Massachusetts</a>.", "links": [{"title": "Boston University", "link": "https://wikipedia.org/wiki/Boston_University"}, {"title": "Commonwealth of Massachusetts", "link": "https://wikipedia.org/wiki/Commonwealth_of_Massachusetts"}]}, {"year": "1879", "text": "Russia and the United Kingdom sign the Treaty of Gandamak establishing an Afghan state.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a> and the United Kingdom sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Gandamak\" title=\"Treaty of Gandamak\">Treaty of Gandamak</a> establishing an <a href=\"https://wikipedia.org/wiki/Afghan_state\" class=\"mw-redirect\" title=\"Afghan state\">Afghan state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a> and the United Kingdom sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Gandamak\" title=\"Treaty of Gandamak\">Treaty of Gandamak</a> establishing an <a href=\"https://wikipedia.org/wiki/Afghan_state\" class=\"mw-redirect\" title=\"Afghan state\">Afghan state</a>.", "links": [{"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Treaty of Gandamak", "link": "https://wikipedia.org/wiki/Treaty_of_Gandamak"}, {"title": "Afghan state", "link": "https://wikipedia.org/wiki/Afghan_state"}]}, {"year": "1896", "text": "<PERSON> is crowned as the last Tsar of Imperial Russia.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> II of Russia\"><PERSON> II</a> is <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>_and_<PERSON>\" title=\"Coronation of <PERSON> and <PERSON>\">crowned</a> as the last <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> of <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Imperial Russia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"Nicholas II of Russia\"><PERSON> II</a> is <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Coronation of <PERSON> and <PERSON>\">crowned</a> as the last <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> of <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Imperial Russia</a>.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}, {"title": "Coronation of <PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "Tsar", "link": "https://wikipedia.org/wiki/Tsar"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}]}, {"year": "1896", "text": "Charles Dow publishes the first edition of the Dow Jones Industrial Average.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes the first edition of the <a href=\"https://wikipedia.org/wiki/Dow_Jones_Industrial_Average\" title=\"Dow Jones Industrial Average\">Dow Jones Industrial Average</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes the first edition of the <a href=\"https://wikipedia.org/wiki/Dow_Jones_Industrial_Average\" title=\"Dow Jones Industrial Average\">Dow Jones Industrial Average</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dow Jones Industrial Average", "link": "https://wikipedia.org/wiki/<PERSON>_Jones_Industrial_Average"}]}, {"year": "1900", "text": "Thousand Days' War: The Colombian Conservative Party turns the tide of war in their favor with victory against the Colombian Liberal Party in the Battle of Palonegro.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Thousand_Days%27_War\" title=\"Thousand Days' War\">Thousand Days' War</a>: The <a href=\"https://wikipedia.org/wiki/Colombian_Conservative_Party\" title=\"Colombian Conservative Party\">Colombian Conservative Party</a> turns the tide of war in their favor with victory against the <a href=\"https://wikipedia.org/wiki/Colombian_Liberal_Party\" title=\"Colombian Liberal Party\">Colombian Liberal Party</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Palonegro\" title=\"Battle of Palonegro\">Battle of Palonegro</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thousand_Days%27_War\" title=\"Thousand Days' War\">Thousand Days' War</a>: The <a href=\"https://wikipedia.org/wiki/Colombian_Conservative_Party\" title=\"Colombian Conservative Party\">Colombian Conservative Party</a> turns the tide of war in their favor with victory against the <a href=\"https://wikipedia.org/wiki/Colombian_Liberal_Party\" title=\"Colombian Liberal Party\">Colombian Liberal Party</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Palonegro\" title=\"Battle of Palonegro\">Battle of Palonegro</a>.", "links": [{"title": "Thousand Days' War", "link": "https://wikipedia.org/wiki/Thousand_Days%27_War"}, {"title": "Colombian Conservative Party", "link": "https://wikipedia.org/wiki/Colombian_Conservative_Party"}, {"title": "Colombian Liberal Party", "link": "https://wikipedia.org/wiki/Colombian_Liberal_Party"}, {"title": "Battle of Palonegro", "link": "https://wikipedia.org/wiki/Battle_of_Palonegro"}]}, {"year": "1903", "text": "Românul de la Pind, the longest-running newspaper by and about Aromanians until World War II, is founded.", "html": "1903 - <i><a href=\"https://wikipedia.org/wiki/Rom%C3%A2nul_de_la_Pind\" title=\"Românul de la Pind\">Românul de la Pind</a></i>, the longest-running newspaper by and about <a href=\"https://wikipedia.org/wiki/Aromanians\" title=\"Aromanians\">Aromanians</a> until <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>, is founded.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Rom%C3%A2nul_de_la_Pind\" title=\"Românul de la Pind\">Românul de la Pind</a></i>, the longest-running newspaper by and about <a href=\"https://wikipedia.org/wiki/Aromanians\" title=\"Aromanians\">Aromanians</a> until <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>, is founded.", "links": [{"title": "Românul de la Pind", "link": "https://wikipedia.org/wiki/Rom%C3%A2nul_de_la_Pind"}, {"title": "Aromanians", "link": "https://wikipedia.org/wiki/Aromanians"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1908", "text": "The first major commercial oil strike in the Middle East is made at Masjed Soleyman in southwest Persia. The rights to the resource were quickly acquired by the Anglo-Persian Oil Company.", "html": "1908 - The first major commercial oil strike in the Middle East is made at <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> in southwest <a href=\"https://wikipedia.org/wiki/Persia\" class=\"mw-redirect\" title=\"Persia\">Persia</a>. The rights to the resource were quickly acquired by the <a href=\"https://wikipedia.org/wiki/Anglo-Persian_Oil_Company\" title=\"Anglo-Persian Oil Company\">Anglo-Persian Oil Company</a>.", "no_year_html": "The first major commercial oil strike in the Middle East is made at <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> in southwest <a href=\"https://wikipedia.org/wiki/Persia\" class=\"mw-redirect\" title=\"Persia\">Persia</a>. The rights to the resource were quickly acquired by the <a href=\"https://wikipedia.org/wiki/Anglo-Persian_Oil_Company\" title=\"Anglo-Persian Oil Company\">Anglo-Persian Oil Company</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Persia", "link": "https://wikipedia.org/wiki/Persia"}, {"title": "Anglo-Persian Oil Company", "link": "https://wikipedia.org/wiki/Anglo-Persian_Oil_Company"}]}, {"year": "1918", "text": "The Democratic Republic of Georgia is established.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Georgia\" title=\"Democratic Republic of Georgia\">Democratic Republic of Georgia</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Georgia\" title=\"Democratic Republic of Georgia\">Democratic Republic of Georgia</a> is established.", "links": [{"title": "Democratic Republic of Georgia", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_Georgia"}]}, {"year": "1923", "text": "The first 24 Hours of Le Mans is held in France. Run annually in June thereafter, it became the oldest endurance racing event in the world.", "html": "1923 - The first <a href=\"https://wikipedia.org/wiki/24_Hours_of_Le_Mans\" title=\"24 Hours of Le Mans\">24 Hours of Le Mans</a> is held in France. Run annually in June thereafter, it became the oldest <a href=\"https://wikipedia.org/wiki/Endurance_racing_(motorsport)\" title=\"Endurance racing (motorsport)\">endurance racing</a> event in the world.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/24_Hours_of_Le_Mans\" title=\"24 Hours of Le Mans\">24 Hours of Le Mans</a> is held in France. Run annually in June thereafter, it became the oldest <a href=\"https://wikipedia.org/wiki/Endurance_racing_(motorsport)\" title=\"Endurance racing (motorsport)\">endurance racing</a> event in the world.", "links": [{"title": "24 Hours of Le Mans", "link": "https://wikipedia.org/wiki/24_Hours_of_<PERSON>_Mans"}, {"title": "Endurance racing (motorsport)", "link": "https://wikipedia.org/wiki/Endurance_racing_(motorsport)"}]}, {"year": "1927", "text": "The last Ford Model T rolls off the assembly line after a production run of 15,007,003 vehicles.", "html": "1927 - The last <a href=\"https://wikipedia.org/wiki/Ford_Model_T\" title=\"Ford Model T\">Ford Model T</a> rolls off the <a href=\"https://wikipedia.org/wiki/Assembly_line\" title=\"Assembly line\">assembly line</a> after a production run of 15,007,003 vehicles.", "no_year_html": "The last <a href=\"https://wikipedia.org/wiki/Ford_Model_T\" title=\"Ford Model T\">Ford Model T</a> rolls off the <a href=\"https://wikipedia.org/wiki/Assembly_line\" title=\"Assembly line\">assembly line</a> after a production run of 15,007,003 vehicles.", "links": [{"title": "Ford Model T", "link": "https://wikipedia.org/wiki/Ford_Model_T"}, {"title": "Assembly line", "link": "https://wikipedia.org/wiki/Assembly_line"}]}, {"year": "1936", "text": "In the House of Commons of Northern Ireland, <PERSON> begins speaking on the Appropriation bill. By the time he sits down in the early hours of the following morning, he had spoken for ten hours.", "html": "1936 - In the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_Northern_Ireland\" title=\"House of Commons of Northern Ireland\">House of Commons of Northern Ireland</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins speaking on the <a href=\"https://wikipedia.org/wiki/Appropriation_bill\" title=\"Appropriation bill\">Appropriation bill</a>. By the time he sits down in the early hours of the following morning, he had spoken for ten hours.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_Northern_Ireland\" title=\"House of Commons of Northern Ireland\">House of Commons of Northern Ireland</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins speaking on the <a href=\"https://wikipedia.org/wiki/Appropriation_bill\" title=\"Appropriation bill\">Appropriation bill</a>. By the time he sits down in the early hours of the following morning, he had spoken for ten hours.", "links": [{"title": "House of Commons of Northern Ireland", "link": "https://wikipedia.org/wiki/House_of_Commons_of_Northern_Ireland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Appropriation bill", "link": "https://wikipedia.org/wiki/Appropriation_bill"}]}, {"year": "1937", "text": "<PERSON> and members of the United Auto Workers (UAW) clash with Ford Motor Company security guards at the River Rouge Complex complex in Dearborn, Michigan, during the Battle of the Overpass.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and members of the <a href=\"https://wikipedia.org/wiki/United_Auto_Workers\" title=\"United Auto Workers\">United Auto Workers</a> (UAW) clash with <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> security guards at the <a href=\"https://wikipedia.org/wiki/Ford_River_Rouge_Complex\" class=\"mw-redirect\" title=\"Ford River Rouge Complex\">River Rouge Complex</a> complex in <a href=\"https://wikipedia.org/wiki/Dearborn,_Michigan\" title=\"Dearborn, Michigan\">Dearborn, Michigan</a>, during the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Overpass\" title=\"Battle of the Overpass\">Battle of the Overpass</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and members of the <a href=\"https://wikipedia.org/wiki/United_Auto_Workers\" title=\"United Auto Workers\">United Auto Workers</a> (UAW) clash with <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> security guards at the <a href=\"https://wikipedia.org/wiki/Ford_River_Rouge_Complex\" class=\"mw-redirect\" title=\"Ford River Rouge Complex\">River Rouge Complex</a> complex in <a href=\"https://wikipedia.org/wiki/Dearborn,_Michigan\" title=\"Dearborn, Michigan\">Dearborn, Michigan</a>, during the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Overpass\" title=\"Battle of the Overpass\">Battle of the Overpass</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Auto Workers", "link": "https://wikipedia.org/wiki/United_Auto_Workers"}, {"title": "Ford Motor Company", "link": "https://wikipedia.org/wiki/Ford_Motor_Company"}, {"title": "Ford River Rouge Complex", "link": "https://wikipedia.org/wiki/Ford_River_Rouge_Complex"}, {"title": "Dearborn, Michigan", "link": "https://wikipedia.org/wiki/Dearborn,_Michigan"}, {"title": "Battle of the Overpass", "link": "https://wikipedia.org/wiki/Battle_of_the_Overpass"}]}, {"year": "1938", "text": "In the United States, the House Un-American Activities Committee begins its first session.", "html": "1938 - In the United States, the <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Un-American Activities Committee</a> begins its first session.", "no_year_html": "In the United States, the <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Un-American Activities Committee</a> begins its first session.", "links": [{"title": "House Un-American Activities Committee", "link": "https://wikipedia.org/wiki/House_Un-American_Activities_Committee"}]}, {"year": "1940", "text": "World War II: Operation Dynamo: In northern France, Allied forces begin a massive evacuation from Dunkirk, France. The Battle of Dunkirk begins simultaneously as Allied defenders fight to slow down the German offensive.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Dunkirk_evacuation\" title=\"Dunkirk evacuation\">Operation Dynamo</a>: In northern France, <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces begin a massive evacuation from <a href=\"https://wikipedia.org/wiki/Dunkirk\" title=\"Dunkirk\">Dunkirk</a>, France. The <a href=\"https://wikipedia.org/wiki/Battle_of_Dunkirk\" title=\"Battle of Dunkirk\">Battle of Dunkirk</a> begins simultaneously as Allied defenders fight to slow down the <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> offensive.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Dunkirk_evacuation\" title=\"Dunkirk evacuation\">Operation Dynamo</a>: In northern France, <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces begin a massive evacuation from <a href=\"https://wikipedia.org/wiki/Dunkirk\" title=\"Dunkirk\">Dunkirk</a>, France. The <a href=\"https://wikipedia.org/wiki/Battle_of_Dunkirk\" title=\"Battle of Dunkirk\">Battle of Dunkirk</a> begins simultaneously as Allied defenders fight to slow down the <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> offensive.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Dunkirk evacuation", "link": "https://wikipedia.org/wiki/Dunkirk_evacuation"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Dunkirk", "link": "https://wikipedia.org/wiki/Dunkirk"}, {"title": "Battle of Dunkirk", "link": "https://wikipedia.org/wiki/Battle_of_Dunkirk"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1940", "text": "World War II: The Siege of Calais ends with the surrender of the British and French garrison.", "html": "1940 - World War II: The <a href=\"https://wikipedia.org/wiki/Siege_of_Calais_(1940)\" title=\"Siege of Calais (1940)\">Siege of Calais</a> ends with the surrender of the British and French garrison.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Siege_of_Calais_(1940)\" title=\"Siege of Calais (1940)\">Siege of Calais</a> ends with the surrender of the British and French garrison.", "links": [{"title": "Siege of Calais (1940)", "link": "https://wikipedia.org/wiki/Siege_of_Calais_(1940)"}]}, {"year": "1942", "text": "World War II: The Battle of Gazala begins, in present-day Libya.", "html": "1942 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Gazala\" title=\"Battle of Gazala\">Battle of Gazala</a> begins, in present-day <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Gazala\" title=\"Battle of Gazala\">Battle of Gazala</a> begins, in present-day <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>.", "links": [{"title": "Battle of Gazala", "link": "https://wikipedia.org/wiki/Battle_of_Gazala"}, {"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}]}, {"year": "1948", "text": "The U.S. Congress passes Public Law 80-557, which permanently establishes the Civil Air Patrol as an auxiliary of the United States Air Force.", "html": "1948 - The U.S. Congress passes <a href=\"https://wikipedia.org/wiki/Public_Law_80-557\" class=\"mw-redirect\" title=\"Public Law 80-557\">Public Law 80-557</a>, which permanently establishes the <a href=\"https://wikipedia.org/wiki/Civil_Air_Patrol\" title=\"Civil Air Patrol\">Civil Air Patrol</a> as an auxiliary of the <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a>.", "no_year_html": "The U.S. Congress passes <a href=\"https://wikipedia.org/wiki/Public_Law_80-557\" class=\"mw-redirect\" title=\"Public Law 80-557\">Public Law 80-557</a>, which permanently establishes the <a href=\"https://wikipedia.org/wiki/Civil_Air_Patrol\" title=\"Civil Air Patrol\">Civil Air Patrol</a> as an auxiliary of the <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a>.", "links": [{"title": "Public Law 80-557", "link": "https://wikipedia.org/wiki/Public_Law_80-557"}, {"title": "Civil Air Patrol", "link": "https://wikipedia.org/wiki/Civil_Air_Patrol"}, {"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}]}, {"year": "1966", "text": "British Guiana gains independence, becoming Guyana.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/British_Guiana\" title=\"British Guiana\">British Guiana</a> gains independence, becoming <a href=\"https://wikipedia.org/wiki/Guyana\" title=\"Guyana\">Guyana</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Guiana\" title=\"British Guiana\">British Guiana</a> gains independence, becoming <a href=\"https://wikipedia.org/wiki/Guyana\" title=\"Guyana\">Guyana</a>.", "links": [{"title": "British Guiana", "link": "https://wikipedia.org/wiki/British_Guiana"}, {"title": "Guyana", "link": "https://wikipedia.org/wiki/Guyana"}]}, {"year": "1967", "text": "The Beatles' album <PERSON><PERSON>'s Lonely Hearts Club Band is released.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a>' album <i><a href=\"https://wikipedia.org/wiki/Sgt._Pepper%27s_Lonely_Hearts_Club_Band\" title=\"Sgt<PERSON> Pepper's Lonely Hearts Club Band\">Sgt<PERSON> Pepper's Lonely Hearts Club Band</a></i> is released.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a>' album <i><a href=\"https://wikipedia.org/wiki/Sgt._Pepper%27s_Lonely_Hearts_Club_Band\" title=\"Sgt<PERSON> Pepper's Lonely Hearts Club Band\">Sgt<PERSON> Pepper's Lonely Hearts Club Band</a></i> is released.", "links": [{"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}, {"title": "<PERSON><PERSON>'s Lonely Hearts Club Band", "link": "https://wikipedia.org/wiki/Sgt._Pepper%27s_Lonely_Hearts_Club_Band"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in Iceland: Traffic changes from driving on the left to driving on the right overnight.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/H-dagurinn\" title=\"H-dagurinn\"><PERSON>-dagurinn</a> in Iceland: Traffic changes from driving on the left to driving on the right overnight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H-dagurinn\" title=\"H-dagurinn\"><PERSON>-dagurinn</a> in Iceland: Traffic changes from driving on the left to driving on the right overnight.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-da<PERSON><PERSON>n"}]}, {"year": "1969", "text": "Apollo program: Apollo 10 returns to Earth after a successful eight-day test of all the components needed for the forthcoming first crewed Moon landing.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_10\" title=\"Apollo 10\">Apollo 10</a> returns to Earth after a successful eight-day test of all the components needed for the forthcoming first crewed <a href=\"https://wikipedia.org/wiki/Moon_landing\" title=\"Moon landing\">Moon landing</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_10\" title=\"Apollo 10\">Apollo 10</a> returns to Earth after a successful eight-day test of all the components needed for the forthcoming first crewed <a href=\"https://wikipedia.org/wiki/Moon_landing\" title=\"Moon landing\">Moon landing</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 10", "link": "https://wikipedia.org/wiki/Apollo_10"}, {"title": "Moon landing", "link": "https://wikipedia.org/wiki/Moon_landing"}]}, {"year": "1970", "text": "The Soviet Tupolev Tu-144 becomes the first commercial transport to exceed Mach 2.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-144\" title=\"Tupolev Tu-144\">Tupolev Tu-144</a> becomes the first commercial transport to exceed <a href=\"https://wikipedia.org/wiki/Speed_of_sound\" title=\"Speed of sound\">Mach 2</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-144\" title=\"Tupolev Tu-144\">Tupolev Tu-144</a> becomes the first commercial transport to exceed <a href=\"https://wikipedia.org/wiki/Speed_of_sound\" title=\"Speed of sound\">Mach 2</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Tupolev Tu-144", "link": "https://wikipedia.org/wiki/Tupolev_Tu-144"}, {"title": "Speed of sound", "link": "https://wikipedia.org/wiki/Speed_of_sound"}]}, {"year": "1971", "text": "Bangladesh Liberation War: The Pakistan Army slaughters at least 71 Hindus in Burunga, Sylhet, Bangladesh.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Bangladesh_Liberation_War\" title=\"Bangladesh Liberation War\">Bangladesh Liberation War</a>: The <a href=\"https://wikipedia.org/wiki/Pakistan_Army\" title=\"Pakistan Army\">Pakistan Army</a> <a href=\"https://wikipedia.org/wiki/Burunga_massacre\" title=\"Burunga massacre\">slaughters</a> at least 71 Hindus in Burunga, <a href=\"https://wikipedia.org/wiki/Sylhet\" title=\"Sylhet\">Sylhet</a>, <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bangladesh_Liberation_War\" title=\"Bangladesh Liberation War\">Bangladesh Liberation War</a>: The <a href=\"https://wikipedia.org/wiki/Pakistan_Army\" title=\"Pakistan Army\">Pakistan Army</a> <a href=\"https://wikipedia.org/wiki/Burunga_massacre\" title=\"Burunga massacre\">slaughters</a> at least 71 Hindus in Burunga, <a href=\"https://wikipedia.org/wiki/Sylhet\" title=\"Sylhet\">Sylhet</a>, <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "links": [{"title": "Bangladesh Liberation War", "link": "https://wikipedia.org/wiki/Bangladesh_Liberation_War"}, {"title": "Pakistan Army", "link": "https://wikipedia.org/wiki/Pakistan_Army"}, {"title": "Burunga massacre", "link": "https://wikipedia.org/wiki/Burunga_massacre"}, {"title": "<PERSON>yl<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sylhet"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "1972", "text": "The United States and the Soviet Union sign the Anti-Ballistic Missile Treaty.", "html": "1972 - The United States and the Soviet Union sign the <a href=\"https://wikipedia.org/wiki/Anti-Ballistic_Missile_Treaty\" title=\"Anti-Ballistic Missile Treaty\">Anti-Ballistic Missile Treaty</a>.", "no_year_html": "The United States and the Soviet Union sign the <a href=\"https://wikipedia.org/wiki/Anti-Ballistic_Missile_Treaty\" title=\"Anti-Ballistic Missile Treaty\">Anti-Ballistic Missile Treaty</a>.", "links": [{"title": "Anti-Ballistic Missile Treaty", "link": "https://wikipedia.org/wiki/Anti-Ballistic_Missile_Treaty"}]}, {"year": "1981", "text": "Italian Prime Minister <PERSON><PERSON><PERSON> and his coalition cabinet resign following a scandal over membership of the pseudo-masonic lodge P2 (Propaganda Due).", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Italian Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and his coalition cabinet resign following a scandal over membership of the <a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">pseudo-masonic</a> lodge <a href=\"https://wikipedia.org/wiki/Propaganda_Due\" title=\"Propaganda Due\">P2 <i>(Propaganda Due)</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Italian Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and his coalition cabinet resign following a scandal over membership of the <a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">pseudo-masonic</a> lodge <a href=\"https://wikipedia.org/wiki/Propaganda_Due\" title=\"Propaganda Due\">P2 <i>(Propaganda Due)</i></a>.", "links": [{"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arnal<PERSON>_<PERSON>lani"}, {"title": "Freemasonry", "link": "https://wikipedia.org/wiki/Freemasonry"}, {"title": "Propaganda Due", "link": "https://wikipedia.org/wiki/Propaganda_Due"}]}, {"year": "1981", "text": "An EA-6B Prowler crashes on the flight deck of the aircraft carrier USS Nimitz, killing 14 crewmen and injuring 45 others.", "html": "1981 - An <a href=\"https://wikipedia.org/wiki/Northrop_Grumman_EA-6B_Prowler\" class=\"mw-redirect\" title=\"Northrop Grumman EA-6B Prowler\">EA-6B Prowler</a> crashes on the flight deck of the <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/USS_Nimitz\" title=\"USS Nimitz\">USS <i>Nimitz</i></a>, killing 14 crewmen and injuring 45 others.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Northrop_Grumman_EA-6B_Prowler\" class=\"mw-redirect\" title=\"Northrop Grumman EA-6B Prowler\">EA-6B Prowler</a> crashes on the flight deck of the <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/USS_Nimitz\" title=\"USS Nimitz\">USS <i>Nimitz</i></a>, killing 14 crewmen and injuring 45 others.", "links": [{"title": "Northrop Grumman EA-6B Prowler", "link": "https://wikipedia.org/wiki/Northrop_Grumman_EA-6B_Prowler"}, {"title": "Aircraft carrier", "link": "https://wikipedia.org/wiki/Aircraft_carrier"}, {"title": "USS Nimitz", "link": "https://wikipedia.org/wiki/USS_Nimitz"}]}, {"year": "1983", "text": "The 7.8 Mw  Sea of Japan earthquake shakes northern Honshu with a maximum Mercalli intensity of VIII (Severe). A destructive tsunami is generated that leaves about 100 people dead.", "html": "1983 - The 7.8 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1983_Sea_of_Japan_earthquake\" title=\"1983 Sea of Japan earthquake\">Sea of Japan earthquake</a> shakes northern <a href=\"https://wikipedia.org/wiki/Honshu\" title=\"Honshu\">Honshu</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>). A destructive tsunami is generated that leaves about 100 people dead.", "no_year_html": "The 7.8 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1983_Sea_of_Japan_earthquake\" title=\"1983 Sea of Japan earthquake\">Sea of Japan earthquake</a> shakes northern <a href=\"https://wikipedia.org/wiki/Honshu\" title=\"Honshu\">Honshu</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>). A destructive tsunami is generated that leaves about 100 people dead.", "links": [{"title": "1983 Sea of Japan earthquake", "link": "https://wikipedia.org/wiki/1983_Sea_of_Japan_earthquake"}, {"title": "Honshu", "link": "https://wikipedia.org/wiki/Honshu"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1986", "text": "The European Community adopts the European flag.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/European_Community\" class=\"mw-redirect\" title=\"European Community\">European Community</a> adopts the <a href=\"https://wikipedia.org/wiki/European_flag\" class=\"mw-redirect\" title=\"European flag\">European flag</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/European_Community\" class=\"mw-redirect\" title=\"European Community\">European Community</a> adopts the <a href=\"https://wikipedia.org/wiki/European_flag\" class=\"mw-redirect\" title=\"European flag\">European flag</a>.", "links": [{"title": "European Community", "link": "https://wikipedia.org/wiki/European_Community"}, {"title": "European flag", "link": "https://wikipedia.org/wiki/European_flag"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON> becomes the first elected President of the Republic of Georgia in the post-Soviet era.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Zviad_Gamsakhurdia\" title=\"Zviad Gamsakhurdia\"><PERSON><PERSON><PERSON> Gamsakhur<PERSON></a> becomes the first elected <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President</a> of the <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Republic of Georgia</a> in the post-Soviet era.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zviad_Gamsakhurdia\" title=\"Zviad Gamsakhurdia\"><PERSON><PERSON><PERSON>amsakhur<PERSON></a> becomes the first elected <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President</a> of the <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Republic of Georgia</a> in the post-Soviet era.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zviad_Gamsakhurdia"}, {"title": "President of Georgia", "link": "https://wikipedia.org/wiki/President_of_Georgia"}, {"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}]}, {"year": "1991", "text": "Lauda Air Flight 004 breaks apart in mid-air and crashes in the Phu Toei National Park in the Suphan Buri Province of Thailand, killing all 223 people on board.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Lauda_Air_Flight_004\" title=\"Lauda Air Flight 004\">Lauda Air Flight 004</a> breaks apart in mid-air and crashes in the <a href=\"https://wikipedia.org/wiki/Phu_Toei_National_Park\" title=\"Phu Toei National Park\">Phu Toei National Park</a> in the <a href=\"https://wikipedia.org/wiki/Suphan_Buri_Province\" class=\"mw-redirect\" title=\"Suphan Buri Province\">Suphan Buri Province</a> of <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>, killing all 223 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lauda_Air_Flight_004\" title=\"Lauda Air Flight 004\">Lauda Air Flight 004</a> breaks apart in mid-air and crashes in the <a href=\"https://wikipedia.org/wiki/Phu_Toei_National_Park\" title=\"Phu Toei National Park\">Phu Toei National Park</a> in the <a href=\"https://wikipedia.org/wiki/Suphan_Buri_Province\" class=\"mw-redirect\" title=\"Suphan Buri Province\">Suphan Buri Province</a> of <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>, killing all 223 people on board.", "links": [{"title": "Lauda Air Flight 004", "link": "https://wikipedia.org/wiki/Lauda_Air_Flight_004"}, {"title": "Phu Toei National Park", "link": "https://wikipedia.org/wiki/Phu_Toei_National_Park"}, {"title": "Suphan Buri Province", "link": "https://wikipedia.org/wiki/Suphan_Buri_Province"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}]}, {"year": "1998", "text": "The Supreme Court of the United States rules in New Jersey v. New York that Ellis Island, the historic gateway for millions of immigrants, is mainly in the state of New Jersey, not New York.", "html": "1998 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> rules in <i><a href=\"https://wikipedia.org/wiki/New_Jersey_v._New_York\" title=\"New Jersey v. New York\">New Jersey v. New York</a></i> that <a href=\"https://wikipedia.org/wiki/Ellis_Island\" title=\"Ellis Island\">Ellis Island</a>, the historic gateway for millions of immigrants, is mainly in the state of <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a>, not New York.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> rules in <i><a href=\"https://wikipedia.org/wiki/New_Jersey_v._New_York\" title=\"New Jersey v. New York\">New Jersey v. New York</a></i> that <a href=\"https://wikipedia.org/wiki/Ellis_Island\" title=\"Ellis Island\">Ellis Island</a>, the historic gateway for millions of immigrants, is mainly in the state of <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a>, not New York.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "New Jersey v. New York", "link": "https://wikipedia.org/wiki/New_Jersey_v._New_York"}, {"title": "Ellis Island", "link": "https://wikipedia.org/wiki/Ellis_Island"}, {"title": "New Jersey", "link": "https://wikipedia.org/wiki/New_Jersey"}]}, {"year": "1998", "text": "The first \"National Sorry Day\" is held in Australia. Reconciliation events are held nationally, and attended by over a million people.", "html": "1998 - The first \"<a href=\"https://wikipedia.org/wiki/National_Sorry_Day\" title=\"National Sorry Day\">National Sorry Day</a>\" is held in Australia. Reconciliation events are held nationally, and attended by over a million people.", "no_year_html": "The first \"<a href=\"https://wikipedia.org/wiki/National_Sorry_Day\" title=\"National Sorry Day\">National Sorry Day</a>\" is held in Australia. Reconciliation events are held nationally, and attended by over a million people.", "links": [{"title": "National Sorry Day", "link": "https://wikipedia.org/wiki/National_Sorry_Day"}]}, {"year": "1998", "text": "A MIAT Mongolian Airlines Harbin Y-12 crashes near Erdenet, Orkhon Province, Mongolia, resulting in 28 deaths.", "html": "1998 - A <a href=\"https://wikipedia.org/wiki/MIAT_Mongolian_Airlines\" title=\"MIAT Mongolian Airlines\">MIAT Mongolian Airlines</a> <a href=\"https://wikipedia.org/wiki/Harbin_Y-12\" title=\"Harbin Y-12\">Harbin Y-12</a> <a href=\"https://wikipedia.org/wiki/1998_MIAT_Mongolian_Airlines_crash\" class=\"mw-redirect\" title=\"1998 MIAT Mongolian Airlines crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Erdenet\" title=\"Erdenet\">Erdenet</a>, <a href=\"https://wikipedia.org/wiki/Orkhon_Province\" title=\"Orkhon Province\">Orkhon Province</a>, <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a>, resulting in 28 deaths.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/MIAT_Mongolian_Airlines\" title=\"MIAT Mongolian Airlines\">MIAT Mongolian Airlines</a> <a href=\"https://wikipedia.org/wiki/Harbin_Y-12\" title=\"Harbin Y-12\">Harbin Y-12</a> <a href=\"https://wikipedia.org/wiki/1998_MIAT_Mongolian_Airlines_crash\" class=\"mw-redirect\" title=\"1998 MIAT Mongolian Airlines crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Erdenet\" title=\"Erdenet\">Erdenet</a>, <a href=\"https://wikipedia.org/wiki/Orkhon_Province\" title=\"Orkhon Province\">Orkhon Province</a>, <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a>, resulting in 28 deaths.", "links": [{"title": "MIAT Mongolian Airlines", "link": "https://wikipedia.org/wiki/MIAT_Mongolian_Airlines"}, {"title": "Harbin Y-12", "link": "https://wikipedia.org/wiki/Harbin_Y-12"}, {"title": "1998 MIAT Mongolian Airlines crash", "link": "https://wikipedia.org/wiki/1998_MIAT_Mongolian_Airlines_crash"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erdenet"}, {"title": "Orkhon Province", "link": "https://wikipedia.org/wiki/Orkhon_Province"}, {"title": "Mongolia", "link": "https://wikipedia.org/wiki/Mongolia"}]}, {"year": "2002", "text": "The tugboat <PERSON> collides with a support pier of Interstate 40 on the Arkansas River near Webbers Falls, Oklahoma, resulting in 14 deaths and 11 others injured.", "html": "2002 - The tugboat <i><PERSON></i> <a href=\"https://wikipedia.org/wiki/I-40_bridge_disaster\" title=\"I-40 bridge disaster\">collides</a> with a support pier of Interstate 40 on the <a href=\"https://wikipedia.org/wiki/Arkansas_River\" title=\"Arkansas River\">Arkansas River</a> near <a href=\"https://wikipedia.org/wiki/Webbers_Falls,_Oklahoma\" title=\"Webbers Falls, Oklahoma\">Webbers Falls, Oklahoma</a>, resulting in 14 deaths and 11 others injured.", "no_year_html": "The tugboat <i><PERSON></i> <a href=\"https://wikipedia.org/wiki/I-40_bridge_disaster\" title=\"I-40 bridge disaster\">collides</a> with a support pier of Interstate 40 on the <a href=\"https://wikipedia.org/wiki/Arkansas_River\" title=\"Arkansas River\">Arkansas River</a> near <a href=\"https://wikipedia.org/wiki/Webbers_Falls,_Oklahoma\" title=\"Webbers Falls, Oklahoma\">Webbers Falls, Oklahoma</a>, resulting in 14 deaths and 11 others injured.", "links": [{"title": "I-40 bridge disaster", "link": "https://wikipedia.org/wiki/I-40_bridge_disaster"}, {"title": "Arkansas River", "link": "https://wikipedia.org/wiki/Arkansas_River"}, {"title": "Webbers Falls, Oklahoma", "link": "https://wikipedia.org/wiki/Webbers_Falls,_Oklahoma"}]}, {"year": "2003", "text": "Ukrainian-Mediterranean Airlines Flight 4230 crashes in the Turkish town of Maçka, killing 75.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Ukrainian-Mediterranean_Airlines_Flight_4230\" title=\"Ukrainian-Mediterranean Airlines Flight 4230\">Ukrainian-Mediterranean Airlines Flight 4230</a> crashes in the Turkish town of <a href=\"https://wikipedia.org/wiki/Ma%C3%A7ka\" title=\"Maçka\">Ma<PERSON><PERSON></a>, killing 75.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukrainian-Mediterranean_Airlines_Flight_4230\" title=\"Ukrainian-Mediterranean Airlines Flight 4230\">Ukrainian-Mediterranean Airlines Flight 4230</a> crashes in the Turkish town of <a href=\"https://wikipedia.org/wiki/Ma%C3%A7ka\" title=\"Maçka\">Maçka</a>, killing 75.", "links": [{"title": "Ukrainian-Mediterranean Airlines Flight 4230", "link": "https://wikipedia.org/wiki/Ukrainian-Mediterranean_Airlines_Flight_4230"}, {"title": "Maçka", "link": "https://wikipedia.org/wiki/Ma%C3%A7ka"}]}, {"year": "2008", "text": "Severe flooding begins in eastern and southern China that will ultimately cause 148 deaths and force the evacuation of 1.3 million.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/2008_South_China_floods\" title=\"2008 South China floods\">Severe flooding</a> begins in eastern and southern China that will ultimately cause 148 deaths and force the evacuation of 1.3 million.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2008_South_China_floods\" title=\"2008 South China floods\">Severe flooding</a> begins in eastern and southern China that will ultimately cause 148 deaths and force the evacuation of 1.3 million.", "links": [{"title": "2008 South China floods", "link": "https://wikipedia.org/wiki/2008_South_China_floods"}]}, {"year": "2020", "text": "Protests triggered by the murder of <PERSON> erupt in Minneapolis-Saint Paul, later becoming widespread across the United States and around the world.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_protests\" title=\"<PERSON> protests\">Protests</a> triggered by the <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">murder</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> erupt in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_protests_in_Minneapolis%E2%80%93Saint_<PERSON>\" title=\"<PERSON> protests in Minneapolis-Saint Paul\">Minneapolis-Saint <PERSON></a>, later becoming widespread <a href=\"https://wikipedia.org/wiki/List_of_<PERSON>_<PERSON>_protests_in_the_United_States\" title=\"List of George <PERSON> protests in the United States\">across the United States</a> and <a href=\"https://wikipedia.org/wiki/List_of_<PERSON>_<PERSON>_protests_outside_the_United_States\" title=\"List of <PERSON> protests outside the United States\">around the world</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_protests\" title=\"<PERSON> protests\">Protests</a> triggered by the <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">murder</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> erupt in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_protests_in_Minneapolis%E2%80%93Saint_Paul\" title=\"<PERSON> protests in Minneapolis-Saint Paul\">Minneapolis-Saint Paul</a>, later becoming widespread <a href=\"https://wikipedia.org/wiki/List_of_<PERSON>_<PERSON>_protests_in_the_United_States\" title=\"List of George <PERSON> protests in the United States\">across the United States</a> and <a href=\"https://wikipedia.org/wiki/List_of_<PERSON>_<PERSON>_protests_outside_the_United_States\" title=\"List of <PERSON> protests outside the United States\">around the world</a>.", "links": [{"title": "<PERSON> protests", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_protests"}, {"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> protests in Minneapolis-Saint Paul", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_protests_in_Minneapolis%E2%80%93Saint_<PERSON>"}, {"title": "List of George <PERSON> protests in the United States", "link": "https://wikipedia.org/wiki/List_of_<PERSON>_<PERSON>_protests_in_the_United_States"}, {"title": "List of <PERSON> protests outside the United States", "link": "https://wikipedia.org/wiki/List_of_<PERSON>_<PERSON>_protests_outside_the_United_States"}]}, {"year": "2021", "text": "Ten people are killed in a shooting at a VTA rail yard in San Jose, California, United States.", "html": "2021 - Ten people are killed in a <a href=\"https://wikipedia.org/wiki/2021_San_Jose_shooting\" title=\"2021 San Jose shooting\">shooting</a> at a <a href=\"https://wikipedia.org/wiki/Santa_Clara_Valley_Transportation_Authority\" title=\"Santa Clara Valley Transportation Authority\">VTA</a> <a href=\"https://wikipedia.org/wiki/Rail_yard\" title=\"Rail yard\">rail yard</a> in <a href=\"https://wikipedia.org/wiki/San_Jose,_California\" title=\"San Jose, California\">San Jose, California</a>, <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "no_year_html": "Ten people are killed in a <a href=\"https://wikipedia.org/wiki/2021_San_Jose_shooting\" title=\"2021 San Jose shooting\">shooting</a> at a <a href=\"https://wikipedia.org/wiki/Santa_Clara_Valley_Transportation_Authority\" title=\"Santa Clara Valley Transportation Authority\">VTA</a> <a href=\"https://wikipedia.org/wiki/Rail_yard\" title=\"Rail yard\">rail yard</a> in <a href=\"https://wikipedia.org/wiki/San_Jose,_California\" title=\"San Jose, California\">San Jose, California</a>, <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "links": [{"title": "2021 San Jose shooting", "link": "https://wikipedia.org/wiki/2021_<PERSON>_<PERSON>_shooting"}, {"title": "Santa Clara Valley Transportation Authority", "link": "https://wikipedia.org/wiki/Santa_Clara_Valley_Transportation_Authority"}, {"title": "Rail yard", "link": "https://wikipedia.org/wiki/Rail_yard"}, {"title": "San Jose, California", "link": "https://wikipedia.org/wiki/San_Jose,_California"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}], "Births": [{"year": "1264", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese prince and shōgun (d. 1326)", "html": "1264 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>\" title=\"Prince <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese prince and shōgun (d. 1326)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>\" title=\"Prince <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese prince and sh<PERSON>gun (d. 1326)", "links": [{"title": "Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1478", "text": "<PERSON>, pope of the Catholic Church (d. 1534)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/Pope_Clement_VII\" title=\"Pope Clement VII\"><PERSON></a>, pope of the Catholic Church (d. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_VII\" title=\"Pope Clement VII\"><PERSON></a>, pope of the Catholic Church (d. 1534)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1562", "text": "<PERSON>, margrave of Baden-Hachberg (d. 1590)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Margrave_of_Baden-Hachberg\" title=\"<PERSON>, Margrave of Baden-Hachberg\"><PERSON></a>, margrave of Baden-Hachberg (d. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Mar<PERSON>_of_Baden-Hachberg\" title=\"<PERSON>, Margrave of Baden-Hachberg\"><PERSON></a>, margrave of Baden-Hachberg (d. 1590)", "links": [{"title": "<PERSON>, Margrave of Baden-Hachberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Hachberg"}]}, {"year": "1566", "text": "<PERSON><PERSON><PERSON>, Ottoman sultan (d. 1603)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_III\" title=\"Mehmed III\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (d. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III\" title=\"Mehmed III\"><PERSON><PERSON><PERSON> III</a>, Ottoman sultan (d. 1603)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III"}]}, {"year": "1602", "text": "<PERSON>, Dutch-French painter (d. 1674)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-French painter (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-French painter (d. 1674)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1623", "text": "<PERSON>, English economist and philosopher (d. 1687)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and philosopher (d. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and philosopher (d. 1687)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1650", "text": "<PERSON>, 1st Duke of Marlborough, English general and politician, Lord Lieutenant of Oxfordshire (d. 1722)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Marlborough\" title=\"<PERSON>, 1st Duke of Marlborough\"><PERSON>, 1st Duke of Marlborough</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Oxfordshire\" title=\"Lord Lieutenant of Oxfordshire\">Lord Lieutenant of Oxfordshire</a> (d. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Marlborough\" title=\"<PERSON>, 1st Duke of Marlborough\"><PERSON>, 1st Duke of Marlborough</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Oxfordshire\" title=\"Lord Lieutenant of Oxfordshire\">Lord Lieutenant of Oxfordshire</a> (d. 1722)", "links": [{"title": "<PERSON>, 1st Duke of Marlborough", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Marlborough"}, {"title": "Lord Lieutenant of Oxfordshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Oxfordshire"}]}, {"year": "1667", "text": "<PERSON>, French-English mathematician and theorist (d. 1754)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English mathematician and theorist (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English mathematician and theorist (d. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1669", "text": "<PERSON><PERSON><PERSON><PERSON>, French botanist and mycologist (d. 1722)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French botanist and mycologist (d. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French botanist and mycologist (d. 1722)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1700", "text": "<PERSON><PERSON>, German bishop and saint (d. 1760)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German bishop and saint (d. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German bishop and saint (d. 1760)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, British actuary (d. 1833)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actuary)\" title=\"<PERSON> (actuary)\"><PERSON></a>, British actuary (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actuary)\" title=\"<PERSON> (actuary)\"><PERSON></a>, British actuary (d. 1833)", "links": [{"title": "<PERSON> (actuary)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actuary)"}]}, {"year": "1799", "text": "<PERSON>, German poet and painter (d. 1853)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German poet and painter (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German poet and painter (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>ch"}]}, {"year": "1822", "text": "<PERSON>, French author and critic, founded the Académie Goncourt (d. 1896)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic, founded the <a href=\"https://wikipedia.org/wiki/Acad%C3%A9<PERSON>_<PERSON>\" title=\"Académie Goncourt\">Académie Goncourt</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic, founded the <a href=\"https://wikipedia.org/wiki/Acad%C3%A9<PERSON>_<PERSON>\" title=\"Académie Goncourt\">Académie Goncourt</a> (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Académie Goncourt", "link": "https://wikipedia.org/wiki/Acad%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, English-New Zealand boxer (d. 1917)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand boxer (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand boxer (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American author and illustrator (d. 1933)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON> Teck, English-born queen consort of the United Kingdom (d. 1953)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Teck\"><PERSON> of Teck</a>, English-born queen consort of the United Kingdom (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Teck\"><PERSON> of <PERSON>ck</a>, English-born queen consort of the United Kingdom (d. 1953)", "links": [{"title": "<PERSON> of Teck", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Norwegian painter and illustrator (d. 1958)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter and illustrator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter and illustrator (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, English cricketer (d. 1945)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON>, American composer and educator (d. 1967)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American composer and educator (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer and educator (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON>, Mexican politician and provisional president, 1920 (d. 1955)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican politician and provisional president, 1920 (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican politician and provisional president, 1920 (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, American singer, actress, dancer, and pianist (d. 1946)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, actress, dancer, and pianist (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, actress, dancer, and pianist (d. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American singer and actor (d. 1950)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1887", "text": "<PERSON>, 2nd President of Burma (d. 1963)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Ba_U\" title=\"Ba U\">Ba U</a>, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Burma\" class=\"mw-redirect\" title=\"President of Burma\">President of Burma</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ba_U\" title=\"Ba U\">Ba U</a>, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Burma\" class=\"mw-redirect\" title=\"President of Burma\">President of Burma</a> (d. 1963)", "links": [{"title": "Ba U", "link": "https://wikipedia.org/wiki/Ba_U"}, {"title": "President of Burma", "link": "https://wikipedia.org/wiki/President_of_Burma"}]}, {"year": "1893", "text": "<PERSON>, English conductor and composer (d. 1962)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Go<PERSON>\"><PERSON></a>, English conductor and composer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and composer (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American photographer and journalist (d. 1965)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Hungarian-American actor and singer (d. 1971)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actor and singer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actor and singer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American pianist, composer, and conductor (d. 1990)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Estonian speed skater (d. 1965)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian speed skater (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian speed skater (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Canadian lawyer and politician, 18th Premier of Quebec (d. 1968)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Canadian lawyer and politician, Canadian Speaker of the Senate (d. 1997)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Senate_(Canada)\" class=\"mw-redirect\" title=\"Speaker of the Senate (Canada)\">Canadian Speaker of the Senate</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Senate_(Canada)\" class=\"mw-redirect\" title=\"Speaker of the Senate (Canada)\">Canadian Speaker of the Senate</a> (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the Senate (Canada)", "link": "https://wikipedia.org/wiki/Speaker_of_the_Senate_(Canada)"}]}, {"year": "1900", "text": "<PERSON>, Swedish singer, actress, and writer (d. 1976)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer, actress, and writer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer, actress, and writer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Tatar author and prisoner of war (d. 1983)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Tamur<PERSON>_Dawl<PERSON>chin\" title=\"Tamur<PERSON> Dawletschin\"><PERSON><PERSON><PERSON></a>, Tatar author and prisoner of war (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dawl<PERSON>chin\" title=\"<PERSON><PERSON><PERSON> Dawletschin\"><PERSON><PERSON><PERSON></a>, Tatar author and prisoner of war (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON> Dawletschin", "link": "https://wikipedia.org/wiki/Tamur<PERSON>_Dawletschin"}]}, {"year": "1904", "text": "<PERSON>, English singer-songwriter and actor (d. 1961)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Turkish author, poet, and playwright (d. 1983)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Necip_Faz%C4%B1l_K%C4%B1sak%C3%BCrek\" title=\"Necip <PERSON>azıl Kısakürek\"><PERSON><PERSON><PERSON></a>, Turkish author, poet, and playwright (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Necip_Faz%C4%B1l_K%C4%B1sak%C3%BCrek\" title=\"Necip <PERSON>azıl Kısakürek\"><PERSON><PERSON><PERSON></a>, Turkish author, poet, and playwright (d. 1983)", "links": [{"title": "Necip <PERSON>", "link": "https://wikipedia.org/wiki/Necip_Faz%C4%B1l_K%C4%B1sak%C3%BCrek"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Lithuanian-French pianist and educator (d. 2002)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian-French pianist and educator (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian-French pianist and educator (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, French physician and haematologist (d. 2006)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, French physician and haematologist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, French physician and haematologist (d. 2006)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(physician)"}]}, {"year": "1907", "text": "<PERSON>, American actor, director, and producer (d. 1979)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English actor (d. 1992)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese politician, 1st Prime Minister of the Republic of Vietnam (d. 1976)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ng%E1%BB%8Dc_Th%C6%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese politician, 1st <a href=\"https://wikipedia.org/wiki/Leaders_of_South_Vietnam\" class=\"mw-redirect\" title=\"Leaders of South Vietnam\">Prime Minister of the Republic of Vietnam</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ng%E1%BB%8Dc_Th%C6%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese politician, 1st <a href=\"https://wikipedia.org/wiki/Leaders_of_South_Vietnam\" class=\"mw-redirect\" title=\"Leaders of South Vietnam\">Prime Minister of the Republic of Vietnam</a> (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ng%E1%BB%8Dc_Th%C6%A1"}, {"title": "Leaders of South Vietnam", "link": "https://wikipedia.org/wiki/Leaders_of_South_Vietnam"}]}, {"year": "1909", "text": "<PERSON>, Scottish footballer and manager (d. 1994)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Mexican politician, 48th President of Mexico (d. 1969)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Adolfo_L%C3%B3pez_Mateos\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican politician, 48th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf<PERSON>_L%C3%B3pez_Mateos\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican politician, 48th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolfo_L%C3%B3<PERSON>z_Mateo<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Hungarian-Israeli martial artist, boxer, and gymnast (d. 1998)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-Israeli martial artist, boxer, and gymnast (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-Israeli martial artist, boxer, and gymnast (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, French actor and cellist (d. 2005)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and cellist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and cellist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American playwright, screenwriter, and producer (d. 1992)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, screenwriter, and producer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, screenwriter, and producer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Hungarian mechanic and politician, 46th Prime Minister of Hungary (d. 1989)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian mechanic and politician, 46th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Prime Minister of Hungary</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian mechanic and politician, 46th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Prime Minister of Hungary</a> (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r"}, {"title": "List of Prime Ministers of Hungary", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary"}]}, {"year": "1912", "text": "<PERSON>, Canadian-American actor (d. 1980)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English actor (d. 1994)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, French author (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Swedish actress (d. 1976)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, German weightlifter (d. 1991)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German weightlifter (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German weightlifter (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American dancer and choreographer (d. 2009)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American bassist (d. 2004)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Vernon_Alley\" title=\"Vernon Alley\"><PERSON></a>, American bassist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vernon_Alley\" title=\"Vernon Alley\"><PERSON></a>, American bassist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vernon_Alley"}]}, {"year": "1915", "text": "<PERSON><PERSON>, English author (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Antonia_Forest\" title=\"Antonia Forest\">Antonia Forest</a>, English author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antonia_Forest\" title=\"Antonia Forest\">Antonia Forest</a>, English author (d. 2003)", "links": [{"title": "Antonia Forest", "link": "https://wikipedia.org/wiki/Antonia_Forest"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Dutch journalist and author (d. 1972)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist and author (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist and author (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Cuban pianist (d. 2003)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Gonz%C3%<PERSON><PERSON><PERSON>_(pianist)\" title=\"<PERSON><PERSON><PERSON> (pianist)\"><PERSON><PERSON><PERSON></a>, Cuban pianist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Gonz%C3%<PERSON><PERSON><PERSON>_(pianist)\" title=\"<PERSON><PERSON><PERSON> (pianist)\"><PERSON><PERSON><PERSON></a>, Cuban pianist (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON> (pianist)", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Gonz%C3%<PERSON><PERSON><PERSON>_(pianist)"}]}, {"year": "1920", "text": "<PERSON>, South African cricketer (d. 1980)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American singer-songwriter and actress (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, German soprano (d. 2018)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Inge_Borkh\" title=\"Inge Borkh\">In<PERSON> Bo<PERSON></a>, German soprano (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inge_Borkh\" title=\"Inge Borkh\">In<PERSON> Bo<PERSON></a>, German soprano (d. 2018)", "links": [{"title": "Inge Borkh", "link": "https://wikipedia.org/wiki/Inge_Borkh"}]}, {"year": "1923", "text": "<PERSON>, American actor (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English actor (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Cuban-Mexican actress (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-Mexican actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-Mexican actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English actor (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American trumpet player, composer, and bandleader (d. 1991)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and bandleader (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and bandleader (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, French actor and businessman (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and businessman (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and businessman (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American pathologist, author, and assisted suicide activist (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pathologist, author, and assisted suicide activist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pathologist, author, and assisted suicide activist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON> <PERSON><PERSON>, Nigerian historian and academic (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Nigerian historian and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Nigerian historian and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian television personality and producer (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television personality and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television personality and producer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>,  Australian bioinorganic chemist and protein crystallographer (d. 2008)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bioinorganic chemist and protein crystallographer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bioinorganic chemist and protein crystallographer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, French singer and actress (d. 1998)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer and actress (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer and actress (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Indian-Iranian lexicographer and critic (d. 2005)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Iranian lexicographer and critic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Iranian lexicographer and critic (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ami"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Estonian philosopher and academic", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lo<PERSON>\"><PERSON><PERSON></a>, Estonian philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian philosopher and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>_<PERSON>one"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Russian-Polish poet and activist (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Polish poet and activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Polish poet and activist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Indian actress and singer (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Tamil_actress)\" title=\"<PERSON><PERSON> (Tamil actress)\"><PERSON><PERSON></a>, Indian actress and singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Tamil_actress)\" title=\"<PERSON><PERSON> (Tamil actress)\"><PERSON><PERSON></a>, Indian actress and singer (d. 2015)", "links": [{"title": "<PERSON><PERSON> (Tamil actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(Tamil_actress)"}]}, {"year": "1937", "text": "<PERSON>, American politician, 59th Governor of Kentucky", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 59th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 59th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Kentucky", "link": "https://wikipedia.org/wiki/Governor_of_Kentucky"}]}, {"year": "1938", "text": "<PERSON>, American pianist and composer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_Bo<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, British engineer (d. 2019)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British engineer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British engineer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Russian author and playwright", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian author and playwright", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Indian director and producer (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian director and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian director and producer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian soprano and actress", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Canadian academic and politician, Deputy Premier of Quebec", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>rembla<PERSON>\" title=\"<PERSON><PERSON>rembla<PERSON>\"><PERSON><PERSON></a>, Canadian academic and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_Quebec\" title=\"Deputy Premier of Quebec\">Deputy Premier of Quebec</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian academic and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_Quebec\" title=\"Deputy Premier of Quebec\">Deputy Premier of Quebec</a>", "links": [{"title": "<PERSON><PERSON>Tremblay", "link": "https://wikipedia.org/wiki/Mon<PERSON>_<PERSON>-<PERSON>remblay"}, {"title": "Deputy Premier of Quebec", "link": "https://wikipedia.org/wiki/Deputy_Premier_of_Quebec"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American singer-songwriter, drummer, producer, and actor (d. 2012)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Helm\"><PERSON><PERSON></a>, American singer-songwriter, drummer, producer, and actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lm\" title=\"<PERSON><PERSON> Helm\"><PERSON><PERSON></a>, American singer-songwriter, drummer, producer, and actor (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lm"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, American CIA officer and criminal", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer and criminal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer and criminal", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "CIA", "link": "https://wikipedia.org/wiki/CIA"}]}, {"year": "1941", "text": "<PERSON>, Scottish microbiologist and politician (d. 2014)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish microbiologist and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish microbiologist and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, South African tennis player and sportscaster", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Cliff_Drysdale\" title=\"Cliff Drysdale\"><PERSON></a>, South African tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cliff_Drysdale\" title=\"Cliff Drysdale\"><PERSON></a>, South African tennis player and sportscaster", "links": [{"title": "Cliff Drysdale", "link": "https://wikipedia.org/wiki/Cliff_Drysdale"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Latvian composer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Imants_Kalni%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_Kalni%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Imants_Kalni%C5%86%C5%A1"}]}, {"year": "1943", "text": "<PERSON>, Dutch swimmer, journalist, and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer, journalist, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer, journalist, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American-Canadian journalist and politician (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist and politician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Norwegian ice hockey player (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian ice hockey player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian ice hockey player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American race car driver and journalist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 17th Chief Minister of Maharashtra (d. 2012)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of Maharashtra", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra"}]}, {"year": "1945", "text": "<PERSON>, English lawyer and judge", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian-American drummer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Bulgarian gymnast and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian gymnast and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian gymnast and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English guitarist, songwriter, and producer (d. 1993)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter, and producer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter, and producer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author and painter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a>, American author and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a>, American author and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carol_O%27Connell"}]}, {"year": "1947", "text": "<PERSON>, New Zealand cricketer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Canadian model and actress (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian model and actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian model and actress (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>don"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stevie Nicks\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stevie_Nicks\" title=\"Stevie Nicks\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, British journalist and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American computer programmer, developed the first wiki", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer, developed the first <a href=\"https://wikipedia.org/wiki/Wiki\" title=\"Wiki\">wiki</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer, developed the first <a href=\"https://wikipedia.org/wiki/Wiki\" title=\"Wiki\">wiki</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Cunningham"}, {"title": "Wiki", "link": "https://wikipedia.org/wiki/Wiki"}]}, {"year": "1949", "text": "<PERSON>, American actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Scottish educator and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1951", "text": "<PERSON>, Spanish lawyer and businessman", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Calder%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Calder%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Calder%C3%B3n"}]}, {"year": "1951", "text": "<PERSON>, Dutch mathematician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Syrian military aviator and cosmonaut (d. 2024)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian military aviator and cosmonaut (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian military aviator and cosmonaut (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American physicist and astronaut, founded <PERSON> Science (d. 2012)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Sally_Ride\" title=\"Sally Ride\">Sally Ride</a>, American physicist and astronaut, founded <a href=\"https://wikipedia.org/wiki/Sally_Ride_Science\" title=\"Sally Ride Science\">Sally Ride Science</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sally_Ride\" title=\"Sally Ride\">Sally Ride</a>, American physicist and astronaut, founded <a href=\"https://wikipedia.org/wiki/Sally_Ride_Science\" title=\"Sally Ride Science\">Sally Ride Science</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Irish educator and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish educator and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American lawyer and politician (d. 2019)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English journalist, politician and TV presenter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, politician and TV presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, politician and TV presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Irish Republican hunger strike participant (d. 1981)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)\" title=\"<PERSON> (hunger striker)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a> <a href=\"https://wikipedia.org/wiki/1981_Irish_hunger_strike\" title=\"1981 Irish hunger strike\">hunger strike</a> participant (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(hunger_striker)\" title=\"<PERSON> (hunger striker)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a> <a href=\"https://wikipedia.org/wiki/1981_Irish_hunger_strike\" title=\"1981 Irish hunger strike\">hunger strike</a> participant (d. 1981)", "links": [{"title": "<PERSON> (hunger striker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)"}, {"title": "Irish Republican", "link": "https://wikipedia.org/wiki/Irish_Republican"}, {"title": "1981 Irish hunger strike", "link": "https://wikipedia.org/wiki/1981_Irish_hunger_strike"}]}, {"year": "1954", "text": "<PERSON>, English novelist, poet, short story writer, and translator", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, poet, short story writer, and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, poet, short story writer, and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian businessman and politician, 29th Canadian Minister of Transport", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_of_Transport_(Canada)\" title=\"Minister of Transport (Canada)\">Canadian Minister of Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_of_Transport_(Canada)\" title=\"Minister of Transport (Canada)\">Canadian Minister of Transport</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Transport (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Transport_(Canada)"}]}, {"year": "1956", "text": "<PERSON>, English lawyer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Indian academician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian academician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian academician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Colombian singer-songwriter (d. 2013)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Diomedes_D%C3%ADaz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diomedes_D%C3%ADaz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian singer-songwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Diomedes_D%C3%ADaz"}]}, {"year": "1957", "text": "<PERSON>, Canadian businessman and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Legault"}]}, {"year": "1957", "text": "<PERSON>, Italian racing driver", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American golfer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Finnish hurdler and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Arto_B<PERSON>\" title=\"Arto Bryggare\"><PERSON><PERSON></a>, Finnish hurdler and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arto_B<PERSON>\" title=\"Arto Brygg<PERSON>\"><PERSON><PERSON></a>, Finnish hurdler and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arto_B<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Danish actor, director, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1960", "text": "<PERSON>, Australian weightlifter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian weightlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian weightlifter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese racing driver", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Lithuanian discus thrower", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Roma<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian discus thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Indian-American director, producer, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English singer-songwriter (d. 2016)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter (d. 2016)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Canadian-American actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actor, director, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Goldthwait\" title=\"<PERSON><PERSON> Goldthwait\"><PERSON><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Goldthwait\" title=\"Bob<PERSON> Goldthwait\"><PERSON><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Goldthwait"}]}, {"year": "1963", "text": "<PERSON>, English poet, playwright and novelist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright and novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright and novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Simon_Armitage"}]}, {"year": "1963", "text": "<PERSON>, Canadian actor and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish-American paleontologist and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Caitl%C3%ADn_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Irish-American paleontologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Caitl%C3%ADn_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Irish-American paleontologist and author", "links": [{"title": "Caitlín <PERSON>", "link": "https://wikipedia.org/wiki/Caitl%C3%ADn_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter, multi-instrumentalist, producer, and actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, multi-instrumentalist, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, multi-instrumentalist, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek basketball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, South African runner", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Zola Budd\"><PERSON><PERSON></a>, South African runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Zola Budd\"><PERSON><PERSON></a>, South African runner", "links": [{"title": "Zola Budd", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Budd"}]}, {"year": "1967", "text": "<PERSON>, Irish milliner, hat designer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish milliner, hat designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish milliner, hat designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Japanese journalist (d. 2012)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese journalist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese journalist (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Spanish director, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_Le%C3%B3n_de_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, King of Denmark", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>k_X_of_Denmark\" class=\"mw-redirect\" title=\"<PERSON>erik X of Denmark\"><PERSON><PERSON><PERSON></a>, King of Denmark", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>k_X_of_Denmark\" class=\"mw-redirect\" title=\"Frederik X of Denmark\"><PERSON><PERSON><PERSON></a>, King of Denmark", "links": [{"title": "Frederik X of Denmark", "link": "https://wikipedia.org/wiki/Frederik_X_of_Denmark"}]}, {"year": "1968", "text": "<PERSON>, English footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian politician, 10th Canadian Minister of Foreign Affairs", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)\" title=\"Minister of Foreign Affairs (Canada)\">Canadian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)\" title=\"Minister of Foreign Affairs (Canada)\">Canadian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Canadian_politician)"}, {"title": "Minister of Foreign Affairs (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American triathlete and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American triathlete and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American triathlete and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Japanese illustrator", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Watsuki\" title=\"<PERSON><PERSON><PERSON> Watsuki\"><PERSON><PERSON><PERSON></a>, Japanese illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Watsuki\" title=\"<PERSON><PERSON><PERSON> Watsuki\"><PERSON><PERSON><PERSON></a>, Japanese illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>uki"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Lebanese footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor, animator, screenwriter, producer, and composer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, animator, screenwriter, producer, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, animator, screenwriter, producer, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian-American photographer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Swedish swimmer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lander\" title=\"<PERSON>\"><PERSON></a>, Swedish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lander\" title=\"<PERSON>\"><PERSON></a>, Swedish swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lars_<PERSON>%C3%B6lander"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English cricketer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian comedian and actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Australian comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Australian comedian and actor", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>(comedian)"}]}, {"year": "1976", "text": "<PERSON>, American mixed martial artist and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American author and cartoonist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and cartoonist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>meier"}]}, {"year": "1977", "text": "<PERSON>, Italian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Japanese actress and model", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1978", "text": "<PERSON>, Australian-Scottish rugby player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Scottish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Scottish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dan_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American wrestler and model (d. 2019)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and model (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and model (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Russian sprinter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Turkish basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Canadian singer and songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English actor, screenwriter, and television host", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, screenwriter, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, screenwriter, and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Turkish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/De<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De<PERSON>_<PERSON>_<PERSON>eeuw"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Greek singer-songwriter and guitarist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Swedish long jumper", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9us\" title=\"<PERSON>\"><PERSON></a>, Swedish long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9us\" title=\"<PERSON>\"><PERSON></a>, Swedish long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Michel_Torn%C3%A9us"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Olcay_%C5%9Eahan\" title=\"<PERSON>l<PERSON><PERSON> Şahan\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olcay_%C5%9Eahan\" title=\"<PERSON>l<PERSON><PERSON> Şahan\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "Olcay Şahan", "link": "https://wikipedia.org/wiki/Olcay_%C5%9Eahan"}]}, {"year": "1988", "text": "<PERSON>, Italian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian discus thrower", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian discus thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian triathlete", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian triathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, South Korean singer and actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Belgian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Myung\"><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Myung\"><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, South African cricketer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Australian cricketer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Georgia_Wareham\" title=\"Georgia Wareham\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_Wareham\" title=\"Georgia Wareham\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georgia_Wareham"}]}, {"year": "2000", "text": "<PERSON><PERSON>, South Korean singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/2000\" title=\"2000\">2000</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2000\" title=\"2000\">2000</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "2000", "link": "https://wikipedia.org/wiki/2000"}, {"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}], "Deaths": [{"year": "604", "text": "<PERSON> of Canterbury, Benedictine monk and archbishop", "html": "604 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Canterbury\" title=\"<PERSON> of Canterbury\"><PERSON> of Canterbury</a>, Benedictine monk and archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Canterbury\" title=\"<PERSON> of Canterbury\"><PERSON> of Canterbury</a>, Benedictine monk and archbishop", "links": [{"title": "<PERSON> of Canterbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Canterbury"}]}, {"year": "735", "text": "<PERSON><PERSON>, English monk, historian, and theologian", "html": "735 - <a href=\"https://wikipedia.org/wiki/<PERSON>e\" title=\"<PERSON>e\"><PERSON><PERSON></a>, English monk, historian, and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English monk, historian, and theologian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>e"}]}, {"year": "818", "text": "<PERSON>, 8th of The Twelve Imams", "html": "818 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> al-Ridha\"><PERSON></a>, 8th of <a href=\"https://wikipedia.org/wiki/The_Twelve_Imams\" class=\"mw-redirect\" title=\"The Twelve Imams\">The Twelve Imams</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> al-Ridha\"><PERSON></a>, 8th of <a href=\"https://wikipedia.org/wiki/The_Twelve_Imams\" class=\"mw-redirect\" title=\"The Twelve Imams\">The Twelve Imams</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Twelve Imams", "link": "https://wikipedia.org/wiki/The_Twelve_Imams"}]}, {"year": "926", "text": "<PERSON>, Chinese general and governor", "html": "926 - <a href=\"https://wikipedia.org/wiki/Yuan_Xingqin\" title=\"<PERSON> Xingqin\"><PERSON></a>, Chinese general and governor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yuan_Xingqin\" title=\"Yuan Xingqin\"><PERSON></a>, Chinese general and governor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Yuan_Xingqin"}]}, {"year": "946", "text": "<PERSON>, king of England (b. 921)", "html": "946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, king of England (b. 921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, king of England (b. 921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1035", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish nobleman (b. 1005)", "html": "1035 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Barcelona\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Count of Barcelona\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish nobleman (b. 1005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Barcelona\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Count of Barcelona\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish nobleman (b. 1005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Count of Barcelona", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Barcelona"}]}, {"year": "1055", "text": "<PERSON><PERSON>, margrave of Austria", "html": "1055 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON>_of_Austria\" title=\"<PERSON><PERSON>, <PERSON><PERSON> of Austria\"><PERSON><PERSON></a>, margrave of Austria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON>_of_Austria\" title=\"<PERSON><PERSON>, <PERSON><PERSON> of Austria\"><PERSON><PERSON></a>, margrave of Austria", "links": [{"title": "<PERSON><PERSON>, Margrave of Austria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON>_of_Austria"}]}, {"year": "1250", "text": "<PERSON>, duke of Brittany", "html": "1250 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON></a>, duke of Brittany", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON></a>, duke of Brittany", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1339", "text": "<PERSON><PERSON><PERSON>, queen of Poland", "html": "1339 - <a href=\"https://wikipedia.org/wiki/Aldona_of_Lithuania\" title=\"Aldona of Lithuania\"><PERSON><PERSON><PERSON></a>, queen of Poland", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aldona_of_Lithuania\" title=\"Aldona of Lithuania\"><PERSON><PERSON><PERSON></a>, queen of Poland", "links": [{"title": "Aldona of Lithuania", "link": "https://wikipedia.org/wiki/Aldona_of_Lithuania"}]}, {"year": "1362", "text": "<PERSON>, king of Naples (b. 1320)", "html": "1362 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> I</a>, king of Naples (b. 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON></a>, king of Naples (b. 1320)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples"}]}, {"year": "1421", "text": "<PERSON><PERSON><PERSON>, Ottoman sultan (b. 1389)", "html": "1421 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1389)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1389)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}]}, {"year": "1512", "text": "<PERSON><PERSON><PERSON>, Ottoman sultan (b. 1447)", "html": "1512 - <a href=\"https://wikipedia.org/wiki/Bayezid_II\" title=\"Bayezid II\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1447)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bayezid_II\" title=\"Bayezid II\"><PERSON><PERSON><PERSON> II</a>, Ottoman sultan (b. 1447)", "links": [{"title": "Bayezid II", "link": "https://wikipedia.org/wiki/Bayezid_II"}]}, {"year": "1536", "text": "<PERSON>, Italian poet (b. 1498)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet (b. 1498)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet (b. 1498)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1552", "text": "<PERSON>, German cartographer and cosmographer (b. 1488)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCnster\" title=\"<PERSON>\"><PERSON></a>, German cartographer and cosmographer (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCnster\" title=\"<PERSON>\"><PERSON></a>, German cartographer and cosmographer (b. 1488)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sebastian_M%C3%BCnster"}]}, {"year": "1648", "text": "<PERSON>, French poet and author (b. 1597)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1597)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Voiture"}]}, {"year": "1653", "text": "<PERSON>, English theorist and author (b. 1588)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theorist and author (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theorist and author (b. 1588)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1679", "text": "<PERSON>, Elector of Bavaria (b. 1636)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria\" title=\"<PERSON>, Elector of Bavaria\"><PERSON></a>, Elector of Bavaria (b. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria\" title=\"<PERSON>, Elector of Bavaria\"><PERSON></a>, Elector of Bavaria (b. 1636)", "links": [{"title": "<PERSON>, Elector of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria"}]}, {"year": "1685", "text": "<PERSON>, German elector palatine (b. 1651)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON></a>, German elector palatine (b. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>latine\" title=\"<PERSON>, Elector <PERSON>\"><PERSON></a>, German elector palatine (b. 1651)", "links": [{"title": "<PERSON>, Elector <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1702", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mughal princess and poet (b. 1638)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-un-<PERSON><PERSON>\" title=\"<PERSON><PERSON>-un-<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON>-<PERSON><PERSON></a>, Mughal princess and poet (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-un-<PERSON><PERSON>\" title=\"<PERSON><PERSON>-un-<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON>-<PERSON><PERSON></a>, Mughal princess and poet (b. 1638)", "links": [{"title": "Zeb-un-Nissa", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON><PERSON>"}]}, {"year": "1703", "text": "<PERSON>, English politician (b. 1633)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1633)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian diplomat (b. 1672)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian diplomat (b. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>ly<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian diplomat (b. 1672)", "links": [{"title": "Pyl<PERSON>p <PERSON>", "link": "https://wikipedia.org/wiki/Pylyp_<PERSON><PERSON>k"}]}, {"year": "1746", "text": "<PERSON>, Irish playwright (b. 1660)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish playwright (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish playwright (b. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, German philosopher and academic (b. 1714)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, Lord <PERSON>, Scottish linguist, biologist, and judge (b. 1714)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish linguist, biologist, and judge (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish linguist, biologist, and judge (b. 1714)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, Russian field marshal and politician, Governor-General of Finland (b. 1761)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> de <PERSON></a>, Russian field marshal and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Finland\" title=\"Governor-General of Finland\">Governor-General of Finland</a> (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> de <PERSON></a>, Russian field marshal and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Finland\" title=\"Governor-General of Finland\">Governor-General of Finland</a> (b. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor-General of Finland", "link": "https://wikipedia.org/wiki/Governor-General_of_Finland"}]}, {"year": "1818", "text": "<PERSON>, Chilean lawyer and guerrilla leader (b. 1785)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_Erdo%C3%ADza\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and guerrilla leader (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_Erdo%C3%ADza\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and guerrilla leader (b. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Rod<PERSON>%C3%ADguez_Erdo%C3%ADza"}]}, {"year": "1824", "text": "<PERSON><PERSON>, English lawyer (b. 1751)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Capel_Lo<PERSON>t\" title=\"Capel Lo<PERSON>t\"><PERSON><PERSON></a>, English lawyer (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Capel_Lofft\" title=\"Capel Lofft\"><PERSON><PERSON></a>, English lawyer (b. 1751)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1840", "text": "<PERSON>, English admiral and politician (b. 1764)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral and politician (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral and politician (b. 1764)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1881", "text": "<PERSON>, German philologist and academic (b. 1824)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and academic (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and academic (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON><PERSON>, Algerian ruler (b. 1808)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Abdelkader_<PERSON>_D<PERSON>zairi\" class=\"mw-redirect\" title=\"Abdelkader El Djezairi\"><PERSON><PERSON><PERSON><PERSON> D<PERSON>zai<PERSON></a>, Algerian ruler (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abdelkader_<PERSON>_<PERSON>zairi\" class=\"mw-redirect\" title=\"Abdelkader El Djezairi\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian ruler (b. 1808)", "links": [{"title": "Abdelkader El Djezairi", "link": "https://wikipedia.org/wiki/Abd<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, American soldier and inventor (b. 1839)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>rowger\" title=\"<PERSON><PERSON> Strowger\"><PERSON><PERSON></a>, American soldier and inventor (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>rowger\" title=\"<PERSON><PERSON> Strowger\"><PERSON><PERSON></a>, American soldier and inventor (b. 1839)", "links": [{"title": "<PERSON><PERSON>rowger", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Strowger"}]}, {"year": "1908", "text": "<PERSON>, Indian religious leader, founded the Ahmadiyya movement (b. 1835)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Ahmad<PERSON><PERSON>\" title=\"Ahmadiyya\">Ahmadiyya movement</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Ahmad<PERSON>\" title=\"Ahmadiyya\">Ahmadiyya movement</a> (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Danish-American journalist, photographer, and reformer (b. 1849)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American journalist, photographer, and reformer (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American journalist, photographer, and reformer (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Irish-American cellist, composer, and conductor, founded the American Society of Composers, Authors and Publishers (b. 1859)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American cellist, composer, and conductor, founded the <a href=\"https://wikipedia.org/wiki/American_Society_of_Composers,_Authors_and_Publishers\" title=\"American Society of Composers, Authors and Publishers\">American Society of Composers, Authors and Publishers</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American cellist, composer, and conductor, founded the <a href=\"https://wikipedia.org/wiki/American_Society_of_Composers,_Authors_and_Publishers\" title=\"American Society of Composers, Authors and Publishers\">American Society of Composers, Authors and Publishers</a> (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Society of Composers, Authors and Publishers", "link": "https://wikipedia.org/wiki/American_Society_of_Composers,_Authors_and_Publishers"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian poet (b. 1904)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Sre%C4%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian poet (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sre%C4%8D<PERSON>_<PERSON>sovel\" title=\"<PERSON><PERSON><PERSON><PERSON> Kosovel\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian poet (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sre%C4%8Dko_<PERSON>vel"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, English financier, journalist, and politician (b. 1860)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>rat<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English financier, journalist, and politician (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English financier, journalist, and politician (b. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Horatio_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1897)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_singer)\" class=\"mw-redirect\" title=\"<PERSON> (country singer)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_singer)\" class=\"mw-redirect\" title=\"<PERSON> (country singer)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1897)", "links": [{"title": "<PERSON> (country singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_singer)"}]}, {"year": "1939", "text": "<PERSON>, American physician, co-founded Mayo Clinic (b. 1865)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, co-founded <a href=\"https://wikipedia.org/wiki/Mayo_Clinic\" title=\"Mayo Clinic\">Mayo Clinic</a> (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, co-founded <a href=\"https://wikipedia.org/wiki/Mayo_Clinic\" title=\"Mayo Clinic\">Mayo Clinic</a> (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayo Clinic", "link": "https://wikipedia.org/wiki/Mayo_Clinic"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American businessman (b. 1893)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ed<PERSON> Ford\"><PERSON><PERSON></a>, American businessman (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ford\"><PERSON><PERSON></a>, American businessman (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Swedish organist, composer, and educator (b. 1864)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r\" title=\"<PERSON>\"><PERSON></a>, Swedish organist, composer, and educator (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r\" title=\"<PERSON>\"><PERSON></a>, Swedish organist, composer, and educator (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alice_Tegn%C3%A9r"}]}, {"year": "1944", "text": "<PERSON>, German SS officer (b. 1885)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Christian_Wirth\" title=\"Christian Wirth\">Christian W<PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_Wirth\" title=\"Christian Wirth\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Wirth"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Swedish actor and director (b. 1896)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Torsten_Bergstr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor and director (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Torsten_Bergstr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor and director (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Torsten_Bergstr%C3%B6m"}]}, {"year": "1951", "text": "<PERSON>, American explorer (b. 1880)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Lincoln_Ellsworth\" title=\"Lincoln Ellsworth\"><PERSON></a>, American explorer (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lincoln_Ellsworth\" title=\"Lincoln Ellsworth\"><PERSON></a>, American explorer (b. 1880)", "links": [{"title": "<PERSON> Ellsworth", "link": "https://wikipedia.org/wiki/Lincoln_Ellsworth"}]}, {"year": "1954", "text": "<PERSON>, Canadian football player and politician (b. 1900)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player and politician (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player and politician (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Italian racing driver (b. 1918)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American baseball player and coach (b. 1902)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American gymnast (b. 1876)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Finnish fraudster (b. 1906)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish fraudster (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish fraudster (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American author and activist (b. 1894)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian racing driver (b. 1937)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian racing driver (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian racing driver (b. 1937)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "1969", "text": "<PERSON>, American engineer, co-founded the Lockheed Corporation (b. 1889)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American engineer, co-founded the <a href=\"https://wikipedia.org/wiki/Lockheed_Corporation\" title=\"Lockheed Corporation\">Lockheed Corporation</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Loughead\"><PERSON></a>, American engineer, co-founded the <a href=\"https://wikipedia.org/wiki/Lockheed_Corporation\" title=\"Lockheed Corporation\">Lockheed Corporation</a> (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lockheed Corporation", "link": "https://wikipedia.org/wiki/Lockheed_Corporation"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Swiss racing driver (b. 1941)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss racing driver (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss racing driver (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, German philosopher and academic (b. 1889)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Greek actress (b. 1887)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actress (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actress (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Irish-American actor (b. 1904)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American journalist (b. 1936)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer and manager (b. 1927)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ie"}]}, {"year": "1994", "text": "<PERSON>, American guitarist (b. 1940)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American animator, director, and producer (b. 1906)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American animator, director, and producer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American animator, director, and producer (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Friz_Freleng"}]}, {"year": "1997", "text": "<PERSON>, American football player and coach (b. 1896)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Swiss conductor and philanthropist (b. 1906)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss conductor and philanthropist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss conductor and philanthropist (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American chemist and engineer (b. 1898)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>aldo Semon\"><PERSON><PERSON></a>, American chemist and engineer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Se<PERSON>\"><PERSON><PERSON></a>, American chemist and engineer (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>mon"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Italian racing driver (b. 1937)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian racing driver (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian racing driver (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American actress (b. 1934)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Zimbabwean soldier and politician, Zimbabwean Minister of Defence (b. 1952)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Moven_<PERSON>\" title=\"<PERSON><PERSON> Ma<PERSON>\"><PERSON><PERSON></a>, Zimbabwean soldier and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Zimbabwe)\" title=\"Ministry of Defence (Zimbabwe)\">Zimbabwean Minister of Defence</a> (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zimbabwean soldier and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Zimbabwe)\" title=\"Ministry of Defence (Zimbabwe)\">Zimbabwean Minister of Defence</a> (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>n_<PERSON><PERSON>"}, {"title": "Ministry of Defence (Zimbabwe)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Zimbabwe)"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Canadian actress and choreographer (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and choreographer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and choreographer (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Ethiopian runner (b. 1932)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>old<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian runner (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>old<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian runner (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>olde"}]}, {"year": "2003", "text": "<PERSON>, American journalist and author (b. 1919)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Russian astronomer (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian astronomer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian astronomer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikolai_<PERSON>ern<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American actor (b. 1906)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Venezuelan baseball player and manager (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and manager (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chico_Carr<PERSON>quel"}]}, {"year": "2005", "text": "<PERSON>, American pianist and educator (b. 1937)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and educator (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and educator (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English businessman, co-founded Lesney Products (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Lesney_Products\" title=\"Lesney Products\">Lesney Products</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Lesney_Products\" title=\"Lesney Products\">Lesney Products</a> (b. 1918)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>(businessman)"}, {"title": "Lesney Products", "link": "https://wikipedia.org/wiki/<PERSON>ney_Products"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, French businessman (b. 1963)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>_(born_1963)\" title=\"<PERSON><PERSON><PERSON> (born 1963)\"><PERSON><PERSON><PERSON></a>, French businessman (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>_(born_1963)\" title=\"<PERSON><PERSON><PERSON> (born 1963)\"><PERSON><PERSON><PERSON></a>, French businessman (b. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON> (born 1963)", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>_(born_1963)"}]}, {"year": "2006", "text": "<PERSON>, Irish footballer and physician (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Flanagan\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and physician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27Flanagan\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and physician (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Flanagan"}]}, {"year": "2007", "text": "<PERSON>, English illustrator (b. 1942)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American basketball player (b. 1948)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1948)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2008", "text": "<PERSON>, American actor, director, and screenwriter (b. 1934)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Sydney_Pollack\" title=\"Sydney Pollack\"><PERSON>ack</a>, American actor, director, and screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Pollack\" title=\"Sydney Pollack\"><PERSON> Pollack</a>, American actor, director, and screenwriter (b. 1934)", "links": [{"title": "<PERSON>ack", "link": "https://wikipedia.org/wiki/Sydney_Pollack"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Lithuanian cyclist (b. 1973)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>onait%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian cyclist (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>onait%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian cyclist (b. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zita_Urbonait%C4%97"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Greek journalist and politician (b. 1941)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek journalist and politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek journalist and politician (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Canadian ice hockey and soccer player (b. 1965)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey and soccer player (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey and soccer player (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Canadian-American radio and television host (b. 1912)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Art_Linkletter\" title=\"Art Linkletter\"><PERSON></a>, Canadian-American radio and television host (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Linkletter\" title=\"<PERSON> Link<PERSON>r\"><PERSON></a>, Canadian-American radio and television host (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Linkletter"}]}, {"year": "2010", "text": "<PERSON>, English air marshal and pilot (b. 1956)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English air marshal and pilot (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English air marshal and pilot (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Irish politician (b. 1949)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish politician (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Sri Lankan scholar, author, and playwright (b. 1920)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ahubudu\"><PERSON><PERSON></a>, Sri Lankan scholar, author, and playwright (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ahubudu\"><PERSON><PERSON></a>, Sri Lankan scholar, author, and playwright (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ubudu"}]}, {"year": "2012", "text": "<PERSON>, Belgian cyclist (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ooter"}]}, {"year": "2012", "text": "<PERSON>, American illustrator (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Welsh captain and footballer (b. 1982)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh captain and footballer (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh captain and footballer (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Japanese politician (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian wrestler (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler (b. 1925)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "2012", "text": "<PERSON>, English-Canadian illustrator (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian illustrator (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian illustrator (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American businessman and politician (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and businessman (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Italian-Brazilian businessman (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Brazilian businessman (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Brazilian businessman (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ivita"}]}, {"year": "2013", "text": "<PERSON>, American football player and coach (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Austrian painter (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American author (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Indian metropolitan (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Baselios_Thoma_Didymos_I\" class=\"mw-redirect\" title=\"Baselios Thoma Didymos I\">Baselios Thoma Didymos I</a>, Indian metropolitan (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baselios_Thoma_Didymos_I\" class=\"mw-redirect\" title=\"Baselios Thoma Didymos I\"><PERSON>ios Thoma Didymos <PERSON></a>, Indian metropolitan (b. 1921)", "links": [{"title": "<PERSON>ios <PERSON>", "link": "https://wikipedia.org/wiki/Basel<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_I"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Serbian-American academic and neuropharmacologist (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-American academic and neuropharmacologist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-American academic and neuropharmacologist (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "2014", "text": "<PERSON>, American physician, journalist, and politician (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, journalist, and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, journalist, and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Iranian-Canadian architect, sculptor, and painter (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>oshang_<PERSON>\" title=\"Hooshang <PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-Canadian architect, sculptor, and painter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>oshang_<PERSON>\" title=\"<PERSON>osh<PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-Canadian architect, sculptor, and painter (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "2015", "text": "<PERSON>, Spanish director, producer, and screenwriter (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director, producer, and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director, producer, and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian politician and diplomat, Australian High Commissioner to New Zealand (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Australian_High_Commissioners_to_New_Zealand\" class=\"mw-redirect\" title=\"List of Australian High Commissioners to New Zealand\">Australian High Commissioner to New Zealand</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Australian_High_Commissioners_to_New_Zealand\" class=\"mw-redirect\" title=\"List of Australian High Commissioners to New Zealand\">Australian High Commissioner to New Zealand</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Australian High Commissioners to New Zealand", "link": "https://wikipedia.org/wiki/List_of_Australian_High_Commissioners_to_New_Zealand"}]}, {"year": "2015", "text": "<PERSON>, American astronomer and academic (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, American astronomer and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, American astronomer and academic (b. 1927)", "links": [{"title": "<PERSON> (astronomer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)"}]}, {"year": "2015", "text": "<PERSON>, Portuguese footballer (b. 1979)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A<PERSON><PERSON>_<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, Portuguese footballer (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A<PERSON><PERSON>_<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, Portuguese footballer (b. 1979)", "links": [{"title": "<PERSON> (footballer, born 1979)", "link": "https://wikipedia.org/wiki/Jo%C3%<PERSON><PERSON><PERSON>_<PERSON>_(footballer,_born_1979)"}]}, {"year": "2016", "text": "<PERSON><PERSON>, German-born American human rights activist and Holocaust survivor (b. 1924)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-born American human rights activist and Holocaust survivor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-born American human rights activist and Holocaust survivor (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish-born American politician (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-born American politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-born American politician (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Former Prime Minister of Thailand (b. 1920)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Pre<PERSON>_Tin<PERSON>\" title=\"Pre<PERSON> Tin<PERSON>\"><PERSON><PERSON></a>, Former Prime Minister of Thailand (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pre<PERSON>_<PERSON>\" title=\"Pre<PERSON>\"><PERSON><PERSON></a>, Former Prime Minister of Thailand (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prem_Tin<PERSON>anonda"}]}, {"year": "2022", "text": "<PERSON>, English musician (b. 1961)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician (b. 1961)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2022", "text": "<PERSON>, American actor (b. 1954)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, English drummer (b. 1949)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Yes_drummer)\" title=\"<PERSON> (Yes drummer)\"><PERSON></a>, English drummer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Yes_drummer)\" title=\"<PERSON> (Yes drummer)\"><PERSON></a>, English drummer (b. 1949)", "links": [{"title": "<PERSON> (Yes drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Yes_drummer)"}]}]}}