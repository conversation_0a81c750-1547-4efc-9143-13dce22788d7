{"date": "April 16", "url": "https://wikipedia.org/wiki/April_16", "data": {"Events": [{"year": "1457 BC", "text": "Battle of Megido - the first battle to have been recorded in what is accepted as relatively reliable detail.", "html": "1457 BC - 1457 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Megiddo_(15th_century_BC)\" title=\"Battle of Megiddo (15th century BC)\">Battle of Megido</a> - the first <a href=\"https://wikipedia.org/wiki/Battle\" title=\"Battle\">battle</a> to have been recorded in what is accepted as relatively reliable detail.", "no_year_html": "1457 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Megiddo_(15th_century_BC)\" title=\"Battle of Megiddo (15th century BC)\">Battle of Megido</a> - the first <a href=\"https://wikipedia.org/wiki/Battle\" title=\"Battle\">battle</a> to have been recorded in what is accepted as relatively reliable detail.", "links": [{"title": "Battle of Megiddo (15th century BC)", "link": "https://wikipedia.org/wiki/Battle_of_Megiddo_(15th_century_BC)"}, {"title": "Battle", "link": "https://wikipedia.org/wiki/Battle"}]}, {"year": "69", "text": "Defeated by <PERSON><PERSON><PERSON><PERSON>' troops at Bedriacum, Roman emperor <PERSON><PERSON><PERSON> commits suicide.", "html": "69 - Defeated by <a href=\"https://wikipedia.org/wiki/Vitelli<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>' troops at Bedriacum, Roman emperor <a href=\"https://wikipedia.org/wiki/Otho\" title=\"Oth<PERSON>\"><PERSON><PERSON><PERSON></a> commits suicide.", "no_year_html": "Defeated by <a href=\"https://wikipedia.org/wiki/Vitelli<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>' troops at Bedriacum, Roman emperor <a href=\"https://wikipedia.org/wiki/Otho\" title=\"Oth<PERSON>\"><PERSON><PERSON><PERSON></a> commits suicide.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitellius"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Otho"}]}, {"year": "73", "text": "Masada, a Jewish fortress, falls to the Romans after several months of siege, ending the First Jewish-Roman War.", "html": "73 - <a href=\"https://wikipedia.org/wiki/Masada\" title=\"<PERSON>sad<PERSON>\">Masada</a>, a Jewish fortress, falls to the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Romans</a> after several months of siege, ending the <a href=\"https://wikipedia.org/wiki/First_Jewish%E2%80%93Roman_War\" title=\"First Jewish-Roman War\">First Jewish-Roman War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Masada</a>, a Jewish fortress, falls to the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Romans</a> after several months of siege, ending the <a href=\"https://wikipedia.org/wiki/First_Jewish%E2%80%93Roman_War\" title=\"First Jewish-Roman War\">First Jewish-Roman War</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}, {"title": "First Jewish-Roman War", "link": "https://wikipedia.org/wiki/First_Jewish%E2%80%93Roman_War"}]}, {"year": "1346", "text": "<PERSON>, \"the Mighty\", is crowned Emperor of the Serbs at Skopje, his empire occupying much of the Balkans.", "html": "1346 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1an\" title=\"<PERSON>\"><PERSON></a>, \"the Mighty\", is crowned <a href=\"https://wikipedia.org/wiki/Emperor_of_the_Serbs\" title=\"Emperor of the Serbs\">Emperor of the Serbs</a> at <a href=\"https://wikipedia.org/wiki/Skopje\" title=\"Skopje\">Skopje</a>, <a href=\"https://wikipedia.org/wiki/Serbian_Empire\" title=\"Serbian Empire\">his empire</a> occupying much of the <a href=\"https://wikipedia.org/wiki/Balkans\" title=\"Balkans\">Balkans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1an\" title=\"<PERSON>\"><PERSON></a>, \"the Mighty\", is crowned <a href=\"https://wikipedia.org/wiki/Emperor_of_the_Serbs\" title=\"Emperor of the Serbs\">Emperor of the Serbs</a> at <a href=\"https://wikipedia.org/wiki/Skopje\" title=\"Skopje\">Skopje</a>, <a href=\"https://wikipedia.org/wiki/Serbian_Empire\" title=\"Serbian Empire\">his empire</a> occupying much of the <a href=\"https://wikipedia.org/wiki/Balkans\" title=\"Balkans\">Balkans</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stefan_Du%C5%A1an"}, {"title": "Emperor of the Serbs", "link": "https://wikipedia.org/wiki/Emperor_of_the_Serbs"}, {"title": "Skopje", "link": "https://wikipedia.org/wiki/Skopje"}, {"title": "Serbian Empire", "link": "https://wikipedia.org/wiki/Serbian_Empire"}, {"title": "Balkans", "link": "https://wikipedia.org/wiki/Balkans"}]}, {"year": "1520", "text": "The Revolt of the Comuneros begins in Spain against the rule of Charles V.", "html": "1520 - The <a href=\"https://wikipedia.org/wiki/Revolt_of_the_Comuneros\" title=\"Revolt of the Comuneros\">Revolt of the Comuneros</a> begins in Spain against the rule of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Revolt_of_the_Comuneros\" title=\"Revolt of the Comuneros\">Revolt of the Comuneros</a> begins in Spain against the rule of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON></a>.", "links": [{"title": "Revolt of the Comuneros", "link": "https://wikipedia.org/wiki/Revolt_of_the_Comuneros"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1582", "text": "Spanish conquistador <PERSON><PERSON><PERSON> founds the settlement of Salta, Argentina.", "html": "1582 - Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistador</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> founds the settlement of <a href=\"https://wikipedia.org/wiki/Salta\" title=\"Salta\">Salta</a>, Argentina.", "no_year_html": "Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistador</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> founds the settlement of <a href=\"https://wikipedia.org/wiki/Salta\" title=\"Salta\">Salta</a>, Argentina.", "links": [{"title": "Conquistador", "link": "https://wikipedia.org/wiki/Conquistador"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Salta", "link": "https://wikipedia.org/wiki/Salta"}]}, {"year": "1746", "text": "The Battle of Culloden is fought between the French-supported Jacobites and the British Hanoverian forces commanded by <PERSON>, Duke of Cumberland, in Scotland.", "html": "1746 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Culloden\" title=\"Battle of Culloden\">Battle of Culloden</a> is fought between the French-supported <a href=\"https://wikipedia.org/wiki/Jacobitism\" title=\"Jacobitism\">Jacobites</a> and the British <a href=\"https://wikipedia.org/wiki/House_of_Hanover\" title=\"House of Hanover\">Hanoverian</a> forces commanded by <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Cumberland\" title=\"Prince <PERSON>, Duke of Cumberland\"><PERSON>, Duke of Cumberland</a>, in Scotland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Culloden\" title=\"Battle of Culloden\">Battle of Culloden</a> is fought between the French-supported <a href=\"https://wikipedia.org/wiki/Jacobitism\" title=\"Jacobitism\">Jacobites</a> and the British <a href=\"https://wikipedia.org/wiki/House_of_Hanover\" title=\"House of Hanover\">Hanoverian</a> forces commanded by <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Cumberland\" title=\"Prince <PERSON>, Duke of Cumberland\"><PERSON>, Duke of Cumberland</a>, in Scotland.", "links": [{"title": "Battle of Culloden", "link": "https://wikipedia.org/wiki/Battle_of_Culloden"}, {"title": "Jacobitism", "link": "https://wikipedia.org/wiki/Jacobitism"}, {"title": "House of Hanover", "link": "https://wikipedia.org/wiki/House_of_Hanover"}, {"title": "<PERSON> <PERSON>, Duke of Cumberland", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Cumberland"}]}, {"year": "1780", "text": "<PERSON> founds the University of Münster.", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCrstenberg\" title=\"<PERSON>\"><PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/University_of_M%C3%BCnster\" title=\"University of Münster\">University of Münster</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCrstenberg\" title=\"<PERSON>\"><PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/University_of_M%C3%BCnster\" title=\"University of Münster\">University of Münster</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCrstenberg"}, {"title": "University of Münster", "link": "https://wikipedia.org/wiki/University_of_M%C3%BCnster"}]}, {"year": "1799", "text": "French Revolutionary Wars: The Battle of Mount Tabor: <PERSON> drives Ottoman Turks across the River Jordan near Acre.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mount_Tabor_(1799)\" title=\"Battle of Mount Tabor (1799)\">Battle of Mount Tabor</a>: <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> drives <a href=\"https://wikipedia.org/wiki/Ottoman_Turks\" title=\"Ottoman Turks\">Ottoman Turks</a> across the <a href=\"https://wikipedia.org/wiki/River_Jordan\" class=\"mw-redirect\" title=\"River Jordan\">River Jordan</a> near <a href=\"https://wikipedia.org/wiki/Acre,_Israel\" title=\"Acre, Israel\">Acre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mount_Tabor_(1799)\" title=\"Battle of Mount Tabor (1799)\">Battle of Mount Tabor</a>: <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a> drives <a href=\"https://wikipedia.org/wiki/Ottoman_Turks\" title=\"Ottoman Turks\">Ottoman Turks</a> across the <a href=\"https://wikipedia.org/wiki/River_Jordan\" class=\"mw-redirect\" title=\"River Jordan\">River Jordan</a> near <a href=\"https://wikipedia.org/wiki/Acre,_Israel\" title=\"Acre, Israel\">Acre</a>.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "Battle of Mount Tabor (1799)", "link": "https://wikipedia.org/wiki/Battle_of_Mount_Tabor_(1799)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Ottoman Turks", "link": "https://wikipedia.org/wiki/Ottoman_Turks"}, {"title": "River Jordan", "link": "https://wikipedia.org/wiki/River_Jordan"}, {"title": "Acre, Israel", "link": "https://wikipedia.org/wiki/Acre,_Israel"}]}, {"year": "1818", "text": "The United States Senate ratifies the Rush-Bagot Treaty, limiting naval armaments on the Great Lakes and Lake Champlain.", "html": "1818 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> ratifies the <a href=\"https://wikipedia.org/wiki/Rush%E2%80%93Bagot_Treaty\" title=\"Rush-Bagot Treaty\">Rush-Bagot Treaty</a>, limiting naval armaments on the <a href=\"https://wikipedia.org/wiki/Great_Lakes\" title=\"Great Lakes\">Great Lakes</a> and <a href=\"https://wikipedia.org/wiki/Lake_Champlain\" title=\"Lake Champlain\">Lake Champlain</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> ratifies the <a href=\"https://wikipedia.org/wiki/Rush%E2%80%93Bagot_Treaty\" title=\"Rush-Bagot Treaty\">Rush-Bagot Treaty</a>, limiting naval armaments on the <a href=\"https://wikipedia.org/wiki/Great_Lakes\" title=\"Great Lakes\">Great Lakes</a> and <a href=\"https://wikipedia.org/wiki/Lake_Champlain\" title=\"Lake Champlain\">Lake Champlain</a>.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "Rush-Bagot Treaty", "link": "https://wikipedia.org/wiki/Rush%E2%80%93Bagot_Treaty"}, {"title": "Great Lakes", "link": "https://wikipedia.org/wiki/Great_Lakes"}, {"title": "Lake Champlain", "link": "https://wikipedia.org/wiki/Lake_Champlain"}]}, {"year": "1838", "text": "The French Army captures Veracruz in the Pastry War.", "html": "1838 - The <a href=\"https://wikipedia.org/wiki/French_Army\" title=\"French Army\">French Army</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Veracruz_(1838)\" title=\"Battle of Veracruz (1838)\">captures Veracruz</a> in the <a href=\"https://wikipedia.org/wiki/Pastry_War\" title=\"Pastry War\">Pastry War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_Army\" title=\"French Army\">French Army</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Veracruz_(1838)\" title=\"Battle of Veracruz (1838)\">captures Veracruz</a> in the <a href=\"https://wikipedia.org/wiki/Pastry_War\" title=\"Pastry War\">Pastry War</a>.", "links": [{"title": "French Army", "link": "https://wikipedia.org/wiki/French_Army"}, {"title": "Battle of Veracruz (1838)", "link": "https://wikipedia.org/wiki/Battle_of_Veracruz_(1838)"}, {"title": "Pastry War", "link": "https://wikipedia.org/wiki/Pastry_War"}]}, {"year": "1847", "text": "Shooting of a Māori by an English sailor results in the opening of the Wanganui Campaign of the New Zealand Wars.", "html": "1847 - Shooting of a <a href=\"https://wikipedia.org/wiki/M%C4%81ori_people\" title=\"Māori people\">Māori</a> by an English sailor results in the opening of the <a href=\"https://wikipedia.org/wiki/Wanganui_Campaign\" class=\"mw-redirect\" title=\"Wanganui Campaign\">Wanganui Campaign</a> of the <a href=\"https://wikipedia.org/wiki/New_Zealand_Wars\" title=\"New Zealand Wars\">New Zealand Wars</a>.", "no_year_html": "Shooting of a <a href=\"https://wikipedia.org/wiki/M%C4%81ori_people\" title=\"Māori people\">Māori</a> by an English sailor results in the opening of the <a href=\"https://wikipedia.org/wiki/Wanganui_Campaign\" class=\"mw-redirect\" title=\"Wanganui Campaign\">Wanganui Campaign</a> of the <a href=\"https://wikipedia.org/wiki/New_Zealand_Wars\" title=\"New Zealand Wars\">New Zealand Wars</a>.", "links": [{"title": "Māori people", "link": "https://wikipedia.org/wiki/M%C4%81ori_people"}, {"title": "Wanganui Campaign", "link": "https://wikipedia.org/wiki/Wanganui_Campaign"}, {"title": "New Zealand Wars", "link": "https://wikipedia.org/wiki/New_Zealand_Wars"}]}, {"year": "1853", "text": "The Great Indian Peninsula Railway opens the first passenger rail in India, from Bori Bunder to Thane.", "html": "1853 - The <a href=\"https://wikipedia.org/wiki/Great_Indian_Peninsula_Railway\" title=\"Great Indian Peninsula Railway\">Great Indian Peninsula Railway</a> opens the first passenger rail in India, from <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bunder\" title=\"<PERSON><PERSON> B<PERSON>er\"><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Thane\" title=\"Thane\">Thane</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Indian_Peninsula_Railway\" title=\"Great Indian Peninsula Railway\">Great Indian Peninsula Railway</a> opens the first passenger rail in India, from <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bunder\" title=\"<PERSON><PERSON> Bunder\"><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Thane\" title=\"Thane\">Thane</a>.", "links": [{"title": "Great Indian Peninsula Railway", "link": "https://wikipedia.org/wiki/Great_Indian_Peninsula_Railway"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>er"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thane"}]}, {"year": "1858", "text": "The Wernerian Natural History Society, a former Scottish learned society, is dissolved.", "html": "1858 - The <a href=\"https://wikipedia.org/wiki/Wernerian_Natural_History_Society\" title=\"Wernerian Natural History Society\">Wernerian Natural History Society</a>, a former Scottish <a href=\"https://wikipedia.org/wiki/Learned_society\" title=\"Learned society\">learned society</a>, is dissolved.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wernerian_Natural_History_Society\" title=\"Wernerian Natural History Society\">Wernerian Natural History Society</a>, a former Scottish <a href=\"https://wikipedia.org/wiki/Learned_society\" title=\"Learned society\">learned society</a>, is dissolved.", "links": [{"title": "Wernerian Natural History Society", "link": "https://wikipedia.org/wiki/Wernerian_Natural_History_Society"}, {"title": "Learned society", "link": "https://wikipedia.org/wiki/Learned_society"}]}, {"year": "1862", "text": "American Civil War: Battle at Lee's Mills in Virginia.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_at_Lee%27s_<PERSON>\" class=\"mw-redirect\" title=\"Battle at Lee's Mills\">Battle at Lee's Mills</a> in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_at_Lee%27s_<PERSON>\" class=\"mw-redirect\" title=\"Battle at Lee's Mills\">Battle at Lee's Mills</a> in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle at Lee's Mills", "link": "https://wikipedia.org/wiki/Battle_at_Lee%27s_<PERSON>"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}]}, {"year": "1862", "text": "American Civil War: The District of Columbia Compensated Emancipation Act, a bill ending slavery in the District of Columbia, becomes law.", "html": "1862 - American Civil War: The <a href=\"https://wikipedia.org/wiki/District_of_Columbia_Compensated_Emancipation_Act\" title=\"District of Columbia Compensated Emancipation Act\">District of Columbia Compensated Emancipation Act</a>, a bill ending <a href=\"https://wikipedia.org/wiki/Slavery_in_the_District_of_Columbia\" title=\"Slavery in the District of Columbia\">slavery in the District of Columbia</a>, becomes law.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/District_of_Columbia_Compensated_Emancipation_Act\" title=\"District of Columbia Compensated Emancipation Act\">District of Columbia Compensated Emancipation Act</a>, a bill ending <a href=\"https://wikipedia.org/wiki/Slavery_in_the_District_of_Columbia\" title=\"Slavery in the District of Columbia\">slavery in the District of Columbia</a>, becomes law.", "links": [{"title": "District of Columbia Compensated Emancipation Act", "link": "https://wikipedia.org/wiki/District_of_Columbia_Compensated_Emancipation_Act"}, {"title": "Slavery in the District of Columbia", "link": "https://wikipedia.org/wiki/Slavery_in_the_District_of_Columbia"}]}, {"year": "1863", "text": "American Civil War: During the Vicksburg Campaign, gunboats commanded by acting Rear Admiral <PERSON> run downriver past Confederate artillery batteries at Vicksburg.", "html": "1863 - American Civil War: During the <a href=\"https://wikipedia.org/wiki/Vicksburg_Campaign\" class=\"mw-redirect\" title=\"Vicksburg Campaign\">Vicksburg Campaign</a>, <a href=\"https://wikipedia.org/wiki/Gunboat\" title=\"Gunboat\">gunboats</a> commanded by acting Rear Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> run downriver past Confederate <a href=\"https://wikipedia.org/wiki/Artillery_battery\" title=\"Artillery battery\">artillery batteries</a> at Vicksburg.", "no_year_html": "American Civil War: During the <a href=\"https://wikipedia.org/wiki/Vicksburg_Campaign\" class=\"mw-redirect\" title=\"Vicksburg Campaign\">Vicksburg Campaign</a>, <a href=\"https://wikipedia.org/wiki/Gunboat\" title=\"Gunboat\">gunboats</a> commanded by acting Rear Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> run downriver past Confederate <a href=\"https://wikipedia.org/wiki/Artillery_battery\" title=\"Artillery battery\">artillery batteries</a> at Vicksburg.", "links": [{"title": "Vicksburg Campaign", "link": "https://wikipedia.org/wiki/Vicksburg_Campaign"}, {"title": "Gunboat", "link": "https://wikipedia.org/wiki/Gunboat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Artillery battery", "link": "https://wikipedia.org/wiki/Artillery_battery"}]}, {"year": "1878", "text": "The Senate of the Grand Duchy of Finland issues a declaration establishing a city of Kotka on the southern part islands from the old Kymi parish.", "html": "1878 - The <a href=\"https://wikipedia.org/wiki/Senate_of_the_Grand_Duchy_of_Finland\" class=\"mw-redirect\" title=\"Senate of the Grand Duchy of Finland\">Senate of the Grand Duchy of Finland</a> issues a declaration establishing a city of <a href=\"https://wikipedia.org/wiki/Kotka\" title=\"Kotka\">Kotka</a> on the southern part islands from the <a href=\"https://wikipedia.org/wiki/Kymi,_Finland\" title=\"Kymi, Finland\">old Kymi parish</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Senate_of_the_Grand_Duchy_of_Finland\" class=\"mw-redirect\" title=\"Senate of the Grand Duchy of Finland\">Senate of the Grand Duchy of Finland</a> issues a declaration establishing a city of <a href=\"https://wikipedia.org/wiki/Kotka\" title=\"Kotka\">Kotka</a> on the southern part islands from the <a href=\"https://wikipedia.org/wiki/Kymi,_Finland\" title=\"Kymi, Finland\">old Kymi parish</a>.", "links": [{"title": "Senate of the Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Senate_of_the_Grand_Duchy_of_Finland"}, {"title": "Kotka", "link": "https://wikipedia.org/wiki/Kotka"}, {"title": "Kymi, Finland", "link": "https://wikipedia.org/wiki/Kymi,_Finland"}]}, {"year": "1881", "text": "In Dodge City, Kansas, <PERSON> fights his last gun battle.", "html": "1881 - In <a href=\"https://wikipedia.org/wiki/Dodge_City,_Kansas\" title=\"Dodge City, Kansas\">Dodge City, Kansas</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Masterson\"><PERSON></a> fights his last gun battle.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Dodge_City,_Kansas\" title=\"Dodge City, Kansas\">Dodge City, Kansas</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Masterson\"><PERSON></a> fights his last gun battle.", "links": [{"title": "Dodge City, Kansas", "link": "https://wikipedia.org/wiki/Dodge_City,_Kansas"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Masterson"}]}, {"year": "1908", "text": "Natural Bridges National Monument is established in Utah.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Natural_Bridges_National_Monument\" title=\"Natural Bridges National Monument\">Natural Bridges National Monument</a> is established in <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Natural_Bridges_National_Monument\" title=\"Natural Bridges National Monument\">Natural Bridges National Monument</a> is established in <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a>.", "links": [{"title": "Natural Bridges National Monument", "link": "https://wikipedia.org/wiki/Natural_Bridges_National_Monument"}, {"title": "Utah", "link": "https://wikipedia.org/wiki/Utah"}]}, {"year": "1910", "text": "The oldest existing indoor ice hockey arena still used for the sport in the 21st century, Boston Arena, opens for the first time.", "html": "1910 - The oldest existing indoor <a href=\"https://wikipedia.org/wiki/Ice_hockey\" title=\"Ice hockey\">ice hockey</a> arena still used for the sport in the 21st century, <a href=\"https://wikipedia.org/wiki/Matthews_Arena\" title=\"Matthews Arena\">Boston Arena</a>, opens for the first time.", "no_year_html": "The oldest existing indoor <a href=\"https://wikipedia.org/wiki/Ice_hockey\" title=\"Ice hockey\">ice hockey</a> arena still used for the sport in the 21st century, <a href=\"https://wikipedia.org/wiki/Matthews_Arena\" title=\"Matthews Arena\">Boston Arena</a>, opens for the first time.", "links": [{"title": "Ice hockey", "link": "https://wikipedia.org/wiki/Ice_hockey"}, {"title": "Matthews Arena", "link": "https://wikipedia.org/wiki/Matthews_Arena"}]}, {"year": "1912", "text": "<PERSON> becomes the first woman to fly an airplane across the English Channel.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to fly an <a href=\"https://wikipedia.org/wiki/Aircraft\" title=\"Aircraft\">airplane</a> across the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to fly an <a href=\"https://wikipedia.org/wiki/Aircraft\" title=\"Aircraft\">airplane</a> across the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Aircraft", "link": "https://wikipedia.org/wiki/Aircraft"}, {"title": "English Channel", "link": "https://wikipedia.org/wiki/English_Channel"}]}, {"year": "1917", "text": "Russian Revolution: <PERSON> returns to Petrograd, Russia, from exile in Switzerland.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Russian_Revolution\" title=\"Russian Revolution\">Russian Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Petrograd</a>, <a href=\"https://wikipedia.org/wiki/Russian_Provisional_Government\" title=\"Russian Provisional Government\">Russia</a>, from exile in Switzerland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russian_Revolution\" title=\"Russian Revolution\">Russian Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Petrograd</a>, <a href=\"https://wikipedia.org/wiki/Russian_Provisional_Government\" title=\"Russian Provisional Government\">Russia</a>, from exile in Switzerland.", "links": [{"title": "Russian Revolution", "link": "https://wikipedia.org/wiki/Russian_Revolution"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lenin"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}, {"title": "Russian Provisional Government", "link": "https://wikipedia.org/wiki/Russian_Provisional_Government"}]}, {"year": "1919", "text": "<PERSON><PERSON> organizes a day of \"prayer and fasting\" in response to the killing of Indian protesters in the Jallianwala Bagh massacre by the British colonial troops three days earlier.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a> organizes a day of \"prayer and fasting\" in response to the killing of Indian protesters in the <a href=\"https://wikipedia.org/wiki/Jallianwala_Bagh_massacre\" title=\"Jallianwala Bagh massacre\">Jallianwala Bagh massacre</a> by the British colonial troops three days earlier.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a> organizes a day of \"prayer and fasting\" in response to the killing of Indian protesters in the <a href=\"https://wikipedia.org/wiki/Jallianwala_Bagh_massacre\" title=\"Jallianwala Bagh massacre\">Jallianwala Bagh massacre</a> by the British colonial troops three days earlier.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Jallianwala Bagh massacre", "link": "https://wikipedia.org/wiki/Jallianwala_Bagh_massacre"}]}, {"year": "1919", "text": "Polish-Lithuanian War: The Polish Army launches the Vilna offensive to capture Vilnius in modern Lithuania.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_War\" title=\"Polish-Lithuanian War\">Polish-Lithuanian War</a>: The <a href=\"https://wikipedia.org/wiki/Polish_Land_Forces\" title=\"Polish Land Forces\">Polish Army</a> launches the <a href=\"https://wikipedia.org/wiki/Vilna_offensive\" title=\"Vilna offensive\">Vilna offensive</a> to capture <a href=\"https://wikipedia.org/wiki/Vilnius\" title=\"Vilnius\">Vilnius</a> in modern <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_War\" title=\"Polish-Lithuanian War\">Polish-Lithuanian War</a>: The <a href=\"https://wikipedia.org/wiki/Polish_Land_Forces\" title=\"Polish Land Forces\">Polish Army</a> launches the <a href=\"https://wikipedia.org/wiki/Vilna_offensive\" title=\"Vilna offensive\">Vilna offensive</a> to capture <a href=\"https://wikipedia.org/wiki/Vilnius\" title=\"Vilnius\">Vilnius</a> in modern <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "links": [{"title": "Polish-Lithuanian War", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_War"}, {"title": "Polish Land Forces", "link": "https://wikipedia.org/wiki/Polish_Land_Forces"}, {"title": "Vilna offensive", "link": "https://wikipedia.org/wiki/Vilna_offensive"}, {"title": "Vilnius", "link": "https://wikipedia.org/wiki/Vilnius"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}]}, {"year": "1922", "text": "The Treaty of Rapallo, pursuant to which Germany and the Soviet Union re-establish diplomatic relations, is signed.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Rapallo_(1922)\" title=\"Treaty of Rapallo (1922)\">Treaty of Rapallo</a>, pursuant to which <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Germany</a> and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> re-establish <a href=\"https://wikipedia.org/wiki/Diplomatic_relations\" class=\"mw-redirect\" title=\"Diplomatic relations\">diplomatic relations</a>, is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Rapallo_(1922)\" title=\"Treaty of Rapallo (1922)\">Treaty of Rapallo</a>, pursuant to which <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Germany</a> and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> re-establish <a href=\"https://wikipedia.org/wiki/Diplomatic_relations\" class=\"mw-redirect\" title=\"Diplomatic relations\">diplomatic relations</a>, is signed.", "links": [{"title": "Treaty of Rapallo (1922)", "link": "https://wikipedia.org/wiki/Treaty_of_Rapallo_(1922)"}, {"title": "Weimar Republic", "link": "https://wikipedia.org/wiki/Weimar_Republic"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Diplomatic relations", "link": "https://wikipedia.org/wiki/Diplomatic_relations"}]}, {"year": "1925", "text": "During the Communist St Nedelya Church assault in Sofia, Bulgaria, 150 are killed and 500 are wounded.", "html": "1925 - During the <a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">Communist</a> <a href=\"https://wikipedia.org/wiki/St_Nedelya_Church_assault\" class=\"mw-redirect\" title=\"St Nedelya Church assault\">St Nedelya Church assault</a> in <a href=\"https://wikipedia.org/wiki/Sofia\" title=\"Sofia\">Sofia</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bulgaria\" class=\"mw-redirect\" title=\"Kingdom of Bulgaria\">Bulgaria</a>, 150 are killed and 500 are wounded.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">Communist</a> <a href=\"https://wikipedia.org/wiki/St_Nedelya_Church_assault\" class=\"mw-redirect\" title=\"St Nedelya Church assault\">St Nedelya Church assault</a> in <a href=\"https://wikipedia.org/wiki/Sofia\" title=\"Sofia\">Sofia</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bulgaria\" class=\"mw-redirect\" title=\"Kingdom of Bulgaria\">Bulgaria</a>, 150 are killed and 500 are wounded.", "links": [{"title": "Communist", "link": "https://wikipedia.org/wiki/Communist"}, {"title": "St Nedelya Church assault", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_Church_assault"}, {"title": "Sofia", "link": "https://wikipedia.org/wiki/Sofia"}, {"title": "Kingdom of Bulgaria", "link": "https://wikipedia.org/wiki/Kingdom_of_Bulgaria"}]}, {"year": "1941", "text": "World War II: The Italian-German Tarigo convoy is attacked and destroyed by British ships.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Italian-German <i>Tarigo</i> convoy is <a href=\"https://wikipedia.org/wiki/Battle_of_the_Tarigo_Convoy\" title=\"Battle of the Tarigo Convoy\">attacked and destroyed</a> by British ships.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Italian-German <i>Tarigo</i> convoy is <a href=\"https://wikipedia.org/wiki/Battle_of_the_Tarigo_Convoy\" title=\"Battle of the Tarigo Convoy\">attacked and destroyed</a> by British ships.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of the Tarigo Convoy", "link": "https://wikipedia.org/wiki/Battle_of_the_<PERSON><PERSON><PERSON>_Convoy"}]}, {"year": "1941", "text": "World War II: The Nazi-affiliated Us<PERSON>š<PERSON> is put in charge of the Independent State of Croatia by the Axis powers after Operation 25 is effected.", "html": "1941 - World War II: The Nazi-affiliated <a href=\"https://wikipedia.org/wiki/Usta%C5%A1e\" title=\"Ustaše\"><PERSON><PERSON><PERSON><PERSON></a> is put in charge of the <a href=\"https://wikipedia.org/wiki/Independent_State_of_Croatia\" title=\"Independent State of Croatia\">Independent State of Croatia</a> by the <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis powers</a> after <a href=\"https://wikipedia.org/wiki/Invasion_of_Yugoslavia\" title=\"Invasion of Yugoslavia\">Operation 25</a> is effected.", "no_year_html": "World War II: The Nazi-affiliated <a href=\"https://wikipedia.org/wiki/Usta%C5%A1e\" title=\"Ustaše\"><PERSON><PERSON><PERSON><PERSON></a> is put in charge of the <a href=\"https://wikipedia.org/wiki/Independent_State_of_Croatia\" title=\"Independent State of Croatia\">Independent State of Croatia</a> by the <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis powers</a> after <a href=\"https://wikipedia.org/wiki/Invasion_of_Yugoslavia\" title=\"Invasion of Yugoslavia\">Operation 25</a> is effected.", "links": [{"title": "<PERSON><PERSON>š<PERSON>", "link": "https://wikipedia.org/wiki/Usta%C5%A1e"}, {"title": "Independent State of Croatia", "link": "https://wikipedia.org/wiki/Independent_State_of_Croatia"}, {"title": "Axis powers", "link": "https://wikipedia.org/wiki/Axis_powers"}, {"title": "Invasion of Yugoslavia", "link": "https://wikipedia.org/wiki/Invasion_of_Yugoslavia"}]}, {"year": "1942", "text": "King <PERSON> awarded the George Cross to the people of Malta in appreciation of their heroism.", "html": "1942 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George VI\"><PERSON></a> awarded the <a href=\"https://wikipedia.org/wiki/Award_of_the_<PERSON>_<PERSON>_to_Malta\" title=\"Award of the George Cross to Malta\"><PERSON> to the people of Malta</a> in appreciation of their heroism.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George VI\"><PERSON></a> awarded the <a href=\"https://wikipedia.org/wiki/Award_of_the_<PERSON>_<PERSON>_to_Malta\" title=\"Award of the George Cross to Malta\"><PERSON> to the people of Malta</a> in appreciation of their heroism.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Award of the George Cross to Malta", "link": "https://wikipedia.org/wiki/Award_of_the_<PERSON>_<PERSON>_to_Malta"}]}, {"year": "1943", "text": "<PERSON> accidentally discovers the hallucinogenic effects of the research drug LSD. He intentionally takes the drug three days later on April 19.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> accidentally discovers the hallucinogenic effects of the research drug <a href=\"https://wikipedia.org/wiki/LSD\" title=\"LSD\">LSD</a>. He intentionally takes the drug three days later on <a href=\"https://wikipedia.org/wiki/April_19\" title=\"April 19\">April 19</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> accidentally discovers the hallucinogenic effects of the research drug <a href=\"https://wikipedia.org/wiki/LSD\" title=\"LSD\">LSD</a>. He intentionally takes the drug three days later on <a href=\"https://wikipedia.org/wiki/April_19\" title=\"April 19\">April 19</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "LSD", "link": "https://wikipedia.org/wiki/LSD"}, {"title": "April 19", "link": "https://wikipedia.org/wiki/April_19"}]}, {"year": "1944", "text": "World War II: Allied forces start bombing Belgrade, killing about 1,100 people. This bombing fell on the Orthodox Christian Easter.", "html": "1944 - World War II: Allied forces start <a href=\"https://wikipedia.org/wiki/Allied_bombing_of_Yugoslavia_in_World_War_II\" title=\"Allied bombing of Yugoslavia in World War II\">bombing Belgrade</a>, killing about 1,100 people. This bombing fell on the Orthodox Christian Easter.", "no_year_html": "World War II: Allied forces start <a href=\"https://wikipedia.org/wiki/Allied_bombing_of_Yugoslavia_in_World_War_II\" title=\"Allied bombing of Yugoslavia in World War II\">bombing Belgrade</a>, killing about 1,100 people. This bombing fell on the Orthodox Christian Easter.", "links": [{"title": "Allied bombing of Yugoslavia in World War II", "link": "https://wikipedia.org/wiki/Allied_bombing_of_Yugoslavia_in_World_War_II"}]}, {"year": "1945", "text": "World War II: The Red Army begins the final assault on German forces around Berlin, with nearly one million troops fighting in the Battle of the Seelow Heights.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> begins the <a href=\"https://wikipedia.org/wiki/Battle_of_Berlin\" title=\"Battle of Berlin\">final assault</a> on <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> forces around Berlin, with nearly one million troops fighting in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Seelow_Heights\" title=\"Battle of the Seelow Heights\">Battle of the Seelow Heights</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> begins the <a href=\"https://wikipedia.org/wiki/Battle_of_Berlin\" title=\"Battle of Berlin\">final assault</a> on <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> forces around Berlin, with nearly one million troops fighting in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Seelow_Heights\" title=\"Battle of the Seelow Heights\">Battle of the Seelow Heights</a>.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Battle of Berlin", "link": "https://wikipedia.org/wiki/Battle_of_Berlin"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Battle of the Seelow Heights", "link": "https://wikipedia.org/wiki/Battle_of_the_Seelow_Heights"}]}, {"year": "1945", "text": "The United States Army liberates <PERSON> Sonderlager (high security) prisoner-of-war camp Oflag IV-C (better known as Colditz).", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> liberates Nazi <i><PERSON><PERSON>lager</i> (high security) <a href=\"https://wikipedia.org/wiki/Prisoner-of-war_camp\" title=\"Prisoner-of-war camp\">prisoner-of-war camp</a> <a href=\"https://wikipedia.org/wiki/Oflag_IV-C\" title=\"Oflag IV-C\">Oflag IV-C</a> (better known as <a href=\"https://wikipedia.org/wiki/Colditz_Castle\" title=\"Colditz Castle\">Colditz</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> liberates Nazi <i><PERSON>derlager</i> (high security) <a href=\"https://wikipedia.org/wiki/Prisoner-of-war_camp\" title=\"Prisoner-of-war camp\">prisoner-of-war camp</a> <a href=\"https://wikipedia.org/wiki/Oflag_IV-C\" title=\"Oflag IV-C\">Oflag IV-C</a> (better known as <a href=\"https://wikipedia.org/wiki/Colditz_Castle\" title=\"Colditz Castle\">Colditz</a>).", "links": [{"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Prisoner-of-war camp", "link": "https://wikipedia.org/wiki/Prisoner-of-war_camp"}, {"title": "Oflag IV-C", "link": "https://wikipedia.org/wiki/Oflag_IV-C"}, {"title": "Colditz Castle", "link": "https://wikipedia.org/wiki/Colditz_Castle"}]}, {"year": "1945", "text": "More than 7,000 die when the German transport ship Goya is sunk by a Soviet submarine.", "html": "1945 - More than 7,000 die when the German transport ship <i><a href=\"https://wikipedia.org/wiki/Goya_(ship)\" class=\"mw-redirect\" title=\"Goya (ship)\">Goya</a></i> is sunk by a Soviet <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a>.", "no_year_html": "More than 7,000 die when the German transport ship <i><a href=\"https://wikipedia.org/wiki/Goya_(ship)\" class=\"mw-redirect\" title=\"Goya (ship)\">Goya</a></i> is sunk by a Soviet <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a>.", "links": [{"title": "<PERSON><PERSON> (ship)", "link": "https://wikipedia.org/wiki/Goya_(ship)"}, {"title": "Submarine", "link": "https://wikipedia.org/wiki/Submarine"}]}, {"year": "1947", "text": "An explosion on board a freighter in port causes the city of Texas City, Texas, United States, to catch fire, killing almost 600 people.", "html": "1947 - An <a href=\"https://wikipedia.org/wiki/Texas_City_disaster\" title=\"Texas City disaster\">explosion</a> on board a freighter in port causes the city of <a href=\"https://wikipedia.org/wiki/Texas_City,_Texas\" title=\"Texas City, Texas\">Texas City, Texas</a>, United States, to catch fire, killing almost 600 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Texas_City_disaster\" title=\"Texas City disaster\">explosion</a> on board a freighter in port causes the city of <a href=\"https://wikipedia.org/wiki/Texas_City,_Texas\" title=\"Texas City, Texas\">Texas City, Texas</a>, United States, to catch fire, killing almost 600 people.", "links": [{"title": "Texas City disaster", "link": "https://wikipedia.org/wiki/Texas_City_disaster"}, {"title": "Texas City, Texas", "link": "https://wikipedia.org/wiki/Texas_City,_Texas"}]}, {"year": "1947", "text": "<PERSON> first applies the term \"Cold War\" to describe the relationship between the United States and the Soviet Union.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> first applies the term \"<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>\" to describe the relationship between the United States and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> first applies the term \"<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>\" to describe the relationship between the United States and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1948", "text": "The Organization of European Economic Co-operation is formed.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Organization_of_European_Economic_Co-operation\" class=\"mw-redirect\" title=\"Organization of European Economic Co-operation\">Organization of European Economic Co-operation</a> is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Organization_of_European_Economic_Co-operation\" class=\"mw-redirect\" title=\"Organization of European Economic Co-operation\">Organization of European Economic Co-operation</a> is formed.", "links": [{"title": "Organization of European Economic Co-operation", "link": "https://wikipedia.org/wiki/Organization_of_European_Economic_Co-operation"}]}, {"year": "1961", "text": "In a nationally broadcast speech, Cuban leader <PERSON><PERSON> declares that he is a Marxist-Leninist and that Cuba is going to adopt Communism.", "html": "1961 - In a nationally broadcast speech, Cuban leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> declares that he is a <a href=\"https://wikipedia.org/wiki/Marxism%E2%80%93Leninism\" title=\"Marxism-Leninism\">Marxist-Leninist</a> and that Cuba is going to adopt <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communism</a>.", "no_year_html": "In a nationally broadcast speech, Cuban leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> declares that he is a <a href=\"https://wikipedia.org/wiki/Marxism%E2%80%93Leninism\" title=\"Marxism-Leninism\">Marxist-Leninist</a> and that Cuba is going to adopt <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communism</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Marxism-Leninism", "link": "https://wikipedia.org/wiki/Marxism%E2%80%93Leninism"}, {"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}]}, {"year": "1963", "text": "U.S. civil rights campaigner Dr. <PERSON> writes his open letter from Birmingham Jail, sometimes known as \"The Negro Is Your Brother\", while incarcerated in Birmingham, Alabama, for protesting against segregation.", "html": "1963 - U.S. civil rights campaigner Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a> writes his open <a href=\"https://wikipedia.org/wiki/Letter_from_Birmingham_Jail\" title=\"Letter from Birmingham Jail\">letter from Birmingham Jail</a>, sometimes known as \"The Negro Is Your Brother\", while incarcerated in <a href=\"https://wikipedia.org/wiki/Birmingham,_Alabama\" title=\"Birmingham, Alabama\">Birmingham, Alabama</a>, for protesting against segregation.", "no_year_html": "U.S. civil rights campaigner Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a> writes his open <a href=\"https://wikipedia.org/wiki/Letter_from_Birmingham_Jail\" title=\"Letter from Birmingham Jail\">letter from Birmingham Jail</a>, sometimes known as \"The Negro Is Your Brother\", while incarcerated in <a href=\"https://wikipedia.org/wiki/Birmingham,_Alabama\" title=\"Birmingham, Alabama\">Birmingham, Alabama</a>, for protesting against segregation.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Letter from Birmingham Jail", "link": "https://wikipedia.org/wiki/Letter_from_Birmingham_Jail"}, {"title": "Birmingham, Alabama", "link": "https://wikipedia.org/wiki/Birmingham,_Alabama"}]}, {"year": "1972", "text": "Apollo program: The launch of Apollo 16 from Cape Canaveral, Florida.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: The launch of <a href=\"https://wikipedia.org/wiki/Apollo_16\" title=\"Apollo 16\">Apollo 16</a> from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral,_Florida\" title=\"Cape Canaveral, Florida\">Cape Canaveral, Florida</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: The launch of <a href=\"https://wikipedia.org/wiki/Apollo_16\" title=\"Apollo 16\">Apollo 16</a> from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral,_Florida\" title=\"Cape Canaveral, Florida\">Cape Canaveral, Florida</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 16", "link": "https://wikipedia.org/wiki/Apollo_16"}, {"title": "Cape Canaveral, Florida", "link": "https://wikipedia.org/wiki/Cape_Canaveral,_Florida"}]}, {"year": "2001", "text": "India and Bangladesh begin a five-day border conflict, but are unable to resolve the disputes about their border.", "html": "2001 - India and <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> begin a <a href=\"https://wikipedia.org/wiki/2001_Bangladeshi-Indian_border_skirmish\" class=\"mw-redirect\" title=\"2001 Bangladeshi-Indian border skirmish\">five-day border conflict</a>, but are unable to resolve the disputes about <a href=\"https://wikipedia.org/wiki/India%E2%80%93Bangladesh_border\" class=\"mw-redirect\" title=\"India-Bangladesh border\">their border</a>.", "no_year_html": "India and <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> begin a <a href=\"https://wikipedia.org/wiki/2001_Bangladeshi-Indian_border_skirmish\" class=\"mw-redirect\" title=\"2001 Bangladeshi-Indian border skirmish\">five-day border conflict</a>, but are unable to resolve the disputes about <a href=\"https://wikipedia.org/wiki/India%E2%80%93Bangladesh_border\" class=\"mw-redirect\" title=\"India-Bangladesh border\">their border</a>.", "links": [{"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}, {"title": "2001 Bangladeshi-Indian border skirmish", "link": "https://wikipedia.org/wiki/2001_Bangladeshi-Indian_border_skirmish"}, {"title": "India-Bangladesh border", "link": "https://wikipedia.org/wiki/India%E2%80%93Bangladesh_border"}]}, {"year": "2003", "text": "The Treaty of Accession is signed in Athens admitting ten new member states to the European Union.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Accession_2003\" title=\"Treaty of Accession 2003\">Treaty of Accession</a> is signed in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a> admitting ten new member states to the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Accession_2003\" title=\"Treaty of Accession 2003\">Treaty of Accession</a> is signed in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a> admitting ten new member states to the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "links": [{"title": "Treaty of Accession 2003", "link": "https://wikipedia.org/wiki/Treaty_of_Accession_2003"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "2007", "text": "Virginia Tech shooting: <PERSON><PERSON><PERSON><PERSON> guns down 32 people and injures 17 before committing suicide.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Virginia_Tech_shooting\" title=\"Virginia Tech shooting\">Virginia Tech shooting</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> guns down 32 people and injures 17 before committing suicide.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Tech_shooting\" title=\"Virginia Tech shooting\">Virginia Tech shooting</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> guns down 32 people and injures 17 before committing suicide.", "links": [{"title": "Virginia Tech shooting", "link": "https://wikipedia.org/wiki/Virginia_Tech_shooting"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "The U.S. Supreme Court rules in the <PERSON><PERSON> v<PERSON> decision that execution by lethal injection does not violate the Eighth Amendment ban against cruel and unusual punishment.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules in the <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> v<PERSON>\"><PERSON><PERSON> v<PERSON></a></i> decision that <a href=\"https://wikipedia.org/wiki/Lethal_injection\" title=\"Lethal injection\">execution by lethal injection</a> does not violate the <a href=\"https://wikipedia.org/wiki/Eighth_Amendment_to_the_United_States_Constitution\" title=\"Eighth Amendment to the United States Constitution\">Eighth Amendment</a> ban against <a href=\"https://wikipedia.org/wiki/Cruel_and_unusual_punishment\" title=\"Cruel and unusual punishment\">cruel and unusual punishment</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules in the <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> v<PERSON>\"><PERSON><PERSON> v<PERSON></a></i> decision that <a href=\"https://wikipedia.org/wiki/Lethal_injection\" title=\"Lethal injection\">execution by lethal injection</a> does not violate the <a href=\"https://wikipedia.org/wiki/Eighth_Amendment_to_the_United_States_Constitution\" title=\"Eighth Amendment to the United States Constitution\">Eighth Amendment</a> ban against <a href=\"https://wikipedia.org/wiki/Cruel_and_unusual_punishment\" title=\"Cruel and unusual punishment\">cruel and unusual punishment</a>.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "<PERSON><PERSON> v. Rees", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_v._<PERSON>"}, {"title": "Lethal injection", "link": "https://wikipedia.org/wiki/Lethal_injection"}, {"title": "Eighth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Eighth_Amendment_to_the_United_States_Constitution"}, {"title": "Cruel and unusual punishment", "link": "https://wikipedia.org/wiki/Cruel_and_unusual_punishment"}]}, {"year": "2012", "text": "The trial for <PERSON>, the perpetrator of the 2011 Norway attacks, begins in Oslo, Norway.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>_Breivik\" title=\"Trial of <PERSON> Breivik\">trial</a> for <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Breiv<PERSON>\" title=\"<PERSON> Breiv<PERSON>\"><PERSON></a>, the perpetrator of the <a href=\"https://wikipedia.org/wiki/2011_Norway_attacks\" title=\"2011 Norway attacks\">2011 Norway attacks</a>, begins in <a href=\"https://wikipedia.org/wiki/Oslo\" title=\"Oslo\">Oslo</a>, Norway.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>_Breivik\" title=\"Trial of <PERSON> Breiv<PERSON>\">trial</a> for <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Breivik\" title=\"<PERSON> Breiv<PERSON>\"><PERSON></a>, the perpetrator of the <a href=\"https://wikipedia.org/wiki/2011_Norway_attacks\" title=\"2011 Norway attacks\">2011 Norway attacks</a>, begins in <a href=\"https://wikipedia.org/wiki/Oslo\" title=\"Oslo\">Oslo</a>, Norway.", "links": [{"title": "Trial of <PERSON>", "link": "https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>iv<PERSON>"}, {"title": "2011 Norway attacks", "link": "https://wikipedia.org/wiki/2011_Norway_attacks"}, {"title": "Oslo", "link": "https://wikipedia.org/wiki/Oslo"}]}, {"year": "2012", "text": "The Pulitzer Prize winners were announced, it was the first time since 1977 that no book won the Fiction Prize.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/2012_Pulitzer_Prize\" title=\"2012 Pulitzer Prize\">Pulitzer Prize</a> winners were announced, it was the first time <a href=\"https://wikipedia.org/wiki/1977_Pulitzer_Prize\" title=\"1977 Pulitzer Prize\">since 1977</a> that no book won the <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Fiction\" title=\"Pulitzer Prize for Fiction\">Fiction Prize</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2012_Pulitzer_Prize\" title=\"2012 Pulitzer Prize\">Pulitzer Prize</a> winners were announced, it was the first time <a href=\"https://wikipedia.org/wiki/1977_Pulitzer_Prize\" title=\"1977 Pulitzer Prize\">since 1977</a> that no book won the <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Fiction\" title=\"Pulitzer Prize for Fiction\">Fiction Prize</a>.", "links": [{"title": "2012 Pulitzer Prize", "link": "https://wikipedia.org/wiki/2012_Pulitzer_Prize"}, {"title": "1977 Pulitzer Prize", "link": "https://wikipedia.org/wiki/1977_Pulitzer_Prize"}, {"title": "Pulitzer Prize for Fiction", "link": "https://wikipedia.org/wiki/Pulitzer_Prize_for_Fiction"}]}, {"year": "2013", "text": "A 7.8-magnitude earthquake strikes Sistan and Baluchestan province, Iran, killing at least 35 people and injuring 117 others.", "html": "2013 - A 7.8-<a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">magnitude</a> <a href=\"https://wikipedia.org/wiki/2013_Saravan_earthquake\" title=\"2013 Saravan earthquake\">earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Sistan_and_Baluchestan_province\" title=\"Sistan and Baluchestan province\">Sistan and Baluchestan province</a>, Iran, killing at least 35 people and injuring 117 others.", "no_year_html": "A 7.8-<a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">magnitude</a> <a href=\"https://wikipedia.org/wiki/2013_Saravan_earthquake\" title=\"2013 Saravan earthquake\">earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Sistan_and_Baluchestan_province\" title=\"Sistan and Baluchestan province\">Sistan and Baluchestan province</a>, Iran, killing at least 35 people and injuring 117 others.", "links": [{"title": "Moment magnitude scale", "link": "https://wikipedia.org/wiki/Moment_magnitude_scale"}, {"title": "2013 Saravan earthquake", "link": "https://wikipedia.org/wiki/2013_Saravan_earthquake"}, {"title": "Sistan and Baluchestan province", "link": "https://wikipedia.org/wiki/Sistan_and_Baluchestan_province"}]}, {"year": "2013", "text": "The 2013 Baga massacre is started when Boko Haram militants engage government soldiers in Baga.", "html": "2013 - The <a href=\"https://wikipedia.org/wiki/2013_Baga_massacre\" title=\"2013 Baga massacre\">2013 Baga massacre</a> is started when <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Haram\" title=\"Boko Haram\"><PERSON><PERSON></a> militants engage government soldiers in <a href=\"https://wikipedia.org/wiki/Baga,_Borno\" title=\"Baga, Borno\">Baga</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2013_Baga_massacre\" title=\"2013 Baga massacre\">2013 Baga massacre</a> is started when <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Haram\" title=\"Boko Haram\"><PERSON><PERSON></a> militants engage government soldiers in <a href=\"https://wikipedia.org/wiki/Baga,_Borno\" title=\"Baga, Borno\">Baga</a>.", "links": [{"title": "2013 Baga massacre", "link": "https://wikipedia.org/wiki/2013_Baga_massacre"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>m"}, {"title": "Baga, Borno", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON>"}]}, {"year": "2014", "text": "The South Korean ferry MV Sewol capsizes and sinks near Jindo Island, killing 304 passengers and crew and leading to widespread criticism of the South Korean government, media, and shipping authorities.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korean</a> ferry <a href=\"https://wikipedia.org/wiki/MV_Sewol\" title=\"MV Sewol\">MV <i>Sewol</i></a> <a href=\"https://wikipedia.org/wiki/Sinking_of_MV_Sewol\" title=\"Sinking of MV Sewol\">capsizes and sinks</a> near <a href=\"https://wikipedia.org/wiki/Jindo_(island)\" title=\"Jindo (island)\">Jindo Island</a>, killing 304 passengers and crew and leading to widespread criticism of the South Korean government, media, and shipping authorities.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korean</a> ferry <a href=\"https://wikipedia.org/wiki/MV_Sewol\" title=\"MV Sewol\">MV <i>Sewol</i></a> <a href=\"https://wikipedia.org/wiki/Sinking_of_MV_Sewol\" title=\"Sinking of MV Sewol\">capsizes and sinks</a> near <a href=\"https://wikipedia.org/wiki/Jindo_(island)\" title=\"Jindo (island)\">Jindo Island</a>, killing 304 passengers and crew and leading to widespread criticism of the South Korean government, media, and shipping authorities.", "links": [{"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}, {"title": "MV Sewol", "link": "https://wikipedia.org/wiki/MV_Sewol"}, {"title": "Sinking of MV Sewol", "link": "https://wikipedia.org/wiki/Sinking_of_MV_Sewol"}, {"title": "Jindo (island)", "link": "https://wikipedia.org/wiki/Jindo_(island)"}]}, {"year": "2016", "text": "Ecuador's worst earthquake in nearly 40 years kills 676 and injures more than 230,000.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/2016_Ecuador_earthquake\" title=\"2016 Ecuador earthquake\">Ecuador's worst earthquake in nearly 40 years</a> kills 676 and injures more than 230,000.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2016_Ecuador_earthquake\" title=\"2016 Ecuador earthquake\">Ecuador's worst earthquake in nearly 40 years</a> kills 676 and injures more than 230,000.", "links": [{"title": "2016 Ecuador earthquake", "link": "https://wikipedia.org/wiki/2016_Ecuador_earthquake"}]}, {"year": "2018", "text": "The New York Times and the New Yorker win the Pulitzer Prize for Public Service for breaking news of the <PERSON> Weinstein sexual abuse scandal.", "html": "2018 - <i><a href=\"https://wikipedia.org/wiki/The_New_York_Times\" title=\"The New York Times\">The New York Times</a></i> and the <i><a href=\"https://wikipedia.org/wiki/The_New_Yorker\" title=\"The New Yorker\">New Yorker</a></i> win the <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Public_Service\" title=\"Pulitzer Prize for Public Service\">Pulitzer Prize for Public Service</a> for breaking news of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_sexual_abuse_cases\" title=\"<PERSON> sexual abuse cases\"><PERSON> sexual abuse scandal</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_New_York_Times\" title=\"The New York Times\">The New York Times</a></i> and the <i><a href=\"https://wikipedia.org/wiki/The_New_Yorker\" title=\"The New Yorker\">New Yorker</a></i> win the <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Public_Service\" title=\"Pulitzer Prize for Public Service\">Pulitzer Prize for Public Service</a> for breaking news of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_sexual_abuse_cases\" title=\"<PERSON> sexual abuse cases\"><PERSON> sexual abuse scandal</a>.", "links": [{"title": "The New York Times", "link": "https://wikipedia.org/wiki/The_New_York_Times"}, {"title": "The New Yorker", "link": "https://wikipedia.org/wiki/The_New_Yorker"}, {"title": "Pulitzer Prize for Public Service", "link": "https://wikipedia.org/wiki/Pulitzer_Prize_for_Public_Service"}, {"title": "<PERSON> sexual abuse cases", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_sexual_abuse_cases"}]}, {"year": "2024", "text": "The historic Børsen in Copenhagen, Denmark, is severely damaged by a fire.", "html": "2024 - The historic <b><a href=\"https://wikipedia.org/wiki/B%C3%<PERSON>8<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></b> in <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a>, Denmark, is <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_fire\" class=\"mw-redirect\" title=\"Borsen fire\">severely damaged</a> by a fire.", "no_year_html": "The historic <b><a href=\"https://wikipedia.org/wiki/B%C3%<PERSON>8<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></b> in <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a>, Denmark, is <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_fire\" class=\"mw-redirect\" title=\"<PERSON>rsen fire\">severely damaged</a> by a fire.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%B8rsen"}, {"title": "Copenhagen", "link": "https://wikipedia.org/wiki/Copenhagen"}, {"title": "Borsen fire", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_fire"}]}], "Births": [{"year": "1488", "text": "<PERSON><PERSON> of Joseon (d. 1544)", "html": "1488 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Joseon\" title=\"<PERSON><PERSON> of Joseon\"><PERSON><PERSON> of Joseon</a> (d. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Joseon\" title=\"Jung<PERSON> of Joseon\"><PERSON><PERSON> of Joseon</a> (d. 1544)", "links": [{"title": "<PERSON><PERSON> of Joseon", "link": "https://wikipedia.org/wiki/Jungjong_of_Joseon"}]}, {"year": "1495", "text": "<PERSON><PERSON>, German mathematician and astronomer (d. 1557)", "html": "1495 - <a href=\"https://wikipedia.org/wiki/Petrus_Apianus\" title=\"Petrus Apianus\"><PERSON><PERSON></a>, German mathematician and astronomer (d. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petrus_Apianus\" title=\"Petrus Apianus\"><PERSON><PERSON></a>, German mathematician and astronomer (d. 1557)", "links": [{"title": "<PERSON><PERSON>us", "link": "https://wikipedia.org/wiki/Petrus_Apianus"}]}, {"year": "1516", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Burmese king (d. 1550)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/Tabin<PERSON><PERSON>hti\" title=\"Tabin<PERSON><PERSON>ht<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tabin<PERSON><PERSON>hti\" title=\"Tabin<PERSON><PERSON>ht<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (d. 1550)", "links": [{"title": "<PERSON>binshweht<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>binshwehti"}]}, {"year": "1569", "text": "<PERSON>, English poet and lawyer (d. 1626)", "html": "1569 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet,_born_1569)\" title=\"<PERSON> (poet, born 1569)\"><PERSON></a>, English poet and lawyer (d. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet,_born_1569)\" title=\"<PERSON> (poet, born 1569)\"><PERSON></a>, English poet and lawyer (d. 1626)", "links": [{"title": "<PERSON> (poet, born 1569)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet,_born_1569)"}]}, {"year": "1635", "text": "<PERSON><PERSON> the Elder, Dutch painter (d. 1681)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the Elder</a>, Dutch painter (d. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the Elder</a>, Dutch painter (d. 1681)", "links": [{"title": "<PERSON><PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_the_<PERSON>"}]}, {"year": "1646", "text": "<PERSON><PERSON><PERSON>, French architect (probable; d. 1708)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (probable; d. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (probable; d. 1708)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1660", "text": "<PERSON>, Irish-English physician and academic (d. 1753)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English physician and academic (d. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English physician and academic (d. 1753)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1661", "text": "<PERSON>, 1st Earl of Halifax, English poet and politician, First Lord of the Treasury (d. 1715)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Halifax\" title=\"<PERSON>, 1st Earl of Halifax\"><PERSON>, 1st Earl of Halifax</a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/First_Lord_of_the_Treasury\" title=\"First Lord of the Treasury\">First Lord of the Treasury</a> (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Halifax\" title=\"<PERSON>, 1st Earl of Halifax\"><PERSON>, 1st Earl of Halifax</a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/First_Lord_of_the_Treasury\" title=\"First Lord of the Treasury\">First Lord of the Treasury</a> (d. 1715)", "links": [{"title": "<PERSON>, 1st Earl of Halifax", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Halifax"}, {"title": "First Lord of the Treasury", "link": "https://wikipedia.org/wiki/First_Lord_of_the_Treasury"}]}, {"year": "1682", "text": "<PERSON>, English mathematician, invented the octant (d. 1744)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, invented the <a href=\"https://wikipedia.org/wiki/Octant_(instrument)\" title=\"Octant (instrument)\">octant</a> (d. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, invented the <a href=\"https://wikipedia.org/wiki/Octant_(instrument)\" title=\"Octant (instrument)\">octant</a> (d. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Octant (instrument)", "link": "https://wikipedia.org/wiki/Octant_(instrument)"}]}, {"year": "1697", "text": "<PERSON>, German organist and composer (d. 1778)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6rner\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_G%C3%B6rner"}]}, {"year": "1728", "text": "<PERSON>, French-Scottish physician and chemist (d. 1799)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Scottish physician and chemist (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Scottish physician and chemist (d. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON>, English general and politician (d. 1795)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1730)\" title=\"<PERSON> (British Army officer, born 1730)\"><PERSON></a>, English general and politician (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1730)\" title=\"<PERSON> (British Army officer, born 1730)\"><PERSON></a>, English general and politician (d. 1795)", "links": [{"title": "<PERSON> (British Army officer, born 1730)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer,_born_1730)"}]}, {"year": "1755", "text": "<PERSON>, French painter (d. 1842)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/%C3%89lisabeth_Vig%C3%A9e_Le_Brun\" title=\"<PERSON><PERSON>abeth Vigée <PERSON>\"><PERSON></a>, French painter (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lisabeth_Vig%C3%A9e_Le_Brun\" title=\"<PERSON><PERSON>abe<PERSON> Vigée <PERSON>\"><PERSON></a>, French painter (d. 1842)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lisabeth_Vig%C3%A9e_<PERSON>_<PERSON>run"}]}, {"year": "1786", "text": "<PERSON>, English admiral and politician, fourth Lieutenant Governor of Van Diemen's Land (d. 1847)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, fourth <a href=\"https://wikipedia.org/wiki/Governor_of_Tasmania\" title=\"Governor of Tasmania\">Lieutenant Governor of Van Diemen's Land</a> (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, fourth <a href=\"https://wikipedia.org/wiki/Governor_of_Tasmania\" title=\"Governor of Tasmania\">Lieutenant Governor of Van Diemen's Land</a> (d. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Tasmania", "link": "https://wikipedia.org/wiki/Governor_of_Tasmania"}]}, {"year": "1800", "text": "<PERSON>, 3rd Earl of Lucan, English field marshal and politician (d. 1888)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Lucan\" title=\"<PERSON>, 3rd Earl of Lucan\"><PERSON>, 3rd Earl of Lucan</a>, English field marshal and politician (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_<PERSON>_Lucan\" title=\"<PERSON>, 3rd Earl of Lucan\"><PERSON>, 3rd Earl of Lucan</a>, English field marshal and politician (d. 1888)", "links": [{"title": "<PERSON>, 3rd Earl of Lucan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Lucan"}]}, {"year": "1808", "text": "<PERSON>, American journalist, lawyer, and politician, sixth United States Secretary of the Interior (d. 1864)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist, lawyer, and politician, sixth <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist, lawyer, and politician, sixth <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1821", "text": "<PERSON>, French-English soldier and painter (d. 1893)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ox_<PERSON>\" title=\"Ford Madox Brown\"><PERSON></a>, French-English soldier and painter (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Madox_Brown\" title=\"Ford Madox Brown\"><PERSON></a>, French-English soldier and painter (d. 1893)", "links": [{"title": "<PERSON> Madox Brown", "link": "https://wikipedia.org/wiki/Ford_Madox_Brown"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON>, German mathematician and academic (d. 1852)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>th<PERSON>_E<PERSON>\" title=\"Got<PERSON><PERSON> E<PERSON>nstein\"><PERSON><PERSON><PERSON></a>, German mathematician and academic (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>th<PERSON>_E<PERSON>nstein\" title=\"Gotth<PERSON> Eisenstein\"><PERSON><PERSON><PERSON></a>, German mathematician and academic (d. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>th<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "Sir <PERSON>, 1st Baronet, British politician (d. 1891)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, British politician (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, British politician (d. 1891)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1827", "text": "<PERSON><PERSON>, Canadian poet and bookseller (d. 1879)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Octave_Cr%C3%A9mazie\" title=\"Octave Crémazie\">Octave <PERSON><PERSON></a>, Canadian poet and bookseller (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octave_Cr%C3%A9mazie\" title=\"Octave Crémazie\">Octave C<PERSON><PERSON></a>, Canadian poet and bookseller (d. 1879)", "links": [{"title": "Octave <PERSON>", "link": "https://wikipedia.org/wiki/Octave_Cr%C3%A9mazie"}]}, {"year": "1839", "text": "<PERSON>, <PERSON><PERSON>, Italian politician, 12th Prime Minister of Italy (d. 1908)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Marchese_<PERSON>_<PERSON>udin%C3%AC\" title=\"<PERSON>, March<PERSON> di Rudinì\"><PERSON>, <PERSON><PERSON> di <PERSON></a>, Italian politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>n%C3%AC\" title=\"<PERSON>, March<PERSON> di Rudinì\"><PERSON>, <PERSON><PERSON> di Rudi<PERSON>ì</a>, Italian politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1908)", "links": [{"title": "<PERSON>, <PERSON><PERSON> di Rudinì", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>%C3%AC"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1834", "text": "<PERSON>, English merchant (d. 1862)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English merchant (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English merchant (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON><PERSON><PERSON>, French journalist, novelist, and poet, Nobel Prize laureate (d. 1924)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Anatole_France\" title=\"Anatole France\"><PERSON><PERSON><PERSON></a>, French journalist, novelist, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anatole_France\" title=\"Anatole France\"><PERSON><PERSON><PERSON> France</a>, French journalist, novelist, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1924)", "links": [{"title": "Anatole France", "link": "https://wikipedia.org/wiki/Anatole_France"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1847", "text": "<PERSON>, Swiss-Austrian architect, designed the Federal Palace of Switzerland (d. 1906)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Austrian architect, designed the <a href=\"https://wikipedia.org/wiki/Federal_Palace_of_Switzerland\" title=\"Federal Palace of Switzerland\">Federal Palace of Switzerland</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Austrian architect, designed the <a href=\"https://wikipedia.org/wiki/Federal_Palace_of_Switzerland\" title=\"Federal Palace of Switzerland\">Federal Palace of Switzerland</a> (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Palace of Switzerland", "link": "https://wikipedia.org/wiki/Federal_Palace_of_Switzerland"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author and activist (d. 1919)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Kand<PERSON><PERSON>_<PERSON>\" title=\"Kand<PERSON><PERSON> Veeresaling<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and activist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kand<PERSON><PERSON>_<PERSON>\" title=\"Kand<PERSON><PERSON> Veer<PERSON>aling<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and activist (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON><PERSON>, Sri Lankan lawyer and politician, third Solicitor General of Sri Lanka (d. 1930)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician, third <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Sri_Lanka\" title=\"Solicitor General of Sri Lanka\">Solicitor General of Sri Lanka</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician, third <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Sri_Lanka\" title=\"Solicitor General of Sri Lanka\">Solicitor General of Sri Lanka</a> (d. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Po<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Solicitor General of Sri Lanka", "link": "https://wikipedia.org/wiki/Solicitor_General_of_Sri_Lanka"}]}, {"year": "1864", "text": "<PERSON>, American medical doctor and professor (d. 1915)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American medical doctor and professor (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Bull<PERSON>\"><PERSON></a>, American medical doctor and professor (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ard"}]}, {"year": "1865", "text": "<PERSON>, Australian general (d. 1945)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Puerto Rican journalist, lawyer, and politician (d. 1918)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican journalist, lawyer, and politician (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican journalist, lawyer, and politician (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, American inventor (d. 1912)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American inventor (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American inventor (d. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Irish author, poet, and playwright (d. 1909)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Synge\" title=\"John <PERSON>ington Synge\"><PERSON></a>, Irish author, poet, and playwright (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Synge\" title=\"<PERSON>ington Synge\"><PERSON></a>, Irish author, poet, and playwright (d. 1909)", "links": [{"title": "<PERSON> Synge", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Synge"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese general (d. 1936)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/J%C5%8Dtar%C5%8D_Watanabe\" title=\"<PERSON><PERSON><PERSON><PERSON> Watanabe\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C5%8Dtar%C5%8D_Watanabe\" title=\"<PERSON><PERSON><PERSON><PERSON> Watanabe\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C5%8Dtar%C5%8D_Watanabe"}]}, {"year": "1878", "text": "<PERSON><PERSON> <PERSON><PERSON>, English cricketer and footballer (d. 1914)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and footballer (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and footballer (d. 1914)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American organist and composer (d. 1972)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, 3rd Baron <PERSON>, English cricketer, journalist, and politician (d. 1963)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English cricketer, journalist, and politician (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd <PERSON>\"><PERSON>, 3rd <PERSON></a>, English cricketer, journalist, and politician (d. 1963)", "links": [{"title": "<PERSON>, 3rd <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Hungarian composer and educator (d. 1960)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Le%C3%B3_Weiner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian composer and educator (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le%C3%B3_Weiner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian composer and educator (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le%C3%B3_<PERSON>ner"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, Greek-American football player and javelin thrower (d. 1957)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-American football player and javelin thrower (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-American football player and javelin thrower (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, German politician (d. 1944)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A4lmann\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_Th%C3%A4lmann"}]}, {"year": "1888", "text": "<PERSON>, English footballer and manager (d. 1940)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, English actor, director, producer, screenwriter, and composer (d. 1977)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, screenwriter, and composer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chaplin\"><PERSON></a>, English actor, director, producer, screenwriter, and composer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, English cricketer and umpire (d. 1954)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American author and educator (d. 1979)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American author and illustrator (d. 1980)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, German transgender woman and the first known person to undergo complete male-to-female gender-affirming surgery (d. unknown)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German transgender woman and the first known person to undergo complete male-to-female <a href=\"https://wikipedia.org/wiki/Gender-affirming_surgery\" title=\"Gender-affirming surgery\">gender-affirming surgery</a> (d. unknown)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German transgender woman and the first known person to undergo complete male-to-female <a href=\"https://wikipedia.org/wiki/Gender-affirming_surgery\" title=\"Gender-affirming surgery\">gender-affirming surgery</a> (d. unknown)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gender-affirming surgery", "link": "https://wikipedia.org/wiki/Gender-affirming_surgery"}]}, {"year": "1892", "text": "<PERSON>, American author, critic, and academic (d. 1980)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, critic, and academic (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, critic, and academic (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Canadian journalist and author (d. 1968)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u%C3%A8vre<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and author (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u%C3%A8vremont\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and author (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Germaine_Gu%C3%A8vremont"}]}, {"year": "1893", "text": "<PERSON>, American hurdler (d. 1979)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler (d. 1979)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "1895", "text": "<PERSON><PERSON>, English-Danish engineer and businessman, founded Arup (d. 1988)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Ove_A<PERSON>\" title=\"<PERSON><PERSON> A<PERSON>\"><PERSON><PERSON></a>, English-Danish engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Arup_Group_Limited\" class=\"mw-redirect\" title=\"Arup Group Limited\">Arup</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ove_A<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Danish engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Arup_Group_Limited\" class=\"mw-redirect\" title=\"Arup Group Limited\">Arup</a> (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ove_<PERSON>"}, {"title": "Arup Group Limited", "link": "https://wikipedia.org/wiki/Arup_Group_Limited"}]}, {"year": "1896", "text": "<PERSON>, American journalist (d. 1952)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian footballer (d. 1944)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81rp%C3%A1d_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Polish chemist and academic (d. 1988)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish chemist and academic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish chemist and academic (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Russian-American madam and author (d. 1962)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American madam and author (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American madam and author (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American baseball player and manager (d. 1965)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Canadian-American vaudevillian, actress, and singer (d. 1983)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Fifi_D%27Orsay\" title=\"Fifi D'Orsay\"><PERSON><PERSON></a>, Canadian-American vaudevillian, actress, and singer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fifi_D%27Orsay\" title=\"Fifi D'Orsay\"><PERSON><PERSON></a>, Canadian-American vaudevillian, actress, and singer (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fifi_D%27Orsay"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Dutch businessman (d. 2005)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Frits_Philips\" title=\"Frits Philips\"><PERSON><PERSON></a>, Dutch businessman (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frits_Philips\" title=\"Frits Philips\"><PERSON><PERSON></a>, Dutch businessman (d. 2005)", "links": [{"title": "Frits Philips", "link": "https://wikipedia.org/wiki/Frits_Philips"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Canadian inventor and businessman, founded Bombardier Inc. (d. 1964)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Canadian inventor and businessman, founded <a href=\"https://wikipedia.org/wiki/Bombardier_Inc.\" title=\"Bombardier Inc.\">Bombardier Inc.</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Canadian inventor and businessman, founded <a href=\"https://wikipedia.org/wiki/Bombardier_Inc.\" title=\"Bombardier Inc.\">Bombardier Inc.</a> (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_Bombardier"}, {"title": "Bombardier Inc.", "link": "https://wikipedia.org/wiki/Bombardier_Inc."}]}, {"year": "1907", "text": "<PERSON>, Austrian-German politician (d. 1947)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>ruber\" title=\"August E<PERSON>ruber\">August <PERSON></a>, Austrian-German politician (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>ruber\" title=\"August Eigruber\">August <PERSON></a>, Austrian-German politician (d. 1947)", "links": [{"title": "August <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_Eigruber"}]}, {"year": "1908", "text": "<PERSON>, Sr., American businessman and activist (d. 2004)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American businessman and activist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American businessman and activist (d. 2004)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>."}]}, {"year": "1908", "text": "<PERSON>, French jazz bandleader (d. 1979)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French jazz bandleader (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French jazz bandleader (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, American journalist and author (d. 1994)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Berton_Rouech%C3%A9"}]}, {"year": "1911", "text": "<PERSON>, English-Russian spy (d. 1963)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Russian spy (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Russian spy (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor (d. 1955)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Canadian politician, first Mayor of Mississauga (d. 1972)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/1915\" title=\"1915\">1915</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician, first <a href=\"https://wikipedia.org/wiki/Mayor_of_Mississauga\" title=\"Mayor of Mississauga\">Mayor of Mississauga</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1915\" title=\"1915\">1915</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician, first <a href=\"https://wikipedia.org/wiki/Mayor_of_Mississauga\" title=\"Mayor of Mississauga\">Mayor of Mississauga</a> (d. 1972)", "links": [{"title": "1915", "link": "https://wikipedia.org/wiki/1915"}, {"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Mayor of Mississauga", "link": "https://wikipedia.org/wiki/Mayor_of_Mississauga"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish author, poet, and translator (d. 1979)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Beh%C3%A7et_Necatigil\" title=\"Behçet Necatigil\"><PERSON><PERSON><PERSON><PERSON> Necatigil</a>, Turkish author, poet, and translator (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beh%C3%A7et_Necatigil\" title=\"Behçet Necatigil\"><PERSON><PERSON><PERSON><PERSON> Necatigil</a>, Turkish author, poet, and translator (d. 1979)", "links": [{"title": "Behçet Necatigil", "link": "https://wikipedia.org/wiki/Beh%C3%A7et_Necatigil"}]}, {"year": "1917", "text": "<PERSON>, 18th Duchess of Medinaceli (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Victoria_Eugenia_Fern%C3%A1ndez_de_C%C3%B3rdo<PERSON>,_18th_Duchess_of_Medinaceli\" title=\"<PERSON>, 18th Duchess of Medinaceli\"><PERSON>, 18th Duchess of Medinaceli</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Eugenia_Fern%C3%A1ndez_de_C%C3%B3<PERSON><PERSON>,_18th_Duchess_of_Medinaceli\" title=\"<PERSON>, 18th Duchess of Medinaceli\"><PERSON>, 18th Duchess of Medinaceli</a> (d. 2013)", "links": [{"title": "<PERSON>, 18th Duchess of Medinaceli", "link": "https://wikipedia.org/wiki/Victoria_Eugenia_Fern%C3%A1ndez_de_C%C3%B3<PERSON><PERSON>,_18th_Duchess_of_Medinaceli"}]}, {"year": "1917", "text": "<PERSON>, American actor (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2007)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Chinese-American monk and author (d. 1995)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-American monk and author (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-American monk and author (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1918", "text": "<PERSON>, Irish actor, comedian, and writer (d. 2002)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor, comedian, and writer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor, comedian, and writer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, American dancer and choreographer (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American dancer and choreographer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American dancer and choreographer (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Italian singer (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Mexican architect (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADrez_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican architect (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADrez_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican architect (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Ram%C3%ADrez_V%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Sri Lankan politician (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan politician (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON> of Denmark (d. 1986)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Denmark\" title=\"Prince <PERSON> of Denmark\">Prince <PERSON> of Denmark</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Denmark\" title=\"Prince <PERSON> of Denmark\">Prince <PERSON> of Denmark</a> (d. 1986)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1921", "text": "<PERSON>, German historian and author (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English actor, director, producer, and screenwriter (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and screenwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English novelist, poet, and critic (d. 1995)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, poet, and critic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, poet, and critic (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American colonel (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Belgian politician, 43rd Prime Minister of Belgium (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician, 43rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician, 43rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "1923", "text": "<PERSON>, American composer (d. 2006)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON> <PERSON><PERSON>, American sergeant, lawyer, and politician, 28th Governor of West Virginia (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American sergeant, lawyer, and politician, 28th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON><PERSON>.</a>, American sergeant, lawyer, and politician, 28th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a> (d. 2015)", "links": [{"title": "<PERSON> <PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Governor of West Virginia", "link": "https://wikipedia.org/wiki/Governor_of_West_Virginia"}]}, {"year": "1924", "text": "<PERSON>, English academic and businessman (d. 2008)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and businessman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and businessman (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American composer and conductor (d. 1994)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian diplomat, author, and philanthropist (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian diplomat, author, and philanthropist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian diplomat, author, and philanthropist (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, French pharmacist, founded Laboratoires Pierre Fabre (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, French pharmacist, founded <a href=\"https://wikipedia.org/wiki/Laboratoires_<PERSON>\" title=\"Laboratoires Pierre <PERSON>\">Laboratoires <PERSON></a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, French pharmacist, founded <a href=\"https://wikipedia.org/wiki/Laboratoires_<PERSON>\" title=\"Laboratoires Pierre <PERSON>\">Laboratoires <PERSON></a> (d. 2013)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}, {"title": "Laboratoires Pierre Fabre", "link": "https://wikipedia.org/wiki/Laboratoires_Pierre_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American actress and singer (d. 2008)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON> (d. 2022)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> (d. 2022)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, German actor (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American football player (d. 2002)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Night_Train_Lane\" title=\"Night Train Lane\">Night Train Lane</a>, American football player (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Night_Train_Lane\" title=\"Night Train Lane\">Night Train Lane</a>, American football player (d. 2002)", "links": [{"title": "Night Train Lane", "link": "https://wikipedia.org/wiki/Night_Train_Lane"}]}, {"year": "1929", "text": "<PERSON>, American singer (d. 1969)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian biologist and ecologist (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist and ecologist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist and ecologist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer-songwriter and producer (d. 2003)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Australian footballer and educator (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and educator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American flute player and composer (d. 2003)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American flute player and composer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American flute player and composer (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American politician, 54th Governor of Kentucky (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Kentucky", "link": "https://wikipedia.org/wiki/Governor_of_Kentucky"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American lawyer and politician (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Spanish footballer (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English journalist and author", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American composer, arranger and musician (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American composer, arranger and musician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American composer, arranger and musician (d. 2021)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1933", "text": "<PERSON>, Russian long jumper (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian long jumper (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian long jumper (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vera_<PERSON>a"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American journalist and actor (d. 2008)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and actor (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English singer-songwriter (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Vince Hill\"><PERSON></a>, English singer-songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Hill\" title=\"Vince Hill\"><PERSON></a>, English singer-songwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vince_Hill"}]}, {"year": "1934", "text": "<PERSON>, Australian producer and manager (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian producer and manager (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian producer and manager (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Australian politician, 36th Premier of New South Wales", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian politician, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian politician, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1934", "text": "Vicar, Chilean cartoonist (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Vicar_(cartoonist)\" title=\"Vicar (cartoonist)\">Vicar</a>, Chilean cartoonist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vicar_(cartoonist)\" title=\"Vicar (cartoonist)\">Vicar</a>, Chilean cartoonist (d. 2012)", "links": [{"title": "Vicar (cartoonist)", "link": "https://wikipedia.org/wiki/Vicar_(cartoonist)"}]}, {"year": "1935", "text": "<PERSON>, Canadian director and screenwriter", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcel_Carri%C3%A8re"}]}, {"year": "1935", "text": "<PERSON>, German poet and author (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" class=\"mw-redirect\" title=\"<PERSON> (poet)\"><PERSON></a>, German poet and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" class=\"mw-redirect\" title=\"<PERSON> (poet)\"><PERSON></a>, German poet and author (d. 2013)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Swedish boxer (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> R<PERSON>berg\"><PERSON><PERSON><PERSON></a>, Swedish boxer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> R<PERSON>berg\"><PERSON><PERSON><PERSON></a>, Swedish boxer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>berg"}]}, {"year": "1935", "text": "<PERSON>, French journalist and historian (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and historian (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and historian (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American singer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Russian physicist and academic (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(physicist)\" title=\"<PERSON><PERSON><PERSON> (physicist)\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(physicist)\" title=\"<PERSON><PERSON><PERSON> (physicist)\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(physicist)"}]}, {"year": "1937", "text": "<PERSON><PERSON>, South African hurdler and coach", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(athlete)\" title=\"<PERSON><PERSON> (athlete)\"><PERSON><PERSON></a>, South African hurdler and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(athlete)\" title=\"<PERSON><PERSON> (athlete)\"><PERSON><PERSON></a>, South African hurdler and coach", "links": [{"title": "<PERSON><PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(athlete)"}]}, {"year": "1937", "text": "<PERSON>, American wrestler and actor (d. 2017)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American baseball player", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Scottish lawyer and politician (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_politician)\" title=\"<PERSON> (Scottish politician)\"><PERSON></a>, Scottish lawyer and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_politician)\" title=\"<PERSON> (Scottish politician)\"><PERSON></a>, Scottish lawyer and politician (d. 2017)", "links": [{"title": "<PERSON> (Scottish politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_politician)"}]}, {"year": "1939", "text": "<PERSON>, American football player and coach (d. 2012)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2012)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1939", "text": "<PERSON>, English singer and record producer (d. 1999)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dusty Springfield\"><PERSON></a>, English singer and record producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dusty Springfield\"><PERSON></a>, English singer and record producer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian academic and politician, 18th Canadian Minister of Transport", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Beno%C3%AE<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian academic and politician, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Transport_(Canada)\" title=\"Minister of Transport (Canada)\">Canadian Minister of Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_<PERSON>ard\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian academic and politician, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Transport_(Canada)\" title=\"Minister of Transport (Canada)\">Canadian Minister of Transport</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beno%C3%AEt_Bo<PERSON>"}, {"title": "Minister of Transport (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Transport_(Canada)"}]}, {"year": "1940", "text": "<PERSON>, Barbadian cricketer (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "Queen <PERSON><PERSON><PERSON><PERSON> of Denmark", "html": "1940 - Queen <a href=\"https://wikipedia.org/wiki/Margrethe_II_of_Denmark\" class=\"mw-redirect\" title=\"Margrethe II of Denmark\">Margrethe II of Denmark</a>", "no_year_html": "Queen <a href=\"https://wikipedia.org/wiki/Margrethe_II_of_Denmark\" class=\"mw-redirect\" title=\"Margrethe II of Denmark\">Margrethe II of Denmark</a>", "links": [{"title": "Margrethe II of Denmark", "link": "https://wikipedia.org/wiki/Margrethe_II_of_Denmark"}]}, {"year": "1940", "text": "<PERSON>, American painter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, 7th Baron <PERSON>, English banker and politician, Lord Chamberlain of the United Kingdom (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Baron_<PERSON>\" title=\"<PERSON>, 7th Baron <PERSON>\"><PERSON>, 7th Baron <PERSON></a>, English banker and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chamberlain\" title=\"Lord Chamberlain\">Lord Chamberlain of the United Kingdom</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Baron_<PERSON>\" title=\"<PERSON>, 7th Baron <PERSON>\"><PERSON>, 7th Baron <PERSON></a>, English banker and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chamberlain\" title=\"Lord Chamberlain\">Lord Chamberlain of the United Kingdom</a> (d. 2023)", "links": [{"title": "<PERSON>, 7th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Baron_<PERSON>"}, {"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American director and producer (d. 2012)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American baseball pitcher", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "Sir <PERSON>, English businessman, founded the Williams F1 Racing Team (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\">Sir <PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/Williams_F1\" class=\"mw-redirect\" title=\"Williams F1\">Williams F1 Racing Team</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\">Sir <PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/Williams_F1\" class=\"mw-redirect\" title=\"Williams F1\">Williams F1 Racing Team</a> (d. 2021)", "links": [{"title": "Sir <PERSON>", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>"}, {"title": "Williams F1", "link": "https://wikipedia.org/wiki/Williams_F1"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Austrian-German businessman", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-German businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-German businessman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian cricketer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (Australian cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_cricketer)"}]}, {"year": "1945", "text": "<PERSON>, American lawyer and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Maine_politician)\" title=\"<PERSON> (Maine politician)\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Maine_politician)\" title=\"<PERSON> (Maine politician)\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON> (Maine politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Maine_politician)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American journalist and author (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Dutch politician (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Liberian lawyer and politician, 18th Chief Justice of Liberia (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Liberian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Liberia\" title=\"Chief Justice of Liberia\">Chief Justice of Liberia</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Liberian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Liberia\" title=\"Chief Justice of Liberia\">Chief Justice of Liberia</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Justice of Liberia", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Liberia"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American flute player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American flute player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American flute player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Scottish singer-songwriter (d. 2011)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian businessman and politician, 17th Canadian President of the Treasury Board (d. 2011)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_the_Treasury_Board_(Canada)\" class=\"mw-redirect\" title=\"President of the Treasury Board (Canada)\">Canadian President of the Treasury Board</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_the_Treasury_Board_(Canada)\" class=\"mw-redirect\" title=\"President of the Treasury Board (Canada)\">Canadian President of the Treasury Board</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Treasury Board (Canada)", "link": "https://wikipedia.org/wiki/President_of_the_Treasury_Board_(Canada)"}]}, {"year": "1950", "text": "<PERSON>, American actor (d. 2001)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Australian singer and actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Romanian author and photographer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian author and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian author and photographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, French actor and director (d. 2024)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Israeli sprinter and hurdler", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli sprinter and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli sprinter and hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian singer-songwriter and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian racing driver", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1954", "text": "<PERSON>, Canadian ice hockey player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Grand Duke of Luxembourg", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Luxembourg\" title=\"<PERSON>, Grand Duke of Luxembourg\"><PERSON>, Grand Duke of Luxembourg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Luxembourg\" title=\"<PERSON>, Grand Duke of Luxembourg\"><PERSON>, Grand Duke of Luxembourg</a>", "links": [{"title": "<PERSON>, Grand Duke of Luxembourg", "link": "https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Luxembourg"}]}, {"year": "1956", "text": "<PERSON>, American captain, pilot, and astronaut (d. 2003)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American keyboard player, composer, and producer (d. 2010)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/T_<PERSON><PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON></a>, American keyboard player, composer, and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON></a>, American keyboard player, composer, and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss skier", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss skier", "links": [{"title": "Li<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Belgian philosopher, author, and academic (d. 2009)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian philosopher, author, and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian philosopher, author, and academic (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English photographer and director", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Swedish guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English-Scottish field hockey player and lawyer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish field hockey player and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish field hockey player and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Filipino politician (d. 2007)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino politician (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Spanish footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADtez\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADtez\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_Ben%C3%ADtez"}]}, {"year": "1960", "text": "<PERSON>, German footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, seventh Chief Minister of Arunachal Pradesh (d. 2014)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Jarbom_Gamlin\" title=\"Jarbom Gamlin\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, seventh <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Arunachal_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Arunachal Pradesh\">Chief Minister of Arunachal Pradesh</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jarbom_Gamlin\" title=\"Jar<PERSON>m Gamlin\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, seventh <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Arunachal_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Arunachal Pradesh\">Chief Minister of Arunachal Pradesh</a> (d. 2014)", "links": [{"title": "Jarbom Gamlin", "link": "https://wikipedia.org/wiki/Jarbom_Gamlin"}, {"title": "Chief Minister of Arunachal Pradesh", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Arunachal_Pradesh"}]}, {"year": "1962", "text": "<PERSON>, Italian journalist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American singer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Swedish pianist (d. 2008)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Esbj%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish pianist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esbj%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish pianist (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Esbj%C3%B6<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Canadian politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yves-Fran%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Norwegian drummer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian drummer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American wrestler and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, German runner", "html": "1968 - <a href=\"https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%<PERSON>diger_<PERSON>zel"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Swedish skier", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Patrik_J%C3%A4rbyn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pat<PERSON>_J%C3%A4rbyn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patrik_J%C3%A4rbyn"}]}, {"year": "1969", "text": "<PERSON>, American baseball player and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_Vi%C3%B1a"}]}, {"year": "1970", "text": "<PERSON><PERSON>, German singer-songwriter and drummer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>o_Go<PERSON>\" title=\"<PERSON>o Go<PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Go<PERSON>\" title=\"Dero Go<PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and drummer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dero_Goi"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian filmmaker", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian filmmaker", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American basketball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cameron Blades\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Blades\" title=\"Cameron Blades\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cameron_Blades"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter, actress, and fashion designer (d. 1995)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Selena\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, actress, and fashion designer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Selena\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, actress, and fashion designer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Selena"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Japanese racing driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Belarusian tennis player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Spanish-American tennis player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Conchita_Mart%C3%ADnez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Conchita_Mart%C3%ADnez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Conchita_Mart%C3%ADnez"}]}, {"year": "1972", "text": "<PERSON>, American poet and educator", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Senegalese-American singer, rapper and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Akon\" title=\"Akon\"><PERSON><PERSON></a>, Senegalese-American singer, rapper and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Akon\" title=\"Akon\"><PERSON><PERSON></a>, Senegalese-American singer, rapper and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Akon"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish golfer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Charlotta_S%C3%B6renstam\" title=\"Charlotta Sörenstam\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charlotta_S%C3%B6renstam\" title=\"Charlotta Sörenstam\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish golfer", "links": [{"title": "Charlotta Sörenstam", "link": "https://wikipedia.org/wiki/Charlotta_S%C3%B6renstam"}]}, {"year": "1973", "text": "<PERSON>, Spanish-Ecuadorian expressionist and representational sculptor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Spanish-Ecuadorian expressionist and representational sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Spanish-Ecuadorian expressionist and representational sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%B1a"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American actor and musician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American actress and singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Hara\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Hara\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kelli_O%27Hara"}]}, {"year": "1977", "text": "<PERSON>, Swedish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Dutch racing driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German pole vaulter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON>ling\" title=\"<PERSON>\"><PERSON></a>, German pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON>ling\" title=\"<PERSON>\"><PERSON></a>, German pole vaulter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lars_B%C3%B6rgeling"}]}, {"year": "1979", "text": "<PERSON>, New Zealand rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Anest<PERSON>_Agritis\" title=\"An<PERSON><PERSON> Agritis\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Agrit<PERSON>\" title=\"An<PERSON><PERSON> Agrit<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>is", "link": "https://wikipedia.org/wiki/Anest<PERSON>_Agritis"}]}, {"year": "1981", "text": "<PERSON>, Israeli singer-songwriter and pianist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Canadian football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>eu_Proulx\" title=\"<PERSON><PERSON><PERSON> Proulx\"><PERSON><PERSON><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Proulx\" title=\"<PERSON><PERSON>eu Proulx\"><PERSON><PERSON><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ulx"}]}, {"year": "1982", "text": "<PERSON>, American mixed martial artist and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, French basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Mari%C3%A9_Digby\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mari%C3%A9_Digby\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mari%C3%A9_Digby"}]}, {"year": "1983", "text": "<PERSON>, American softball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American softball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American softball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American composer and producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Teddy Blass\"><PERSON></a>, American composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Teddy Blass\"><PERSON></a>, American composer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American speed skater", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Ki<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Ki<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Jamaican sprinter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Sudanese-English basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sudanese-English basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sudanese-English basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Deng"}]}, {"year": "1985", "text": "<PERSON>, American mixed martial artist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, New Zealand rugby player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Nigerian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wo"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Okazaki"}]}, {"year": "1986", "text": "<PERSON>, Danish ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Dutch gymnast", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>pke Zond<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Epke Zond<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch gymnast", "links": [{"title": "Epke Z<PERSON>", "link": "https://wikipedia.org/wiki/Epke_Zonderland"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Turkish basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ce<PERSON>_<PERSON>\" title=\"Cenk Akyol\"><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Cenk Akyol\"><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "Cenk <PERSON>", "link": "https://wikipedia.org/wiki/Cenk_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English international footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1990)\" title=\"<PERSON> (basketball, born 1990)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1990)\" title=\"<PERSON> (basketball, born 1990)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1990)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1990)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American sprinter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, South Korean footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jung\" title=\"<PERSON>jung\"><PERSON>j<PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jung\" title=\"<PERSON>-jung\"><PERSON>j<PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jung"}]}, {"year": "1993", "text": "<PERSON> the <PERSON>per, American rapper", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Rapper\" title=\"<PERSON> the Rapper\"><PERSON> the Rapper</a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Rapper\" title=\"<PERSON> the Rapper\"><PERSON> the Rapper</a>, American rapper", "links": [{"title": "<PERSON> the Rapper", "link": "https://wikipedia.org/wiki/<PERSON>_the_Rapper"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Japanese-American figure skater", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u\" title=\"<PERSON><PERSON> Nagasu\"><PERSON><PERSON></a>, Japanese-American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Nagasu\"><PERSON><PERSON></a>, Japanese-American figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mirai_<PERSON>gasu"}]}, {"year": "1996", "text": "<PERSON>-<PERSON>, Argentine-British actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine-British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine-British actress", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actress", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sink\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sink\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>k"}]}], "Deaths": [{"year": "69", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. AD 32)", "html": "69 - AD 69 - <a href=\"https://wikipedia.org/wiki/Otho\" title=\"<PERSON>th<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. AD 32)", "no_year_html": "AD 69 - <a href=\"https://wikipedia.org/wiki/Otho\" title=\"<PERSON>th<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. AD 32)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Otho"}]}, {"year": "665", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Braga, French archbishop and saint", "html": "665 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_of_Braga\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Braga\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Braga</a>, French archbishop and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_of_Braga\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Braga\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Braga</a>, French archbishop and saint", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Braga", "link": "https://wikipedia.org/wiki/Fructuosus_of_Braga"}]}, {"year": "1090", "text": "<PERSON><PERSON><PERSON><PERSON>, duchess of Apulia (b. c. 1040)", "html": "1090 - <a href=\"https://wikipedia.org/wiki/<PERSON>kel<PERSON><PERSON>\" title=\"<PERSON>kel<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, duchess of Apulia (b. c. <a href=\"https://wikipedia.org/wiki/1040\" title=\"1040\">1040</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sikel<PERSON><PERSON>\" title=\"<PERSON>kel<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, duchess of Apulia (b. c. <a href=\"https://wikipedia.org/wiki/1040\" title=\"1040\">1040</a>)", "links": [{"title": "<PERSON>kel<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sikelgaita"}, {"title": "1040", "link": "https://wikipedia.org/wiki/1040"}]}, {"year": "1113", "text": "Sviatopolk II of Kiev (b. 1050)", "html": "1113 - <a href=\"https://wikipedia.org/wiki/Sviatopolk_II_of_Kiev\" title=\"Sviatopolk II of Kiev\">Sviatopolk II of Kiev</a> (b. 1050)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sviatopolk_II_of_Kiev\" title=\"Sviatopolk II of Kiev\">Sviatopolk II of Kiev</a> (b. 1050)", "links": [{"title": "Sviatopolk II of Kiev", "link": "https://wikipedia.org/wiki/Sviatopolk_II_of_Kiev"}]}, {"year": "1118", "text": "<PERSON>, regent of Sicily, mother of <PERSON> of Sicily, queen of Baldwin I of Jerusalem", "html": "1118 - <a href=\"https://wikipedia.org/wiki/Adelaide_del_Vasto\" title=\"Adelaide del Vasto\"><PERSON> del Vasto</a>, regent of Sicily, mother of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a>, queen of Baldwin I of Jerusalem", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_del_Vasto\" title=\"Adelaide del Vasto\"><PERSON> del Vasto</a>, regent of Sicily, mother of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a>, queen of <PERSON> of Jerusalem", "links": [{"title": "Adelaide del Vasto", "link": "https://wikipedia.org/wiki/Adelaide_del_Vasto"}, {"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}]}, {"year": "1198", "text": "<PERSON>, Duke of Austria (b. 1175)", "html": "1198 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria_(Babenberg)\" title=\"<PERSON>, Duke of Austria (Babenberg)\"><PERSON>, Duke of Austria</a> (b. 1175)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria_(Babenberg)\" title=\"<PERSON>, Duke of Austria (Babenberg)\"><PERSON>, Duke of Austria</a> (b. 1175)", "links": [{"title": "<PERSON>, Duke of Austria (Babenberg)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria_(Babenberg)"}]}, {"year": "1234", "text": "<PERSON>, 3rd Earl of Pembroke (b. 1191)", "html": "1234 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Pembroke\" title=\"<PERSON>, 3rd Earl of Pembroke\"><PERSON>, 3rd Earl of Pembroke</a> (b. 1191)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Pembroke\" title=\"<PERSON>, 3rd Earl of Pembroke\"><PERSON>, 3rd Earl of Pembroke</a> (b. 1191)", "links": [{"title": "<PERSON>, 3rd Earl of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Pembroke"}]}, {"year": "1375", "text": "<PERSON>, 2nd Earl of Pembroke, English nobleman and soldier (b. 1347)", "html": "1375 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke\" title=\"<PERSON>, 2nd Earl of Pembroke\"><PERSON>, 2nd Earl of Pembroke</a>, English nobleman and soldier (b. 1347)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke\" title=\"<PERSON>, 2nd Earl of Pembroke\"><PERSON>, 2nd Earl of Pembroke</a>, English nobleman and soldier (b. 1347)", "links": [{"title": "<PERSON>, 2nd Earl of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Pembroke"}]}, {"year": "1496", "text": "<PERSON>, Duke of Savoy (b. 1489)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a> (b. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a> (b. 1489)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy"}]}, {"year": "1587", "text": "<PERSON>, Duchess of Somerset (b. 1497)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Somerset\" title=\"<PERSON>, Duchess of Somerset\"><PERSON>, Duchess of Somerset</a> (b. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Somerset\" title=\"<PERSON>, Duchess of Somerset\"><PERSON>, Duchess of Somerset</a> (b. 1497)", "links": [{"title": "<PERSON>, Duchess of Somerset", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Somerset"}]}, {"year": "1640", "text": "Countess <PERSON> of Nassau (b. 1579)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_of_Nassau\" title=\"Countess <PERSON> of Nassau\">Countess <PERSON> of Nassau</a> (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_of_Nassau\" title=\"Countess <PERSON> of Nassau\">Countess <PERSON> of Nassau</a> (b. 1579)", "links": [{"title": "Countess <PERSON> of Nassau", "link": "https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_<PERSON>_Nassau"}]}, {"year": "1645", "text": "<PERSON>, Scottish soldier, viol player, and composer (b. 1569)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier, <a href=\"https://wikipedia.org/wiki/Viol\" title=\"Viol\">viol</a> player, and composer (b. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier, <a href=\"https://wikipedia.org/wiki/Viol\" title=\"Viol\">viol</a> player, and composer (b. 1569)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Viol", "link": "https://wikipedia.org/wiki/Viol"}]}, {"year": "1687", "text": "<PERSON>, 2nd Duke of Buckingham, English poet and politician, Lord Lieutenant of the West Riding of Yorkshire (b. 1628)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Buckingham\" title=\"<PERSON>, 2nd Duke of Buckingham\"><PERSON>, 2nd Duke of Buckingham</a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_the_West_Riding_of_Yorkshire\" title=\"Lord Lieutenant of the West Riding of Yorkshire\">Lord Lieutenant of the West Riding of Yorkshire</a> (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Buckingham\" title=\"<PERSON>, 2nd Duke of Buckingham\"><PERSON>, 2nd Duke of Buckingham</a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_the_West_Riding_of_Yorkshire\" title=\"Lord Lieutenant of the West Riding of Yorkshire\">Lord Lieutenant of the West Riding of Yorkshire</a> (b. 1628)", "links": [{"title": "<PERSON>, 2nd Duke of Buckingham", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Buckingham"}, {"title": "Lord Lieutenant of the West Riding of Yorkshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_the_West_Riding_of_Yorkshire"}]}, {"year": "1689", "text": "<PERSON><PERSON><PERSON>, English author and playwright (b. 1640)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author and playwright (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author and playwright (b. 1640)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON>, Italian poet and translator (b. 1672)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and translator (b. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and translator (b. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1756", "text": "<PERSON>, French astronomer (b. 1677)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1677)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1677)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON>, Czech astronomer and educator (b. 1719)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, Czech astronomer and educator (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, Czech astronomer and educator (b. 1719)", "links": [{"title": "<PERSON> (astronomer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)"}]}, {"year": "1788", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, French mathematician, cosmologist, and author (b. 1707)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> de B<PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON></a>, French mathematician, cosmologist, and author (b. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> de B<PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON></a>, French mathematician, cosmologist, and author (b. 1707)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, Spanish-French painter and illustrator (b. 1746)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Francisco_Goya\" title=\"Francisco Goya\"><PERSON></a>, Spanish-French painter and illustrator (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Goya\" title=\"Francisco Goya\"><PERSON></a>, Spanish-French painter and illustrator (b. 1746)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Goya"}]}, {"year": "1846", "text": "<PERSON>, Italian bassist and composer (b. 1763)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian bassist and composer (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian bassist and composer (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, French-English sculptor, founded the Madame Tussauds Wax Museum (b. 1761)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English sculptor, founded the <a href=\"https://wikipedia.org/wiki/Madame_<PERSON>\" title=\"Madame Tussauds\">Madame Tussauds Wax Museum</a> (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English sculptor, founded the <a href=\"https://wikipedia.org/wiki/Madame_<PERSON>\" title=\"Madame Tussauds\">Madame Tussauds Wax Museum</a> (b. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON> Tu<PERSON>uds", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, French historian and philosopher, French Minister of Foreign Affairs (b. 1805)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and philosopher, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a> (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and philosopher, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a> (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (France)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, French nun and saint (b. 1844)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Bernadette_Soubirous\" title=\"Bernadette Soubirous\">Bernadette Soubirous</a>, French nun and saint (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bernadette_Soubirous\" title=\"Bernadette Soubirous\"><PERSON><PERSON><PERSON>ubi<PERSON></a>, French nun and saint (b. 1844)", "links": [{"title": "Bernadette Soubirous", "link": "https://wikipedia.org/wiki/Bernadette_Soubirous"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, Polish physicist and chemist (b. 1845)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Zygmunt_Florenty_Wr%C3%B3<PERSON><PERSON>\" title=\"Zygmunt Florenty <PERSON>\">Zygmunt <PERSON>lore<PERSON><PERSON></a>, Polish physicist and chemist (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zygmunt_Florenty_Wr%C3%B3<PERSON><PERSON>\" title=\"Zygmunt Florenty W<PERSON>\">Zygmunt <PERSON>lore<PERSON><PERSON></a>, Polish physicist and chemist (b. 1845)", "links": [{"title": "<PERSON><PERSON>g<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zygmunt_Florenty_Wr%C3%B3<PERSON><PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Filipino journalist and activist (b. 1875)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and activist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and activist (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, German poet and author (b. 1888)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Scottish-English author (b. 1812)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Smile<PERSON>\"><PERSON></a>, Scottish-English author (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Smiles\"><PERSON></a>, Scottish-English author (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1914", "text": "<PERSON>, American astronomer and mathematician (b. 1838)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and mathematician (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George <PERSON>\"><PERSON></a>, American astronomer and mathematician (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American businessman and politician (b. 1841)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Bulgarian general (b. 1867)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian general (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian general (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian businessman, founded Henry Birks and Sons (b. 1840)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_Sons\" class=\"mw-redirect\" title=\"Henry Birks and Sons\">Henry <PERSON> and Sons</a> (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_Sons\" class=\"mw-redirect\" title=\"Henry Birks and Sons\">Henry <PERSON> and Sons</a> (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Henry Birks and Sons", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_Sons"}]}, {"year": "1928", "text": "<PERSON>, Estonian wrestler (b. 1900)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Roman Steinberg\"><PERSON></a>, Estonian wrestler (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Roman Steinberg\"><PERSON></a>, Estonian wrestler (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Peruvian journalist, philosopher, and activist (b. 1894)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%A1tegui\" title=\"<PERSON>\"><PERSON></a>, Peruvian journalist, philosopher, and activist (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%A1tegui\" title=\"<PERSON>\"><PERSON></a>, Peruvian journalist, philosopher, and activist (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%A1tegui"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Romanian journalist and author (b. 1884)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Panait Is<PERSON>ti\"><PERSON><PERSON></a>, Romanian journalist and author (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Panait Is<PERSON>ti\"><PERSON><PERSON></a>, Romanian journalist and author (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panait_Istrati"}]}, {"year": "1937", "text": "<PERSON>, American military engineer and politician, third Governor of the Panama Canal Zone (b. 1870)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military engineer and politician, third <a href=\"https://wikipedia.org/wiki/Governor_of_the_Panama_Canal_Zone\" class=\"mw-redirect\" title=\"Governor of the Panama Canal Zone\">Governor of the Panama Canal Zone</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military engineer and politician, third <a href=\"https://wikipedia.org/wiki/Governor_of_the_Panama_Canal_Zone\" class=\"mw-redirect\" title=\"Governor of the Panama Canal Zone\">Governor of the Panama Canal Zone</a> (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of the Panama Canal Zone", "link": "https://wikipedia.org/wiki/Governor_of_the_Panama_Canal_Zone"}]}, {"year": "1938", "text": "<PERSON>, English footballer and manager (b. 1874)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Irish Republican died while on hunger strike (b. 1908)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Arcy\" title=\"<PERSON>\"><PERSON></a>, Irish Republican died while on hunger strike (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Arcy\" title=\"<PERSON>\"><PERSON></a>, Irish Republican died while on hunger strike (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tony_<PERSON>%27Arcy"}]}, {"year": "1941", "text": "<PERSON>, 1st Baron <PERSON>, English economist and civil servant (b. 1880)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English economist and civil servant (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English economist and civil servant (b. 1880)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1942", "text": "Princess <PERSON> of Saxe-Coburg and Gotha (b. 1878)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Princess_Alexandra_of_Saxe-Coburg_and_Gotha\" title=\"Princess Alexandra of Saxe-Coburg and Gotha\">Princess <PERSON> of Saxe-Coburg and Gotha</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Alexandra_of_Saxe-Coburg_and_Gotha\" title=\"Princess Alexandra of Saxe-Coburg and Gotha\">Princess <PERSON> of Saxe-Coburg and Gotha</a> (b. 1878)", "links": [{"title": "Princess <PERSON> of Saxe-Coburg and Gotha", "link": "https://wikipedia.org/wiki/Princess_Alexandra_of_Saxe-Coburg_and_Gotha"}]}, {"year": "1942", "text": "<PERSON>, Irish polo player (b. 1862)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Denis_<PERSON>._<PERSON>_<PERSON>\" title=\"Denis <PERSON>. George <PERSON>\"><PERSON></a>, Irish polo player (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Denis_<PERSON>_<PERSON>_<PERSON>\" title=\"Denis <PERSON>. George <PERSON>\"><PERSON></a>, Irish polo player (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Denis_<PERSON>._<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Swiss-American race car driver and engineer (b. 1884)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Chevrolet\" title=\"Arthur Chevrolet\"><PERSON></a>, Swiss-American race car driver and engineer (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Chevrolet\" title=\"Arthur Chevrolet\"><PERSON></a>, Swiss-American race car driver and engineer (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, German SS officer (b. 1900)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ss\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ss\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rudolf_H%C3%B6ss"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1950", "text": "<PERSON>, Estonian composer, conductor, and critic (b. 1905)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian composer, conductor, and critic (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian composer, conductor, and critic (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Danish target shooter (b. 1867)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish target shooter (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish target shooter (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Scottish engineer and politician (b. 1872)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and politician (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and politician (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, English biophysicist and academic (b. 1920)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English biophysicist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English biophysicist and academic (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian actor, screenwriter and film director (b. 1884)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Mih%C3%<PERSON><PERSON>_<PERSON>kete\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian actor, screenwriter and film director (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mih%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian actor, screenwriter and film director (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mih%C3%A1ly_Fekete"}]}, {"year": "1961", "text": "<PERSON>, American psychologist and academic (b. 1912)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English soldier and colonial administrator (b. 1884)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonial_administrator)\" title=\"<PERSON> (colonial administrator)\"><PERSON></a>, English soldier and colonial administrator (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonial_administrator)\" title=\"<PERSON> (colonial administrator)\"><PERSON></a>, English soldier and colonial administrator (b. 1884)", "links": [{"title": "<PERSON> (colonial administrator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonial_administrator)"}]}, {"year": "1965", "text": "<PERSON>, English actor, comedian, brother of <PERSON> (b. 1885)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Sydney_Chaplin\" title=\"<PERSON> Chaplin\"><PERSON></a>, English actor, comedian, brother of <PERSON> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Chaplin\" title=\"Sydney Chaplin\"><PERSON></a>, English actor, comedian, brother of <PERSON> (b. 1885)", "links": [{"title": "Sydney Chaplin", "link": "https://wikipedia.org/wiki/Sydney_Chaplin"}]}, {"year": "1966", "text": "<PERSON>, Australian author (b. 1918)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Australian author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Australian author (b. 1918)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1968", "text": "<PERSON>, American actress (b. 1893)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American novelist, short story writer, and playwright (b. 1885)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and playwright (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and playwright (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Thai illustrator and painter (b. 1904)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai illustrator and painter (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai illustrator and painter (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Austrian-American architect, designed the Los Angeles County Hall of Records (b. 1892)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American architect, designed the <a href=\"https://wikipedia.org/wiki/Los_Angeles_County_Hall_of_Records\" title=\"Los Angeles County Hall of Records\">Los Angeles County Hall of Records</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American architect, designed the <a href=\"https://wikipedia.org/wiki/Los_Angeles_County_Hall_of_Records\" title=\"Los Angeles County Hall of Records\">Los Angeles County Hall of Records</a> (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Los Angeles County Hall of Records", "link": "https://wikipedia.org/wiki/Los_Angeles_County_Hall_of_Records"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Hungarian politician, Hungarian Minister of Defence (b. 1897)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/P%C3%<PERSON><PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON></a>, Hungarian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)\" class=\"mw-redirect\" title=\"Ministry of Defence (Hungary)\">Hungarian Minister of Defence</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%<PERSON><PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON></a>, Hungarian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)\" class=\"mw-redirect\" title=\"Ministry of Defence (Hungary)\">Hungarian Minister of Defence</a> (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/P%C3%A<PERSON><PERSON>_<PERSON><PERSON>_(politician)"}, {"title": "Ministry of Defence (Hungary)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Japanese novelist and short story writer, Nobel Prize laureate (b. 1899)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1972", "text": "<PERSON>, Australian public servant (b. 1894)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (b. 1894)", "links": [{"title": "<PERSON> (public servant)", "link": "https://wikipedia.org/wiki/Frank_<PERSON>%27C<PERSON><PERSON>_(public_servant)"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Hungarian conductor and educator (b. 1929)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Kert%C3%A9<PERSON>_(conductor)\" title=\"<PERSON><PERSON><PERSON> (conductor)\"><PERSON><PERSON><PERSON></a>, Hungarian conductor and educator (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Kert%C3%A9<PERSON>_(conductor)\" title=\"<PERSON><PERSON><PERSON> (conductor)\"><PERSON><PERSON><PERSON></a>, Hungarian conductor and educator (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON> (conductor)", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_Kert%C3%A9sz_(conductor)"}]}, {"year": "1978", "text": "<PERSON>, American officer and military governor in occupied Germany (b. 1898)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American officer and military governor in occupied Germany (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American officer and military governor in occupied Germany (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American composer (b. 1898)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_St<PERSON>ff"}]}, {"year": "1985", "text": "<PERSON>, American actor (b. 1924)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Palestinian commander, founded Fat<PERSON> (b. 1935)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian commander, founded <a href=\"https://wikipedia.org/wiki/Fatah\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>W<PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian commander, founded <a href=\"https://wikipedia.org/wiki/Fatah\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ah"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Russian pianist (b. 1954)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian pianist (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian pianist (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American baseball player and umpire (b. 1899)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and umpire (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and umpire (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON> Japanese author and educator (b. 1915)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> Japanese author and educator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> Japanese author and educator (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English cricketer (b. 1940)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer, manager and president (b. 1910)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Hakk%C4%B1_Yeten\" title=\"Hakk<PERSON> Yeten\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Turkish footballer, manager and president (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hakk%C4%B1_Yeten\" title=\"Hakk<PERSON> Yeten\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Turkish footballer, manager and president (b. 1910)", "links": [{"title": "Hakkı Yeten", "link": "https://wikipedia.org/wiki/Hakk%C4%B1_Yeten"}]}, {"year": "1991", "text": "<PERSON>, English director, producer, and screenwriter (b. 1908)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor (b. 1920)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brand\"><PERSON></a>, American actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Neville Brand\"><PERSON></a>, American actor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Romanian spy and activist (b. 1915)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian spy and activist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian spy and activist (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American singer and actor (b. 1919)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actor (b. 1919)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian labor unionist (b. 1919)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89milien_<PERSON><PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian labor unionist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89milien_<PERSON><PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian labor unionist (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89milien_<PERSON>p%C3%A9"}]}, {"year": "1994", "text": "<PERSON>, American novelist and critic (b. 1913)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and critic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and critic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American actress and dancer (b. 1917)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Colombian politician (b. 1921)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Esmeralda_Arboleda_Cadavid\" title=\"Esmeralda Arboleda Cadavid\"><PERSON><PERSON><PERSON><PERSON> Arboleda Cada<PERSON></a>, Colombian politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esmeralda_Arboleda_Cadavid\" title=\"Esmeralda Arboleda Cadavid\"><PERSON><PERSON><PERSON><PERSON> Arboleda Cada<PERSON></a>, Colombian politician (b. 1921)", "links": [{"title": "Esmeralda Arboleda Cadavid", "link": "https://wikipedia.org/wiki/Esmeralda_Arboleda_Cadavid"}]}, {"year": "1997", "text": "<PERSON>, French actor, director, and painter (b. 1938)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and painter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and painter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Argentinian-American mathematician and academic (b. 1920)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian-American mathematician and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian-American mathematician and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberto_Calder%C3%B3n"}]}, {"year": "1998", "text": "<PERSON>, English snooker player (b. 1913)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player (b. 1913)", "links": [{"title": "<PERSON> (snooker player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Canadian super-centenarian (b. 1880)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian super-centenarian (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian super-centenarian (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Canadian-American singer-songwriter and guitarist (b. 1946)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American singer-songwriter and guitarist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American singer-songwriter and guitarist (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American actor (b. 1918)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American director, producer, and screenwriter (b. 1938)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" class=\"mw-redirect\" title=\"<PERSON> (film director)\"><PERSON></a>, American director, producer, and screenwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(film_director)\" class=\"mw-redirect\" title=\"<PERSON> (film director)\"><PERSON></a>, American director, producer, and screenwriter (b. 1938)", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_(film_director)"}]}, {"year": "2001", "text": "<PERSON>, English footballer and manager (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English footballer and manager (b. 1952)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American businesswoman, founded Ruth's Chris Steak House (b. 1927)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_<PERSON>_<PERSON>_House\" title=\"<PERSON>'s Chris Steak House\"><PERSON>'s Chris Steak House</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>'s Chris Steak House\"><PERSON>'s Chris Steak House</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s Chris <PERSON> House", "link": "https://wikipedia.org/wiki/<PERSON>%27s_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actor (b. 1946)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Canadian actor (b. 1930)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English horticulturalist and author (b. 1909)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English horticulturalist and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English horticulturalist and author (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English actress, singer, and dancer (b. 1911)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and dancer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and dancer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, New Zealand astronomer (b. 1909)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand astronomer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand astronomer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player (b. 1962)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Ga%C3%A9<PERSON>_<PERSON>\" title=\"G<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ga%C3%A9<PERSON>_<PERSON>\" title=\"Ga<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (b. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ga%C3%A9tan_<PERSON>ne"}]}, {"year": "2007", "text": "<PERSON>, Brazilian swimmer (b. 1915)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian swimmer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian swimmer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Sri Lankan journalist", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "2008", "text": "<PERSON>, American mathematician and meteorologist (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and meteorologist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and meteorologist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Bosnian general and convicted war criminal (b. 1949)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Rasim_Deli%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian general and convicted war criminal (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rasi<PERSON>_Deli%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian general and convicted war criminal (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rasim_Deli%C4%87"}]}, {"year": "2010", "text": "<PERSON>, American police officer, created the D.A.R.E. Program (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer, created the <a href=\"https://wikipedia.org/wiki/Drug_Abuse_Resistance_Education\" title=\"Drug Abuse Resistance Education\">D.A.R.E. Program</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer, created the <a href=\"https://wikipedia.org/wiki/Drug_Abuse_Resistance_Education\" title=\"Drug Abuse Resistance Education\">D.A.R.E. Program</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Drug Abuse Resistance Education", "link": "https://wikipedia.org/wiki/Drug_Abuse_Resistance_Education"}]}, {"year": "2011", "text": "<PERSON>, Jamaican cricketer and veterinarian (b. 1928)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer and veterinarian (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer and veterinarian (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Canadian scholar and politician, tenth Premier of Saskatchewan (b. 1925)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian scholar and politician, tenth <a href=\"https://wikipedia.org/wiki/Premier_of_Saskatchewan\" title=\"Premier of Saskatchewan\">Premier of Saskatchewan</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian scholar and politician, tenth <a href=\"https://wikipedia.org/wiki/Premier_of_Saskatchewan\" title=\"Premier of Saskatchewan\">Premier of Saskatchewan</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Saskatchewan", "link": "https://wikipedia.org/wiki/Premier_of_Saskatchewan"}]}, {"year": "2011", "text": "<PERSON>, American screenwriter and producer (b. 1910)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Sol_Saks\" title=\"Sol Saks\"><PERSON></a>, American screenwriter and producer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sol_Saks\" title=\"Sol Saks\"><PERSON></a>, American screenwriter and producer (b. 1910)", "links": [{"title": "Sol Saks", "link": "https://wikipedia.org/wiki/Sol_Saks"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Hungarian soprano (b. 1914)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ri_Barab%C3%A1s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian soprano (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ri_Barab%C3%A1s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian soprano (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ri_Barab%C3%A1s"}]}, {"year": "2012", "text": "<PERSON>, Polish author and academic (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish author and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish author and academic (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>up"}]}, {"year": "2012", "text": "<PERSON>, English clarinet player and conductor (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clarinet player and conductor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clarinet player and conductor (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Zambian lawyer and politician, 11th Vice-President of Zambia (b. 1956)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Vice-President_of_Zambia\" title=\"Vice-President of Zambia\">Vice-President of Zambia</a> (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Vice-President_of_Zambia\" title=\"Vice-President of Zambia\">Vice-President of Zambia</a> (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice-President of Zambia", "link": "https://wikipedia.org/wiki/Vice-President_of_Zambia"}]}, {"year": "2012", "text": "M<PERSON>rsk Mc-<PERSON><PERSON>, Danish businessman (b. 1913)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/M%C3%A6<PERSON>_<PERSON><PERSON>-<PERSON><PERSON>_M%C3%B8ller\" title=\"Mærsk Mc-<PERSON><PERSON>\">Mærsk Mc-<PERSON><PERSON></a>, Danish businessman (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A6<PERSON>_<PERSON><PERSON>-<PERSON><PERSON>_M%C3%B8ller\" title=\"Mærsk Mc-<PERSON><PERSON>\">Mærsk Mc-<PERSON><PERSON></a>, Danish businessman (b. 1913)", "links": [{"title": "Mærsk Mc-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A6rsk_<PERSON><PERSON>-<PERSON><PERSON>_M%C3%B8ller"}]}, {"year": "2012", "text": "<PERSON>, Italian footballer and coach (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Italian footballer and coach (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Italian footballer and coach (b. 1948)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "2013", "text": "<PERSON>, Gibraltarian politician (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gibraltarian politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gibraltarian politician (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Algerian politician (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian politician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Austrian politician, 18th Governor of Lower Austria (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Lower_Austria\" title=\"List of governors of Lower Austria\">Governor of Lower Austria</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Lower_Austria\" title=\"List of governors of Lower Austria\">Governor of Lower Austria</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of governors of Lower Austria", "link": "https://wikipedia.org/wiki/List_of_governors_of_Lower_Austria"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Finnish-Canadian ice hockey player (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-Canadian ice hockey player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-Canadian ice hockey player (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lund"}]}, {"year": "2013", "text": "<PERSON>, Canadian-American singer-songwriter (b. 1909)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player and sportscaster (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Mexican architect, designed the Tijuana Cultural Center and National Museum of Anthropology (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADrez_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican architect, designed the <a href=\"https://wikipedia.org/wiki/Tijuana_Cultural_Center\" title=\"Tijuana Cultural Center\">Tijuana Cultural Center</a> and <a href=\"https://wikipedia.org/wiki/National_Museum_of_Anthropology_(Mexico)\" title=\"National Museum of Anthropology (Mexico)\">National Museum of Anthropology</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADrez_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican architect, designed the <a href=\"https://wikipedia.org/wiki/Tijuana_Cultural_Center\" title=\"Tijuana Cultural Center\">Tijuana Cultural Center</a> and <a href=\"https://wikipedia.org/wiki/National_Museum_of_Anthropology_(Mexico)\" title=\"National Museum of Anthropology (Mexico)\">National Museum of Anthropology</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Ram%C3%ADrez_V%C3%<PERSON><PERSON><PERSON>"}, {"title": "Tijuana Cultural Center", "link": "https://wikipedia.org/wiki/Tijuana_Cultural_Center"}, {"title": "National Museum of Anthropology (Mexico)", "link": "https://wikipedia.org/wiki/National_Museum_of_Anthropology_(Mexico)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Liberian businessman and politician (b. 1949)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Liberian businessman and politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Liberian businessman and politician (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Finnish footballer and manager (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Au<PERSON>_Rytk%C3%B6nen\" title=\"<PERSON><PERSON> Rytkönen\"><PERSON><PERSON></a>, Finnish footballer and manager (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Au<PERSON>_Rytk%C3%B6nen\" title=\"<PERSON><PERSON> Rytkö<PERSON>\"><PERSON><PERSON></a>, Finnish footballer and manager (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aulis_Rytk%C3%B6nen"}]}, {"year": "2014", "text": "<PERSON>, Austrian-American historian and political scientist (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ernst <PERSON>ian Winter\"><PERSON></a>, Austrian-American historian and political scientist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ernst <PERSON> Winter\"><PERSON></a>, Austrian-American historian and political scientist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Russian ice hockey player and coach (b. 1948)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player and coach (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player and coach (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Czech lawyer and politician, fifth Prime Minister of the Czech Republic (b. 1969)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech lawyer and politician, fifth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Czech_Republic\" title=\"Prime Minister of the Czech Republic\">Prime Minister of the Czech Republic</a> (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech lawyer and politician, fifth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Czech_Republic\" title=\"Prime Minister of the Czech Republic\">Prime Minister of the Czech Republic</a> (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Czech Republic", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Czech_Republic"}]}, {"year": "2018", "text": "<PERSON>, American actor and magician (b. 1952)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and magician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and magician (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Australian politician (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, British actress (b. 1968)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, British choreographer (b. 1986)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British choreographer (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British choreographer (b. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Welsh rugby union player (b. 1940)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh <a href=\"https://wikipedia.org/wiki/Rugby_union\" title=\"Rugby union\">rugby union</a> player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh <a href=\"https://wikipedia.org/wiki/Rugby_union\" title=\"Rugby union\">rugby union</a> player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Rugby union", "link": "https://wikipedia.org/wiki/Rugby_union"}]}, {"year": "2024", "text": "<PERSON>, American baseball player (b. 1926)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American lawyer, author, and politician, 38th governor of Florida (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and politician, 38th <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Florida\" title=\"List of governors of Florida\">governor of Florida</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and politician, 38th <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Florida\" title=\"List of governors of Florida\">governor of Florida</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of governors of Florida", "link": "https://wikipedia.org/wiki/List_of_governors_of_Florida"}]}]}}