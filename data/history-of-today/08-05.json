{"date": "August 5", "url": "https://wikipedia.org/wiki/August_5", "data": {"Events": [{"year": "25", "text": "<PERSON><PERSON><PERSON> claims the throne as Emperor of China, restoring the Han dynasty after the collapse of the short-lived Xin dynasty.", "html": "25 - AD 25 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON><PERSON> of Han\"><PERSON><PERSON><PERSON></a> claims the throne as <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Emperor of China</a>, restoring the <a href=\"https://wikipedia.org/wiki/Han_dynasty\" title=\"Han dynasty\">Han dynasty</a> after the collapse of the short-lived <a href=\"https://wikipedia.org/wiki/Xin_dynasty\" title=\"Xin dynasty\">Xin dynasty</a>.", "no_year_html": "AD 25 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON><PERSON> of Han\"><PERSON><PERSON><PERSON></a> claims the throne as <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Emperor of China</a>, restoring the <a href=\"https://wikipedia.org/wiki/Han_dynasty\" title=\"Han dynasty\">Han dynasty</a> after the collapse of the short-lived <a href=\"https://wikipedia.org/wiki/Xin_dynasty\" title=\"Xin dynasty\">Xin dynasty</a>.", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Han"}, {"title": "Emperor of China", "link": "https://wikipedia.org/wiki/Emperor_of_China"}, {"title": "Han dynasty", "link": "https://wikipedia.org/wiki/Han_dynasty"}, {"title": "Xin dynasty", "link": "https://wikipedia.org/wiki/Xin_dynasty"}]}, {"year": "70", "text": "Fires resulting from the destruction of the Second Temple in Jerusalem are extinguished.", "html": "70 - Fires resulting from the destruction of the <a href=\"https://wikipedia.org/wiki/Second_Temple\" title=\"Second Temple\">Second Temple</a> in Jerusalem are extinguished.", "no_year_html": "Fires resulting from the destruction of the <a href=\"https://wikipedia.org/wiki/Second_Temple\" title=\"Second Temple\">Second Temple</a> in Jerusalem are extinguished.", "links": [{"title": "Second Temple", "link": "https://wikipedia.org/wiki/Second_Temple"}]}, {"year": "642", "text": "Battle of Maserfield: <PERSON><PERSON> of Mercia defeats and kills <PERSON> of Northumbria.", "html": "642 - <a href=\"https://wikipedia.org/wiki/Battle_of_Maserfield\" title=\"Battle of Maserfield\">Battle of Maserfield</a>: <a href=\"https://wikipedia.org/wiki/Penda_of_Mercia\" title=\"Pen<PERSON> of Mercia\"><PERSON><PERSON> of Mercia</a> defeats and kills <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Northumbria\" title=\"<PERSON> of Northumbria\"><PERSON> of Northumbria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Maserfield\" title=\"Battle of Maserfield\">Battle of Maserfield</a>: <a href=\"https://wikipedia.org/wiki/Penda_of_Mercia\" title=\"Penda of Mercia\"><PERSON><PERSON> of Mercia</a> defeats and kills <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Northumbria\" title=\"<PERSON> of Northumbria\"><PERSON> of Northumbria</a>.", "links": [{"title": "Battle of Maserfield", "link": "https://wikipedia.org/wiki/Battle_of_Maserfield"}, {"title": "Penda of Mercia", "link": "https://wikipedia.org/wiki/Penda_of_Mercia"}, {"title": "<PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/<PERSON>_of_Northumbria"}]}, {"year": "910", "text": "The last major Danish army to raid England for nearly a century is defeated at the Battle of Tettenhall by the allied forces of Mercia and Wessex, led by King <PERSON> the Elder and <PERSON><PERSON><PERSON><PERSON>, Lord of the Mercians.", "html": "910 - The last major Danish army to raid <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> for nearly a century is defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tettenhall\" title=\"Battle of Tettenhall\">Battle of Tettenhall</a> by the allied forces of <a href=\"https://wikipedia.org/wiki/Mercia\" title=\"Mercia\">Mercia</a> and <a href=\"https://wikipedia.org/wiki/Wessex\" title=\"Wessex\">Wessex</a>, led by King <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Elder\" title=\"<PERSON> the Elder\">Edward the Elder</a> and <a href=\"https://wikipedia.org/wiki/%C3%86thel<PERSON>,_Lord_of_the_Mercians\" title=\"<PERSON><PERSON><PERSON><PERSON>, Lord of the Mercians\"><PERSON><PERSON><PERSON><PERSON>, Lord of the Mercians</a>.", "no_year_html": "The last major Danish army to raid <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> for nearly a century is defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tettenhall\" title=\"Battle of Tettenhall\">Battle of Tettenhall</a> by the allied forces of <a href=\"https://wikipedia.org/wiki/Mercia\" title=\"Mercia\">Mercia</a> and <a href=\"https://wikipedia.org/wiki/Wessex\" title=\"Wessex\">Wessex</a>, led by King <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a> and <a href=\"https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON>,_Lord_of_the_Mercians\" title=\"<PERSON><PERSON><PERSON><PERSON>, Lord of the Mercians\"><PERSON><PERSON><PERSON><PERSON>, Lord of the Mercians</a>.", "links": [{"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Battle of Tettenhall", "link": "https://wikipedia.org/wiki/Battle_of_Tettenhall"}, {"title": "Mercia", "link": "https://wikipedia.org/wiki/Mercia"}, {"title": "Wessex", "link": "https://wikipedia.org/wiki/Wessex"}, {"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>, Lord of the Mercians", "link": "https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON>,_Lord_of_the_Mercians"}]}, {"year": "939", "text": "The Battle of Alhandic is fought between <PERSON><PERSON> of León and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III at Zamora in the context of the Spanish Reconquista. The battle resulted in a victory for the Emirate of Córdoba.", "html": "939 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Alhandic\" title=\"Battle of Alhandic\">Battle of Alhandic</a> is fought between <a href=\"https://wikipedia.org/wiki/Ramiro_II_of_Le%C3%B3n\" title=\"<PERSON><PERSON> II of León\"><PERSON><PERSON> II of León</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_III\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III</a> at <a href=\"https://wikipedia.org/wiki/Zamora,_Spain\" title=\"Zamora, Spain\">Zamora</a> in the context of the <a href=\"https://wikipedia.org/wiki/Spanish_Reconquista\" class=\"mw-redirect\" title=\"Spanish Reconquista\">Spanish Reconquista</a>. The battle resulted in a victory for the <a href=\"https://wikipedia.org/wiki/Emirate_of_C%C3%B3rdoba\" class=\"mw-redirect\" title=\"Emirate of Córdoba\">Emirate of Córdoba</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Alhandic\" title=\"Battle of Alhandic\">Battle of Alhandic</a> is fought between <a href=\"https://wikipedia.org/wiki/Ram<PERSON>_II_of_Le%C3%B3n\" title=\"<PERSON><PERSON> II of León\"><PERSON><PERSON> II of León</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_III\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III</a> at <a href=\"https://wikipedia.org/wiki/Zamora,_Spain\" title=\"Zamora, Spain\">Zamora</a> in the context of the <a href=\"https://wikipedia.org/wiki/Spanish_Reconquista\" class=\"mw-redirect\" title=\"Spanish Reconquista\">Spanish Reconquista</a>. The battle resulted in a victory for the <a href=\"https://wikipedia.org/wiki/Emirate_of_C%C3%B3rdoba\" class=\"mw-redirect\" title=\"Emirate of Córdoba\">Emirate of Córdoba</a>.", "links": [{"title": "Battle of Alhandic", "link": "https://wikipedia.org/wiki/Battle_of_Alhandic"}, {"title": "Ramiro II of León", "link": "https://wikipedia.org/wiki/Ramiro_II_of_Le%C3%B3n"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Zamora, Spain", "link": "https://wikipedia.org/wiki/Zamora,_Spain"}, {"title": "Spanish Reconquista", "link": "https://wikipedia.org/wiki/Spanish_Reconquista"}, {"title": "Emirate of Córdoba", "link": "https://wikipedia.org/wiki/Emirate_of_C%C3%B3rdoba"}]}, {"year": "1068", "text": "Byzantine-Norman wars: Italo-Normans begin a nearly-three-year siege of Bari.", "html": "1068 - <a href=\"https://wikipedia.org/wiki/Byzantine%E2%80%93Norman_wars\" title=\"Byzantine-Norman wars\">Byzantine-Norman wars</a>: <a href=\"https://wikipedia.org/wiki/Italo-Norman\" class=\"mw-redirect\" title=\"Italo-Norman\">Italo-Normans</a> begin a <a href=\"https://wikipedia.org/wiki/Siege_of_Bari\" title=\"Siege of Bari\">nearly-three-year siege of Bari</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine%E2%80%93Norman_wars\" title=\"Byzantine-Norman wars\">Byzantine-Norman wars</a>: <a href=\"https://wikipedia.org/wiki/Italo-Norman\" class=\"mw-redirect\" title=\"Italo-Norman\">Italo-Normans</a> begin a <a href=\"https://wikipedia.org/wiki/Siege_of_Bari\" title=\"Siege of Bari\">nearly-three-year siege of Bari</a>.", "links": [{"title": "Byzantine-Norman wars", "link": "https://wikipedia.org/wiki/Byzantine%E2%80%93Norman_wars"}, {"title": "Italo-Norman", "link": "https://wikipedia.org/wiki/Italo-Norman"}, {"title": "Siege of Bari", "link": "https://wikipedia.org/wiki/Siege_of_Bari"}]}, {"year": "1100", "text": "<PERSON> is crowned King of England in Westminster Abbey.", "html": "1100 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> I</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_English_monarchs\" title=\"List of English monarchs\">King of England</a> in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> I</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_English_monarchs\" title=\"List of English monarchs\">King of England</a> in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "List of English monarchs", "link": "https://wikipedia.org/wiki/List_of_English_monarchs"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1278", "text": "Spanish Reconquista: the forces of the Kingdom of Castile initiate the ultimately futile Siege of Algeciras against the Emirate of Granada.", "html": "1278 - <a href=\"https://wikipedia.org/wiki/Spanish_Reconquista\" class=\"mw-redirect\" title=\"Spanish Reconquista\">Spanish Reconquista</a>: the forces of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a> initiate the ultimately futile <a href=\"https://wikipedia.org/wiki/Siege_of_Algeciras_(1278)\" class=\"mw-redirect\" title=\"Siege of Algeciras (1278)\">Siege of Algeciras</a> against the <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Emirate of Granada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Reconquista\" class=\"mw-redirect\" title=\"Spanish Reconquista\">Spanish Reconquista</a>: the forces of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a> initiate the ultimately futile <a href=\"https://wikipedia.org/wiki/Siege_of_Algeciras_(1278)\" class=\"mw-redirect\" title=\"Siege of Algeciras (1278)\">Siege of Algeciras</a> against the <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Emirate of Granada</a>.", "links": [{"title": "Spanish Reconquista", "link": "https://wikipedia.org/wiki/Spanish_Reconquista"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}, {"title": "Siege of Algeciras (1278)", "link": "https://wikipedia.org/wiki/Siege_of_Algeciras_(1278)"}, {"title": "Emirate of Granada", "link": "https://wikipedia.org/wiki/Emirate_of_Granada"}]}, {"year": "1305", "text": "First Scottish War of Independence: Sir <PERSON> of Menteith, the pro-English Sheriff of Dumbarton, successfully manages to capture Sir <PERSON> of Scotland, leading to <PERSON>'s subsequent execution by hanging, evisceration, drawing and quartering, and beheading 18 days later.", "html": "1305 - <a href=\"https://wikipedia.org/wiki/First_Scottish_War_of_Independence\" class=\"mw-redirect\" title=\"First Scottish War of Independence\">First Scottish War of Independence</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON> of Menteith</a>, the pro-English Sheriff of Dumbarton, successfully manages to capture <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON> of Scotland</a>, leading to <PERSON>'s subsequent execution by hanging, evisceration, drawing and quartering, and beheading 18 days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Scottish_War_of_Independence\" class=\"mw-redirect\" title=\"First Scottish War of Independence\">First Scottish War of Independence</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON> of Menteith</a>, the pro-English Sheriff of Dumbarton, successfully manages to capture <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON> of Scotland</a>, leading to <PERSON>'s subsequent execution by hanging, evisceration, drawing and quartering, and beheading 18 days later.", "links": [{"title": "First Scottish War of Independence", "link": "https://wikipedia.org/wiki/First_Scottish_War_of_Independence"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1388", "text": "The Battle of Otterburn, a border skirmish between the Scottish and the English in Northern England, is fought near Otterburn.", "html": "1388 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Otterburn\" title=\"Battle of Otterburn\">Battle of Otterburn</a>, a border skirmish between the Scottish and the English in Northern England, is fought near <a href=\"https://wikipedia.org/wiki/Otterburn,_Northumberland\" title=\"Otterburn, Northumberland\">Otterburn</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Otterburn\" title=\"Battle of Otterburn\">Battle of Otterburn</a>, a border skirmish between the Scottish and the English in Northern England, is fought near <a href=\"https://wikipedia.org/wiki/Otterburn,_Northumberland\" title=\"Otterburn, Northumberland\">Otterburn</a>.", "links": [{"title": "Battle of Otterburn", "link": "https://wikipedia.org/wiki/Battle_of_Otterburn"}, {"title": "Otterburn, Northumberland", "link": "https://wikipedia.org/wiki/Otterburn,_Northumberland"}]}, {"year": "1460", "text": "The Kingdom of Scotland captures Roxburgh, one of the last English strongholds in Scotland, following a siege.", "html": "1460 - The Kingdom of Scotland <a href=\"https://wikipedia.org/wiki/Capture_of_Roxburgh_(1460)\" title=\"Capture of Roxburgh (1460)\">captures Roxburgh</a>, one of the last English strongholds in Scotland, following a siege.", "no_year_html": "The Kingdom of Scotland <a href=\"https://wikipedia.org/wiki/Capture_of_Roxburgh_(1460)\" title=\"Capture of Roxburgh (1460)\">captures Roxburgh</a>, one of the last English strongholds in Scotland, following a siege.", "links": [{"title": "Capture of Roxburgh (1460)", "link": "https://wikipedia.org/wiki/Capture_of_Roxburgh_(1460)"}]}, {"year": "1506", "text": "The Grand Duchy of Lithuania defeats the Crimean Khanate in the Battle of Kletsk.", "html": "1506 - The <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> defeats the <a href=\"https://wikipedia.org/wiki/Crimean_Khanate\" title=\"Crimean Khanate\">Crimean Khanate</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Kletsk\" title=\"Battle of Kletsk\">Battle of Kletsk</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> defeats the <a href=\"https://wikipedia.org/wiki/Crimean_Khanate\" title=\"Crimean Khanate\">Crimean Khanate</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Kletsk\" title=\"Battle of Kletsk\">Battle of Kletsk</a>.", "links": [{"title": "Grand Duchy of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania"}, {"title": "Crimean Khanate", "link": "https://wikipedia.org/wiki/Crimean_Khanate"}, {"title": "Battle of Kletsk", "link": "https://wikipedia.org/wiki/Battle_of_Kletsk"}]}, {"year": "1583", "text": "Sir <PERSON> establishes the first English colony in North America, at what is now St. John's, Newfoundland and Labrador.", "html": "1583 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> establishes the first English <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> in North America, at what is now <a href=\"https://wikipedia.org/wiki/St._John%27s,_Newfoundland_and_Labrador\" title=\"St. John's, Newfoundland and Labrador\">St. John's, Newfoundland and Labrador</a>.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> establishes the first English <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> in North America, at what is now <a href=\"https://wikipedia.org/wiki/St._John%27s,_Newfoundland_and_Labrador\" title=\"St. John's, Newfoundland and Labrador\">St. John's, Newfoundland and Labrador</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Colony", "link": "https://wikipedia.org/wiki/Colony"}, {"title": "St. John's, Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/St._<PERSON>%27s,_Newfoundland_and_Labrador"}]}, {"year": "1600", "text": "The Gowrie Conspiracy against King <PERSON> of Scotland (later to become King <PERSON> of England) takes place at Gowrie House (Perth, Scotland).", "html": "1600 - The <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Gowrie#Gowrie_conspiracy\" title=\"<PERSON>, 3rd Earl of Gowrie\">Gowrie Conspiracy</a> against <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_I\" title=\"<PERSON> VI and I\">King <PERSON> of Scotland</a> (later to become King <PERSON> of England) takes place at <a href=\"https://wikipedia.org/wiki/Gowrie_House_(Perth,_Scotland)\" title=\"Gowrie House (Perth, Scotland)\">Gowrie House (Perth, Scotland)</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Gowrie#Gowrie_conspiracy\" title=\"<PERSON>, 3rd Earl of Gowrie\">Gowrie Conspiracy</a> against <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>\" title=\"<PERSON> VI and I\">King <PERSON> of Scotland</a> (later to become King <PERSON> of England) takes place at <a href=\"https://wikipedia.org/wiki/Gowrie_House_(Perth,_Scotland)\" title=\"Gowrie House (Perth, Scotland)\">Gowrie House (Perth, Scotland)</a>.", "links": [{"title": "<PERSON>, 3rd Earl of Gowrie", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Go<PERSON><PERSON>#Go<PERSON><PERSON>_conspiracy"}, {"title": "James <PERSON> and I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_I"}, {"title": "<PERSON><PERSON><PERSON> House (Perth, Scotland)", "link": "https://wikipedia.org/wiki/Gowrie_House_(Perth,_Scotland)"}]}, {"year": "1620", "text": "The Mayflower departs from Southampton, England, carrying would-be settlers, on its first attempt to reach North America; it is forced to dock in Dartmouth when its companion ship, the Speedwell, springs a leak.", "html": "1620 - The <i><a href=\"https://wikipedia.org/wiki/Mayflower\" title=\"Mayflower\">Mayflower</a></i> departs from <a href=\"https://wikipedia.org/wiki/Southampton\" title=\"Southampton\">Southampton</a>, England, carrying would-be settlers, on its first attempt to reach North America; it is forced to dock in Dartmouth when its companion ship, the <i>Speedwell</i>, springs a leak.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Mayflower\" title=\"Mayflower\">Mayflower</a></i> departs from <a href=\"https://wikipedia.org/wiki/Southampton\" title=\"Southampton\">Southampton</a>, England, carrying would-be settlers, on its first attempt to reach North America; it is forced to dock in Dartmouth when its companion ship, the <i>Speedwell</i>, springs a leak.", "links": [{"title": "May<PERSON>", "link": "https://wikipedia.org/wiki/Mayflower"}, {"title": "Southampton", "link": "https://wikipedia.org/wiki/Southampton"}]}, {"year": "1689", "text": "Beaver Wars: Fifteen hundred Iroquois attack Lachine in New France.", "html": "1689 - <a href=\"https://wikipedia.org/wiki/Beaver_Wars\" title=\"Beaver Wars\">Beaver Wars</a>: Fifteen hundred <a href=\"https://wikipedia.org/wiki/Iroquois\" title=\"Iroquois\">Iroquois</a> <a href=\"https://wikipedia.org/wiki/Lachine_massacre\" title=\"Lachine massacre\">attack</a> <a href=\"https://wikipedia.org/wiki/Lachine,_Quebec\" title=\"Lachine, Quebec\">Lachine</a> in <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beaver_Wars\" title=\"Beaver Wars\">Beaver Wars</a>: Fifteen hundred <a href=\"https://wikipedia.org/wiki/Iroquois\" title=\"Iroquois\">Iroquois</a> <a href=\"https://wikipedia.org/wiki/Lachine_massacre\" title=\"Lachine massacre\">attack</a> <a href=\"https://wikipedia.org/wiki/Lachine,_Quebec\" title=\"Lachine, Quebec\">Lachine</a> in <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a>.", "links": [{"title": "Beaver Wars", "link": "https://wikipedia.org/wiki/Beaver_Wars"}, {"title": "Iroquois", "link": "https://wikipedia.org/wiki/Iroquois"}, {"title": "Lachine massacre", "link": "https://wikipedia.org/wiki/Lac<PERSON>_massacre"}, {"title": "Lachine, Quebec", "link": "https://wikipedia.org/wiki/Lachine,_Quebec"}, {"title": "New France", "link": "https://wikipedia.org/wiki/New_France"}]}, {"year": "1716", "text": "Austro-Turkish War (1716-1718): One-fifth of a Turkish army and the Grand Vizier are killed in the Battle of Petrovaradin.", "html": "1716 - <a href=\"https://wikipedia.org/wiki/Austro-Turkish_War_(1716%E2%80%931718)\" title=\"Austro-Turkish War (1716-1718)\">Austro-Turkish War (1716-1718)</a>: One-fifth of a Turkish army and the <a href=\"https://wikipedia.org/wiki/Grand_Vizier\" class=\"mw-redirect\" title=\"Grand Vizier\">Grand Vizier</a> are killed in the <a href=\"https://wikipedia.org/wiki/Battle_of_Petrovaradin\" title=\"Battle of Petrovaradin\">Battle of Petrovaradin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austro-Turkish_War_(1716%E2%80%931718)\" title=\"Austro-Turkish War (1716-1718)\">Austro-Turkish War (1716-1718)</a>: One-fifth of a Turkish army and the <a href=\"https://wikipedia.org/wiki/Grand_Vizier\" class=\"mw-redirect\" title=\"Grand Vizier\">Grand Vizier</a> are killed in the <a href=\"https://wikipedia.org/wiki/Battle_of_Petrovaradin\" title=\"Battle of Petrovaradin\">Battle of Petrovaradin</a>.", "links": [{"title": "Austro-Turkish War (1716-1718)", "link": "https://wikipedia.org/wiki/Austro-Turkish_War_(1716%E2%80%931718)"}, {"title": "Grand Vizier", "link": "https://wikipedia.org/wiki/Grand_Vizier"}, {"title": "Battle of Petrovaradin", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>rovaradin"}]}, {"year": "1735", "text": "Freedom of the press: New York Weekly Journal writer <PERSON> is acquitted of seditious libel against the royal governor of New York, on the basis that what he had published was true.", "html": "1735 - <a href=\"https://wikipedia.org/wiki/Freedom_of_the_press\" title=\"Freedom of the press\">Freedom of the press</a>: <i>New York Weekly Journal</i> writer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is acquitted of <a href=\"https://wikipedia.org/wiki/Seditious_libel\" title=\"Seditious libel\">seditious libel</a> against the royal governor of New York, on the basis that what he had published was true.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Freedom_of_the_press\" title=\"Freedom of the press\">Freedom of the press</a>: <i>New York Weekly Journal</i> writer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is acquitted of <a href=\"https://wikipedia.org/wiki/Seditious_libel\" title=\"Seditious libel\">seditious libel</a> against the royal governor of New York, on the basis that what he had published was true.", "links": [{"title": "Freedom of the press", "link": "https://wikipedia.org/wiki/Freedom_of_the_press"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Seditious libel", "link": "https://wikipedia.org/wiki/Seditious_libel"}]}, {"year": "1763", "text": "Pontiac's War: Battle of Bushy Run: British forces led by <PERSON> defeat Chief <PERSON>'s Indians at Bushy Run.", "html": "1763 - <a href=\"https://wikipedia.org/wiki/Pontiac%27s_War\" title=\"Pontiac's War\">Pontiac's War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Bushy_Run\" title=\"Battle of Bushy Run\">Battle of Bushy Run</a>: British forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat <a href=\"https://wikipedia.org/wiki/Chief_<PERSON>\" class=\"mw-redirect\" title=\"Chief Pontiac\">Chief <PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Indians</a> at <a href=\"https://wikipedia.org/wiki/Battle_of_Bushy_Run\" title=\"Battle of Bushy Run\">Bushy Run</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pontiac%27s_War\" title=\"Pontiac's War\">Pontiac's War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Bushy_Run\" title=\"Battle of Bushy Run\">Battle of Bushy Run</a>: British forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat <a href=\"https://wikipedia.org/wiki/Chief_<PERSON>\" class=\"mw-redirect\" title=\"Chief <PERSON>\">Chief <PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Indians</a> at <a href=\"https://wikipedia.org/wiki/Battle_of_Bushy_Run\" title=\"Battle of Bushy Run\">Bushy Run</a>.", "links": [{"title": "Pontiac's War", "link": "https://wikipedia.org/wiki/Pontiac%27s_War"}, {"title": "Battle of Bushy Run", "link": "https://wikipedia.org/wiki/Battle_of_Bushy_Run"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief <PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON>"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}, {"title": "Battle of Bushy Run", "link": "https://wikipedia.org/wiki/Battle_of_Bushy_Run"}]}, {"year": "1772", "text": "First Partition of Poland: The representatives of Austria, Prussia, and Russia sign three bilateral conventions condemning the \"anarchy\" of the Polish-Lithuanian Commonwealth and imputing to the three powers \"ancient and legitimate rights\" to the territories of the Commonwealth. The conventions allow each of the three great powers to annex a part of the Commonwealth, which they proceed to do over the course of the following two months.", "html": "1772 - <a href=\"https://wikipedia.org/wiki/First_Partition_of_Poland\" title=\"First Partition of Poland\">First Partition of Poland</a>: The representatives of <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Austria</a>, <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>, and <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a> sign three bilateral conventions condemning the \"anarchy\" of the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a> and imputing to the three powers \"ancient and legitimate rights\" to the territories of the Commonwealth. The conventions allow each of the three great powers to annex a part of the Commonwealth, which they proceed to do over the course of the following two months.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Partition_of_Poland\" title=\"First Partition of Poland\">First Partition of Poland</a>: The representatives of <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Austria</a>, <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>, and <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a> sign three bilateral conventions condemning the \"anarchy\" of the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a> and imputing to the three powers \"ancient and legitimate rights\" to the territories of the Commonwealth. The conventions allow each of the three great powers to annex a part of the Commonwealth, which they proceed to do over the course of the following two months.", "links": [{"title": "First Partition of Poland", "link": "https://wikipedia.org/wiki/First_Partition_of_Poland"}, {"title": "Habsburg monarchy", "link": "https://wikipedia.org/wiki/Habsburg_monarchy"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Polish-Lithuanian Commonwealth", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth"}]}, {"year": "1781", "text": "The Battle of Dogger Bank takes place.", "html": "1781 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Dogger_Bank_(1781)\" title=\"Battle of Dogger Bank (1781)\">Battle of Dogger Bank</a> takes place.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Dogger_Bank_(1781)\" title=\"Battle of Dogger Bank (1781)\">Battle of Dogger Bank</a> takes place.", "links": [{"title": "Battle of Dogger Bank (1781)", "link": "https://wikipedia.org/wiki/Battle_of_Dogger_Bank_(1781)"}]}, {"year": "1796", "text": "The Battle of Castiglione in Napoleon's first Italian campaigns of the French Revolutionary Wars results in a French victory.", "html": "1796 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Castiglione\" title=\"Battle of Castiglione\">Battle of Castiglione</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Napoleon\"><PERSON></a>'s first <a href=\"https://wikipedia.org/wiki/Italian_campaigns_of_the_French_Revolutionary_Wars\" title=\"Italian campaigns of the French Revolutionary Wars\">Italian campaigns of the French Revolutionary Wars</a> results in a French victory.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Castiglione\" title=\"Battle of Castiglione\">Battle of Castiglione</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s first <a href=\"https://wikipedia.org/wiki/Italian_campaigns_of_the_French_Revolutionary_Wars\" title=\"Italian campaigns of the French Revolutionary Wars\">Italian campaigns of the French Revolutionary Wars</a> results in a French victory.", "links": [{"title": "Battle of Castiglione", "link": "https://wikipedia.org/wiki/Battle_of_Castiglione"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Italian campaigns of the French Revolutionary Wars", "link": "https://wikipedia.org/wiki/Italian_campaigns_of_the_French_Revolutionary_Wars"}]}, {"year": "1816", "text": "The British Admiralty dismisses <PERSON>'s new invention of the first working electric telegraph as \"wholly unnecessary\", preferring to continue using the semaphore.", "html": "1816 - The British Admiralty dismisses <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s new invention of the first working <a href=\"https://wikipedia.org/wiki/Electrical_telegraph\" title=\"Electrical telegraph\">electric telegraph</a> as \"wholly unnecessary\", preferring to continue using the semaphore.", "no_year_html": "The British Admiralty dismisses <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s new invention of the first working <a href=\"https://wikipedia.org/wiki/Electrical_telegraph\" title=\"Electrical telegraph\">electric telegraph</a> as \"wholly unnecessary\", preferring to continue using the semaphore.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Electrical telegraph", "link": "https://wikipedia.org/wiki/Electrical_telegraph"}]}, {"year": "1824", "text": "Greek War of Independence: <PERSON><PERSON> leads a Greek fleet to victory against Ottoman and Egyptian naval forces in the Battle of Samos.", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> leads a Greek fleet to victory against Ottoman and Egyptian naval forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Samos_(1824)\" class=\"mw-redirect\" title=\"Battle of Samos (1824)\">Battle of Samos</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> leads a Greek fleet to victory against Ottoman and Egyptian naval forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Samos_(1824)\" class=\"mw-redirect\" title=\"Battle of Samos (1824)\">Battle of Samos</a>.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Samos (1824)", "link": "https://wikipedia.org/wiki/Battle_of_Samos_(1824)"}]}, {"year": "1858", "text": "<PERSON> and others complete the first transatlantic telegraph cable after several unsuccessful attempts. It will operate for less than a month.", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Cyrus_West_Field\" class=\"mw-redirect\" title=\"Cyrus West Field\"><PERSON> Field</a> and others complete the first <a href=\"https://wikipedia.org/wiki/Transatlantic_telegraph_cable\" title=\"Transatlantic telegraph cable\">transatlantic telegraph cable</a> after several unsuccessful attempts. It will operate for less than a month.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cyrus_West_Field\" class=\"mw-redirect\" title=\"Cyrus West Field\"><PERSON> Field</a> and others complete the first <a href=\"https://wikipedia.org/wiki/Transatlantic_telegraph_cable\" title=\"Transatlantic telegraph cable\">transatlantic telegraph cable</a> after several unsuccessful attempts. It will operate for less than a month.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cyrus_West_Field"}, {"title": "Transatlantic telegraph cable", "link": "https://wikipedia.org/wiki/Transatlantic_telegraph_cable"}]}, {"year": "1860", "text": "<PERSON> of Sweden-Norway is crowned king of Norway in Trondheim.", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles XV\"><PERSON></a> of Sweden-Norway is crowned king of Norway in <a href=\"https://wikipedia.org/wiki/Trondheim\" title=\"Trondheim\">Trondheim</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles XV\"><PERSON></a> of Sweden-Norway is crowned king of Norway in <a href=\"https://wikipedia.org/wiki/Trondheim\" title=\"Trondheim\">Trondheim</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Trondheim", "link": "https://wikipedia.org/wiki/Trondheim"}]}, {"year": "1861", "text": "American Civil War: In order to help pay for the war effort, the United States government levies the first income tax as part of the Revenue Act of 1861 (3% of all incomes over US$800; rescinded in 1872).", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: In order to help pay for the war effort, the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">United States government</a> levies the first <a href=\"https://wikipedia.org/wiki/Income_tax\" title=\"Income tax\">income tax</a> as part of the <a href=\"https://wikipedia.org/wiki/Revenue_Act_of_1861\" title=\"Revenue Act of 1861\">Revenue Act of 1861</a> (3% of all incomes over US$800; rescinded in <a href=\"https://wikipedia.org/wiki/1872\" title=\"1872\">1872</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: In order to help pay for the war effort, the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">United States government</a> levies the first <a href=\"https://wikipedia.org/wiki/Income_tax\" title=\"Income tax\">income tax</a> as part of the <a href=\"https://wikipedia.org/wiki/Revenue_Act_of_1861\" title=\"Revenue Act of 1861\">Revenue Act of 1861</a> (3% of all incomes over US$800; rescinded in <a href=\"https://wikipedia.org/wiki/1872\" title=\"1872\">1872</a>).", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Federal government of the United States", "link": "https://wikipedia.org/wiki/Federal_government_of_the_United_States"}, {"title": "Income tax", "link": "https://wikipedia.org/wiki/Income_tax"}, {"title": "Revenue Act of 1861", "link": "https://wikipedia.org/wiki/Revenue_Act_of_1861"}, {"title": "1872", "link": "https://wikipedia.org/wiki/1872"}]}, {"year": "1861", "text": "The United States Army abolishes flogging.", "html": "1861 - The <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> abolishes <a href=\"https://wikipedia.org/wiki/Flagellation\" title=\"Flagellation\">flogging</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> abolishes <a href=\"https://wikipedia.org/wiki/Flagellation\" title=\"Flagellation\">flogging</a>.", "links": [{"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Flagellation", "link": "https://wikipedia.org/wiki/Flagellation"}]}, {"year": "1862", "text": "American Civil War: Battle of Baton Rouge: Along the Mississippi River near Baton Rouge, Louisiana, Confederate troops attempt to take the city, but are driven back by fire from Union gunboats.", "html": "1862 - American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Baton_Rouge_(1862)\" title=\"Battle of Baton Rouge (1862)\">Battle of Baton Rouge</a>: Along the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> near <a href=\"https://wikipedia.org/wiki/Baton_Rouge,_Louisiana\" title=\"Baton Rouge, Louisiana\">Baton Rouge, Louisiana</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops attempt to take the city, but are driven back by fire from Union gunboats.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Baton_Rouge_(1862)\" title=\"Battle of Baton Rouge (1862)\">Battle of Baton Rouge</a>: Along the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> near <a href=\"https://wikipedia.org/wiki/Baton_Rouge,_Louisiana\" title=\"Baton Rouge, Louisiana\">Baton Rouge, Louisiana</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops attempt to take the city, but are driven back by fire from Union gunboats.", "links": [{"title": "Battle of Baton Rouge (1862)", "link": "https://wikipedia.org/wiki/Battle_of_Baton_Rouge_(1862)"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}, {"title": "Baton Rouge, Louisiana", "link": "https://wikipedia.org/wiki/Baton_Rouge,_Louisiana"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1864", "text": "American Civil War: The Battle of Mobile Bay begins at Mobile Bay near Mobile, Alabama, Admiral <PERSON> leads a Union flotilla through Confederate defenses and seals one of the last major Southern ports.", "html": "1864 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mobile_Bay\" title=\"Battle of Mobile Bay\">Battle of Mobile Bay</a> begins at <a href=\"https://wikipedia.org/wiki/Mobile_Bay\" title=\"Mobile Bay\">Mobile Bay</a> near <a href=\"https://wikipedia.org/wiki/Mobile,_Alabama\" title=\"Mobile, Alabama\">Mobile, Alabama</a>, Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads a Union flotilla through Confederate defenses and seals one of the last major Southern ports.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mobile_Bay\" title=\"Battle of Mobile Bay\">Battle of Mobile Bay</a> begins at <a href=\"https://wikipedia.org/wiki/Mobile_Bay\" title=\"Mobile Bay\">Mobile Bay</a> near <a href=\"https://wikipedia.org/wiki/Mobile,_Alabama\" title=\"Mobile, Alabama\">Mobile, Alabama</a>, Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads a Union flotilla through Confederate defenses and seals one of the last major Southern ports.", "links": [{"title": "Battle of Mobile Bay", "link": "https://wikipedia.org/wiki/Battle_of_Mobile_Bay"}, {"title": "Mobile Bay", "link": "https://wikipedia.org/wiki/Mobile_Bay"}, {"title": "Mobile, Alabama", "link": "https://wikipedia.org/wiki/Mobile,_Alabama"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1874", "text": "Japan launches its postal savings system, modeled after a similar system in the United Kingdom.", "html": "1874 - Japan launches its <a href=\"https://wikipedia.org/wiki/Postal_savings_system\" title=\"Postal savings system\">postal savings system</a>, modeled after a similar system in the United Kingdom.", "no_year_html": "Japan launches its <a href=\"https://wikipedia.org/wiki/Postal_savings_system\" title=\"Postal savings system\">postal savings system</a>, modeled after a similar system in the United Kingdom.", "links": [{"title": "Postal savings system", "link": "https://wikipedia.org/wiki/Postal_savings_system"}]}, {"year": "1882", "text": "Standard Oil Company of New Jersey, today known as ExxonMobil, is established officially. The company would later grow to become the holder of all Standard Oil companies and the entity at the center of the breakup of Standard Oil.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Standard_Oil\" title=\"Standard Oil\">Standard Oil</a> Company of New Jersey, today known as <a href=\"https://wikipedia.org/wiki/ExxonMobil\" title=\"ExxonMobil\">ExxonMobil</a>, is established officially. The company would later grow to become the holder of all Standard Oil companies and the entity at the center of the <a href=\"https://wikipedia.org/wiki/Breakup_of_Standard_Oil\" class=\"mw-redirect\" title=\"Breakup of Standard Oil\">breakup of Standard Oil</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Standard_Oil\" title=\"Standard Oil\">Standard Oil</a> Company of New Jersey, today known as <a href=\"https://wikipedia.org/wiki/ExxonMobil\" title=\"ExxonMobil\">ExxonMobil</a>, is established officially. The company would later grow to become the holder of all Standard Oil companies and the entity at the center of the <a href=\"https://wikipedia.org/wiki/Breakup_of_Standard_Oil\" class=\"mw-redirect\" title=\"Breakup of Standard Oil\">breakup of Standard Oil</a>.", "links": [{"title": "Standard Oil", "link": "https://wikipedia.org/wiki/Standard_Oil"}, {"title": "ExxonMobil", "link": "https://wikipedia.org/wiki/ExxonMobil"}, {"title": "Breakup of Standard Oil", "link": "https://wikipedia.org/wiki/Breakup_of_Standard_Oil"}]}, {"year": "1884", "text": "The cornerstone for the Statue of Liberty is laid on Bedloe's Island (now Liberty Island) in New York Harbor.", "html": "1884 - The cornerstone for the <a href=\"https://wikipedia.org/wiki/Statue_of_Liberty\" title=\"Statue of Liberty\">Statue of Liberty</a> is laid on <a href=\"https://wikipedia.org/wiki/Liberty_Island\" title=\"Liberty Island\">Bedloe's Island</a> (now Liberty Island) in <a href=\"https://wikipedia.org/wiki/New_York_Harbor\" title=\"New York Harbor\">New York Harbor</a>.", "no_year_html": "The cornerstone for the <a href=\"https://wikipedia.org/wiki/Statue_of_Liberty\" title=\"Statue of Liberty\">Statue of Liberty</a> is laid on <a href=\"https://wikipedia.org/wiki/Liberty_Island\" title=\"Liberty Island\">Bedloe's Island</a> (now Liberty Island) in <a href=\"https://wikipedia.org/wiki/New_York_Harbor\" title=\"New York Harbor\">New York Harbor</a>.", "links": [{"title": "Statue of Liberty", "link": "https://wikipedia.org/wiki/Statue_of_Liberty"}, {"title": "Liberty Island", "link": "https://wikipedia.org/wiki/Liberty_Island"}, {"title": "New York Harbor", "link": "https://wikipedia.org/wiki/New_York_Harbor"}]}, {"year": "1888", "text": "<PERSON><PERSON> drives from Mannheim to Pforzheim and back in the first long distance automobile trip, commemorated as the <PERSON>ha Benz Memorial Route since 2008.", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ha Benz\"><PERSON><PERSON></a> drives from <a href=\"https://wikipedia.org/wiki/Mannheim\" title=\"Mannheim\">Mannheim</a> to <a href=\"https://wikipedia.org/wiki/Pforzheim\" title=\"Pforzheim\">Pforzheim</a> and back in the first long distance automobile trip, commemorated as the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Benz_Memorial_Route\" title=\"Bertha Benz Memorial Route\"><PERSON><PERSON> Memorial Route</a> since 2008.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Benz\"><PERSON><PERSON></a> drives from <a href=\"https://wikipedia.org/wiki/Mannheim\" title=\"Mannheim\">Mannheim</a> to <a href=\"https://wikipedia.org/wiki/Pforzheim\" title=\"Pforzheim\">Pforzheim</a> and back in the first long distance automobile trip, commemorated as the <a href=\"https://wikipedia.org/wiki/Bert<PERSON>_Benz_Memorial_Route\" title=\"Bertha Benz Memorial Route\"><PERSON><PERSON> Memorial Route</a> since 2008.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Mannheim", "link": "https://wikipedia.org/wiki/Mannheim"}, {"title": "Pforzheim", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>rzheim"}, {"title": "<PERSON><PERSON> Memorial Route", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Memorial_Route"}]}, {"year": "1901", "text": "<PERSON> sets the first World Athletics recognised long jump world record of 24 ft 11.75 in (7.6137 m), a record that would stand for 20 years.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a> sets the first <a href=\"https://wikipedia.org/wiki/World_Athletics\" title=\"World Athletics\">World Athletics</a> recognised <a href=\"https://wikipedia.org/wiki/Long_jump\" title=\"Long jump\">long jump</a> <a href=\"https://wikipedia.org/wiki/List_of_world_records_in_athletics\" title=\"List of world records in athletics\">world record</a> of 24 ft 11.75 in (7.6137 m), a record that would stand for 20 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a> sets the first <a href=\"https://wikipedia.org/wiki/World_Athletics\" title=\"World Athletics\">World Athletics</a> recognised <a href=\"https://wikipedia.org/wiki/Long_jump\" title=\"Long jump\">long jump</a> <a href=\"https://wikipedia.org/wiki/List_of_world_records_in_athletics\" title=\"List of world records in athletics\">world record</a> of 24 ft 11.75 in (7.6137 m), a record that would stand for 20 years.", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)"}, {"title": "World Athletics", "link": "https://wikipedia.org/wiki/World_Athletics"}, {"title": "Long jump", "link": "https://wikipedia.org/wiki/Long_jump"}, {"title": "List of world records in athletics", "link": "https://wikipedia.org/wiki/List_of_world_records_in_athletics"}]}, {"year": "1906", "text": "Persian Constitutional Revolution: <PERSON><PERSON><PERSON><PERSON>, King of Iran, agrees to convert the government to a constitutional monarchy.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Persian_Constitutional_Revolution\" title=\"Persian Constitutional Revolution\">Persian Constitutional Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, King of Iran, agrees to convert the government to a <a href=\"https://wikipedia.org/wiki/Constitutional_monarchy\" title=\"Constitutional monarchy\">constitutional monarchy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Persian_Constitutional_Revolution\" title=\"Persian Constitutional Revolution\">Persian Constitutional Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, King of Iran, agrees to convert the government to a <a href=\"https://wikipedia.org/wiki/Constitutional_monarchy\" title=\"Constitutional monarchy\">constitutional monarchy</a>.", "links": [{"title": "Persian Constitutional Revolution", "link": "https://wikipedia.org/wiki/Persian_Constitutional_Revolution"}, {"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>_<PERSON>"}, {"title": "Constitutional monarchy", "link": "https://wikipedia.org/wiki/Constitutional_monarchy"}]}, {"year": "1914", "text": "World War I: The German minelayer SS Königin Luise lays a minefield about 40 miles (64 km) off the Thames Estuary (Lowestoft). She is intercepted and sunk by the British light-cruiser HMS Amphion.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The German <a href=\"https://wikipedia.org/wiki/Minelayer\" title=\"Minelayer\">minelayer</a> <a href=\"https://wikipedia.org/wiki/SS_K%C3%B6nigin_Luise_(1913)\" title=\"SS Königin Luise (1913)\">SS <i>Königin Luise</i></a> lays a minefield about 40 miles (64 km) off the <a href=\"https://wikipedia.org/wiki/Thames_Estuary\" title=\"Thames Estuary\">Thames Estuary</a> (<a href=\"https://wikipedia.org/wiki/Lowestoft\" title=\"Lowestoft\">Lowestoft</a>). She is intercepted and sunk by the British light-cruiser <a href=\"https://wikipedia.org/wiki/HMS_Amphion_(1911)\" title=\"HMS Amphion (1911)\">HMS <i>Amphion</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The German <a href=\"https://wikipedia.org/wiki/Minelayer\" title=\"Minelayer\">minelayer</a> <a href=\"https://wikipedia.org/wiki/SS_K%C3%B6<PERSON>gin_Luis<PERSON>_(1913)\" title=\"SS Königin Luise (1913)\">SS <i>Königin Luise</i></a> lays a minefield about 40 miles (64 km) off the <a href=\"https://wikipedia.org/wiki/Thames_Estuary\" title=\"Thames Estuary\">Thames Estuary</a> (<a href=\"https://wikipedia.org/wiki/Lowestoft\" title=\"Lowestoft\">Lowestoft</a>). She is intercepted and sunk by the British light-cruiser <a href=\"https://wikipedia.org/wiki/HMS_Amphion_(1911)\" title=\"HMS Amphion (1911)\">HMS <i>Amphion</i></a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Minelayer", "link": "https://wikipedia.org/wiki/Minelayer"}, {"title": "SS Königin <PERSON> (1913)", "link": "https://wikipedia.org/wiki/SS_K%C3%B6ni<PERSON>_<PERSON><PERSON>_(1913)"}, {"title": "Thames Estuary", "link": "https://wikipedia.org/wiki/Thames_Estuary"}, {"title": "Lowestoft", "link": "https://wikipedia.org/wiki/Lowestoft"}, {"title": "HMS Amphion (1911)", "link": "https://wikipedia.org/wiki/HMS_Amphion_(1911)"}]}, {"year": "1914", "text": "World War I: The guns of Point Nepean fort at Port Phillip Heads in Victoria (Australia) fire across the bows of the Norddeutscher Lloyd steamer SS Pfalz which is attempting to leave the Port of Melbourne in ignorance of the declaration of war and she is detained; this is said to be the first Allied shot of the War.", "html": "1914 - World War I: The guns of Point Nepean fort at Port Phillip Heads in Victoria (Australia) fire across the bows of the Norddeutscher Lloyd steamer <a href=\"https://wikipedia.org/wiki/SS_Pfalz_(1913)\" title=\"SS Pfalz (1913)\">SS <i>Pfalz</i></a> which is attempting to leave the Port of Melbourne in ignorance of the declaration of war and she is detained; this is said to be the first Allied shot of the War.", "no_year_html": "World War I: The guns of Point Nepean fort at Port Phillip Heads in Victoria (Australia) fire across the bows of the Norddeutscher Lloyd steamer <a href=\"https://wikipedia.org/wiki/SS_Pfalz_(1913)\" title=\"SS Pfalz (1913)\">SS <i>Pfalz</i></a> which is attempting to leave the Port of Melbourne in ignorance of the declaration of war and she is detained; this is said to be the first Allied shot of the War.", "links": [{"title": "SS Pfalz (1913)", "link": "https://wikipedia.org/wiki/SS_Pfalz_(1913)"}]}, {"year": "1914", "text": "In Cleveland, Ohio, the first electric traffic light is installed.", "html": "1914 - In <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>, the first electric <a href=\"https://wikipedia.org/wiki/Traffic_light\" title=\"Traffic light\">traffic light</a> is installed.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>, the first electric <a href=\"https://wikipedia.org/wiki/Traffic_light\" title=\"Traffic light\">traffic light</a> is installed.", "links": [{"title": "Cleveland, Ohio", "link": "https://wikipedia.org/wiki/Cleveland,_Ohio"}, {"title": "Traffic light", "link": "https://wikipedia.org/wiki/Traffic_light"}]}, {"year": "1916", "text": "World War I: Battle of Romani: Allied forces, under the command of <PERSON>, defeat an attacking Ottoman army under the command of <PERSON>, securing the Suez Canal and beginning the Ottoman retreat from the Sinai Peninsula.", "html": "1916 - World War I: <a href=\"https://wikipedia.org/wiki/Battle_of_Romani\" title=\"Battle of Romani\">Battle of Romani</a>: Allied forces, under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, defeat an attacking <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman army</a> under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, securing the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> and beginning the Ottoman retreat from the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a>.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/Battle_of_Romani\" title=\"Battle of Romani\">Battle of Romani</a>: Allied forces, under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, defeat an attacking <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman army</a> under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, securing the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> and beginning the Ottoman retreat from the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a>.", "links": [{"title": "Battle of Romani", "link": "https://wikipedia.org/wiki/Battle_of_Romani"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}, {"title": "Sinai Peninsula", "link": "https://wikipedia.org/wiki/Sinai_Peninsula"}]}, {"year": "1925", "text": "Plaid Cymru is formed with the aim of disseminating knowledge of the Welsh language that is at the time in danger of dying out.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Plaid_Cymru\" title=\"Plaid Cymru\">Plaid Cymru</a> is formed with the aim of disseminating knowledge of the <a href=\"https://wikipedia.org/wiki/Welsh_language\" title=\"Welsh language\">Welsh language</a> that is at the time in danger of dying out.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plaid_Cymru\" title=\"Plaid Cymru\">Plaid Cymru</a> is formed with the aim of disseminating knowledge of the <a href=\"https://wikipedia.org/wiki/Welsh_language\" title=\"Welsh language\">Welsh language</a> that is at the time in danger of dying out.", "links": [{"title": "Plaid Cymru", "link": "https://wikipedia.org/wiki/Plaid_Cymru"}, {"title": "Welsh language", "link": "https://wikipedia.org/wiki/Welsh_language"}]}, {"year": "1926", "text": "<PERSON> performs his greatest feat, spending 91 minutes underwater in a sealed tank before escaping.", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs his greatest feat, spending 91 minutes underwater in a sealed tank before escaping.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs his greatest feat, spending 91 minutes underwater in a sealed tank before escaping.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "The Thirteen Roses: Thirteen female members of the Unified Socialist Youth are executed by Francoist forces in Madrid, Spain.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/The_Thirteen_Roses\" class=\"mw-redirect\" title=\"The Thirteen Roses\">The Thirteen Roses</a>: Thirteen female members of the <a href=\"https://wikipedia.org/wiki/Unified_Socialist_Youth\" title=\"Unified Socialist Youth\">Unified Socialist Youth</a> are executed by <a href=\"https://wikipedia.org/wiki/Francoist\" class=\"mw-redirect\" title=\"Francoist\">Francoist</a> forces in <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>, Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Thirteen_Roses\" class=\"mw-redirect\" title=\"The Thirteen Roses\">The Thirteen Roses</a>: Thirteen female members of the <a href=\"https://wikipedia.org/wiki/Unified_Socialist_Youth\" title=\"Unified Socialist Youth\">Unified Socialist Youth</a> are executed by <a href=\"https://wikipedia.org/wiki/Francoist\" class=\"mw-redirect\" title=\"Francoist\">Francoist</a> forces in <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>, Spain.", "links": [{"title": "The Thirteen Roses", "link": "https://wikipedia.org/wiki/The_Thirteen_Roses"}, {"title": "Unified Socialist Youth", "link": "https://wikipedia.org/wiki/Unified_Socialist_Youth"}, {"title": "Francoist", "link": "https://wikipedia.org/wiki/Francoist"}, {"title": "Madrid", "link": "https://wikipedia.org/wiki/Madrid"}]}, {"year": "1940", "text": "World War II: The Soviet Union formally annexes Latvia.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> formally <a href=\"https://wikipedia.org/wiki/Soviet_occupation_of_Latvia_in_1940#1940-1941:_The_first_Soviet_occupation\" title=\"Soviet occupation of Latvia in 1940\">annexes</a> <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> formally <a href=\"https://wikipedia.org/wiki/Soviet_occupation_of_Latvia_in_1940#1940-1941:_The_first_Soviet_occupation\" title=\"Soviet occupation of Latvia in 1940\">annexes</a> <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Soviet occupation of Latvia in 1940", "link": "https://wikipedia.org/wiki/Soviet_occupation_of_Latvia_in_1940#1940-1941:_The_first_Soviet_occupation"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}]}, {"year": "1944", "text": "World War II: At least 1,104 Japanese POWs in Australia attempt to escape from a camp at Cowra, New South Wales; 545 temporarily succeed but are later either killed, commit suicide, or are recaptured.", "html": "1944 - World War II: At least 1,104 <a href=\"https://wikipedia.org/wiki/Japanese_people\" title=\"Japanese people\">Japanese</a> <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">POWs in Australia</a> <a href=\"https://wikipedia.org/wiki/Cowra_breakout\" title=\"Cowra breakout\">attempt to escape from a camp at Cowra, New South Wales</a>; 545 temporarily succeed but are later either killed, commit suicide, or are recaptured.", "no_year_html": "World War II: At least 1,104 <a href=\"https://wikipedia.org/wiki/Japanese_people\" title=\"Japanese people\">Japanese</a> <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">POWs in Australia</a> <a href=\"https://wikipedia.org/wiki/Cowra_breakout\" title=\"Cowra breakout\">attempt to escape from a camp at Cowra, New South Wales</a>; 545 temporarily succeed but are later either killed, commit suicide, or are recaptured.", "links": [{"title": "Japanese people", "link": "https://wikipedia.org/wiki/Japanese_people"}, {"title": "Prisoner of war", "link": "https://wikipedia.org/wiki/Prisoner_of_war"}, {"title": "Cowra breakout", "link": "https://wikipedia.org/wiki/<PERSON>wra_breakout"}]}, {"year": "1944", "text": "World War II: Polish insurgents liberate a German labor camp (Gęsiówka) in Warsaw, freeing 348 Jewish prisoners.", "html": "1944 - World War II: Polish insurgents liberate a German <a href=\"https://wikipedia.org/wiki/Labor_camp\" title=\"Labor camp\">labor camp</a> (<a href=\"https://wikipedia.org/wiki/G%C4%99si%C3%B3wka\" title=\"Gęsiówka\">Gęsiówka</a>) in <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>, freeing 348 <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jewish</a> prisoners.", "no_year_html": "World War II: Polish insurgents liberate a German <a href=\"https://wikipedia.org/wiki/Labor_camp\" title=\"Labor camp\">labor camp</a> (<a href=\"https://wikipedia.org/wiki/G%C4%99si%C3%B3wka\" title=\"Gęsiówka\">Gęsiówka</a>) in <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>, freeing 348 <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jewish</a> prisoners.", "links": [{"title": "Labor camp", "link": "https://wikipedia.org/wiki/Labor_camp"}, {"title": "Gęsiówka", "link": "https://wikipedia.org/wiki/G%C4%99si%C3%B3wka"}, {"title": "Warsaw", "link": "https://wikipedia.org/wiki/Warsaw"}, {"title": "Jews", "link": "https://wikipedia.org/wiki/Jews"}]}, {"year": "1944", "text": "World War II: The Nazis begin a week-long massacre of between 40,000 and 50,000 civilians and prisoners of war in Wola, Poland.", "html": "1944 - World War II: The Nazis begin a <a href=\"https://wikipedia.org/wiki/Wola_massacre\" title=\"Wola massacre\">week-long massacre</a> of between 40,000 and 50,000 civilians and prisoners of war in <a href=\"https://wikipedia.org/wiki/Wola\" title=\"Wola\">Wola</a>, Poland.", "no_year_html": "World War II: The Nazis begin a <a href=\"https://wikipedia.org/wiki/Wola_massacre\" title=\"Wola massacre\">week-long massacre</a> of between 40,000 and 50,000 civilians and prisoners of war in <a href=\"https://wikipedia.org/wiki/Wola\" title=\"Wola\">Wola</a>, Poland.", "links": [{"title": "Wola massacre", "link": "https://wikipedia.org/wiki/Wola_massacre"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wola"}]}, {"year": "1949", "text": "In Ecuador, an earthquake destroys 50 towns and kills more than 6,000.", "html": "1949 - In <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>, <a href=\"https://wikipedia.org/wiki/1949_Ambato_earthquake\" title=\"1949 Ambato earthquake\">an earthquake</a> destroys 50 towns and kills more than 6,000.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>, <a href=\"https://wikipedia.org/wiki/1949_Ambato_earthquake\" title=\"1949 Ambato earthquake\">an earthquake</a> destroys 50 towns and kills more than 6,000.", "links": [{"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}, {"title": "1949 Ambato earthquake", "link": "https://wikipedia.org/wiki/1949_Ambato_earthquake"}]}, {"year": "1949", "text": "In Montana, 12 smokejumper firefighters and 1 US Forest Service fire guard are killed in the Mann Gulch Fire.", "html": "1949 - In <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a>, 12 <a href=\"https://wikipedia.org/wiki/Smokejumper\" title=\"Smokejumper\">smokejumper</a> firefighters and 1 <a href=\"https://wikipedia.org/wiki/United_States_Forest_Service\" title=\"United States Forest Service\">US Forest Service</a> fire guard are killed in the <a href=\"https://wikipedia.org/wiki/Mann_Gulch_fire\" title=\"Mann Gulch fire\">Mann Gulch Fire</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a>, 12 <a href=\"https://wikipedia.org/wiki/Smokejumper\" title=\"Smokejumper\">smokejumper</a> firefighters and 1 <a href=\"https://wikipedia.org/wiki/United_States_Forest_Service\" title=\"United States Forest Service\">US Forest Service</a> fire guard are killed in the <a href=\"https://wikipedia.org/wiki/Mann_Gulch_fire\" title=\"Mann Gulch fire\">Mann Gulch Fire</a>.", "links": [{"title": "Montana", "link": "https://wikipedia.org/wiki/Montana"}, {"title": "Smokejumper", "link": "https://wikipedia.org/wiki/Smokejumper"}, {"title": "United States Forest Service", "link": "https://wikipedia.org/wiki/United_States_Forest_Service"}, {"title": "Mann Gulch fire", "link": "https://wikipedia.org/wiki/Mann_Gulch_fire"}]}, {"year": "1957", "text": "American Bandstand, a show dedicated to the teenage \"baby-boomers\" by playing the songs and showing popular dances of the time, debuts on the ABC television network.", "html": "1957 - <i><a href=\"https://wikipedia.org/wiki/American_Bandstand\" title=\"American Bandstand\">American Bandstand</a></i>, a show dedicated to the teenage \"baby-boomers\" by playing the songs and showing popular dances of the time, debuts on the <a href=\"https://wikipedia.org/wiki/American_Broadcasting_Company\" title=\"American Broadcasting Company\">ABC</a> television network.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/American_Bandstand\" title=\"American Bandstand\">American Bandstand</a></i>, a show dedicated to the teenage \"baby-boomers\" by playing the songs and showing popular dances of the time, debuts on the <a href=\"https://wikipedia.org/wiki/American_Broadcasting_Company\" title=\"American Broadcasting Company\">ABC</a> television network.", "links": [{"title": "American Bandstand", "link": "https://wikipedia.org/wiki/American_Bandstand"}, {"title": "American Broadcasting Company", "link": "https://wikipedia.org/wiki/American_Broadcasting_Company"}]}, {"year": "1960", "text": "Burkina Faso, then known as Upper Volta, becomes independent from France.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Burkina_Faso\" title=\"Burkina Faso\">Burkina Faso</a>, then known as <a href=\"https://wikipedia.org/wiki/Republic_of_Upper_Volta\" title=\"Republic of Upper Volta\">Upper Volta</a>, becomes independent from France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Burkina_Faso\" title=\"Burkina Faso\">Burkina Faso</a>, then known as <a href=\"https://wikipedia.org/wiki/Republic_of_Upper_Volta\" title=\"Republic of Upper Volta\">Upper Volta</a>, becomes independent from France.", "links": [{"title": "Burkina Faso", "link": "https://wikipedia.org/wiki/Burkina_Faso"}, {"title": "Republic of Upper Volta", "link": "https://wikipedia.org/wiki/Republic_of_Upper_Volta"}]}, {"year": "1962", "text": "Apartheid: <PERSON> is jailed. He would not be released until 1990.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a>: <a href=\"https://wikipedia.org/wiki/Nelson_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is jailed. He would not be released until <a href=\"https://wikipedia.org/wiki/1990\" title=\"1990\">1990</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a>: <a href=\"https://wikipedia.org/wiki/Nelson_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is jailed. He would not be released until <a href=\"https://wikipedia.org/wiki/1990\" title=\"1990\">1990</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Mandela"}, {"title": "1990", "link": "https://wikipedia.org/wiki/1990"}]}, {"year": "1962", "text": "American actress <PERSON> is found dead at her home from a drug overdose.", "html": "1962 - American actress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>\" title=\"Death of <PERSON>\">found dead</a> at her home from a drug overdose.", "no_year_html": "American actress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>\" title=\"Death of <PERSON>\">found dead</a> at her home from a drug overdose.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Death of <PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "Cold War: The United States, the United Kingdom, and the Soviet Union sign the Partial Nuclear Test Ban Treaty.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The United States, the United Kingdom, and the Soviet Union sign the <a href=\"https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty\" title=\"Partial Nuclear Test Ban Treaty\">Partial Nuclear Test Ban Treaty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The United States, the United Kingdom, and the Soviet Union sign the <a href=\"https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty\" title=\"Partial Nuclear Test Ban Treaty\">Partial Nuclear Test Ban Treaty</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Partial Nuclear Test Ban Treaty", "link": "https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty"}]}, {"year": "1964", "text": "Vietnam War: Operation Pierce Arrow: American aircraft from carriers USS Ticonderoga and USS Constellation bomb North Vietnam in retaliation for strikes against U.S. destroyers in the Gulf of Tonkin.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Pierce_Arrow\" title=\"Operation Pierce Arrow\">Operation Pierce Arrow</a>: American aircraft from carriers <a href=\"https://wikipedia.org/wiki/USS_Ticonderoga_(CV-14)\" title=\"USS Ticonderoga (CV-14)\">USS <i>Ticonderoga</i></a> and <a href=\"https://wikipedia.org/wiki/USS_Constellation_(CV-64)\" title=\"USS Constellation (CV-64)\">USS <i>Constellation</i></a> bomb <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a> in retaliation for strikes against U.S. destroyers in the <a href=\"https://wikipedia.org/wiki/Gulf_of_Tonkin\" title=\"Gulf of Tonkin\">Gulf of Tonkin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Pierce_Arrow\" title=\"Operation Pierce Arrow\">Operation Pierce Arrow</a>: American aircraft from carriers <a href=\"https://wikipedia.org/wiki/USS_Ticonderoga_(CV-14)\" title=\"USS Ticonderoga (CV-14)\">USS <i>Ticonderoga</i></a> and <a href=\"https://wikipedia.org/wiki/USS_Constellation_(CV-64)\" title=\"USS Constellation (CV-64)\">USS <i>Constellation</i></a> bomb <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a> in retaliation for strikes against U.S. destroyers in the <a href=\"https://wikipedia.org/wiki/Gulf_of_Tonkin\" title=\"Gulf of Tonkin\">Gulf of Tonkin</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Operation Pierce Arrow", "link": "https://wikipedia.org/wiki/Operation_<PERSON>_Arrow"}, {"title": "USS Ticonderoga (CV-14)", "link": "https://wikipedia.org/wiki/USS_Ticonderoga_(CV-14)"}, {"title": "USS Constellation (CV-64)", "link": "https://wikipedia.org/wiki/USS_Constellation_(CV-64)"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "Gulf of Tonkin", "link": "https://wikipedia.org/wiki/Gulf_of_Tonkin"}]}, {"year": "1965", "text": "The Indo-Pakistani war of 1965 begins as Pakistani soldiers cross the Line of Control dressed as locals.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_war_of_1965\" title=\"Indo-Pakistani war of 1965\">Indo-Pakistani war of 1965</a> begins as Pakistani soldiers cross the <a href=\"https://wikipedia.org/wiki/Line_of_Control\" title=\"Line of Control\">Line of Control</a> dressed as locals.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_war_of_1965\" title=\"Indo-Pakistani war of 1965\">Indo-Pakistani war of 1965</a> begins as Pakistani soldiers cross the <a href=\"https://wikipedia.org/wiki/Line_of_Control\" title=\"Line of Control\">Line of Control</a> dressed as locals.", "links": [{"title": "Indo-Pakistani war of 1965", "link": "https://wikipedia.org/wiki/Indo-Pakistani_war_of_1965"}, {"title": "Line of Control", "link": "https://wikipedia.org/wiki/Line_of_Control"}]}, {"year": "1966", "text": "A group of red guards at Experimental High in Beijing, including <PERSON><PERSON> and <PERSON>, daughters of <PERSON><PERSON> and <PERSON> respectively, beat the deputy vice principal, <PERSON><PERSON>, to death with sticks after accusing her of counter-revolutionary revisionism, producing one of the first fatalities of the Cultural Revolution.", "html": "1966 - A group of <a href=\"https://wikipedia.org/wiki/Red_Guards\" title=\"Red Guards\">red guards</a> at <a href=\"https://wikipedia.org/wiki/Experimental_High_School_Attached_to_Beijing_Normal_University\" title=\"Experimental High School Attached to Beijing Normal University\">Experimental High</a> in Beijing, including <a href=\"https://wikipedia.org/wiki/Deng_<PERSON>\" title=\"Deng <PERSON>\"><PERSON><PERSON></a> and <PERSON>, daughters of <a href=\"https://wikipedia.org/wiki/Deng_<PERSON>\" title=\"Deng <PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> respectively, beat the deputy vice principal, <PERSON><PERSON>, to death with sticks after accusing her of counter-revolutionary revisionism, producing one of the first fatalities of the <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>.", "no_year_html": "A group of <a href=\"https://wikipedia.org/wiki/Red_Guards\" title=\"Red Guards\">red guards</a> at <a href=\"https://wikipedia.org/wiki/Experimental_High_School_Attached_to_Beijing_Normal_University\" title=\"Experimental High School Attached to Beijing Normal University\">Experimental High</a> in Beijing, including <a href=\"https://wikipedia.org/wiki/Deng_<PERSON>\" title=\"Deng <PERSON>\"><PERSON><PERSON></a> and <PERSON>, daughters of <a href=\"https://wikipedia.org/wiki/Deng_<PERSON>\" title=\"Deng <PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> respectively, beat the deputy vice principal, <PERSON><PERSON>, to death with sticks after accusing her of counter-revolutionary revisionism, producing one of the first fatalities of the <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>.", "links": [{"title": "Red Guards", "link": "https://wikipedia.org/wiki/Red_Guards"}, {"title": "Experimental High School Attached to Beijing Normal University", "link": "https://wikipedia.org/wiki/Experimental_High_School_Attached_to_Beijing_Normal_University"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Deng_<PERSON>g"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Den<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cultural Revolution", "link": "https://wikipedia.org/wiki/Cultural_Revolution"}]}, {"year": "1969", "text": "The Lonesome Cowboys police raid occurs in Atlanta, Georgia, leading to the creation of the Georgia Gay Liberation Front.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/Lonesome_Cowboys_police_raid\" title=\"Lonesome Cowboys police raid\"><i>Lonesome Cowboys</i> police raid</a> occurs in <a href=\"https://wikipedia.org/wiki/Atlanta,_Georgia\" class=\"mw-redirect\" title=\"Atlanta, Georgia\">Atlanta, Georgia</a>, leading to the creation of the Georgia Gay Liberation Front.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lonesome_Cowboys_police_raid\" title=\"Lonesome Cowboys police raid\"><i>Lonesome Cowboys</i> police raid</a> occurs in <a href=\"https://wikipedia.org/wiki/Atlanta,_Georgia\" class=\"mw-redirect\" title=\"Atlanta, Georgia\">Atlanta, Georgia</a>, leading to the creation of the Georgia Gay Liberation Front.", "links": [{"title": "Lonesome Cowboys police raid", "link": "https://wikipedia.org/wiki/Lonesome_Cowboys_police_raid"}, {"title": "Atlanta, Georgia", "link": "https://wikipedia.org/wiki/Atlanta,_Georgia"}]}, {"year": "1971", "text": "The first Pacific Islands Forum (then known as the \"South Pacific Forum\") is held in Wellington, New Zealand, with the aim of enhancing cooperation between the independent countries of the Pacific Ocean.", "html": "1971 - The first <a href=\"https://wikipedia.org/wiki/Pacific_Islands_Forum\" title=\"Pacific Islands Forum\">Pacific Islands Forum</a> (then known as the \"South Pacific Forum\") is held in <a href=\"https://wikipedia.org/wiki/Wellington\" title=\"Wellington\">Wellington</a>, New Zealand, with the aim of enhancing cooperation between the independent countries of the Pacific Ocean.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Pacific_Islands_Forum\" title=\"Pacific Islands Forum\">Pacific Islands Forum</a> (then known as the \"South Pacific Forum\") is held in <a href=\"https://wikipedia.org/wiki/Wellington\" title=\"Wellington\">Wellington</a>, New Zealand, with the aim of enhancing cooperation between the independent countries of the Pacific Ocean.", "links": [{"title": "Pacific Islands Forum", "link": "https://wikipedia.org/wiki/Pacific_Islands_Forum"}, {"title": "Wellington", "link": "https://wikipedia.org/wiki/Wellington"}]}, {"year": "1973", "text": "Mars 6 is launched from the USSR.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Mars_6\" title=\"Mars 6\">Mars 6</a> is launched from the USSR.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mars_6\" title=\"Mars 6\">Mars 6</a> is launched from the USSR.", "links": [{"title": "Mars 6", "link": "https://wikipedia.org/wiki/Mars_6"}]}, {"year": "1974", "text": "Watergate scandal: President <PERSON>, under orders of the US Supreme Court, releases the \"Smoking Gun\" tape, recorded on June 23, 1972, clearly revealing his actions in covering up and interfering investigations into the break-in. His political support vanishes completely.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, under orders of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">US Supreme Court</a>, releases the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_House_tapes\" title=\"Nixon White House tapes\">\"Smoking Gun\" tape</a>, recorded on June 23, 1972, clearly revealing his actions in covering up and interfering investigations into the break-in. His political support vanishes completely.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, under orders of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">US Supreme Court</a>, releases the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_House_tapes\" title=\"Nixon White House tapes\">\"Smoking Gun\" tape</a>, recorded on June 23, 1972, clearly revealing his actions in covering up and interfering investigations into the break-in. His political support vanishes completely.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "<PERSON> White House tapes", "link": "https://wikipedia.org/wiki/<PERSON>_White_House_tapes"}]}, {"year": "1979", "text": "In Afghanistan, Maoists undertake the Bala Hissar uprising against the Leninist government.", "html": "1979 - In Afghanistan, Maoists undertake the <a href=\"https://wikipedia.org/wiki/Bala_Hissar_uprising\" title=\"Bala Hissar uprising\">Bala Hissar uprising</a> against the Leninist government.", "no_year_html": "In Afghanistan, Maoists undertake the <a href=\"https://wikipedia.org/wiki/Bala_Hissar_uprising\" title=\"Bala Hissar uprising\">Bala Hissar uprising</a> against the Leninist government.", "links": [{"title": "Bala Hissar uprising", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_uprising"}]}, {"year": "1981", "text": "President <PERSON> fires 11,359 striking air-traffic controllers who ignored his order for them to return to work.", "html": "1981 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Professional_Air_Traffic_Controllers_Organization_(1968)\" title=\"Professional Air Traffic Controllers Organization (1968)\">fires</a> 11,359 <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">striking</a> air-traffic controllers who ignored his order for them to return to work.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Professional_Air_Traffic_Controllers_Organization_(1968)\" title=\"Professional Air Traffic Controllers Organization (1968)\">fires</a> 11,359 <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">striking</a> air-traffic controllers who ignored his order for them to return to work.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Professional Air Traffic Controllers Organization (1968)", "link": "https://wikipedia.org/wiki/Professional_Air_Traffic_Controllers_Organization_(1968)"}, {"title": "Strike action", "link": "https://wikipedia.org/wiki/Strike_action"}]}, {"year": "1984", "text": "A Biman Bangladesh Airlines Fokker F27 Friendship crashes on approach to Zia International Airport, in Dhaka, Bangladesh, killing all 49 people on board.", "html": "1984 - A <a href=\"https://wikipedia.org/wiki/Biman_Bangladesh_Airlines\" title=\"Biman Bangladesh Airlines\">Biman Bangladesh Airlines</a> <a href=\"https://wikipedia.org/wiki/Fokker_F27_Friendship\" title=\"Fokker F27 Friendship\">Fokker F27 Friendship</a> <a href=\"https://wikipedia.org/wiki/1984_Biman_Bangladesh_Airlines_Fokker_F27_crash\" title=\"1984 Biman Bangladesh Airlines Fokker F27 crash\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Hazrat_Shahjalal_International_Airport\" title=\"Hazrat <PERSON>lal International Airport\">Zia International Airport</a>, in <a href=\"https://wikipedia.org/wiki/Dhaka\" title=\"Dhaka\">Dhaka</a>, <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>, killing all 49 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Biman_Bangladesh_Airlines\" title=\"Biman Bangladesh Airlines\">Biman Bangladesh Airlines</a> <a href=\"https://wikipedia.org/wiki/Fokker_F27_Friendship\" title=\"Fokker F27 Friendship\">Fokker F27 Friendship</a> <a href=\"https://wikipedia.org/wiki/1984_Biman_Bangladesh_Airlines_Fokker_F27_crash\" title=\"1984 Biman Bangladesh Airlines Fokker F27 crash\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Hazrat_Shahjalal_International_Airport\" title=\"Hazrat <PERSON> International Airport\">Zia International Airport</a>, in <a href=\"https://wikipedia.org/wiki/Dhaka\" title=\"Dhaka\">Dhaka</a>, <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>, killing all 49 people on board.", "links": [{"title": "Biman Bangladesh Airlines", "link": "https://wikipedia.org/wiki/Biman_Bangladesh_Airlines"}, {"title": "Fokker F27 Friendship", "link": "https://wikipedia.org/wiki/Fokker_F27_Friendship"}, {"title": "1984 Biman Bangladesh Airlines Fokker F27 crash", "link": "https://wikipedia.org/wiki/1984_Biman_Bangladesh_Airlines_Fokker_F27_crash"}, {"title": "<PERSON><PERSON><PERSON> International Airport", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>rat_<PERSON>lal_International_Airport"}, {"title": "Dhaka", "link": "https://wikipedia.org/wiki/Dhaka"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "1995", "text": "Yugoslav Wars: The city of Knin, Croatia, a significant Serb stronghold, is taken by Croatian forces during Operation Storm. The date is celebrated in Croatia as Victory Day.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: The city of <a href=\"https://wikipedia.org/wiki/Knin\" title=\"Knin\">Knin</a>, Croatia, a significant <a href=\"https://wikipedia.org/wiki/Serbs\" title=\"Serbs\">Serb</a> stronghold, is taken by <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatian</a> forces during <a href=\"https://wikipedia.org/wiki/Operation_Storm\" title=\"Operation Storm\">Operation Storm</a>. The date is celebrated in Croatia as <a href=\"https://wikipedia.org/wiki/Victory_Day_(Croatia)\" title=\"Victory Day (Croatia)\">Victory Day</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: The city of <a href=\"https://wikipedia.org/wiki/Knin\" title=\"Knin\">Knin</a>, Croatia, a significant <a href=\"https://wikipedia.org/wiki/Serbs\" title=\"Serbs\">Serb</a> stronghold, is taken by <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatian</a> forces during <a href=\"https://wikipedia.org/wiki/Operation_Storm\" title=\"Operation Storm\">Operation Storm</a>. The date is celebrated in Croatia as <a href=\"https://wikipedia.org/wiki/Victory_Day_(Croatia)\" title=\"Victory Day (Croatia)\">Victory Day</a>.", "links": [{"title": "Yugoslav Wars", "link": "https://wikipedia.org/wiki/Yugoslav_Wars"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Knin"}, {"title": "Serbs", "link": "https://wikipedia.org/wiki/Serbs"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "Operation Storm", "link": "https://wikipedia.org/wiki/Operation_Storm"}, {"title": "Victory Day (Croatia)", "link": "https://wikipedia.org/wiki/Victory_Day_(Croatia)"}]}, {"year": "2003", "text": "A car bomb explodes in the Indonesian capital of Jakarta outside the Marriott Hotel killing 12 and injuring 150.", "html": "2003 - A <a href=\"https://wikipedia.org/wiki/2003_Marriott_Hotel_bombing\" title=\"2003 Marriott Hotel bombing\">car bomb</a> explodes in the <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> capital of <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a> outside the <a href=\"https://wikipedia.org/wiki/Marriott_International\" title=\"Marriott International\">Marriott Hotel</a> killing 12 and injuring 150.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2003_Marriott_Hotel_bombing\" title=\"2003 Marriott Hotel bombing\">car bomb</a> explodes in the <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> capital of <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a> outside the <a href=\"https://wikipedia.org/wiki/Marriott_International\" title=\"Marriott International\">Marriott Hotel</a> killing 12 and injuring 150.", "links": [{"title": "2003 Marriott Hotel bombing", "link": "https://wikipedia.org/wiki/2003_Marriott_Hotel_bombing"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Jakarta", "link": "https://wikipedia.org/wiki/Jakarta"}, {"title": "Marriott International", "link": "https://wikipedia.org/wiki/Marriott_International"}]}, {"year": "2008", "text": "The New England Revolution win the 2008 North American SuperLiga final against the Houston Dynamo.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/New_England_Revolution\" title=\"New England Revolution\">New England Revolution</a> win the <a href=\"https://wikipedia.org/wiki/2008_North_American_SuperLiga_final\" title=\"2008 North American SuperLiga final\">2008 North American SuperLiga final</a> against the <a href=\"https://wikipedia.org/wiki/Houston_Dynamo\" class=\"mw-redirect\" title=\"Houston Dynamo\">Houston Dynamo</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_England_Revolution\" title=\"New England Revolution\">New England Revolution</a> win the <a href=\"https://wikipedia.org/wiki/2008_North_American_SuperLiga_final\" title=\"2008 North American SuperLiga final\">2008 North American SuperLiga final</a> against the <a href=\"https://wikipedia.org/wiki/Houston_Dynamo\" class=\"mw-redirect\" title=\"Houston Dynamo\">Houston Dynamo</a>.", "links": [{"title": "New England Revolution", "link": "https://wikipedia.org/wiki/New_England_Revolution"}, {"title": "2008 North American SuperLiga final", "link": "https://wikipedia.org/wiki/2008_North_American_SuperLiga_final"}, {"title": "Houston Dynamo", "link": "https://wikipedia.org/wiki/Houston_Dynamo"}]}, {"year": "2010", "text": "The Copiapó mining accident occurs, trapping 33 Chilean miners approximately 2,300 ft (700 m) below the ground for 69 days.", "html": "2010 - The <a href=\"https://wikipedia.org/wiki/2010_Copiap%C3%B3_mining_accident\" title=\"2010 Copiapó mining accident\">Copiapó mining accident</a> occurs, trapping 33 Chilean miners approximately 2,300 ft (700 m) below the ground for 69 days.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2010_Copiap%C3%B3_mining_accident\" title=\"2010 Copiapó mining accident\">Copiapó mining accident</a> occurs, trapping 33 Chilean miners approximately 2,300 ft (700 m) below the ground for 69 days.", "links": [{"title": "2010 Copiapó mining accident", "link": "https://wikipedia.org/wiki/2010_Copiap%C3%B3_mining_accident"}]}, {"year": "2010", "text": "Ten members of International Assistance Mission Nuristan Eye Camp team are killed by persons unknown in Kuran wa Munjan District of Badakhshan Province, Afghanistan.", "html": "2010 - Ten members of <a href=\"https://wikipedia.org/wiki/International_Assistance_Mission\" title=\"International Assistance Mission\">International Assistance Mission</a> Nuristan Eye Camp team <a href=\"https://wikipedia.org/wiki/2010_Badakhshan_massacre\" title=\"2010 Badakhshan massacre\">are killed</a> by persons unknown in <a href=\"https://wikipedia.org/wiki/Kuran_wa_Munjan_District\" title=\"Kuran wa Munjan District\">Kuran wa Munjan District</a> of <a href=\"https://wikipedia.org/wiki/Badakhshan_Province\" title=\"Badakhshan Province\">Badakhshan Province, Afghanistan</a>.", "no_year_html": "Ten members of <a href=\"https://wikipedia.org/wiki/International_Assistance_Mission\" title=\"International Assistance Mission\">International Assistance Mission</a> Nuristan Eye Camp team <a href=\"https://wikipedia.org/wiki/2010_Badakhshan_massacre\" title=\"2010 Badakhshan massacre\">are killed</a> by persons unknown in <a href=\"https://wikipedia.org/wiki/Kuran_wa_Munjan_District\" title=\"Kuran wa Munjan District\">Kuran wa Munjan District</a> of <a href=\"https://wikipedia.org/wiki/Badakhshan_Province\" title=\"Badakhshan Province\">Badakhshan Province, Afghanistan</a>.", "links": [{"title": "International Assistance Mission", "link": "https://wikipedia.org/wiki/International_Assistance_Mission"}, {"title": "2010 Badakhshan massacre", "link": "https://wikipedia.org/wiki/2010_Badakhshan_massacre"}, {"title": "Kuran wa Munjan District", "link": "https://wikipedia.org/wiki/Kuran_wa_Munjan_District"}, {"title": "Badakhshan Province", "link": "https://wikipedia.org/wiki/Badakhshan_Province"}]}, {"year": "2012", "text": "The Wisconsin Sikh temple shooting took place in Oak Creek, Wisconsin, killing six victims; the perpetrator committed suicide after being wounded by police.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/Wisconsin_Sikh_temple_shooting\" title=\"Wisconsin Sikh temple shooting\">Wisconsin Sikh temple shooting</a> took place in <a href=\"https://wikipedia.org/wiki/Oak_Creek,_Wisconsin\" title=\"Oak Creek, Wisconsin\">Oak Creek, Wisconsin</a>, killing six victims; the perpetrator committed suicide after being wounded by police.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wisconsin_Sikh_temple_shooting\" title=\"Wisconsin Sikh temple shooting\">Wisconsin Sikh temple shooting</a> took place in <a href=\"https://wikipedia.org/wiki/Oak_Creek,_Wisconsin\" title=\"Oak Creek, Wisconsin\">Oak Creek, Wisconsin</a>, killing six victims; the perpetrator committed suicide after being wounded by police.", "links": [{"title": "Wisconsin Sikh temple shooting", "link": "https://wikipedia.org/wiki/Wisconsin_Sikh_temple_shooting"}, {"title": "Oak Creek, Wisconsin", "link": "https://wikipedia.org/wiki/Oak_Creek,_Wisconsin"}]}, {"year": "2015", "text": "The Environmental Protection Agency at Gold King Mine waste water spill releases three million gallons of heavy metal toxin tailings and waste water into the Animas River in Colorado.", "html": "2015 - The Environmental Protection Agency at <a href=\"https://wikipedia.org/wiki/2015_Gold_King_Mine_waste_water_spill\" title=\"2015 Gold King Mine waste water spill\">Gold King Mine waste water spill</a> releases three million gallons of <a href=\"https://wikipedia.org/wiki/Toxic_heavy_metal\" title=\"Toxic heavy metal\">heavy metal toxin tailings</a> and waste water into the <a href=\"https://wikipedia.org/wiki/Animas_River\" title=\"Animas River\">Animas River</a> in Colorado.", "no_year_html": "The Environmental Protection Agency at <a href=\"https://wikipedia.org/wiki/2015_Gold_King_Mine_waste_water_spill\" title=\"2015 Gold King Mine waste water spill\">Gold King Mine waste water spill</a> releases three million gallons of <a href=\"https://wikipedia.org/wiki/Toxic_heavy_metal\" title=\"Toxic heavy metal\">heavy metal toxin tailings</a> and waste water into the <a href=\"https://wikipedia.org/wiki/Animas_River\" title=\"Animas River\">Animas River</a> in Colorado.", "links": [{"title": "2015 Gold King Mine waste water spill", "link": "https://wikipedia.org/wiki/2015_Gold_King_Mine_waste_water_spill"}, {"title": "Toxic heavy metal", "link": "https://wikipedia.org/wiki/Toxic_heavy_metal"}, {"title": "Animas River", "link": "https://wikipedia.org/wiki/Animas_River"}]}, {"year": "2019", "text": "The revocation of the special status of Jammu and Kashmir (state) occurred and the state was bifurcated into two union territories (Jammu and Kashmir (union territory) and Ladakh).", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/Revocation_of_the_special_status_of_Jammu_and_Kashmir\" title=\"Revocation of the special status of Jammu and Kashmir\">revocation of the special status</a> of <a href=\"https://wikipedia.org/wiki/Jammu_and_Kashmir_(state)\" title=\"Jammu and Kashmir (state)\">Jammu and Kashmir (state)</a> occurred and the state was bifurcated into two union territories (<a href=\"https://wikipedia.org/wiki/Jammu_and_Kashmir_(union_territory)\" title=\"Jammu and Kashmir (union territory)\">Jammu and Kashmir (union territory)</a> and <a href=\"https://wikipedia.org/wiki/Ladakh\" title=\"Ladakh\">Ladakh</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Revocation_of_the_special_status_of_Jammu_and_Kashmir\" title=\"Revocation of the special status of Jammu and Kashmir\">revocation of the special status</a> of <a href=\"https://wikipedia.org/wiki/Jammu_and_Kashmir_(state)\" title=\"Jammu and Kashmir (state)\">Jammu and Kashmir (state)</a> occurred and the state was bifurcated into two union territories (<a href=\"https://wikipedia.org/wiki/Jammu_and_Kashmir_(union_territory)\" title=\"Jammu and Kashmir (union territory)\">Jammu and Kashmir (union territory)</a> and <a href=\"https://wikipedia.org/wiki/Ladakh\" title=\"Ladakh\">Ladakh</a>).", "links": [{"title": "Revocation of the special status of Jammu and Kashmir", "link": "https://wikipedia.org/wiki/Revocation_of_the_special_status_of_Jammu_and_Kashmir"}, {"title": "Jammu and Kashmir (state)", "link": "https://wikipedia.org/wiki/Jammu_and_Kashmir_(state)"}, {"title": "Jammu and Kashmir (union territory)", "link": "https://wikipedia.org/wiki/Jammu_and_Kashmir_(union_territory)"}, {"title": "Ladakh", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>h"}]}, {"year": "2021", "text": "Australia's second most populous state Victoria enters its sixth COVID-19 lockdown, enacting stage four restrictions statewide in reaction to six new COVID-19 cases recorded that morning.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Australia</a>'s second most populous state <a href=\"https://wikipedia.org/wiki/Victoria_(state)\" title=\"Victoria (state)\">Victoria</a> enters its sixth <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a> <a href=\"https://wikipedia.org/wiki/Lockdown\" title=\"Lockdown\">lockdown</a>, enacting stage four restrictions statewide in reaction to six new COVID-19 cases recorded that morning.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Australia</a>'s second most populous state <a href=\"https://wikipedia.org/wiki/Victoria_(state)\" title=\"Victoria (state)\">Victoria</a> enters its sixth <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a> <a href=\"https://wikipedia.org/wiki/Lockdown\" title=\"Lockdown\">lockdown</a>, enacting stage four restrictions statewide in reaction to six new COVID-19 cases recorded that morning.", "links": [{"title": "Australia", "link": "https://wikipedia.org/wiki/Australia"}, {"title": "Victoria (state)", "link": "https://wikipedia.org/wiki/Victoria_(state)"}, {"title": "COVID-19", "link": "https://wikipedia.org/wiki/COVID-19"}, {"title": "Lockdown", "link": "https://wikipedia.org/wiki/Lockdown"}]}, {"year": "2024", "text": "Following the non-cooperation movement against the government of Bangladesh, Bangladeshi Prime Minister Sheikh <PERSON><PERSON> resigns and flees the country, ending her rule of 15 consecutive years and a total of almost two decades.  ", "html": "2024 - Following the <a href=\"https://wikipedia.org/wiki/Non-cooperation_movement_(2024)\" title=\"Non-cooperation movement (2024)\">non-cooperation movement</a> against the <a href=\"https://wikipedia.org/wiki/Fifth_<PERSON><PERSON>_ministry\" title=\"Fifth <PERSON><PERSON> ministry\">government of Bangladesh</a>, Bangladeshi <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON>\" title=\"Sheikh <PERSON><PERSON>\">Sheikh <PERSON><PERSON></a> resigns and flees the country, ending her rule of 15 consecutive years and a total of almost two decades. ", "no_year_html": "Following the <a href=\"https://wikipedia.org/wiki/Non-cooperation_movement_(2024)\" title=\"Non-cooperation movement (2024)\">non-cooperation movement</a> against the <a href=\"https://wikipedia.org/wiki/Fifth_<PERSON><PERSON>_ministry\" title=\"Fifth <PERSON><PERSON> ministry\">government of Bangladesh</a>, Bangladeshi <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON>\" title=\"Sheikh <PERSON><PERSON>\">Sheikh <PERSON><PERSON></a> resigns and flees the country, ending her rule of 15 consecutive years and a total of almost two decades. ", "links": [{"title": "Non-cooperation movement (2024)", "link": "https://wikipedia.org/wiki/Non-cooperation_movement_(2024)"}, {"title": "Fifth <PERSON><PERSON> ministry", "link": "https://wikipedia.org/wiki/Fifth_<PERSON><PERSON>_ministry"}, {"title": "Prime Minister of Bangladesh", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh"}, {"title": "Sheikh <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}], "Births": [{"year": "79 BC", "text": "<PERSON><PERSON><PERSON>, Roman daughter of <PERSON> (d. 45 BC)", "html": "79 BC - 79 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(daughter_of_<PERSON>)\" title=\"<PERSON><PERSON><PERSON> (daughter of <PERSON>)\"><PERSON><PERSON><PERSON></a>, Roman daughter of <a href=\"https://wikipedia.org/wiki/Cicero\" title=\"Cicero\"><PERSON></a> (d. 45 BC)", "no_year_html": "79 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(daughter_of_<PERSON>)\" title=\"<PERSON><PERSON><PERSON> (daughter of <PERSON>)\"><PERSON><PERSON><PERSON></a>, Roman daughter of <a href=\"https://wikipedia.org/wiki/Cicero\" title=\"Cicero\"><PERSON></a> (d. 45 BC)", "links": [{"title": "<PERSON><PERSON><PERSON> (daughter of <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(daughter_of_<PERSON>)"}, {"title": "Cicero", "link": "https://wikipedia.org/wiki/Cicero"}]}, {"year": "1262", "text": "<PERSON><PERSON><PERSON> of Hungary (d. 1290)", "html": "1262 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Hungary\" title=\"<PERSON><PERSON><PERSON> IV of Hungary\"><PERSON><PERSON><PERSON> IV of Hungary</a> (d. 1290)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Hungary\" title=\"<PERSON><PERSON><PERSON> IV of Hungary\"><PERSON><PERSON><PERSON> IV of Hungary</a> (d. 1290)", "links": [{"title": "<PERSON><PERSON><PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>laus_IV_of_Hungary"}]}, {"year": "1301", "text": "<PERSON> of Woodstock, 1st Earl of Kent, English politician, Lord Warden of the Cinque Ports (d. 1330)", "html": "1301 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Woodstock,_1st_Earl_of_Kent\" title=\"<PERSON> of Woodstock, 1st Earl of Kent\"><PERSON> of Woodstock, 1st Earl of Kent</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (d. 1330)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Woodstock,_1st_Earl_of_Kent\" title=\"<PERSON> of Woodstock, 1st Earl of Kent\"><PERSON> of Woodstock, 1st Earl of Kent</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (d. 1330)", "links": [{"title": "<PERSON> of Woodstock, 1st Earl of Kent", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock,_1st_Earl_of_Kent"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1397", "text": "<PERSON>, Belgian-Italian composer and theorist (d. 1474)", "html": "1397 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-Italian composer and theorist (d. 1474)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-Italian composer and theorist (d. 1474)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1461", "text": "<PERSON>, Polish king (d. 1506)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish king (d. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish king (d. 1506)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1540", "text": "<PERSON>, French philologist and historian (d. 1609)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philologist and historian (d. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philologist and historian (d. 1609)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, Italian cardinal (d. 1671)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1671)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1623", "text": "<PERSON>, Italian organist and composer (d. 1669)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1626", "text": "<PERSON>, English politician (d. 1670)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1662", "text": "<PERSON>, Scottish lawyer and historian (d. 1728)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Scottish lawyer and historian (d. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Scottish lawyer and historian (d. 1728)", "links": [{"title": "<PERSON> (lawyer)", "link": "https://wikipedia.org/wiki/<PERSON>_(lawyer)"}]}, {"year": "1681", "text": "<PERSON><PERSON><PERSON>, Danish-born Russian explorer (d. 1741)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/Vitus_<PERSON>\" title=\"Vitus Be<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish-born Russian explorer (d. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vitus_<PERSON>\" title=\"Vitus Bering\"><PERSON><PERSON><PERSON></a>, Danish-born Russian explorer (d. 1741)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitus_<PERSON>"}]}, {"year": "1694", "text": "<PERSON>, Italian composer (d. 1744)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Leo\"><PERSON></a>, Italian composer (d. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON>, American commander and politician (d. 1779)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American commander and politician (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American commander and politician (d. 1779)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1797", "text": "<PERSON>, German cellist and composer (d. 1879)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cellist and composer (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cellist and composer (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_<PERSON>"}]}, {"year": "1802", "text": "<PERSON><PERSON>, Norwegian mathematician and theorist (d. 1829)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Norwegian mathematician and theorist (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian mathematician and theorist (d. 1829)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON><PERSON><PERSON>, French composer (d. 1896)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer (d. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON><PERSON>, Norwegian poet and linguist (d. 1896)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian poet and linguist (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian poet and linguist (d. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, English explorer and politician, Governor of Jamaica (d. 1901)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Jamaica\" class=\"mw-redirect\" title=\"List of Governors of Jamaica\">Governor of Jamaica</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Jamaica\" class=\"mw-redirect\" title=\"List of Governors of Jamaica\">Governor of Jamaica</a> (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Jamaica", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Jamaica"}]}, {"year": "1827", "text": "<PERSON><PERSON><PERSON>, Brazilian field marshal and politician, 1st President of Brazil (d. 1892)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian field marshal and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Fons<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian field marshal and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>eca", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>eca"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1828", "text": "<PERSON> of the Netherlands (d. 1871)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Netherlands\" title=\"<PERSON> of the Netherlands\"><PERSON> of the Netherlands</a> (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Netherlands\" title=\"<PERSON> of the Netherlands\"><PERSON> of the Netherlands</a> (d. 1871)", "links": [{"title": "Louise of the Netherlands", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Netherlands"}]}, {"year": "1833", "text": "<PERSON><PERSON> Vasa (d. 1907)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Vasa\" title=\"<PERSON><PERSON> of Vasa\"><PERSON><PERSON> of Vasa</a> (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Vasa\" title=\"<PERSON><PERSON> of Vasa\"><PERSON><PERSON> of Vasa</a> (d. 1907)", "links": [{"title": "<PERSON><PERSON> of Vasa", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Vasa"}]}, {"year": "1843", "text": "<PERSON>, Scottish violinist and composer (d. 1927)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish violinist and composer (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish violinist and composer (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON><PERSON>, Russian painter and sculptor (d. 1930)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian painter and sculptor (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian painter and sculptor (d. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ilya_Repin"}]}, {"year": "1850", "text": "<PERSON>, French short story writer, novelist, and poet (d. 1893)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French short story writer, novelist, and poet (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French short story writer, novelist, and poet (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, English artist (d. 1939)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English artist (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English artist (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, English man with severe deformities (d. 1890)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English man with severe deformities (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English man with severe deformities (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, German chemist and academic (d. 1923)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Australian cricketer (d. 1917)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON>, Finnish pianist and composer (d. 1924)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish pianist and composer (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish pianist and composer (d. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON>, Brazilian physician, bacteriologist, and epidemiologist, founded the Oswaldo Cruz Foundation (d. 1917)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian physician, bacteriologist, and epidemiologist, founded the <a href=\"https://wikipedia.org/wiki/Oswald<PERSON>_<PERSON>_Foundation\" title=\"Oswaldo Cruz Foundation\">Oswaldo Cruz Foundation</a> (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian physician, bacteriologist, and epidemiologist, founded the <a href=\"https://wikipedia.org/wiki/Oswald<PERSON>_<PERSON>_Foundation\" title=\"Oswaldo Cruz Foundation\">Oswaldo Cruz Foundation</a> (d. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Oswaldo Cruz Foundation", "link": "https://wikipedia.org/wiki/Oswaldo_Cruz_Foundation"}]}, {"year": "1874", "text": "<PERSON>, American economist and academic (d. 1948)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, English golfer (d. 1935)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American historian and activist (d. 1958)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and activist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and activist (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Canadian painter (d. 1917)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American lawyer and jurist (d. 1962)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rush\"><PERSON></a>, American lawyer and jurist (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gertrude Rush\"><PERSON></a>, American lawyer and jurist (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American author and educator (d. 1970)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Irish sculptor (d. 1962)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish sculptor (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish sculptor (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, English-American actor and singer (d. 1972)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and singer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and singer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American novelist, short story writer, critic, and poet (d. 1973)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, critic, and poet (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, critic, and poet (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, Russian-American sculptor (d. 1977)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Naum_<PERSON>\" title=\"Naum Gabo\"><PERSON><PERSON></a>, Russian-American sculptor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naum_<PERSON>\" title=\"Naum Gabo\"><PERSON><PERSON></a>, Russian-American sculptor (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>_<PERSON>o"}]}, {"year": "1890", "text": "<PERSON>, Austrian conductor and director (d. 1956)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor and director (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor and director (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American soprano and educator (d. 1954)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Danish lawyer and politician (d. 1972)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish lawyer and politician (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish lawyer and politician (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German philosopher, classical philologist and translator (d. 1988)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, classical philologist and translator (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, classical philologist and translator (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, French director, screenwriter, and politician (d. 2000)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, screenwriter, and politician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, screenwriter, and politician (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English-American botanist and microbiologist (d. 1997)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American botanist and microbiologist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American botanist and microbiologist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, German-American economist and academic, Nobel Prize laureate (d. 1999)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/1905\" title=\"1905\">1905</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1905\" title=\"1905\">1905</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1999)", "links": [{"title": "1905", "link": "https://wikipedia.org/wiki/1905"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1906", "text": "<PERSON>, English actress (d. 1998)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American actor, director, and screenwriter (d. 1987)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Australian lawyer and politician, 17th Prime Minister of Australia (d. 1967)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1908", "text": "<PERSON>, Filipino short story writer and poet (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Jose <PERSON> Villa\"><PERSON></a>, Filipino short story writer and poet (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Jose <PERSON> Villa\"><PERSON></a>, Filipino short story writer and poet (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, French songwriter and manager (d. 1979)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French songwriter and manager (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French songwriter and manager (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bruno_Coquatrix"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer (d. 1956)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_<PERSON>io\" title=\"<PERSON><PERSON><PERSON> Ma<PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Herminio_Ma<PERSON>tonio"}]}, {"year": "1911", "text": "<PERSON>, American actor and singer (d. 1969)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor and singer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor and singer (d. 1969)", "links": [{"title": "<PERSON> (American actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(American_actor)"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, French priest and humanitarian (d. 2007)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Abb%C3%A9_<PERSON>\" title=\"A<PERSON><PERSON> <PERSON>\">A<PERSON><PERSON></a>, French priest and humanitarian (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abb%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and humanitarian (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abb%C3%A9_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American actor (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American poet and academic (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actor and singer (d. 1982)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English-Canadian ballerina, co-founded Canada's National Ballet School (d. 2004)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian ballerina, co-founded <a href=\"https://wikipedia.org/wiki/Canada%27s_National_Ballet_School\" title=\"Canada's National Ballet School\">Canada's National Ballet School</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian ballerina, co-founded <a href=\"https://wikipedia.org/wiki/Canada%27s_National_Ballet_School\" title=\"Canada's National Ballet School\">Canada's National Ballet School</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Canada's National Ballet School", "link": "https://wikipedia.org/wiki/Canada%27s_National_Ballet_School"}]}, {"year": "1919", "text": "<PERSON><PERSON>, British literary guardian and the only child of author, <PERSON> (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British literary guardian and the only child of author, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British literary guardian and the only child of author, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American painter and academic (d. 2011)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor, director, and producer (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American businessman and religious leader (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman and religious leader (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman and religious leader (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American golfer (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Malaysian-Singaporean union leader and politician, 3rd President of Singapore (d. 2005)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian-Singaporean union leader and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian-Singaporean union leader and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Singapore", "link": "https://wikipedia.org/wiki/President_of_Singapore"}]}, {"year": "1926", "text": "<PERSON>, French composer", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American jazz singer and pianist (d. 1991)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jazz singer and pianist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jazz singer and pianist (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American soldier, police officer, and actor (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, police officer, and actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, police officer, and actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American lawyer and judge (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> II</a>, American lawyer and judge (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American pilot, engineer, and astronaut (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and astronaut (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and astronaut (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American comedian, actress, and singer (d. 1998)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian, actress, and singer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian, actress, and singer (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American race car driver (d. 1989)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Slovak lawyer and politician, 1st President of Slovakia (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kov%C3%A1%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Slovakia\" title=\"President of Slovakia\">President of Slovakia</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v%C3%A1%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Slovakia\" title=\"President of Slovakia\">President of Slovakia</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Kov%C3%A1%C4%8D"}, {"title": "President of Slovakia", "link": "https://wikipedia.org/wiki/President_of_Slovakia"}]}, {"year": "1931", "text": "<PERSON>, Australian footballer and coach (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Dutch pianist and composer (d. 1996)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_Oyens\" title=\"<PERSON><PERSON> de <PERSON>ye<PERSON>\"><PERSON><PERSON> <PERSON></a>, Dutch pianist and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>yens\" title=\"<PERSON>ra de <PERSON> Oye<PERSON>\"><PERSON><PERSON> <PERSON></a>, Dutch pianist and composer (d. 1996)", "links": [{"title": "<PERSON>ra de Marez Oyens", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_O<PERSON>ns"}]}, {"year": "1932", "text": "<PERSON>, Russian conductor", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Swedish engineer and theorist", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%85str%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish engineer and theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%85str%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish engineer and theorist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%85str%C3%B6m"}]}, {"year": "1934", "text": "<PERSON>, American novelist, short story writer, poet, and essayist", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, poet, and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, poet, and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Irish radio and television host (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish radio and television host (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish radio and television host (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, German director and cinematographer (d. 2017)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and cinematographer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and cinematographer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Baron <PERSON>, English field marshal (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English field marshal (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English field marshal (d. 2022)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American soldier, Medal of Honor Winner (d. 1998)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, Medal of Honor Winner (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, Medal of Honor Winner (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Estonian author and playwright (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian author and playwright (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian author and playwright (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Saxon\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American ice hockey player and coach (d. 2003)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English-American astronomer and academic (d. 2010)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American astronomer and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American astronomer and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English race car driver (d. 1998)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rally_driver)\" title=\"<PERSON> (rally driver)\"><PERSON></a>, English race car driver (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rally_driver)\" title=\"<PERSON> (rally driver)\"><PERSON></a>, English race car driver (d. 1998)", "links": [{"title": "<PERSON> (rally driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(rally_driver)"}]}, {"year": "1939", "text": "<PERSON>, Mexican actress and politician (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Carmen_<PERSON>\" title=\"Carmen Sal<PERSON>\"><PERSON></a>, Mexican actress and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carmen_Sal<PERSON>\" title=\"Carmen Salinas\"><PERSON></a>, Mexican actress and politician (d. 2021)", "links": [{"title": "Carmen Sal<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_Salinas"}]}, {"year": "1940", "text": "<PERSON>, American country music songwriter, musician, and producer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music songwriter, musician, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music songwriter, musician, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American football player, coach, and actor (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gabriel\" title=\"Roman Gabriel\"><PERSON></a>, American football player, coach, and actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gabriel\" title=\"Roman Gabriel\"><PERSON></a>, American football player, coach, and actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Gabriel"}]}, {"year": "1940", "text": "<PERSON>, English bass player (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American director, producer, and screenwriter (d. 2007)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Ukrainian general, pilot, and astronaut (d. 2010)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian general, pilot, and astronaut (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian general, pilot, and astronaut (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Brazilian-American drummer and composer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Airto_Moreira\" title=\"Airto Moreira\"><PERSON><PERSON></a>, Brazilian-American drummer and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Airto_Moreira\" title=\"Airto Moreira\"><PERSON><PERSON></a>, Brazilian-American drummer and composer", "links": [{"title": "Airto Moreira", "link": "https://wikipedia.org/wiki/Airto_Moreira"}]}, {"year": "1942", "text": "<PERSON>, American record producer, founded Hannibal Records", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, founded <a href=\"https://wikipedia.org/wiki/Hannibal_Records\" title=\"Hannibal Records\">Hannibal Records</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, founded <a href=\"https://wikipedia.org/wiki/Hannibal_Records\" title=\"Hannibal Records\">Hannibal Records</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hannibal Records", "link": "https://wikipedia.org/wiki/Hannibal_Records"}]}, {"year": "1943", "text": "<PERSON>, American baseball player (d. 2005)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American country music singer-songwriter (d. 2005)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country music singer-songwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country music singer-songwriter (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English composer (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American football player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American physicist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Dutch keyboard player and songwriter (d. 2006)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch keyboard player and songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch keyboard player and songwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian rugby league player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Spanish pediatrician and politician, 118th Mayor of Barcelona", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish pediatrician and politician, 118th <a href=\"https://wikipedia.org/wiki/Mayor_of_Barcelona\" class=\"mw-redirect\" title=\"Mayor of Barcelona\">Mayor of Barcelona</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish pediatrician and politician, 118th <a href=\"https://wikipedia.org/wiki/Mayor_of_Barcelona\" class=\"mw-redirect\" title=\"Mayor of Barcelona\">Mayor of Barcelona</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Barcelona", "link": "https://wikipedia.org/wiki/Mayor_of_Barcelona"}]}, {"year": "1947", "text": "<PERSON>, Australian singer and actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Anderson\"><PERSON></a>, Australian singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American baseball player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Carbo\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bernie Carbo\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON> <PERSON><PERSON>, American astrophysicist and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/France_A._C%C3%B3rdova\" title=\"France A. C<PERSON>\">France <PERSON><PERSON></a>, American astrophysicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France_A._C%C3%B3rdova\" title=\"France A. <PERSON>\">France <PERSON></a>, American astrophysicist and academic", "links": [{"title": "France <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/France_A._C%C3%B3rdova"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian guitarist and songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English footballer and manager (d. 2020)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American bass guitarist, producer, and arranger", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass guitarist, producer, and arranger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass guitarist, producer, and arranger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Japanese architect and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese architect and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese architect and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Brazilian trade union leader and politician (d. 2013)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian trade union leader and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian trade union leader and politician (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ken"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician (d. 2013)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian pop singer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pop singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Hungarian water polo player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Tam%C3%A1s_Farag%C3%B3\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian water polo player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tam%C3%A1s_Farag%C3%B3\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian water polo player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tam%C3%A1s_Farag%C3%B3"}]}, {"year": "1952", "text": "<PERSON>, Australian actor and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Irish talent manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish talent manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish talent manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American baseball player and coach (d. 2005)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American guitarist and songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English Anglican bishop", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Anglican bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Anglican bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian actor, director, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian rugby league player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English businessman", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(executive)\" class=\"mw-redirect\" title=\"<PERSON> (executive)\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(executive)\" class=\"mw-redirect\" title=\"<PERSON> (executive)\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON> (executive)", "link": "https://wikipedia.org/wiki/<PERSON>(executive)"}]}, {"year": "1958", "text": "<PERSON><PERSON>, German equestrian", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Salzgeber\" title=\"<PERSON><PERSON> Salzgeber\"><PERSON><PERSON></a>, German equestrian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Salzgeber\" title=\"<PERSON><PERSON> Salzgeber\"><PERSON><PERSON></a>, German equestrian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ulla_Salzgeber"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter (d. 2016)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American guitarist and songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ar\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ar\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_Smear"}]}, {"year": "1960", "text": "<PERSON>, American lawyer and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Sri Lankan cricketer and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/At<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Samarasekera\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Samarasekera\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Athul<PERSON>_<PERSON>kera"}]}, {"year": "1961", "text": "<PERSON>, American comedian, singer-songwriter, and guitarist (d. 2014)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, singer-songwriter, and guitarist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, singer-songwriter, and guitarist (d. 2014)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1962", "text": "<PERSON>, Jamaican-American basketball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Swiss singer-songwriter (d. 2010)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Swiss singer-songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Swiss singer-songwriter (d. 2010)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Belgian sports administrator", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Ingmar_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian sports administrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian sports administrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_De_V<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English journalist (d. 2013)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American rapper and director (d. 2012)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and director (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and director (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American saxophonist and composer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Japanese keyboard player and composer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese keyboard player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese keyboard player and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sakuraba"}]}, {"year": "1966", "text": "<PERSON>, American singer, bass player, and photographer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, bass player, and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, bass player, and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Canadian singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Japanese wrestler and mixed martial artist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Ken<PERSON>_Kashin\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>shin\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler and mixed martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, French lawyer and politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Marine_Le_Pen\" title=\"Marine Le Pen\"><PERSON></a>, French lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marine_Le_Pen\" title=\"Marine Le Pen\"><PERSON></a>, French lawyer and politician", "links": [{"title": "Marine Le Pen", "link": "https://wikipedia.org/wiki/Marine_Le_Pen"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Ukrainian footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Oleh_Luzhnyi\" title=\"Oleh Luzhnyi\"><PERSON><PERSON></a>, Ukrainian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oleh_Luzhnyi\" title=\"Oleh Luzhnyi\"><PERSON><PERSON></a>, Ukrainian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oleh_Luzhnyi"}]}, {"year": "1968", "text": "<PERSON>, Scottish race car driver (d. 2007)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>-<PERSON>, English politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>-Price", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Barbadian cricketer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricketer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian rower", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Australian rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Australian rower", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)"}]}, {"year": "1970", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" class=\"mw-redirect\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(filmmaker)\" class=\"mw-redirect\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_(filmmaker)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Latvian academic and politician, 11th Prime Minister of Latvia", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian academic and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian academic and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Latvia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Latvia"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Javed\" title=\"<PERSON><PERSON><PERSON> Javed\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aved\" title=\"<PERSON><PERSON><PERSON> Javed\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Javed"}]}, {"year": "1972", "text": "<PERSON>, English-American actor and martial artist (d. 2015)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and martial artist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and martial artist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English rugby player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Jamaican footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Belgian-American guitarist, songwriter, and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, Belgian-American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, Belgian-American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>bers"}]}, {"year": "1973", "text": "<PERSON>, Australian rugby league player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, New Zealand rugby player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Indian film actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON>l"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Swedish footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English guitarist and journalist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Spanish cyclist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>fr%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>fr%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Josep_Jufr%C3%A9"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Finnish cellist and composer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Eicca_Toppinen\" title=\"Eicca Toppinen\">E<PERSON><PERSON></a>, Finnish cellist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eicca_Toppinen\" title=\"Eicca Toppinen\"><PERSON><PERSON><PERSON></a>, Finnish cellist and composer", "links": [{"title": "Eicca Toppinen", "link": "https://wikipedia.org/wiki/Eicca_Toppinen"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Latvian footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Romanian footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%83\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%83\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eugen_<PERSON>%C4%83"}]}, {"year": "1977", "text": "<PERSON>, American baseball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1977)\" title=\"<PERSON> (footballer, born 1977)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1977)\" title=\"<PERSON> (footballer, born 1977)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1977)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1977)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Romanian footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Cosmin_B%C4%83rc%C4%83uan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cosmin_B%C4%83rc%C4%83uan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cosmin_B%C4%83rc%C4%83uan"}]}, {"year": "1978", "text": "<PERSON>, Belgian sprinter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Israeli tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Irish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wayne Bridge\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wayne Bridge\"><PERSON></a>, English footballer", "links": [{"title": "Wayne <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Paraguayan footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Salvador_Caba%C3%B1as\" title=\"Salvador Cabañas\"><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Caba%C3%B1as\" title=\"Salvador Cabañas\"><PERSON>aba<PERSON></a>, Paraguayan footballer", "links": [{"title": "Salvador Cabañas", "link": "https://wikipedia.org/wiki/Salvador_Caba%C3%B1as"}]}, {"year": "1980", "text": "<PERSON>, Australian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor, director, producer, and political activist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, producer, and political activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, producer, and political activist", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1981", "text": "<PERSON>, English ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, English ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, English ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, German footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian skier", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American rapper, singer, and songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>ra<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper, singer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ra<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper, singer, and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian golfer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American murder victim, inspired the Rachel's Challenge (d. 1999)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murder victim, inspired the <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Challenge\" title=\"<PERSON>'s Challenge\"><PERSON>'s Challenge</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murder victim, inspired the <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Challenge\" title=\"<PERSON>'s Challenge\"><PERSON>'s Challenge</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s Challenge", "link": "https://wikipedia.org/wiki/Rachel%27s_Challenge"}]}, {"year": "1982", "text": "<PERSON>, English-German rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American hurdler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Italian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian rugby league player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American mixed martial artist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, New Zealand rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Belgian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Ivorian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Israeli footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>mouth"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Swedish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rka<PERSON> Zengin\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Zengin\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erkan_<PERSON>gin"}]}, {"year": "1986", "text": "<PERSON>, American golfer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Austrian skier", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Scottish-English swimmer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Italian swimmer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Federic<PERSON>_<PERSON>\" title=\"Federica Pellegrini\"><PERSON><PERSON><PERSON></a>, Italian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Federic<PERSON>_<PERSON>\" title=\"Federica <PERSON>rini\"><PERSON><PERSON><PERSON></a>, Italian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Federic<PERSON>_<PERSON>rini"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, French footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Mexican race car driver", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Esteban_Guti%C3%A9rrez\" title=\"Esteban Gutiérrez\"><PERSON><PERSON><PERSON></a>, Mexican race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Guti%C3%A9rrez\" title=\"Esteban Gutiérrez\"><PERSON><PERSON><PERSON></a>, Mexican race car driver", "links": [{"title": "Esteban Gutiérrez", "link": "https://wikipedia.org/wiki/Esteban_Guti%C3%A9rrez"}]}, {"year": "1991", "text": "<PERSON>, Tongan rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tongan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tongan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Dani%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dani%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dani%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Austrian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Spanish rhythmic gymnast", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Spanish rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Spanish rhythmic gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Natalia_Garc%C3%ADa"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, Danish footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>%C3%B8jbjerg\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B8jbjerg\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>%C3%B8jbjerg"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Takakeish%C5%8D_Mitsunobu\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Takakeish%C5%8D_Mitsunob<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Takakeish%C5%8D_<PERSON><PERSON><PERSON>u"}]}, {"year": "1996", "text": "<PERSON>, South Korean singer-songwriter and rapper", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-youn\" class=\"mw-redirect\" title=\"<PERSON>-youn\"><PERSON></a>, South Korean singer-songwriter and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-youn\" class=\"mw-redirect\" title=\"<PERSON>-youn\"><PERSON></a>, South Korean singer-songwriter and rapper", "links": [{"title": "<PERSON>n", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-youn"}]}, {"year": "1997", "text": "<PERSON>, Australian rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actress and singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Chinese dancer, singer and actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Yibo\"><PERSON></a>, Chinese dancer, singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Yibo\"><PERSON></a>, Chinese dancer, singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English musician and actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Yungblud\" title=\"Yungblu<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English musician and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>gblud\" title=\"Yungblud\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English musician and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON><PERSON>d"}]}, {"year": "1998", "text": "<PERSON>, Australian-Lebanese rugby league player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Lebanese rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Lebanese rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Australian rugby league player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "2001", "text": "<PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2003", "text": "<PERSON>, British Paralympic swimmer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Paralympic swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Paralympic swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Spanish footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)"}]}], "Deaths": [{"year": "553", "text": "<PERSON>, prince of the Liang dynasty (b. 508)", "html": "553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ji\"><PERSON></a>, prince of the <a href=\"https://wikipedia.org/wiki/Liang_dynasty\" title=\"Liang dynasty\">Liang dynasty</a> (b. 508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ji\" title=\"<PERSON> Ji\"><PERSON></a>, prince of the <a href=\"https://wikipedia.org/wiki/Liang_dynasty\" title=\"Liang dynasty\">Liang dynasty</a> (b. 508)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Liang dynasty", "link": "https://wikipedia.org/wiki/Liang_dynasty"}]}, {"year": "642", "text": "<PERSON><PERSON>, king of Mercia", "html": "642 - <a href=\"https://wikipedia.org/wiki/Eowa_of_Mercia\" title=\"Eowa of Mercia\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Mercia\" title=\"Mercia\">Mercia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eowa_of_Mercia\" title=\"Eowa of Mercia\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Mercia\" title=\"Mercia\">Mercia</a>", "links": [{"title": "Eowa of Mercia", "link": "https://wikipedia.org/wiki/Eowa_of_Mercia"}, {"title": "Mercia", "link": "https://wikipedia.org/wiki/Mercia"}]}, {"year": "642", "text": "<PERSON>, king of Northumbria", "html": "642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Northumbria\" title=\"<PERSON> of Northumbria\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Northumbria\" class=\"mw-redirect\" title=\"Kingdom of Northumbria\">Northumbria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Northumbria\" title=\"<PERSON> of Northumbria\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Northumbria\" class=\"mw-redirect\" title=\"Kingdom of Northumbria\">Northumbria</a>", "links": [{"title": "<PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/<PERSON>_of_Northumbria"}, {"title": "Kingdom of Northumbria", "link": "https://wikipedia.org/wiki/Kingdom_of_Northumbria"}]}, {"year": "824", "text": "<PERSON><PERSON><PERSON>, Japanese emperor (b. 773)", "html": "824 - <a href=\"https://wikipedia.org/wiki/Emperor_Heizei\" title=\"Emperor He<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>i\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 773)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_He<PERSON>i"}]}, {"year": "877", "text": "<PERSON><PERSON><PERSON> ibn <PERSON><PERSON> ibn <PERSON>, <PERSON><PERSON> vizier", "html": "877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_ibn_<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON> ibn <PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Vizier\" title=\"Vizier\">vizier</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_ibn_<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON> ibn <PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Vizier\" title=\"Vizier\">vizier</a>", "links": [{"title": "<PERSON><PERSON><PERSON> ibn <PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_ibn_<PERSON><PERSON>_ibn_<PERSON>"}, {"title": "Vizier", "link": "https://wikipedia.org/wiki/Vizier"}]}, {"year": "882", "text": "<PERSON>, Frankish king (b. 863)", "html": "882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> III</a>, Frankish king (b. 863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON></a>, Frankish king (b. 863)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_<PERSON>_of_France"}]}, {"year": "890", "text": "<PERSON><PERSON><PERSON> <PERSON>, duke of Aquitaine (b. 850)", "html": "890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Aquitaine\" title=\"<PERSON><PERSON><PERSON> II of Aquitaine\"><PERSON><PERSON><PERSON> II</a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Aquitaine\" title=\"Duchy of Aquitaine\">Aquitaine</a> (b. 850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Aquitaine\" title=\"<PERSON><PERSON><PERSON> II of Aquitaine\"><PERSON><PERSON><PERSON> II</a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Aquitaine\" title=\"Duchy of Aquitaine\">Aquitaine</a> (b. 850)", "links": [{"title": "<PERSON><PERSON><PERSON> of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Aquitaine"}, {"title": "Duchy of Aquitaine", "link": "https://wikipedia.org/wiki/Duchy_of_Aquitaine"}]}, {"year": "910", "text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>, joint kings of Northumbria", "html": "910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_and_<PERSON>dan\" title=\"<PERSON><PERSON><PERSON> and <PERSON><PERSON>\"><PERSON><PERSON><PERSON> and <PERSON><PERSON></a>, joint kings of Northumbria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_and_<PERSON>dan\" title=\"<PERSON><PERSON><PERSON> and Halfdan\"><PERSON><PERSON><PERSON> and <PERSON><PERSON></a>, joint kings of Northumbria", "links": [{"title": "Eowils and Halfdan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_and_<PERSON><PERSON>"}]}, {"year": "910", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, king of Northumbria", "html": "910 - <a href=\"https://wikipedia.org/wiki/Ingw%C3%A6r\" title=\"Ingwær\">Ingw<PERSON><PERSON></a>, king of Northumbria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingw%C3%A6r\" title=\"Ingwær\">Ingw<PERSON><PERSON></a>, king of Northumbria", "links": [{"title": "Ingwær", "link": "https://wikipedia.org/wiki/Ingw%C3%A6r"}]}, {"year": "917", "text": "<PERSON><PERSON><PERSON><PERSON> of Constantinople (b. 834)", "html": "917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON><PERSON> I of Constantinople\"><PERSON><PERSON><PERSON><PERSON> of Constantinople</a> (b. 834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON><PERSON> I of Constantinople\"><PERSON><PERSON><PERSON><PERSON> of Constantinople</a> (b. 834)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I_of_Constantinople"}]}, {"year": "940", "text": "<PERSON>, Chinese general (b. 863)", "html": "940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>g\" title=\"Li Decheng\"><PERSON></a>, Chinese general (b. 863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Decheng\"><PERSON></a>, Chinese general (b. 863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>heng"}]}, {"year": "1063", "text": "<PERSON><PERSON><PERSON><PERSON> ap <PERSON>, King of Gwynedd", "html": "1063 - <a href=\"https://wikipedia.org/wiki/Gruffydd_ap_Llywelyn\" class=\"mw-redirect\" title=\"Gruffydd ap Llywelyn\">Gruffydd ap Llywelyn</a>, <a href=\"https://wikipedia.org/wiki/King_of_Gwynedd\" class=\"mw-redirect\" title=\"King of Gwynedd\">King of Gwynedd</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gruffydd_ap_Llywelyn\" class=\"mw-redirect\" title=\"Gruffydd ap Llywelyn\">Gruffydd ap Llywelyn</a>, <a href=\"https://wikipedia.org/wiki/King_of_Gwynedd\" class=\"mw-redirect\" title=\"King of Gwynedd\">King of Gwynedd</a>", "links": [{"title": "G<PERSON><PERSON>d ap Llywelyn", "link": "https://wikipedia.org/wiki/Gruffydd_ap_Lly<PERSON>lyn"}, {"title": "King of Gwynedd", "link": "https://wikipedia.org/wiki/King_of_Gwynedd"}]}, {"year": "1364", "text": "<PERSON><PERSON><PERSON>, Japanese emperor (b. 1313)", "html": "1364 - <a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dgon\" title=\"Emperor Kō<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 1313)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dgon\" title=\"Emperor Kō<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 1313)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_K%C5%8Dgon"}]}, {"year": "1415", "text": "<PERSON> Conisburgh, 3rd Earl of Cambridge (b. 1375)", "html": "1415 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Conisburgh,_3rd_Earl_of_Cambridge\" title=\"<PERSON> Conisburgh, 3rd Earl of Cambridge\"><PERSON> Conisburgh, 3rd Earl of Cambridge</a> (b. 1375)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Con<PERSON>,_3rd_Earl_of_Cambridge\" title=\"<PERSON> Conisburgh, 3rd Earl of Cambridge\"><PERSON> Conisburgh, 3rd Earl <PERSON> Cambridge</a> (b. 1375)", "links": [{"title": "<PERSON> Conisburgh, 3rd Earl of Cambridge", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Earl_<PERSON>_Cambridge"}]}, {"year": "1415", "text": "<PERSON>, 3rd Baron <PERSON> of Masham (b. 1370)", "html": "1415 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>_of_Masham\" title=\"<PERSON>, 3rd Baron <PERSON> of Masham\"><PERSON>, 3rd Baron <PERSON> of Masham</a> (b. 1370)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>_of_Masham\" title=\"<PERSON>, 3rd Baron <PERSON> of Masham\"><PERSON>, 3rd Baron <PERSON> of Masham</a> (b. 1370)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON> of Masham", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>_of_Masham"}]}, {"year": "1447", "text": "<PERSON>, 2nd Duke of Exeter (b. 1395)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Exeter\" title=\"<PERSON>, 2nd Duke of Exeter\"><PERSON>, 2nd Duke of Exeter</a> (b. 1395)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Exeter\" title=\"<PERSON>, 2nd Duke of Exeter\"><PERSON>, 2nd Duke of Exeter</a> (b. 1395)", "links": [{"title": "<PERSON>, 2nd Duke of Exeter", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Exeter"}]}, {"year": "1579", "text": "<PERSON><PERSON><PERSON>, Polish cardinal (b. 1504)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cardinal (b. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cardinal (b. 1504)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1600", "text": "<PERSON>, 3rd Earl of Gowrie, Scottish conspirator (b. 1577)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Gowrie\" title=\"<PERSON>, 3rd Earl of Gowrie\"><PERSON>, 3rd Earl of Gowrie</a>, Scottish conspirator (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Gowrie\" title=\"<PERSON>, 3rd Earl of Gowrie\"><PERSON>, 3rd Earl of Gowrie</a>, Scottish conspirator (b. 1577)", "links": [{"title": "<PERSON>, 3rd Earl of Gowrie", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_<PERSON>_Go<PERSON>"}]}, {"year": "1610", "text": "<PERSON>, Spanish soldier and politician, Royal Governor of Chile (b. 1552)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_de_Ram%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish soldier and politician, <a href=\"https://wikipedia.org/wiki/Royal_Governor_of_Chile\" title=\"Royal Governor of Chile\">Royal Governor of Chile</a> (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_de_Ram%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish soldier and politician, <a href=\"https://wikipedia.org/wiki/Royal_Governor_of_Chile\" title=\"Royal Governor of Chile\">Royal Governor of Chile</a> (b. 1552)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alonso_Garc%C3%ADa_de_Ram%C3%B3n"}, {"title": "Royal Governor of Chile", "link": "https://wikipedia.org/wiki/Royal_Governor_of_Chile"}]}, {"year": "1633", "text": "<PERSON>, English archbishop and academic (b. 1562)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Abbot_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English archbishop and academic (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Abbot_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English archbishop and academic (b. 1562)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1678", "text": "<PERSON>, Mexican tenor and composer (b. 1619)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_de_Z%C3%A9spedes\" title=\"<PERSON>\"><PERSON></a>, Mexican tenor and composer (b. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_de_Z%C3%A9spedes\" title=\"<PERSON>\"><PERSON></a>, Mexican tenor and composer (b. 1619)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_<PERSON>%C3%ADa_de_Z%C3%A9spedes"}]}, {"year": "1729", "text": "<PERSON>, English engineer, invented the eponymous Newcomen atmospheric engine (b. 1664)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, invented the eponymous <a href=\"https://wikipedia.org/wiki/Newcomen_atmospheric_engine\" title=\"Newcomen atmospheric engine\">Newcomen atmospheric engine</a> (b. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, invented the eponymous <a href=\"https://wikipedia.org/wiki/Newcomen_atmospheric_engine\" title=\"Newcomen atmospheric engine\">Newcomen atmospheric engine</a> (b. 1664)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Newcomen atmospheric engine", "link": "https://wikipedia.org/wiki/Newcomen_atmospheric_engine"}]}, {"year": "1743", "text": "<PERSON>, 2nd Baron <PERSON>, English courtier and politician, Vice-Chamberlain of the Household (b. 1696)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English courtier and politician, <a href=\"https://wikipedia.org/wiki/Vice-Chamberlain_of_the_Household\" title=\"Vice-Chamberlain of the Household\">Vice-Chamberlain of the Household</a> (b. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English courtier and politician, <a href=\"https://wikipedia.org/wiki/Vice-Chamberlain_of_the_Household\" title=\"Vice-Chamberlain of the Household\">Vice-Chamberlain of the Household</a> (b. 1696)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>"}, {"title": "Vice-Chamberlain of the Household", "link": "https://wikipedia.org/wiki/Vice-Chamberlain_of_the_Household"}]}, {"year": "1778", "text": "<PERSON>, French historian and author (b. 1703)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9mencet\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9mencet\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_Cl%C3%A9mencet"}]}, {"year": "1778", "text": "<PERSON> the younger, English composer (b. 1756)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_younger\" title=\"<PERSON> the younger\"><PERSON> the younger</a>, English composer (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_younger\" title=\"<PERSON> the younger\"><PERSON> the younger</a>, English composer (b. 1756)", "links": [{"title": "<PERSON> the younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_younger"}]}, {"year": "1792", "text": "<PERSON>, Lord <PERSON>, English politician, Prime Minister of Great Britain (b. 1732)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord North\"><PERSON>, Lord <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord North\"><PERSON>, Lord <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1732)", "links": [{"title": "<PERSON>, Lord North", "link": "https://wikipedia.org/wiki/Frederick_<PERSON>,_Lord_<PERSON>"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1799", "text": "<PERSON>, 1st <PERSON>, English admiral and politician (b. 1726)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, English admiral and politician (b. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, English admiral and politician (b. 1726)", "links": [{"title": "<PERSON>, 1st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON>, French archaeologist and historian (b. 1788)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_de_Cr%C3%A8vec%C5%93ur_de_<PERSON>\" title=\"<PERSON> Crèvecœur de Perthes\"><PERSON>ur de <PERSON></a>, French archaeologist and historian (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_de_Cr%C3%A8vec%C5%93ur_de_<PERSON>\" title=\"<PERSON>rèvecœur de Perthes\"><PERSON>ur <PERSON></a>, French archaeologist and historian (b. 1788)", "links": [{"title": "<PERSON>rève<PERSON>œur de Perth<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_de_Cr%C3%A8vec%C5%93ur_de_<PERSON>"}]}, {"year": "1877", "text": "<PERSON> (known as <PERSON><PERSON><PERSON>), Welsh poet (b. 1830)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON><PERSON><PERSON>_<PERSON>)\" title=\"<PERSON> (Trebor Mai)\"><PERSON> (known as <PERSON><PERSON><PERSON>)</a>, Welsh poet (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON><PERSON><PERSON>_<PERSON>)\" title=\"<PERSON> (Trebor Mai)\"><PERSON> (known as <PERSON><PERSON><PERSON>)</a>, Welsh poet (b. 1830)", "links": [{"title": "<PERSON> (<PERSON><PERSON>bor <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON><PERSON><PERSON>_<PERSON>)"}]}, {"year": "1880", "text": "<PERSON>, Austrian physician and dermatologist (b. 1816)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and dermatologist (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and dermatologist (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON>, American tribal chief (b. 1823)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Spotted_Tail\" title=\"Spotted Tail\"><PERSON>ted Tail</a>, American tribal chief (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spotted_Tail\" title=\"Spotted Tail\">Spotted Tail</a>, American tribal chief (b. 1823)", "links": [{"title": "Spotted Tail", "link": "https://wikipedia.org/wiki/Spotted_Tail"}]}, {"year": "1895", "text": "<PERSON>, German philosopher (b. 1820)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Princess Royal of the United Kingdom and German Empress (b. 1840)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Victoria,_Princess_<PERSON>\" title=\"<PERSON>, Princess <PERSON>\"><PERSON>, Princess <PERSON></a> of the United Kingdom and German Empress (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria,_Princess_<PERSON>\" title=\"<PERSON>, Princess <PERSON>\"><PERSON>, Princess <PERSON></a> of the United Kingdom and German Empress (b. 1840)", "links": [{"title": "<PERSON>, Princess <PERSON>", "link": "https://wikipedia.org/wiki/Victoria,_Princess_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Australian politician, 10th Premier of New South Wales (b. 1834)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1911", "text": "<PERSON>, American baseball player and umpire (b. 1864)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, British composer (b. 1885)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British composer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British composer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Greek lawyer and politician, 78th Prime Minister of Greece (b. 1844)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 78th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 78th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (b. 1844)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, English trade union leader and activist (b. 1847)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Millicent_Fawcett\" title=\"Millicent Fawcett\"><PERSON><PERSON><PERSON></a>, English trade union leader and activist (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Millicent_Fawcett\" title=\"Millicent Fawcett\"><PERSON><PERSON><PERSON></a>, English trade union leader and activist (b. 1847)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Millicent_Faw<PERSON>tt"}]}, {"year": "1933", "text": "<PERSON>, American painter and academic (b. 1856)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American art director and set designer (b. 1891)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_director)\" title=\"<PERSON> (art director)\"><PERSON></a>, American art director and set designer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_director)\" title=\"<PERSON> (art director)\"><PERSON></a>, American art director and set designer (b. 1891)", "links": [{"title": "<PERSON> (art director)", "link": "https://wikipedia.org/wiki/<PERSON>_(art_director)"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Hungarian economist and politician, Minister of Education of Hungary (b. 1865)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/B%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian economist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Education_of_Hungary\" class=\"mw-redirect\" title=\"Minister of Education of Hungary\">Minister of Education of Hungary</a> (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian economist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Education_of_Hungary\" class=\"mw-redirect\" title=\"Minister of Education of Hungary\">Minister of Education of Hungary</a> (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Education of Hungary", "link": "https://wikipedia.org/wiki/Minister_of_Education_of_Hungary"}]}, {"year": "1944", "text": "<PERSON>, Welsh cricketer and rugby player (b. 1906)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh cricketer and rugby player (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh cricketer and rugby player (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, German lawyer and politician, 17th Chancellor of Germany (b. 1863)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "1948", "text": "<PERSON><PERSON>, English cricketer and lawyer (b. 1871)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ller\" title=\"<PERSON><PERSON> Toller\"><PERSON><PERSON></a>, English cricketer and lawyer (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Toller\"><PERSON><PERSON></a>, English cricketer and lawyer (b. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ller"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Egyptian physicist and academic (b. 1917)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian physicist and academic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian physicist and academic (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Portuguese-Brazilian actress and singer (b. 1909)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-Brazilian actress and singer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-Brazilian actress and singer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (b. 1877)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1959", "text": "<PERSON>, English-American journalist and poet (b. 1881)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and poet (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Guest\"><PERSON></a>, English-American journalist and poet (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian lawyer and politician, 9th Prime Minister of Canada (b. 1874)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1963", "text": "<PERSON>, Spanish composer (b. 1898)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Salvador_Bacarisse\" title=\"Salvador Bacarisse\"><PERSON></a>, Spanish composer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Bacarisse\" title=\"Salvador Bacarisse\"><PERSON></a>, Spanish composer (b. 1898)", "links": [{"title": "Salvador Bacarisse", "link": "https://wikipedia.org/wiki/Salvador_Bacarisse"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Swedish author (b. 1890)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian-American ice hockey player and coach (b. 1886)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Ross"}]}, {"year": "1968", "text": "<PERSON>, American guitarist (b. 1928)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player and coach (b. 1893)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American soldier and politician (b. 1924)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>s"}]}, {"year": "1983", "text": "<PERSON>, American actress and comedian (b. 1913)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English economist and author (b. 1903)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and author (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Welsh actor (b. 1925)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player and coach (b. 1898)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German politician, Mayor of Marburg (b. 1910)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fmann\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Marburg\" class=\"mw-redirect\" title=\"Mayor of Marburg\">Mayor of Marburg</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fmann\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Marburg\" class=\"mw-redirect\" title=\"Mayor of Marburg\">Mayor of Marburg</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_Ga%C3%9Fmann"}, {"title": "Mayor of Marburg", "link": "https://wikipedia.org/wiki/Mayor_of_Marburg"}]}, {"year": "1991", "text": "<PERSON>, American football player and coach (b. 1908)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Japanese engineer and businessman, founded Honda (b. 1906)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Soichiro_Honda\" title=\"Soichiro Honda\"><PERSON><PERSON><PERSON></a>, Japanese engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Honda\" title=\"Honda\">Honda</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soichiro_Honda\" title=\"Soichiro Honda\"><PERSON><PERSON><PERSON></a>, Japanese engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Honda\" title=\"Honda\">Honda</a> (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON> Honda", "link": "https://wikipedia.org/wiki/<PERSON>ichiro_Honda"}, {"title": "Honda", "link": "https://wikipedia.org/wiki/Honda"}]}, {"year": "1992", "text": "<PERSON>, New Zealand politician, 31st Prime Minister of New Zealand (b. 1921)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Israeli composer (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Menachem_Avidom\" title=\"Menachem Avidom\"><PERSON><PERSON><PERSON></a>, Israeli composer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Menachem_Avidom\" title=\"Menachem Avidom\"><PERSON><PERSON><PERSON></a>, Israeli composer (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Menachem_Avidom"}]}, {"year": "1994", "text": "<PERSON>, Belgian race car driver (b. 1922)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, German commander (b. 1912)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German commander (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German commander (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Bulgarian commander and politician, 36th Prime Minister of Bulgaria (b. 1911)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian commander and politician, 36th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria\" title=\"Prime Minister of Bulgaria\">Prime Minister of Bulgaria</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian commander and politician, 36th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria\" title=\"Prime Minister of Bulgaria\">Prime Minister of Bulgaria</a> (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Bulgaria", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria"}]}, {"year": "2000", "text": "<PERSON>, Austrian-Brazilian journalist and activist (b. 1920)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Brazilian journalist and activist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Brazilian journalist and activist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Montenegrin-Italian pilot and painter (b. 1910)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Montenegrin-Italian pilot and painter (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Montenegrin-Italian pilot and painter (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Indian cricketer (b. 1911)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English actor (b. 1914)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Ugandan politician, 2nd Prime Minister of Uganda (b. 1929)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Otema_Allimadi\" title=\"Otema Allimadi\"><PERSON><PERSON><PERSON></a>, Ugandan politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Uganda\" title=\"Prime Minister of Uganda\">Prime Minister of Uganda</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>tema_Allimadi\" title=\"<PERSON>tema Allimadi\"><PERSON><PERSON><PERSON></a>, Ugandan politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Uganda\" title=\"Prime Minister of Uganda\">Prime Minister of Uganda</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON> All<PERSON>", "link": "https://wikipedia.org/wiki/Otema_Allimadi"}, {"title": "Prime Minister of Uganda", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Uganda"}]}, {"year": "2001", "text": "<PERSON>, Australian-Spanish businessman (b. 1948)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Spanish businessman (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Spanish businessman (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actor (b. 1982)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American sportscaster (b. 1916)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Chick_Hearn\" title=\"Chick Hearn\"><PERSON><PERSON> Hearn</a>, American sportscaster (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chick_Hearn\" title=\"Chick Hearn\"><PERSON><PERSON>n</a>, American sportscaster (b. 1916)", "links": [{"title": "<PERSON><PERSON>n", "link": "https://wikipedia.org/wiki/Chi<PERSON>_Hearn"}]}, {"year": "2002", "text": "<PERSON>, Italian journalist and author (b. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Franco_<PERSON>i\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco_Luce<PERSON>i\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Lucentini"}]}, {"year": "2002", "text": "<PERSON>, American baseball player (b. 1952)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actor, producer, and screenwriter (b. 1937)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, producer, and screenwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, producer, and screenwriter (b. 1937)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Russian gymnast and coach (b. 1936)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast and coach (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast and coach (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American football player and coach (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hora\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hora\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jim_O%27Hora"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Filipino lawyer and politician, 31st Filipino Secretary of Education (b. 1941)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/Secretary_of_Education_(Philippines)\" title=\"Secretary of Education (Philippines)\">Filipino Secretary of Education</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/Secretary_of_Education_(Philippines)\" title=\"Secretary of Education (Philippines)\">Filipino Secretary of Education</a> (b. 1941)", "links": [{"title": "Raul Roco", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}, {"title": "Secretary of Education (Philippines)", "link": "https://wikipedia.org/wiki/Secretary_of_Education_(Philippines)"}]}, {"year": "2005", "text": "<PERSON>, Welsh footballer (b. 1909)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)\" title=\"<PERSON> (footballer, born 1909)\"><PERSON></a>, Welsh footballer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)\" title=\"<PERSON> (footballer, born 1909)\"><PERSON></a>, Welsh footballer (b. 1909)", "links": [{"title": "<PERSON> (footballer, born 1909)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, French cardinal (b. 1926)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Romanian actor, singer, director, and producer (b. 1943)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C8%99\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian actor, singer, director, and producer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C8%99\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian actor, singer, director, and producer (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Florian_<PERSON>%C8%99"}]}, {"year": "2008", "text": "<PERSON>, English-American chemist and academic (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English-American chemist and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English-American chemist and academic (b. 1932)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>(chemist)"}]}, {"year": "2008", "text": "<PERSON>, Australian singer-songwriter, guitarist, and producer (b. 1929)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American author, screenwriter, and producer (b. 1914)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, screenwriter, and producer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, screenwriter, and producer (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Polish farmer and politician, Deputy Prime Minister of Poland (b. 1954)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish farmer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Poland\" title=\"Deputy Prime Minister of Poland\">Deputy Prime Minister of Poland</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish farmer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Poland\" title=\"Deputy Prime Minister of Poland\">Deputy Prime Minister of Poland</a> (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Poland"}]}, {"year": "2011", "text": "<PERSON>, Australian bodybuilder (b. 1989)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian bodybuilder (b. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian bodybuilder (b. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Polish director and screenwriter (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish director and screenwriter (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish director and screenwriter (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Belgian lawyer and politician (b. 1949)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian lawyer and politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian lawyer and politician (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American football player (b. 1984)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian-American businessman, co-founded Film Society of Lincoln Center (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Film_Society_of_Lincoln_Center\" class=\"mw-redirect\" title=\"Film Society of Lincoln Center\">Film Society of Lincoln Center</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Film_Society_of_Lincoln_Center\" class=\"mw-redirect\" title=\"Film Society of Lincoln Center\">Film Society of Lincoln Center</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Film Society of Lincoln Center", "link": "https://wikipedia.org/wiki/Film_Society_of_Lincoln_Center"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Costa Rican-Mexican singer-songwriter and actress (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Costa Rican-Mexican singer-songwriter and actress (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Costa Rican-Mexican singer-songwriter and actress (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French author and translator (b. 1960)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and translator (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and translator (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American sculptor and educator (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian-American ice hockey player (b. 1966)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian singer-songwriter and producer (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American basketball player and coach (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (b. 1925)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2013", "text": "<PERSON>, American activist (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/May_<PERSON>_<PERSON>\" title=\"May <PERSON>\"><PERSON></a>, American activist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_<PERSON>_<PERSON>\" title=\"May <PERSON>\">May <PERSON></a>, American activist (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American commander and judge (b. 1959)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and judge (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and judge (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>a"}]}, {"year": "2014", "text": "<PERSON>, American general (b. 1962)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Russian author (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Russian author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Russian author (b. 1936)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "2014", "text": "<PERSON>, Indian-English historian, journalist, and author (b. 1914)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pincher\"><PERSON></a>, Indian-English historian, journalist, and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pincher\"><PERSON></a>, Indian-English historian, journalist, and author (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "2014", "text": "<PERSON>, American physician and academic, 11th Surgeon General of the United States (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic, 11th <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic, 11th <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Surgeon General of the United States", "link": "https://wikipedia.org/wiki/Surgeon_General_of_the_United_States"}]}, {"year": "2015", "text": "<PERSON>, English journalist and politician (b. 1912)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Welsh footballer (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American zoologist (b. 1953)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American author, Pulitzer Prize winner, and Nobel laureate (b. 1931).", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Fiction\" title=\"Pulitzer Prize for Fiction\">Pulitzer Prize winner</a>, and <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel laureate</a> (b. 1931).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Fiction\" title=\"Pulitzer Prize for Fiction\">Pulitzer Prize winner</a>, and <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel laureate</a> (b. 1931).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pulitzer Prize for Fiction", "link": "https://wikipedia.org/wiki/Pulitzer_Prize_for_Fiction"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Somali human rights activist and physician (b. 1947)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Somali_people\" title=\"Somali people\">Somali</a> human rights activist and physician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Somali_people\" title=\"Somali people\">Somali</a> human rights activist and physician (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}, {"title": "Somali people", "link": "https://wikipedia.org/wiki/Somali_people"}]}, {"year": "2022", "text": "<PERSON>, Australian singer-songwriter (b. 1943)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Judith_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Judith_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Judith_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Filipino actress (b. 1963)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>erie <PERSON>\"><PERSON><PERSON></a>, Filipino actress (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>erie <PERSON>\"><PERSON><PERSON> <PERSON></a>, Filipino actress (b. 1963)", "links": [{"title": "Cherie <PERSON>", "link": "https://wikipedia.org/wiki/Cherie_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Syrian army officer (b. 1932)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Syrian_army_officer)\" title=\"<PERSON> (Syrian army officer)\"><PERSON></a>, Syrian army officer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Syrian_army_officer)\" title=\"<PERSON> (Syrian army officer)\"><PERSON></a>, Syrian army officer (b. 1932)", "links": [{"title": "<PERSON> (Syrian army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Syrian_army_officer)"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Japanese fashion designer (b. 1938)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese fashion designer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese fashion designer (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Irish hurler (b. 1998)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurler (b. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurler (b. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}