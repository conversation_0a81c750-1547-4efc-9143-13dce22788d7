{"date": "December 15", "url": "https://wikipedia.org/wiki/December_15", "data": {"Events": [{"year": "533", "text": "Vandalic War: Byzantine general <PERSON><PERSON><PERSON> defeats the Vandals, commanded by King <PERSON><PERSON><PERSON>, at the Battle of Tricamarum.", "html": "533 - <a href=\"https://wikipedia.org/wiki/Vandalic_War\" title=\"Vandalic War\">Vandalic War</a>: <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> general <a href=\"https://wikipedia.org/wiki/Belisarius\" title=\"Bel<PERSON>rius\"><PERSON><PERSON><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Vandals\" title=\"Vandals\">Vandals</a>, commanded by King <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tricamarum\" title=\"Battle of Tricamarum\">Battle of Tricamarum</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vandalic_War\" title=\"Vandalic War\">Vandalic War</a>: <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> general <a href=\"https://wikipedia.org/wiki/Bel<PERSON>rius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Vandals\" title=\"Vandals\">Vandals</a>, commanded by King <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tricamarum\" title=\"Battle of Tricamarum\">Battle of Tricamarum</a>.", "links": [{"title": "Vandalic War", "link": "https://wikipedia.org/wiki/Vandalic_War"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Belisarius"}, {"title": "Vandals", "link": "https://wikipedia.org/wiki/Vandals"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Battle of Tricamarum", "link": "https://wikipedia.org/wiki/Battle_of_Tricamarum"}]}, {"year": "687", "text": "<PERSON> <PERSON><PERSON><PERSON> is elected as a compromise between antipopes <PERSON> and <PERSON>.", "html": "687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sergius_<PERSON>\" title=\"Pope Sergius I\">Pope <PERSON><PERSON><PERSON> I</a> is elected as a compromise between <a href=\"https://wikipedia.org/wiki/Antipope\" title=\"Antipope\">antipopes</a> <PERSON><PERSON><PERSON> and <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ser<PERSON>us_I\" title=\"Pope Sergius I\">Pope <PERSON><PERSON><PERSON> I</a> is elected as a compromise between <a href=\"https://wikipedia.org/wiki/Antipope\" title=\"Antipope\">antipopes</a> <PERSON><PERSON><PERSON> and <PERSON>.", "links": [{"title": "<PERSON> Sergi<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_I"}, {"title": "Antipope", "link": "https://wikipedia.org/wiki/Antipope"}]}, {"year": "1025", "text": "<PERSON> becomes sole emperor of the Byzantine Empire, 63 years after being crowned co-emperor.", "html": "1025 - <a href=\"https://wikipedia.org/wiki/Constantine_VIII\" title=\"Constantine VIII\"><PERSON> VIII</a> becomes sole <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">emperor</a> of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>, 63 years after being crowned co-emperor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_VIII\" title=\"Constantine VIII\"><PERSON> VIII</a> becomes sole <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">emperor</a> of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>, 63 years after being crowned co-emperor.", "links": [{"title": "Constantine VIII", "link": "https://wikipedia.org/wiki/Constantine_VIII"}, {"title": "Byzantine Emperor", "link": "https://wikipedia.org/wiki/Byzantine_Emperor"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "1161", "text": "Jin-Song wars: Military officers conspire against the emperor <PERSON><PERSON> of the Jin dynasty after a military defeat at the Battle of Caishi, and assassinate the emperor at his camp.", "html": "1161 - <a href=\"https://wikipedia.org/wiki/Jin%E2%80%93Song_wars\" title=\"Jin-Song wars\">Jin-Song wars</a>: Military officers conspire against the emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)\" title=\"Jin dynasty (1115-1234)\">Jin dynasty</a> after a military defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_Caishi\" title=\"Battle of Caishi\">Battle of Caishi</a>, and assassinate the emperor at his camp.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jin%E2%80%93Song_wars\" title=\"Jin-Song wars\">Jin-Song wars</a>: Military officers conspire against the emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)\" title=\"Jin dynasty (1115-1234)\">Jin dynasty</a> after a military defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_Caishi\" title=\"Battle of Caishi\">Battle of Caishi</a>, and assassinate the emperor at his camp.", "links": [{"title": "Jin-Song wars", "link": "https://wikipedia.org/wiki/Jin%E2%80%93Song_wars"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Jin dynasty (1115-1234)", "link": "https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)"}, {"title": "Battle of Caishi", "link": "https://wikipedia.org/wiki/Battle_of_Caishi"}]}, {"year": "1167", "text": "Sicilian Chancellor <PERSON> moves the royal court to Messina to prevent a rebellion.", "html": "1167 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sicily_(Medieval_and_Early_Modern)\" class=\"mw-redirect\" title=\"Kingdom of Sicily (Medieval and Early Modern)\">Sicilian</a> <a href=\"https://wikipedia.org/wiki/Chancellor\" title=\"Chancellor\">Chancellor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> moves the royal court to <a href=\"https://wikipedia.org/wiki/Messina\" title=\"Messina\">Messina</a> to prevent a rebellion.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_Sicily_(Medieval_and_Early_Modern)\" class=\"mw-redirect\" title=\"Kingdom of Sicily (Medieval and Early Modern)\">Sicilian</a> <a href=\"https://wikipedia.org/wiki/Chancellor\" title=\"Chancellor\">Chancellor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> moves the royal court to <a href=\"https://wikipedia.org/wiki/Messina\" title=\"Messina\">Messina</a> to prevent a rebellion.", "links": [{"title": "Kingdom of Sicily (Medieval and Early Modern)", "link": "https://wikipedia.org/wiki/Kingdom_of_Sicily_(Medieval_and_Early_Modern)"}, {"title": "Chancellor", "link": "https://wikipedia.org/wiki/Chancellor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Messina", "link": "https://wikipedia.org/wiki/Messina"}]}, {"year": "1256", "text": "Mongol forces under <PERSON><PERSON><PERSON> enter and dismantle the Nizari Ismaili (Assassin) stronghold at Alamut Castle (in present-day Iran) as part of their offensive on Islamic southwest Asia.", "html": "1256 - <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongol</a> forces under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> enter and dismantle the <a href=\"https://wikipedia.org/wiki/Nizari_Ismaili_state\" title=\"Nizari Ismaili state\">Nizari Ismaili</a> (<a href=\"https://wikipedia.org/wiki/Order_of_Assassins\" title=\"Order of Assassins\">Assassin</a>) stronghold at <a href=\"https://wikipedia.org/wiki/Alamut_Castle\" title=\"Alamut Castle\">Alamut Castle</a> (in present-day <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>) as part of <a href=\"https://wikipedia.org/wiki/Mongol_campaign_against_the_Nizaris\" title=\"Mongol campaign against the Nizaris\">their offensive</a> on Islamic southwest Asia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongol</a> forces under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> enter and dismantle the <a href=\"https://wikipedia.org/wiki/Nizari_Ismaili_state\" title=\"Nizari Ismaili state\">Nizari Ismaili</a> (<a href=\"https://wikipedia.org/wiki/Order_of_Assassins\" title=\"Order of Assassins\">Assassin</a>) stronghold at <a href=\"https://wikipedia.org/wiki/Alamut_Castle\" title=\"Alamut Castle\">Alamut Castle</a> (in present-day <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>) as part of <a href=\"https://wikipedia.org/wiki/Mongol_campaign_against_the_Nizaris\" title=\"Mongol campaign against the Nizaris\">their offensive</a> on Islamic southwest Asia.", "links": [{"title": "Mongols", "link": "https://wikipedia.org/wiki/Mongols"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nizari Ismaili state", "link": "https://wikipedia.org/wiki/Nizari_Ismaili_state"}, {"title": "Order of Assassins", "link": "https://wikipedia.org/wiki/Order_of_Assassins"}, {"title": "Alamut Castle", "link": "https://wikipedia.org/wiki/Alamut_Castle"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Mongol campaign against the Nizaris", "link": "https://wikipedia.org/wiki/Mongol_campaign_against_the_Nizaris"}]}, {"year": "1270", "text": "The Nizari Ismaili garrison of Gerdkuh, Persia surrender after 17 years to the Mongols.", "html": "1270 - The <a href=\"https://wikipedia.org/wiki/Nizari_Ismaili_state\" title=\"Nizari Ismaili state\">Nizari Ismaili</a> garrison of <a href=\"https://wikipedia.org/wiki/Gerdkuh\" title=\"Gerdkuh\">Gerdkuh</a>, Persia surrender after 17 years to the <a href=\"https://wikipedia.org/wiki/Mongol_campaign_against_the_Nizaris\" title=\"Mongol campaign against the Nizaris\">Mongols</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nizari_Ismaili_state\" title=\"Nizari Ismaili state\">Nizari Ismaili</a> garrison of <a href=\"https://wikipedia.org/wiki/Gerdkuh\" title=\"Gerdkuh\">Gerdkuh</a>, Persia surrender after 17 years to the <a href=\"https://wikipedia.org/wiki/Mongol_campaign_against_the_Nizaris\" title=\"Mongol campaign against the Nizaris\">Mongols</a>.", "links": [{"title": "Nizari Ismaili state", "link": "https://wikipedia.org/wiki/Nizari_Ismaili_state"}, {"title": "Gerdkuh", "link": "https://wikipedia.org/wiki/Gerdkuh"}, {"title": "Mongol campaign against the Nizaris", "link": "https://wikipedia.org/wiki/Mongol_campaign_against_the_Nizaris"}]}, {"year": "1467", "text": "<PERSON> of Moldavia defeats <PERSON> of Hungary, with the latter being injured thrice, at the Battle of Baia.", "html": "1467 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Moldavia\" class=\"mw-redirect\" title=\"<PERSON> of Moldavia\"><PERSON> of Moldavia</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Hungary, with the latter being injured thrice, at the <a href=\"https://wikipedia.org/wiki/Battle_of_Baia\" title=\"Battle of Baia\">Battle of Baia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Moldavia\" class=\"mw-redirect\" title=\"<PERSON> of Moldavia\"><PERSON> of Moldavia</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Hungary, with the latter being injured thrice, at the <a href=\"https://wikipedia.org/wiki/Battle_of_Baia\" title=\"Battle of Baia\">Battle of Baia</a>.", "links": [{"title": "<PERSON> of Moldavia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Moldavia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Baia", "link": "https://wikipedia.org/wiki/Battle_of_Baia"}]}, {"year": "1546", "text": "The town of Ekenäs (Finnish: Tammisaari) is founded by King <PERSON> of Sweden.", "html": "1546 - The town of <a href=\"https://wikipedia.org/wiki/Eken%C3%A4s,_Finland\" title=\"Ekenäs, Finland\"><PERSON>ken<PERSON><PERSON></a> (<a href=\"https://wikipedia.org/wiki/Finnish_language\" title=\"Finnish language\">Finnish</a>: <i lang=\"fi\">Tammisaari</i>) is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\">King <PERSON> of Sweden</a>.", "no_year_html": "The town of <a href=\"https://wikipedia.org/wiki/Eken%C3%A4s,_Finland\" title=\"Ekenäs, Finland\"><PERSON>ken<PERSON>s</a> (<a href=\"https://wikipedia.org/wiki/Finnish_language\" title=\"Finnish language\">Finnish</a>: <i lang=\"fi\">Tammisaari</i>) is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\">King <PERSON> of Sweden</a>.", "links": [{"title": "Ekenäs, Finland", "link": "https://wikipedia.org/wiki/Eken%C3%A4s,_Finland"}, {"title": "Finnish language", "link": "https://wikipedia.org/wiki/Finnish_language"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1651", "text": "Castle Cornet in Guernsey, the last stronghold which had supported the King in the Third English Civil War, surrenders.", "html": "1651 - <a href=\"https://wikipedia.org/wiki/Castle_Cornet\" title=\"Castle Cornet\">Castle Cornet</a> in <a href=\"https://wikipedia.org/wiki/Guernsey\" title=\"Guernsey\">Guernsey</a>, the last stronghold which had supported the <PERSON> in the <a href=\"https://wikipedia.org/wiki/Third_English_Civil_War\" class=\"mw-redirect\" title=\"Third English Civil War\">Third English Civil War</a>, surrenders.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Castle_Cornet\" title=\"Castle Cornet\">Castle Cornet</a> in <a href=\"https://wikipedia.org/wiki/Guernsey\" title=\"Guernsey\">Guernsey</a>, the last stronghold which had supported the <PERSON> in the <a href=\"https://wikipedia.org/wiki/Third_English_Civil_War\" class=\"mw-redirect\" title=\"Third English Civil War\">Third English Civil War</a>, surrenders.", "links": [{"title": "Castle Cornet", "link": "https://wikipedia.org/wiki/Castle_Cornet"}, {"title": "Guernsey", "link": "https://wikipedia.org/wiki/Guernsey"}, {"title": "Third English Civil War", "link": "https://wikipedia.org/wiki/Third_English_Civil_War"}]}, {"year": "1778", "text": "American Revolutionary War: British and French fleets clash in the Battle of St. Lucia.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">British</a> and French fleets clash in the <a href=\"https://wikipedia.org/wiki/Battle_of_St._Lucia\" title=\"Battle of St. Lucia\">Battle of St. Lucia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">British</a> and French fleets clash in the <a href=\"https://wikipedia.org/wiki/Battle_of_St._Lucia\" title=\"Battle of St. Lucia\">Battle of St. Lucia</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Battle of St. Lucia", "link": "https://wikipedia.org/wiki/Battle_of_St._Lucia"}]}, {"year": "1791", "text": "The United States Bill of Rights becomes law when ratified by the Virginia General Assembly.", "html": "1791 - The <a href=\"https://wikipedia.org/wiki/United_States_Bill_of_Rights\" title=\"United States Bill of Rights\">United States Bill of Rights</a> becomes law when ratified by the <a href=\"https://wikipedia.org/wiki/Virginia_General_Assembly\" title=\"Virginia General Assembly\">Virginia General Assembly</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Bill_of_Rights\" title=\"United States Bill of Rights\">United States Bill of Rights</a> becomes law when ratified by the <a href=\"https://wikipedia.org/wiki/Virginia_General_Assembly\" title=\"Virginia General Assembly\">Virginia General Assembly</a>.", "links": [{"title": "United States Bill of Rights", "link": "https://wikipedia.org/wiki/United_States_Bill_of_Rights"}, {"title": "Virginia General Assembly", "link": "https://wikipedia.org/wiki/Virginia_General_Assembly"}]}, {"year": "1836", "text": "The U.S. Patent Office building in Washington, D.C., nearly burns to the ground, destroying all 9,957 patents issued by the federal government to that date, as well as 7,000 related patent models.", "html": "1836 - The <a href=\"https://wikipedia.org/wiki/Old_Patent_Office_Building\" title=\"Old Patent Office Building\">U.S. Patent Office</a> building in Washington, D.C., nearly <a href=\"https://wikipedia.org/wiki/1836_U.S._Patent_Office_fire\" title=\"1836 U.S. Patent Office fire\">burns to the ground</a>, destroying all 9,957 patents issued by the federal government to that date, as well as 7,000 related <a href=\"https://wikipedia.org/wiki/Patent_model\" title=\"Patent model\">patent models</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Old_Patent_Office_Building\" title=\"Old Patent Office Building\">U.S. Patent Office</a> building in Washington, D.C., nearly <a href=\"https://wikipedia.org/wiki/1836_U.S._Patent_Office_fire\" title=\"1836 U.S. Patent Office fire\">burns to the ground</a>, destroying all 9,957 patents issued by the federal government to that date, as well as 7,000 related <a href=\"https://wikipedia.org/wiki/Patent_model\" title=\"Patent model\">patent models</a>.", "links": [{"title": "Old Patent Office Building", "link": "https://wikipedia.org/wiki/Old_Patent_Office_Building"}, {"title": "1836 U.S. Patent Office fire", "link": "https://wikipedia.org/wiki/1836_U.S._Patent_Office_fire"}, {"title": "Patent model", "link": "https://wikipedia.org/wiki/Patent_model"}]}, {"year": "1862", "text": "American Civil War: The Battle of Fredericksburg ends in a Union defeat as General <PERSON> withdraws the Army of the Potomac across the Rappahannock River.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Fredericksburg\" title=\"Battle of Fredericksburg\">Battle of Fredericksburg</a> ends in a <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> defeat as General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> withdraws the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> across the <a href=\"https://wikipedia.org/wiki/Rappahannock_River\" title=\"Rappahannock River\">Rappahannock River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Fredericksburg\" title=\"Battle of Fredericksburg\">Battle of Fredericksburg</a> ends in a <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> defeat as General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> withdraws the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> across the <a href=\"https://wikipedia.org/wiki/Rappahannock_River\" title=\"Rappahannock River\">Rappahannock River</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Fredericksburg", "link": "https://wikipedia.org/wiki/Battle_of_Fredericksburg"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Army of the Potomac", "link": "https://wikipedia.org/wiki/Army_of_the_Potomac"}, {"title": "Rappahannock River", "link": "https://wikipedia.org/wiki/Rappahannock_River"}]}, {"year": "1864", "text": "American Civil War: The Battle of Nashville begins at Nashville, Tennessee, and ends the following day with the destruction of the Confederate Army of Tennessee under General <PERSON> as a fighting force by the Union Army of the Cumberland under General <PERSON>.", "html": "1864 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Nashville\" title=\"Battle of Nashville\">Battle of Nashville</a> begins at <a href=\"https://wikipedia.org/wiki/Nashville,_Tennessee\" title=\"Nashville, Tennessee\">Nashville, Tennessee</a>, and ends the following day with the destruction of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Army_of_Tennessee\" title=\"Army of Tennessee\">Army of Tennessee</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as a fighting force by the Union <a href=\"https://wikipedia.org/wiki/Army_of_the_Cumberland\" title=\"Army of the Cumberland\">Army of the Cumberland</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Nashville\" title=\"Battle of Nashville\">Battle of Nashville</a> begins at <a href=\"https://wikipedia.org/wiki/Nashville,_Tennessee\" title=\"Nashville, Tennessee\">Nashville, Tennessee</a>, and ends the following day with the destruction of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Army_of_Tennessee\" title=\"Army of Tennessee\">Army of Tennessee</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as a fighting force by the Union <a href=\"https://wikipedia.org/wiki/Army_of_the_Cumberland\" title=\"Army of the Cumberland\">Army of the Cumberland</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Battle of Nashville", "link": "https://wikipedia.org/wiki/Battle_of_Nashville"}, {"title": "Nashville, Tennessee", "link": "https://wikipedia.org/wiki/Nashville,_Tennessee"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Army of Tennessee", "link": "https://wikipedia.org/wiki/Army_of_Tennessee"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Army of the Cumberland", "link": "https://wikipedia.org/wiki/Army_of_the_Cumberland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "The short-lived Republic of Ezo is proclaimed in the Ezo area of Japan. It is the first attempt to establish a democracy in Japan.", "html": "1869 - The short-lived <a href=\"https://wikipedia.org/wiki/Republic_of_Ezo\" title=\"Republic of Ezo\">Republic of Ezo</a> is proclaimed in the <a href=\"https://wikipedia.org/wiki/Ezo\" title=\"Ezo\">Ezo</a> area of <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>. It is the first attempt to establish a <a href=\"https://wikipedia.org/wiki/Democracy\" title=\"Democracy\">democracy</a> in Japan.", "no_year_html": "The short-lived <a href=\"https://wikipedia.org/wiki/Republic_of_Ezo\" title=\"Republic of Ezo\">Republic of Ezo</a> is proclaimed in the <a href=\"https://wikipedia.org/wiki/Ezo\" title=\"Ezo\">Ezo</a> area of <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>. It is the first attempt to establish a <a href=\"https://wikipedia.org/wiki/Democracy\" title=\"Democracy\">democracy</a> in Japan.", "links": [{"title": "Republic of Ezo", "link": "https://wikipedia.org/wiki/Republic_of_Ezo"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ezo"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}, {"title": "Democracy", "link": "https://wikipedia.org/wiki/Democracy"}]}, {"year": "1871", "text": "Sixteen-year-old telegraphist <PERSON> keys and sends the first telegraphed message from Arizona Territory at the Deseret Telegraph Company office in Pipe Spring.", "html": "1871 - Sixteen-year-old <a href=\"https://wikipedia.org/wiki/Telegraphist\" title=\"Telegraphist\">telegraphist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> keys and sends the first telegraphed message from <a href=\"https://wikipedia.org/wiki/Arizona_Territory\" title=\"Arizona Territory\">Arizona Territory</a> at the <a href=\"https://wikipedia.org/wiki/Deseret_Telegraph_Company\" title=\"Deseret Telegraph Company\">Deseret Telegraph Company</a> office in <a href=\"https://wikipedia.org/wiki/Pipe_Spring_National_Monument\" title=\"Pipe Spring National Monument\">Pipe Spring</a>.", "no_year_html": "Sixteen-year-old <a href=\"https://wikipedia.org/wiki/Telegraphist\" title=\"Telegraphist\">telegraphist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> keys and sends the first telegraphed message from <a href=\"https://wikipedia.org/wiki/Arizona_Territory\" title=\"Arizona Territory\">Arizona Territory</a> at the <a href=\"https://wikipedia.org/wiki/Deseret_Telegraph_Company\" title=\"Deseret Telegraph Company\">Deseret Telegraph Company</a> office in <a href=\"https://wikipedia.org/wiki/Pipe_Spring_National_Monument\" title=\"Pipe Spring National Monument\">Pipe Spring</a>.", "links": [{"title": "Telegraphist", "link": "https://wikipedia.org/wiki/Telegraphist"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Arizona Territory", "link": "https://wikipedia.org/wiki/Arizona_Territory"}, {"title": "Deseret Telegraph Company", "link": "https://wikipedia.org/wiki/Deseret_Telegraph_Company"}, {"title": "Pipe Spring National Monument", "link": "https://wikipedia.org/wiki/Pipe_Spring_National_Monument"}]}, {"year": "1890", "text": "Hunkpapa Lakota leader <PERSON> is killed on Standing Rock Indian Reservation, leading to the Wounded Knee Massacre.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Hunkpapa\" title=\"Hunkpapa\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> leader <a href=\"https://wikipedia.org/wiki/Sitting_Bull\" title=\"Sitting Bull\">Sitting Bull</a> is killed on <a href=\"https://wikipedia.org/wiki/Standing_Rock_Indian_Reservation\" title=\"Standing Rock Indian Reservation\">Standing Rock Indian Reservation</a>, leading to the <a href=\"https://wikipedia.org/wiki/Wounded_Knee_Massacre\" title=\"Wounded Knee Massacre\">Wounded Knee Massacre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hunk<PERSON>pa\" title=\"Hunkpapa\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> leader <a href=\"https://wikipedia.org/wiki/Sitting_Bull\" title=\"Sitting Bull\">Sitting Bull</a> is killed on <a href=\"https://wikipedia.org/wiki/Standing_Rock_Indian_Reservation\" title=\"Standing Rock Indian Reservation\">Standing Rock Indian Reservation</a>, leading to the <a href=\"https://wikipedia.org/wiki/Wounded_Knee_Massacre\" title=\"Wounded Knee Massacre\">Wounded Knee Massacre</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hunkpapa"}, {"title": "Lakota people", "link": "https://wikipedia.org/wiki/Lakota_people"}, {"title": "Sitting Bull", "link": "https://wikipedia.org/wiki/Sitting_Bull"}, {"title": "Standing Rock Indian Reservation", "link": "https://wikipedia.org/wiki/Standing_Rock_Indian_Reservation"}, {"title": "Wounded Knee Massacre", "link": "https://wikipedia.org/wiki/Wounded_Knee_Massacre"}]}, {"year": "1893", "text": "Symphony No. 9 (\"From the New World\" a.k.a. the \"New World Symphony\") by <PERSON><PERSON> premieres in a public afternoon rehearsal at Carnegie Hall in New York City, followed by a concert premiere on the evening of December 16.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Symphony_No._9_(Dvo%C5%99%C3%A1k)\" title=\"Symphony No. 9 (<PERSON><PERSON><PERSON><PERSON>)\"><i>Symphony No. 9</i> (\"From the New World\" <abbr title=\"also known as\">a.k.a.</abbr> the \"New World Symphony\")</a> by <a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> premieres in a public afternoon rehearsal at <a href=\"https://wikipedia.org/wiki/Carnegie_Hall\" title=\"Carnegie Hall\">Carnegie Hall</a> in New York City, followed by a concert premiere on the evening of December 16.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Symphony_No._9_(Dvo%C5%99%C3%A1k)\" title=\"Symphony No. 9 (<PERSON><PERSON><PERSON><PERSON>)\"><i>Symphony No. 9</i> (\"From the New World\" <abbr title=\"also known as\">a.k.a.</abbr> the \"New World Symphony\")</a> by <a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> premieres in a public afternoon rehearsal at <a href=\"https://wikipedia.org/wiki/Carnegie_Hall\" title=\"Carnegie Hall\">Carnegie Hall</a> in New York City, followed by a concert premiere on the evening of December 16.", "links": [{"title": "Symphony No. 9 (<PERSON><PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._9_(Dvo%C5%99%C3%A1k)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k"}, {"title": "Carnegie Hall", "link": "https://wikipedia.org/wiki/Carnegie_Hall"}]}, {"year": "1899", "text": "British Army forces are defeated at the Battle of Colenso in Natal, South Africa, the third and final battle fought during the Black Week of the Second Boer War.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> forces are defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Colenso\" class=\"mw-redirect\" title=\"Battle of Colenso\">Battle of Colenso</a> in <a href=\"https://wikipedia.org/wiki/Natal_(province)\" title=\"Natal (province)\">Natal</a>, <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a>, the third and final battle fought during the <a href=\"https://wikipedia.org/wiki/Black_Week\" title=\"Black Week\">Black Week</a> of the <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> forces are defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Colenso\" class=\"mw-redirect\" title=\"Battle of Colenso\">Battle of Colenso</a> in <a href=\"https://wikipedia.org/wiki/Natal_(province)\" title=\"Natal (province)\">Natal</a>, <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a>, the third and final battle fought during the <a href=\"https://wikipedia.org/wiki/Black_Week\" title=\"Black Week\">Black Week</a> of the <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>.", "links": [{"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}, {"title": "Battle of Colenso", "link": "https://wikipedia.org/wiki/Battle_of_Colenso"}, {"title": "Natal (province)", "link": "https://wikipedia.org/wiki/Natal_(province)"}, {"title": "South Africa", "link": "https://wikipedia.org/wiki/South_Africa"}, {"title": "Black Week", "link": "https://wikipedia.org/wiki/Black_Week"}, {"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}]}, {"year": "1903", "text": "Italian American food cart vendor <PERSON><PERSON> receives a U.S. patent for inventing a machine that makes ice cream cones.", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Italian_American\" class=\"mw-redirect\" title=\"Italian American\">Italian American</a> food cart vendor <PERSON><PERSON> receives a <a href=\"https://wikipedia.org/wiki/United_States_patent_law\" title=\"United States patent law\">U.S. patent</a> for inventing a machine that makes <a href=\"https://wikipedia.org/wiki/Ice_cream_cone\" title=\"Ice cream cone\">ice cream cones</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italian_American\" class=\"mw-redirect\" title=\"Italian American\">Italian American</a> food cart vendor <PERSON><PERSON> receives a <a href=\"https://wikipedia.org/wiki/United_States_patent_law\" title=\"United States patent law\">U.S. patent</a> for inventing a machine that makes <a href=\"https://wikipedia.org/wiki/Ice_cream_cone\" title=\"Ice cream cone\">ice cream cones</a>.", "links": [{"title": "Italian American", "link": "https://wikipedia.org/wiki/Italian_American"}, {"title": "United States patent law", "link": "https://wikipedia.org/wiki/United_States_patent_law"}, {"title": "Ice cream cone", "link": "https://wikipedia.org/wiki/Ice_cream_cone"}]}, {"year": "1905", "text": "The Pushkin House is established in Saint Petersburg, Russia, to preserve the cultural heritage of <PERSON>.", "html": "1905 - The <a href=\"https://wikipedia.org/wiki/Pushkin_House\" title=\"Pushkin House\">Pushkin House</a> is established in <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, Russia, to preserve the cultural heritage of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pushkin_House\" title=\"Pushkin House\">Pushkin House</a> is established in <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, Russia, to preserve the cultural heritage of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Pushkin House", "link": "https://wikipedia.org/wiki/Pushkin_House"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "The London Underground's Great Northern, Piccadilly and Brompton Railway opens.", "html": "1906 - The <a href=\"https://wikipedia.org/wiki/London_Underground\" title=\"London Underground\">London Underground</a>'s <a href=\"https://wikipedia.org/wiki/Great_Northern,_Piccadilly_and_Brompton_Railway\" title=\"Great Northern, Piccadilly and Brompton Railway\">Great Northern, Piccadilly and Brompton Railway</a> opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/London_Underground\" title=\"London Underground\">London Underground</a>'s <a href=\"https://wikipedia.org/wiki/Great_Northern,_Piccadilly_and_Brompton_Railway\" title=\"Great Northern, Piccadilly and Brompton Railway\">Great Northern, Piccadilly and Brompton Railway</a> opens.", "links": [{"title": "London Underground", "link": "https://wikipedia.org/wiki/London_Underground"}, {"title": "Great Northern, Piccadilly and Brompton Railway", "link": "https://wikipedia.org/wiki/Great_Northern,_Piccadilly_and_Brompton_Railway"}]}, {"year": "1914", "text": "World War I: The Serbian Army recaptures Belgrade from the invading Austro-Hungarian Army.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Serbian_Army\" title=\"Serbian Army\">Serbian Army</a> recaptures <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade</a> from the invading <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Army\" title=\"Austro-Hungarian Army\">Austro-Hungarian Army</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Serbian_Army\" title=\"Serbian Army\">Serbian Army</a> recaptures <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade</a> from the invading <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Army\" title=\"Austro-Hungarian Army\">Austro-Hungarian Army</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Serbian Army", "link": "https://wikipedia.org/wiki/Serbian_Army"}, {"title": "Belgrade", "link": "https://wikipedia.org/wiki/Belgrade"}, {"title": "Austro-Hungarian Army", "link": "https://wikipedia.org/wiki/Austro-Hungarian_Army"}]}, {"year": "1914", "text": "A gas explosion at Mitsubishi Hōjō coal mine, in Kyushu, Japan, kills 687.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_Coal_Mine_Disaster\" title=\"Hōjō Coal Mine Disaster\">A gas explosion</a> at <a href=\"https://wikipedia.org/wiki/Mitsubishi\" title=\"Mitsubishi\">Mitsubishi</a> Hōjō coal mine, in <a href=\"https://wikipedia.org/wiki/Kyushu\" title=\"Kyushu\">Kyushu</a>, Japan, kills 687.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_Coal_Mine_Disaster\" title=\"Hōjō Coal Mine Disaster\">A gas explosion</a> at <a href=\"https://wikipedia.org/wiki/Mitsubishi\" title=\"Mitsubishi\">Mitsubishi</a> Hōjō coal mine, in <a href=\"https://wikipedia.org/wiki/Kyushu\" title=\"Kyushu\">Kyushu</a>, Japan, kills 687.", "links": [{"title": "Hōjō Coal Mine Disaster", "link": "https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_Coal_Mine_Disaster"}, {"title": "Mitsubishi", "link": "https://wikipedia.org/wiki/Mitsubishi"}, {"title": "Kyushu", "link": "https://wikipedia.org/wiki/Kyushu"}]}, {"year": "1917", "text": "World War I: An armistice between Russia and the Central Powers is signed.", "html": "1917 - World War I: An <a href=\"https://wikipedia.org/wiki/Armistice_between_Russia_and_the_Central_Powers\" title=\"Armistice between Russia and the Central Powers\">armistice between Russia and the Central Powers</a> is signed.", "no_year_html": "World War I: An <a href=\"https://wikipedia.org/wiki/Armistice_between_Russia_and_the_Central_Powers\" title=\"Armistice between Russia and the Central Powers\">armistice between Russia and the Central Powers</a> is signed.", "links": [{"title": "Armistice between Russia and the Central Powers", "link": "https://wikipedia.org/wiki/Armistice_between_Russia_and_the_Central_Powers"}]}, {"year": "1933", "text": "Anarchist insurrection suppressed in Zaragoza, Spain.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Anarchist_insurrection_of_December_1933\" title=\"Anarchist insurrection of December 1933\">Anarchist insurrection</a> suppressed in <a href=\"https://wikipedia.org/wiki/Zaragoza\" title=\"Zaragoza\">Zaragoza</a>, <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anarchist_insurrection_of_December_1933\" title=\"Anarchist insurrection of December 1933\">Anarchist insurrection</a> suppressed in <a href=\"https://wikipedia.org/wiki/Zaragoza\" title=\"Zaragoza\">Zaragoza</a>, <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spain</a>.", "links": [{"title": "Anarchist insurrection of December 1933", "link": "https://wikipedia.org/wiki/Anarchist_insurrection_of_December_1933"}, {"title": "Zaragoza", "link": "https://wikipedia.org/wiki/Zaragoza"}, {"title": "Second Spanish Republic", "link": "https://wikipedia.org/wiki/Second_Spanish_Republic"}]}, {"year": "1939", "text": "Gone with the Wind (highest inflation adjusted grossing film) receives its premiere at Loew's Grand Theatre in Atlanta, Georgia, United States.", "html": "1939 - <i><a href=\"https://wikipedia.org/wiki/Gone_with_the_Wind_(film)\" title=\"Gone with the Wind (film)\">Gone with the Wind</a></i> (highest inflation adjusted grossing film) receives its premiere at <a href=\"https://wikipedia.org/wiki/Loew%27s_Grand_Theatre\" title=\"Loew's Grand Theatre\">Loew's Grand Theatre</a> in <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, Georgia</a>, United States.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Gone_with_the_Wind_(film)\" title=\"Gone with the Wind (film)\">Gone with the Wind</a></i> (highest inflation adjusted grossing film) receives its premiere at <a href=\"https://wikipedia.org/wiki/Loew%27s_Grand_Theatre\" title=\"Loew's Grand Theatre\">Loew's Grand Theatre</a> in <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, Georgia</a>, United States.", "links": [{"title": "Gone with the Wind (film)", "link": "https://wikipedia.org/wiki/Gone_with_the_Wind_(film)"}, {"title": "Loew's Grand Theatre", "link": "https://wikipedia.org/wiki/Loew%27s_Grand_Theatre"}, {"title": "Atlanta", "link": "https://wikipedia.org/wiki/Atlanta"}]}, {"year": "1941", "text": "The Holocaust in Ukraine: German troops murder over 15,000 Jews at Drobytsky Yar, a ravine southeast of the city of Kharkiv.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/The_Holocaust_in_Ukraine\" title=\"The Holocaust in Ukraine\">The Holocaust in Ukraine</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops murder over 15,000 Jews at <a href=\"https://wikipedia.org/wiki/Drobytsky_Yar\" title=\"Drobytsky Yar\">Drobytsky Yar</a>, a ravine southeast of the city of <a href=\"https://wikipedia.org/wiki/Kharkiv\" title=\"Kharkiv\">Kharkiv</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust_in_Ukraine\" title=\"The Holocaust in Ukraine\">The Holocaust in Ukraine</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops murder over 15,000 Jews at <a href=\"https://wikipedia.org/wiki/Drobytsky_Yar\" title=\"Drobytsky Yar\">Drobytsky Yar</a>, a ravine southeast of the city of <a href=\"https://wikipedia.org/wiki/Kharkiv\" title=\"Kharkiv\">Kharkiv</a>.", "links": [{"title": "The Holocaust in Ukraine", "link": "https://wikipedia.org/wiki/The_Holocaust_in_Ukraine"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Drobytsky Yar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Yar"}, {"title": "Kharkiv", "link": "https://wikipedia.org/wiki/Kharkiv"}]}, {"year": "1942", "text": "World War II: The Battle of Mount Austen, the Galloping Horse, and the Sea Horse begins during the Guadalcanal Campaign.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mount_Austen,_the_Galloping_Horse,_and_the_Sea_Horse\" title=\"Battle of Mount Austen, the Galloping Horse, and the Sea Horse\">Battle of Mount Austen, the Galloping Horse, and the Sea Horse</a> begins during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mount_Austen,_the_Galloping_Horse,_and_the_Sea_Horse\" title=\"Battle of Mount Austen, the Galloping Horse, and the Sea Horse\">Battle of Mount Austen, the Galloping Horse, and the Sea Horse</a> begins during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Mount Austen, the Galloping Horse, and the Sea Horse", "link": "https://wikipedia.org/wiki/Battle_of_Mount_Austen,_the_Galloping_Horse,_and_the_Sea_Horse"}, {"title": "Guadalcanal Campaign", "link": "https://wikipedia.org/wiki/Guadalcanal_Campaign"}]}, {"year": "1943", "text": "World War II: The Battle of Arawe begins during the New Britain campaign.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Arawe\" title=\"Battle of Arawe\">Battle of Arawe</a> begins during the <a href=\"https://wikipedia.org/wiki/New_Britain_campaign\" title=\"New Britain campaign\">New Britain campaign</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Arawe\" title=\"Battle of Arawe\">Battle of Arawe</a> begins during the <a href=\"https://wikipedia.org/wiki/New_Britain_campaign\" title=\"New Britain campaign\">New Britain campaign</a>.", "links": [{"title": "Battle of Arawe", "link": "https://wikipedia.org/wiki/Battle_of_Arawe"}, {"title": "New Britain campaign", "link": "https://wikipedia.org/wiki/New_Britain_campaign"}]}, {"year": "1944", "text": "World War II: a single-engine UC-64A Norseman aeroplane carrying United States Army Air Forces Major <PERSON> is lost in a flight over the English Channel.", "html": "1944 - World War II: a single-engine <a href=\"https://wikipedia.org/wiki/UC-64A_Norseman\" class=\"mw-redirect\" title=\"UC-64A Norseman\">UC-64A Norseman</a> aeroplane carrying <a href=\"https://wikipedia.org/wiki/United_States_Army_Air_Forces\" title=\"United States Army Air Forces\">United States Army Air Forces</a> Major <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is lost in a flight over the English Channel.", "no_year_html": "World War II: a single-engine <a href=\"https://wikipedia.org/wiki/UC-64A_Norseman\" class=\"mw-redirect\" title=\"UC-64A Norseman\">UC-64A Norseman</a> aeroplane carrying <a href=\"https://wikipedia.org/wiki/United_States_Army_Air_Forces\" title=\"United States Army Air Forces\">United States Army Air Forces</a> Major <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is lost in a flight over the English Channel.", "links": [{"title": "UC-64A Norseman", "link": "https://wikipedia.org/wiki/UC-64A_Norseman"}, {"title": "United States Army Air Forces", "link": "https://wikipedia.org/wiki/United_States_Army_Air_Forces"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "Occupation of Japan/Shinto Directive: General <PERSON> orders that Shinto be abolished as the state religion of Japan.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Occupation_of_Japan\" title=\"Occupation of Japan\">Occupation of Japan</a>/<a href=\"https://wikipedia.org/wiki/Shinto_Directive\" title=\"Shinto Directive\">Shinto Directive</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders that <a href=\"https://wikipedia.org/wiki/Shinto\" title=\"Shinto\">Shinto</a> be abolished as the state religion of Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Occupation_of_Japan\" title=\"Occupation of Japan\">Occupation of Japan</a>/<a href=\"https://wikipedia.org/wiki/Shinto_Directive\" title=\"Shinto Directive\">Shinto Directive</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders that <a href=\"https://wikipedia.org/wiki/Shinto\" title=\"Shinto\">Shinto</a> be abolished as the state religion of Japan.", "links": [{"title": "Occupation of Japan", "link": "https://wikipedia.org/wiki/Occupation_of_Japan"}, {"title": "Shinto Directive", "link": "https://wikipedia.org/wiki/Shinto_Directive"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shin<PERSON>", "link": "https://wikipedia.org/wiki/Shinto"}]}, {"year": "1960", "text": "<PERSON> is arrested for plotting to assassinate U.S. President-El<PERSON>t <PERSON>.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested for plotting to assassinate <a href=\"https://wikipedia.org/wiki/President-elect_of_the_United_States\" title=\"President-elect of the United States\">U.S. President-Elect</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested for plotting to assassinate <a href=\"https://wikipedia.org/wiki/President-elect_of_the_United_States\" title=\"President-elect of the United States\">U.S. President-El<PERSON>t</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President-elect of the United States", "link": "https://wikipedia.org/wiki/President-elect_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "King <PERSON><PERSON><PERSON> of Nepal suspends the country's constitution, dissolves parliament, dismisses the cabinet, and imposes direct rule.", "html": "1960 - King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> suspends the country's constitution, dissolves parliament, dismisses the cabinet, and imposes direct rule.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> suspends the country's constitution, dissolves parliament, dismisses the cabinet, and imposes direct rule.", "links": [{"title": "<PERSON><PERSON><PERSON> of Nepal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nepal"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON> trial: <PERSON> is sentenced to death after being found guilty by an Israeli court of 15 criminal charges, including charges of crimes against humanity, crimes against the Jewish people, and membership of an outlawed organization.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_trial\" title=\"<PERSON><PERSON><PERSON> trial\"><PERSON><PERSON><PERSON> trial</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to death after being found guilty by an <a href=\"https://wikipedia.org/wiki/Judicial_system_of_Israel\" class=\"mw-redirect\" title=\"Judicial system of Israel\">Israeli court</a> of 15 criminal charges, including charges of crimes against humanity, crimes against the Jewish people, and membership of an outlawed organization.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_trial\" title=\"<PERSON><PERSON><PERSON> trial\"><PERSON><PERSON><PERSON> trial</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to death after being found guilty by an <a href=\"https://wikipedia.org/wiki/Judicial_system_of_Israel\" class=\"mw-redirect\" title=\"Judicial system of Israel\">Israeli court</a> of 15 criminal charges, including charges of crimes against humanity, crimes against the Jewish people, and membership of an outlawed organization.", "links": [{"title": "<PERSON><PERSON><PERSON> trial", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_trial"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Judicial system of Israel", "link": "https://wikipedia.org/wiki/Judicial_system_of_Israel"}]}, {"year": "1965", "text": "Project Gemini: Gemini 6A, crewed by <PERSON> and <PERSON>, is launched from Cape Kennedy, Florida. Four orbits later, it achieves the first space rendezvous, with Gemini 7.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Project_Gemini\" title=\"Project Gemini\">Project Gemini</a>: <a href=\"https://wikipedia.org/wiki/Gemini_6A\" title=\"Gemini 6A\">Gemini 6A</a>, crewed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Thomas <PERSON> Stafford\"><PERSON></a>, is launched from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral\" title=\"Cape Canaveral\"><PERSON> Kennedy</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>. Four orbits later, it achieves the first <a href=\"https://wikipedia.org/wiki/Space_rendezvous\" title=\"Space rendezvous\">space rendezvous</a>, with <a href=\"https://wikipedia.org/wiki/Gemini_7\" title=\"Gemini 7\">Gemini 7</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Gemini\" title=\"Project Gemini\">Project Gemini</a>: <a href=\"https://wikipedia.org/wiki/Gemini_6A\" title=\"Gemini 6A\">Gemini 6A</a>, crewed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Thomas <PERSON> Stafford\"><PERSON></a>, is launched from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral\" title=\"Cape Canaveral\"><PERSON> Kennedy</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>. Four orbits later, it achieves the first <a href=\"https://wikipedia.org/wiki/Space_rendezvous\" title=\"Space rendezvous\">space rendezvous</a>, with <a href=\"https://wikipedia.org/wiki/Gemini_7\" title=\"Gemini 7\">Gemini 7</a>.", "links": [{"title": "Project Gemini", "link": "https://wikipedia.org/wiki/Project_Gemini"}, {"title": "Gemini 6A", "link": "https://wikipedia.org/wiki/Gemini_6A"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cape Canaveral", "link": "https://wikipedia.org/wiki/Cape_Canaveral"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Space rendezvous", "link": "https://wikipedia.org/wiki/Space_rendezvous"}, {"title": "Gemini 7", "link": "https://wikipedia.org/wiki/Gemini_7"}]}, {"year": "1970", "text": "Soviet spacecraft Venera 7 successfully lands on Venus. It is the first successful soft landing on another planet.", "html": "1970 - Soviet spacecraft <a href=\"https://wikipedia.org/wiki/Venera_7\" title=\"Venera 7\">Venera 7</a> successfully lands on <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>. It is the first successful soft <a href=\"https://wikipedia.org/wiki/List_of_landings_on_extraterrestrial_bodies\" title=\"List of landings on extraterrestrial bodies\">landing on another planet</a>.", "no_year_html": "Soviet spacecraft <a href=\"https://wikipedia.org/wiki/Venera_7\" title=\"Venera 7\">Venera 7</a> successfully lands on <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>. It is the first successful soft <a href=\"https://wikipedia.org/wiki/List_of_landings_on_extraterrestrial_bodies\" title=\"List of landings on extraterrestrial bodies\">landing on another planet</a>.", "links": [{"title": "Venera 7", "link": "https://wikipedia.org/wiki/Venera_7"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}, {"title": "List of landings on extraterrestrial bodies", "link": "https://wikipedia.org/wiki/List_of_landings_on_extraterrestrial_bodies"}]}, {"year": "1973", "text": "<PERSON>, grandson of American billionaire <PERSON><PERSON>, is found alive near Naples, Italy, after being kidnapped by an Italian gang on July 10.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, grandson of American billionaire <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is found alive near <a href=\"https://wikipedia.org/wiki/Naples\" title=\"Naples\">Naples</a>, Italy, after being kidnapped by an Italian gang on July 10.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, grandson of American billionaire <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is found alive near <a href=\"https://wikipedia.org/wiki/Naples\" title=\"Naples\">Naples</a>, Italy, after being kidnapped by an Italian gang on July 10.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Naples", "link": "https://wikipedia.org/wiki/Naples"}]}, {"year": "1973", "text": "The American Psychiatric Association votes 13-0 to remove homosexuality from its official list of psychiatric disorders, the Diagnostic and Statistical Manual of Mental Disorders.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/American_Psychiatric_Association\" title=\"American Psychiatric Association\">American Psychiatric Association</a> votes 13-0 to remove <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexuality</a> from its official list of <a href=\"https://wikipedia.org/wiki/Mental_disorder\" title=\"Mental disorder\">psychiatric disorders</a>, the <a href=\"https://wikipedia.org/wiki/Diagnostic_and_Statistical_Manual_of_Mental_Disorders\" title=\"Diagnostic and Statistical Manual of Mental Disorders\">Diagnostic and Statistical Manual of Mental Disorders</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/American_Psychiatric_Association\" title=\"American Psychiatric Association\">American Psychiatric Association</a> votes 13-0 to remove <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexuality</a> from its official list of <a href=\"https://wikipedia.org/wiki/Mental_disorder\" title=\"Mental disorder\">psychiatric disorders</a>, the <a href=\"https://wikipedia.org/wiki/Diagnostic_and_Statistical_Manual_of_Mental_Disorders\" title=\"Diagnostic and Statistical Manual of Mental Disorders\">Diagnostic and Statistical Manual of Mental Disorders</a>.", "links": [{"title": "American Psychiatric Association", "link": "https://wikipedia.org/wiki/American_Psychiatric_Association"}, {"title": "Homosexuality", "link": "https://wikipedia.org/wiki/Homosexuality"}, {"title": "Mental disorder", "link": "https://wikipedia.org/wiki/Mental_disorder"}, {"title": "Diagnostic and Statistical Manual of Mental Disorders", "link": "https://wikipedia.org/wiki/Diagnostic_and_Statistical_Manual_of_Mental_Disorders"}]}, {"year": "1978", "text": "U.S. President <PERSON> announces that the United States will recognize the People's Republic of China and sever diplomatic relations with the Republic of China (Taiwan).", "html": "1978 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that the United States will <a href=\"https://wikipedia.org/wiki/China%E2%80%93United_States_relations\" title=\"China-United States relations\">recognize the People's Republic of China</a> and <a href=\"https://wikipedia.org/wiki/Taiwan%E2%80%93United_States_relations\" title=\"Taiwan-United States relations\">sever diplomatic relations with the Republic of China (Taiwan)</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that the United States will <a href=\"https://wikipedia.org/wiki/China%E2%80%93United_States_relations\" title=\"China-United States relations\">recognize the People's Republic of China</a> and <a href=\"https://wikipedia.org/wiki/Taiwan%E2%80%93United_States_relations\" title=\"Taiwan-United States relations\">sever diplomatic relations with the Republic of China (Taiwan)</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "China-United States relations", "link": "https://wikipedia.org/wiki/China%E2%80%93United_States_relations"}, {"title": "Taiwan-United States relations", "link": "https://wikipedia.org/wiki/Taiwan%E2%80%93United_States_relations"}]}, {"year": "1981", "text": "A suicide car bombing targeting the Iraqi embassy in Beirut, Lebanon, levels the embassy and kills 61 people, including Iraq's ambassador to Lebanon. The attack is considered the first modern suicide bombing.", "html": "1981 - A <a href=\"https://wikipedia.org/wiki/1981_Iraqi_embassy_bombing\" class=\"mw-redirect\" title=\"1981 Iraqi embassy bombing\">suicide car bombing targeting the Iraqi embassy</a> in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut, Lebanon</a>, levels the embassy and kills 61 people, including Iraq's ambassador to Lebanon. The attack is considered the first modern <a href=\"https://wikipedia.org/wiki/Suicide_attack\" title=\"Suicide attack\">suicide bombing</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1981_Iraqi_embassy_bombing\" class=\"mw-redirect\" title=\"1981 Iraqi embassy bombing\">suicide car bombing targeting the Iraqi embassy</a> in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut, Lebanon</a>, levels the embassy and kills 61 people, including Iraq's ambassador to Lebanon. The attack is considered the first modern <a href=\"https://wikipedia.org/wiki/Suicide_attack\" title=\"Suicide attack\">suicide bombing</a>.", "links": [{"title": "1981 Iraqi embassy bombing", "link": "https://wikipedia.org/wiki/1981_Iraqi_embassy_bombing"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}, {"title": "Suicide attack", "link": "https://wikipedia.org/wiki/Suicide_attack"}]}, {"year": "1989", "text": "Second Optional Protocol to the International Covenant on Civil and Political Rights relating the abolition of capital punishment is adopted.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Second_Optional_Protocol_to_the_International_Covenant_on_Civil_and_Political_Rights\" title=\"Second Optional Protocol to the International Covenant on Civil and Political Rights\">Second Optional Protocol to the International Covenant on Civil and Political Rights</a> relating the <a href=\"https://wikipedia.org/wiki/Abolition_of_capital_punishment\" class=\"mw-redirect\" title=\"Abolition of capital punishment\">abolition of capital punishment</a> is adopted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Optional_Protocol_to_the_International_Covenant_on_Civil_and_Political_Rights\" title=\"Second Optional Protocol to the International Covenant on Civil and Political Rights\">Second Optional Protocol to the International Covenant on Civil and Political Rights</a> relating the <a href=\"https://wikipedia.org/wiki/Abolition_of_capital_punishment\" class=\"mw-redirect\" title=\"Abolition of capital punishment\">abolition of capital punishment</a> is adopted.", "links": [{"title": "Second Optional Protocol to the International Covenant on Civil and Political Rights", "link": "https://wikipedia.org/wiki/Second_Optional_Protocol_to_the_International_Covenant_on_Civil_and_Political_Rights"}, {"title": "Abolition of capital punishment", "link": "https://wikipedia.org/wiki/Abolition_of_capital_punishment"}]}, {"year": "1993", "text": "The Troubles: The Downing Street Declaration is issued by British Prime Minister <PERSON> and Irish Taoiseach <PERSON>.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Downing_Street_Declaration\" title=\"Downing Street Declaration\">Downing Street Declaration</a> is issued by British <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/John_<PERSON>\" title=\"John <PERSON>\"><PERSON></a> and Irish <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Downing_Street_Declaration\" title=\"Downing Street Declaration\">Downing Street Declaration</a> is issued by British <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/John_<PERSON>\" title=\"John <PERSON>\"><PERSON></a> and Irish <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Downing Street Declaration", "link": "https://wikipedia.org/wiki/Downing_Street_Declaration"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "Tajikistan Airlines Flight 3183 crashes in the desert near Sharjah, United Arab Emirates, killing 85.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Tajikistan_Airlines_Flight_3183\" title=\"Tajikistan Airlines Flight 3183\">Tajikistan Airlines Flight 3183</a> crashes in the desert near <a href=\"https://wikipedia.org/wiki/Emirate_of_Sharjah\" title=\"Emirate of Sharjah\">Sharjah</a>, <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a>, killing 85.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tajikistan_Airlines_Flight_3183\" title=\"Tajikistan Airlines Flight 3183\">Tajikistan Airlines Flight 3183</a> crashes in the desert near <a href=\"https://wikipedia.org/wiki/Emirate_of_Sharjah\" title=\"Emirate of Sharjah\">Sharjah</a>, <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a>, killing 85.", "links": [{"title": "Tajikistan Airlines Flight 3183", "link": "https://wikipedia.org/wiki/Tajikistan_Airlines_Flight_3183"}, {"title": "Emirate of Sharjah", "link": "https://wikipedia.org/wiki/Emirate_of_Sharjah"}, {"title": "United Arab Emirates", "link": "https://wikipedia.org/wiki/United_Arab_Emirates"}]}, {"year": "2000", "text": "The third reactor at the Chernobyl Nuclear Power Plant is shut down.", "html": "2000 - The third reactor at the <a href=\"https://wikipedia.org/wiki/Chernobyl_Nuclear_Power_Plant\" title=\"Chernobyl Nuclear Power Plant\">Chernobyl Nuclear Power Plant</a> is shut down.", "no_year_html": "The third reactor at the <a href=\"https://wikipedia.org/wiki/Chernobyl_Nuclear_Power_Plant\" title=\"Chernobyl Nuclear Power Plant\">Chernobyl Nuclear Power Plant</a> is shut down.", "links": [{"title": "Chernobyl Nuclear Power Plant", "link": "https://wikipedia.org/wiki/Chernobyl_Nuclear_Power_Plant"}]}, {"year": "2001", "text": "The Leaning Tower of Pisa reopens after 11 years and $27,000,000 spent to stabilize it, without fixing its famous lean.", "html": "2001 - The <a href=\"https://wikipedia.org/wiki/Leaning_Tower_of_Pisa\" title=\"Leaning Tower of Pisa\">Leaning Tower of Pisa</a> reopens after 11 years and $27,000,000 spent to stabilize it, without fixing its famous lean.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Leaning_Tower_of_Pisa\" title=\"Leaning Tower of Pisa\">Leaning Tower of Pisa</a> reopens after 11 years and $27,000,000 spent to stabilize it, without fixing its famous lean.", "links": [{"title": "Leaning Tower of Pisa", "link": "https://wikipedia.org/wiki/Leaning_Tower_of_Pisa"}]}, {"year": "2005", "text": "Introduction of the Lockheed Martin F-22 Raptor into USAF active service.", "html": "2005 - Introduction of the <a href=\"https://wikipedia.org/wiki/Lockheed_Martin_F-22_Raptor\" title=\"Lockheed Martin F-22 Raptor\">Lockheed Martin F-22 Raptor</a> into <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">USAF</a> active service.", "no_year_html": "Introduction of the <a href=\"https://wikipedia.org/wiki/Lockheed_Martin_F-22_Raptor\" title=\"Lockheed Martin F-22 Raptor\">Lockheed Martin F-22 Raptor</a> into <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">USAF</a> active service.", "links": [{"title": "Lockheed Martin F-22 Raptor", "link": "https://wikipedia.org/wiki/Lockheed_Martin_F-22_Raptor"}, {"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}]}, {"year": "2010", "text": "A boat carrying 90 asylum seekers crashes into rocks off the coast of Christmas Island, Australia, killing 48 people.", "html": "2010 - A boat carrying 90 <a href=\"https://wikipedia.org/wiki/Refugee#Asylum_seekers\" title=\"Refugee\">asylum seekers</a> <a href=\"https://wikipedia.org/wiki/2010_Christmas_Island_boat_disaster\" title=\"2010 Christmas Island boat disaster\">crashes into rocks</a> off the coast of <a href=\"https://wikipedia.org/wiki/Christmas_Island\" title=\"Christmas Island\">Christmas Island</a>, Australia, killing 48 people.", "no_year_html": "A boat carrying 90 <a href=\"https://wikipedia.org/wiki/Refugee#Asylum_seekers\" title=\"Refugee\">asylum seekers</a> <a href=\"https://wikipedia.org/wiki/2010_Christmas_Island_boat_disaster\" title=\"2010 Christmas Island boat disaster\">crashes into rocks</a> off the coast of <a href=\"https://wikipedia.org/wiki/Christmas_Island\" title=\"Christmas Island\">Christmas Island</a>, Australia, killing 48 people.", "links": [{"title": "Refugee", "link": "https://wikipedia.org/wiki/Refugee#Asylum_seekers"}, {"title": "2010 Christmas Island boat disaster", "link": "https://wikipedia.org/wiki/2010_Christmas_Island_boat_disaster"}, {"title": "Christmas Island", "link": "https://wikipedia.org/wiki/Christmas_Island"}]}, {"year": "2013", "text": "The South Sudanese Civil War begins when opposition leaders Dr. <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> vote to boycott the meeting of the National Liberation Council at Nyakuron.", "html": "2013 - The <a href=\"https://wikipedia.org/wiki/South_Sudanese_Civil_War\" title=\"South Sudanese Civil War\">South Sudanese Civil War</a> begins when opposition leaders Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Pagan_Amum\" title=\"Pagan Amum\"><PERSON>gan Am<PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>den<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> vote to boycott the meeting of the National Liberation Council at Nyakuron.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/South_Sudanese_Civil_War\" title=\"South Sudanese Civil War\">South Sudanese Civil War</a> begins when opposition leaders Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Pagan_Amum\" title=\"Pagan Amum\">Pagan Am<PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> vote to boycott the meeting of the National Liberation Council at Nyakuron.", "links": [{"title": "South Sudanese Civil War", "link": "https://wikipedia.org/wiki/South_Sudanese_Civil_War"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>um"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "Gunman Man <PERSON> takes 18 hostages inside a café in Martin Place for 16 hours in Sydney. <PERSON><PERSON> and two hostages are killed when police raid the café the following morning.", "html": "2014 - Gunman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Man <PERSON>\">Man <PERSON><PERSON></a> takes <a href=\"https://wikipedia.org/wiki/2014_Sydney_hostage_crisis\" class=\"mw-redirect\" title=\"2014 Sydney hostage crisis\">18 hostages</a> inside a café in <a href=\"https://wikipedia.org/wiki/Martin_Place,_Sydney\" class=\"mw-redirect\" title=\"Martin Place, Sydney\"><PERSON></a> for 16 hours in Sydney. <PERSON><PERSON> and two hostages are killed when police raid the café the following morning.", "no_year_html": "Gunman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Man <PERSON></a> takes <a href=\"https://wikipedia.org/wiki/2014_Sydney_hostage_crisis\" class=\"mw-redirect\" title=\"2014 Sydney hostage crisis\">18 hostages</a> inside a café in <a href=\"https://wikipedia.org/wiki/Martin_Place,_Sydney\" class=\"mw-redirect\" title=\"Martin Place, Sydney\">Martin <PERSON></a> for 16 hours in Sydney. <PERSON><PERSON> and two hostages are killed when police raid the café the following morning.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "2014 Sydney hostage crisis", "link": "https://wikipedia.org/wiki/2014_Sydney_hostage_crisis"}, {"title": "Martin Place, Sydney", "link": "https://wikipedia.org/wiki/Martin_Place,_Sydney"}]}, {"year": "2017", "text": "A 6.5Mw earthquake strikes the Indonesian island of Java in the city of Tasikmalaya, resulting in four deaths.", "html": "2017 - A 6.5M<sub>w</sub> <a href=\"https://wikipedia.org/wiki/2017_Java_earthquake\" title=\"2017 Java earthquake\">earthquake</a> strikes the Indonesian island of <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a> in the city of <a href=\"https://wikipedia.org/wiki/Tasikmalaya\" title=\"Tasikmalaya\">Tasikmalaya</a>, resulting in four deaths.", "no_year_html": "A 6.5M<sub>w</sub> <a href=\"https://wikipedia.org/wiki/2017_Java_earthquake\" title=\"2017 Java earthquake\">earthquake</a> strikes the Indonesian island of <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a> in the city of <a href=\"https://wikipedia.org/wiki/Tasikmalaya\" title=\"Tasikmalaya\">Tasikmalaya</a>, resulting in four deaths.", "links": [{"title": "2017 Java earthquake", "link": "https://wikipedia.org/wiki/2017_Java_earthquake"}, {"title": "Java", "link": "https://wikipedia.org/wiki/Java"}, {"title": "Tasikmalaya", "link": "https://wikipedia.org/wiki/Tasikmalaya"}]}], "Births": [{"year": "37", "text": "<PERSON>, Roman emperor (d. 68)", "html": "37 - AD 37 - <a href=\"https://wikipedia.org/wiki/Nero\" title=\"Nero\"><PERSON></a>, Roman emperor (d. 68)", "no_year_html": "AD 37 - <a href=\"https://wikipedia.org/wiki/Nero\" title=\"Nero\"><PERSON></a>, Roman emperor (d. 68)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nero"}]}, {"year": "130", "text": "<PERSON>, Roman emperor (d. 169)", "html": "130 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> V<PERSON>\"><PERSON></a>, Roman emperor (d. 169)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Verus\"><PERSON></a>, Roman emperor (d. 169)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>erus"}]}, {"year": "1242", "text": "<PERSON>, Japanese shōgun (d. 1274)", "html": "1242 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/List_of_sh%C5%8Dguns\" class=\"mw-redirect\" title=\"List of shōguns\">shōgun</a> (d. 1274)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/List_of_sh%C5%8Dguns\" class=\"mw-redirect\" title=\"List of shōguns\">shōgun</a> (d. 1274)", "links": [{"title": "Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of shōguns", "link": "https://wikipedia.org/wiki/List_of_sh%C5%8Dguns"}]}, {"year": "1447", "text": "<PERSON>, Duke of Bavaria (d. 1508)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1508)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1567", "text": "<PERSON>, German composer, poet, and theorist (d. 1643)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, poet, and theorist (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, poet, and theorist (d. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1610", "text": "<PERSON> the Younger, Flemish painter (d. 1690)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Flemish painter (d. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Flemish painter (d. 1690)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Younger"}]}, {"year": "1657", "text": "<PERSON>, French organist and composer (d. 1726)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1726)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON><PERSON><PERSON>, Flemish violinist and composer (d. 1746)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish violinist and composer (d. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish violinist and composer (d. 1746)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, Maltese painter (d. 1773)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese painter (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese painter (d. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, Venezuelan general and politician, 11th President of Venezuela (d. 1870)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "1832", "text": "<PERSON><PERSON>, French architect and engineer, co-designed the Eiffel Tower (d. 1923)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French architect and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Eiffel_Tower\" title=\"Eiffel Tower\">Eiffel Tower</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French architect and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Eiffel_Tower\" title=\"Eiffel Tower\">Eiffel Tower</a> (d. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Eiffel Tower", "link": "https://wikipedia.org/wiki/Eiffel_Tower"}]}, {"year": "1837", "text": "<PERSON><PERSON> <PERSON><PERSON>, English minister, scholar, and theologian (d. 1913)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English minister, scholar, and theologian (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English minister, scholar, and theologian (d. 1913)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON><PERSON>, Norwegian opera singer (d. 1882)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ko<PERSON>\"><PERSON><PERSON></a>, Norwegian opera singer (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kolderup\"><PERSON><PERSON></a>, Norwegian opera singer (d. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>up"}]}, {"year": "1852", "text": "<PERSON>, French physicist and chemist, Nobel Prize laureate (d. 1908)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1859", "text": "<PERSON><PERSON> <PERSON><PERSON>, Polish linguist and ophthalmologist, created <PERSON><PERSON><PERSON><PERSON> (d. 1917)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Polish linguist and ophthalmologist, created <a href=\"https://wikipedia.org/wiki/Esperanto\" title=\"Esperanto\">Esperanto</a> (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"L. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Polish linguist and ophthalmologist, created <a href=\"https://wikipedia.org/wiki/Esperanto\" title=\"Esperanto\">Esperanto</a> (d. 1917)", "links": [{"title": "L. L. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Esperanto", "link": "https://wikipedia.org/wiki/Esperanto"}]}, {"year": "1860", "text": "<PERSON><PERSON>, Faroese-Danish physician and educator, Nobel Prize laureate (d. 1904)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese-Danish physician and educator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese-Danish physician and educator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1860", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1953)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American engineer and businessman, co-founded the Duryea Motor Wagon Company (d. 1938)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Duryea_Motor_Wagon_Company\" title=\"Duryea Motor Wagon Company\">Duryea Motor Wagon Company</a> (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Duryea_Motor_Wagon_Company\" title=\"Duryea Motor Wagon Company\">Duryea Motor Wagon Company</a> (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Duryea Motor Wagon Company", "link": "https://wikipedia.org/wiki/Duryea_Motor_Wagon_Company"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON>, Finnish lawyer, judge, and politician, 3rd President of Finland (d. 1944)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>hufvud\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish lawyer, judge, and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>fvud\" title=\"<PERSON><PERSON><PERSON>vu<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish lawyer, judge, and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>d"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1863", "text": "<PERSON>, American chemist and engineer (d. 1935)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, Polish chemist and academic (d. 1946)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish chemist and academic (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish chemist and academic (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Filipino journalist and activist (d. 1899)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and activist (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and activist (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, German author and poet (d. 1956)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Slovenian lawyer, philosopher, and academic (d. 1971)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian lawyer, philosopher, and academic (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian lawyer, philosopher, and academic (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>, Polish politician and resistance fighter (d. 1968)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish politician and resistance fighter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish politician and resistance fighter (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American contralto singer and professor of music (d. 1977)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Contralto\" title=\"Contral<PERSON>\">contralto</a> singer and professor of music (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Contralto\" title=\"Contral<PERSON>\">contralto</a> singer and professor of music (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Contralto"}]}, {"year": "1888", "text": "<PERSON>, American journalist and playwright (d. 1959)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and playwright (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and playwright (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American pole vaulter (d. 1965)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pole_vaulter)\" title=\"<PERSON> (pole vaulter)\"><PERSON></a>, American pole vaulter (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pole_vaulter)\" title=\"<PERSON> (pole vaulter)\"><PERSON></a>, American pole vaulter (d. 1965)", "links": [{"title": "<PERSON> (pole vaulter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pole_vaulter)"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON><PERSON>, American country singer-songwriter and musician (d. 1960)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American country singer-songwriter and musician (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American country singer-songwriter and musician (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, American-English businessman and art collector, founded Getty Oil (d. 1976)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-English businessman and art collector, founded <a href=\"https://wikipedia.org/wiki/Getty_Oil\" title=\"Getty Oil\">Getty Oil</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-English businessman and art collector, founded <a href=\"https://wikipedia.org/wiki/Getty_Oil\" title=\"Getty Oil\">Getty Oil</a> (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Getty Oil", "link": "https://wikipedia.org/wiki/Getty_Oil"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Canadian astrophysicist and astronomer (d. 1988)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian astrophysicist and astronomer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian astrophysicist and astronomer (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Swiss sprinter (d. 1964)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Swiss sprinter (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Swiss sprinter (d. 1964)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1896", "text": "<PERSON>, American author and playwright (d. 1972)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, English sprinter, lawyer, and journalist (d. 1978)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter, lawyer, and journalist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter, lawyer, and journalist (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American lawyer and politician, 57th Governor of Massachusetts (d. 1983)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 57th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 57th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 32nd <PERSON><PERSON><PERSON>na (d. 1938)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27emon\" title=\"<PERSON><PERSON><PERSON><PERSON> San'emon\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 32nd <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27emon\" title=\"<PERSON><PERSON><PERSON><PERSON> San'emon\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 32nd <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> San'emon", "link": "https://wikipedia.org/wiki/Tamanishiki_San%27emon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1907", "text": "<PERSON>, American actor, director, and screenwriter (d. 1993)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American actor, director, and screenwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American actor, director, and screenwriter (d. 1993)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(director)"}]}, {"year": "1907", "text": "<PERSON>, Brazilian architect, designed the United Nations Headquarters and the Cathedral of Brasília (d. 2012)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian architect, designed the <a href=\"https://wikipedia.org/wiki/United_Nations_Headquarters\" class=\"mw-redirect\" title=\"United Nations Headquarters\">United Nations Headquarters</a> and the <a href=\"https://wikipedia.org/wiki/Cathedral_of_Bras%C3%ADlia\" title=\"Cathedral of Brasília\">Cathedral of Brasília</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian architect, designed the <a href=\"https://wikipedia.org/wiki/United_Nations_Headquarters\" class=\"mw-redirect\" title=\"United Nations Headquarters\">United Nations Headquarters</a> and the <a href=\"https://wikipedia.org/wiki/Cathedral_of_Bras%C3%ADlia\" title=\"Cathedral of Brasília\">Cathedral of Brasília</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Nations Headquarters", "link": "https://wikipedia.org/wiki/United_Nations_Headquarters"}, {"title": "Cathedral of Brasília", "link": "https://wikipedia.org/wiki/Cathedral_of_Bras%C3%ADlia"}]}, {"year": "1908", "text": "Swami <PERSON>, Indian monk, scholar, and author (d. 2005)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Swami_<PERSON>\" class=\"mw-redirect\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian monk, scholar, and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swami_<PERSON>\" class=\"mw-redirect\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian monk, scholar, and author (d. 2005)", "links": [{"title": "Swami <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Azerbaijani-Russian painter (d. 1974)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani-Russian painter (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani-Russian painter (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American librarian (d. 2009)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American record producer and critic (d. 1987)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" class=\"mw-redirect\" title=\"<PERSON> (producer)\"><PERSON></a>, American record producer and critic (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(producer)\" class=\"mw-redirect\" title=\"<PERSON> (producer)\"><PERSON></a>, American record producer and critic (d. 1987)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_(producer)"}]}, {"year": "1911", "text": "<PERSON>, American psychiatrist and illustrator (d. 1991)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and illustrator (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and illustrator (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American pianist and composer (d. 1979)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Canadian chemist and businessman (d. 2001)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian chemist and businessman (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian chemist and businessman (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American poet, academic, and activist (d. 1980)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet, academic, and activist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet, academic, and activist (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, English geographer and cartographer (d. 1994)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English geographer and cartographer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English geographer and cartographer (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Brazilian lawyer and politician, Governor of Pernambuco (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Pernambuco\" title=\"Governor of Pernambuco\">Governor of Pernambuco</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Pernambuco\" title=\"Governor of Pernambuco\">Governor of Pernambuco</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Pernambuco", "link": "https://wikipedia.org/wiki/Governor_of_Pernambuco"}]}, {"year": "1916", "text": "<PERSON>, American pianist and conductor (d. 1964)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and conductor (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and conductor (d. 1964)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1916", "text": "<PERSON>, New Zealand-English physicist and biologist, Nobel Prize laureate (d. 2004)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English physicist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English physicist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian-Pakistani linguist and lexicographer (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani linguist and lexicographer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani linguist and lexicographer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actor (d. 1961)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1961)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Japanese painter and illustrator (d. 1974)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese painter and illustrator (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese painter and illustrator (d. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American dairy farmer and host of the Woodstock Music & Art Fair (d. 1973)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ya<PERSON>gu<PERSON>\"><PERSON></a>, American dairy farmer and host of the <a href=\"https://wikipedia.org/wiki/Woodstock\" title=\"Woodstock\">Woodstock Music &amp; Art Fair</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ya<PERSON>gur\"><PERSON></a>, American dairy farmer and host of the <a href=\"https://wikipedia.org/wiki/Woodstock\" title=\"Woodstock\">Woodstock Music &amp; Art Fair</a> (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Woodstock", "link": "https://wikipedia.org/wiki/Woodstock"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Egyptian author and scholar (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_al-Banna\" title=\"Gamal al-Banna\"><PERSON><PERSON><PERSON></a>, Egyptian author and scholar (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_al-Banna\" title=\"G<PERSON><PERSON> al-Banna\"><PERSON><PERSON><PERSON></a>, Egyptian author and scholar (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON> al<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al-<PERSON>na"}]}, {"year": "1920", "text": "<PERSON>, German-American sergeant and illustrator (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sergeant and illustrator (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sergeant and illustrator (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American radio host (d. 1965)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Freed"}]}, {"year": "1923", "text": "<PERSON>, American producer and manager (d. 2009)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and manager (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and manager (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English-American physicist and mathematician (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and mathematician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and mathematician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, German-Israeli engineer, designed the Uzi gun (d. 2002)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Uziel_Gal\" title=\"Uziel Gal\"><PERSON><PERSON><PERSON></a>, German-Israeli engineer, designed the <a href=\"https://wikipedia.org/wiki/Uzi\" title=\"<PERSON>zi\"><PERSON><PERSON> gun</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uziel_Gal\" title=\"Uziel Gal\"><PERSON><PERSON><PERSON></a>, German-Israeli engineer, designed the <a href=\"https://wikipedia.org/wiki/Uzi\" title=\"<PERSON>zi\"><PERSON><PERSON> gun</a> (d. 2002)", "links": [{"title": "Uziel Gal", "link": "https://wikipedia.org/wiki/U<PERSON>l_Gal"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uzi"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Russian general and politician (d. 2009)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and politician (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English-American mathematician and academic (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American mathematician and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American mathematician and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Turkish triple jumper and educator (d. 2001)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Ruhi_Sar%C4%B1alp\" title=\"<PERSON><PERSON><PERSON>rıal<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish triple jumper and educator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ruhi_Sar%C4%B1alp\" title=\"<PERSON>uh<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish triple jumper and educator (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ruhi_Sar%C4%B1alp"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American actress and author (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and author (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Australian race car driver (d. 2017) ", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver (d. 2017) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver (d. 2017) ", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1928", "text": "<PERSON>, American singer-songwriter (d. 2009)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Polish-English violinist and educator (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English violinist and educator (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English violinist and educator (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Austrian-New Zealand painter and architect (d. 2000)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>ied<PERSON><PERSON><PERSON>_<PERSON>wasser\" title=\"Friedens<PERSON><PERSON> Hundertwasser\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Austrian-New Zealand painter and architect (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ied<PERSON><PERSON><PERSON>_<PERSON>wasser\" title=\"Friedens<PERSON><PERSON> Hundertwasser\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Austrian-New Zealand painter and architect (d. 2000)", "links": [{"title": "Friedensreich <PERSON>was<PERSON>", "link": "https://wikipedia.org/wiki/Friedensreich_<PERSON>wasser"}]}, {"year": "1930", "text": "<PERSON>, Irish novelist, playwright, poet and short story writer (d. 2024)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, playwright, poet and short story writer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, playwright, poet and short story writer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edna_O%27Brien"}]}, {"year": "1931", "text": "<PERSON>, Danish author and poet (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and poet (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and poet (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American singer-songwriter and pianist (d. 1960)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Welsh chemist and academic (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh chemist and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh chemist and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Indian director and screenwriter (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(director)\" title=\"<PERSON><PERSON> (director)\"><PERSON><PERSON></a>, Indian director and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(director)\" title=\"<PERSON><PERSON> (director)\"><PERSON><PERSON></a>, Indian director and screenwriter (d. 2014)", "links": [{"title": "<PERSON><PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(director)"}]}, {"year": "1933", "text": "<PERSON>, American comedian, actor, producer, and screenwriter (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, South African journalist and activist (d. 2001)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African journalist and activist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African journalist and activist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Italian director and producer (d. 1999)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Joe_D%27Amato\" title=\"<PERSON>\"><PERSON></a>, Italian director and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joe_D%27Amato\" title=\"<PERSON>\"><PERSON></a>, Italian director and producer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joe_D%27Amato"}]}, {"year": "1938", "text": "<PERSON>, Welsh director and screenwriter (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh director and screenwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh director and screenwriter (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American football player (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English musician and songwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1940", "text": "<PERSON>, American football player and sportscaster (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American educator and politician, 54th Governor of Louisiana (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Louisiana", "link": "https://wikipedia.org/wiki/Governor_of_Louisiana"}]}, {"year": "1943", "text": "<PERSON>, Dutch sculptor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American baseball player and manager", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Brazilian trade union leader and activist (d. 1988)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian trade union leader and activist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian trade union leader and activist (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Mendes"}]}, {"year": "1945", "text": "<PERSON>, American civil rights activist, feminist, and political strategist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist, feminist, and political strategist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist, feminist, and political strategist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, English political scientist and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English political scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English political scientist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American drummer and songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_App<PERSON>\" title=\"Carmine Appice\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Appice\" title=\"Carmine Appice\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "Carmine <PERSON>", "link": "https://wikipedia.org/wiki/Carmine_Appice"}]}, {"year": "1946", "text": "<PERSON>, American baseball player and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Howe"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American writer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lim"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer (<PERSON>, Cagliari, national team) (d. 2024)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer (<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_1903\" title=\"SE<PERSON> Torres 1903\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cagliari_Calcio\" title=\"Cagliari Calcio\">Cagliari</a>, <a href=\"https://wikipedia.org/wiki/Italy_national_football_team\" title=\"Italy national football team\">national team</a>) (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer (<a href=\"https://wikipedia.org/wiki/SE<PERSON>_<PERSON>_1903\" title=\"SEF Torres 1903\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cagliari_Calcio\" title=\"Cagliari Calcio\">Cagliari</a>, <a href=\"https://wikipedia.org/wiki/Italy_national_football_team\" title=\"Italy national football team\">national team</a>) (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "SEF Torres 1903", "link": "https://wikipedia.org/wiki/SE<PERSON>_<PERSON>_1903"}, {"title": "Cagliari <PERSON>", "link": "https://wikipedia.org/wiki/Cagliari_Calcio"}, {"title": "Italy national football team", "link": "https://wikipedia.org/wiki/Italy_national_football_team"}]}, {"year": "1948", "text": "<PERSON>, Australian actress (d. 1991)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American basketball player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English economist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)\" class=\"mw-redirect\" title=\"<PERSON> (academic)\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)\" class=\"mw-redirect\" title=\"<PERSON> (academic)\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON> (academic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)"}]}, {"year": "1950", "text": "<PERSON>, American actress and comedian", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>off\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chartoff\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>off"}]}, {"year": "1950", "text": "<PERSON>, American theoretical physicist and professor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theoretical physicist and professor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theoretical physicist and professor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian journalist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish footballer and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joe Jordan\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian journalist and sportscaster", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>trudi\" title=\"<PERSON><PERSON>udi\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Protrudi\" title=\"<PERSON><PERSON>trudi\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rudi_Protrudi"}]}, {"year": "1952", "text": "<PERSON>, Danish footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American general and diplomat", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>atteis\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>atteis\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>._DeMatteis"}]}, {"year": "1953", "text": "<PERSON>, American-Canadian author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English film director, screenwriter, actor, non-fiction author and broadcaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English film director, screenwriter, actor, non-fiction author and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English film director, screenwriter, actor, non-fiction author and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English lawyer and politician, Solicitor General for England and Wales", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales\" title=\"Solicitor General for England and Wales\">Solicitor General for England and Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales\" title=\"Solicitor General for England and Wales\">Solicitor General for England and Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Solicitor General for England and Wales", "link": "https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales"}]}, {"year": "1954", "text": "<PERSON>, American businessman and politician, 69th Governor of Virginia", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 69th <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 69th <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Virginia", "link": "https://wikipedia.org/wiki/Governor_of_Virginia"}]}, {"year": "1955", "text": "<PERSON>, English banker", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English singer-songwriter and bass player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American screenwriter, film director, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, film director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, film director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, South African lawyer and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American journalist and author (d. 1998)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German-American singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Filipino director and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American classical pianist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American classical pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American classical pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian cricketer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, New Zealand rugby player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1959", "text": "<PERSON>, New Zealand rugby player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Austrian composer and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Austrian journalist and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American bass player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English rugby player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American screenwriter and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American basketball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1964", "text": "<PERSON>, English actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Guyanese cricketer and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English footballer and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Canadian wheelchair racer and senator", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wheelchair racer and senator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wheelchair racer and senator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American discus thrower and lawyer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Italian jockey", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian jockey", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1970", "text": "<PERSON>, American basketball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>erburke\" title=\"<PERSON> Funderburke\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>erburke\" title=\"<PERSON> Funderburke\"><PERSON></a>, American basketball player", "links": [{"title": "Lawrence Funderburke", "link": "https://wikipedia.org/wiki/Lawrence_Funderburke"}]}, {"year": "1970", "text": "<PERSON>, Canadian actor, screenwriter and director", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, screenwriter and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, screenwriter and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, South Korean actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ae"}]}, {"year": "1972", "text": "<PERSON>, Irish actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress, director, writer and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, writer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, writer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, French figure skater", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bonaly\"><PERSON><PERSON></a>, French figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aly\" title=\"<PERSON><PERSON>aly\"><PERSON><PERSON></a>, French figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sur<PERSON>_Bonaly"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, South Korean actor, director, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Ryo<PERSON>_<PERSON><PERSON>-wan\" title=\"<PERSON>yo<PERSON> <PERSON>-wan\"><PERSON><PERSON><PERSON>-wan</a>, South Korean actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ryo<PERSON>_<PERSON><PERSON>-wan\" title=\"<PERSON>yo<PERSON> <PERSON>-wan\"><PERSON><PERSON><PERSON>-wan</a>, South Korean actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>wan", "link": "https://wikipedia.org/wiki/Ryo<PERSON>_<PERSON><PERSON>-wan"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, English rugby player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>,  Palestinian actor, filmmaker, poet and rapper", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian actor, filmmaker, poet and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian actor, filmmaker, poet and rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Indian footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Baichung_Bhutia\" class=\"mw-redirect\" title=\"Baichung Bhutia\"><PERSON><PERSON><PERSON> Bhutia</a>, Indian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baichung_Bhutia\" class=\"mw-redirect\" title=\"Baichung Bhutia\"><PERSON><PERSON><PERSON> Bhutia</a>, Indian footballer and manager", "links": [{"title": "Baichung Bhutia", "link": "https://wikipedia.org/wiki/Baichung_Bhutia"}]}, {"year": "1976", "text": "<PERSON>, Canadian sport shooter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Kim_Eagles\" title=\"Kim Eagles\"><PERSON></a>, Canadian sport shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kim_Eagles\" title=\"Kim Eagles\"><PERSON></a>, Canadian sport shooter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kim_Eagles"}]}, {"year": "1976", "text": "<PERSON>, American baseball player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player and umpire", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Brazilian-Turkish footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_Aur%C3%A9lio\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian-Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_<PERSON>r%C3%A9lio\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian-Turkish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mehmet_Aur%C3%A9lio"}]}, {"year": "1977", "text": "<PERSON>, American actor and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American drummer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1978", "text": "<PERSON>, Dutch guitarist and songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brody\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brody\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian-American wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian-American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian-American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>(wrestler)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, French beauty pageant titleholder and model", "html": "1980 - <a href=\"https://wikipedia.org/wiki/%C3%89lodie_Gossuin\" title=\"<PERSON><PERSON><PERSON> Go<PERSON>\"><PERSON><PERSON><PERSON></a>, French beauty pageant titleholder and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lo<PERSON>_Gossuin\" title=\"<PERSON><PERSON><PERSON> Gossuin\"><PERSON><PERSON><PERSON></a>, French beauty pageant titleholder and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lodie_Go<PERSON>uin"}]}, {"year": "1980", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian actor and screenwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Puerto Rican-American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican-American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nz%C3%A1lez_(baseball)"}]}, {"year": "1981", "text": "<PERSON>, American football player (d. 2005)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Russian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Spanish race car driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Borja_Garc%C3%<PERSON><PERSON>_(racing_driver)\" title=\"<PERSON><PERSON><PERSON> (racing driver)\"><PERSON><PERSON><PERSON></a>, Spanish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Garc%C3%<PERSON><PERSON>_(racing_driver)\" title=\"<PERSON><PERSON><PERSON> (racing driver)\"><PERSON><PERSON><PERSON></a>, Spanish race car driver", "links": [{"title": "<PERSON><PERSON><PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/Borja_Garc%C3%AD<PERSON>_(racing_driver)"}]}, {"year": "1982", "text": "<PERSON>, American actor and comedian", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Gore_II\" title=\"<PERSON> II\"><PERSON> II</a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON> II</a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Ukrainian tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Trinidadian-English rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Delon_Armitage\" title=\"Delon Armitage\"><PERSON><PERSON> Armitage</a>, Trinidadian-English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Delon_Armitage\" title=\"Delon Armitage\"><PERSON><PERSON> Armitage</a>, Trinidadian-English rugby player", "links": [{"title": "Delon Armitage", "link": "https://wikipedia.org/wiki/Delon_Armitage"}]}, {"year": "1983", "text": "<PERSON>, Canadian professional wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Dupr%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, Canadian professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Dupr%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, Canadian professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Dupr%C3%A9e"}]}, {"year": "1983", "text": "<PERSON><PERSON>, English actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "Camilla <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ddington"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Vincentian-American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vincentian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a>, Vincentian-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, English high jumper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English high jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Slovak footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0krtel\" title=\"<PERSON>\"><PERSON></a>, Slovak footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0krtel\" title=\"<PERSON>\"><PERSON></a>, Slovak footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C5%A0krtel"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1986", "text": "<PERSON>, South Korean singer-songwriter and dancer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer-songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Czech sprinter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Iveta_Maz%C3%A1%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iveta_Maz%C3%A1%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iveta_Maz%C3%A1%C4%8Dov%C3%A1"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Costa Rican footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>lor_Navas\" title=\"<PERSON>lor Navas\"><PERSON><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lor_Navas\" title=\"<PERSON>lor Navas\"><PERSON><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Keylor_Navas"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Ukrainian model", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Onopka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Snejana_Onopka"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Swedish ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1988)\" title=\"<PERSON> (ice hockey, born 1988)\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1988)\" title=\"<PERSON> (ice hockey, born 1988)\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1988)", "link": "https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1988)"}]}, {"year": "1988", "text": "<PERSON>, English actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Head\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, French footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American actress and model", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American race car driver", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American musician and actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>m"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Genki"}]}, {"year": "1992", "text": "<PERSON>, English footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Argentine footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1992)\" title=\"<PERSON><PERSON> (footballer, born 1992)\"><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1992)\" title=\"<PERSON><PERSON> (footballer, born 1992)\"><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1992)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1992)"}]}, {"year": "1992", "text": "<PERSON>, Brazilian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, German singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rening\" title=\"<PERSON><PERSON> Brening\"><PERSON><PERSON></a>, German singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rening\" title=\"<PERSON><PERSON> Brening\"><PERSON><PERSON></a>, German singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jen<PERSON>_Brening"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American race car driver", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Polish tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Magdalena_Fr%C4%99ch\" title=\"<PERSON>\"><PERSON></a>, Polish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magdalena_Fr%C4%99ch\" title=\"<PERSON>\"><PERSON></a>, Polish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Magdalena_Fr%C4%99ch"}]}, {"year": "1997", "text": "<PERSON><PERSON>, New Zealand-American actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chandler Canterbury\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chandler Canterbury\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chandler_Canterbury"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Thibodeaux\" title=\"<PERSON><PERSON> Thibodeaux\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Thibodeaux\" title=\"<PERSON><PERSON> Thibodeaux\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}], "Deaths": [{"year": "933", "text": "<PERSON>, Chinese emperor (b. 867)", "html": "933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor (b. 867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Si<PERSON>\"><PERSON></a>, Chinese emperor (b. 867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_Siyuan"}]}, {"year": "1025", "text": "<PERSON>, Byzantine emperor (b. 958)", "html": "1025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Basil II\"><PERSON></a>, Byzantine emperor (b. 958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Basil II\"><PERSON></a>, Byzantine emperor (b. 958)", "links": [{"title": "Basil II", "link": "https://wikipedia.org/wiki/Basil_II"}]}, {"year": "1072", "text": "<PERSON><PERSON>, Turkish sultan (b. 1029)", "html": "1072 - <a href=\"https://wikipedia.org/wiki/Alp_A<PERSON>lan\" title=\"Alp Arslan\"><PERSON><PERSON></a>, Turkish sultan (b. 1029)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alp_A<PERSON>lan\" title=\"Alp Arslan\"><PERSON><PERSON></a>, Turkish sultan (b. 1029)", "links": [{"title": "Alp A<PERSON>lan", "link": "https://wikipedia.org/wiki/Alp_<PERSON>lan"}]}, {"year": "1161", "text": "<PERSON><PERSON>, Chinese emperor (b. 1122)", "html": "1161 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese emperor (b. 1122)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese emperor (b. 1122)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1230", "text": "<PERSON><PERSON>, duke of Bohemia (b. 1155)", "html": "1230 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Bohemia\" title=\"<PERSON><PERSON> I of Bohemia\"><PERSON><PERSON></a>, duke of Bohemia (b. 1155)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Bohemia\" title=\"<PERSON><PERSON> I of Bohemia\"><PERSON><PERSON></a>, duke of Bohemia (b. 1155)", "links": [{"title": "<PERSON><PERSON> I of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_Bohemia"}]}, {"year": "1283", "text": "<PERSON>, Latin emperor (b. 1243)", "html": "1283 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Latin_Emperor\" title=\"<PERSON>, Latin Emperor\"><PERSON></a>, Latin emperor (b. 1243)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Latin_Emperor\" title=\"<PERSON>, Latin Emperor\"><PERSON></a>, Latin emperor (b. 1243)", "links": [{"title": "<PERSON>, Latin Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Latin_Emperor"}]}, {"year": "1343", "text": "<PERSON>, Chopanid prince (b. c. 1319)", "html": "1343 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Chopanid prince (b. c. 1319)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Chopanid prince (b. c. 1319)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1467", "text": "<PERSON><PERSON><PERSON>, archbishop and regent of Sweden (b. 1417)", "html": "1467 - <a href=\"https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON><PERSON><PERSON>_Oxenstierna\" title=\"<PERSON><PERSON><PERSON>stier<PERSON>\"><PERSON><PERSON><PERSON></a>, archbishop and regent of Sweden (b. 1417)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON><PERSON><PERSON>_Oxenstierna\" title=\"<PERSON><PERSON><PERSON>stierna\"><PERSON><PERSON><PERSON></a>, archbishop and regent of Sweden (b. 1417)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>stierna"}]}, {"year": "1574", "text": "<PERSON><PERSON>, Ottoman sultan (b. 1524)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/Selim_II\" title=\"Selim II\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Selim_II\" title=\"Selim II\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1524)", "links": [{"title": "Selim II", "link": "https://wikipedia.org/wiki/Se<PERSON>_II"}]}, {"year": "1598", "text": "<PERSON> of Marnix, Lord of Saint-Aldegonde, Dutch nobleman (b. 1540)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Marnix,_Lord_of_Saint-Aldegonde\" title=\"<PERSON> of Marnix, Lord of Saint-Aldegonde\"><PERSON> of Marnix, Lord of Saint-Aldegonde</a>, Dutch nobleman (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Marnix,_Lord_of_Saint-Aldegonde\" title=\"<PERSON> of Marnix, Lord of Saint-Aldegonde\"><PERSON> of Marnix, Lord of Saint-Aldegonde</a>, Dutch nobleman (b. 1540)", "links": [{"title": "<PERSON> of Marnix, Lord of Saint-Aldegonde", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>,_Lord_of_Saint-Aldegonde"}]}, {"year": "1621", "text": "<PERSON>, du<PERSON> <PERSON>, French courtier, Constable of France (b. 1578)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc <PERSON></a>, French courtier, <a href=\"https://wikipedia.org/wiki/Constable_of_France\" title=\"Constable of France\">Constable of France</a> (b. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc <PERSON></a>, French courtier, <a href=\"https://wikipedia.org/wiki/Constable_of_France\" title=\"Constable of France\">Constable of France</a> (b. 1578)", "links": [{"title": "<PERSON>, duc de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON>,_duc_<PERSON>_<PERSON>s"}, {"title": "Constable of France", "link": "https://wikipedia.org/wiki/Constable_of_France"}]}, {"year": "1673", "text": "<PERSON>, Duchess of Newcastle-upon-Tyne, English noblewoman (b. 1623)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Newcastle-upon-Tyne\" title=\"<PERSON>, Duchess of Newcastle-upon-Tyne\"><PERSON>, Duchess of Newcastle-upon-Tyne</a>, English noblewoman (b. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Newcastle-upon-Tyne\" title=\"<PERSON>, Duchess of Newcastle-upon-Tyne\"><PERSON>, Duchess of Newcastle-upon-Tyne</a>, English noblewoman (b. 1623)", "links": [{"title": "<PERSON>, Duchess of Newcastle-upon-Tyne", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Newcastle-upon-Tyne"}]}, {"year": "1675", "text": "<PERSON>, Dutch painter and educator (b. 1632)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and educator (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and educator (b. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON><PERSON><PERSON>, English author (b. 1593)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, English author (b. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, English author (b. 1593)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON><PERSON>, Dutch lawyer and politician (b. 1634)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/Gaspar_Fagel\" title=\"Gaspar Fagel\"><PERSON><PERSON></a>, Dutch lawyer and politician (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gaspar_Fagel\" title=\"Gaspar Fagel\"><PERSON><PERSON></a>, Dutch lawyer and politician (b. 1634)", "links": [{"title": "Gaspar <PERSON>l", "link": "https://wikipedia.org/wiki/Gaspar_Fagel"}]}, {"year": "1698", "text": "<PERSON>, French nobleman (b. 1636)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_de_Mortemart\" class=\"mw-redirect\" title=\"<PERSON> Mortemart\"><PERSON></a>, French nobleman (b. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Mortemart\" class=\"mw-redirect\" title=\"<PERSON> Mortemart\"><PERSON></a>, French nobleman (b. 1636)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, English minister and scholar (b. 1642)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(divine)\" title=\"<PERSON> (divine)\"><PERSON></a>, English minister and scholar (b. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(divine)\" title=\"<PERSON> (divine)\"><PERSON></a>, English minister and scholar (b. 1642)", "links": [{"title": "<PERSON> (divine)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(divine)"}]}, {"year": "1753", "text": "<PERSON>, 3rd Earl of Burlington, English architect and politician, designed Chiswick House (b. 1694)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Burlington\" title=\"<PERSON>, 3rd Earl of Burlington\"><PERSON>, 3rd Earl of Burlington</a>, English architect and politician, designed <a href=\"https://wikipedia.org/wiki/Chiswick_House\" title=\"Chiswick House\">Chiswick House</a> (b. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Burlington\" title=\"<PERSON>, 3rd Earl of Burlington\"><PERSON>, 3rd Earl of Burlington</a>, English architect and politician, designed <a href=\"https://wikipedia.org/wiki/Chiswick_House\" title=\"Chiswick House\">Chiswick House</a> (b. 1694)", "links": [{"title": "<PERSON>, 3rd Earl of Burlington", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Burlington"}, {"title": "Chiswick House", "link": "https://wikipedia.org/wiki/Chiswick_House"}]}, {"year": "1792", "text": "<PERSON>, Swedish pianist, violinist, and composer (b. 1756)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish pianist, violinist, and composer (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish pianist, violinist, and composer (b. 1756)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON><PERSON><PERSON>, Russian rabbi, author and founder of Chabad (b. 1745)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian rabbi, author and founder of <a href=\"https://wikipedia.org/wiki/Chabad\" title=\"Chabad\"><PERSON><PERSON></a> (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian rabbi, author and founder of <a href=\"https://wikipedia.org/wiki/Chabad\" title=\"Chabad\"><PERSON><PERSON></a> (b. 1745)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chabad", "link": "https://wikipedia.org/wiki/Chabad"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON>, astronomer, director of the Astronomical Observatory of Naples (b. 1783)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>eri<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, astronomer, director of the Astronomical Observatory of Naples (b. <a href=\"https://wikipedia.org/wiki/1783\" title=\"1783\">1783</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, astronomer, director of the Astronomical Observatory of Naples (b. <a href=\"https://wikipedia.org/wiki/1783\" title=\"1783\">1783</a>)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Federigo_<PERSON>i"}, {"title": "1783", "link": "https://wikipedia.org/wiki/1783"}]}, {"year": "1819", "text": "<PERSON>, Scottish chemist and physician (b. 1749)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and physician (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and physician (b. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, French mathematician and academic (b. 1803)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Sturm\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Sturm\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Sturm"}]}, {"year": "1878", "text": "<PERSON>, English chemist and businessman, invented baking powder (b. 1811)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and businessman, invented <a href=\"https://wikipedia.org/wiki/Baking_powder\" title=\"Baking powder\">baking powder</a> (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bird\"><PERSON></a>, English chemist and businessman, invented <a href=\"https://wikipedia.org/wiki/Baking_powder\" title=\"Baking powder\">baking powder</a> (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Baking powder", "link": "https://wikipedia.org/wiki/Baking_powder"}]}, {"year": "1890", "text": "Sitting Bull, Hunkpapa Lakota tribal chief (b. 1831)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Sitting_Bull\" title=\"Sitting Bull\">Sitting Bull</a>, <a href=\"https://wikipedia.org/wiki/Hunkpapa\" title=\"Hunkpapa\"><PERSON>nkpapa</a> <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> tribal chief (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sitting_Bull\" title=\"Sitting Bull\">Sitting Bull</a>, <a href=\"https://wikipedia.org/wiki/Hunkpapa\" title=\"Hunkpapa\"><PERSON><PERSON>papa</a> <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> tribal chief (b. 1831)", "links": [{"title": "Sitting Bull", "link": "https://wikipedia.org/wiki/Sitting_Bull"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hunkpapa"}, {"title": "Lakota people", "link": "https://wikipedia.org/wiki/Lakota_people"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American singer-songwriter and pianist (b. 1904)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Waller\" title=\"<PERSON><PERSON> Waller\"><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Waller\" title=\"<PERSON><PERSON> Waller\"><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Waller"}]}, {"year": "1944", "text": "<PERSON>, American bandleader and composer (b. 1904)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and composer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and composer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Welsh journalist and author (b. 1863)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh journalist and author (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh journalist and author (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Australian politician, 27th Premier of South Australia (b. 1874)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 1st Deputy Prime Minister of India (b. 1875)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India\" title=\"Deputy Prime Minister of India\">Deputy Prime Minister of India</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India\" title=\"Deputy Prime Minister of India\">Deputy Prime Minister of India</a> (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of India", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India"}]}, {"year": "1958", "text": "<PERSON>, Austrian-Swiss physicist and academic, Nobel Prize laureate (b. 1900)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1962", "text": "<PERSON>, English-American actor, director, and producer (b. 1899)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, director, and producer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, director, and producer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Sri Lankan journalist, lawyer, and politician (b. 1903)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan journalist, lawyer, and politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan journalist, lawyer, and politician (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, 15th Viscount of Arbuthnott, Indian-Scottish general and politician, Lord Lieutenant of Kincardineshire (b. 1897)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_15th_Viscount_of_A<PERSON>uthnot<PERSON>\" title=\"<PERSON>, 15th Viscount of Arbuthnott\"><PERSON>, 15th Viscount of Arbuthnott</a>, Indian-Scottish general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Kincardineshire\" title=\"Lord Lieutenant of Kincardineshire\">Lord Lieutenant of Kincardineshire</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_15th_Viscount_of_A<PERSON>uth<PERSON>\" title=\"<PERSON>, 15th Viscount of Arbuthnott\"><PERSON>, 15th Viscount of Arbuthnott</a>, Indian-Scottish general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Kincardineshire\" title=\"Lord Lieutenant of Kincardineshire\">Lord Lieutenant of Kincardineshire</a> (b. 1897)", "links": [{"title": "<PERSON>, 15th Viscount of Arbuthnott", "link": "https://wikipedia.org/wiki/<PERSON>,_15th_Viscount_of_<PERSON>"}, {"title": "Lord Lieutenant of Kincardineshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Kincardineshire"}]}, {"year": "1966", "text": "<PERSON>, American animator, director, producer, and screenwriter, co-founded The Walt Disney Company (b. 1901)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON></a>, American animator, director, producer, and screenwriter, co-founded <a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON></a>, American animator, director, producer, and screenwriter, co-founded <a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Walt Disney Company", "link": "https://wikipedia.org/wiki/The_Walt_Disney_Company"}]}, {"year": "1968", "text": "<PERSON>, Canadian politician, 18th Premier of Quebec (b. 1899)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_premiers_of_Quebec\" title=\"List of premiers of Quebec\">Premier of Quebec</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_premiers_of_Quebec\" title=\"List of premiers of Quebec\">Premier of Quebec</a> (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of premiers of Quebec", "link": "https://wikipedia.org/wiki/List_of_premiers_of_Quebec"}]}, {"year": "1968", "text": "<PERSON>, American boxer and actor (b. 1881)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, German lawyer and politician, 12th Mayor of Marburg (b. 1898)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Marburg", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Marburg"}]}, {"year": "1971", "text": "<PERSON>, French mathematician and theorist (b. 1886)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, French mathematician and theorist (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, French mathematician and theorist (b. 1886)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_(mathematician)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Russian-American director, producer, and screenwriter (b. 1902)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Litvak\" title=\"<PERSON><PERSON><PERSON> Litvak\"><PERSON><PERSON><PERSON></a>, Russian-American director, producer, and screenwriter (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tvak\" title=\"<PERSON><PERSON><PERSON> Litvak\"><PERSON><PERSON><PERSON></a>, Russian-American director, producer, and screenwriter (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anatole_Litvak"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, English 7th General of The Salvation Army (b. 1893)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, English 7th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"Wil<PERSON>\">W<PERSON><PERSON></a>, English 7th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American actor (b. 1903)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chill_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American race car driver (b. 1940)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1940)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1984", "text": "<PERSON>, American tenor and actor (b. 1904)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Mauritian physician and politician, 1st Prime Minister of Mauritius (b. 1900)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Seewoosagur_<PERSON>goolam\" title=\"Seewoosagur <PERSON>\">See<PERSON><PERSON><PERSON><PERSON></a>, Mauritian physician and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Mauritius\" class=\"mw-redirect\" title=\"List of Prime Ministers of Mauritius\">Prime Minister of Mauritius</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seewoosagur_<PERSON>olam\" title=\"Seewoosagu<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mauritian physician and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Mauritius\" class=\"mw-redirect\" title=\"List of Prime Ministers of Mauritius\">Prime Minister of Mauritius</a> (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Seewoosagur_Ramgoolam"}, {"title": "List of Prime Ministers of Mauritius", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Mauritius"}]}, {"year": "1986", "text": "<PERSON>, Russian-French ballet dancer and choreographer (b. 1905)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French ballet dancer and choreographer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French ballet dancer and choreographer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English actor and jockey (b. 1908)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and jockey (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>down\"><PERSON></a>, English actor and jockey (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Russian captain (b. 1915)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(sniper)\" title=\"<PERSON><PERSON> (sniper)\"><PERSON><PERSON></a>, Russian captain (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(sniper)\" title=\"<PERSON><PERSON> (sniper)\"><PERSON><PERSON></a>, Russian captain (b. 1915)", "links": [{"title": "<PERSON><PERSON> (sniper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(sniper)"}]}, {"year": "1993", "text": "<PERSON>, American chemist and engineer (b. 1925)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Bosnian-Serbian basketball player (b. 1974)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Hari<PERSON>_B<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian-Serbian basketball player (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hari<PERSON>_B<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian-Serbian basketball player (b. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Haris_Brki%C4%87"}]}, {"year": "2003", "text": "<PERSON>, Maltese sculptor (b. 1909)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese sculptor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese sculptor (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American cartoonist (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (b. 1923)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)"}]}, {"year": "2003", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1947)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Nauruan educator and politician, Speaker of the Nauru Parliament (b. 1943)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Vassal_Gadoengin\" title=\"Vassal Gadoengin\"><PERSON><PERSON><PERSON></a>, Nauruan educator and politician, <a href=\"https://wikipedia.org/wiki/List_of_Speakers_of_the_Parliament_of_Nauru\" class=\"mw-redirect\" title=\"List of Speakers of the Parliament of Nauru\">Speaker of the Nauru Parliament</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vassal_Gadoengin\" title=\"Vassal Gadoengin\"><PERSON><PERSON><PERSON></a>, Nauruan educator and politician, <a href=\"https://wikipedia.org/wiki/List_of_Speakers_of_the_Parliament_of_Nauru\" class=\"mw-redirect\" title=\"List of Speakers of the Parliament of Nauru\">Speaker of the Nauru Parliament</a> (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vassal_G<PERSON>in"}, {"title": "List of Speakers of the Parliament of Nauru", "link": "https://wikipedia.org/wiki/List_of_Speakers_of_the_Parliament_of_Nauru"}]}, {"year": "2005", "text": "<PERSON>, Austrian physician and psychiatrist (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and psychiatrist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and psychiatrist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian golfer (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American soldier, journalist, and politician (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and politician (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and politician (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American football player (b. 1976)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1976)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2006", "text": "<PERSON>, Swiss race car driver (b. 1939)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Reg<PERSON>i\"><PERSON></a>, Swiss race car driver (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i\" title=\"Clay Regazzoni\"><PERSON></a>, Swiss race car driver (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Clay_Regazzoni"}]}, {"year": "2006", "text": "<PERSON>, American journalist and author (b. 1920)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American lawyer and politician (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Ecuadorian engineer and politician, 46th President of Ecuador (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Le%C3%B3n_Febres_Cordero\" title=\"León Febres Cordero\"><PERSON></a>, Ecuadorian engineer and politician, 46th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ecuador\" class=\"mw-redirect\" title=\"List of heads of state of Ecuador\">President of Ecuador</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le%C3%B3n_Febres_Cordero\" title=\"León Febres Cordero\"><PERSON></a>, Ecuadorian engineer and politician, 46th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ecuador\" class=\"mw-redirect\" title=\"List of heads of state of Ecuador\">President of Ecuador</a> (b. 1931)", "links": [{"title": "León Febres Cordero", "link": "https://wikipedia.org/wiki/Le%C3%B3n_Febres_Cordero"}, {"title": "List of heads of state of Ecuador", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Ecuador"}]}, {"year": "2009", "text": "<PERSON>, American librarian (b. 1909)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American evangelist, founded the Oral Roberts Evangelistic Association (b. 1918)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Roberts\"><PERSON></a>, American evangelist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_Roberts_Evangelistic_Association\" title=\"Oral Roberts Evangelistic Association\">Oral Roberts Evangelistic Association</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Roberts\"><PERSON></a>, American evangelist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_Roberts_Evangelistic_Association\" title=\"Oral Roberts Evangelistic Association\">Oral Roberts Evangelistic Association</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Oral Roberts Evangelistic Association", "link": "https://wikipedia.org/wiki/<PERSON>_Roberts_Evangelistic_Association"}]}, {"year": "2010", "text": "<PERSON>, American director, producer, and screenwriter (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American baseball player and sportscaster (b. 1918)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American psychoanalyst and theorist (b. 1940)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychoanalyst and theorist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychoanalyst and theorist (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American trombone player and composer (b. 1929)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombone player and composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombone player and composer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English-American essayist, literary critic, and journalist (b. 1949)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American essayist, literary critic, and journalist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American essayist, literary critic, and journalist (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Nigerian general (b. 1952)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian general (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian general (b. 1952)", "links": [{"title": "Owoye <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Nigerian politician, 18th Governor of Kaduna State (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Nigerian politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Kaduna_State\" class=\"mw-redirect\" title=\"List of Governors of Kaduna State\">Governor of Kaduna State</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Nigerian politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Kaduna_State\" class=\"mw-redirect\" title=\"List of Governors of Kaduna State\">Governor of Kaduna State</a> (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Governors of Kaduna State", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Kaduna_State"}]}, {"year": "2012", "text": "<PERSON>, Argentinian actress (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American evangelist, author, radio host (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist, author, radio host (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Camping\"><PERSON></a>, American evangelist, author, radio host (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, British-American actress (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American basketball player (b. 1967)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (b. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Australian physiologist and immunologist (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physiologist and immunologist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physiologist and immunologist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>,  Mexican journalist, lawyer, and politician, Governor of San Luis Potosí (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican journalist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_San_Luis_Potos%C3%AD\" title=\"Governor of San Luis Potosí\">Governor of San Luis Potosí</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican journalist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_San_Luis_Potos%C3%AD\" title=\"Governor of San Luis Potosí\">Governor of San Luis Potosí</a> (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of San Luis Potosí", "link": "https://wikipedia.org/wiki/Governor_of_San_Luis_<PERSON>%C3%AD"}]}, {"year": "2015", "text": "<PERSON>, English-Israeli physicist and engineer (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Israeli physicist and engineer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Israeli physicist and engineer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American sports journalist (b. 1951)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports journalist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports journalist (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, scientist and TV presenter (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, scientist and TV presenter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, scientist and TV presenter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, academic (b. 1953)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>us_Juma\" title=\"Calestous Juma\"><PERSON><PERSON><PERSON></a>, academic (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Juma\" title=\"<PERSON>stous Juma\"><PERSON><PERSON><PERSON></a>, academic (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Calestous_Juma"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, Chinese historical fiction writer (b.1945) ", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese historical fiction writer (b.1945) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese historical fiction writer (b.1945) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, President of Ethiopia (b. 1924)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>old<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>old<PERSON>\"><PERSON><PERSON><PERSON></a>, President of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, President of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gir<PERSON>_<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON><PERSON>, Tuvaluan politician, 8th Prime Minister of Tuvalu (b. 1952)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Saufatu_Sopoanga\" title=\"Saufatu Sopoanga\"><PERSON><PERSON><PERSON><PERSON> Sopoanga</a>, Tuvaluan politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu\" title=\"Prime Minister of Tuvalu\">Prime Minister of Tuvalu</a> (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saufatu_Sopoanga\" title=\"Saufatu Sopoanga\"><PERSON><PERSON><PERSON><PERSON> Sopoanga</a>, Tuvaluan politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu\" title=\"Prime Minister of Tuvalu\">Prime Minister of Tuvalu</a> (b. 1952)", "links": [{"title": "Saufatu Sopoanga", "link": "https://wikipedia.org/wiki/Saufatu_Sopoanga"}, {"title": "Prime Minister of Tuvalu", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Indian tabla player, musical producer, film actor and composer (b. 1951)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Indian tabla player, musical producer, film actor and composer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Indian tabla player, musical producer, film actor and composer (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(musician)"}]}]}}