{"date": "February 11", "url": "https://wikipedia.org/wiki/February_11", "data": {"Events": [{"year": "660 BC", "text": "Traditional date for the foundation of Japan by Emperor <PERSON><PERSON>.", "html": "660 BC - 660 BC - Traditional date for the foundation of Japan by <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a>.", "no_year_html": "660 BC - Traditional date for the foundation of Japan by <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a>.", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "55", "text": "The death under mysterious circumstances of <PERSON><PERSON><PERSON>, heir to the Roman Empire, on the eve of his coming of age clears the way for <PERSON> to become Emperor.", "html": "55 - The death under mysterious circumstances of <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_Claudius_Caesar_Britannicus\" class=\"mw-redirect\" title=\"Tiberius Claudius Caesar Britannicus\">T<PERSON><PERSON>laudius <PERSON> Britannicus</a>, heir to the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a>, on the eve of his coming of age clears the way for <a href=\"https://wikipedia.org/wiki/Nero\" title=\"Nero\">Nero</a> to become Emperor.", "no_year_html": "The death under mysterious circumstances of <a href=\"https://wikipedia.org/wiki/Tiber<PERSON>_Claudius_Caesar_Britannicus\" class=\"mw-redirect\" title=\"Tiberius Claudius Caesar Britannicus\">T<PERSON><PERSON>laudius <PERSON> Britannicus</a>, heir to the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a>, on the eve of his coming of age clears the way for <a href=\"https://wikipedia.org/wiki/Nero\" title=\"Nero\">Nero</a> to become Emperor.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON><PERSON>dius_Caesar_Britannicus"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nero"}]}, {"year": "951", "text": "<PERSON>, a court official, leads a military coup and declares himself emperor of the new Later Zhou.", "html": "951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a court official, leads a military coup and declares himself emperor of the new <a href=\"https://wikipedia.org/wiki/<PERSON>_Zhou\" title=\"<PERSON> Zhou\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a court official, leads a military coup and declares himself emperor of the new <a href=\"https://wikipedia.org/wiki/<PERSON>_Zhou\" title=\"<PERSON> Zhou\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Later <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1144", "text": "<PERSON> Chester completes his translation from Arabic to Latin of the Liber de compositione alchemiae, marking the birth of Western alchemy.", "html": "1144 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Chester\"><PERSON> Chester</a> completes his translation from <a href=\"https://wikipedia.org/wiki/Arabic\" title=\"Arabic\">Arabic</a> to <a href=\"https://wikipedia.org/wiki/Latin\" title=\"Latin\">Latin</a> of the <i><a href=\"https://wikipedia.org/wiki/Liber_de_compositione_alchemiae\" title=\"Liber de compositione alchemiae\">Liber de compositione alchemiae</a></i>, marking the birth of Western <a href=\"https://wikipedia.org/wiki/Alchemy\" title=\"Alchemy\">alchemy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Chester\"><PERSON> Chester</a> completes his translation from <a href=\"https://wikipedia.org/wiki/Arabic\" title=\"Arabic\">Arabic</a> to <a href=\"https://wikipedia.org/wiki/Latin\" title=\"Latin\">Latin</a> of the <i><a href=\"https://wikipedia.org/wiki/Liber_de_compositione_alchemiae\" title=\"Liber de compositione alchemiae\">Liber de compositione alchemiae</a></i>, marking the birth of Western <a href=\"https://wikipedia.org/wiki/Alchemy\" title=\"Alchemy\">alchemy</a>.", "links": [{"title": "<PERSON> of Chester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Chester"}, {"title": "Arabic", "link": "https://wikipedia.org/wiki/Arabic"}, {"title": "Latin", "link": "https://wikipedia.org/wiki/Latin"}, {"title": "Liber de compositione alchemiae", "link": "https://wikipedia.org/wiki/Liber_de_compositione_alchemiae"}, {"title": "Alchemy", "link": "https://wikipedia.org/wiki/Alchemy"}]}, {"year": "1534", "text": "At the Convocation of Canterbury, the Catholic bishops comprising the Upper House of the Province of Canterbury agree to style <PERSON> VIII supreme head of the English church and clergy \"so far as the law of Christ allows\".", "html": "1534 - At the <a href=\"https://wikipedia.org/wiki/Convocations_of_Canterbury_and_York\" title=\"Convocations of Canterbury and York\">Convocation of Canterbury</a>, the Catholic bishops comprising the Upper House of the <a href=\"https://wikipedia.org/wiki/Province_of_Canterbury\" title=\"Province of Canterbury\">Province of Canterbury</a> agree to style <a href=\"https://wikipedia.org/wiki/Henry_VIII\" title=\"<PERSON> VIII\"><PERSON> VIII</a> supreme head of the English church and clergy \"so far as the law of Christ allows\".", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Convocations_of_Canterbury_and_York\" title=\"Convocations of Canterbury and York\">Convocation of Canterbury</a>, the Catholic bishops comprising the Upper House of the <a href=\"https://wikipedia.org/wiki/Province_of_Canterbury\" title=\"Province of Canterbury\">Province of Canterbury</a> agree to style <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII\" title=\"<PERSON> VIII\"><PERSON> VIII</a> supreme head of the English church and clergy \"so far as the law of Christ allows\".", "links": [{"title": "Convocations of Canterbury and York", "link": "https://wikipedia.org/wiki/Convocations_of_Canterbury_and_York"}, {"title": "Province of Canterbury", "link": "https://wikipedia.org/wiki/Province_of_Canterbury"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1584", "text": "A naval expedition led by <PERSON> founds Nombre de Jesús, the first of two short-lived Spanish settlements in the Strait of Magellan.", "html": "1584 - A naval expedition led by <a href=\"https://wikipedia.org/wiki/Pedro_Sarmiento_de_Gamboa\" title=\"<PERSON> de Gamboa\"><PERSON> Gamboa</a> founds <a href=\"https://wikipedia.org/wiki/Nombre_de_Jes%C3%BAs_(Patagonia)\" title=\"Nombre de Jesús (Patagonia)\">Nombre de Jesús</a>, the first of two short-lived <a href=\"https://wikipedia.org/wiki/Spanish_colonization_attempt_of_the_Strait_of_Magellan\" title=\"Spanish colonization attempt of the Strait of Magellan\">Spanish settlements in the Strait of Magellan</a>.", "no_year_html": "A naval expedition led by <a href=\"https://wikipedia.org/wiki/Pedro_Sarmiento_de_Gamboa\" title=\"Pedro <PERSON> de Gamboa\"><PERSON> Gamboa</a> founds <a href=\"https://wikipedia.org/wiki/Nombre_de_Jes%C3%BAs_(Patagonia)\" title=\"Nombre de Jesús (Patagonia)\">Nombre de Jesús</a>, the first of two short-lived <a href=\"https://wikipedia.org/wiki/Spanish_colonization_attempt_of_the_Strait_of_Magellan\" title=\"Spanish colonization attempt of the Strait of Magellan\">Spanish settlements in the Strait of Magellan</a>.", "links": [{"title": "<PERSON> Gamboa", "link": "https://wikipedia.org/wiki/Pedro_Sarmiento_de_Gamboa"}, {"title": "Nombre de Jesús (Patagonia)", "link": "https://wikipedia.org/wiki/Nombre_de_Jes%C3%BAs_(Patagonia)"}, {"title": "Spanish colonization attempt of the Strait of Magellan", "link": "https://wikipedia.org/wiki/Spanish_colonization_attempt_of_the_Strait_of_Magellan"}]}, {"year": "1586", "text": "Sir <PERSON> with an English force captures and occupies the Spanish colonial port of Cartagena de Indias for two months, obtaining a ransom and booty.", "html": "1586 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> with an English force <a href=\"https://wikipedia.org/wiki/Battle_of_Cartagena_de_Indias_(1586)\" title=\"Battle of Cartagena de Indias (1586)\">captures and occupies</a> the Spanish colonial port of <a href=\"https://wikipedia.org/wiki/Cartagena_de_Indias\" class=\"mw-redirect\" title=\"Cartagena de Indias\">Cartagena de Indias</a> for two months, obtaining a ransom and booty.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> with an English force <a href=\"https://wikipedia.org/wiki/Battle_of_Cartagena_de_Indias_(1586)\" title=\"Battle of Cartagena de Indias (1586)\">captures and occupies</a> the Spanish colonial port of <a href=\"https://wikipedia.org/wiki/Cartagena_de_Indias\" class=\"mw-redirect\" title=\"Cartagena de Indias\">Cartagena de Indias</a> for two months, obtaining a ransom and booty.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Cartagena de Indias (1586)", "link": "https://wikipedia.org/wiki/Battle_of_Cartagena_de_Indias_(1586)"}, {"title": "Cartagena de Indias", "link": "https://wikipedia.org/wiki/Cartagena_de_Indias"}]}, {"year": "1659", "text": "The assault on Copenhagen by Swedish forces is beaten back with heavy losses.", "html": "1659 - <a href=\"https://wikipedia.org/wiki/Assault_on_Copenhagen_(1659)\" title=\"Assault on Copenhagen (1659)\">The assault on Copenhagen</a> by Swedish forces is beaten back with heavy losses.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Assault_on_Copenhagen_(1659)\" title=\"Assault on Copenhagen (1659)\">The assault on Copenhagen</a> by Swedish forces is beaten back with heavy losses.", "links": [{"title": "Assault on Copenhagen (1659)", "link": "https://wikipedia.org/wiki/Assault_on_Copenhagen_(1659)"}]}, {"year": "1794", "text": "First session of United States Senate opens to the public.", "html": "1794 - First session of <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> opens to the public.", "no_year_html": "First session of <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> opens to the public.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "1808", "text": "<PERSON> burns anthracite on an open grate as an experiment in heating homes with coal.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> burns <a href=\"https://wikipedia.org/wiki/Anthracite\" title=\"Anthracite\">anthracite</a> on an open grate as an experiment in heating homes with coal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Fell\"><PERSON></a> burns <a href=\"https://wikipedia.org/wiki/Anthracite\" title=\"Anthracite\">anthracite</a> on an open grate as an experiment in heating homes with coal.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Anthracite", "link": "https://wikipedia.org/wiki/Anthracite"}]}, {"year": "1812", "text": "Massachusetts governor <PERSON><PERSON> is accused of \"gerrymandering\" for the first time.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> governor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gerry\" title=\"<PERSON><PERSON> Gerry\"><PERSON><PERSON></a> is accused of \"<a href=\"https://wikipedia.org/wiki/Gerrymandering\" title=\"Gerrymandering\">gerrymandering</a>\" for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> governor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gerry\" title=\"<PERSON><PERSON> Gerry\"><PERSON><PERSON></a> is accused of \"<a href=\"https://wikipedia.org/wiki/Gerrymandering\" title=\"Gerrymandering\">gerrymandering</a>\" for the first time.", "links": [{"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}, {"title": "El<PERSON> Gerry", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Gerrymandering", "link": "https://wikipedia.org/wiki/Gerrymandering"}]}, {"year": "1823", "text": "Carnival tragedy of 1823: About 110 boys are killed during a human crush at the Convent of the Minori Osservanti in Valletta, Malta.", "html": "1823 - <a href=\"https://wikipedia.org/wiki/Carnival_tragedy_of_1823\" title=\"Carnival tragedy of 1823\">Carnival tragedy of 1823</a>: About 110 boys are killed during a <a href=\"https://wikipedia.org/wiki/Human_crush\" class=\"mw-redirect\" title=\"Human crush\">human crush</a> at the <a href=\"https://wikipedia.org/wiki/Franciscan_Church_of_St_Mary_of_Jesus\" title=\"Franciscan Church of St Mary of Jesus\">Convent of the Minori Osservanti</a> in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a>, <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Malta\" title=\"Crown Colony of Malta\">Malta</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carnival_tragedy_of_1823\" title=\"Carnival tragedy of 1823\">Carnival tragedy of 1823</a>: About 110 boys are killed during a <a href=\"https://wikipedia.org/wiki/Human_crush\" class=\"mw-redirect\" title=\"Human crush\">human crush</a> at the <a href=\"https://wikipedia.org/wiki/Franciscan_Church_of_St_Mary_of_Jesus\" title=\"Franciscan Church of St Mary of Jesus\">Convent of the Minori Osservanti</a> in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a>, <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Malta\" title=\"Crown Colony of Malta\">Malta</a>.", "links": [{"title": "Carnival tragedy of 1823", "link": "https://wikipedia.org/wiki/Carnival_tragedy_of_1823"}, {"title": "Human crush", "link": "https://wikipedia.org/wiki/Human_crush"}, {"title": "Franciscan Church of St Mary of Jesus", "link": "https://wikipedia.org/wiki/Franciscan_Church_of_St_Mary_of_Jesus"}, {"title": "Valletta", "link": "https://wikipedia.org/wiki/Valletta"}, {"title": "Crown Colony of Malta", "link": "https://wikipedia.org/wiki/Crown_Colony_of_Malta"}]}, {"year": "1826", "text": "University College London is founded as University of London.", "html": "1826 - <a href=\"https://wikipedia.org/wiki/University_College_London\" title=\"University College London\">University College London</a> is founded as <a href=\"https://wikipedia.org/wiki/University_of_London\" title=\"University of London\">University of London</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/University_College_London\" title=\"University College London\">University College London</a> is founded as <a href=\"https://wikipedia.org/wiki/University_of_London\" title=\"University of London\">University of London</a>.", "links": [{"title": "University College London", "link": "https://wikipedia.org/wiki/University_College_London"}, {"title": "University of London", "link": "https://wikipedia.org/wiki/University_of_London"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON>'s opera La fille du régiment receives its first performance in Paris, France.", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/La_fille_du_r%C3%A9giment\" title=\"La fille du régiment\">La fille du régiment</a></i> receives its first performance in Paris, France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/La_fille_du_r%C3%A9giment\" title=\"La fille du régiment\">La fille du régiment</a></i> receives its first performance in Paris, France.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "La fille du régiment", "link": "https://wikipedia.org/wiki/La_fille_du_r%C3%A9giment"}]}, {"year": "1843", "text": "<PERSON>'s opera I Lombardi alla prima crociata receives its first performance in Milan, Italy.", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Giuseppe Verdi\"><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/I_Lombardi_alla_prima_crociata\" title=\"I Lombardi alla prima crociata\">I Lombardi alla prima crociata</a></i> receives its first performance in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>, Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Verdi\" title=\"Giuseppe Verdi\"><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/I_Lombardi_alla_prima_crociata\" title=\"I Lombardi alla prima crociata\">I Lombardi alla prima crociata</a></i> receives its first performance in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>, Italy.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "I Lombardi alla prima crociata", "link": "https://wikipedia.org/wiki/I_Lombard<PERSON>_alla_prima_crociata"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}]}, {"year": "1855", "text": "<PERSON><PERSON> is crowned <PERSON><PERSON><PERSON><PERSON> <PERSON>, Emperor of Ethiopia.", "html": "1855 - <PERSON><PERSON> is crowned <a href=\"https://wikipedia.org/wiki/Tewodros_II\" title=\"Tewodros II\">Tewodros II</a>, <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a>.", "no_year_html": "<PERSON><PERSON> is crowned <a href=\"https://wikipedia.org/wiki/Tewodros_II\" title=\"Tewodros II\">Tewodros II</a>, <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a>.", "links": [{"title": "Tewodros II", "link": "https://wikipedia.org/wiki/Tewodros_II"}, {"title": "Emperor of Ethiopia", "link": "https://wikipedia.org/wiki/Emperor_of_Ethiopia"}]}, {"year": "1856", "text": "The Kingdom of Awadh is annexed by the British East India Company and <PERSON><PERSON><PERSON>, the king of Awadh, is deposed.", "html": "1856 - The Kingdom of <a href=\"https://wikipedia.org/wiki/Awadh\" title=\"Awadh\">Awadh</a> is annexed by the British <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> and <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the king of Awadh, is deposed.", "no_year_html": "The Kingdom of <a href=\"https://wikipedia.org/wiki/Awadh\" title=\"Awadh\">Awadh</a> is annexed by the British <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> and <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the king of Awadh, is deposed.", "links": [{"title": "Awadh", "link": "https://wikipedia.org/wiki/Awadh"}, {"title": "East India Company", "link": "https://wikipedia.org/wiki/East_India_Company"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON>'s first vision of the Blessed Virgin <PERSON> occurs in Lourdes, France.", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Bernadette_Soubirous\" title=\"Bernadette Soubirous\">Bernadette Soubirous</a>'s first <a href=\"https://wikipedia.org/wiki/Lourdes_apparitions\" title=\"Lourdes apparitions\">vision</a> of the <a href=\"https://wikipedia.org/wiki/Our_Lady_of_Lourdes\" title=\"Our Lady of Lourdes\">Blessed Virgin Mary</a> occurs in <a href=\"https://wikipedia.org/wiki/Lourdes\" title=\"Lourdes\">Lourdes</a>, France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bernadette_Soubirous\" title=\"Bernadette Soubirous\">Bernadette Soubirous</a>'s first <a href=\"https://wikipedia.org/wiki/Lourdes_apparitions\" title=\"Lourdes apparitions\">vision</a> of the <a href=\"https://wikipedia.org/wiki/Our_Lady_of_Lourdes\" title=\"Our Lady of Lourdes\">Blessed Virgin Mary</a> occurs in <a href=\"https://wikipedia.org/wiki/Lourdes\" title=\"Lourdes\">Lourdes</a>, France.", "links": [{"title": "Bernadette Soubirous", "link": "https://wikipedia.org/wiki/Bernadette_Soubirous"}, {"title": "Lourdes apparitions", "link": "https://wikipedia.org/wiki/Lourdes_apparitions"}, {"title": "Our Lady of Lourdes", "link": "https://wikipedia.org/wiki/Our_Lady_of_Lourdes"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lourdes"}]}, {"year": "1861", "text": "American Civil War: The United States House of Representatives unanimously passes a resolution guaranteeing noninterference with slavery in any state.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> unanimously passes a resolution guaranteeing noninterference with <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> in any <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> unanimously passes a resolution guaranteeing noninterference with <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> in any <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">state</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1873", "text": "King <PERSON><PERSON><PERSON> of Spain abdicates, triggering the proclamation of the First Spanish Republic.", "html": "1873 - King <a href=\"https://wikipedia.org/wiki/Amadeo_I_of_Spain\" title=\"Amadeo I of Spain\">Amadeo I of Spain</a> abdicates, triggering the proclamation of the <a href=\"https://wikipedia.org/wiki/First_Spanish_Republic\" title=\"First Spanish Republic\">First Spanish Republic</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Amadeo_I_of_Spain\" title=\"Amadeo I of Spain\">Amadeo I of Spain</a> abdicates, triggering the proclamation of the <a href=\"https://wikipedia.org/wiki/First_Spanish_Republic\" title=\"First Spanish Republic\">First Spanish Republic</a>.", "links": [{"title": "Amadeo I of Spain", "link": "https://wikipedia.org/wiki/Amadeo_I_of_Spain"}, {"title": "First Spanish Republic", "link": "https://wikipedia.org/wiki/First_Spanish_Republic"}]}, {"year": "1889", "text": "The Meiji Constitution of Japan is adopted.", "html": "1889 - The <a href=\"https://wikipedia.org/wiki/Meiji_Constitution\" title=\"Meiji Constitution\">Meiji Constitution</a> of Japan is adopted.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Meiji_Constitution\" title=\"Meiji Constitution\">Meiji Constitution</a> of Japan is adopted.", "links": [{"title": "Meiji Constitution", "link": "https://wikipedia.org/wiki/Meiji_Constitution"}]}, {"year": "1903", "text": "<PERSON>'s 9th Symphony receives its first performance in Vienna, Austria.", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Symphony_No._9_(<PERSON><PERSON><PERSON><PERSON>)\" title=\"Symphony No. 9 (<PERSON><PERSON><PERSON><PERSON>)\">9th Symphony</a> receives its first performance in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, Austria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Symphony_No._9_(<PERSON><PERSON><PERSON><PERSON>)\" title=\"Symphony No. 9 (<PERSON><PERSON><PERSON><PERSON>)\">9th Symphony</a> receives its first performance in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, Austria.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Symphony No. 9 (<PERSON><PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._9_(<PERSON><PERSON><PERSON><PERSON>)"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}]}, {"year": "1906", "text": "<PERSON> <PERSON> publishes the encyclical Vehementer Nos.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius <PERSON>\">Pope <PERSON></a> publishes the encyclical <i><a href=\"https://wikipedia.org/wiki/Vehementer_Nos\" title=\"Vehementer Nos\">Vehementer Nos</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Pius_<PERSON>\" title=\"Pope Pius <PERSON>\">Pope <PERSON></a> publishes the encyclical <i><a href=\"https://wikipedia.org/wiki/Vehementer_Nos\" title=\"Vehementer Nos\">Vehementer Nos</a></i>.", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vehementer Nos", "link": "https://wikipedia.org/wiki/Vehementer_Nos"}]}, {"year": "1919", "text": "<PERSON> (SPD), is elected President of Germany.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (<a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_of_Germany\" title=\"Social Democratic Party of Germany\">SPD</a>), is elected <a href=\"https://wikipedia.org/wiki/President_of_Germany\" title=\"President of Germany\">President of Germany</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (<a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_of_Germany\" title=\"Social Democratic Party of Germany\">SPD</a>), is elected <a href=\"https://wikipedia.org/wiki/President_of_Germany\" title=\"President of Germany\">President of Germany</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Social Democratic Party of Germany", "link": "https://wikipedia.org/wiki/Social_Democratic_Party_of_Germany"}, {"title": "President of Germany", "link": "https://wikipedia.org/wiki/President_of_Germany"}]}, {"year": "1929", "text": "The Kingdom of Italy and the Vatican sign the Lateran Treaty.", "html": "1929 - The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Kingdom of Italy</a> and the <a href=\"https://wikipedia.org/wiki/Holy_See\" title=\"Holy See\">Vatican</a> sign the <a href=\"https://wikipedia.org/wiki/Lateran_Treaty\" title=\"Lateran Treaty\">Lateran Treaty</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Kingdom of Italy</a> and the <a href=\"https://wikipedia.org/wiki/Holy_See\" title=\"Holy See\">Vatican</a> sign the <a href=\"https://wikipedia.org/wiki/Lateran_Treaty\" title=\"Lateran Treaty\">Lateran Treaty</a>.", "links": [{"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "Holy See", "link": "https://wikipedia.org/wiki/Holy_See"}, {"title": "Lateran Treaty", "link": "https://wikipedia.org/wiki/Lateran_Treaty"}]}, {"year": "1933", "text": "LAPD Red Squad raid on John Reed Club art show in the U.S. results in the destruction of a dozen political artworks.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/LAPD_Red_Squad_raid_on_John_Reed_Club_art_show\" title=\"LAPD Red Squad raid on John Reed Club art show\">LAPD Red Squad raid on John Reed Club art show</a> in the U.S. results in the destruction of a dozen political artworks.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/LAPD_Red_Squad_raid_on_John_Reed_Club_art_show\" title=\"LAPD Red Squad raid on John Reed Club art show\">LAPD Red Squad raid on John Reed Club art show</a> in the U.S. results in the destruction of a dozen political artworks.", "links": [{"title": "LAPD Red Squad raid on John Reed Club art show", "link": "https://wikipedia.org/wiki/LAPD_Red_Squad_raid_on_<PERSON>_Reed_Club_art_show"}]}, {"year": "1937", "text": "The Flint sit-down strike ends when General Motors recognizes the United Auto Workers trade union.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/Flint_sit-down_strike\" title=\"Flint sit-down strike\">Flint sit-down strike</a> ends when <a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> recognizes the <a href=\"https://wikipedia.org/wiki/United_Auto_Workers\" title=\"United Auto Workers\">United Auto Workers</a> trade union.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Flint_sit-down_strike\" title=\"Flint sit-down strike\">Flint sit-down strike</a> ends when <a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> recognizes the <a href=\"https://wikipedia.org/wiki/United_Auto_Workers\" title=\"United Auto Workers\">United Auto Workers</a> trade union.", "links": [{"title": "Flint sit-down strike", "link": "https://wikipedia.org/wiki/Flint_sit-down_strike"}, {"title": "General Motors", "link": "https://wikipedia.org/wiki/General_Motors"}, {"title": "United Auto Workers", "link": "https://wikipedia.org/wiki/United_Auto_Workers"}]}, {"year": "1938", "text": "BBC Television produces the world's first ever science fiction television programme, an adaptation of a section of the <PERSON><PERSON> play R.U.R., that coined the term \"robot\".", "html": "1938 - <a href=\"https://wikipedia.org/wiki/BBC_One\" title=\"BBC One\">BBC Television</a> produces the world's first ever <a href=\"https://wikipedia.org/wiki/Science_fiction_on_television\" title=\"Science fiction on television\">science fiction television</a> programme, an adaptation of a section of the <a href=\"https://wikipedia.org/wiki/Karel_%C4%8Capek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> play <i><a href=\"https://wikipedia.org/wiki/R.U.R.\" title=\"R.U.R.\">R.U.R.</a></i>, that coined the term \"<a href=\"https://wikipedia.org/wiki/Robot\" title=\"Robot\">robot</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/BBC_One\" title=\"BBC One\">BBC Television</a> produces the world's first ever <a href=\"https://wikipedia.org/wiki/Science_fiction_on_television\" title=\"Science fiction on television\">science fiction television</a> programme, an adaptation of a section of the <a href=\"https://wikipedia.org/wiki/Karel_%C4%8Capek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> play <i><a href=\"https://wikipedia.org/wiki/R.U.R.\" title=\"R.U.R.\">R.U.R.</a></i>, that coined the term \"<a href=\"https://wikipedia.org/wiki/Robot\" title=\"Robot\">robot</a>\".", "links": [{"title": "BBC One", "link": "https://wikipedia.org/wiki/BBC_One"}, {"title": "Science fiction on television", "link": "https://wikipedia.org/wiki/Science_fiction_on_television"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON>_%C4%8Capek"}, {"title": "R.U.R.", "link": "https://wikipedia.org/wiki/R.U.R."}, {"title": "Robot", "link": "https://wikipedia.org/wiki/Robot"}]}, {"year": "1942", "text": "World War II: Second day of the Battle of Bukit Timah is fought in Singapore.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Bukit_Timah\" title=\"Battle of Bukit Timah\">Battle of Bukit Timah</a> is fought in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Bukit_Timah\" title=\"Battle of Bukit Timah\">Battle of Bukit Timah</a> is fought in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Bukit Timah", "link": "https://wikipedia.org/wiki/Battle_of_Bukit_Timah"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}]}, {"year": "1946", "text": "The New Testament of the Revised Standard Version of the Bible, the first significant challenge to the Authorized King James Version, is published.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/New_Testament\" title=\"New Testament\">New Testament</a> of the <a href=\"https://wikipedia.org/wiki/Revised_Standard_Version\" title=\"Revised Standard Version\">Revised Standard Version</a> of the <a href=\"https://wikipedia.org/wiki/Bible\" title=\"Bible\">Bible</a>, the first significant challenge to the <a href=\"https://wikipedia.org/wiki/Authorized_King_James_Version\" class=\"mw-redirect\" title=\"Authorized King James Version\">Authorized King James Version</a>, is published.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_Testament\" title=\"New Testament\">New Testament</a> of the <a href=\"https://wikipedia.org/wiki/Revised_Standard_Version\" title=\"Revised Standard Version\">Revised Standard Version</a> of the <a href=\"https://wikipedia.org/wiki/Bible\" title=\"Bible\">Bible</a>, the first significant challenge to the <a href=\"https://wikipedia.org/wiki/Authorized_King_James_Version\" class=\"mw-redirect\" title=\"Authorized King James Version\">Authorized King James Version</a>, is published.", "links": [{"title": "New Testament", "link": "https://wikipedia.org/wiki/New_Testament"}, {"title": "Revised Standard Version", "link": "https://wikipedia.org/wiki/Revised_Standard_Version"}, {"title": "Bible", "link": "https://wikipedia.org/wiki/Bible"}, {"title": "Authorized King <PERSON>", "link": "https://wikipedia.org/wiki/Authorized_King_<PERSON>_Version"}]}, {"year": "1953", "text": "Cold War: U.S. President <PERSON> denies all appeals for clemency for <PERSON> and <PERSON>.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> denies all appeals for clemency for <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> denies all appeals for clemency for <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "Israeli-Soviet relations are severed.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Russia_relations\" title=\"Israel-Russia relations\">Israeli-Soviet relations</a> are severed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Russia_relations\" title=\"Israel-Russia relations\">Israeli-Soviet relations</a> are severed.", "links": [{"title": "Israel-Russia relations", "link": "https://wikipedia.org/wiki/Israel%E2%80%93Russia_relations"}]}, {"year": "1959", "text": "The Federation of Arab Emirates of the South is created as a protectorate of the United Kingdom.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/Federation_of_Arab_Emirates_of_the_South\" class=\"mw-redirect\" title=\"Federation of Arab Emirates of the South\">Federation of Arab Emirates of the South</a> is created as a <a href=\"https://wikipedia.org/wiki/Protectorate\" title=\"Protectorate\">protectorate</a> of the United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federation_of_Arab_Emirates_of_the_South\" class=\"mw-redirect\" title=\"Federation of Arab Emirates of the South\">Federation of Arab Emirates of the South</a> is created as a <a href=\"https://wikipedia.org/wiki/Protectorate\" title=\"Protectorate\">protectorate</a> of the United Kingdom.", "links": [{"title": "Federation of Arab Emirates of the South", "link": "https://wikipedia.org/wiki/Federation_of_Arab_Emirates_of_the_South"}, {"title": "Protectorate", "link": "https://wikipedia.org/wiki/Protectorate"}]}, {"year": "1963 — The Beatles recorded their first album Please Please Me[38]", "text": null, "html": "1963 — The Beatles recorded their first album Please Please Me[38] - <a href=\"https://wikipedia.org/wiki/1963\" title=\"1963\">1963</a> — <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> recorded their first album <i><a href=\"https://wikipedia.org/wiki/Please_Please_Me\" title=\"Please Please Me\">Please Please Me</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1963\" title=\"1963\">1963</a> — <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> recorded their first album <i><a href=\"https://wikipedia.org/wiki/Please_Please_Me\" title=\"Please Please Me\">Please Please Me</a></i>", "links": [{"title": "1963", "link": "https://wikipedia.org/wiki/1963"}, {"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}, {"title": "Please Please Me", "link": "https://wikipedia.org/wiki/Please_Please_Me"}]}, {"year": "1970", "text": "Japan launches Ohsumi, becoming the fourth nation to put an object into orbit using its own booster.", "html": "1970 - Japan launches <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(satellite)\" title=\"<PERSON><PERSON><PERSON> (satellite)\"><PERSON>sumi</a></i>, becoming the fourth nation to put an object into orbit using its own booster.", "no_year_html": "Japan launches <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(satellite)\" title=\"<PERSON><PERSON><PERSON> (satellite)\"><PERSON><PERSON><PERSON></a></i>, becoming the fourth nation to put an object into orbit using its own booster.", "links": [{"title": "Ohsumi (satellite)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(satellite)"}]}, {"year": "1971", "text": "Cold War: the Seabed Arms Control Treaty opened for signature outlawing nuclear weapons on the ocean floor in international waters.", "html": "1971 - Cold War: the <a href=\"https://wikipedia.org/wiki/Seabed_Arms_Control_Treaty\" title=\"Seabed Arms Control Treaty\">Seabed Arms Control Treaty</a> opened for signature outlawing <a href=\"https://wikipedia.org/wiki/Nuclear_weapon\" title=\"Nuclear weapon\">nuclear weapons</a> on the ocean floor in <a href=\"https://wikipedia.org/wiki/International_waters\" title=\"International waters\">international waters</a>.", "no_year_html": "Cold War: the <a href=\"https://wikipedia.org/wiki/Seabed_Arms_Control_Treaty\" title=\"Seabed Arms Control Treaty\">Seabed Arms Control Treaty</a> opened for signature outlawing <a href=\"https://wikipedia.org/wiki/Nuclear_weapon\" title=\"Nuclear weapon\">nuclear weapons</a> on the ocean floor in <a href=\"https://wikipedia.org/wiki/International_waters\" title=\"International waters\">international waters</a>.", "links": [{"title": "Seabed Arms Control Treaty", "link": "https://wikipedia.org/wiki/Seabed_Arms_Control_Treaty"}, {"title": "Nuclear weapon", "link": "https://wikipedia.org/wiki/Nuclear_weapon"}, {"title": "International waters", "link": "https://wikipedia.org/wiki/International_waters"}]}, {"year": "1978", "text": "Pacific Western Airlines Flight 314 crashes at the Cranbrook/Canadian Rockies International Airport in Cranbrook, British Columbia, Canada with 42 deaths and seven survivors.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Pacific_Western_Airlines_Flight_314\" title=\"Pacific Western Airlines Flight 314\">Pacific Western Airlines Flight 314</a> crashes at the <a href=\"https://wikipedia.org/wiki/Cranbrook/Canadian_Rockies_International_Airport\" title=\"Cranbrook/Canadian Rockies International Airport\">Cranbrook/Canadian Rockies International Airport</a> in <a href=\"https://wikipedia.org/wiki/Cranbrook,_British_Columbia\" title=\"Cranbrook, British Columbia\">Cranbrook, British Columbia</a>, Canada with 42 deaths and seven survivors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pacific_Western_Airlines_Flight_314\" title=\"Pacific Western Airlines Flight 314\">Pacific Western Airlines Flight 314</a> crashes at the <a href=\"https://wikipedia.org/wiki/Cranbrook/Canadian_Rockies_International_Airport\" title=\"Cranbrook/Canadian Rockies International Airport\">Cranbrook/Canadian Rockies International Airport</a> in <a href=\"https://wikipedia.org/wiki/Cranbrook,_British_Columbia\" title=\"Cranbrook, British Columbia\">Cranbrook, British Columbia</a>, Canada with 42 deaths and seven survivors.", "links": [{"title": "Pacific Western Airlines Flight 314", "link": "https://wikipedia.org/wiki/Pacific_Western_Airlines_Flight_314"}, {"title": "Cranbrook/Canadian Rockies International Airport", "link": "https://wikipedia.org/wiki/Cranbrook/Canadian_Rockies_International_Airport"}, {"title": "Cranbrook, British Columbia", "link": "https://wikipedia.org/wiki/Cranbrook,_British_Columbia"}]}, {"year": "1979", "text": "The Iranian Revolution establishes an Islamic theocracy under the leadership of <PERSON><PERSON><PERSON><PERSON>.", "html": "1979 - The <a href=\"https://wikipedia.org/wiki/Iranian_Revolution\" class=\"mw-redirect\" title=\"Iranian Revolution\">Iranian Revolution</a> establishes an Islamic theocracy under the leadership of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Ruh<PERSON><PERSON>_<PERSON>homeini\" title=\"Ruh<PERSON><PERSON>homeini\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Iranian_Revolution\" class=\"mw-redirect\" title=\"Iranian Revolution\">Iranian Revolution</a> establishes an Islamic theocracy under the leadership of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_<PERSON>homeini\" title=\"<PERSON><PERSON><PERSON><PERSON>homeini\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Iranian Revolution", "link": "https://wikipedia.org/wiki/Iranian_Revolution"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>ollah_Khomeini"}]}, {"year": "1990", "text": "<PERSON> is released from Victor <PERSON> Prison outside Cape Town, South Africa after 27 years as a political prisoner.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is released from <a href=\"https://wikipedia.org/wiki/Drakenstein_Correctional_Centre\" title=\"Drakenstein Correctional Centre\">Victor <PERSON> Prison</a> outside <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>, South Africa after 27 years as a political prisoner.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is released from <a href=\"https://wikipedia.org/wiki/Drakenstein_Correctional_Centre\" title=\"Drakenstein Correctional Centre\">Victor <PERSON> Prison</a> outside <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>, South Africa after 27 years as a political prisoner.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Mandela"}, {"title": "Drakenstein Correctional Centre", "link": "https://wikipedia.org/wiki/Drakenstein_Correctional_Centre"}, {"title": "Cape Town", "link": "https://wikipedia.org/wiki/Cape_Town"}]}, {"year": "1990", "text": "<PERSON>, a 42:1 underdog, knocks out <PERSON> in ten rounds at Tokyo to win boxing's world Heavyweight title.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a 42:1 underdog, <a href=\"https://wikipedia.org/wiki/<PERSON>_vs._<PERSON>_<PERSON>\" title=\"<PERSON> vs. <PERSON>\">knocks out</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in ten rounds at Tokyo to win <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a>'s world Heavyweight title.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a 42:1 underdog, <a href=\"https://wikipedia.org/wiki/<PERSON>_vs._<PERSON>_<PERSON>\" title=\"<PERSON> vs. <PERSON>\">knocks out</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in ten rounds at Tokyo to win <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a>'s world Heavyweight title.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> vs. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_vs._<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Boxing", "link": "https://wikipedia.org/wiki/Boxing"}]}, {"year": "1997", "text": "Space Shuttle Discovery is launched on a mission to service the Hubble Space Telescope.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on a <a href=\"https://wikipedia.org/wiki/STS-82\" title=\"STS-82\">mission</a> to service the <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on a <a href=\"https://wikipedia.org/wiki/STS-82\" title=\"STS-82\">mission</a> to service the <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a>.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-82", "link": "https://wikipedia.org/wiki/STS-82"}, {"title": "<PERSON>bble Space Telescope", "link": "https://wikipedia.org/wiki/Hubble_Space_Telescope"}]}, {"year": "1999", "text": "Pluto crosses Neptune's orbit, ending a nearly 20-year period when it was closer to the Sun than the gas giant; Pluto is not expected to interact with Neptune's orbit again until 2231.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> crosses Neptune's orbit, ending a nearly 20-year period when it was closer to the Sun than the gas giant; Pluto is not expected to interact with Neptune's orbit again until 2231.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> crosses Neptune's orbit, ending a nearly 20-year period when it was closer to the Sun than the gas giant; Pluto is not expected to interact with Neptune's orbit again until 2231.", "links": [{"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}]}, {"year": "2000", "text": "Space Shuttle Endeavour is launched on STS-99 to conduct the Shuttle Radar Topography Mission.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-99\" title=\"STS-99\">STS-99</a> to conduct the <a href=\"https://wikipedia.org/wiki/Shuttle_Radar_Topography_Mission\" title=\"Shuttle Radar Topography Mission\">Shuttle Radar Topography Mission</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-99\" title=\"STS-99\">STS-99</a> to conduct the <a href=\"https://wikipedia.org/wiki/Shuttle_Radar_Topography_Mission\" title=\"Shuttle Radar Topography Mission\">Shuttle Radar Topography Mission</a>.", "links": [{"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-99", "link": "https://wikipedia.org/wiki/STS-99"}, {"title": "Shuttle Radar Topography Mission", "link": "https://wikipedia.org/wiki/Shuttle_Radar_Topography_Mission"}]}, {"year": "2001", "text": "A Dutch programmer launched the <PERSON> virus infecting millions of emails via a trick photo of the tennis star.", "html": "2001 - A Dutch programmer launched the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_virus)\" title=\"<PERSON> (computer virus)\"><PERSON> virus</a> infecting millions of <a href=\"https://wikipedia.org/wiki/Email\" title=\"Email\">emails</a> via a trick photo of the <a href=\"https://wikipedia.org/wiki/Tennis\" title=\"Tennis\">tennis</a> star.", "no_year_html": "A Dutch programmer launched the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_virus)\" title=\"<PERSON> (computer virus)\"><PERSON> virus</a> infecting millions of <a href=\"https://wikipedia.org/wiki/Email\" title=\"Email\">emails</a> via a trick photo of the <a href=\"https://wikipedia.org/wiki/Tennis\" title=\"Tennis\">tennis</a> star.", "links": [{"title": "<PERSON> (computer virus)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_virus)"}, {"title": "Email", "link": "https://wikipedia.org/wiki/Email"}, {"title": "Tennis", "link": "https://wikipedia.org/wiki/Tennis"}]}, {"year": "2008", "text": "Rebel East Timorese soldiers seriously wound President <PERSON>. Rebel leader <PERSON> is killed in the attack.", "html": "2008 - Rebel <a href=\"https://wikipedia.org/wiki/East_Timor\" class=\"mw-redirect\" title=\"East Timor\">East Timorese</a> soldiers <a href=\"https://wikipedia.org/wiki/2008_East_Timorese_assassination_attempts\" title=\"2008 East Timorese assassination attempts\">seriously wound</a> President <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. Rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed in the attack.", "no_year_html": "Rebel <a href=\"https://wikipedia.org/wiki/East_Timor\" class=\"mw-redirect\" title=\"East Timor\">East Timorese</a> soldiers <a href=\"https://wikipedia.org/wiki/2008_East_Timorese_assassination_attempts\" title=\"2008 East Timorese assassination attempts\">seriously wound</a> President <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. Rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed in the attack.", "links": [{"title": "East Timor", "link": "https://wikipedia.org/wiki/East_Timor"}, {"title": "2008 East Timorese assassination attempts", "link": "https://wikipedia.org/wiki/2008_East_Timorese_assassination_attempts"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>-<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "Arab Spring: The first wave of the Egyptian revolution culminates in the resignation of <PERSON><PERSON><PERSON> and the transfer of power to the Supreme Military Council after 17 days of protests.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Arab_Spring\" title=\"Arab Spring\">Arab Spring</a>: The <a href=\"https://wikipedia.org/wiki/Timeline_of_the_Egyptian_revolution_of_2011\" title=\"Timeline of the Egyptian revolution of 2011\">first wave</a> of the <a href=\"https://wikipedia.org/wiki/2011_Egyptian_revolution\" title=\"2011 Egyptian revolution\">Egyptian revolution</a> culminates in the resignation of <a href=\"https://wikipedia.org/wiki/Hos<PERSON>_Mu<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and the transfer of power to the Supreme Military Council after 17 days of protests.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arab_Spring\" title=\"Arab Spring\">Arab Spring</a>: The <a href=\"https://wikipedia.org/wiki/Timeline_of_the_Egyptian_revolution_of_2011\" title=\"Timeline of the Egyptian revolution of 2011\">first wave</a> of the <a href=\"https://wikipedia.org/wiki/2011_Egyptian_revolution\" title=\"2011 Egyptian revolution\">Egyptian revolution</a> culminates in the resignation of <a href=\"https://wikipedia.org/wiki/Hos<PERSON>_Mubara<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mu<PERSON>\"><PERSON><PERSON><PERSON></a> and the transfer of power to the Supreme Military Council after 17 days of protests.", "links": [{"title": "Arab Spring", "link": "https://wikipedia.org/wiki/Arab_Spring"}, {"title": "Timeline of the Egyptian revolution of 2011", "link": "https://wikipedia.org/wiki/Timeline_of_the_Egyptian_revolution_of_2011"}, {"title": "2011 Egyptian revolution", "link": "https://wikipedia.org/wiki/2011_Egyptian_revolution"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>k"}]}, {"year": "2013", "text": "The Vatican confirmed that <PERSON> <PERSON> would resign the papacy as a result of his advanced age.", "html": "2013 - The Vatican confirmed that <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> would <a href=\"https://wikipedia.org/wiki/Resignation_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Resignation of <PERSON> <PERSON>\">resign the papacy</a> as a result of his advanced age.", "no_year_html": "The Vatican confirmed that <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> would <a href=\"https://wikipedia.org/wiki/Resignation_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Resignation of <PERSON> <PERSON>\">resign the papacy</a> as a result of his advanced age.", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Resignation of <PERSON>", "link": "https://wikipedia.org/wiki/Resignation_of_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "Militants claiming to be from the Sultanate of Sulu invade Lahad Datu District, Sabah, Malaysia, beginning the Lahad Datu standoff.", "html": "2013 - Militants claiming to be from the <a href=\"https://wikipedia.org/wiki/Sultanate_of_Sulu\" title=\"Sultanate of Sulu\">Sultanate of Sulu</a> invade <a href=\"https://wikipedia.org/wiki/Lahad_Datu_District\" title=\"Lahad Datu District\">Lahad Datu District</a>, <a href=\"https://wikipedia.org/wiki/Sabah\" title=\"Sabah\">Sabah</a>, Malaysia, beginning the <a href=\"https://wikipedia.org/wiki/2013_Lahad_Datu_standoff\" title=\"2013 Lahad Datu standoff\">Lahad Datu standoff</a>.", "no_year_html": "Militants claiming to be from the <a href=\"https://wikipedia.org/wiki/Sultanate_of_Sulu\" title=\"Sultanate of Sulu\">Sultanate of Sulu</a> invade <a href=\"https://wikipedia.org/wiki/Lahad_Datu_District\" title=\"Lahad Datu District\">Lahad Datu District</a>, <a href=\"https://wikipedia.org/wiki/Sabah\" title=\"Sabah\">Sabah</a>, Malaysia, beginning the <a href=\"https://wikipedia.org/wiki/2013_Lahad_Datu_standoff\" title=\"2013 Lahad Datu standoff\">Lahad Datu standoff</a>.", "links": [{"title": "Sultanate of Sulu", "link": "https://wikipedia.org/wiki/Sultanate_of_Sulu"}, {"title": "Lahad Datu District", "link": "https://wikipedia.org/wiki/Lahad_Datu_District"}, {"title": "Sabah", "link": "https://wikipedia.org/wiki/Sabah"}, {"title": "2013 <PERSON><PERSON> standoff", "link": "https://wikipedia.org/wiki/2013_<PERSON><PERSON>_<PERSON><PERSON>_standoff"}]}, {"year": "2014", "text": "A military transport plane crashes in a mountainous area of Oum El Bouaghi Province in eastern Algeria, killing 77 people.", "html": "2014 - A military transport <a href=\"https://wikipedia.org/wiki/2014_Algerian_Air_Force_C-130_crash\" title=\"2014 Algerian Air Force C-130 crash\">plane crashes</a> in a mountainous area of <a href=\"https://wikipedia.org/wiki/Oum_El_Bouaghi_Province\" title=\"Oum El Bouaghi Province\">Oum El Bouaghi Province</a> in eastern <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>, killing 77 people.", "no_year_html": "A military transport <a href=\"https://wikipedia.org/wiki/2014_Algerian_Air_Force_C-130_crash\" title=\"2014 Algerian Air Force C-130 crash\">plane crashes</a> in a mountainous area of <a href=\"https://wikipedia.org/wiki/Oum_El_Bouaghi_Province\" title=\"Oum El Bouaghi Province\">Oum El Bouaghi Province</a> in eastern <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>, killing 77 people.", "links": [{"title": "2014 Algerian Air Force C-130 crash", "link": "https://wikipedia.org/wiki/2014_Algerian_Air_Force_C-130_crash"}, {"title": "Oum El Bouaghi Province", "link": "https://wikipedia.org/wiki/Oum_El_Bouaghi_Province"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "2015", "text": "A university student was murdered as she resisted an attempted rape in Turkey, sparking nationwide protests and public outcry against harassment and violence against women.", "html": "2015 - A university student was <a href=\"https://wikipedia.org/wiki/Murder_of_%C3%96zgecan_Aslan\" title=\"Murder of Özgecan Aslan\">murdered</a> as she resisted an attempted rape in <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, sparking nationwide protests and public outcry against harassment and <a href=\"https://wikipedia.org/wiki/Violence_against_women\" title=\"Violence against women\">violence against women</a>.", "no_year_html": "A university student was <a href=\"https://wikipedia.org/wiki/Murder_of_%C3%96zgecan_Aslan\" title=\"Murder of Özgecan Aslan\">murdered</a> as she resisted an attempted rape in <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, sparking nationwide protests and public outcry against harassment and <a href=\"https://wikipedia.org/wiki/Violence_against_women\" title=\"Violence against women\">violence against women</a>.", "links": [{"title": "Murder of Özgecan <PERSON>lan", "link": "https://wikipedia.org/wiki/Murder_of_%C3%96zgecan_Aslan"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Violence against women", "link": "https://wikipedia.org/wiki/Violence_against_women"}]}, {"year": "2016", "text": "A man shoots seven people dead at an education center in Jizan Province, Saudi Arabia.", "html": "2016 - A man <a href=\"https://wikipedia.org/wiki/2016_Ad_Dair_shooting\" title=\"2016 Ad Dair shooting\">shoots</a> seven people dead at an education center in <a href=\"https://wikipedia.org/wiki/Jazan_Province\" title=\"Jazan Province\">Jizan Province</a>, Saudi Arabia.", "no_year_html": "A man <a href=\"https://wikipedia.org/wiki/2016_Ad_Dair_shooting\" title=\"2016 Ad Dair shooting\">shoots</a> seven people dead at an education center in <a href=\"https://wikipedia.org/wiki/Jazan_Province\" title=\"Jazan Province\">Jizan Province</a>, Saudi Arabia.", "links": [{"title": "2016 Ad Dair shooting", "link": "https://wikipedia.org/wiki/2016_<PERSON>_<PERSON>_shooting"}, {"title": "Jazan Province", "link": "https://wikipedia.org/wiki/Jazan_Province"}]}, {"year": "2017", "text": "North Korea test fires a ballistic missile across the Sea of Japan.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> <a href=\"https://wikipedia.org/wiki/Pukguksong-2\" title=\"Pukguksong-2\">test fires a ballistic missile</a> across the <a href=\"https://wikipedia.org/wiki/Sea_of_Japan\" title=\"Sea of Japan\">Sea of Japan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> <a href=\"https://wikipedia.org/wiki/Pukguksong-2\" title=\"Pukguksong-2\">test fires a ballistic missile</a> across the <a href=\"https://wikipedia.org/wiki/Sea_of_Japan\" title=\"Sea of Japan\">Sea of Japan</a>.", "links": [{"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Pukguksong-2", "link": "https://wikipedia.org/wiki/Pukguksong-2"}, {"title": "Sea of Japan", "link": "https://wikipedia.org/wiki/Sea_of_Japan"}]}, {"year": "2018", "text": "Saratov Airlines Flight 703 crashes near Moscow, Russia with 71 deaths and no survivors.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Saratov_Airlines_Flight_703\" title=\"Saratov Airlines Flight 703\">Saratov Airlines Flight 703</a> crashes near <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>, Russia with 71 deaths and no survivors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saratov_Airlines_Flight_703\" title=\"Saratov Airlines Flight 703\">Saratov Airlines Flight 703</a> crashes near <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>, Russia with 71 deaths and no survivors.", "links": [{"title": "Saratov Airlines Flight 703", "link": "https://wikipedia.org/wiki/Saratov_Airlines_Flight_703"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}]}, {"year": "2020", "text": "COVID-19 pandemic: The World Health Organization officially names the coronavirus outbreak as COVID-19, with the virus being designated SARS-CoV-2.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>: The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> officially names the coronavirus outbreak as <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a>, with the virus being designated <a href=\"https://wikipedia.org/wiki/Severe_acute_respiratory_syndrome_coronavirus_2\" class=\"mw-redirect\" title=\"Severe acute respiratory syndrome coronavirus 2\">SARS-CoV-2</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>: The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> officially names the coronavirus outbreak as <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a>, with the virus being designated <a href=\"https://wikipedia.org/wiki/Severe_acute_respiratory_syndrome_coronavirus_2\" class=\"mw-redirect\" title=\"Severe acute respiratory syndrome coronavirus 2\">SARS-CoV-2</a>.", "links": [{"title": "COVID-19 pandemic", "link": "https://wikipedia.org/wiki/COVID-19_pandemic"}, {"title": "World Health Organization", "link": "https://wikipedia.org/wiki/World_Health_Organization"}, {"title": "COVID-19", "link": "https://wikipedia.org/wiki/COVID-19"}, {"title": "Severe acute respiratory syndrome coronavirus 2", "link": "https://wikipedia.org/wiki/Severe_acute_respiratory_syndrome_coronavirus_2"}]}, {"year": "2024", "text": "2024 Finnish presidential election: <PERSON> is elected as the 13th president of Finland.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024_Finnish_presidential_election\" title=\"2024 Finnish presidential election\">2024 Finnish presidential election</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as the <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">13th president of Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024_Finnish_presidential_election\" title=\"2024 Finnish presidential election\">2024 Finnish presidential election</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as the <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">13th president of Finland</a>.", "links": [{"title": "2024 Finnish presidential election", "link": "https://wikipedia.org/wiki/2024_Finnish_presidential_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}], "Births": [{"year": "1380", "text": "<PERSON><PERSON>, Italian scholar and translator (d. 1459)", "html": "1380 - <a href=\"https://wikipedia.org/wiki/Po<PERSON>_Bracciolini\" title=\"Poggio Bracciolini\"><PERSON><PERSON> Bracciolini</a>, Italian scholar and translator (d. 1459)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Po<PERSON>_Bracciolini\" title=\"Poggio Bracciolini\"><PERSON><PERSON></a>, Italian scholar and translator (d. 1459)", "links": [{"title": "Poggio Bracciolini", "link": "https://wikipedia.org/wiki/Po<PERSON>_<PERSON>lini"}]}, {"year": "1466", "text": "<PERSON> York (d. 1503)", "html": "1466 - <a href=\"https://wikipedia.org/wiki/Elizabeth_of_York\" title=\"Elizabeth of York\"><PERSON> York</a> (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_of_York\" title=\"Elizabeth of York\"><PERSON> York</a> (d. 1503)", "links": [{"title": "<PERSON> of York", "link": "https://wikipedia.org/wiki/Elizabeth_of_York"}]}, {"year": "1535", "text": "<PERSON> (d. 1591)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XIV\" title=\"Pope Gregory XIV\"><PERSON> <PERSON></a> (d. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory XIV\"><PERSON></a> (d. 1591)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1568", "text": "<PERSON><PERSON>, French author and playwright (d. 1625)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_d%27Urf%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and playwright (d. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_d%27Urf%C3%A9\" title=\"<PERSON><PERSON>'Urf<PERSON>\"><PERSON><PERSON></a>, French author and playwright (d. 1625)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Honor%C3%A9_d%27Urf%C3%A9"}]}, {"year": "1649", "text": "<PERSON>, Scottish minister and academic (d. 1715)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and academic (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and academic (d. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1657", "text": "<PERSON>, French poet and playwright (d. 1757)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and playwright (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and playwright (d. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1708", "text": "<PERSON><PERSON><PERSON>, Italian composer (d. 1775)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (d. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (d. 1775)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Egi<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON><PERSON><PERSON>, French poet and playwright (d. 1811)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9nier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and playwright (d. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9nier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and playwright (d. 1811)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9nier"}]}, {"year": "1776", "text": "<PERSON><PERSON><PERSON>, Greek politician, 1st Governor of Greece (d. 1831)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece\" title=\"List of heads of state of Greece\">Governor of Greece</a> (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece\" title=\"List of heads of state of Greece\">Governor of Greece</a> (d. 1831)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of Greece", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece"}]}, {"year": "1800", "text": "<PERSON>, English photographer and politician, invented the calotype (d. 1877)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and politician, invented the <a href=\"https://wikipedia.org/wiki/Calotype\" title=\"Calotype\">calotype</a> (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and politician, invented the <a href=\"https://wikipedia.org/wiki/Calotype\" title=\"Calotype\">calotype</a> (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Calotype", "link": "https://wikipedia.org/wiki/Calotype"}]}, {"year": "1802", "text": "<PERSON>, American journalist, author, and activist (d. 1880)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and activist (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and activist (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, Native American-French Canadian explorer (d. 1866)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American-French Canadian explorer (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American-French Canadian explorer (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, American lawyer and politician, Vice President of the Confederate States of America (d. 1883)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Confederate_States_of_America\" title=\"Vice President of the Confederate States of America\">Vice President of the Confederate States of America</a> (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Confederate_States_of_America\" title=\"Vice President of the Confederate States of America\">Vice President of the Confederate States of America</a> (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the Confederate States of America", "link": "https://wikipedia.org/wiki/Vice_President_of_the_Confederate_States_of_America"}]}, {"year": "1813", "text": "<PERSON>, German author, playwright, and critic (d. 1865)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, German author, playwright, and critic (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, German author, playwright, and critic (d. 1865)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1821", "text": "<PERSON>, French archaeologist and scholar (d. 1881)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archaeologist and scholar (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archaeologist and scholar (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Prussian pianist and composer (d. 1913)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian pianist and composer (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_von_<PERSON>\" title=\"<PERSON>\"><PERSON> von <PERSON></a>, Prussian pianist and composer (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, American lawyer and jurist, 8th Chief Justice of the United States (d. 1910)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, 8th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, 8th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1839", "text": "<PERSON>, American physicist (d. 1903)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON>, Ottoman soldier and politician, Grand Vizier of the Ottoman Empire (d. 1936)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (d. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1847", "text": "<PERSON>, American engineer and businessman, developed the light bulb and phonograph (d. 1931)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas <PERSON>\"><PERSON></a>, American engineer and businessman, developed the <a href=\"https://wikipedia.org/wiki/Light_bulb\" class=\"mw-redirect\" title=\"Light bulb\">light bulb</a> and <a href=\"https://wikipedia.org/wiki/Phonograph\" title=\"Phonograph\">phonograph</a> (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Edison\"><PERSON></a>, American engineer and businessman, developed the <a href=\"https://wikipedia.org/wiki/Light_bulb\" class=\"mw-redirect\" title=\"Light bulb\">light bulb</a> and <a href=\"https://wikipedia.org/wiki/Phonograph\" title=\"Phonograph\">phonograph</a> (d. 1931)", "links": [{"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Light bulb", "link": "https://wikipedia.org/wiki/Light_bulb"}, {"title": "Phonograph", "link": "https://wikipedia.org/wiki/Phonograph"}]}, {"year": "1855", "text": "<PERSON>, American painter and author (d. 1940)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, French author and playwright (d. 1953)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and playwright (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and playwright (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e"}]}, {"year": "1863", "text": "<PERSON>, American politician; Mayor of Boston (d. 1950)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician; <a href=\"https://wikipedia.org/wiki/Mayor_of_Boston\" title=\"Mayor of Boston\">Mayor of Boston</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician; <a href=\"https://wikipedia.org/wiki/Mayor_of_Boston\" title=\"Mayor of Boston\">Mayor of Boston</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Boston", "link": "https://wikipedia.org/wiki/Mayor_of_Boston"}]}, {"year": "1864", "text": "<PERSON>, French chemist (d. 1909)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Bouveault"}]}, {"year": "1869", "text": "<PERSON><PERSON>, German-Dutch art collector and philanthropist, founded the Kröller-Müller Museum (d. 1939)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kr%C3%B6ller-M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Dutch art collector and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Kr%C3%B6ller-M%C3%BCller_Museum\" title=\"Kröller-Müller Museum\">Kröller-Müller Museum</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r%C3%B6ller-M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Dutch art collector and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Kr%C3%B6ller-M%C3%BCller_Museum\" title=\"Kröller-Müller Museum\">Kröller-Müller Museum</a> (d. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Helen<PERSON>_Kr%C3%B6ller-M%C3%BCller"}, {"title": "Kröller-Müller Museum", "link": "https://wikipedia.org/wiki/Kr%C3%B6ller-M%C3%BCller_Museum"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German poet and author (d. 1945)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>h%C3%<PERSON>ler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and author (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>h%C3%<PERSON>ler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and author (d. 1945)", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-Sch%C3%BCler"}]}, {"year": "1874", "text": "<PERSON>, Swedish author and illustrator (d. 1953)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish author and illustrator (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish author and illustrator (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elsa_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON>, Norwegian midwife (d. 1968)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian midwife (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian midwife (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Italian painter (d. 1966)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A0\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A0\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A0"}]}, {"year": "1888", "text": "<PERSON>, American educator, college administrator, and civil rights leader (d. 1980)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(college_president)\" title=\"<PERSON> (college president)\"><PERSON></a>, American educator, college administrator, and civil rights leader (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(college_president)\" title=\"<PERSON> (college president)\"><PERSON></a>, American educator, college administrator, and civil rights leader (d. 1980)", "links": [{"title": "<PERSON> (college president)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(college_president)"}]}, {"year": "1897", "text": "<PERSON>, Polish-American mathematician and logician (d. 1954)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American mathematician and logician (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American mathematician and logician (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Hungarian-American physicist and academic (d. 1964)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American physicist and academic (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American physicist and academic (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Danish nurse, pioneer in nursing education (d. 1994)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish nurse, pioneer in nursing education (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish nurse, pioneer in nursing education (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, German philosopher and scholar (d. 2002)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German philosopher and scholar (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German philosopher and scholar (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Japanese educator and activist (d. 1958)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/J%C5%8Dsei_Toda\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese educator and activist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C5%8Dsei_Toda\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese educator and activist (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C5%8Dsei_Toda"}]}, {"year": "1902", "text": "<PERSON><PERSON>, Danish architect, designed Radisson Blu Royal Hotel (d. 1971)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish architect, designed <a href=\"https://wikipedia.org/wiki/Ra<PERSON>son_Blu_Royal_Hotel,_Copenhagen\" class=\"mw-redirect\" title=\"Radisson Blu Royal Hotel, Copenhagen\">Radisson Blu Royal Hotel</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish architect, designed <a href=\"https://wikipedia.org/wiki/Ra<PERSON>son_Blu_Royal_Hotel,_Copenhagen\" class=\"mw-redirect\" title=\"Radisson Blu Royal Hotel, Copenhagen\">Radisson Blu Royal Hotel</a> (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Radisson Blu Royal Hotel, Copenhagen", "link": "https://wikipedia.org/wiki/Ra<PERSON>son_Blu_Royal_Hotel,_Copenhagen"}]}, {"year": "1904", "text": "<PERSON>, New Zealand farmer and politician, 26th Prime Minister of New Zealand (d. 1983)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand farmer and politician, 26th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand farmer and politician, 26th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1904", "text": "<PERSON><PERSON>, French supercentenarian (d. 2023)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lucile <PERSON>\"><PERSON><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Supercentenarian\" title=\"Supercentenarian\">supercentenarian</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucile_<PERSON>\" title=\"Lucile <PERSON>on\"><PERSON><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Supercentenarian\" title=\"Supercentenarian\">supercentenarian</a> (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucile_<PERSON>on"}, {"title": "Supercentenarian", "link": "https://wikipedia.org/wiki/Supercentenarian"}]}, {"year": "1908", "text": "<PERSON>, American screenwriter (d. 1992)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter (d. 1992)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1908", "text": "<PERSON>, English explorer (d. 1999)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American boxer and actor (d. 1959)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer and actor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer and actor (d. 1959)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}]}, {"year": "1909", "text": "<PERSON>, American director, producer, and screenwriter (d. 1993)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Czech-American pianist and educator (d. 1994)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1n%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech-American pianist and educator (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1n%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech-American pianist and educator (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rudolf_Firku%C5%A1n%C3%BD"}]}, {"year": "1914", "text": "<PERSON>, American singer-songwriter and pianist (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American blues singer-songwriter and guitarist (d. 1969)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues singer-songwriter and guitarist (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues singer-songwriter and guitarist (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English soldier, author, and scholar (d. 2011)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, author, and scholar (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, author, and scholar (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American mathematician and academic (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American author and screenwriter (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Hungarian-American actress (d. 1995)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actress (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON> of Egypt (d. 1965)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Farouk_of_Egypt\" title=\"Farouk of Egypt\">Farouk of Egypt</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Farouk_of_Egypt\" title=\"Farouk of Egypt\"><PERSON><PERSON><PERSON> of Egypt</a> (d. 1965)", "links": [{"title": "Far<PERSON>k of Egypt", "link": "https://wikipedia.org/wiki/Farouk_of_Egypt"}]}, {"year": "1920", "text": "<PERSON>, American author (d. 1976)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor (d. 1976)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Jr., American general and pilot (d. 1978)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and pilot (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and pilot (d. 1978)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1921", "text": "<PERSON>, American politician, 69th United States Secretary of the Treasury (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 69th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 69th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Italian hurdler and fashion designer, founded <PERSON><PERSON> (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian hurdler and fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian hurdler and fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English philosopher and academic (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lew"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American tennis player (d. 2021)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Patty\"><PERSON><PERSON></a>, American tennis player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Patty\"><PERSON><PERSON></a>, American tennis player (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON> <PERSON><PERSON>, American psychologist and academic (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>_<PERSON>\" title=\"Virginia E. Johnson\"><PERSON> <PERSON><PERSON></a>, American psychologist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>_<PERSON>\" title=\"Virginia E<PERSON> Johnson\"><PERSON> <PERSON><PERSON></a>, American psychologist and academic (d. 2013)", "links": [{"title": "Virginia <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actress (d. 2001)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, French chef (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Canadian-American actor and producer (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American painter and academic (d. 2007).", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2007).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2007).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, British fashion designer (d. 2023)[notes 1]", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British fashion designer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Quant\"><PERSON></a>, British fashion designer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>uant"}]}, {"year": "1932", "text": "<PERSON>, English miner and politician", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English miner and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English miner and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American lawyer and politician, 51st Governor of Missouri (d. 2000)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Missouri\" class=\"mw-redirect\" title=\"List of Governors of Missouri\">Governor of Missouri</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Missouri\" class=\"mw-redirect\" title=\"List of Governors of Missouri\">Governor of Missouri</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Missouri", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Missouri"}]}, {"year": "1934", "text": "<PERSON>, American actress and singer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Panamanian general and politician, Military leader of Panama (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Panama\" title=\"List of heads of state of Panama\">Military leader of Panama</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Panama\" title=\"List of heads of state of Panama\">Military leader of Panama</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of heads of state of Panama", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Panama"}]}, {"year": "1934", "text": "<PERSON>, English veterinarian and television host (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(veterinarian)\" class=\"mw-redirect\" title=\"<PERSON> (veterinarian)\"><PERSON></a>, English veterinarian and television host (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(veterinarian)\" class=\"mw-redirect\" title=\"<PERSON> (veterinarian)\"><PERSON></a>, English veterinarian and television host (d. 2013)", "links": [{"title": "<PERSON> (veterinarian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(veterinarian)"}]}, {"year": "1935", "text": "<PERSON>, American singer and guitarist (d. 1971)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor and director (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, British politician (d. 1990)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Australian cricketer and sportscaster", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Canadian ice hockey player (d. 2020)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Eddie Shack\"><PERSON></a>, Canadian ice hockey player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer and guitarist (d. 2010)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and guitarist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and guitarist (d. 2010)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1938", "text": "<PERSON><PERSON>, New Zealand cricketer (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>don"}]}, {"year": "1939", "text": "<PERSON>, American songwriter (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American basketball player (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian pianist and composer (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian pianist and composer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian pianist and composer (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9rg<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter (d. 2016)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Clay\"><PERSON></a>, American singer-songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Spanish singer and actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Spanish singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Spanish singer and actor", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "1943", "text": "<PERSON>, American trumpet player (d. 2011)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American lawyer and politician (d. 2016)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American novelist, short story writer, and essayist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_writer)\" title=\"<PERSON> (American writer)\"><PERSON></a>, American novelist, short story writer, and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_writer)\" title=\"<PERSON> (American writer)\"><PERSON></a>, American novelist, short story writer, and essayist", "links": [{"title": "<PERSON> (American writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_writer)"}]}, {"year": "1946", "text": "<PERSON>, Scottish footballer and manager (d. 2007)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Japanese engineer and politician and Prime Minister of Japan", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese engineer and politician and Prime Minister of Japan", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese engineer and politician and Prime Minister of Japan", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Scottish singer-songwriter and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American basketball player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American politician, 14th Governor of Utah", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Utah\" class=\"mw-redirect\" title=\"Governor of Utah\">Governor of Utah</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Utah\" class=\"mw-redirect\" title=\"Governor of Utah\">Governor of Utah</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Utah", "link": "https://wikipedia.org/wiki/Governor_of_Utah"}]}, {"year": "1953", "text": "<PERSON>, American actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American banker and politician, 43rd Governor of Florida", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American banker and politician, 43rd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Florida\" class=\"mw-redirect\" title=\"List of Governors of Florida\">Governor of Florida</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American banker and politician, 43rd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Florida\" class=\"mw-redirect\" title=\"List of Governors of Florida\">Governor of Florida</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Governors of Florida", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Florida"}]}, {"year": "1953", "text": "<PERSON>, American baseball player (d. 2014)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American director and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, French violinist (d. 2018)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French violinist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French violinist (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Indian actress and chairperson", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actress and chairperson", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actress and chairperson", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Brazilian race car driver", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American engineer and astronaut", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American lawyer and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American politician, 9th Governor of Alaska", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Alaska\" class=\"mw-redirect\" title=\"List of Governors of Alaska\">Governor of Alaska</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Alaska\" class=\"mw-redirect\" title=\"List of Governors of Alaska\">Governor of Alaska</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Alaska", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Alaska"}]}, {"year": "1964", "text": "<PERSON>, American martial artist and wrestler", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American martial artist and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American martial artist and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Australian netball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian netball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Italian footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Ciro_Ferrara\" title=\"<PERSON>iro Ferrara\"><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ciro_Ferrara\" title=\"Ciro Ferrara\"><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ciro_Ferrara"}]}, {"year": "1968", "text": "<PERSON>, American author and illustrator", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Swiss footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Nigerian-English footballer, manager, and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-English footballer, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-English footballer, manager, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American surfer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Norwegian guitarist and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Varg_<PERSON>ike<PERSON>\" title=\"Varg <PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Varg_<PERSON>\" title=\"Varg <PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian guitarist and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Varg_<PERSON>s"}]}, {"year": "1974", "text": "<PERSON>, English footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, guitarist, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/D%27Angelo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%27Angelo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%27Angelo"}]}, {"year": "1974", "text": "<PERSON>, American radio show host and conspiracy theorist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio show host and conspiracy theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio show host and conspiracy theorist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor and football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0pa%C4%8Dek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0pa%C4%8Dek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0pa%C4%8Dek"}]}, {"year": "1975", "text": "<PERSON>, American race car driver", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American musician and artist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>y_Norwood"}]}, {"year": "1980", "text": "<PERSON>, American actor and singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ledge"}]}, {"year": "1982", "text": "<PERSON>, English actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Slovak biathlete", "html": "1982 - <a href=\"https://wikipedia.org/wiki/%C4%BDubom%C3%ADra_Kalinov%C3%A1\" title=\"Ľubom<PERSON><PERSON>\">Ľubom<PERSON><PERSON></a>, Slovak biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%BDubom%C3%ADra_Kalinov%C3%A1\" title=\"Ľubom<PERSON><PERSON>\"><PERSON>ub<PERSON><PERSON><PERSON></a>, Slovak biathlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%BDubom%C3%ADra_Kalinov%C3%A1"}]}, {"year": "1982", "text": "<PERSON>, Australian snooker player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Dutch footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Dutch sprinter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Maarten_Heisen\" title=\"Maarten Heisen\"><PERSON><PERSON><PERSON></a>, Dutch sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maarten_Heisen\" title=\"Maarten Heisen\"><PERSON><PERSON><PERSON></a>, Dutch sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>isen"}]}, {"year": "1984", "text": "<PERSON>, Italian cyclist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer and reality television personality", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Day\" title=\"Aubrey O'Day\"><PERSON></a>, American singer and reality television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Day\" title=\"<PERSON> O'Day\"><PERSON></a>, American singer and reality television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Aubrey_O%27Day"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Talbot"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Czech skier", "html": "1985 - <a href=\"https://wikipedia.org/wiki/%C5%A0%C3%A1rka_Strachov%C3%A1\" title=\"<PERSON><PERSON><PERSON> Strachová\"><PERSON><PERSON><PERSON>rachov<PERSON></a>, Czech skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%A0%C3%A1rka_Strachov%C3%A1\" title=\"Š<PERSON>rka Strachová\"><PERSON><PERSON><PERSON>rachov<PERSON></a>, Czech skier", "links": [{"title": "Šárka Strachová", "link": "https://wikipedia.org/wiki/%C5%A0%C3%A1rka_Strachov%C3%A1"}]}, {"year": "1986", "text": "<PERSON>, Chilean politician, 36th President of Chile", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1987", "text": "<PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ej%C3%B3n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ej%C3%B3n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juanmi_Callej%C3%B3n"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Dutch speed skater", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Dutch cyclist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Romanian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Mexican footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German-American actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Q%27<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>'or<PERSON><PERSON>\"><PERSON><PERSON>or<PERSON><PERSON></a>, German-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Q%27<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>or<PERSON><PERSON>\"><PERSON><PERSON>or<PERSON><PERSON></a>, German-American actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Q%27<PERSON><PERSON>_<PERSON>cher"}]}, {"year": "1990", "text": "<PERSON><PERSON>-<PERSON>, South Korean singer and actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-sung\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1991", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Canadian football player and medical doctor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>rdi<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, Canadian football player and medical doctor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>rdi<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, Canadian football player and medical doctor", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Tardif"}]}, {"year": "1991", "text": "<PERSON>, Spanish basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Danish track and road cyclist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish track and road cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish track and road cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Swanson\" title=\"Dansby Swanson\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dansby_Swanson\" title=\"Dansby Swanson\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "Dansby Swanson", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Swanson"}]}, {"year": "1995", "text": "<PERSON>, Slovak footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Milan_%C5%A0kriniar\" title=\"Milan Škriniar\"><PERSON></a>, Slovak footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_%C5%A0kriniar\" title=\"Milan Škriniar\"><PERSON></a>, Slovak footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milan_%C5%A0kriniar"}]}, {"year": "1995", "text": "<PERSON>, Dutch footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Russian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, German footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Uruguayan footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1997", "text": "<PERSON>, Polish tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, New Zealand-South Korean singer and dancer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Ros%C3%A9_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, New Zealand-South Korean singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ros%C3%A9_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, New Zealand-South Korean singer and dancer", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/Ros%C3%A9_(singer)"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Frederic\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American singer and songwriter", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON> (American singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(American_singer)"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Spanish footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, New Zealand racing driver", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "55", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman son of <PERSON><PERSON><PERSON> (b. 41)", "html": "55 - AD 55 - <a href=\"https://wikipedia.org/wiki/Britannicus\" title=\"Britannicus\"><PERSON><PERSON><PERSON><PERSON></a>, Roman son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 41)", "no_year_html": "AD 55 - <a href=\"https://wikipedia.org/wiki/Britannicus\" title=\"Britannicus\"><PERSON><PERSON><PERSON><PERSON></a>, Roman son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 41)", "links": [{"title": "Britannicus", "link": "https://wikipedia.org/wiki/Britannicus"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>laudius"}]}, {"year": "244", "text": "<PERSON><PERSON><PERSON> <PERSON>, Roman emperor (b. 225)", "html": "244 - <a href=\"https://wikipedia.org/wiki/Gordian_III\" title=\"Gordian III\"><PERSON><PERSON><PERSON> III</a>, Roman emperor (b. 225)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gordian_III\" title=\"Gordian III\"><PERSON><PERSON><PERSON> III</a>, Roman emperor (b. 225)", "links": [{"title": "<PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/Gordian_III"}]}, {"year": "641", "text": "<PERSON><PERSON><PERSON>, Byzantine emperor (b. 575)", "html": "641 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine emperor (b. 575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine emperor (b. 575)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "731", "text": "<PERSON> (b. 669)", "html": "731 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II\" title=\"<PERSON> Gregory II\"><PERSON> <PERSON> II</a> (b. 669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Gregory II\"><PERSON> II</a> (b. 669)", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "824", "text": "<PERSON>", "html": "824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Paschal_I\" title=\"Pope Paschal I\"><PERSON> Paschal I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Paschal_I\" title=\"Pope Paschal I\"><PERSON> Paschal I</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_I"}]}, {"year": "1141", "text": "<PERSON> of Saint Victor, German philosopher and theologian (b. 1096)", "html": "1141 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saint_Victor\" title=\"<PERSON> of Saint Victor\"><PERSON> of Saint Victor</a>, German philosopher and theologian (b. 1096)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saint_Victor\" title=\"<PERSON> of Saint Victor\"><PERSON> of Saint Victor</a>, German philosopher and theologian (b. 1096)", "links": [{"title": "<PERSON> of Saint Victor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saint_Victor"}]}, {"year": "1503", "text": "<PERSON> York (b. 1466)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/Elizabeth_of_York\" title=\"Elizabeth of York\"><PERSON> York</a> (b. 1466)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_of_York\" title=\"Elizabeth of York\"><PERSON> York</a> (b. 1466)", "links": [{"title": "<PERSON> of York", "link": "https://wikipedia.org/wiki/Elizabeth_of_York"}]}, {"year": "1626", "text": "<PERSON>, Italian mathematician and astronomer (b. 1548)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and astronomer (b. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and astronomer (b. 1548)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pietro_<PERSON>aldi"}]}, {"year": "1650", "text": "<PERSON>, French mathematician and philosopher (b. 1596)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (b. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (b. 1596)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, marchese di <PERSON>ffei, Italian archaeologist, playwright, and critic (b. 1675)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_marchese_di_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, marchese di Maffei\"><PERSON>, marchese di <PERSON></a>, Italian archaeologist, playwright, and critic (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_marchese_di_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, marchese di Maffei\"><PERSON>, marchese di <PERSON></a>, Italian archaeologist, playwright, and critic (b. 1675)", "links": [{"title": "<PERSON>, marchese di Maffei", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_marchese_<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, English poet and gardener (b. 1714)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and gardener (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and gardener (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON> the Elder, English architect, designed St Leonard's and St Botolph's Aldgate (b. 1695)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, English architect, designed <a href=\"https://wikipedia.org/wiki/<PERSON>_Leonard%27s,_Shoreditch\" title=\"<PERSON> Leonard's, Shoreditch\"><PERSON> Leonard's</a> and <a href=\"https://wikipedia.org/wiki/St_Botolph%27s_Aldgate\" title=\"St Botolph's Aldgate\">St Botolph's Aldgate</a> (b. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, English architect, designed <a href=\"https://wikipedia.org/wiki/<PERSON>_Leonard%27s,_Shoreditch\" title=\"<PERSON> Leonard's, Shoreditch\"><PERSON> Leonard's</a> and <a href=\"https://wikipedia.org/wiki/St_Botolph%27s_Aldgate\" title=\"St Botolph's Aldgate\">St Botolph's Aldgate</a> (b. 1695)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_Dance_the_Elder"}, {"title": "St Leonard's, Shoreditch", "link": "https://wikipedia.org/wiki/<PERSON>_Leonard%27s,_<PERSON><PERSON><PERSON>"}, {"title": "St Botolph's Aldgate", "link": "https://wikipedia.org/wiki/St_Botolph%27s_Aldgate"}]}, {"year": "1795", "text": "<PERSON>, Swedish poet and composer (b. 1740)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish poet and composer (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish poet and composer (b. 1740)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, leader of the troops that fought against the French rule of Santo Domingo's colony between 1808 and 1809 (b. 1762)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/Juan_S%C3%A1nchez_Ram%C3%ADrez\" title=\"<PERSON>\"><PERSON></a>, leader of the troops that fought against the French rule of Santo Domingo's colony between 1808 and 1809 (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_S%C3%A1nchez_Ram%C3%ADrez\" title=\"<PERSON>\"><PERSON></a>, leader of the troops that fought against the French rule of Santo Domingo's colony between 1808 and 1809 (b. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_S%C3%A1nchez_Ram%C3%ADrez"}]}, {"year": "1829", "text": "<PERSON>, Russian poet, playwright, and composer (b. 1795)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet, playwright, and composer (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet, playwright, and composer (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, English poet and artist's model (b. 1829)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and artist's model (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and artist's model (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, French physicist and academic (b. 1819)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Mexican general and unconstitutional interim president (b. 1813)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Mar%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and unconstitutional interim president (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Mar%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and unconstitutional interim president (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Mar%C3%ADa_Zuloaga"}]}, {"year": "1901", "text": "<PERSON> of Serbia (b. 1855)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Milan_I_of_Serbia\" title=\"Milan I of Serbia\">Milan I of Serbia</a> (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_I_of_Serbia\" title=\"Milan I of Serbia\">Milan I of Serbia</a> (b. 1855)", "links": [{"title": "Milan I of Serbia", "link": "https://wikipedia.org/wiki/Milan_I_of_Serbia"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Brazilian physician and epidemiologist (b. 1872)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian physician and epidemiologist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian physician and epidemiologist (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Russian general (b. 1861)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (b. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English-Irish engineer, invented the steam turbine (b. 1854)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish engineer, invented the <a href=\"https://wikipedia.org/wiki/Steam_turbine\" title=\"Steam turbine\">steam turbine</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish engineer, invented the <a href=\"https://wikipedia.org/wiki/Steam_turbine\" title=\"Steam turbine\">steam turbine</a> (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Steam turbine", "link": "https://wikipedia.org/wiki/Steam_turbine"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Finnish politician (b. 1878)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Scottish-Canadian historian and politician, Governor General of Canada (b. 1875)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian historian and politician, <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian historian and politician, <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1940", "text": "<PERSON>, American painter and author (b. 1855)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Indian businessman and philanthropist (b. 1884)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Jamnalal_Bajaj\" title=\"Jamnalal Bajaj\"><PERSON><PERSON><PERSON></a>, Indian businessman and philanthropist (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jamnalal_Bajaj\" title=\"Jamnalal Bajaj\"><PERSON><PERSON><PERSON></a>, Indian businessman and philanthropist (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jamnalal_Bajaj"}]}, {"year": "1947", "text": "<PERSON>, Estonian wrestler and coach (b. 1884)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Estonian wrestler and coach (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Estonian wrestler and coach (b. 1884)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>(wrestler)"}]}, {"year": "1948", "text": "<PERSON>, Russian director and screenwriter (b. 1898)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and screenwriter (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and screenwriter (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Swedish doctor (b. 1857)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish doctor (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish doctor (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Welsh neurologist and psychoanalyst (b. 1879)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh neurologist and psychoanalyst (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh neurologist and psychoanalyst (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Swedish-American soldier, Medal of Honor recipient (b. 1872)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1963", "text": "<PERSON>, American poet, novelist, and short story writer (b. 1932)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, novelist, and short story writer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, novelist, and short story writer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON> <PERSON><PERSON>, Dutch-American minister and activist (b. 1885)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Dutch-American minister and activist (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Dutch-American minister and activist (b. 1885)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American playwright (b. 1889)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON> <PERSON>, German physicist and academic, Nobel Prize laureate (b. 1907)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1975", "text": "<PERSON>, Malagasy colonel and politician, President of Madagascar (b. 1931)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malagasy colonel and politician, <a href=\"https://wikipedia.org/wiki/President_of_Madagascar\" class=\"mw-redirect\" title=\"President of Madagascar\">President of Madagascar</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malagasy colonel and politician, <a href=\"https://wikipedia.org/wiki/President_of_Madagascar\" class=\"mw-redirect\" title=\"President of Madagascar\">President of Madagascar</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Madagascar", "link": "https://wikipedia.org/wiki/President_of_Madagascar"}]}, {"year": "1976", "text": "<PERSON>, American actor (b. 1911)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, German pilot and engineer (b. 1894)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot and engineer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot and engineer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 5th President of India (b. 1905)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1977", "text": "<PERSON>, Dutch academic and politician, Prime Minister of the Netherlands (b. 1902)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1978", "text": "<PERSON>, American chemist and academic (b. 1893)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Swedish novelist, essayist, and poet, Nobel Prize laureate (b. 1904)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish novelist, essayist, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish novelist, essayist, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1982", "text": "<PERSON>, American actress and dancer (b. 1912)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor, director, and producer (b. 1898)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American journalist and author (b. 1920)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Filipino politician (b. 1942)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino politician (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actor and voice artist (b. 1912)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hanlon\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hanlon\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_O%27Hanlon"}]}, {"year": "1993", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (b. 1922)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1994", "text": "<PERSON>, American race car driver (b. 1946)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actor and director (b. 1930)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON> Booke\"><PERSON><PERSON></a>, American actor and director (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and director (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1994", "text": "<PERSON>, American actor, director, and producer (b. 1920)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Austrian-Swiss philosopher and academic (b. 1924)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss philosopher and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss philosopher and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Italian poet and author (b. 1930)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Trinidadian singer (b. 1922)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_(calypsonian)\" title=\"Lord <PERSON> (calypsonian)\"><PERSON></a>, Trinidadian singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_(calypsonian)\" title=\"Lord <PERSON> (calypsonian)\">Lord <PERSON></a>, Trinidadian singer (b. 1922)", "links": [{"title": "<PERSON> (calypsonian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(calypsonian)"}]}, {"year": "2000", "text": "<PERSON>, French director, producer, and screenwriter (b. 1928)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American baseball player and coach (b. 1910)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English actor (b. 1931)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1931)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2004", "text": "<PERSON>, American voice actor (b. 1947)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Australian runner (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American author (b. 1944)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American author and screenwriter (b. 1940)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Australian tennis player (b. 1940)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English wrestler and actor (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler and actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler and actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American chicken and stage magician, oldest known chicken (h. 1990)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Matilda_(chicken)\" title=\"<PERSON> (chicken)\">Matilda</a>, American <a href=\"https://wikipedia.org/wiki/Chicken\" title=\"Chicken\">chicken</a> and <a href=\"https://wikipedia.org/wiki/Stage_magician\" class=\"mw-redirect\" title=\"Stage magician\">stage magician</a>, oldest known chicken (h. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matilda_(chicken)\" title=\"<PERSON> (chicken)\">Matilda</a>, American <a href=\"https://wikipedia.org/wiki/Chicken\" title=\"Chicken\">chicken</a> and <a href=\"https://wikipedia.org/wiki/Stage_magician\" class=\"mw-redirect\" title=\"Stage magician\">stage magician</a>, oldest known chicken (h. 1990)", "links": [{"title": "<PERSON> (chicken)", "link": "https://wikipedia.org/wiki/<PERSON>_(chicken)"}, {"title": "Chicken", "link": "https://wikipedia.org/wiki/Chicken"}, {"title": "Stage magician", "link": "https://wikipedia.org/wiki/Stage_magician"}]}, {"year": "2008", "text": "<PERSON>, American lawyer and politician (b. 1928)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American engineer (b. 1919)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, American singer (b. 1941)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Dutch-American physician and academic (b. 1911)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American physician and academic (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American physician and academic (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Canadian businessman and politician (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman and politician (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ey"}]}, {"year": "2010", "text": "<PERSON>, English fashion designer, founder of his eponymous brand (b. 1969)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer, founder of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)\" class=\"mw-redirect\" title=\"<PERSON> (brand)\">his eponymous brand</a> (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer, founder of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)\" class=\"mw-redirect\" title=\"<PERSON> (brand)\">his eponymous brand</a> (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (brand)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)"}]}, {"year": "2011", "text": "<PERSON>, American baseball player and manager (b. 1928)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Norwegian politician, Norwegian Minister of the Environment (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Environment_(Norway)\" class=\"mw-redirect\" title=\"Minister of the Environment (Norway)\">Norwegian Minister of the Environment</a> (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Environment_(Norway)\" class=\"mw-redirect\" title=\"Minister of the Environment (Norway)\">Norwegian Minister of the Environment</a> (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Minister of the Environment (Norway)", "link": "https://wikipedia.org/wiki/Minister_of_the_Environment_(Norway)"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Israeli general (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli general (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli general (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aro<PERSON>_<PERSON>i"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter, producer, and actress (b. 1963)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Whitney_Houston\" title=\"Whitney Houston\"><PERSON></a>, American singer-songwriter, producer, and actress (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Whitney_Houston\" title=\"Whitney Houston\"><PERSON></a>, American singer-songwriter, producer, and actress (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Whitney_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English bass player (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bass player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bass player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Swedish singer and actress (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ba<PERSON>\"><PERSON></a>, Swedish singer and actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Babs\"><PERSON></a>, Swedish singer and actress (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>bs"}]}, {"year": "2014", "text": "<PERSON>, Dominican-American painter (b. 1916)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American painter (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American painter (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Colombian journalist and actor (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Pacheco\" title=\"<PERSON>\"><PERSON></a>, Colombian journalist and actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Pacheco\" title=\"<PERSON>\"><PERSON></a>, Colombian journalist and actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gonz%C3%A1lez_Pacheco"}]}, {"year": "2015", "text": "<PERSON>, French actor, director, and screenwriter (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American journalist (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and coach (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jerry_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American mixed martial artist and wrestler (b. 1971)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and wrestler (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and wrestler (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Thai-Chinese footballer and manager (b. 1929)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai-Chinese footballer and manager (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai-Chinese footballer and manager (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Brazilian basketball player (b. 1990)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Fab_<PERSON>o\" title=\"Fab Melo\"><PERSON><PERSON> <PERSON></a>, Brazilian basketball player (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ab_<PERSON><PERSON>\" title=\"<PERSON>ab Mel<PERSON>\"><PERSON><PERSON></a>, Brazilian basketball player (b. 1990)", "links": [{"title": "<PERSON>ab <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fab_Melo"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Dutch Olympian (b. 1919)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Jaa<PERSON>_<PERSON>ijk<PERSON>\" title=\"<PERSON>aa<PERSON> Rijk<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch Olympian (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaa<PERSON>_<PERSON>ijk<PERSON>\" title=\"<PERSON>aa<PERSON> Rijk<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch Olympian (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aa<PERSON>_<PERSON>s"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Korean-Canadian musician (b. 1985)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean-Canadian musician (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean-Canadian musician (b. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>an"}]}, {"year": "2018", "text": "<PERSON>, American singer, songwriter and actor (b. 1928)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_Damone"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Pakistani human-rights lawyer and social activist (b. 1952)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani <a href=\"https://wikipedia.org/wiki/Lawyer\" title=\"Lawyer\">human-rights lawyer</a> and social activist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani <a href=\"https://wikipedia.org/wiki/Lawyer\" title=\"Lawyer\">human-rights lawyer</a> and social activist (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asma_Jahangir"}, {"title": "Lawyer", "link": "https://wikipedia.org/wiki/Lawyer"}]}, {"year": "2018", "text": "<PERSON>, American stage and television actress (b. 1956)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage and television actress (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage and television actress (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Pakistani drama actor, writer and artist (b. 1930)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aj<PERSON>\" title=\"<PERSON><PERSON> Wajid\"><PERSON><PERSON></a>, Pakistani drama actor, writer and artist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aj<PERSON>\" title=\"<PERSON><PERSON> Wajid\"><PERSON><PERSON></a>, Pakistani drama actor, writer and artist (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ajid"}]}, {"year": "2023", "text": "<PERSON>, American biographer (b. 1941)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biographer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biographer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American chemist (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>d"}]}, {"year": "2025", "text": "<PERSON>, American classical pianist (b. 1959)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American classical pianist (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American classical pianist (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, Singaporean actor (b. 1949)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean actor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean actor (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}