{"date": "June 23", "url": "https://wikipedia.org/wiki/June_23", "data": {"Events": [{"year": "229", "text": "<PERSON> proclaims himself emperor of Eastern Wu.", "html": "229 - <a href=\"https://wikipedia.org/wiki/Sun_Quan\" title=\"Sun Quan\"><PERSON></a> proclaims himself emperor of <a href=\"https://wikipedia.org/wiki/Eastern_Wu\" title=\"Eastern Wu\">Eastern Wu</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sun_Quan\" title=\"Sun Quan\"><PERSON> Quan</a> proclaims himself emperor of <a href=\"https://wikipedia.org/wiki/Eastern_Wu\" title=\"Eastern Wu\">Eastern Wu</a>.", "links": [{"title": "Sun <PERSON>", "link": "https://wikipedia.org/wiki/Sun_Quan"}, {"title": "Eastern Wu", "link": "https://wikipedia.org/wiki/Eastern_Wu"}]}, {"year": "1266", "text": "War of Saint Sabas: In the Battle of Trapani, the Venetians defeat a larger Genoese fleet, capturing all its ships.", "html": "1266 - <a href=\"https://wikipedia.org/wiki/War_of_Saint_Sabas\" title=\"War of Saint Sabas\">War of Saint Sabas</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Trapani\" title=\"Battle of Trapani\">Battle of Trapani</a>, the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetians</a> defeat a larger <a href=\"https://wikipedia.org/wiki/Republic_of_Genoa\" title=\"Republic of Genoa\">Genoese</a> fleet, capturing all its ships.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_Saint_Sabas\" title=\"War of Saint Sabas\">War of Saint Sabas</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Trapani\" title=\"Battle of Trapani\">Battle of Trapani</a>, the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetians</a> defeat a larger <a href=\"https://wikipedia.org/wiki/Republic_of_Genoa\" title=\"Republic of Genoa\">Genoese</a> fleet, capturing all its ships.", "links": [{"title": "War of Saint Sabas", "link": "https://wikipedia.org/wiki/War_of_Saint_Sabas"}, {"title": "Battle of Trapani", "link": "https://wikipedia.org/wiki/Battle_of_Trapani"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "Republic of Genoa", "link": "https://wikipedia.org/wiki/Republic_of_Genoa"}]}, {"year": "1280", "text": "The Spanish Reconquista: In the Battle of Moclín the Emirate of Granada ambush a superior pursuing force, killing most of them in a military disaster for the Kingdom of Castile.", "html": "1280 - The <a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Spanish Reconquista</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Mocl%C3%ADn_(1280)\" title=\"Battle of Moclín (1280)\">Battle of Moclín</a> the <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Emirate of Granada</a> ambush a superior pursuing force, killing most of them in a military disaster for the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Spanish Reconquista</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Mocl%C3%ADn_(1280)\" title=\"Battle of Moclín (1280)\">Battle of Moclín</a> the <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Emirate of Granada</a> ambush a superior pursuing force, killing most of them in a military disaster for the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a>.", "links": [{"title": "Reconquista", "link": "https://wikipedia.org/wiki/Reconquista"}, {"title": "Battle of Moclín (1280)", "link": "https://wikipedia.org/wiki/Battle_of_Mocl%C3%ADn_(1280)"}, {"title": "Emirate of Granada", "link": "https://wikipedia.org/wiki/Emirate_of_Granada"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}]}, {"year": "1305", "text": "A peace treaty between the Flemish and the French is signed at Athis-sur-Orge.", "html": "1305 - A peace treaty between the <a href=\"https://wikipedia.org/wiki/Flanders\" title=\"Flanders\">Flemish</a> and the French is signed at <a href=\"https://wikipedia.org/wiki/Treaty_of_Athis-sur-Orge\" title=\"Treaty of Athis-sur-Orge\">Athis-sur-Orge</a>.", "no_year_html": "A peace treaty between the <a href=\"https://wikipedia.org/wiki/Flanders\" title=\"Flanders\">Flemish</a> and the French is signed at <a href=\"https://wikipedia.org/wiki/Treaty_of_Athis-sur-Orge\" title=\"Treaty of Athis-sur-Orge\">Athis-sur-Orge</a>.", "links": [{"title": "Flanders", "link": "https://wikipedia.org/wiki/Flanders"}, {"title": "Treaty of Athis-sur-Orge", "link": "https://wikipedia.org/wiki/Treaty_of_Athis-sur-Orge"}]}, {"year": "1314", "text": "First War of Scottish Independence: The Battle of Bannockburn (south of Stirling) begins.", "html": "1314 - <a href=\"https://wikipedia.org/wiki/First_War_of_Scottish_Independence\" title=\"First War of Scottish Independence\">First War of Scottish Independence</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Bannockburn\" title=\"Battle of Bannockburn\">Battle of Bannockburn</a> (south of <a href=\"https://wikipedia.org/wiki/Stirling\" title=\"Stirling\">Stirling</a>) begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_War_of_Scottish_Independence\" title=\"First War of Scottish Independence\">First War of Scottish Independence</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Bannockburn\" title=\"Battle of Bannockburn\">Battle of Bannockburn</a> (south of <a href=\"https://wikipedia.org/wiki/Stirling\" title=\"Stirling\">Stirling</a>) begins.", "links": [{"title": "First War of Scottish Independence", "link": "https://wikipedia.org/wiki/First_War_of_Scottish_Independence"}, {"title": "Battle of Bannockburn", "link": "https://wikipedia.org/wiki/Battle_of_Bannockburn"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stirling"}]}, {"year": "1532", "text": "<PERSON> of England and <PERSON> of France sign the \"Treaty of Closer Amity With France\" (also known as the Pommeraye treaty), pledging mutual aid against <PERSON>, Holy Roman Emperor.", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> of England</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> sign the \"Treaty of Closer Amity With France\" (also known as the Pommeraye treaty), pledging mutual aid against <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> of England</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> sign the \"Treaty of Closer Amity With France\" (also known as the Pommeraye treaty), pledging mutual aid against <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1565", "text": "<PERSON><PERSON><PERSON>, commander of the Ottoman navy, dies during the Great Siege of Malta.", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, commander of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> navy, dies during the <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Malta\" title=\"Great Siege of Malta\">Great Siege of Malta</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, commander of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> navy, dies during the <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Malta\" title=\"Great Siege of Malta\">Great Siege of Malta</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Great Siege of Malta", "link": "https://wikipedia.org/wiki/Great_Siege_of_Malta"}]}, {"year": "1594", "text": "The Action of Faial, Azores. The Portuguese carrack Cinco Chagas, loaded with slaves and treasure, is attacked and sunk by English ships with only 13 survivors out of over 700 on board.", "html": "1594 - The <a href=\"https://wikipedia.org/wiki/Action_of_Faial\" title=\"Action of Faial\">Action of Faial</a>, Azores. The Portuguese carrack <i>Cinco <PERSON></i>, loaded with slaves and treasure, is attacked and sunk by English ships with only 13 survivors out of over 700 on board.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Action_of_Faial\" title=\"Action of Faial\">Action of Faial</a>, Azores. The Portuguese carrack <i>Cinco <PERSON></i>, loaded with slaves and treasure, is attacked and sunk by English ships with only 13 survivors out of over 700 on board.", "links": [{"title": "Action of Faial", "link": "https://wikipedia.org/wiki/Action_of_Faial"}]}, {"year": "1611", "text": "The mutinous crew of <PERSON>'s fourth voyage sets <PERSON>, his son and seven loyal crew members adrift in an open boat in what is now Hudson Bay; they are never heard from again.", "html": "1611 - The mutinous crew of <a href=\"https://wikipedia.org/wiki/Henry_<PERSON>\" title=\"Henry Hudson\"><PERSON></a>'s fourth voyage sets <PERSON>, his son and seven loyal crew members adrift in an open boat in what is now <a href=\"https://wikipedia.org/wiki/Hudson_Bay\" title=\"Hudson Bay\">Hudson Bay</a>; they are never heard from again.", "no_year_html": "The mutinous crew of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Henry Hudson\"><PERSON></a>'s fourth voyage sets <PERSON>, his son and seven loyal crew members adrift in an open boat in what is now <a href=\"https://wikipedia.org/wiki/Hudson_Bay\" title=\"Hudson Bay\">Hudson Bay</a>; they are never heard from again.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hudson Bay", "link": "https://wikipedia.org/wiki/Hudson_Bay"}]}, {"year": "1683", "text": "<PERSON> signs a friendship treaty with Lenape Indians in Pennsylvania.", "html": "1683 - <a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Penn\"><PERSON></a> signs a friendship treaty with <a href=\"https://wikipedia.org/wiki/Lenape\" title=\"Lenape\">Lenape</a> Indians in <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Penn\"><PERSON></a> signs a friendship treaty with <a href=\"https://wikipedia.org/wiki/Lenape\" title=\"Lenape\">Lenape</a> Indians in <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lenape", "link": "https://wikipedia.org/wiki/Lenape"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}]}, {"year": "1713", "text": "The French residents of Acadia are given one year to declare allegiance to Britain or leave Nova Scotia, Canada.", "html": "1713 - The French residents of <a href=\"https://wikipedia.org/wiki/Acadia\" title=\"Acadia\">Acadia</a> are given one year to declare allegiance to Britain or leave <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a>, Canada.", "no_year_html": "The French residents of <a href=\"https://wikipedia.org/wiki/Acadia\" title=\"Acadia\">Acadia</a> are given one year to declare allegiance to Britain or leave <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a>, Canada.", "links": [{"title": "Acadia", "link": "https://wikipedia.org/wiki/Acadia"}, {"title": "Nova Scotia", "link": "https://wikipedia.org/wiki/Nova_Scotia"}]}, {"year": "1757", "text": "Battle of Plassey: Three thousand British troops under <PERSON> defeat a 50,000-strong Indian army under <PERSON><PERSON> at Plassey.", "html": "1757 - <a href=\"https://wikipedia.org/wiki/Battle_of_Plassey\" title=\"Battle of Plassey\">Battle of Plassey</a>: Three thousand British troops under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat a 50,000-strong Indian army under <a href=\"https://wikipedia.org/wiki/Sir<PERSON>_ud-Daulah\" class=\"mw-redirect\" title=\"<PERSON><PERSON> ud-Daulah\"><PERSON><PERSON> u<PERSON><PERSON><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Plassey\" class=\"mw-redirect\" title=\"P<PERSON><PERSON>\">P<PERSON>ey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Plassey\" title=\"Battle of Plassey\">Battle of Plassey</a>: Three thousand British troops under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat a 50,000-strong Indian army under <a href=\"https://wikipedia.org/wiki/Sir<PERSON>_ud-Daulah\" class=\"mw-redirect\" title=\"<PERSON><PERSON> ud-Daulah\"><PERSON><PERSON> u<PERSON><PERSON><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Plassey\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Battle of Plassey", "link": "https://wikipedia.org/wiki/Battle_of_Plassey"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ey"}]}, {"year": "1758", "text": "Seven Years' War: Battle of Krefeld: British, Hanoverian, and Prussian forces defeat French troops at Krefeld in Germany.", "html": "1758 - <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Krefeld\" title=\"Battle of Krefeld\">Battle of Krefeld</a>: British, Hanoverian, and Prussian forces defeat French troops at <a href=\"https://wikipedia.org/wiki/Krefeld\" title=\"Krefeld\">Krefeld</a> in Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Krefeld\" title=\"Battle of Krefeld\">Battle of Krefeld</a>: British, Hanoverian, and Prussian forces defeat French troops at <a href=\"https://wikipedia.org/wiki/Krefeld\" title=\"Krefeld\">Krefeld</a> in Germany.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "Battle of Krefeld", "link": "https://wikipedia.org/wiki/Battle_of_Krefeld"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1760", "text": "Seven Years' War: Battle of Landeshut: Austria defeats Prussia.", "html": "1760 - Seven Years' War: <a href=\"https://wikipedia.org/wiki/Battle_of_Landeshut_(1760)\" title=\"Battle of Landeshut (1760)\">Battle of Landeshut</a>: Austria defeats <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>.", "no_year_html": "Seven Years' War: <a href=\"https://wikipedia.org/wiki/Battle_of_Landeshut_(1760)\" title=\"Battle of Landeshut (1760)\">Battle of Landeshut</a>: Austria defeats <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>.", "links": [{"title": "Battle of Landeshut (1760)", "link": "https://wikipedia.org/wiki/Battle_of_Landeshut_(1760)"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}]}, {"year": "1780", "text": "American Revolution: Battle of Springfield fought in and around Springfield, New Jersey (including Short Hills, formerly of Springfield, now of Millburn Township).", "html": "1780 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Springfield_(1780)\" class=\"mw-redirect\" title=\"Battle of Springfield (1780)\">Battle of Springfield</a> fought in and around <a href=\"https://wikipedia.org/wiki/Springfield_Township,_Union_County,_New_Jersey\" title=\"Springfield Township, Union County, New Jersey\">Springfield, New Jersey</a> (including <a href=\"https://wikipedia.org/wiki/Short_Hills,_New_Jersey\" title=\"Short Hills, New Jersey\">Short Hills</a>, formerly of Springfield, now of <a href=\"https://wikipedia.org/wiki/Millburn,_New_Jersey\" title=\"Millburn, New Jersey\">Millburn</a> Township).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Springfield_(1780)\" class=\"mw-redirect\" title=\"Battle of Springfield (1780)\">Battle of Springfield</a> fought in and around <a href=\"https://wikipedia.org/wiki/Springfield_Township,_Union_County,_New_Jersey\" title=\"Springfield Township, Union County, New Jersey\">Springfield, New Jersey</a> (including <a href=\"https://wikipedia.org/wiki/Short_Hills,_New_Jersey\" title=\"Short Hills, New Jersey\">Short Hills</a>, formerly of Springfield, now of <a href=\"https://wikipedia.org/wiki/Millburn,_New_Jersey\" title=\"Millburn, New Jersey\">Millburn</a> Township).", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "Battle of Springfield (1780)", "link": "https://wikipedia.org/wiki/Battle_of_Springfield_(1780)"}, {"title": "Springfield Township, Union County, New Jersey", "link": "https://wikipedia.org/wiki/Springfield_Township,_Union_County,_New_Jersey"}, {"title": "Short Hills, New Jersey", "link": "https://wikipedia.org/wiki/Short_Hills,_New_Jersey"}, {"title": "Millburn, New Jersey", "link": "https://wikipedia.org/wiki/Millburn,_New_Jersey"}]}, {"year": "1794", "text": "Empress <PERSON> of Russia grants Jews permission to settle in Kyiv.", "html": "1794 - Empress <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> II</a> of Russia grants Jews permission to settle in <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a>.", "no_year_html": "Empress <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> II</a> of Russia grants Jews permission to settle in <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a>.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Kyiv", "link": "https://wikipedia.org/wiki/Kyiv"}]}, {"year": "1810", "text": "<PERSON> forms the Pacific Fur Company.", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> forms the <a href=\"https://wikipedia.org/wiki/Pacific_Fur_Company\" title=\"Pacific Fur Company\">Pacific Fur Company</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> forms the <a href=\"https://wikipedia.org/wiki/Pacific_Fur_Company\" title=\"Pacific Fur Company\">Pacific Fur Company</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pacific Fur Company", "link": "https://wikipedia.org/wiki/Pacific_Fur_Company"}]}, {"year": "1812", "text": "War of 1812: Great Britain revokes the restrictions on American commerce, thus eliminating one of the chief reasons for going to war.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: Great Britain revokes the restrictions on American commerce, thus eliminating one of the chief reasons for going to war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: Great Britain revokes the restrictions on American commerce, thus eliminating one of the chief reasons for going to war.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}]}, {"year": "1860", "text": "The United States Congress establishes the Government Printing Office.", "html": "1860 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> establishes the <a href=\"https://wikipedia.org/wiki/United_States_Government_Publishing_Office\" title=\"United States Government Publishing Office\">Government Printing Office</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> establishes the <a href=\"https://wikipedia.org/wiki/United_States_Government_Publishing_Office\" title=\"United States Government Publishing Office\">Government Printing Office</a>.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "United States Government Publishing Office", "link": "https://wikipedia.org/wiki/United_States_Government_Publishing_Office"}]}, {"year": "1865", "text": "American Civil War: At Fort Towson in the Oklahoma Territory, Confederate Brigadier General <PERSON> surrenders the last significant Confederate army.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: At <a href=\"https://wikipedia.org/wiki/Fort_Towson\" title=\"Fort Towson\">Fort Towson</a> in the <a href=\"https://wikipedia.org/wiki/Oklahoma_Territory\" title=\"Oklahoma Territory\">Oklahoma Territory</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Brigadier_General_(CSA)#Brigadier_general\" class=\"mw-redirect\" title=\"Brigadier General (CSA)\">Brigadier General</a> <a href=\"https://wikipedia.org/wiki/Stand_Watie\" title=\"Stand Watie\">Stand Watie</a> surrenders the last significant Confederate army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: At <a href=\"https://wikipedia.org/wiki/Fort_Towson\" title=\"Fort Towson\">Fort Towson</a> in the <a href=\"https://wikipedia.org/wiki/Oklahoma_Territory\" title=\"Oklahoma Territory\">Oklahoma Territory</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Brigadier_General_(CSA)#Brigadier_general\" class=\"mw-redirect\" title=\"Brigadier General (CSA)\">Brigadier General</a> <a href=\"https://wikipedia.org/wiki/Stand_Watie\" title=\"Stand Watie\">Stand Watie</a> surrenders the last significant Confederate army.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Fort Towson", "link": "https://wikipedia.org/wiki/Fort_Towson"}, {"title": "Oklahoma Territory", "link": "https://wikipedia.org/wiki/Oklahoma_Territory"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Brigadier General (CSA)", "link": "https://wikipedia.org/wiki/Brigadier_General_(CSA)#Brigadier_general"}, {"title": "Stand Watie", "link": "https://wikipedia.org/wiki/Stand_Watie"}]}, {"year": "1868", "text": "<PERSON> received a patent for an invention he called the \"Type-Writer\".", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sholes\"><PERSON></a> received a patent for an invention he called the <a href=\"https://wikipedia.org/wiki/Typewriter\" title=\"Typewriter\">\"Type-Writer\"</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sholes\"><PERSON></a> received a patent for an invention he called the <a href=\"https://wikipedia.org/wiki/Typewriter\" title=\"Typewriter\">\"Type-Writer\"</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Typewriter", "link": "https://wikipedia.org/wiki/Typewriter"}]}, {"year": "1887", "text": "The Rocky Mountains Park Act becomes law in Canada creating the nation's first national park, Banff National Park.", "html": "1887 - The <a href=\"https://wikipedia.org/wiki/Rocky_Mountains_Park_Act\" title=\"Rocky Mountains Park Act\">Rocky Mountains Park Act</a> becomes law in Canada creating the nation's first <a href=\"https://wikipedia.org/wiki/National_park\" title=\"National park\">national park</a>, <a href=\"https://wikipedia.org/wiki/Banff_National_Park\" title=\"Banff National Park\">Banff National Park</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rocky_Mountains_Park_Act\" title=\"Rocky Mountains Park Act\">Rocky Mountains Park Act</a> becomes law in Canada creating the nation's first <a href=\"https://wikipedia.org/wiki/National_park\" title=\"National park\">national park</a>, <a href=\"https://wikipedia.org/wiki/Banff_National_Park\" title=\"Banff National Park\">Banff National Park</a>.", "links": [{"title": "Rocky Mountains Park Act", "link": "https://wikipedia.org/wiki/Rocky_Mountains_Park_Act"}, {"title": "National park", "link": "https://wikipedia.org/wiki/National_park"}, {"title": "Banff National Park", "link": "https://wikipedia.org/wiki/Banff_National_Park"}]}, {"year": "1894", "text": "The International Olympic Committee is founded at the Sorbonne in Paris, at the initiative of Baron <PERSON>.", "html": "1894 - The <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">International Olympic Committee</a> is founded at the <a href=\"https://wikipedia.org/wiki/University_of_Paris\" title=\"University of Paris\">Sorbonne</a> in Paris, at the initiative of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">International Olympic Committee</a> is founded at the <a href=\"https://wikipedia.org/wiki/University_of_Paris\" title=\"University of Paris\">Sorbonne</a> in Paris, at the initiative of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "International Olympic Committee", "link": "https://wikipedia.org/wiki/International_Olympic_Committee"}, {"title": "University of Paris", "link": "https://wikipedia.org/wiki/University_of_Paris"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "Second Balkan War: The Greeks defeat the Bulgarians in the Battle of Doiran.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Second_Balkan_War\" title=\"Second Balkan War\">Second Balkan War</a>: The Greeks defeat the Bulgarians in the <a href=\"https://wikipedia.org/wiki/Battle_of_Doiran_(1913)\" title=\"Battle of Doiran (1913)\">Battle of Doiran</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Balkan_War\" title=\"Second Balkan War\">Second Balkan War</a>: The Greeks defeat the Bulgarians in the <a href=\"https://wikipedia.org/wiki/Battle_of_Doiran_(1913)\" title=\"Battle of Doiran (1913)\">Battle of Doiran</a>.", "links": [{"title": "Second Balkan War", "link": "https://wikipedia.org/wiki/Second_Balkan_War"}, {"title": "Battle of Doiran (1913)", "link": "https://wikipedia.org/wiki/Battle_of_Doiran_(1913)"}]}, {"year": "1914", "text": "Mexican Revolution: <PERSON><PERSON> takes Zacatecas from Victoriano Huerta.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>: <a href=\"https://wikipedia.org/wiki/Pancho_Villa\" title=\"Pancho Villa\">Pancho Villa</a> takes <a href=\"https://wikipedia.org/wiki/Zacatecas\" title=\"Zacatecas\">Zacatecas</a> from <a href=\"https://wikipedia.org/wiki/Victoriano_Huerta\" title=\"Victoriano Huerta\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>: <a href=\"https://wikipedia.org/wiki/Pancho_Villa\" title=\"Pancho Villa\">Pancho Villa</a> takes <a href=\"https://wikipedia.org/wiki/Zacatecas\" title=\"Zacatecas\">Zacatecas</a> from <a href=\"https://wikipedia.org/wiki/Victoriano_Huerta\" title=\"Victoriano Huerta\"><PERSON><PERSON></a>.", "links": [{"title": "Mexican Revolution", "link": "https://wikipedia.org/wiki/Mexican_Revolution"}, {"title": "Pancho <PERSON>", "link": "https://wikipedia.org/wiki/Pancho_Villa"}, {"title": "Zacatecas", "link": "https://wikipedia.org/wiki/Zacatecas"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "In a game against the Washington Senators, Boston Red Sox pitcher <PERSON> retires 26 batters in a row after replacing <PERSON>, who had been ejected for punching the umpire.", "html": "1917 - In a game against the <a href=\"https://wikipedia.org/wiki/Texas_Rangers_(baseball)\" title=\"Texas Rangers (baseball)\">Washington Senators</a>, <a href=\"https://wikipedia.org/wiki/Boston_Red_Sox\" title=\"Boston Red Sox\">Boston Red Sox</a> pitcher <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ernie Shore\"><PERSON></a> retires 26 batters in a row after replacing <a href=\"https://wikipedia.org/wiki/Babe_Ruth\" title=\"Babe Ruth\"><PERSON></a>, who had been ejected for punching the <a href=\"https://wikipedia.org/wiki/Umpire_(baseball)\" title=\"Umpire (baseball)\">umpire</a>.", "no_year_html": "In a game against the <a href=\"https://wikipedia.org/wiki/Texas_Rangers_(baseball)\" title=\"Texas Rangers (baseball)\">Washington Senators</a>, <a href=\"https://wikipedia.org/wiki/Boston_Red_Sox\" title=\"Boston Red Sox\">Boston Red Sox</a> pitcher <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ernie Shore\"><PERSON></a> retires 26 batters in a row after replacing <a href=\"https://wikipedia.org/wiki/Babe_Ruth\" title=\"<PERSON> Ruth\"><PERSON></a>, who had been ejected for punching the <a href=\"https://wikipedia.org/wiki/Umpire_(baseball)\" title=\"Umpire (baseball)\">umpire</a>.", "links": [{"title": "Texas Rangers (baseball)", "link": "https://wikipedia.org/wiki/Texas_Rangers_(baseball)"}, {"title": "Boston Red Sox", "link": "https://wikipedia.org/wiki/Boston_Red_Sox"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Umpire (baseball)", "link": "https://wikipedia.org/wiki/Umpire_(baseball)"}]}, {"year": "1919", "text": "Estonian War of Independence: The decisive defeat of the Baltische Landeswehr in the Battle of Cēsis; this date is celebrated as Victory Day in Estonia.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Estonian_War_of_Independence\" title=\"Estonian War of Independence\">Estonian War of Independence</a>: The decisive defeat of the <i><a href=\"https://wikipedia.org/wiki/Baltische_Landeswehr\" title=\"Baltische Landeswehr\">Baltische Landeswehr</a></i> in the <a href=\"https://wikipedia.org/wiki/Battle_of_C%C4%93sis_(1919)\" title=\"Battle of Cēsis (1919)\">Battle of Cēsis</a>; this date is celebrated as <a href=\"https://wikipedia.org/wiki/Victory_Day_(Estonia)\" class=\"mw-redirect\" title=\"Victory Day (Estonia)\">Victory Day</a> in <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Estonian_War_of_Independence\" title=\"Estonian War of Independence\">Estonian War of Independence</a>: The decisive defeat of the <i><a href=\"https://wikipedia.org/wiki/Baltische_Landeswehr\" title=\"Baltische Landeswehr\">Baltische Landeswehr</a></i> in the <a href=\"https://wikipedia.org/wiki/Battle_of_C%C4%93sis_(1919)\" title=\"Battle of Cēsis (1919)\">Battle of Cēsis</a>; this date is celebrated as <a href=\"https://wikipedia.org/wiki/Victory_Day_(Estonia)\" class=\"mw-redirect\" title=\"Victory Day (Estonia)\">Victory Day</a> in <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>.", "links": [{"title": "Estonian War of Independence", "link": "https://wikipedia.org/wiki/Estonian_War_of_Independence"}, {"title": "Baltische Landeswehr", "link": "https://wikipedia.org/wiki/Baltische_Landeswehr"}, {"title": "Battle of Cēsis (1919)", "link": "https://wikipedia.org/wiki/Battle_of_C%C4%93sis_(1919)"}, {"title": "Victory Day (Estonia)", "link": "https://wikipedia.org/wiki/Victory_Day_(Estonia)"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}]}, {"year": "1926", "text": "The College Board administers the first SAT exam.", "html": "1926 - The <a href=\"https://wikipedia.org/wiki/College_Board\" title=\"College Board\">College Board</a> administers the first <a href=\"https://wikipedia.org/wiki/SAT\" title=\"SAT\">SAT</a> exam.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/College_Board\" title=\"College Board\">College Board</a> administers the first <a href=\"https://wikipedia.org/wiki/SAT\" title=\"SAT\">SAT</a> exam.", "links": [{"title": "College Board", "link": "https://wikipedia.org/wiki/College_Board"}, {"title": "SAT", "link": "https://wikipedia.org/wiki/SAT"}]}, {"year": "1931", "text": "<PERSON> Post and <PERSON> take off from Roosevelt Field, Long Island in an attempt to circumnavigate the world in a single-engine plane.", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Wiley_Post\" title=\"Wiley Post\">Wiley Post</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> take off from <a href=\"https://wikipedia.org/wiki/Roosevelt_Field,_Long_Island\" class=\"mw-redirect\" title=\"Roosevelt Field, Long Island\">Roosevelt Field, Long Island</a> in an attempt to <a href=\"https://wikipedia.org/wiki/Circumnavigate\" class=\"mw-redirect\" title=\"Circumnavigate\">circumnavigate</a> the world in a single-engine plane.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wiley_Post\" title=\"Wiley Post\">Wiley Post</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> take off from <a href=\"https://wikipedia.org/wiki/Roosevelt_Field,_Long_Island\" class=\"mw-redirect\" title=\"Roosevelt Field, Long Island\">Roosevelt Field, Long Island</a> in an attempt to <a href=\"https://wikipedia.org/wiki/Circumnavigate\" class=\"mw-redirect\" title=\"Circumnavigate\">circumnavigate</a> the world in a single-engine plane.", "links": [{"title": "Wiley Post", "link": "https://wikipedia.org/wiki/Wiley_Post"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Roosevelt Field, Long Island", "link": "https://wikipedia.org/wiki/Roosevelt_Field,_Long_Island"}, {"title": "Circumnavigate", "link": "https://wikipedia.org/wiki/Circumnavigate"}]}, {"year": "1938", "text": "The Civil Aeronautics Act is signed into law, forming the Civil Aeronautics Authority in the United States.", "html": "1938 - The <a href=\"https://wikipedia.org/wiki/Civil_Aeronautics_Act\" class=\"mw-redirect\" title=\"Civil Aeronautics Act\">Civil Aeronautics Act</a> is signed into law, forming the <a href=\"https://wikipedia.org/wiki/Civil_Aeronautics_Authority\" class=\"mw-redirect\" title=\"Civil Aeronautics Authority\">Civil Aeronautics Authority</a> in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Civil_Aeronautics_Act\" class=\"mw-redirect\" title=\"Civil Aeronautics Act\">Civil Aeronautics Act</a> is signed into law, forming the <a href=\"https://wikipedia.org/wiki/Civil_Aeronautics_Authority\" class=\"mw-redirect\" title=\"Civil Aeronautics Authority\">Civil Aeronautics Authority</a> in the United States.", "links": [{"title": "Civil Aeronautics Act", "link": "https://wikipedia.org/wiki/Civil_Aeronautics_Act"}, {"title": "Civil Aeronautics Authority", "link": "https://wikipedia.org/wiki/Civil_Aeronautics_Authority"}]}, {"year": "1940", "text": "<PERSON> goes on a three-hour tour of the architecture of Paris with architect <PERSON> and sculptor <PERSON><PERSON> in his only visit to the city.", "html": "1940 - <PERSON> goes on a three-hour tour of the <a href=\"https://wikipedia.org/wiki/Architecture_of_Paris\" title=\"Architecture of Paris\">architecture of Paris</a> with architect <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and sculptor <a href=\"https://wikipedia.org/wiki/Arno_Breker\" title=\"Arno Breker\"><PERSON><PERSON></a> in his only visit to the city.", "no_year_html": "<PERSON> goes on a three-hour tour of the <a href=\"https://wikipedia.org/wiki/Architecture_of_Paris\" title=\"Architecture of Paris\">architecture of Paris</a> with architect <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and sculptor <a href=\"https://wikipedia.org/wiki/Arno_Breker\" title=\"Arno Breker\"><PERSON><PERSON></a> in his only visit to the city.", "links": [{"title": "Architecture of Paris", "link": "https://wikipedia.org/wiki/Architecture_of_Paris"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1940", "text": "<PERSON> begins the first successful west-to-east navigation of Northwest Passage from Vancouver, British Columbia, Canada.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> begins the first successful west-to-east navigation of <a href=\"https://wikipedia.org/wiki/Northwest_Passage\" title=\"Northwest Passage\">Northwest Passage</a> from Vancouver, <a href=\"https://wikipedia.org/wiki/British_Columbia\" title=\"British Columbia\">British Columbia</a>, Canada.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> begins the first successful west-to-east navigation of <a href=\"https://wikipedia.org/wiki/Northwest_Passage\" title=\"Northwest Passage\">Northwest Passage</a> from Vancouver, <a href=\"https://wikipedia.org/wiki/British_Columbia\" title=\"British Columbia\">British Columbia</a>, Canada.", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>(explorer)"}, {"title": "Northwest Passage", "link": "https://wikipedia.org/wiki/Northwest_Passage"}, {"title": "British Columbia", "link": "https://wikipedia.org/wiki/British_Columbia"}]}, {"year": "1941", "text": "The Lithuanian Activist Front declares independence from the Soviet Union and forms the Provisional Government of Lithuania; it lasts only briefly as the Nazis will occupy Lithuania a few weeks later.", "html": "1941 - The <a href=\"https://wikipedia.org/wiki/Lithuanian_Activist_Front\" title=\"Lithuanian Activist Front\">Lithuanian Activist Front</a> declares <a href=\"https://wikipedia.org/wiki/June_Uprising_in_Lithuania\" title=\"June Uprising in Lithuania\">independence</a> from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> and forms the <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_Lithuania\" title=\"Provisional Government of Lithuania\">Provisional Government of Lithuania</a>; it lasts only briefly as the <a href=\"https://wikipedia.org/wiki/German_occupation_of_Lithuania_during_World_War_II\" title=\"German occupation of Lithuania during World War II\">Nazis will occupy Lithuania</a> a few weeks later.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lithuanian_Activist_Front\" title=\"Lithuanian Activist Front\">Lithuanian Activist Front</a> declares <a href=\"https://wikipedia.org/wiki/June_Uprising_in_Lithuania\" title=\"June Uprising in Lithuania\">independence</a> from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> and forms the <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_Lithuania\" title=\"Provisional Government of Lithuania\">Provisional Government of Lithuania</a>; it lasts only briefly as the <a href=\"https://wikipedia.org/wiki/German_occupation_of_Lithuania_during_World_War_II\" title=\"German occupation of Lithuania during World War II\">Nazis will occupy Lithuania</a> a few weeks later.", "links": [{"title": "Lithuanian Activist Front", "link": "https://wikipedia.org/wiki/Lithuanian_Activist_Front"}, {"title": "June Uprising in Lithuania", "link": "https://wikipedia.org/wiki/June_Uprising_in_Lithuania"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Provisional Government of Lithuania", "link": "https://wikipedia.org/wiki/Provisional_Government_of_Lithuania"}, {"title": "German occupation of Lithuania during World War II", "link": "https://wikipedia.org/wiki/German_occupation_of_Lithuania_during_World_War_II"}]}, {"year": "1942", "text": "World War II: Germany's latest fighter aircraft, a Focke-Wulf Fw 190, is captured intact when it mistakenly lands at RAF Pembrey in Wales.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Germany's latest <a href=\"https://wikipedia.org/wiki/Fighter_aircraft\" title=\"Fighter aircraft\">fighter aircraft</a>, a <a href=\"https://wikipedia.org/wiki/Focke-Wulf_Fw_190\" title=\"Focke-Wulf Fw 190\">Focke-Wulf Fw 190</a>, is captured intact when it <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Faber\" title=\"<PERSON><PERSON> Faber\">mistakenly lands</a> at <a href=\"https://wikipedia.org/wiki/RAF_Pembrey\" class=\"mw-redirect\" title=\"RAF Pembrey\">RAF Pembrey</a> in Wales.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Germany's latest <a href=\"https://wikipedia.org/wiki/Fighter_aircraft\" title=\"Fighter aircraft\">fighter aircraft</a>, a <a href=\"https://wikipedia.org/wiki/Focke-Wulf_Fw_190\" title=\"Focke-Wulf Fw 190\">Focke-Wulf Fw 190</a>, is captured intact when it <a href=\"https://wikipedia.org/wiki/Arm<PERSON>_Faber\" title=\"<PERSON><PERSON> Faber\">mistakenly lands</a> at <a href=\"https://wikipedia.org/wiki/RAF_Pembrey\" class=\"mw-redirect\" title=\"RAF Pembrey\">RAF Pembrey</a> in Wales.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Fighter aircraft", "link": "https://wikipedia.org/wiki/Fighter_aircraft"}, {"title": "Focke-<PERSON><PERSON>w 190", "link": "https://wikipedia.org/wiki/Focke-<PERSON><PERSON>_Fw_190"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "RAF Pembrey", "link": "https://wikipedia.org/wiki/RAF_Pembrey"}]}, {"year": "1946", "text": "The 1946 Vancouver Island earthquake strikes Vancouver Island, British Columbia, Canada.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/1946_Vancouver_Island_earthquake\" title=\"1946 Vancouver Island earthquake\">1946 Vancouver Island earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Vancouver_Island\" title=\"Vancouver Island\">Vancouver Island</a>, British Columbia, Canada.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1946_Vancouver_Island_earthquake\" title=\"1946 Vancouver Island earthquake\">1946 Vancouver Island earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Vancouver_Island\" title=\"Vancouver Island\">Vancouver Island</a>, British Columbia, Canada.", "links": [{"title": "1946 Vancouver Island earthquake", "link": "https://wikipedia.org/wiki/1946_Vancouver_Island_earthquake"}, {"title": "Vancouver Island", "link": "https://wikipedia.org/wiki/Vancouver_Island"}]}, {"year": "1947", "text": "The United States Senate follows the United States House of Representatives in overriding U.S. President <PERSON>'s veto of the Taft-Hartley Act.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> follows the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> in overriding U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Veto_power_in_the_United_States\" title=\"Veto power in the United States\">veto</a> of the <a href=\"https://wikipedia.org/wiki/Taft%E2%80%93Hartley_Act\" title=\"Taft-Hartley Act\">Taft-Hartley Act</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> follows the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> in overriding U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Veto_power_in_the_United_States\" title=\"Veto power in the United States\">veto</a> of the <a href=\"https://wikipedia.org/wiki/Taft%E2%80%93Hartley_Act\" title=\"Taft-Hartley Act\">Taft-Hartley Act</a>.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Veto power in the United States", "link": "https://wikipedia.org/wiki/Veto_power_in_the_United_States"}, {"title": "Taft-Hartley Act", "link": "https://wikipedia.org/wiki/Taft%E2%80%93Hartley_Act"}]}, {"year": "1951", "text": "The ocean liner SS United States is christened and launched.", "html": "1951 - The ocean liner <a href=\"https://wikipedia.org/wiki/SS_United_States\" title=\"SS United States\">SS <i>United States</i></a> is christened and launched.", "no_year_html": "The ocean liner <a href=\"https://wikipedia.org/wiki/SS_United_States\" title=\"SS United States\">SS <i>United States</i></a> is christened and launched.", "links": [{"title": "SS United States", "link": "https://wikipedia.org/wiki/SS_United_States"}]}, {"year": "1956", "text": "The French National Assembly takes the first step in creating the French Community by passing the Loi Cadre, transferring a number of powers from Paris to elected territorial governments in French West Africa.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/French_National_Assembly\" class=\"mw-redirect\" title=\"French National Assembly\">French National Assembly</a> takes the first step in creating the <a href=\"https://wikipedia.org/wiki/French_Community\" title=\"French Community\">French Community</a> by passing the <a href=\"https://wikipedia.org/wiki/Loi_Cadre\" class=\"mw-redirect\" title=\"Loi Cadre\">Loi Cadre</a>, transferring a number of powers from Paris to elected territorial governments in <a href=\"https://wikipedia.org/wiki/French_West_Africa\" title=\"French West Africa\">French West Africa</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_National_Assembly\" class=\"mw-redirect\" title=\"French National Assembly\">French National Assembly</a> takes the first step in creating the <a href=\"https://wikipedia.org/wiki/French_Community\" title=\"French Community\">French Community</a> by passing the <a href=\"https://wikipedia.org/wiki/Loi_Cadre\" class=\"mw-redirect\" title=\"Loi Cadre\"><PERSON><PERSON>ad<PERSON></a>, transferring a number of powers from Paris to elected territorial governments in <a href=\"https://wikipedia.org/wiki/French_West_Africa\" title=\"French West Africa\">French West Africa</a>.", "links": [{"title": "French National Assembly", "link": "https://wikipedia.org/wiki/French_National_Assembly"}, {"title": "French Community", "link": "https://wikipedia.org/wiki/French_Community"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Loi_<PERSON>adre"}, {"title": "French West Africa", "link": "https://wikipedia.org/wiki/French_West_Africa"}]}, {"year": "1959", "text": "Convicted Manhattan Project spy <PERSON> is released after only nine years in prison and allowed to emigrate to Dresden, East Germany where he resumes a scientific career.", "html": "1959 - Convicted <a href=\"https://wikipedia.org/wiki/Manhattan_Project\" title=\"Manhattan Project\">Manhattan Project</a> spy <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is released after only nine years in prison and allowed to emigrate to <a href=\"https://wikipedia.org/wiki/Dresden\" title=\"Dresden\">Dresden</a>, <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> where he resumes a scientific career.", "no_year_html": "Convicted <a href=\"https://wikipedia.org/wiki/Manhattan_Project\" title=\"Manhattan Project\">Manhattan Project</a> spy <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is released after only nine years in prison and allowed to emigrate to <a href=\"https://wikipedia.org/wiki/Dresden\" title=\"Dresden\">Dresden</a>, <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> where he resumes a scientific career.", "links": [{"title": "Manhattan Project", "link": "https://wikipedia.org/wiki/Manhattan_Project"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dresden", "link": "https://wikipedia.org/wiki/Dresden"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}]}, {"year": "1960", "text": "The United States Food and Drug Administration declares Enovid to be the first officially approved combined oral contraceptive pill in the world.", "html": "1960 - The United States <a href=\"https://wikipedia.org/wiki/Food_and_Drug_Administration\" title=\"Food and Drug Administration\">Food and Drug Administration</a> declares <a href=\"https://wikipedia.org/wiki/Mestranol/norethynodrel\" class=\"mw-redirect\" title=\"Mestranol/norethynodrel\">Enovid</a> to be the first officially approved <a href=\"https://wikipedia.org/wiki/Combined_oral_contraceptive_pill\" title=\"Combined oral contraceptive pill\">combined oral contraceptive pill</a> in the world.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Food_and_Drug_Administration\" title=\"Food and Drug Administration\">Food and Drug Administration</a> declares <a href=\"https://wikipedia.org/wiki/Mestranol/norethynodrel\" class=\"mw-redirect\" title=\"Mestranol/norethynodrel\">Enovid</a> to be the first officially approved <a href=\"https://wikipedia.org/wiki/Combined_oral_contraceptive_pill\" title=\"Combined oral contraceptive pill\">combined oral contraceptive pill</a> in the world.", "links": [{"title": "Food and Drug Administration", "link": "https://wikipedia.org/wiki/Food_and_Drug_Administration"}, {"title": "Mestranol/norethynodrel", "link": "https://wikipedia.org/wiki/Mestranol/norethynodrel"}, {"title": "Combined oral contraceptive pill", "link": "https://wikipedia.org/wiki/Combined_oral_contraceptive_pill"}]}, {"year": "1961", "text": "The Antarctic Treaty System, which sets aside Antarctica as a scientific preserve and limits military activity on the continent, its islands and ice shelves, comes into force.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/Antarctic_Treaty_System\" title=\"Antarctic Treaty System\">Antarctic Treaty System</a>, which sets aside Antarctica as a scientific preserve and limits military activity on the continent, its islands and ice shelves, comes into force.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Antarctic_Treaty_System\" title=\"Antarctic Treaty System\">Antarctic Treaty System</a>, which sets aside Antarctica as a scientific preserve and limits military activity on the continent, its islands and ice shelves, comes into force.", "links": [{"title": "Antarctic Treaty System", "link": "https://wikipedia.org/wiki/Antarctic_Treaty_System"}]}, {"year": "1967", "text": "Cold War: U.S. President <PERSON> meets with Soviet Premier <PERSON> in Glassboro, New Jersey for the three-day Glassboro Summit Conference.", "html": "1967 - Cold War: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> meets with Soviet Premier <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Glassboro,_New_Jersey\" title=\"Glassboro, New Jersey\">Glassboro, New Jersey</a> for the three-day <a href=\"https://wikipedia.org/wiki/Glassboro_Summit_Conference\" title=\"Glassboro Summit Conference\">Glassboro Summit Conference</a>.", "no_year_html": "Cold War: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> meets with Soviet Premier <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Glassboro,_New_Jersey\" title=\"Glassboro, New Jersey\">Glassboro, New Jersey</a> for the three-day <a href=\"https://wikipedia.org/wiki/Glassboro_Summit_Conference\" title=\"Glassboro Summit Conference\">Glassboro Summit Conference</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Glassboro, New Jersey", "link": "https://wikipedia.org/wiki/Glassboro,_New_Jersey"}, {"title": "Glassboro Summit Conference", "link": "https://wikipedia.org/wiki/Glassboro_Summit_Conference"}]}, {"year": "1969", "text": "<PERSON> is sworn in as Chief Justice of the United States Supreme Court by retiring Chief Justice <PERSON>.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as Chief Justice of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> by retiring Chief Justice <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as Chief Justice of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> by retiring Chief Justice <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "IBM announces that effective January 1970 it will price its software and services separately from hardware thus creating the modern software industry.", "html": "1969 - IBM announces that effective January 1970 it will price its software and services separately from hardware thus creating the modern <a href=\"https://wikipedia.org/wiki/Software_industry\" title=\"Software industry\">software industry</a>.", "no_year_html": "IBM announces that effective January 1970 it will price its software and services separately from hardware thus creating the modern <a href=\"https://wikipedia.org/wiki/Software_industry\" title=\"Software industry\">software industry</a>.", "links": [{"title": "Software industry", "link": "https://wikipedia.org/wiki/Software_industry"}]}, {"year": "1972", "text": "Watergate scandal: U.S. President <PERSON> and White House Chief of Staff <PERSON><PERSON> <PERSON><PERSON> are taped talking about illegally using the Central Intelligence Agency to obstruct the Federal Bureau of Investigation's investigation into the Watergate break-ins.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> Chief of Staff <a href=\"https://wikipedia.org/wiki/H._<PERSON>._<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> are taped talking about illegally using the <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">Central Intelligence Agency</a> to obstruct the <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">Federal Bureau of Investigation</a>'s investigation into the <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate</a> break-ins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> Chief of Staff <a href=\"https://wikipedia.org/wiki/H._<PERSON>._<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> are taped talking about illegally using the <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">Central Intelligence Agency</a> to obstruct the <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">Federal Bureau of Investigation</a>'s investigation into the <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate</a> break-ins.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Central Intelligence Agency", "link": "https://wikipedia.org/wiki/Central_Intelligence_Agency"}, {"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}, {"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}]}, {"year": "1972", "text": "Title IX of the United States Civil Rights Act of 1964 is amended to prohibit sexual discrimination to any educational program receiving federal funds.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Title_IX\" title=\"Title IX\">Title IX</a> of the United States <a href=\"https://wikipedia.org/wiki/Civil_Rights_Act_of_1964\" title=\"Civil Rights Act of 1964\">Civil Rights Act of 1964</a> is amended to prohibit <a href=\"https://wikipedia.org/wiki/Sexism\" title=\"Sexism\">sexual discrimination</a> to any educational program receiving <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">federal</a> funds.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Title_IX\" title=\"Title IX\">Title IX</a> of the United States <a href=\"https://wikipedia.org/wiki/Civil_Rights_Act_of_1964\" title=\"Civil Rights Act of 1964\">Civil Rights Act of 1964</a> is amended to prohibit <a href=\"https://wikipedia.org/wiki/Sexism\" title=\"Sexism\">sexual discrimination</a> to any educational program receiving <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">federal</a> funds.", "links": [{"title": "Title IX", "link": "https://wikipedia.org/wiki/Title_IX"}, {"title": "Civil Rights Act of 1964", "link": "https://wikipedia.org/wiki/Civil_Rights_Act_of_1964"}, {"title": "Sexism", "link": "https://wikipedia.org/wiki/Sexism"}, {"title": "Federal government of the United States", "link": "https://wikipedia.org/wiki/Federal_government_of_the_United_States"}]}, {"year": "1973", "text": "A fire at a house in Hull, England, which kills a six-year-old boy is passed off as an accident; it later emerges as the first of 26 deaths by fire caused over the next seven years by serial arsonist <PERSON>.", "html": "1973 - A fire at a house in <a href=\"https://wikipedia.org/wiki/Kingston_upon_Hull\" title=\"Kingston upon Hull\">Hull</a>, England, which kills a six-year-old boy is passed off as an accident; it later emerges as the first of 26 deaths by fire caused over the next seven years by serial arsonist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A fire at a house in <a href=\"https://wikipedia.org/wiki/Kingston_upon_Hull\" title=\"Kingston upon Hull\">Hull</a>, England, which kills a six-year-old boy is passed off as an accident; it later emerges as the first of 26 deaths by fire caused over the next seven years by serial arsonist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Kingston upon Hull", "link": "https://wikipedia.org/wiki/Kingston_upon_Hull"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "A terrorist bomb explodes at Narita International Airport near Tokyo, killing two and injuring four. An hour later, the same group detonates a second bomb aboard Air India Flight 182, bringing the Boeing 747 down off the coast of Ireland killing all 329 aboard.", "html": "1985 - A terrorist bomb <a href=\"https://wikipedia.org/wiki/1985_Narita_International_Airport_bombing\" title=\"1985 Narita International Airport bombing\">explodes</a> at <a href=\"https://wikipedia.org/wiki/Narita_International_Airport\" title=\"Narita International Airport\">Narita International Airport</a> near Tokyo, killing two and injuring four. An hour later, the same group detonates a second bomb aboard <a href=\"https://wikipedia.org/wiki/Air_India_Flight_182\" title=\"Air India Flight 182\">Air India Flight 182</a>, bringing the <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a> down off the coast of Ireland killing all 329 aboard.", "no_year_html": "A terrorist bomb <a href=\"https://wikipedia.org/wiki/1985_Narita_International_Airport_bombing\" title=\"1985 Narita International Airport bombing\">explodes</a> at <a href=\"https://wikipedia.org/wiki/Narita_International_Airport\" title=\"Narita International Airport\">Narita International Airport</a> near Tokyo, killing two and injuring four. An hour later, the same group detonates a second bomb aboard <a href=\"https://wikipedia.org/wiki/Air_India_Flight_182\" title=\"Air India Flight 182\">Air India Flight 182</a>, bringing the <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a> down off the coast of Ireland killing all 329 aboard.", "links": [{"title": "1985 Narita International Airport bombing", "link": "https://wikipedia.org/wiki/1985_Narita_International_Airport_bombing"}, {"title": "Narita International Airport", "link": "https://wikipedia.org/wiki/Narita_International_Airport"}, {"title": "Air India Flight 182", "link": "https://wikipedia.org/wiki/Air_India_Flight_182"}, {"title": "Boeing 747", "link": "https://wikipedia.org/wiki/Boeing_747"}]}, {"year": "1991", "text": "Sonic the Hedgehog is released in North America on the Sega Genesis platform, beginning the popular video game franchise.", "html": "1991 - <i><a href=\"https://wikipedia.org/wiki/Sonic_the_Hedgehog_(1991_video_game)\" title=\"Sonic the Hedgehog (1991 video game)\">Sonic the Hedgehog</a></i> is released in North America on the <a href=\"https://wikipedia.org/wiki/Sega_Genesis\" title=\"Sega Genesis\">Sega Genesis</a> platform, beginning <a href=\"https://wikipedia.org/wiki/Sonic_the_Hedgehog\" title=\"Sonic the Hedgehog\">the popular video game franchise</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Sonic_the_Hedgehog_(1991_video_game)\" title=\"Sonic the Hedgehog (1991 video game)\">Sonic the Hedgehog</a></i> is released in North America on the <a href=\"https://wikipedia.org/wiki/Sega_Genesis\" title=\"Sega Genesis\">Sega Genesis</a> platform, beginning <a href=\"https://wikipedia.org/wiki/Sonic_the_Hedgehog\" title=\"Sonic the Hedgehog\">the popular video game franchise</a>.", "links": [{"title": "Sonic the Hedgehog (1991 video game)", "link": "https://wikipedia.org/wiki/Sonic_the_Hedgehog_(1991_video_game)"}, {"title": "Sega Genesis", "link": "https://wikipedia.org/wiki/Sega_Genesis"}, {"title": "<PERSON> the Hedgehog", "link": "https://wikipedia.org/wiki/<PERSON>_the_Hedgehog"}]}, {"year": "1994", "text": "NASA's Space Station Processing Facility, a new state-of-the-art manufacturing building for the International Space Station, officially opens at Kennedy Space Center.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Space_Station_Processing_Facility\" class=\"mw-redirect\" title=\"Space Station Processing Facility\">Space Station Processing Facility</a>, a new state-of-the-art <a href=\"https://wikipedia.org/wiki/Manufacturing_of_the_International_Space_Station\" class=\"mw-redirect\" title=\"Manufacturing of the International Space Station\">manufacturing</a> building for the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>, officially opens at <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Space_Station_Processing_Facility\" class=\"mw-redirect\" title=\"Space Station Processing Facility\">Space Station Processing Facility</a>, a new state-of-the-art <a href=\"https://wikipedia.org/wiki/Manufacturing_of_the_International_Space_Station\" class=\"mw-redirect\" title=\"Manufacturing of the International Space Station\">manufacturing</a> building for the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>, officially opens at <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Space Station Processing Facility", "link": "https://wikipedia.org/wiki/Space_Station_Processing_Facility"}, {"title": "Manufacturing of the International Space Station", "link": "https://wikipedia.org/wiki/Manufacturing_of_the_International_Space_Station"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}]}, {"year": "2001", "text": "The 8.4 Mw  southern Peru earthquake shakes coastal Peru with a maximum Mercalli intensity of VIII (Severe). A destructive tsunami followed, leaving at least 74 people dead, and 2,687 injured.", "html": "2001 - The 8.4 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2001_southern_Peru_earthquake\" title=\"2001 southern Peru earthquake\">southern Peru earthquake</a> shakes coastal Peru with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>). A destructive tsunami followed, leaving at least 74 people dead, and 2,687 injured.", "no_year_html": "The 8.4 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2001_southern_Peru_earthquake\" title=\"2001 southern Peru earthquake\">southern Peru earthquake</a> shakes coastal Peru with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>). A destructive tsunami followed, leaving at least 74 people dead, and 2,687 injured.", "links": [{"title": "2001 southern Peru earthquake", "link": "https://wikipedia.org/wiki/2001_southern_Peru_earthquake"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "2012", "text": "<PERSON> breaks the decathlon world record at the United States Olympic Trials.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ashton Eaton\"><PERSON></a> breaks the <a href=\"https://wikipedia.org/wiki/Decathlon_world_record_progression\" title=\"Decathlon world record progression\">decathlon world record</a> at the <a href=\"https://wikipedia.org/wiki/2012_United_States_Olympic_Trials_(track_and_field)\" class=\"mw-redirect\" title=\"2012 United States Olympic Trials (track and field)\">United States Olympic Trials</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ashton Eaton\"><PERSON></a> breaks the <a href=\"https://wikipedia.org/wiki/Decathlon_world_record_progression\" title=\"Decathlon world record progression\">decathlon world record</a> at the <a href=\"https://wikipedia.org/wiki/2012_United_States_Olympic_Trials_(track_and_field)\" class=\"mw-redirect\" title=\"2012 United States Olympic Trials (track and field)\">United States Olympic Trials</a>.", "links": [{"title": "Ashton Eaton", "link": "https://wikipedia.org/wiki/<PERSON>_Eaton"}, {"title": "Decathlon world record progression", "link": "https://wikipedia.org/wiki/Decathlon_world_record_progression"}, {"title": "2012 United States Olympic Trials (track and field)", "link": "https://wikipedia.org/wiki/2012_United_States_Olympic_Trials_(track_and_field)"}]}, {"year": "2013", "text": "<PERSON> becomes the first man to successfully walk across the Grand Canyon on a tight rope.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Wall<PERSON>\" title=\"<PERSON> Wallenda\"><PERSON></a> becomes the first man to <a href=\"https://wikipedia.org/wiki/Skywire_Live\" title=\"Skywire Live\">successfully walk across</a> the <a href=\"https://wikipedia.org/wiki/Grand_Canyon\" title=\"Grand Canyon\">Grand Canyon</a> on a <a href=\"https://wikipedia.org/wiki/Tightrope_walking\" title=\"Tightrope walking\">tight rope</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wallenda\"><PERSON></a> becomes the first man to <a href=\"https://wikipedia.org/wiki/Skywire_Live\" title=\"Skywire Live\">successfully walk across</a> the <a href=\"https://wikipedia.org/wiki/Grand_Canyon\" title=\"Grand Canyon\">Grand Canyon</a> on a <a href=\"https://wikipedia.org/wiki/Tightrope_walking\" title=\"Tightrope walking\">tight rope</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Skywire Live", "link": "https://wikipedia.org/wiki/Skywire_Live"}, {"title": "Grand Canyon", "link": "https://wikipedia.org/wiki/Grand_Canyon"}, {"title": "Tightrope walking", "link": "https://wikipedia.org/wiki/Tightrope_walking"}]}, {"year": "2013", "text": "Militants storm a high-altitude mountaineering base camp near Nanga Parbat in Gilgit-Baltistan, Pakistan, killing ten climbers and a local guide.", "html": "2013 - Militants storm a high-altitude mountaineering base camp near <a href=\"https://wikipedia.org/wiki/Nanga_Parbat\" title=\"Nanga Parbat\">Nanga Parbat</a> in Gilgit-Baltistan, Pakistan, <a href=\"https://wikipedia.org/wiki/2013_Nanga_Parbat_tourist_shooting\" class=\"mw-redirect\" title=\"2013 Nanga Parbat tourist shooting\">killing ten climbers</a> and a local guide.", "no_year_html": "Militants storm a high-altitude mountaineering base camp near <a href=\"https://wikipedia.org/wiki/Nanga_Parbat\" title=\"Nanga Parbat\">Nanga Parbat</a> in Gilgit-Baltistan, Pakistan, <a href=\"https://wikipedia.org/wiki/2013_Nanga_Parbat_tourist_shooting\" class=\"mw-redirect\" title=\"2013 Nanga Parbat tourist shooting\">killing ten climbers</a> and a local guide.", "links": [{"title": "Nanga Parbat", "link": "https://wikipedia.org/wiki/Nanga_Parbat"}, {"title": "2013 Nanga Parbat tourist shooting", "link": "https://wikipedia.org/wiki/2013_Nanga_Parbat_tourist_shooting"}]}, {"year": "2014", "text": "The last of Syria's declared chemical weapons are shipped out for destruction.", "html": "2014 - The last of Syria's <a href=\"https://wikipedia.org/wiki/Syria_chemical_weapons_program\" class=\"mw-redirect\" title=\"Syria chemical weapons program\">declared chemical weapons</a> are <a href=\"https://wikipedia.org/wiki/Destruction_of_Syria%27s_chemical_weapons\" title=\"Destruction of Syria's chemical weapons\">shipped out for destruction</a>.", "no_year_html": "The last of Syria's <a href=\"https://wikipedia.org/wiki/Syria_chemical_weapons_program\" class=\"mw-redirect\" title=\"Syria chemical weapons program\">declared chemical weapons</a> are <a href=\"https://wikipedia.org/wiki/Destruction_of_Syria%27s_chemical_weapons\" title=\"Destruction of Syria's chemical weapons\">shipped out for destruction</a>.", "links": [{"title": "Syria chemical weapons program", "link": "https://wikipedia.org/wiki/Syria_chemical_weapons_program"}, {"title": "Destruction of Syria's chemical weapons", "link": "https://wikipedia.org/wiki/Destruction_of_Syria%27s_chemical_weapons"}]}, {"year": "2016", "text": "The United Kingdom votes in a referendum to leave the European Union, by 52% to 48%.", "html": "2016 - The <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> votes in a <a href=\"https://wikipedia.org/wiki/2016_United_Kingdom_European_Union_membership_referendum\" title=\"2016 United Kingdom European Union membership referendum\">referendum</a> to leave the European Union, by 52% to 48%.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> votes in a <a href=\"https://wikipedia.org/wiki/2016_United_Kingdom_European_Union_membership_referendum\" title=\"2016 United Kingdom European Union membership referendum\">referendum</a> to leave the European Union, by 52% to 48%.", "links": [{"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "2016 United Kingdom European Union membership referendum", "link": "https://wikipedia.org/wiki/2016_United_Kingdom_European_Union_membership_referendum"}]}, {"year": "2017", "text": "A series of terrorist attacks take place in Pakistan, resulting in 96 deaths and wounding 200 others.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/June_2017_Pakistan_bombings\" class=\"mw-redirect\" title=\"June 2017 Pakistan bombings\">A series of terrorist attacks</a> take place in <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, resulting in 96 deaths and wounding 200 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_2017_Pakistan_bombings\" class=\"mw-redirect\" title=\"June 2017 Pakistan bombings\">A series of terrorist attacks</a> take place in <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, resulting in 96 deaths and wounding 200 others.", "links": [{"title": "June 2017 Pakistan bombings", "link": "https://wikipedia.org/wiki/June_2017_Pakistan_bombings"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "2018", "text": "Twelve boys and an assistant coach from a soccer team in Thailand are trapped in a flooding cave, leading to an 18-day rescue operation.", "html": "2018 - Twelve boys and an assistant coach from a soccer team in Thailand <a href=\"https://wikipedia.org/wiki/Tham_Luang_cave_rescue\" title=\"Tham Luang cave rescue\">are trapped in a flooding cave</a>, leading to an 18-day rescue operation.", "no_year_html": "Twelve boys and an assistant coach from a soccer team in Thailand <a href=\"https://wikipedia.org/wiki/Tham_Luang_cave_rescue\" title=\"Tham Luang cave rescue\">are trapped in a flooding cave</a>, leading to an 18-day rescue operation.", "links": [{"title": "<PERSON><PERSON> cave rescue", "link": "https://wikipedia.org/wiki/T<PERSON>_<PERSON><PERSON>_cave_rescue"}]}], "Births": [{"year": "47 BC", "text": "<PERSON><PERSON>, Egyptian king (d. 30 BC)", "html": "47 BC - 47 BC - <a href=\"https://wikipedia.org/wiki/Caesarion\" title=\"Caesar<PERSON>\"><PERSON><PERSON></a>, Egyptian king (d. 30 BC)", "no_year_html": "47 BC - <a href=\"https://wikipedia.org/wiki/Caesarion\" title=\"Caesar<PERSON>\"><PERSON><PERSON></a>, Egyptian king (d. 30 BC)", "links": [{"title": "Caesarion", "link": "https://wikipedia.org/wiki/Caesarion"}]}, {"year": "1385", "text": "<PERSON>, Count Pa<PERSON> of Simmern-Zweibrücken (d. 1459)", "html": "1385 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_Simmern-Zweibr%C3%BCcken\" title=\"<PERSON>, Count <PERSON><PERSON> of Simmern-Zweibrücken\"><PERSON>, Count <PERSON><PERSON> of Simmern-Zweibrücken</a> (d. 1459)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_Simmern-Zweibr%C3%BCcken\" title=\"<PERSON>, Count <PERSON><PERSON> of Simmern-Zweibrücken\"><PERSON>, Count <PERSON><PERSON> of Simmern-Zweibrücken</a> (d. 1459)", "links": [{"title": "<PERSON>, Count <PERSON><PERSON> of Simmern-Zweibrücken", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_Simmern-Zweibr%C3%BCcken"}]}, {"year": "1433", "text": "<PERSON>, Duke of Brittany (d. 1488)", "html": "1433 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (d. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (d. 1488)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1456", "text": "<PERSON> Denmark, Queen of Scotland (d. 1486)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Denmark,_Queen_of_Scotland\" title=\"<PERSON> of Denmark, Queen of Scotland\"><PERSON> of Denmark, Queen of Scotland</a> (d. 1486)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Denmark,_Queen_of_Scotland\" title=\"<PERSON> of Denmark, Queen of Scotland\"><PERSON> of Denmark, Queen of Scotland</a> (d. 1486)", "links": [{"title": "<PERSON> Denmark, Queen of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Denmark,_Queen_of_Scotland"}]}, {"year": "1489", "text": "<PERSON>, Duke of Savoy, Italian nobleman (d. 1496)", "html": "1489 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a>, Italian nobleman (d. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a>, Italian nobleman (d. 1496)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy"}]}, {"year": "1534", "text": "<PERSON><PERSON>, Japanese warlord (d. 1582)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/Oda_Nobunaga\" title=\"Oda Nobunaga\"><PERSON><PERSON></a>, Japanese warlord (d. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oda_Nobunaga\" title=\"Oda Nobunaga\"><PERSON><PERSON></a>, Japanese warlord (d. 1582)", "links": [{"title": "Oda Nobunaga", "link": "https://wikipedia.org/wiki/Oda_<PERSON>ga"}]}, {"year": "1596", "text": "<PERSON>, Swedish field marshal (d. 1641)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r\" title=\"<PERSON>\"><PERSON></a>, Swedish field marshal (d. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r\" title=\"<PERSON>\"><PERSON></a>, Swedish field marshal (d. 1641)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r"}]}, {"year": "1616", "text": "<PERSON>, Mughal prince (d. 1661)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(Mughal_prince)\" title=\"<PERSON> (Mughal prince)\"><PERSON></a>, Mughal prince (d. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(Mughal_prince)\" title=\"<PERSON> (Mughal prince)\"><PERSON></a>, Mughal prince (d. 1661)", "links": [{"title": "<PERSON> (Mughal prince)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(Mughal_prince)"}]}, {"year": "1625", "text": "<PERSON>, English churchman and influential academic (d. 1686)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English churchman and influential academic (d. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English churchman and influential academic (d. 1686)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)"}]}, {"year": "1668", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian jurist, historian, and philosopher (d. 1744)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian jurist, historian, and philosopher (d. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian jurist, historian, and philosopher (d. 1744)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON>, French orientalist and sinologist (d. 1745)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French orientalist and sinologist (d. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French orientalist and sinologist (d. 1745)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_Fourmont"}]}, {"year": "1711", "text": "<PERSON>, Italian instrument maker (d. 1786)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian instrument maker (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian instrument maker (d. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1716", "text": "<PERSON>, 1st Baron <PERSON>, English lawyer and politician, Solicitor General for England and Wales (d. 1789)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales\" title=\"Solicitor General for England and Wales\">Solicitor General for England and Wales</a> (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales\" title=\"Solicitor General for England and Wales\">Solicitor General for England and Wales</a> (d. 1789)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Solicitor General for England and Wales", "link": "https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales"}]}, {"year": "1750", "text": "<PERSON><PERSON><PERSON><PERSON>, French geologist and academic (d. 1801)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/D%C3%A9odat_Gratet_de_Dolomieu\" title=\"Déodat Gratet de Dolomieu\">Déodat Gratet de Dolomieu</a>, French geologist and academic (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A9odat_Gratet_de_Dolomieu\" title=\"Déodat Gratet de Dolomieu\">Déodat Gratet de Dolomieu</a>, French geologist and academic (d. 1801)", "links": [{"title": "Déodat Gratet de Dolomieu", "link": "https://wikipedia.org/wiki/D%C3%A9odat_Gratet_de_Dolomieu"}]}, {"year": "1763", "text": "<PERSON><PERSON>, French wife of <PERSON> (d. 1814)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French wife of <a href=\"https://wikipedia.org/wiki/Napoleon_I\" class=\"mw-redirect\" title=\"Napoleon I\"><PERSON> I</a> (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French wife of <a href=\"https://wikipedia.org/wiki/Napoleon_I\" class=\"mw-redirect\" title=\"Napoleon I\"><PERSON> I</a> (d. 1814)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9<PERSON>_<PERSON>_<PERSON>s"}, {"title": "<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_I"}]}, {"year": "1799", "text": "<PERSON>, American physician and politician (d. 1881)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON><PERSON>, Polish physician and activist (d. 1846)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish physician and activist (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish physician and activist (d. 1846)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, German pianist, composer, and conductor (d. 1910)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, German scientist (d. 1927)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scientist (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scientist (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Belgian poet and librarian (d. 1929)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian poet and librarian (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian poet and librarian (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON>, Hungarian author and journalist (d. 1924)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Br%C3%B3dy_(writer)\" title=\"<PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON></a>, Hungarian author and journalist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Br%C3%B3<PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON></a>, Hungarian author and journalist (d. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_Br%C3%B3dy_(writer)"}]}, {"year": "1877", "text": "<PERSON>, Indian-English hurdler and actor (d. 1929)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English hurdler and actor (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English hurdler and actor (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Egyptian feminist and journalist (d. 1947)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Huda_Sha%27arawi\" title=\"Hu<PERSON>raw<PERSON>\"><PERSON><PERSON></a>, Egyptian feminist and journalist (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Huda_Sha%27arawi\" title=\"<PERSON><PERSON>raw<PERSON>\"><PERSON><PERSON></a>, Egyptian feminist and journalist (d. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Huda_Sha%27arawi"}]}, {"year": "1884", "text": "<PERSON>, Canadian ice hockey player and politician (d. 1979)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and politician (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Taylor\"><PERSON></a>, Canadian ice hockey player and politician (d. 1979)", "links": [{"title": "Cyclone <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, American publisher and politician (d. 1935)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Cutting\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American publisher and politician (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Cutting\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American publisher and politician (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ting"}]}, {"year": "1889", "text": "<PERSON>, Ukrainian-Russian poet and author (d. 1966)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian poet and author (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian poet and author (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, English engineer (d. 1964)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English engineer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English engineer (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, New Zealand military leader, lawyer and Chief Justice (d. 1972)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand military leader, lawyer and <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_New_Zealand\" title=\"Chief Justice of New Zealand\">Chief Justice</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand military leader, lawyer and <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_New_Zealand\" title=\"Chief Justice of New Zealand\">Chief Justice</a> (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chief Justice of New Zealand", "link": "https://wikipedia.org/wiki/Chief_Justice_of_New_Zealand"}]}, {"year": "1894", "text": "<PERSON>, American entomologist and sexologist (d. 1956)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entomologist and sexologist (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entomologist and sexologist (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, King of the United Kingdom (d. 1972)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VIII\"><PERSON></a>, King of the United Kingdom (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VIII\"><PERSON></a>, King of the United Kingdom (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, English novelist and journalist (d. 1935)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English novelist and journalist (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English novelist and journalist (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian-born French race car driver and sports car manufacturer (d. 1979)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Am%C3%A9d%C3%A9e_<PERSON><PERSON>ni\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-born French race car driver and sports car manufacturer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am%C3%A9d%C3%A9<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>é<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-born French race car driver and sports car manufacturer (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Am%C3%A9d%C3%A9e_<PERSON><PERSON><PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American aviator, winner of the 1936 Bendix Trophy Race (d. 1981) ", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aviator, winner of the 1936 <a href=\"https://wikipedia.org/wiki/Bendix_Trophy\" title=\"Bendix Trophy\">Bendix Trophy Race</a> (d. 1981) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aviator, winner of the 1936 <a href=\"https://wikipedia.org/wiki/Bendix_Trophy\" title=\"Bendix Trophy\">Bendix Trophy Race</a> (d. 1981) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bendix Trophy", "link": "https://wikipedia.org/wiki/Bendix_Trophy"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Turkish author, poet, and scholar (d. 1962)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Ah<PERSON>_<PERSON><PERSON>_Tanp%C4%B1nar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish author, poet, and scholar (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>p%C4%B1nar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish author, poet, and scholar (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ham<PERSON>_Tanp%C4%B1nar"}]}, {"year": "1903", "text": "<PERSON>, Canadian lawyer and politician (d. 1992)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 1992)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, South African cricketer (d. 1938)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer (d. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uin<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Canadian civil servant and politician, 35th Secretary of State for Canada (d. 1997)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian civil servant and politician, 35th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian civil servant and politician, 35th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Canada", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Canada"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON> of Nepal (d. 1955)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Tribhuvan_of_Nepal\" title=\"<PERSON><PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON><PERSON> of Nepal</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON><PERSON> of Nepal</a> (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Nepal", "link": "https://wikipedia.org/wiki/Tribhuvan_of_Nepal"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Brazilian actress and singer (d. 2008)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Der<PERSON>_<PERSON>n%C3%A7alves\" title=\"<PERSON><PERSON> Gon<PERSON>lves\"><PERSON><PERSON></a>, Brazilian actress and singer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Der<PERSON>_<PERSON>n%C3%A7alves\" title=\"<PERSON><PERSON> Gon<PERSON>\"><PERSON><PERSON></a>, Brazilian actress and singer (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dercy_Gon%C3%A7alves"}]}, {"year": "1907", "text": "<PERSON>, English economist and academic, Nobel Prize laureate (d. 1995)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1909", "text": "<PERSON>, Russian-Canadian lawyer and politician (d. 1981)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Russian-Canadian lawyer and politician (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Russian-Canadian lawyer and politician (d. 1981)", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)"}]}, {"year": "1909", "text": "<PERSON>, French actor, director, and screenwriter (d. 1989)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, French playwright and screenwriter (d. 1987)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and screenwriter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and screenwriter (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American religious leader, 15th President of The Church of Jesus Christ of Latter-day Saints (d. 2008)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 15th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 15th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1910", "text": "<PERSON><PERSON>, American bassist and photographer (d. 2000)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bassist and photographer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bassist and photographer (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nton"}]}, {"year": "1910", "text": "<PERSON>, English yachtsman, naval commander and author (d. 2012)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English yachtsman, naval commander and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English yachtsman, naval commander and author (d. 2012)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1910", "text": "<PERSON>, American golfer (d. 1968)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lawson Little\"><PERSON></a>, American golfer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lawson Little\"><PERSON></a>, American golfer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English mathematician and computer scientist (d. 1954)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and computer scientist (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Turin<PERSON>\"><PERSON></a>, English mathematician and computer scientist (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American commander, lawyer, and politician, 55th United States Secretary of State (d. 2001)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, lawyer, and politician, 55th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, lawyer, and politician, 55th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1915", "text": "<PERSON>, American artist and inventor (d. 2016)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and inventor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and inventor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English cricketer and soldier (d. 1990)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actress (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American bandleader and conductor (d. 2020)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and conductor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and conductor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Algerian politician, President of Algeria (d. 1992)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Algeria\" title=\"List of heads of state of Algeria\">President of Algeria</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Algeria\" title=\"List of heads of state of Algeria\">President of Algeria</a> (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of heads of state of Algeria", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Algeria"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Kuwaiti astronomer (d. 2022)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kuwaiti astronomer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kuwaiti astronomer (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American politician (d. 2019)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American lieutenant and physicist (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and physicist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and physicist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Canadian ice hockey player and coach (d. 1998)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Irish-English footballer and manager (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English footballer and manager (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English footballer and manager (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American screenwriter and producer (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter and producer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American politician (d. 2021)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American professional basketball player (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional basketball player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional basketball player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian communist and Partisan (d. 1945)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian communist and Partisan (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian communist and Partisan (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1924", "text": "<PERSON>, American comic-strip artist, comic-book artist and illustrator (d. 2020)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic-strip artist, comic-book artist and illustrator (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic-strip artist, comic-book artist and illustrator (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English actress (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American businessman (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Art_Modell\" title=\"Art Modell\"><PERSON></a>, American businessman (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Modell\" title=\"Art Modell\"><PERSON></a>, American businessman (d. 2012)", "links": [{"title": "Art Modell", "link": "https://wikipedia.org/wiki/Art_Modell"}]}, {"year": "1925", "text": "<PERSON>, Chinese widow of Lieutenant General <PERSON> (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese widow of Lieutenant General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese widow of Lieutenant General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, <PERSON> of Swaffham Prior, English microbiologist and parasitologist (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Swaffham_Prior\" title=\"<PERSON>, Baron <PERSON> of Swaffham Prior\"><PERSON>, Baron <PERSON> of Swaffham Prior</a>, English microbiologist and parasitologist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Swaffham_Prior\" title=\"<PERSON>, Baron <PERSON> of Swaffham Prior\"><PERSON>, Baron <PERSON> of Swaffham Prior</a>, English microbiologist and parasitologist (d. 2017)", "links": [{"title": "<PERSON>, <PERSON> of Swaffham Prior", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Swaffham_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Romanian author, poet and composer, survivor of the Holocaust (d. 2021)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian author, poet and composer, survivor of the Holocaust (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian author, poet and composer, survivor of the Holocaust (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Senegalese writer", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Erneville\" title=\"<PERSON>Erne<PERSON>\"><PERSON></a>, Senegalese writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Erneville\" title=\"<PERSON>\"><PERSON></a>, Senegalese writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Erneville"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Italian sculptor", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Arnaldo_Pomodoro\" title=\"<PERSON>rnaldo Pomodor<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arnal<PERSON>_Pomodoro\" title=\"<PERSON>rnaldo Pomodoro\"><PERSON><PERSON><PERSON></a>, Italian sculptor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arnaldo_Pomodoro"}]}, {"year": "1927", "text": "<PERSON>, American actor, dancer, choreographer, and director (d. 1987)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, choreographer, and director (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, choreographer, and director (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Baron <PERSON>, English archbishop (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English archbishop (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English archbishop (d. 2019)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American baseball player (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, German politician", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi"}]}, {"year": "1928", "text": "<PERSON>, American author and academic (d. 1988)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer-songwriter, musician, and actress (d. 2003)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/June_<PERSON>_<PERSON>\" title=\"June Carter Cash\">June <PERSON></a>, American singer-songwriter, musician, and actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON>_<PERSON>\" title=\"June <PERSON> Cash\">June <PERSON></a>, American singer-songwriter, musician, and actress (d. 2003)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Italian racing cyclist (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing cyclist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing cyclist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON> <PERSON>, American colonel, pilot, and astronaut (d. 1987)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Don<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American colonel, pilot, and astronaut (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American colonel, pilot, and astronaut (d. 1987)", "links": [{"title": "Donn F. <PERSON>le", "link": "https://wikipedia.org/wiki/Donn_<PERSON><PERSON>_<PERSON><PERSON>le"}]}, {"year": "1930", "text": "<PERSON>, English historian and academic (d. 2022) ", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" class=\"mw-redirect\" title=\"<PERSON> (historian)\"><PERSON></a>, English historian and academic (d. 2022) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(historian)\" class=\"mw-redirect\" title=\"<PERSON> (historian)\"><PERSON></a>, English historian and academic (d. 2022) ", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>(historian)"}]}, {"year": "1930", "text": "<PERSON>, 2nd Baron <PERSON>, English businessman and politician", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English poet, critic, and academic (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, critic, and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, critic, and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, former First Lady of Ivory Coast", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>-Th%C3%A9r%C3%A8se_<PERSON><PERSON><PERSON>%C3%ABt-Boigny\" title=\"<PERSON>-<PERSON>h<PERSON>r<PERSON><PERSON>-Boigny\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, former First Lady of Ivory Coast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-Th%C3%A9r%C3%A8se_<PERSON><PERSON><PERSON>%C3%ABt-Boigny\" title=\"<PERSON>-Th<PERSON>r<PERSON><PERSON>hou<PERSON>t-Boigny\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, former First Lady of Ivory Coast", "links": [{"title": "Marie-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-Boigny", "link": "https://wikipedia.org/wiki/Marie-Th%C3%A9r%C3%A8se_<PERSON><PERSON>hou%C3%ABt-Boigny"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Estonian chess player (d. 1981)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Swedish politician and diplomat (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician and diplomat (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician and diplomat (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, <PERSON>, English lawyer and judge (d. 2021)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and judge (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and judge (d. 2021)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English bishop (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (d. 2017)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1934", "text": "<PERSON>, Canadian businessman (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian politician (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Puerto Rican-American politician, 32nd Mayor of Miami (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American politician, 32nd <a href=\"https://wikipedia.org/wiki/Mayor_of_Miami\" class=\"mw-redirect\" title=\"Mayor of Miami\">Mayor of Miami</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American politician, 32nd <a href=\"https://wikipedia.org/wiki/Mayor_of_Miami\" class=\"mw-redirect\" title=\"Mayor of Miami\">Mayor of Miami</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}, {"title": "Mayor of Miami", "link": "https://wikipedia.org/wiki/Mayor_of_Miami"}]}, {"year": "1935", "text": "<PERSON>, English footballer and manager", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American novelist and essayist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Greek economist, lawyer, and politician, 180th Prime Minister of Greece (d. 2025)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek economist, lawyer, and politician, 180th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek economist, lawyer, and politician, 180th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 2025)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Finnish captain and politician, 10th President of Finland, Nobel Prize laureate (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish captain and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish captain and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1937", "text": "<PERSON>, English academic and politician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American guitarist and songwriter  (d. 2004)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist and songwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist and songwriter (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American sculptor (d. 1989)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English singer (d. 2003)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Adam_Faith\" title=\"Adam Faith\"><PERSON></a>, English singer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adam_Faith\" title=\"Adam Faith\"><PERSON></a>, English singer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American sex cult leader and two-time prison escapee (d. 2009)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sex cult leader and two-time prison escapee (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sex cult leader and two-time prison escapee (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Baron <PERSON> of Lairg, Scottish lawyer, judge, and politician, Lord High Chancellor of Great Britain", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Irvine,_Baron_<PERSON>_of_Lairg\" title=\"<PERSON>, Baron <PERSON> of Lairg\"><PERSON>, Baron <PERSON> of Lairg</a>, Scottish lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Irvine,_Baron_<PERSON>_of_Lairg\" title=\"<PERSON>, Baron <PERSON> of Lairg\"><PERSON>, Baron <PERSON> of Lairg</a>, Scottish lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a>", "links": [{"title": "<PERSON>, <PERSON> of Lairg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Lairg"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, American runner (d. 1994)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American runner (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American runner (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, New Zealand cricketer and coach (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Scottish painter and musician (d. 1962)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter and musician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter and musician (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian singer-songwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2019)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lyricist)\" title=\"<PERSON> (lyricist)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lyricist)\" title=\"<PERSON> (lyricist)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2019)", "links": [{"title": "<PERSON> (lyricist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lyricist)"}]}, {"year": "1941", "text": "<PERSON>, Australian author and screenwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English footballer (d. 1998)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 1998)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1942", "text": "<PERSON>, <PERSON> of Ludlow, English cosmologist and astrophysicist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Ludlow\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Ludlow\"><PERSON>, Baron <PERSON> of Ludlow</a>, English cosmologist and astrophysicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Ludlow\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Ludlow\"><PERSON>, Baron <PERSON> of Ludlow</a>, English cosmologist and astrophysicist", "links": [{"title": "<PERSON>, Baron <PERSON> of Ludlow", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Ludlow"}]}, {"year": "1943", "text": "<PERSON>, French filmmaker", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American computer scientist and Internet pioneer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>t Cerf\"><PERSON><PERSON></a>, American computer scientist and Internet pioneer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>t Cerf\"><PERSON><PERSON></a>, American computer scientist and Internet pioneer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>t_Cerf"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American psychologist and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American psychologist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American pianist and conductor (d. 2021)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and conductor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and conductor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Swedish journalist and author (d. 2016)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist and author (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Sudanese colonel and politician, President of Southern Sudan (d. 2005)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese colonel and politician, <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_South_Sudan\" class=\"mw-redirect\" title=\"List of presidents of South Sudan\">President of Southern Sudan</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese colonel and politician, <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_South_Sudan\" class=\"mw-redirect\" title=\"List of presidents of South Sudan\">President of Southern Sudan</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of presidents of South Sudan", "link": "https://wikipedia.org/wiki/List_of_presidents_of_South_Sudan"}]}, {"year": "1946", "text": "<PERSON>, English polo player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English polo player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English polo player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Australian actor and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American lawyer and jurist, Associate Justice of the Supreme Court of the United States", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice of the Supreme Court of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice of the Supreme Court of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Associate Justice of the Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States"}]}, {"year": "1949", "text": "<PERSON>, Australian journalist and sportscaster", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, <PERSON>, English accountant and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English accountant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English accountant and politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Puerto Rican-American political scientist, activist, and academic, founded the National Institute for Latino Policy (d. 2018)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American political scientist, activist, and academic, founded the <a href=\"https://wikipedia.org/wiki/National_Institute_for_Latino_Policy\" class=\"mw-redirect\" title=\"National Institute for Latino Policy\">National Institute for Latino Policy</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American political scientist, activist, and academic, founded the <a href=\"https://wikipedia.org/wiki/National_Institute_for_Latino_Policy\" class=\"mw-redirect\" title=\"National Institute for Latino Policy\">National Institute for Latino Policy</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Angelo_Falc%C3%B3n"}, {"title": "National Institute for Latino Policy", "link": "https://wikipedia.org/wiki/National_Institute_for_Latino_Policy"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, French race car driver and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Mich%C3%A8le_<PERSON>uton\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French race car driver and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mich%C3%A8le_Mouton\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French race car driver and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mich%C3%A8le_Mouton"}]}, {"year": "1952", "text": "<PERSON>, Indian actor and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Armenian physicist, politician and President of Armenia", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Armen_<PERSON>\" title=\"Armen <PERSON>rk<PERSON>\"><PERSON><PERSON></a>, Armenian physicist, politician and President of Armenia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armen_<PERSON>\" title=\"Arm<PERSON>\"><PERSON><PERSON></a>, Armenian physicist, politician and President of Armenia", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Armen_<PERSON>ian"}]}, {"year": "1955", "text": "<PERSON>, Canadian dentist and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian dentist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian dentist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, French footballer and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian academic and educator", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American football player and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON> (wide receiver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)"}]}, {"year": "1956", "text": "<PERSON>, American bass player and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Zimbabwean cricketer and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Zimbabwean cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Zimbabwean cricketer and coach", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1957", "text": "<PERSON>, American actress, winner of the Triple Crown of Acting", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, winner of the <a href=\"https://wikipedia.org/wiki/Triple_Crown_of_Acting\" title=\"Triple Crown of Acting\">Triple Crown of Acting</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, winner of the <a href=\"https://wikipedia.org/wiki/Triple_Crown_of_Acting\" title=\"Triple Crown of Acting\">Triple Crown of Acting</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}, {"title": "Triple Crown of Acting", "link": "https://wikipedia.org/wiki/Triple_Crown_of_Acting"}]}, {"year": "1958", "text": "<PERSON>, English politician, Minister of State at the Department of Energy and Climate Change", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_at_the_Department_of_Energy_and_Climate_Change\" title=\"Minister of State at the Department of Energy and Climate Change\">Minister of State at the Department of Energy and Climate Change</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_at_the_Department_of_Energy_and_Climate_Change\" title=\"Minister of State at the Department of Energy and Climate Change\">Minister of State at the Department of Energy and Climate Change</a>", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_politician)"}, {"title": "Minister of State at the Department of Energy and Climate Change", "link": "https://wikipedia.org/wiki/Minister_of_State_at_the_Department_of_Energy_and_Climate_Change"}]}, {"year": "1960", "text": "<PERSON>, American saxophonist, composer, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese composer and programmer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer and programmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer and programmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English lawyer and judge", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(judge)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Serbian singer and illustrator", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian singer and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian singer and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, American basketball player, coach, and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player, coach, and manager", "links": [{"title": "LaSalle Thompson", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Thompson"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(vocalist)\" class=\"mw-redirect\" title=\"<PERSON> (vocalist)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(vocalist)\" class=\"mw-redirect\" title=\"<PERSON> (vocalist)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (vocalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(vocalist)"}]}, {"year": "1963", "text": "<PERSON>, Scottish golfer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>go<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian economist and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian actress and singer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tara_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American director, producer, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Whedon\" title=\"<PERSON><PERSON> Whedon\"><PERSON><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Whedon\" title=\"<PERSON>ss Whedon\"><PERSON><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>on"}]}, {"year": "1964", "text": "<PERSON>, Chinese gymnast", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American government and non-profit executive", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American government and non-profit executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American government and non-profit executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian golfer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27M<PERSON><PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27M<PERSON><PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27M<PERSON><PERSON>_(golfer)"}]}, {"year": "1966", "text": "<PERSON>, American singer and pianist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_DeBarge"}]}, {"year": "1969", "text": "<PERSON>, American actor, producer, and stuntman", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and stuntman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and stuntman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American football player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1970", "text": "<PERSON>, Canadian singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, French singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian actor and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Potvin\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Potvin\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Potvin"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Dutch dancer and choreographer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch dancer and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, French footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Zinedine_Zidane\" title=\"Zinedine Zidane\"><PERSON><PERSON><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zinedine_Zidane\" title=\"Zinedine Zidane\"><PERSON><PERSON><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zinedine_Zidane"}]}, {"year": "1974", "text": "<PERSON>, Australian actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball and baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball and baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball and baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English golfer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, English golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, English golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1975", "text": "<PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1975)\" title=\"<PERSON> (basketball, born 1975)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1975)\" title=\"<PERSON> (basketball, born 1975)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1975)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1975)"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Scottish singer-songwriter and musician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/KT_<PERSON>\" title=\"KT <PERSON>all\"><PERSON><PERSON></a>, Scottish singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/KT_<PERSON>\" title=\"KT Tunstall\"><PERSON><PERSON></a>, Scottish singer-songwriter and musician", "links": [{"title": "KT Tunstall", "link": "https://wikipedia.org/wiki/KT_<PERSON>all"}]}, {"year": "1976", "text": "<PERSON>, American soccer player and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player and manager", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)"}]}, {"year": "1976", "text": "<PERSON>, American guitarist and composer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and composer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Greek-Cypriot footballer and scout", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Sav<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Cypriot footballer and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sav<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Cypriot footballer and scout", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sav<PERSON>_<PERSON>ursaitidis"}]}, {"year": "1976", "text": "<PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Argentinian tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Paola_Su%C3%A1rez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paola_Su%C3%A1rez\" title=\"Pa<PERSON>\"><PERSON><PERSON></a>, Argentinian tennis player", "links": [{"title": "Paola Suárez", "link": "https://wikipedia.org/wiki/Paola_Su%C3%A1rez"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Canadian actress and singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Vaugier"}]}, {"year": "1976", "text": "<PERSON>, French footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Spanish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Estonian ski jumper", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BCris\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BCris\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian ski jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaan_J%C3%BCris"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mr<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara"}]}, {"year": "1978", "text": "Memphis Bleek, American rapper, producer, and actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Memphis_Bleek\" title=\"Memphis Bleek\">Memphis Bleek</a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Memphis_Bleek\" title=\"Memphis Bleek\">Memphis Bleek</a>, American rapper, producer, and actor", "links": [{"title": "Memphis Bleek", "link": "https://wikipedia.org/wiki/Memphis_Bleek"}]}, {"year": "1978", "text": "<PERSON>, French heavy metal musician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French heavy metal musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French heavy metal musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American football player and sportscaster", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Light\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Light\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">La<PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American author and illustrator", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Italian tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, German rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}]}, {"year": "1982", "text": "<PERSON>, Canadian-American ice hockey player (d. 2011)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Chilean footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON><PERSON>_(footballer,_born_1983)\" title=\"<PERSON> (footballer, born 1983)\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>%C3%A<PERSON>_<PERSON><PERSON>_(footballer,_born_1983)\" title=\"<PERSON> (footballer, born 1983)\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON> (footballer, born 1983)", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON><PERSON><PERSON>_(footballer,_born_1983)"}]}, {"year": "1984", "text": "<PERSON>, Welsh singer-songwriter and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Welsh singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Welsh singer-songwriter and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Japanese swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Saint Lucian high jumper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Saint Lucian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Saint Lucian high jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress and singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Italian swimmer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ess<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Australian singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>et_<PERSON>aker\" title=\"Chet Faker\"><PERSON><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>et_<PERSON>aker\" title=\"Chet Faker\"><PERSON><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chet_<PERSON>aker"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, American gymnast", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ch<PERSON><PERSON>mmel\"><PERSON><PERSON><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ch<PERSON><PERSON>mmel\"><PERSON><PERSON><PERSON></a>, American gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ch<PERSON><PERSON>_<PERSON>mmel"}]}, {"year": "1989", "text": "<PERSON>, New Zealand flatwater canoeist", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand flatwater canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand flatwater canoeist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Clevid_Di<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clev<PERSON>_Di<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Clevid_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Canadian tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Vasek_Pospisil\" title=\"Vasek Pospisil\"><PERSON><PERSON><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vase<PERSON>_Pospisil\" title=\"Vasek Pospisil\"><PERSON><PERSON><PERSON></a>, Canadian tennis player", "links": [{"title": "Vasek Pospisil", "link": "https://wikipedia.org/wiki/Vasek_Pospisil"}]}, {"year": "1990", "text": "<PERSON>, Spanish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_R%C3%A0fols\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_R%C3%A0fols\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Laura_R%C3%A0fols"}]}, {"year": "1991", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Uzbekistani gymnast", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>aliulin<PERSON>\"><PERSON><PERSON></a>, Uzbekistani gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>aliulin<PERSON>\"><PERSON><PERSON></a>, Uzbekistani gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mendy\" title=\"Nam<PERSON>ys Mendy\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mendy\" title=\"Nampalys Mendy\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nampalys_Mendy"}]}, {"year": "1993", "text": "<PERSON>, American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1993", "text": "<PERSON>, German footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian cricketer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>a, New Zealand rugby league player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Starford_To%27a\" title=\"Starford To'a\">Star<PERSON> To'a</a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Starford_To%27a\" title=\"Starford To'a\">Starford To'a</a>, New Zealand rugby league player", "links": [{"title": "Starford To'a", "link": "https://wikipedia.org/wiki/Starford_To%27a"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, American dancer", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American dancer and YouTuber", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer and <a href=\"https://wikipedia.org/wiki/YouTuber\" title=\"YouTuber\">YouTuber</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer and <a href=\"https://wikipedia.org/wiki/YouTuber\" title=\"YouTuber\">YouTuber</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "YouTuber", "link": "https://wikipedia.org/wiki/YouTuber"}]}], "Deaths": [{"year": "79", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman emperor (b. AD 9)", "html": "79 - <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (b. AD 9)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (b. AD 9)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vespasian"}]}, {"year": "679", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, English saint (b. 636)", "html": "679 - <a href=\"https://wikipedia.org/wiki/%C3%86thelthryth\" title=\"Æthelthryt<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English saint (b. 636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86thelthryth\" title=\"Æthe<PERSON>hryt<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English saint (b. 636)", "links": [{"title": "Æ<PERSON>lt<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%86thelthryth"}]}, {"year": "947", "text": "<PERSON>, prince of Later Tang (b. 931)", "html": "947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Congyi\"><PERSON></a>, prince of Later Tang (b. 931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Congyi\"><PERSON></a>, prince of Later Tang (b. 931)", "links": [{"title": "<PERSON>gyi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gyi"}]}, {"year": "947", "text": "<PERSON>, imperial consort of Later Tang", "html": "947 - <a href=\"https://wikipedia.org/wiki/Consort_<PERSON><PERSON>_<PERSON>\" title=\"Consort <PERSON><PERSON>\"><PERSON></a>, imperial consort of Later Tang", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Consort_<PERSON><PERSON>_<PERSON>\" title=\"Consort Dow<PERSON>\"><PERSON></a>, imperial consort of Later Tang", "links": [{"title": "Consort Dowager <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "960", "text": "<PERSON>, chancellor of Southern Tang (b. 903)", "html": "960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Feng Yanji\"><PERSON></a>, chancellor of Southern Tang (b. 903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Feng Yanji\"><PERSON></a>, chancellor of Southern Tang (b. 903)", "links": [{"title": "Feng <PERSON>", "link": "https://wikipedia.org/wiki/Feng_<PERSON>"}]}, {"year": "994", "text": "<PERSON><PERSON><PERSON>, count of Stade (b. 950)", "html": "994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Stade\" title=\"<PERSON><PERSON><PERSON>, Count of Stade\"><PERSON><PERSON><PERSON></a>, count of Stade (b. 950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Stade\" title=\"<PERSON><PERSON><PERSON>, Count of Stade\"><PERSON><PERSON><PERSON></a>, count of Stade (b. 950)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Stade", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Count_of_Stade"}]}, {"year": "1018", "text": "<PERSON>, margrave of Austria", "html": "1018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Austria\" title=\"<PERSON>, Margrave of Austria\"><PERSON></a>, margrave of Austria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Austria\" title=\"<PERSON>, Mar<PERSON> of Austria\"><PERSON></a>, margrave of Austria", "links": [{"title": "<PERSON>, Margrave of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Austria"}]}, {"year": "1137", "text": "<PERSON><PERSON> of Mainz, German archbishop", "html": "1137 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Mainz\" title=\"<PERSON><PERSON> of Mainz\"><PERSON><PERSON> of Mainz</a>, German archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Mainz\" title=\"<PERSON><PERSON> of Mainz\"><PERSON><PERSON> of Mainz</a>, German archbishop", "links": [{"title": "<PERSON><PERSON> of Mainz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Mainz"}]}, {"year": "1222", "text": "<PERSON> of Aragon, Hungarian queen (b. 1179)", "html": "1222 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Aragon,_Holy_Roman_Empress\" title=\"<PERSON> of Aragon, Holy Roman Empress\"><PERSON> of Aragon</a>, Hungarian queen (b. 1179)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Aragon,_Holy_Roman_Empress\" title=\"<PERSON> of Aragon, Holy Roman Empress\"><PERSON> of Aragon</a>, Hungarian queen (b. 1179)", "links": [{"title": "<PERSON> of Aragon, Holy Roman Empress", "link": "https://wikipedia.org/wiki/<PERSON>_of_Aragon,_Holy_Roman_Empress"}]}, {"year": "1290", "text": "<PERSON><PERSON> <PERSON>, duke of Wrocław and high duke of Kraków (b. c. 1258)", "html": "1290 - <a href=\"https://wikipedia.org/wiki/Henryk_IV_Probus\" class=\"mw-redirect\" title=\"Henryk IV Probus\"><PERSON><PERSON> IV Probus</a>, duke of Wrocław and high duke of Kraków (b. c. 1258)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henryk_IV_Probus\" class=\"mw-redirect\" title=\"Henryk IV Probus\"><PERSON><PERSON> IV Probus</a>, duke of Wrocław and high duke of Kraków (b. c. 1258)", "links": [{"title": "Henryk IV Probus", "link": "https://wikipedia.org/wiki/Henryk_IV_Probus"}]}, {"year": "1314", "text": "<PERSON>, English knight", "html": "1314 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English knight", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English knight", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1324", "text": "<PERSON><PERSON><PERSON>, 2nd Earl of Pembroke (b. 1270)", "html": "1324 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke\" title=\"<PERSON><PERSON><PERSON> <PERSON>, 2nd Earl of Pembroke\"><PERSON><PERSON><PERSON> <PERSON>, 2nd Earl of Pembroke</a> (b. 1270)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke\" title=\"<PERSON><PERSON><PERSON> <PERSON>, 2nd Earl of Pembroke\"><PERSON><PERSON><PERSON> <PERSON>, 2nd Earl of Pembroke</a> (b. 1270)", "links": [{"title": "<PERSON><PERSON><PERSON>, 2nd Earl of Pembroke", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke"}]}, {"year": "1343", "text": "<PERSON>, Italian cardinal (b. c. 1270)", "html": "1343 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. c. 1270)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. c. 1270)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1356", "text": "<PERSON>, Holy Roman Empress (b. 1311)", "html": "1356 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Hainaut\" title=\"<PERSON>, Countess of Hainaut\"><PERSON> II</a>, Holy Roman Empress (b. 1311)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Hainaut\" title=\"<PERSON>, Countess of Hainaut\"><PERSON> II</a>, Holy Roman Empress (b. 1311)", "links": [{"title": "<PERSON>, Countess of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Hai<PERSON>ut"}]}, {"year": "1537", "text": "<PERSON>, Spanish conquistador (b. 1487)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish conquistador (b. 1487)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish conquistador (b. 1487)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON><PERSON><PERSON>, Ottoman admiral (b. 1485)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman admiral (b. 1485)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman admiral (b. 1485)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1582", "text": "<PERSON><PERSON><PERSON>, Japanese commander (b. 1537)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1537)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "1615", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (b. 1545)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1545)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1677", "text": "<PERSON>, duke of Württemberg (b. 1647)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON>, Duke of Württemberg\"><PERSON></a>, duke of Württemberg (b. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON>, Duke of Württemberg\"><PERSON></a>, duke of Württemberg (b. 1647)", "links": [{"title": "<PERSON>, Duke of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrt<PERSON>berg"}]}, {"year": "1686", "text": "<PERSON>, English politician (b. 1628)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/William_Coventry\" title=\"William <PERSON>\"><PERSON></a>, English politician (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Coventry\" title=\"William Coventry\"><PERSON></a>, English politician (b. 1628)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_Coventry"}]}, {"year": "1707", "text": "<PERSON>, English theologian and author (b. 1645)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English theologian and author (b. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English theologian and author (b. 1645)", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>(theologian)"}]}, {"year": "1733", "text": "<PERSON>, Swiss paleontologist and scholar (b. 1672)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss paleontologist and scholar (b. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss paleontologist and scholar (b. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, English poet and physician (b. 1721)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and physician (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and physician (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON>, German adventurer and author (b. 1692)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German adventurer and author (b. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German adventurer and author (b. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6llnitz"}]}, {"year": "1779", "text": "<PERSON><PERSON><PERSON>, Ethiopian warlord (b. 1691)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian warlord (b. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian warlord (b. 1691)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON><PERSON><PERSON>, French zoologist and philosopher (b. 1723)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French zoologist and philosopher (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French zoologist and philosopher (b. 1723)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON><PERSON>, Portuguese poet and author (b. 1740)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tolentino_de_Almeida\" title=\"<PERSON><PERSON> Tolentino de Almeida\"><PERSON><PERSON></a>, Portuguese poet and author (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lentino_de_Almeida\" title=\"<PERSON><PERSON> Tolent<PERSON> Almeida\"><PERSON><PERSON> Almeida</a>, Portuguese poet and author (b. 1740)", "links": [{"title": "<PERSON><PERSON> Almeida", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lentino_de_Almeida"}]}, {"year": "1832", "text": "Sir <PERSON>, 4th Baronet, Scottish geologist and geophysicist (b. 1761)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet\" title=\"Sir <PERSON>, 4th Baronet\">Sir <PERSON>, 4th Baronet</a>, Scottish geologist and geophysicist (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet\" title=\"Sir <PERSON>, 4th Baronet\">Sir <PERSON>, 4th Baronet</a>, Scottish geologist and geophysicist (b. 1761)", "links": [{"title": "Sir <PERSON>, 4th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet"}]}, {"year": "1836", "text": "<PERSON>, Scottish economist, historian, and philosopher (b. 1773)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/James_Mill\" title=\"James Mill\"><PERSON></a>, Scottish economist, historian, and philosopher (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/James_Mill\" title=\"James Mill\"><PERSON></a>, Scottish economist, historian, and philosopher (b. 1773)", "links": [{"title": "James Mill", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON> of Austria-<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> of Bavaria (b. 1776)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria-Este\" title=\"Archduchess <PERSON> of Austria-Este\"><PERSON> of Austria-Este</a>, <PERSON><PERSON><PERSON><PERSON> of Bavaria (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria-Este\" title=\"Archduchess <PERSON> of Austria-Este\"><PERSON> of Austria-Este</a>, <PERSON><PERSON><PERSON><PERSON> of Bavaria (b. 1776)", "links": [{"title": "Archduchess <PERSON> of Austria-Este", "link": "https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria-Este"}]}, {"year": "1856", "text": "<PERSON>, Russian philosopher and critic (b. 1806)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philosopher and critic (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philosopher and critic (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, German botanist and academic (b. 1804)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and academic (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and academic (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German physicist and academic (b. 1804)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American lawyer and politician (b. 1825)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, English-New Zealand lawyer and politician, 2nd Prime Minister of New Zealand (b. 1812)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English-New Zealand lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English-New Zealand lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1812)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, English-South African politician (b. 1817)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>phi<PERSON>_<PERSON>\" title=\"Theophi<PERSON>\"><PERSON><PERSON><PERSON></a>, English-South African politician (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>phi<PERSON>_<PERSON>\" title=\"Theophi<PERSON>\"><PERSON><PERSON><PERSON></a>, English-South African politician (b. 1817)", "links": [{"title": "Theophi<PERSON>", "link": "https://wikipedia.org/wiki/Theophi<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian guru and philosopher (b. 1838)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Bhakti<PERSON>oda_Thakur\" title=\"Bhaktivinoda Thakur\"><PERSON><PERSON><PERSON><PERSON> Thakur</a>, Indian guru and philosopher (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON><PERSON>_Thakur\" title=\"Bhaktivinoda Thakur\"><PERSON><PERSON><PERSON><PERSON> Thakur</a>, Indian guru and philosopher (b. 1838)", "links": [{"title": "B<PERSON><PERSON><PERSON> Thakur", "link": "https://wikipedia.org/wiki/Bhaktivinoda_Thakur"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian journalist and activist (b. 1923)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian journalist and activist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian journalist and activist (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1953", "text": "<PERSON>, French painter (b. 1881)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Turkish general (b. 1889)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mu<PERSON>k\" title=\"<PERSON>ih Omurtak\"><PERSON><PERSON></a>, Turkish general (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>murtak\" title=\"<PERSON>ih Omurtak\"><PERSON><PERSON></a>, Turkish general (b. 1889)", "links": [{"title": "<PERSON>ih Omurtak", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Russian composer and educator (b. 1875)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Reinhold_Gli%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian composer and educator (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rein<PERSON>_Gli%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian composer and educator (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Reinhold_Gli%C3%A8re"}]}, {"year": "1959", "text": "<PERSON>, French author, poet, and playwright (b. 1920)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Iraqi poet. (b. 1880)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iraqi poet. (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iraqi poet. (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hidir_<PERSON>tfi"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Finnish runner (b. 1907)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mar<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American soldier and pilot (b. 1895)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soldier and pilot (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soldier and pilot (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Scottish race car driver (b. 1944)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Indian engineer and politician (b. 1946)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian engineer and politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian engineer and politician (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, American painter and academic (b. 1904)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Still\" title=\"<PERSON><PERSON><PERSON><PERSON> Still\"><PERSON><PERSON><PERSON><PERSON></a>, American painter and academic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Still\" title=\"<PERSON>lyfford Still\"><PERSON><PERSON><PERSON><PERSON></a>, American painter and academic (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Still", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Still"}]}, {"year": "1989", "text": "<PERSON>, German police officer and jurist (b. 1903)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German police officer and jurist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German police officer and jurist (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Indian poet, actor, and politician (b. 1898)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hyay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, actor, and politician (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, actor, and politician (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American football player (b. 1966)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American journalist (b. 1928)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American biologist and physician (b. 1914)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and physician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and physician (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Russian ice hockey player and coach (b. 1918)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player and coach (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player and coach (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v"}]}, {"year": "1996", "text": "<PERSON>, Greek economist and politician, 174th Prime Minister of Greece (b. 1919)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek economist and politician, 174th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek economist and politician, 174th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1996", "text": "<PERSON>, Australian cricketer and rugby player (b. 1921)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and rugby player (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and rugby player (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lindwall"}]}, {"year": "1997", "text": "<PERSON>, American educator and activist (b. 1936)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, American educator and activist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, American educator and activist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Irish-American actress (b. 1911)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan\" title=\"<PERSON>\"><PERSON></a>, Irish-American actress (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan\" title=\"<PERSON>\"><PERSON></a>, Irish-American actress (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan"}]}, {"year": "2000", "text": "<PERSON>, Slovak footballer (b. 1972)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Slovak footballer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Slovak footballer (b. 1972)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD_(footballer)"}]}, {"year": "2002", "text": "<PERSON>, Panamanian boxer (b. 1975)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1zar\" title=\"<PERSON>\"><PERSON></a>, Panamanian boxer (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1zar\" title=\"<PERSON>\"><PERSON></a>, Panamanian boxer (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Alc%C3%A1zar"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American journalist and author (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Greek poet and critic (b. 1925)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Manolis_Ana<PERSON>stakis\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek poet and critic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manolis_Ana<PERSON>stakis\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek poet and critic (b. 1925)", "links": [{"title": "Manolis <PERSON>", "link": "https://wikipedia.org/wiki/Manolis_Anagnostakis"}]}, {"year": "2006", "text": "<PERSON>, American actor, producer, and screenwriter, founded Spelling Television (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter, founded <a href=\"https://wikipedia.org/wiki/Spelling_Television\" title=\"Spelling Television\">Spelling Television</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter, founded <a href=\"https://wikipedia.org/wiki/Spelling_Television\" title=\"Spelling Television\">Spelling Television</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Spelling Television", "link": "https://wikipedia.org/wiki/Spelling_Television"}]}, {"year": "2007", "text": "<PERSON>, American baseball player (b. 1968)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Italian-Scottish actor (b. 1952)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Scottish actor (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Scottish actor (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Guyanese surveyor and politician, 1st President of Guyana (b. 1918)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese surveyor and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese surveyor and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Guyana", "link": "https://wikipedia.org/wiki/President_of_Guyana"}]}, {"year": "2008", "text": "<PERSON>, Polish actor and bodybuilder (b. 1943)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish actor and bodybuilder (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish actor and bodybuilder (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Canadian singer-songwriter and producer (b. 1931)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American game show host and announcer (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and announcer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and announcer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American physician and explorer (b. 1952)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and explorer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and explorer (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Australian public servant and diplomat (b. 1915)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Australian public servant and diplomat (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Australian public servant and diplomat (b. 1915)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>(diplomat)"}]}, {"year": "2011", "text": "<PERSON>, American actor (b. 1927)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Costa Rican footballer (b. 1985)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1985)\" title=\"<PERSON> (footballer, born 1985)\"><PERSON></a>, Costa Rican footballer (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1985)\" title=\"<PERSON> (footballer, born 1985)\"><PERSON></a>, Costa Rican footballer (b. 1985)", "links": [{"title": "<PERSON> (footballer, born 1985)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1985)"}]}, {"year": "2011", "text": "<PERSON>, American composer and conductor (b. 1923)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English economist and statistician (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and statistician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and statistician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, French pianist and educator (b. 1952)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French pianist and educator (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French pianist and educator (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Northern Ireland footballer and manager (b. 1963)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(association_football)\" class=\"mw-redirect\" title=\"<PERSON> (association football)\"><PERSON></a>, Northern Ireland footballer and manager (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(association_football)\" class=\"mw-redirect\" title=\"<PERSON> (association football)\"><PERSON></a>, Northern Ireland footballer and manager (b. 1963)", "links": [{"title": "<PERSON> (association football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(association_football)"}]}, {"year": "2012", "text": "<PERSON>, American soldier and politician, 4th Vice President of the Navajo Nation (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Navajo_Nation\" title=\"Vice President of the Navajo Nation\">Vice President of the Navajo Nation</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Navajo_Nation\" title=\"Vice President of the Navajo Nation\">Vice President of the Navajo Nation</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the Navajo Nation", "link": "https://wikipedia.org/wiki/Vice_President_of_the_Navajo_Nation"}]}, {"year": "2012", "text": "<PERSON>, American football player and businessman, founded the Cubic Corporation (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and businessman, founded the <a href=\"https://wikipedia.org/wiki/Cubic_Corporation\" title=\"Cubic Corporation\">Cubic Corporation</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and businessman, founded the <a href=\"https://wikipedia.org/wiki/Cubic_Corporation\" title=\"Cubic Corporation\">Cubic Corporation</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cubic Corporation", "link": "https://wikipedia.org/wiki/Cubic_Corporation"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>land\"><PERSON></a>, American singer-songwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American screenwriter and producer (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American admiral and politician, United States Secretary of the Navy (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Navy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy"}]}, {"year": "2013", "text": "<PERSON>, German mathematician and academic (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American author and screenwriter (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English singer-songwriter, drummer, and actor (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, drummer, and actor (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, drummer, and actor (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American swimmer (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American author (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Nancy_Garden\" title=\"Nancy Garden\"><PERSON></a>, American author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nancy_Garden\" title=\"Nancy Garden\"><PERSON></a>, American author (b. 1938)", "links": [{"title": "Nancy <PERSON>", "link": "https://wikipedia.org/wiki/Nancy_Garden"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Welsh cricketer (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Euro<PERSON>_Lewis\" title=\"Euro<PERSON> Lewis\"><PERSON><PERSON></a>, Welsh cricketer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Euros_Lewis\" title=\"Euro<PERSON> Lewis\"><PERSON><PERSON></a>, Welsh cricketer (b. 1942)", "links": [{"title": "Euros Lewis", "link": "https://wikipedia.org/wiki/Euros_Lewis"}]}, {"year": "2014", "text": "<PERSON>, American businesswoman, co-founded <PERSON><PERSON> (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Red<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ken"}]}, {"year": "2015", "text": "<PERSON>, Honduran businessman (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Barjum\" title=\"<PERSON>\"><PERSON></a>, Honduran businessman (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Barjum\" title=\"<PERSON>\"><PERSON></a>, Honduran businessman (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Barjum"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Indian nun, lawyer, and social worker (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>i\"><PERSON><PERSON><PERSON></a>, Indian nun, lawyer, and social worker (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian nun, lawyer, and social worker (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "2015", "text": "<PERSON>, American actor (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American singer and banjo player (b. 1927)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and banjo player (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and banjo player (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, British-American computer programmer and businessman, founded <PERSON><PERSON><PERSON><PERSON><PERSON> (b. 1945)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American computer programmer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American computer programmer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>c<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McAfee", "link": "https://wikipedia.org/wiki/McAfee"}]}]}}