{"date": "July 30", "url": "https://wikipedia.org/wiki/July_30", "data": {"Events": [{"year": "762", "text": "Baghdad is founded.", "html": "762 - <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a> is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a> is founded.", "links": [{"title": "Baghdad", "link": "https://wikipedia.org/wiki/Baghdad"}]}, {"year": "1419", "text": "First Defenestration of Prague: A crowd of radical Hussites kill seven members of the Prague city council.", "html": "1419 - <a href=\"https://wikipedia.org/wiki/First_Defenestration_of_Prague\" class=\"mw-redirect\" title=\"First Defenestration of Prague\">First Defenestration of Prague</a>: A crowd of radical <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"Hussi<PERSON>\"><PERSON><PERSON><PERSON></a> kill seven members of the <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a> city council.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Defenestration_of_Prague\" class=\"mw-redirect\" title=\"First Defenestration of Prague\">First Defenestration of Prague</a>: A crowd of radical <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"Hu<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> kill seven members of the <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a> city council.", "links": [{"title": "First Defenestration of Prague", "link": "https://wikipedia.org/wiki/First_Defenestration_of_Prague"}, {"title": "Hussites", "link": "https://wikipedia.org/wiki/Hussites"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}]}, {"year": "1502", "text": "<PERSON> lands at Guanaja in the Bay Islands off the coast of Honduras during his fourth voyage.", "html": "1502 - <a href=\"https://wikipedia.org/wiki/Voyages_of_Christopher_Columbus\" title=\"Voyages of Christopher Columbus\"><PERSON></a> lands at <a href=\"https://wikipedia.org/wiki/Guanaja\" title=\"Guanaja\">Guanaja</a> in the <a href=\"https://wikipedia.org/wiki/Bay_Islands_Department\" title=\"Bay Islands Department\">Bay Islands</a> off the coast of <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a> during his fourth voyage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Voyages_of_Christopher_Columbus\" title=\"Voyages of Christopher Columbus\"><PERSON></a> lands at <a href=\"https://wikipedia.org/wiki/Guanaja\" title=\"Guanaja\">Guanaja</a> in the <a href=\"https://wikipedia.org/wiki/Bay_Islands_Department\" title=\"Bay Islands Department\">Bay Islands</a> off the coast of <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a> during his fourth voyage.", "links": [{"title": "Voyages of <PERSON>", "link": "https://wikipedia.org/wiki/Voyages_of_<PERSON>_<PERSON>"}, {"title": "Guanaja", "link": "https://wikipedia.org/wiki/Guanaja"}, {"title": "Bay Islands Department", "link": "https://wikipedia.org/wiki/Bay_Islands_Department"}, {"title": "Honduras", "link": "https://wikipedia.org/wiki/Honduras"}]}, {"year": "1609", "text": "Beaver Wars: At Ticonderoga (now Crown Point, New York), <PERSON> shoots and kills two Iroquois chiefs on behalf of his native allies.", "html": "1609 - <a href=\"https://wikipedia.org/wiki/Beaver_Wars\" title=\"Beaver Wars\">Beaver Wars</a>: At <a href=\"https://wikipedia.org/wiki/Fort_Ticonderoga\" title=\"Fort Ticonderoga\">Ticonderoga</a> (now <a href=\"https://wikipedia.org/wiki/Crown_Point,_New_York\" title=\"Crown Point, New York\">Crown Point, New York</a>), <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> shoots and kills two <a href=\"https://wikipedia.org/wiki/Iroquois\" title=\"Iroquois\">Iroquois</a> chiefs on behalf of his native allies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beaver_Wars\" title=\"Beaver Wars\">Beaver Wars</a>: At <a href=\"https://wikipedia.org/wiki/Fort_Ticonderoga\" title=\"Fort Ticonderoga\">Ticonderoga</a> (now <a href=\"https://wikipedia.org/wiki/Crown_Point,_New_York\" title=\"Crown Point, New York\">Crown Point, New York</a>), <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> shoots and kills two <a href=\"https://wikipedia.org/wiki/Iroquois\" title=\"Iroquois\">Iroquois</a> chiefs on behalf of his native allies.", "links": [{"title": "Beaver Wars", "link": "https://wikipedia.org/wiki/Beaver_Wars"}, {"title": "Fort Ticonderoga", "link": "https://wikipedia.org/wiki/Fort_Ticonderoga"}, {"title": "Crown Point, New York", "link": "https://wikipedia.org/wiki/Crown_Point,_New_York"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Iroquois", "link": "https://wikipedia.org/wiki/Iroquois"}]}, {"year": "1619", "text": "In Jamestown, Virginia, the first Colonial European representative assembly in the Americas, the Virginia General Assembly, convenes for the first time.", "html": "1619 - In <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">Jamestown, Virginia</a>, the first Colonial European representative assembly in the Americas, the <a href=\"https://wikipedia.org/wiki/Virginia_General_Assembly\" title=\"Virginia General Assembly\">Virginia General Assembly</a>, convenes for the first time.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">Jamestown, Virginia</a>, the first Colonial European representative assembly in the Americas, the <a href=\"https://wikipedia.org/wiki/Virginia_General_Assembly\" title=\"Virginia General Assembly\">Virginia General Assembly</a>, convenes for the first time.", "links": [{"title": "Jamestown, Virginia", "link": "https://wikipedia.org/wiki/Jamestown,_Virginia"}, {"title": "Virginia General Assembly", "link": "https://wikipedia.org/wiki/Virginia_General_Assembly"}]}, {"year": "1627", "text": "An earthquake kills about 5,000 people in Gargano, Italy.", "html": "1627 - <a href=\"https://wikipedia.org/wiki/1627_Gargano_earthquake\" title=\"1627 Gargano earthquake\">An earthquake</a> kills about 5,000 people in <a href=\"https://wikipedia.org/wiki/Gargano\" title=\"Gargano\">Gargano</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1627_Gargano_earthquake\" title=\"1627 Gargano earthquake\">An earthquake</a> kills about 5,000 people in <a href=\"https://wikipedia.org/wiki/Gargano\" title=\"Gargano\">Gargano</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>.", "links": [{"title": "1627 Gargano earthquake", "link": "https://wikipedia.org/wiki/1627_Gargano_earthquake"}, {"title": "Gargano", "link": "https://wikipedia.org/wiki/Gargano"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}]}, {"year": "1635", "text": "Eighty Years' War: The Siege of Schenkenschans begins; <PERSON>, Prince of Orange, begins the recapture of the strategically important fortress from the Spanish Army.", "html": "1635 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Schenkenschans\" title=\"Siege of Schenkenschans\">Siege of Schenkenschans</a> begins; <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a>, begins the recapture of the strategically important fortress from the <a href=\"https://wikipedia.org/wiki/Spanish_Army\" title=\"Spanish Army\">Spanish Army</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Schenkenschans\" title=\"Siege of Schenkenschans\">Siege of Schenkenschans</a> begins; <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a>, begins the recapture of the strategically important fortress from the <a href=\"https://wikipedia.org/wiki/Spanish_Army\" title=\"Spanish Army\">Spanish Army</a>.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Siege of Schenkenschans", "link": "https://wikipedia.org/wiki/Siege_of_Schenkenschans"}, {"title": "<PERSON>, Prince of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange"}, {"title": "Spanish Army", "link": "https://wikipedia.org/wiki/Spanish_Army"}]}, {"year": "1645", "text": "English Civil War: Scottish Covenanter forces under the <PERSON> launch the Siege of Hereford, a remaining Royalist stronghold.", "html": "1645 - <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scottish</a> <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Covenanter</a> forces under the <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Leven\" title=\"<PERSON>, 1st Earl of Leven\">Earl of Leven</a> launch the <a href=\"https://wikipedia.org/wiki/Siege_of_Hereford\" title=\"Siege of Hereford\">Siege of Hereford</a>, a remaining <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Royalist</a> stronghold.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scottish</a> <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Covenanter</a> forces under the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>_Leven\" title=\"<PERSON>, 1st Earl of Leven\">Earl of Leven</a> launch the <a href=\"https://wikipedia.org/wiki/Siege_of_Hereford\" title=\"Siege of Hereford\">Siege of Hereford</a>, a remaining <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Royalist</a> stronghold.", "links": [{"title": "English Civil War", "link": "https://wikipedia.org/wiki/English_Civil_War"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}, {"title": "Covenanter", "link": "https://wikipedia.org/wiki/Covenanter"}, {"title": "<PERSON>, 1st Earl of Leven", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_<PERSON>"}, {"title": "Siege of Hereford", "link": "https://wikipedia.org/wiki/Siege_of_Hereford"}, {"title": "Cavalier", "link": "https://wikipedia.org/wiki/Cavalier"}]}, {"year": "1656", "text": "The Battle of Warsaw ends with a Swedish-Brandenburger victory over a larger Polish-Lithuanian force.", "html": "1656 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1656)\" title=\"Battle of Warsaw (1656)\">Battle of Warsaw</a> ends with a <a href=\"https://wikipedia.org/wiki/Swedish_Empire\" title=\"Swedish Empire\">Swedish</a>-<a href=\"https://wikipedia.org/wiki/Brandenburg-Prussia\" class=\"mw-redirect\" title=\"Brandenburg-Prussia\">Brandenburger</a> victory over a larger <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian</a> force.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1656)\" title=\"Battle of Warsaw (1656)\">Battle of Warsaw</a> ends with a <a href=\"https://wikipedia.org/wiki/Swedish_Empire\" title=\"Swedish Empire\">Swedish</a>-<a href=\"https://wikipedia.org/wiki/Brandenburg-Prussia\" class=\"mw-redirect\" title=\"Brandenburg-Prussia\">Brandenburger</a> victory over a larger <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian</a> force.", "links": [{"title": "Battle of Warsaw (1656)", "link": "https://wikipedia.org/wiki/Battle_of_Warsaw_(1656)"}, {"title": "Swedish Empire", "link": "https://wikipedia.org/wiki/Swedish_Empire"}, {"title": "Brandenburg-Prussia", "link": "https://wikipedia.org/wiki/Brandenburg-Prussia"}, {"title": "Polish-Lithuanian Commonwealth", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth"}]}, {"year": "1676", "text": "<PERSON> issues the \"Declaration of the People of Virginia\", beginning <PERSON>'s Rebellion against the rule of Governor <PERSON>.", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonist)\" class=\"mw-redirect\" title=\"<PERSON> (colonist)\"><PERSON></a> issues the \"Declaration of the People of Virginia\", beginning <a href=\"https://wikipedia.org/wiki/Bacon%27s_Rebellion\" title=\"<PERSON>'s Rebellion\"><PERSON>'s Rebellion</a> against the rule of Governor <a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(colonist)\" class=\"mw-redirect\" title=\"<PERSON> (colonist)\"><PERSON></a> issues the \"Declaration of the People of Virginia\", beginning <a href=\"https://wikipedia.org/wiki/Bacon%27s_Rebellion\" title=\"<PERSON>'s Rebellion\"><PERSON>'s Rebellion</a> against the rule of Governor <a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>.", "links": [{"title": "<PERSON> (colonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonist)"}, {"title": "<PERSON>'s Rebellion", "link": "https://wikipedia.org/wiki/Bacon%27s_Rebellion"}, {"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}]}, {"year": "1729", "text": "Founding of Baltimore, Maryland.", "html": "1729 - Founding of <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore, Maryland</a>.", "no_year_html": "Founding of <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore, Maryland</a>.", "links": [{"title": "Baltimore", "link": "https://wikipedia.org/wiki/Baltimore"}]}, {"year": "1733", "text": "The first Masonic Grand Lodge in the future United States is constituted in Massachusetts.", "html": "1733 - The first <a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">Masonic</a> <a href=\"https://wikipedia.org/wiki/Grand_Lodge\" title=\"Grand Lodge\">Grand Lodge</a> in the future United States is constituted in <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">Masonic</a> <a href=\"https://wikipedia.org/wiki/Grand_Lodge\" title=\"Grand Lodge\">Grand Lodge</a> in the future United States is constituted in <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a>.", "links": [{"title": "Freemasonry", "link": "https://wikipedia.org/wiki/Freemasonry"}, {"title": "Grand Lodge", "link": "https://wikipedia.org/wiki/Grand_Lodge"}, {"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}]}, {"year": "1756", "text": "In Saint Petersburg, <PERSON><PERSON><PERSON><PERSON> presents the newly built Catherine Palace to Empress <PERSON> and her courtiers.", "html": "1756 - In <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> presents the newly built <a href=\"https://wikipedia.org/wiki/Catherine_Palace\" title=\"Catherine Palace\">Catherine Palace</a> to <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Russia\" title=\"Elizabeth of Russia\">Empress <PERSON></a> and her courtiers.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> presents the newly built <a href=\"https://wikipedia.org/wiki/Catherine_Palace\" title=\"Catherine Palace\">Catherine Palace</a> to <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Russia\" title=\"Elizabeth of Russia\">Empress <PERSON></a> and her courtiers.", "links": [{"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Catherine Palace", "link": "https://wikipedia.org/wiki/Catherine_Palace"}, {"title": "Elizabeth of Russia", "link": "https://wikipedia.org/wiki/Elizabeth_of_Russia"}]}, {"year": "1811", "text": "Father <PERSON>, leader of the Mexican insurgency, is executed by the Spanish in Chihuahua City, Mexico.", "html": "1811 - Father <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_y_Costilla\" title=\"<PERSON> y Costilla\"><PERSON> y Co<PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Mexican_War_of_Independence\" title=\"Mexican War of Independence\">Mexican insurgency</a>, is executed by the Spanish in <a href=\"https://wikipedia.org/wiki/Chihuahua_City\" title=\"Chihuahua City\">Chihuahua City</a>, Mexico.", "no_year_html": "Father <a href=\"https://wikipedia.org/wiki/<PERSON>_y_Costilla\" title=\"<PERSON> y Costilla\"><PERSON> y <PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Mexican_War_of_Independence\" title=\"Mexican War of Independence\">Mexican insurgency</a>, is executed by the Spanish in <a href=\"https://wikipedia.org/wiki/Chihuahua_City\" title=\"Chihuahua City\">Chihuahua City</a>, Mexico.", "links": [{"title": "<PERSON> y Costilla", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_y_<PERSON>"}, {"title": "Mexican War of Independence", "link": "https://wikipedia.org/wiki/Mexican_War_of_Independence"}, {"title": "Chihuahua City", "link": "https://wikipedia.org/wiki/Chihuahua_City"}]}, {"year": "1859", "text": "First ascent of Grand Combin, one of the highest summits in the Alps.", "html": "1859 - First ascent of <a href=\"https://wikipedia.org/wiki/Grand_Combin\" title=\"Grand Combin\">Grand Combin</a>, one of the highest summits in the <a href=\"https://wikipedia.org/wiki/Alps\" title=\"Alps\">Alps</a>.", "no_year_html": "First ascent of <a href=\"https://wikipedia.org/wiki/Grand_Combin\" title=\"Grand Combin\">Grand Combin</a>, one of the highest summits in the <a href=\"https://wikipedia.org/wiki/Alps\" title=\"Alps\">Alps</a>.", "links": [{"title": "Grand Combin", "link": "https://wikipedia.org/wiki/Grand_Combin"}, {"title": "Alps", "link": "https://wikipedia.org/wiki/Alps"}]}, {"year": "1863", "text": "American Indian Wars: Representatives of the United States and tribal leaders including Chief <PERSON><PERSON><PERSON> (of the Shoshone)  sign the Treaty of Box Elder.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: Representatives of the United States and tribal leaders including <a href=\"https://wikipedia.org/wiki/Chief_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"Chief <PERSON><PERSON><PERSON>\">Chief <PERSON><PERSON><PERSON></a> (of the <a href=\"https://wikipedia.org/wiki/Shoshone\" title=\"Shoshone\">Shoshone</a>) sign the <a href=\"https://wikipedia.org/wiki/Box_Elder_Treaty\" title=\"Box Elder Treaty\">Treaty of Box Elder</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: Representatives of the United States and tribal leaders including <a href=\"https://wikipedia.org/wiki/Chief_<PERSON>\" class=\"mw-redirect\" title=\"Chief <PERSON><PERSON><PERSON>\">Chief <PERSON><PERSON><PERSON></a> (of the <a href=\"https://wikipedia.org/wiki/Shoshone\" title=\"Shoshone\">Shoshone</a>) sign the <a href=\"https://wikipedia.org/wiki/Box_Elder_Treaty\" title=\"Box Elder Treaty\">Treaty of Box Elder</a>.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Chief <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON><PERSON>"}, {"title": "Shoshone", "link": "https://wikipedia.org/wiki/Shoshone"}, {"title": "Box Elder Treaty", "link": "https://wikipedia.org/wiki/Box_Elder_Treaty"}]}, {"year": "1863", "text": "Valuev Circular banned the publication of religious, educational and training books in Ukrainian in the Russian Empire.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Valuev_Circular\" title=\"Valuev Circular\">Valuev Circular</a> banned the publication of religious, educational and training books in <a href=\"https://wikipedia.org/wiki/Ukrainian_language\" title=\"Ukrainian language\">Ukrainian</a> in the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valuev_Circular\" title=\"Valuev Circular\">Valuev Circular</a> banned the publication of religious, educational and training books in <a href=\"https://wikipedia.org/wiki/Ukrainian_language\" title=\"Ukrainian language\">Ukrainian</a> in the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>.", "links": [{"title": "Valuev Circular", "link": "https://wikipedia.org/wiki/Valuev_Circular"}, {"title": "Ukrainian language", "link": "https://wikipedia.org/wiki/Ukrainian_language"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}]}, {"year": "1864", "text": "American Civil War: Battle of the Crater: Union forces attempt to break Confederate lines at Petersburg, Virginia by exploding a large bomb under their trenches.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Crater\" title=\"Battle of the Crater\">Battle of the Crater</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces attempt to break <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> lines at <a href=\"https://wikipedia.org/wiki/Siege_of_Petersburg\" title=\"Siege of Petersburg\">Petersburg, Virginia</a> by exploding a large bomb under their trenches.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Crater\" title=\"Battle of the Crater\">Battle of the Crater</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces attempt to break <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> lines at <a href=\"https://wikipedia.org/wiki/Siege_of_Petersburg\" title=\"Siege of Petersburg\">Petersburg, Virginia</a> by exploding a large bomb under their trenches.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of the Crater", "link": "https://wikipedia.org/wiki/Battle_of_the_Crater"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Siege of Petersburg", "link": "https://wikipedia.org/wiki/Siege_of_Petersburg"}]}, {"year": "1865", "text": "The steamboat <PERSON> sinks off the coast of Crescent City, California, killing 225 passengers, the deadliest shipwreck on the Pacific Coast of the U.S. at the time.", "html": "1865 - The <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamboat</a> <i><a href=\"https://wikipedia.org/wiki/Brother_<PERSON>_(steamer)\" title=\"Brother <PERSON> (steamer)\">Brother <PERSON></a></i> sinks off the coast of <a href=\"https://wikipedia.org/wiki/Crescent_City,_California\" title=\"Crescent City, California\">Crescent City, California</a>, killing 225 passengers, the deadliest shipwreck on the Pacific Coast of the U.S. at the time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamboat</a> <i><a href=\"https://wikipedia.org/wiki/Brother_<PERSON>_(steamer)\" title=\"Brother <PERSON> (steamer)\">Brother <PERSON></a></i> sinks off the coast of <a href=\"https://wikipedia.org/wiki/Crescent_City,_California\" title=\"Crescent City, California\">Crescent City, California</a>, killing 225 passengers, the deadliest shipwreck on the Pacific Coast of the U.S. at the time.", "links": [{"title": "Steamboat", "link": "https://wikipedia.org/wiki/Steamboat"}, {"title": "Brother <PERSON> (steamer)", "link": "https://wikipedia.org/wiki/Brother_<PERSON>_(steamer)"}, {"title": "Crescent City, California", "link": "https://wikipedia.org/wiki/Crescent_City,_California"}]}, {"year": "1866", "text": "Armed Confederate veterans in New Orleans riot against a meeting of Radical Republicans, killing 48 people and injuring another 100.", "html": "1866 - Armed Confederate veterans in <a href=\"https://wikipedia.org/wiki/New_Orleans_riot\" class=\"mw-redirect\" title=\"New Orleans riot\">New Orleans riot</a> against a meeting of <a href=\"https://wikipedia.org/wiki/Radical_Republican\" class=\"mw-redirect\" title=\"Radical Republican\">Radical Republicans</a>, killing 48 people and injuring another 100.", "no_year_html": "Armed Confederate veterans in <a href=\"https://wikipedia.org/wiki/New_Orleans_riot\" class=\"mw-redirect\" title=\"New Orleans riot\">New Orleans riot</a> against a meeting of <a href=\"https://wikipedia.org/wiki/Radical_Republican\" class=\"mw-redirect\" title=\"Radical Republican\">Radical Republicans</a>, killing 48 people and injuring another 100.", "links": [{"title": "New Orleans riot", "link": "https://wikipedia.org/wiki/New_Orleans_riot"}, {"title": "Radical Republican", "link": "https://wikipedia.org/wiki/Radical_Republican"}]}, {"year": "1871", "text": "The Staten Island Ferry Westfield's boiler explodes, killing over 85 people.", "html": "1871 - The <a href=\"https://wikipedia.org/wiki/Staten_Island_Ferry\" title=\"Staten Island Ferry\">Staten Island Ferry</a> <i>Westfield'</i>s boiler explodes, killing over 85 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Staten_Island_Ferry\" title=\"Staten Island Ferry\">Staten Island Ferry</a> <i>Westfield'</i>s boiler explodes, killing over 85 people.", "links": [{"title": "Staten Island Ferry", "link": "https://wikipedia.org/wiki/Staten_Island_Ferry"}]}, {"year": "1912", "text": "Japan's Emperor <PERSON> dies and is succeeded by his son <PERSON><PERSON><PERSON><PERSON>, who is now known as the <PERSON> <PERSON><PERSON><PERSON>.", "html": "1912 - Japan's <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor Meiji\">Emperor Meiji</a> dies and is succeeded by his son <PERSON><PERSON><PERSON><PERSON>, who is now known as the <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a>.", "no_year_html": "Japan's <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor Meiji\">Emperor Meiji</a> dies and is succeeded by his son <PERSON><PERSON><PERSON><PERSON>, who is now known as the <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a>.", "links": [{"title": "Emperor <PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>"}, {"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%8D"}]}, {"year": "1916", "text": "The Black Tom explosion in New York Harbor kills four and destroys some $20,000,000 worth of military goods.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/Black_Tom_explosion\" title=\"Black Tom explosion\">Black Tom explosion</a> in New York Harbor kills four and destroys some $20,000,000 worth of military goods.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Black_Tom_explosion\" title=\"Black Tom explosion\">Black Tom explosion</a> in New York Harbor kills four and destroys some $20,000,000 worth of military goods.", "links": [{"title": "Black Tom explosion", "link": "https://wikipedia.org/wiki/Black_Tom_explosion"}]}, {"year": "1930", "text": "In Montevideo, Uruguay wins the first FIFA World Cup by beating Argentina.", "html": "1930 - In <a href=\"https://wikipedia.org/wiki/Montevideo\" title=\"Montevideo\">Montevideo</a>, <a href=\"https://wikipedia.org/wiki/Uruguay_national_football_team\" title=\"Uruguay national football team\">Uruguay</a> wins the <a href=\"https://wikipedia.org/wiki/1930_FIFA_World_Cup\" title=\"1930 FIFA World Cup\">first FIFA World Cup</a> by beating <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Montevideo\" title=\"Montevideo\">Montevideo</a>, <a href=\"https://wikipedia.org/wiki/Uruguay_national_football_team\" title=\"Uruguay national football team\">Uruguay</a> wins the <a href=\"https://wikipedia.org/wiki/1930_FIFA_World_Cup\" title=\"1930 FIFA World Cup\">first FIFA World Cup</a> by beating <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "links": [{"title": "Montevideo", "link": "https://wikipedia.org/wiki/Montevideo"}, {"title": "Uruguay national football team", "link": "https://wikipedia.org/wiki/Uruguay_national_football_team"}, {"title": "1930 FIFA World Cup", "link": "https://wikipedia.org/wiki/1930_FIFA_World_Cup"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}]}, {"year": "1932", "text": "Premiere of Walt Disney's Flowers and Trees, the first cartoon short to use Technicolor and the first Academy Award winning cartoon short.", "html": "1932 - Premiere of <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Flowers_and_Trees\" title=\"Flowers and Trees\">Flowers and Trees</a></i>, the first cartoon short to use <a href=\"https://wikipedia.org/wiki/Technicolor\" title=\"Technicolor\">Technicolor</a> and the first <a href=\"https://wikipedia.org/wiki/Academy_Award_for_Best_Animated_Short_Film\" title=\"Academy Award for Best Animated Short Film\">Academy Award</a> winning cartoon short.", "no_year_html": "Premiere of <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Flowers_and_Trees\" title=\"Flowers and Trees\">Flowers and Trees</a></i>, the first cartoon short to use <a href=\"https://wikipedia.org/wiki/Technicolor\" title=\"Technicolor\">Technicolor</a> and the first <a href=\"https://wikipedia.org/wiki/Academy_Award_for_Best_Animated_Short_Film\" title=\"Academy Award for Best Animated Short Film\">Academy Award</a> winning cartoon short.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Flowers and Trees", "link": "https://wikipedia.org/wiki/Flowers_and_Trees"}, {"title": "Technicolor", "link": "https://wikipedia.org/wiki/Technicolor"}, {"title": "Academy Award for Best Animated Short Film", "link": "https://wikipedia.org/wiki/Academy_Award_for_Best_Animated_Short_Film"}]}, {"year": "1945", "text": "World War II: Japanese submarine I-58 sinks the USS Indianapolis, killing 883 seamen. Most die during the following four days, until an aircraft notices the survivors.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Japanese_submarine_I-58_(1943)\" title=\"Japanese submarine I-58 (1943)\">Japanese submarine <i>I-58</i></a> sinks the <a href=\"https://wikipedia.org/wiki/USS_Indianapolis_(CA-35)\" title=\"USS Indianapolis (CA-35)\">USS <i>Indianapolis</i></a>, killing 883 seamen. Most die during the following four days, until an aircraft notices the survivors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Japanese_submarine_I-58_(1943)\" title=\"Japanese submarine I-58 (1943)\">Japanese submarine <i>I-58</i></a> sinks the <a href=\"https://wikipedia.org/wiki/USS_Indianapolis_(CA-35)\" title=\"USS Indianapolis (CA-35)\">USS <i>Indianapolis</i></a>, killing 883 seamen. Most die during the following four days, until an aircraft notices the survivors.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Japanese submarine I-58 (1943)", "link": "https://wikipedia.org/wiki/Japanese_submarine_I-58_(1943)"}, {"title": "USS Indianapolis (CA-35)", "link": "https://wikipedia.org/wiki/USS_Indianapolis_(CA-35)"}]}, {"year": "1956", "text": "A joint resolution of the U.S. Congress is signed by President <PERSON>, authorizing In God We Trust as the U.S. national motto.", "html": "1956 - A <a href=\"https://wikipedia.org/wiki/Joint_resolution\" title=\"Joint resolution\">joint resolution</a> of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> is signed by <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, authorizing <i><a href=\"https://wikipedia.org/wiki/In_God_We_Trust\" title=\"In God We Trust\">In God We Trust</a></i> as the U.S. national <a href=\"https://wikipedia.org/wiki/Motto\" title=\"Motto\">motto</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Joint_resolution\" title=\"Joint resolution\">joint resolution</a> of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> is signed by <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, authorizing <i><a href=\"https://wikipedia.org/wiki/In_God_We_Trust\" title=\"In God We Trust\">In God We Trust</a></i> as the U.S. national <a href=\"https://wikipedia.org/wiki/Motto\" title=\"Motto\">motto</a>.", "links": [{"title": "Joint resolution", "link": "https://wikipedia.org/wiki/Joint_resolution"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "In God We Trust", "link": "https://wikipedia.org/wiki/In_God_We_Trust"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1962", "text": "The Trans-Canada Highway, the then longest national highway in the world, is officially opened.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Trans-Canada_Highway\" title=\"Trans-Canada Highway\">Trans-Canada Highway</a>, the then longest national highway in the world, is officially opened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Trans-Canada_Highway\" title=\"Trans-Canada Highway\">Trans-Canada Highway</a>, the then longest national highway in the world, is officially opened.", "links": [{"title": "Trans-Canada Highway", "link": "https://wikipedia.org/wiki/Trans-Canada_Highway"}]}, {"year": "1965", "text": "U.S. President <PERSON> signs the Social Security Act of 1965 into law, establishing Medicare and Medicaid.", "html": "1965 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Social_Security_Act_of_1965\" class=\"mw-redirect\" title=\"Social Security Act of 1965\">Social Security Act of 1965</a> into law, establishing <a href=\"https://wikipedia.org/wiki/Medicare_(United_States)\" title=\"Medicare (United States)\">Medicare</a> and <a href=\"https://wikipedia.org/wiki/Medicaid\" title=\"Medicaid\">Medicaid</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Social_Security_Act_of_1965\" class=\"mw-redirect\" title=\"Social Security Act of 1965\">Social Security Act of 1965</a> into law, establishing <a href=\"https://wikipedia.org/wiki/Medicare_(United_States)\" title=\"Medicare (United States)\">Medicare</a> and <a href=\"https://wikipedia.org/wiki/Medicaid\" title=\"Medicaid\">Medicaid</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Social Security Act of 1965", "link": "https://wikipedia.org/wiki/Social_Security_Act_of_1965"}, {"title": "Medicare (United States)", "link": "https://wikipedia.org/wiki/Medicare_(United_States)"}, {"title": "Medicaid", "link": "https://wikipedia.org/wiki/Medicaid"}]}, {"year": "1966", "text": "England defeats West Germany to win the 1966 FIFA World Cup at Wembley Stadium after extra time.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/England_national_football_team\" title=\"England national football team\">England</a> defeats <a href=\"https://wikipedia.org/wiki/Germany_national_football_team\" title=\"Germany national football team\">West Germany</a> to win the <a href=\"https://wikipedia.org/wiki/1966_FIFA_World_Cup\" title=\"1966 FIFA World Cup\">1966 FIFA World Cup</a> at <a href=\"https://wikipedia.org/wiki/Wembley_Stadium_(1923)\" title=\"Wembley Stadium (1923)\">Wembley Stadium</a> after <a href=\"https://wikipedia.org/wiki/Extra_time\" class=\"mw-redirect\" title=\"Extra time\">extra time</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/England_national_football_team\" title=\"England national football team\">England</a> defeats <a href=\"https://wikipedia.org/wiki/Germany_national_football_team\" title=\"Germany national football team\">West Germany</a> to win the <a href=\"https://wikipedia.org/wiki/1966_FIFA_World_Cup\" title=\"1966 FIFA World Cup\">1966 FIFA World Cup</a> at <a href=\"https://wikipedia.org/wiki/Wembley_Stadium_(1923)\" title=\"Wembley Stadium (1923)\">Wembley Stadium</a> after <a href=\"https://wikipedia.org/wiki/Extra_time\" class=\"mw-redirect\" title=\"Extra time\">extra time</a>.", "links": [{"title": "England national football team", "link": "https://wikipedia.org/wiki/England_national_football_team"}, {"title": "Germany national football team", "link": "https://wikipedia.org/wiki/Germany_national_football_team"}, {"title": "1966 FIFA World Cup", "link": "https://wikipedia.org/wiki/1966_FIFA_World_Cup"}, {"title": "Wembley Stadium (1923)", "link": "https://wikipedia.org/wiki/Wembley_Stadium_(1923)"}, {"title": "Extra time", "link": "https://wikipedia.org/wiki/Extra_time"}]}, {"year": "1969", "text": "Vietnam War: US President <PERSON> makes an unscheduled visit to South Vietnam and meets with President <PERSON><PERSON><PERSON><PERSON> and U.S. military commanders.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: US President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes an unscheduled visit to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> and meets with President <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87u\" title=\"Nguyễ<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and U.S. military commanders.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: US President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes an unscheduled visit to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> and meets with President <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87u\" title=\"<PERSON>uy<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and U.S. military commanders.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87u"}]}, {"year": "1971", "text": "Apollo program: On Apollo 15, <PERSON> and <PERSON> in the Apollo Lunar Module Falcon land on the Moon with the first Lunar Rover.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: On <a href=\"https://wikipedia.org/wiki/Apollo_15\" title=\"Apollo 15\">Apollo 15</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Apollo_Lunar_Module\" title=\"Apollo Lunar Module\">Apollo Lunar Module</a> <i><PERSON></i> land on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a> with the first <a href=\"https://wikipedia.org/wiki/Lunar_Roving_Vehicle\" title=\"Lunar Roving Vehicle\">Lunar Rover</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: On <a href=\"https://wikipedia.org/wiki/Apollo_15\" title=\"Apollo 15\">Apollo 15</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Apollo_Lunar_Module\" title=\"Apollo Lunar Module\">Apollo Lunar Module</a> <i><PERSON></i> land on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a> with the first <a href=\"https://wikipedia.org/wiki/Lunar_Roving_Vehicle\" title=\"Lunar Roving Vehicle\">Lunar Rover</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 15", "link": "https://wikipedia.org/wiki/Apollo_15"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Apollo Lunar Module", "link": "https://wikipedia.org/wiki/Apollo_Lunar_Module"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}, {"title": "Lunar Roving Vehicle", "link": "https://wikipedia.org/wiki/Lunar_Roving_Vehicle"}]}, {"year": "1971", "text": "An All Nippon Airways Boeing 727 and a Japanese Air Force F-86 collide over Morioka, Iwate, Japan killing 162.", "html": "1971 - An <a href=\"https://wikipedia.org/wiki/All_Nippon_Airways\" title=\"All Nippon Airways\">All Nippon Airways</a> <a href=\"https://wikipedia.org/wiki/Boeing\" title=\"Boeing\">Boeing</a> 727 and a <a href=\"https://wikipedia.org/wiki/Japan_Air_Self-Defense_Force\" title=\"Japan Air Self-Defense Force\">Japanese Air Force</a> <a href=\"https://wikipedia.org/wiki/North_American_F-86_Sabre\" title=\"North American F-86 Sabre\">F-86</a> <a href=\"https://wikipedia.org/wiki/All_Nippon_Airways_Flight_58\" title=\"All Nippon Airways Flight 58\">collide</a> over <a href=\"https://wikipedia.org/wiki/Morioka,_Iwate\" class=\"mw-redirect\" title=\"Morioka, Iwate\">Morioka, Iwate</a>, Japan killing 162.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/All_Nippon_Airways\" title=\"All Nippon Airways\">All Nippon Airways</a> <a href=\"https://wikipedia.org/wiki/Boeing\" title=\"Boeing\">Boeing</a> 727 and a <a href=\"https://wikipedia.org/wiki/Japan_Air_Self-Defense_Force\" title=\"Japan Air Self-Defense Force\">Japanese Air Force</a> <a href=\"https://wikipedia.org/wiki/North_American_F-86_Sabre\" title=\"North American F-86 Sabre\">F-86</a> <a href=\"https://wikipedia.org/wiki/All_Nippon_Airways_Flight_58\" title=\"All Nippon Airways Flight 58\">collide</a> over <a href=\"https://wikipedia.org/wiki/Morioka,_Iwate\" class=\"mw-redirect\" title=\"Morioka, Iwate\">Morioka, Iwate</a>, Japan killing 162.", "links": [{"title": "All Nippon Airways", "link": "https://wikipedia.org/wiki/All_Nippon_Airways"}, {"title": "Boeing", "link": "https://wikipedia.org/wiki/Boeing"}, {"title": "Japan Air Self-Defense Force", "link": "https://wikipedia.org/wiki/Japan_Air_Self-Defense_Force"}, {"title": "North American F-86 Sabre", "link": "https://wikipedia.org/wiki/North_American_F-86_Sabre"}, {"title": "All Nippon Airways Flight 58", "link": "https://wikipedia.org/wiki/All_Nippon_Airways_Flight_58"}, {"title": "Morioka, Iwate", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_I<PERSON>e"}]}, {"year": "1974", "text": "Watergate scandal: U.S. President <PERSON> releases subpoenaed White House recordings after being ordered to do so by the Supreme Court of the United States.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: U.S. President <PERSON> releases subpoenaed <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> recordings after being ordered to do so by the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: U.S. President <PERSON> releases subpoenaed <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> recordings after being ordered to do so by the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a>.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}]}, {"year": "1975", "text": "<PERSON> disappears from the parking lot of the Machus Red Fox restaurant in Bloomfield Hills, Michigan, a suburb of Detroit, at about 2:30 p.m. He is never seen or heard from again.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> disappears from the parking lot of the Machus Red Fox restaurant in <a href=\"https://wikipedia.org/wiki/Bloomfield_Hills,_Michigan\" title=\"Bloomfield Hills, Michigan\">Bloomfield Hills, Michigan</a>, a suburb of <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>, at about 2:30 p.m. He is never seen or heard from again.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> disappears from the parking lot of the Machus Red Fox restaurant in <a href=\"https://wikipedia.org/wiki/Bloomfield_Hills,_Michigan\" title=\"Bloomfield Hills, Michigan\">Bloomfield Hills, Michigan</a>, a suburb of <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>, at about 2:30 p.m. He is never seen or heard from again.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bloomfield Hills, Michigan", "link": "https://wikipedia.org/wiki/Bloomfield_Hills,_Michigan"}, {"title": "Detroit", "link": "https://wikipedia.org/wiki/Detroit"}]}, {"year": "1978", "text": "The 730: Okinawa Prefecture changes its traffic on the right-hand side of the road to the left-hand side.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/730_(transport)\" title=\"730 (transport)\">730</a>: <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa Prefecture</a> changes its traffic on the <a href=\"https://wikipedia.org/wiki/Right-_and_left-hand_traffic\" class=\"mw-redirect\" title=\"Right- and left-hand traffic\">right-hand side of the road to the left-hand side</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/730_(transport)\" title=\"730 (transport)\">730</a>: <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa Prefecture</a> changes its traffic on the <a href=\"https://wikipedia.org/wiki/Right-_and_left-hand_traffic\" class=\"mw-redirect\" title=\"Right- and left-hand traffic\">right-hand side of the road to the left-hand side</a>.", "links": [{"title": "730 (transport)", "link": "https://wikipedia.org/wiki/730_(transport)"}, {"title": "Okinawa Prefecture", "link": "https://wikipedia.org/wiki/Okinawa_Prefecture"}, {"title": "Right- and left-hand traffic", "link": "https://wikipedia.org/wiki/Right-_and_left-hand_traffic"}]}, {"year": "1980", "text": "Vanuatu gains independence.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Vanuatu\" title=\"Vanuatu\">Vanuatu</a> gains independence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vanuatu\" title=\"Vanuatu\">Vanuatu</a> gains independence.", "links": [{"title": "Vanuatu", "link": "https://wikipedia.org/wiki/Vanuatu"}]}, {"year": "1980", "text": "Israel's <PERSON><PERSON><PERSON> passes the Jerusalem Law.", "html": "1980 - Israel's <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a> passes the <a href=\"https://wikipedia.org/wiki/Jerusalem_Law\" title=\"Jerusalem Law\">Jerusalem Law</a>.", "no_year_html": "Israel's <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a> passes the <a href=\"https://wikipedia.org/wiki/Jerusalem_Law\" title=\"Jerusalem Law\">Jerusalem Law</a>.", "links": [{"title": "Knesset", "link": "https://wikipedia.org/wiki/Knesset"}, {"title": "Jerusalem Law", "link": "https://wikipedia.org/wiki/Jerusalem_Law"}]}, {"year": "1981", "text": "As many as 50,000 demonstrators, mostly women and children, took to the streets in Łódź to protest food ration shortages in Communist Poland.", "html": "1981 - As many as 50,000 demonstrators, mostly women and children, took to the streets in <a href=\"https://wikipedia.org/wiki/%C5%81%C3%B3d%C5%BA\" title=\"Łódź\">Łódź</a> to <a href=\"https://wikipedia.org/wiki/Summer_1981_hunger_demonstrations_in_Poland\" class=\"mw-redirect\" title=\"Summer 1981 hunger demonstrations in Poland\">protest</a> food ration shortages in <a href=\"https://wikipedia.org/wiki/People%27s_Republic_of_Poland\" class=\"mw-redirect\" title=\"People's Republic of Poland\">Communist Poland</a>.", "no_year_html": "As many as 50,000 demonstrators, mostly women and children, took to the streets in <a href=\"https://wikipedia.org/wiki/%C5%81%C3%B3d%C5%BA\" title=\"Łódź\">Łódź</a> to <a href=\"https://wikipedia.org/wiki/Summer_1981_hunger_demonstrations_in_Poland\" class=\"mw-redirect\" title=\"Summer 1981 hunger demonstrations in Poland\">protest</a> food ration shortages in <a href=\"https://wikipedia.org/wiki/People%27s_Republic_of_Poland\" class=\"mw-redirect\" title=\"People's Republic of Poland\">Communist Poland</a>.", "links": [{"title": "Łódź", "link": "https://wikipedia.org/wiki/%C5%81%C3%B3d%C5%BA"}, {"title": "Summer 1981 hunger demonstrations in Poland", "link": "https://wikipedia.org/wiki/Summer_1981_hunger_demonstrations_in_Poland"}, {"title": "People's Republic of Poland", "link": "https://wikipedia.org/wiki/People%27s_Republic_of_Poland"}]}, {"year": "1990", "text": "<PERSON>, Conservative Member of Parliament, is assassinated at his home by the IRA in a car bombing after he assured the group that the British government would never surrender to them.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Conservative Member of Parliament, is assassinated at his home by the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">IRA</a> in a car bombing after he assured the group that the British government would never surrender to them.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Conservative Member of Parliament, is assassinated at his home by the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">IRA</a> in a car bombing after he assured the group that the British government would never surrender to them.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish Republican Army", "link": "https://wikipedia.org/wiki/Irish_Republican_Army"}]}, {"year": "2003", "text": "In Mexico, the last 'old style' Volkswagen Beetle rolls off the assembly line.", "html": "2003 - In Mexico, the last 'old style' <a href=\"https://wikipedia.org/wiki/Volkswagen_Beetle\" title=\"Volkswagen Beetle\">Volkswagen Beetle</a> rolls off the <a href=\"https://wikipedia.org/wiki/Assembly_line\" title=\"Assembly line\">assembly line</a>.", "no_year_html": "In Mexico, the last 'old style' <a href=\"https://wikipedia.org/wiki/Volkswagen_Beetle\" title=\"Volkswagen Beetle\">Volkswagen Beetle</a> rolls off the <a href=\"https://wikipedia.org/wiki/Assembly_line\" title=\"Assembly line\">assembly line</a>.", "links": [{"title": "Volkswagen Beetle", "link": "https://wikipedia.org/wiki/Volkswagen_Beetle"}, {"title": "Assembly line", "link": "https://wikipedia.org/wiki/Assembly_line"}]}, {"year": "2003", "text": "Three years after the death the last Pyrenean ibex, <PERSON>, a clone of her is born only to subsequently die from lung defects. Within minutes, the Pyrenean ibex becomes the first and so-far only species to have ever gone de-extinct as well as go extinct twice.", "html": "2003 - Three years after the death the last <a href=\"https://wikipedia.org/wiki/Pyrenean_ibex\" title=\"Pyrenean ibex\">Pyrenean ibex</a>, <PERSON>, a <a href=\"https://wikipedia.org/wiki/Cloning\" title=\"Cloning\">clone</a> of her is born only to subsequently die from lung defects. Within minutes, the Pyrenean ibex becomes the first and so-far only species to have ever gone <a href=\"https://wikipedia.org/wiki/De-extinct\" class=\"mw-redirect\" title=\"De-extinct\">de-extinct</a> as well as go extinct twice.", "no_year_html": "Three years after the death the last <a href=\"https://wikipedia.org/wiki/Pyrenean_ibex\" title=\"Pyrenean ibex\">Pyrenean ibex</a>, <PERSON>, a <a href=\"https://wikipedia.org/wiki/Cloning\" title=\"Cloning\">clone</a> of her is born only to subsequently die from lung defects. Within minutes, the Pyrenean ibex becomes the first and so-far only species to have ever gone <a href=\"https://wikipedia.org/wiki/De-extinct\" class=\"mw-redirect\" title=\"De-extinct\">de-extinct</a> as well as go extinct twice.", "links": [{"title": "Pyrenean ibex", "link": "https://wikipedia.org/wiki/Pyrenean_ibex"}, {"title": "Cloning", "link": "https://wikipedia.org/wiki/Cloning"}, {"title": "De-extinct", "link": "https://wikipedia.org/wiki/De-extinct"}]}, {"year": "2006", "text": "The world's longest running music show Top of the Pops is broadcast for the last time on BBC Two. The show had aired for 42 years.", "html": "2006 - The world's longest running music show <i><a href=\"https://wikipedia.org/wiki/Top_of_the_Pops\" title=\"Top of the Pops\">Top of the Pops</a></i> is broadcast for the last time on <a href=\"https://wikipedia.org/wiki/BBC_Two\" title=\"BBC Two\">BBC Two</a>. The show had aired for 42 years.", "no_year_html": "The world's longest running music show <i><a href=\"https://wikipedia.org/wiki/Top_of_the_Pops\" title=\"Top of the Pops\">Top of the Pops</a></i> is broadcast for the last time on <a href=\"https://wikipedia.org/wiki/BBC_Two\" title=\"BBC Two\">BBC Two</a>. The show had aired for 42 years.", "links": [{"title": "Top of the Pops", "link": "https://wikipedia.org/wiki/Top_of_the_Pops"}, {"title": "BBC Two", "link": "https://wikipedia.org/wiki/BBC_Two"}]}, {"year": "2006", "text": "An Israeli airstrike kills 28 Lebanese civilians, including 16 children.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/2006_Qana_airstrike\" title=\"2006 Qana airstrike\">An Israeli airstrike</a> kills 28 Lebanese civilians, including 16 children.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2006_Qana_airstrike\" title=\"2006 Qana airstrike\">An Israeli airstrike</a> kills 28 Lebanese civilians, including 16 children.", "links": [{"title": "2006 Qana airstrike", "link": "https://wikipedia.org/wiki/2006_Qana_airstrike"}]}, {"year": "2011", "text": "Marriage of Queen <PERSON>'s eldest granddaughter <PERSON><PERSON> to former rugby union footballer <PERSON>.", "html": "2011 - Marriage of Queen <PERSON>'s eldest granddaughter <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> to former rugby union footballer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Marriage of Queen <PERSON>'s eldest granddaughter <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> to former rugby union footballer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "A train fire kills 32 passengers and injures 27 on the Tamil Nadu Express in Andhra Pradesh, India.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/Nellore_train_fire\" title=\"Nellore train fire\">train fire</a> kills 32 passengers and injures 27 on the <a href=\"https://wikipedia.org/wiki/Tamil_Nadu_Express\" title=\"Tamil Nadu Express\">Tamil Nadu Express</a> in <a href=\"https://wikipedia.org/wiki/Andhra_Pradesh\" title=\"Andhra Pradesh\">Andhra Pradesh</a>, India.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Nellore_train_fire\" title=\"Nellore train fire\">train fire</a> kills 32 passengers and injures 27 on the <a href=\"https://wikipedia.org/wiki/Tamil_Nadu_Express\" title=\"Tamil Nadu Express\">Tamil Nadu Express</a> in <a href=\"https://wikipedia.org/wiki/Andhra_Pradesh\" title=\"Andhra Pradesh\">Andhra Pradesh</a>, India.", "links": [{"title": "Nellore train fire", "link": "https://wikipedia.org/wiki/Nell<PERSON>_train_fire"}, {"title": "Tamil Nadu Express", "link": "https://wikipedia.org/wiki/Tamil_Nadu_Express"}, {"title": "Andhra Pradesh", "link": "https://wikipedia.org/wiki/Andhra_Pradesh"}]}, {"year": "2012", "text": "A power grid failure in Delhi leaves more than 300 million people without power in northern India.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/2012_India_blackouts\" title=\"2012 India blackouts\">power grid failure</a> in <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a> leaves more than 300 million people without power in northern India.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2012_India_blackouts\" title=\"2012 India blackouts\">power grid failure</a> in <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a> leaves more than 300 million people without power in northern India.", "links": [{"title": "2012 India blackouts", "link": "https://wikipedia.org/wiki/2012_India_blackouts"}, {"title": "Delhi", "link": "https://wikipedia.org/wiki/Delhi"}]}, {"year": "2014", "text": "Twenty killed and 150 are trapped after a landslide in Maharashtra, India.", "html": "2014 - Twenty killed and 150 are trapped after a <a href=\"https://wikipedia.org/wiki/2014_Malin_landslide\" title=\"2014 Malin landslide\">landslide in Maharashtra, India</a>.", "no_year_html": "Twenty killed and 150 are trapped after a <a href=\"https://wikipedia.org/wiki/2014_Malin_landslide\" title=\"2014 Malin landslide\">landslide in Maharashtra, India</a>.", "links": [{"title": "2014 Malin landslide", "link": "https://wikipedia.org/wiki/2014_<PERSON><PERSON>_landslide"}]}, {"year": "2020", "text": "NASA's Mars 2020 mission was launched on an Atlas V rocket from Cape Canaveral Air Force Station.", "html": "2020 - NASA's <a href=\"https://wikipedia.org/wiki/Mars_2020\" title=\"Mars 2020\">Mars 2020</a> mission was launched on an <a href=\"https://wikipedia.org/wiki/Atlas_V\" title=\"Atlas V\">Atlas V</a> rocket from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Space_Force_Station\" title=\"Cape Canaveral Space Force Station\">Cape Canaveral Air Force Station</a>.", "no_year_html": "NASA's <a href=\"https://wikipedia.org/wiki/Mars_2020\" title=\"Mars 2020\">Mars 2020</a> mission was launched on an <a href=\"https://wikipedia.org/wiki/Atlas_V\" title=\"Atlas V\">Atlas V</a> rocket from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Space_Force_Station\" title=\"Cape Canaveral Space Force Station\">Cape Canaveral Air Force Station</a>.", "links": [{"title": "Mars 2020", "link": "https://wikipedia.org/wiki/Mars_2020"}, {"title": "Atlas V", "link": "https://wikipedia.org/wiki/Atlas_V"}, {"title": "Cape Canaveral Space Force Station", "link": "https://wikipedia.org/wiki/Cape_Canaveral_Space_Force_Station"}]}, {"year": "2024", "text": "A series of landslides occurs in Kerala, India, causing over 420 fatalities.", "html": "2024 - A <a href=\"https://wikipedia.org/wiki/2024_Wayanad_landslides\" title=\"2024 Wayanad landslides\">series of landslides</a> occurs in <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a>, India, causing over 420 fatalities.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2024_Wayanad_landslides\" title=\"2024 Wayanad landslides\">series of landslides</a> occurs in <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a>, India, causing over 420 fatalities.", "links": [{"title": "2024 Wayanad landslides", "link": "https://wikipedia.org/wiki/2024_Wayanad_landslides"}, {"title": "Kerala", "link": "https://wikipedia.org/wiki/Kerala"}]}], "Births": [{"year": "1470", "text": "<PERSON><PERSON>, emperor of the Ming dynasty (d. 1505)", "html": "1470 - <a href=\"https://wikipedia.org/wiki/Hongzhi_Emperor\" title=\"Hongzhi Emperor\"><PERSON><PERSON></a>, emperor of the Ming dynasty (d. 1505)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hongzhi_Emperor\" title=\"Hongzhi Emperor\">Hong<PERSON></a>, emperor of the Ming dynasty (d. 1505)", "links": [{"title": "Hongzhi Emperor", "link": "https://wikipedia.org/wiki/Hongzhi_Emperor"}]}, {"year": "1511", "text": "<PERSON>, Italian painter, historian, and architect (d. 1574)", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter, historian, and architect (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter, historian, and architect (d. 1574)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1549", "text": "<PERSON><PERSON>, Grand Duke of Tuscany (d. 1609)", "html": "1549 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON>, Grand Duke of Tuscany</a> (d. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON>, Grand Duke of Tuscany</a> (d. 1609)", "links": [{"title": "<PERSON><PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1641", "text": "<PERSON><PERSON>, Dutch physician and anatomist (d. 1673)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_Graaf\" title=\"Regnier de Graaf\"><PERSON><PERSON></a>, Dutch physician and anatomist (d. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_G<PERSON>af\" title=\"Regnier de Graaf\"><PERSON><PERSON></a>, Dutch physician and anatomist (d. 1673)", "links": [{"title": "Regnier de Graaf", "link": "https://wikipedia.org/wiki/Regnier_de_G<PERSON>af"}]}, {"year": "1751", "text": "<PERSON>, Austrian pianist (d. 1829)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist (d. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, English poet and art collector (d. 1855)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and art collector (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and art collector (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON>, Dutch feminist and pamphleteer (d. 1846)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch feminist and pamphleteer (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch feminist and pamphleteer (d. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, Canadian-American priest and theologian (d. 1899)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American priest and theologian (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American priest and theologian (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, English novelist and poet (d. 1848)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AB\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AB\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emily_Bront%C3%AB"}]}, {"year": "1818", "text": "<PERSON>, Dutch lawyer and politician, 16th and 19th Prime Minister of the Netherlands (d. 1897)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, 16th and 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, 16th and 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1825", "text": "<PERSON><PERSON>, Lithuanian engineer and author (d. 1893)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian engineer and author (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian engineer and author (d. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, American lawyer, judge, and politician, 3rd Governor of Oregon (d. 1890)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1855", "text": "<PERSON>, German-Swiss businessman (d. 1919)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss businessman (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss businessman (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON>, American economist and sociologist (d. 1929)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Thorstein_Veblen\" title=\"Thorstein Veblen\"><PERSON><PERSON></a>, American economist and sociologist (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thorstein_Veblen\" title=\"Thorstein Veblen\"><PERSON><PERSON></a>, American economist and sociologist (d. 1929)", "links": [{"title": "<PERSON><PERSON> Veblen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Veblen"}]}, {"year": "1859", "text": "<PERSON>, English minister and humanitarian, founded Lunn Poly (d. 1939)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English minister and humanitarian, founded <a href=\"https://wikipedia.org/wiki/Lunn_<PERSON>y\" title=\"Lunn Poly\"><PERSON>nn Poly</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English minister and humanitarian, founded <a href=\"https://wikipedia.org/wiki/Lunn_Poly\" title=\"Lunn Poly\">Lunn Poly</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>nn Poly", "link": "https://wikipedia.org/wiki/Lunn_Poly"}]}, {"year": "1862", "text": "<PERSON>, Russian general (d. 1933)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American engineer and businessman, founded the Ford Motor Company (d. 1947)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Henry_<PERSON>\" title=\"Henry Ford\"><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henry_Ford\" title=\"Henry Ford\"><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ford Motor Company", "link": "https://wikipedia.org/wiki/Ford_Motor_Company"}]}, {"year": "1872", "text": "Princess <PERSON><PERSON><PERSON><PERSON> of Belgium (d. 1955)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>l%C3%A9mentine_of_Belgium\" title=\"Princess Clémentine of Belgium\">Princess <PERSON>lé<PERSON><PERSON> of Belgium</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>l%C3%A9mentine_of_Belgium\" title=\"Princess Clémentine of Belgium\">Princess <PERSON>lé<PERSON><PERSON> of Belgium</a> (d. 1955)", "links": [{"title": "Princess <PERSON><PERSON><PERSON><PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Princess_Cl%C3%A9mentine_of_Belgium"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, American general, Medal of Honor recipient (d. 1940)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American general, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American general, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1890", "text": "<PERSON>, American baseball player and manager (d. 1975)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, English sculptor and illustrator (d. 1986)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and illustrator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and illustrator (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, English pianist (d. 1987)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Canadian ice hockey player and coach (d. 1955)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9pine\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9pine\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_L%C3%A9pine"}]}, {"year": "1904", "text": "<PERSON>, Mexican poet and playwright (d. 1974)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Salvador_Novo\" title=\"Salvador Novo\"><PERSON> Novo</a>, Mexican poet and playwright (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Novo\" title=\"Salvador Novo\">Salvador Novo</a>, Mexican poet and playwright (d. 1974)", "links": [{"title": "Salvador Novo", "link": "https://wikipedia.org/wiki/Salvador_Novo"}]}, {"year": "1909", "text": "<PERSON><PERSON>, English historian and author (d. 1993)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English historian and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English historian and author (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._Northcote_Parkinson"}]}, {"year": "1910", "text": "<PERSON>, Mexican-American photographer (d. 2003)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American photographer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American photographer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American soldier and cartoonist (d. 1987)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and cartoonist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and cartoonist (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, 3rd Baron <PERSON>, Irish journalist and author, 6th President of the International Olympic Committee (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, Irish journalist and author, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, Irish journalist and author, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (d. 1999)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}, {"title": "President of the International Olympic Committee", "link": "https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee"}]}, {"year": "1920", "text": "<PERSON>, German lieutenant and pilot (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and pilot (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and pilot (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American pianist and educator (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and educator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and educator (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American banker and businessman, co-founded H&R Block (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and businessman, co-founded <a href=\"https://wikipedia.org/wiki/H%26R_Block\" title=\"H&amp;R Block\">H&amp;R Block</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and businessman, co-founded <a href=\"https://wikipedia.org/wiki/H%26R_Block\" title=\"H&amp;R Block\">H&amp;R Block</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "H&R Block", "link": "https://wikipedia.org/wiki/H%26R_Block"}]}, {"year": "1924", "text": "<PERSON><PERSON> <PERSON><PERSON>, American minister, author, and activist (d. 2020)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American minister, author, and activist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American minister, author, and activist (d. 2020)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Welsh actor and trumpet player (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and trumpet player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and trumpet player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Scottish author and poet (d. 1984)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and poet (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and poet (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American artist", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ar"}]}, {"year": "1926", "text": "<PERSON>, American politician and businessman (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and businessman (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and businessman (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actor, producer, and screenwriter (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, producer, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, producer, and screenwriter (d. 2015)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1927", "text": "<PERSON>, American mountaineer (d. 2004)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor (d. 2001)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1927)\" title=\"<PERSON> (actor, born 1927)\"><PERSON></a>, American actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1927)\" title=\"<PERSON> (actor, born 1927)\"><PERSON></a>, American actor (d. 2001)", "links": [{"title": "<PERSON> (actor, born 1927)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor,_born_1927)"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Indian actress (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American baseball player and sportscaster (d. 2007)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian-American puppeteer and producer", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian-American puppeteer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian-American puppeteer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, French historian and author (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Soviet pilot, engineer and military officer (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet pilot, engineer and military officer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet pilot, engineer and military officer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, 9th Major League Baseball Commissioner", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Selig\" title=\"<PERSON> Selig\"><PERSON></a>, 9th <a href=\"https://wikipedia.org/wiki/Commissioner_of_Baseball\" title=\"Commissioner of Baseball\">Major League Baseball Commissioner</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lig\" title=\"<PERSON> Selig\"><PERSON></a>, 9th <a href=\"https://wikipedia.org/wiki/Commissioner_of_Baseball\" title=\"Commissioner of Baseball\">Major League Baseball Commissioner</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Selig"}, {"title": "Commissioner of Baseball", "link": "https://wikipedia.org/wiki/Commissioner_of_Baseball"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Guy\" title=\"Buddy Guy\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Guy\" title=\"Buddy Guy\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Duchess of Badajoz (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duchess_of_Badajoz\" title=\"<PERSON><PERSON><PERSON>, Duchess of Badajoz\"><PERSON><PERSON><PERSON>, Duchess of Badajoz</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duchess_of_Badajoz\" title=\"<PERSON><PERSON><PERSON>, Duchess of Badajoz\"><PERSON><PERSON><PERSON>, Duchess of Badajoz</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duchess of Badajoz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duchess_of_Badajoz"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, French politician, French Minister of Foreign Affairs", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Herv%C3%A9_de_Charette\" title=\"<PERSON><PERSON><PERSON>\">Her<PERSON><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Herv%C3%A9_de_Charette\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Herv%C3%A9_de_Charette"}, {"title": "Minister of Foreign Affairs (France)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)"}]}, {"year": "1938", "text": "<PERSON>, English photographer (d. 2019)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, English photographer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, English photographer (d. 2019)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(photographer)"}]}, {"year": "1939", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American activist, founded the Feminist Majority Foundation", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/Feminist_Majority_Foundation\" title=\"Feminist Majority Foundation\">Feminist Majority Foundation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/Feminist_Majority_Foundation\" title=\"Feminist Majority Foundation\">Feminist Majority Foundation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>al"}, {"title": "Feminist Majority Foundation", "link": "https://wikipedia.org/wiki/Feminist_Majority_Foundation"}]}, {"year": "1940", "text": "<PERSON>, American lawyer and politician (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English businessman, founded Sinclair Radionics and Sinclair Research (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Sinclair_Radionics\" title=\"Sinclair Radionics\">Sinclair Radionics</a> and <a href=\"https://wikipedia.org/wiki/Sinclair_Research\" title=\"Sinclair Research\">Sinclair Research</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Sinclair_Radionics\" title=\"Sinclair Radionics\">Sinclair Radionics</a> and <a href=\"https://wikipedia.org/wiki/Sinclair_Research\" title=\"Sinclair Research\">Sinclair Research</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sinclair Radionics", "link": "https://wikipedia.org/wiki/Sinclair_Radionics"}, {"title": "Sinclair Research", "link": "https://wikipedia.org/wiki/Sinclair_Research"}]}, {"year": "1941", "text": "<PERSON>, Canadian singer-songwriter and actor", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, English environmentalist and painter (d. 2018)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Polly<PERSON>_Pickering\" title=\"Pollyanna Pickering\"><PERSON><PERSON></a>, English environmentalist and painter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polly<PERSON>_Pickering\" title=\"Pollyanna Pickering\"><PERSON><PERSON></a>, English environmentalist and painter (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pickering"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Canadian physicist and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian physicist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A7<PERSON>_<PERSON>rin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian physicist and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Scottish race car driver (d. 1973)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Frances de la Tour\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Frances de la Tour\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, French novelist and screenwriter, Nobel Prize laureate", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and screenwriter, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and screenwriter, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1945", "text": "<PERSON>, American saxophonist and composer (d. 2024)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American race car driver and sportscaster (d. 1994)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English bass player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, French virologist and biologist, Nobel Prize laureate", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>%C3%A9-Sinoussi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French virologist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>%C3%A9-Sinoussi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French virologist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7oise_Barr%C3%A9-Sinoussi"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1947", "text": "<PERSON>, American physician and author (d. 1998)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(WHO_official)\" class=\"mw-redirect\" title=\"<PERSON> (WHO official)\"><PERSON></a>, American physician and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(WHO_official)\" class=\"mw-redirect\" title=\"<PERSON> (WHO official)\"><PERSON></a>, American physician and author (d. 1998)", "links": [{"title": "<PERSON> (WHO official)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(WHO_official)"}]}, {"year": "1947", "text": "<PERSON>, Austrian-American bodybuilder, actor, and politician, 38th Governor of California", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American bodybuilder, actor, and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American bodybuilder, actor, and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of California", "link": "https://wikipedia.org/wiki/Governor_of_California"}]}, {"year": "1948", "text": "<PERSON>, American basketball player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Moroccan-French actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jean <PERSON>\"><PERSON></a>, Moroccan-French actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jean <PERSON>\"><PERSON></a>, Moroccan-French actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1948", "text": "<PERSON>, Bulgarian pianist and composer (d. 2010)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian pianist and composer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian pianist and composer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English lawyer and judge (d. 2023)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English lawyer and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, South African cricketer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Indian-English painter and sculptor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English botanist and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor, director, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English drummer and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Rat_Scabies\" title=\"Rat Scabies\"><PERSON></a>, English drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rat_Scabies\" title=\"Rat Scabies\"><PERSON></a>, English drummer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rat_Scabies"}]}, {"year": "1955", "text": "<PERSON>-<PERSON>, English violinist and conductor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist and conductor", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Burke\" title=\"<PERSON> Burke\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Burke\" title=\"<PERSON> Burke\"><PERSON></a>, American actress", "links": [{"title": "Delta Burke", "link": "https://wikipedia.org/wiki/<PERSON>_Burke"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/R%C3%A9al_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9al_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9al_<PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German prelate, Prefect of the Pontifical Household, and former personal secretary to <PERSON> <PERSON>", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4<PERSON>wein\" title=\"<PERSON>\"><PERSON></a>, German prelate, Prefect of the Pontifical Household, and former personal secretary to <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4<PERSON>wein\" title=\"<PERSON>\"><PERSON></a>, German prelate, Prefect of the Pontifical Household, and former personal secretary to <a href=\"https://wikipedia.org/wiki/Pope_Benedict_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_G%C3%A4nswein"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American lawyer and academic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anita Hill\"><PERSON></a>, American lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anita Hill\"><PERSON></a>, American lawyer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, American painter and educator", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American painter and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American painter and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>,  Italian director and cinematographer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American basketball player and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American baseball player and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Argentinian footballer, coach, and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ido\" title=\"<PERSON><PERSON> Pumpido\"><PERSON><PERSON></a>, Argentinian footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>umpido\" title=\"<PERSON><PERSON> Pumpido\"><PERSON><PERSON></a>, Argentinian footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nery_<PERSON>umpido"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English radio broadcaster", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, English decathlete and trainer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English decathlete and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English decathlete and trainer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American-English musicologist and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English musicologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English musicologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American director and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Filipino independent film director", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Brillante_Mendoza\" title=\"Brillante Mendoza\"><PERSON><PERSON><PERSON></a>, Filipino independent film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brillante_Mendoza\" title=\"Brillante Mendoza\"><PERSON><PERSON><PERSON></a>, Filipino independent film director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American chef, author, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American chef, author, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American chef, author, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>n_Brown"}]}, {"year": "1962", "text": "<PERSON>, American ice hockey player and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Indian accountant and terrorist (d. 2015)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ku<PERSON> Me<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian accountant and terrorist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Me<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian accountant and terrorist (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yaku<PERSON>_<PERSON>mon"}]}, {"year": "1963", "text": "<PERSON>, English-Australian cricketer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English-Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English-Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer)"}]}, {"year": "1963", "text": "<PERSON>, American actress and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Andorran architect and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Antoni_Mart%C3%AD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Andorran architect and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antoni_Mart%C3%AD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Andorran architect and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antoni_Mart%C3%AD"}]}, {"year": "1963", "text": "<PERSON>, American basketball player, coach, and executive", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" class=\"mw-redirect\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player, coach, and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" class=\"mw-redirect\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player, coach, and executive", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and banjo player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and banjo player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and banjo player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Viv<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ivica <PERSON>\">Viv<PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viv<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Vivica <PERSON>\">V<PERSON><PERSON></a>, American actress", "links": [{"title": "Vivica A. <PERSON>", "link": "https://wikipedia.org/wiki/Viv<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Lebanese-American director, producer, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese-American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese-American director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Estonian lawyer and politician, 6th Estonian Minister of Culture", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>j%C3%A4rv\" title=\"<PERSON><PERSON>v\"><PERSON><PERSON></a>, Estonian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Ministry_of_Culture_(Estonia)\" title=\"Ministry of Culture (Estonia)\">Estonian Minister of Culture</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>j%C3%A4rv\" title=\"<PERSON><PERSON>v\"><PERSON><PERSON></a>, Estonian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Ministry_of_Culture_(Estonia)\" title=\"Ministry of Culture (Estonia)\">Estonian Minister of Culture</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ne_Randj%C3%A4rv"}, {"title": "Ministry of Culture (Estonia)", "link": "https://wikipedia.org/wiki/Ministry_of_Culture_(Estonia)"}]}, {"year": "1965", "text": "<PERSON>, English cricketer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, New Zealand actress and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>,  English guitarist and songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian rugby league player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English author and singer-songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor and football player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Terry_Crews"}]}, {"year": "1968", "text": "<PERSON>, Polish race walker and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish race walker and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish race walker and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Welsh drummer and songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Welsh drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Welsh drummer and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1969", "text": "<PERSON>, Australian actor, director, and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, South African cricketer and lawyer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(South_African_sportsman)\" title=\"<PERSON><PERSON><PERSON> (South African sportsman)\"><PERSON><PERSON><PERSON></a>, South African cricketer and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(South_African_sportsman)\" title=\"<PERSON><PERSON><PERSON> (South African sportsman)\"><PERSON><PERSON><PERSON></a>, South African cricketer and lawyer", "links": [{"title": "<PERSON><PERSON><PERSON> (South African sportsman)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(South_African_sportsman)"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Welsh businessman and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Alun_<PERSON>\" title=\"Alun <PERSON>\"><PERSON><PERSON></a>, Welsh businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alun_<PERSON>\" title=\"<PERSON>un <PERSON>\"><PERSON><PERSON></a>, Welsh businessman and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alun_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American comedian, actor, and singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English-American director, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American-Puerto Rican singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Puerto Rican singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Puerto Rican singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian comedian and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American basketball player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, English mountaineer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cool\" title=\"Kenton Cool\"><PERSON><PERSON></a>, English mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cool\" title=\"Kenton Cool\"><PERSON><PERSON></a>, English mountaineer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Turkish footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/%C3%9Cmit_Davala\" title=\"<PERSON><PERSON> Davala\"><PERSON><PERSON></a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9Cmit_Davala\" title=\"<PERSON><PERSON> Davala\"><PERSON><PERSON></a>, Turkish footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%9Cmit_Da<PERSON>a"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>bis\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anastasios_Katsabis"}]}, {"year": "1973", "text": "<PERSON>, Swedish ice hockey player and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4slund\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_N%C3%A4slund\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Markus_N%C3%A4slund"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Indian playback singer and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sonu <PERSON>\"><PERSON><PERSON></a>, Indian playback singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian playback singer and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gam"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American minister and politician (d. 2015)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Clementa_C._<PERSON>ney\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American minister and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clementa_C._<PERSON>ney\" title=\"Clement<PERSON>\"><PERSON><PERSON></a>, American minister and politician (d. 2015)", "links": [{"title": "Clementa C<PERSON>", "link": "https://wikipedia.org/wiki/Clementa_<PERSON><PERSON>_<PERSON>nc<PERSON>ney"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Bulgarian footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English rugby league footballer, and rugby union footballer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, English rugby league footballer, and rugby union footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, English rugby league footballer, and rugby union footballer and coach", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)"}]}, {"year": "1974", "text": "<PERSON>, American actress and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English author and activist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American basketball player and computer scientist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Starbird\"><PERSON></a>, American basketball player and computer scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kate Starbird\"><PERSON></a>, American basketball player and computer scientist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Starbird"}]}, {"year": "1977", "text": "<PERSON>, Chilean model and journalist;", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean model and journalist;", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean model and journalist;", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>-<PERSON><PERSON><PERSON>, American volleyball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_May-Treanor\" title=\"<PERSON> May-Treanor\"><PERSON> May<PERSON></a>, American volleyball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_May-T<PERSON>nor\" title=\"<PERSON> May-Treanor\"><PERSON> May<PERSON></a>, American volleyball player and coach", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Welsh singer-songwriter and child abuse convict", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lostprophets)\" class=\"mw-redirect\" title=\"<PERSON> (Lostprophets)\"><PERSON></a>, Welsh singer-songwriter and child abuse convict", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lostprophets)\" class=\"mw-redirect\" title=\"<PERSON> (Lostprophets)\"><PERSON></a>, Welsh singer-songwriter and child abuse convict", "links": [{"title": "<PERSON> (Lostprophets)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lostprophets)"}]}, {"year": "1979", "text": "<PERSON>, Puerto Rican basketball player and singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican basketball player and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican basketball player and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, South African cricketer and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Northern Irish golfer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Syrian journalist (d. 2012)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian journalist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian journalist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American folk-rock singer-songwriter and musician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk-rock singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk-rock singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, South African-English golfer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American motorcycle racer (d. 2017)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South African rugby union footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby union footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby union footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American soccer player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Hope_Solo\" title=\"Hope Solo\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hope_Solo\" title=\"Hope Solo\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hope_Solo"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Estonian decathlete", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Indrek_Turi\" title=\"Indrek Turi\"><PERSON><PERSON><PERSON></a>, Estonian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indrek_Turi\" title=\"Indrek Turi\"><PERSON><PERSON><PERSON></a>, Estonian decathlete", "links": [{"title": "Indrek <PERSON>", "link": "https://wikipedia.org/wiki/Indrek_Turi"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Syrian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer)"}]}, {"year": "1982", "text": "<PERSON>, American actor and comedian", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Irish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Se%C3%A1n_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%A1n_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%A1n_Dillon"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Estonian race car driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress and singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Icelandic politician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/%C3%81sa_<PERSON><PERSON>_Hj%C3%A1lmarsd%C3%B3ttir\" title=\"<PERSON><PERSON>lma<PERSON>dóttir\"><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81<PERSON>_<PERSON>_Hj%C3%A1lmarsd%C3%B3ttir\" title=\"<PERSON><PERSON>lma<PERSON>dótti<PERSON>\"><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81sa_<PERSON><PERSON>_Hj%C3%A1lmarsd%C3%B3ttir"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Australian artistic gymnast", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Trud<PERSON>_<PERSON>\" title=\"Trud<PERSON>\">T<PERSON><PERSON></a>, Australian artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trud<PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\">T<PERSON><PERSON></a>, Australian artistic gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1985", "text": "<PERSON>, Norwegian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Italian ice dancer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian ice dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>car"}]}, {"year": "1986", "text": "<PERSON>, Australian rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American golfer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1988", "text": "<PERSON>, Malaysian rhythmic gymnast", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Malaysian rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Malaysian rhythmic gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Spanish motorcycle racer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Aleix_Espargar%C3%B3\" title=\"Aleix <PERSON>spargaró\"><PERSON><PERSON><PERSON></a>, Spanish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleix_Espargar%C3%B3\" title=\"Aleix <PERSON>spargaró\"><PERSON><PERSON><PERSON></a>, Spanish motorcycle racer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleix_Espargar%C3%B3"}]}, {"year": "1989", "text": "<PERSON>, South African cricketer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Welsh footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1990", "text": "<PERSON>, Australian journalist", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Australian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Australian journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1991", "text": "<PERSON>, English singer-songwriter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English wheelchair racer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wheelchair racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wheelchair racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Portuguese footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Go<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Gomes"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Portuguese tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mar<PERSON><PERSON>_Moura"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Malaysian actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nelydia_<PERSON>rose"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Hirving_<PERSON>\" title=\"Hirving <PERSON>\">Hirvin<PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hirving_<PERSON>\" title=\"Hirving <PERSON>\">Hirvin<PERSON></a>, Mexican footballer", "links": [{"title": "Hirving <PERSON>", "link": "https://wikipedia.org/wiki/Hirving_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Serbian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Stojanovi%C4%87"}]}, {"year": "1996", "text": "<PERSON>, American hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_King\" title=\"<PERSON> King\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_King\" title=\"<PERSON> King\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "578", "text": "<PERSON>, Greek bishop", "html": "578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "579", "text": "<PERSON>", "html": "579 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_I\" title=\"Pope Benedict I\">Pope <PERSON> I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_I\" title=\"Pope Benedict I\">Pope Benedict I</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_I"}]}, {"year": "734", "text": "<PERSON><PERSON><PERSON>, English archbishop (b. 670)", "html": "734 - <a href=\"https://wikipedia.org/wiki/Ta<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English archbishop (b. 670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tat<PERSON>\" title=\"Ta<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English archbishop (b. 670)", "links": [{"title": "Tatwine", "link": "https://wikipedia.org/wiki/Tatwine"}]}, {"year": "829", "text": "<PERSON>, general of the Tang Dynasty", "html": "829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Shi Xiancheng\"><PERSON></a>, general of the Tang Dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Shi Xiancheng\"><PERSON></a>, general of the Tang Dynasty", "links": [{"title": "Shi <PERSON>", "link": "https://wikipedia.org/wiki/Shi_<PERSON>ng"}]}, {"year": "1286", "text": "<PERSON>, Syrian scholar and historian (b. 1226)", "html": "1286 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Hebraeus\" title=\"Bar Hebraeus\"><PERSON></a>, Syrian scholar and historian (b. 1226)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Hebraeus\" title=\"Bar Hebraeus\"><PERSON></a>, Syrian scholar and historian (b. 1226)", "links": [{"title": "Bar Hebraeus", "link": "https://wikipedia.org/wiki/Bar_Hebraeus"}]}, {"year": "1393", "text": "<PERSON>, Lord of Ferrara and Modena (b. 1347)", "html": "1393 - <a href=\"https://wikipedia.org/wiki/<PERSON>_d%27Este\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Ferrara_and_of_Modena\" title=\"Duke of Ferrara and of Modena\">Lord of Ferrara and Modena</a> (b. 1347)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_d%27Este\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Ferrara_and_of_Modena\" title=\"Duke of Ferrara and of Modena\">Lord of Ferrara and Modena</a> (b. 1347)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberto_d%27Este"}, {"title": "Duke of Ferrara and of Modena", "link": "https://wikipedia.org/wiki/<PERSON>_of_Ferrara_and_of_Modena"}]}, {"year": "1516", "text": "<PERSON>, Count of Nassau-Siegen (b. 1455)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a> (b. 1455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a> (b. 1455)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-<PERSON>n"}]}, {"year": "1540", "text": "<PERSON>, English priest and martyr (b. 1497)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, English priest and martyr (b. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, English priest and martyr (b. 1497)", "links": [{"title": "<PERSON> (martyr)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)"}]}, {"year": "1540", "text": "<PERSON>, English martyr and reformer (b. 1495)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, English martyr and reformer (b. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, English martyr and reformer (b. 1495)", "links": [{"title": "<PERSON> (martyr)", "link": "https://wikipedia.org/wiki/<PERSON>_(martyr)"}]}, {"year": "1550", "text": "<PERSON>, 1st Earl of Southampton, English politician, Lord Chancellor of the United Kingdom (b. 1505)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Southampton\" title=\"<PERSON>, 1st Earl of Southampton\"><PERSON>, 1st Earl of Southampton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of the United Kingdom</a> (b. 1505)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Southampton\" title=\"<PERSON>, 1st Earl of Southampton\"><PERSON>, 1st Earl of Southampton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of the United Kingdom</a> (b. 1505)", "links": [{"title": "<PERSON>, 1st Earl of Southampton", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Southampton"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1566", "text": "<PERSON>, French doctor (b. 1507)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French doctor (b. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French doctor (b. 1507)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON>, 1st Earl of Tyrconnell, last King of Tyrconnell (b. 1575)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27<PERSON>,_1st_Earl_of_Tyrconnell\" title=\"<PERSON>, 1st Earl of Tyrconnell\"><PERSON>, 1st Earl of Tyrconnell</a>, last King of <a href=\"https://wikipedia.org/wiki/Tyrconnell\" title=\"Tyrconnell\">Tyrconnell</a> (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27<PERSON>,_1st_Earl_of_Tyrconnell\" title=\"<PERSON>, 1st Earl of Tyrconnell\"><PERSON>, 1st Earl of Tyrconnell</a>, last King of <a href=\"https://wikipedia.org/wiki/Tyrconnell\" title=\"Tyrconnell\">Tyrconnell</a> (b. 1575)", "links": [{"title": "<PERSON>, 1st Earl of Tyrconnell", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON>,_1st_Earl_of_Tyrconnell"}, {"title": "Tyrc<PERSON>", "link": "https://wikipedia.org/wiki/Tyrconnell"}]}, {"year": "1624", "text": "<PERSON><PERSON><PERSON>, 3rd Duke of Lennox, British nobleman (b. 1579)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/Esm%C3%A<PERSON>_<PERSON>,_3rd_Duke_of_Lennox\" title=\"<PERSON><PERSON><PERSON>, 3rd Duke of Lennox\"><PERSON><PERSON><PERSON>, 3rd Duke of Lennox</a>, British nobleman (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esm%C3%A<PERSON>_<PERSON>,_3rd_Duke_of_Lennox\" title=\"<PERSON><PERSON><PERSON>, 3rd Duke of Lennox\"><PERSON><PERSON><PERSON>, 3rd Duke of Lennox</a>, British nobleman (b. 1579)", "links": [{"title": "<PERSON><PERSON><PERSON>, 3rd Duke of Lennox", "link": "https://wikipedia.org/wiki/Esm%C3%A<PERSON>_<PERSON>,_3rd_<PERSON>_<PERSON>_Lennox"}]}, {"year": "1652", "text": "<PERSON>, Duke of Nemours (b. 1624)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Nemours\" title=\"<PERSON>, Duke of Nemours\"><PERSON>, Duke of Nemours</a> (b. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Nemours\" title=\"<PERSON>, Duke of Nemours\"><PERSON>, Duke of Nemours</a> (b. 1624)", "links": [{"title": "<PERSON>, Duke of Nemours", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Nemours"}]}, {"year": "1680", "text": "<PERSON>, 6th Earl of Ossory, Irish admiral and politician, Lord Lieutenant of Ireland (b. 1634)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Ossory\" title=\"<PERSON>, 6th Earl of Ossory\"><PERSON>, 6th Earl of Ossory</a>, Irish admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Earl_of_Ossory\" title=\"<PERSON>, 6th Earl of Ossory\"><PERSON>, 6th Earl of Ossory</a>, Irish admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1634)", "links": [{"title": "<PERSON>, 6th Earl of Ossory", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Ossory"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1683", "text": "<PERSON> of Spain (b. 1638)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (b. 1638)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Spain"}]}, {"year": "1691", "text": "<PERSON>, German scholar and academic (b. 1639)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and academic (b. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and academic (b. 1639)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1700", "text": "<PERSON> <PERSON>, Duke of Gloucester, English royal (b. 1689)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester\" title=\"Prince <PERSON>, Duke of Gloucester\">Prince <PERSON>, Duke of Gloucester</a>, English royal (b. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester\" title=\"Prince <PERSON>, Duke of Gloucester\">Prince <PERSON>, Duke of Gloucester</a>, English royal (b. 1689)", "links": [{"title": "<PERSON> <PERSON>, Duke of Gloucester", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester"}]}, {"year": "1718", "text": "<PERSON>, English businessman and philosopher, founded the Province of Pennsylvania (b. 1644)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Penn\"><PERSON></a>, English businessman and philosopher, founded the <a href=\"https://wikipedia.org/wiki/Province_of_Pennsylvania\" title=\"Province of Pennsylvania\">Province of Pennsylvania</a> (b. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Penn\"><PERSON></a>, English businessman and philosopher, founded the <a href=\"https://wikipedia.org/wiki/Province_of_Pennsylvania\" title=\"Province of Pennsylvania\">Province of Pennsylvania</a> (b. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Province of Pennsylvania", "link": "https://wikipedia.org/wiki/Province_of_Pennsylvania"}]}, {"year": "1771", "text": "<PERSON>, English poet (b. 1716)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1716)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, Mexican priest and soldier (b. 1753)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_y_Costilla\" title=\"<PERSON> y Costilla\"><PERSON> y <PERSON></a>, Mexican priest and soldier (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_y_Costilla\" title=\"<PERSON> y Costilla\"><PERSON> y <PERSON></a>, Mexican priest and soldier (b. 1753)", "links": [{"title": "<PERSON> y Costilla", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_y_<PERSON>"}]}, {"year": "1832", "text": "<PERSON><PERSON>, Vietnamese general, mandarin (b. 1763-4)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/L%C3%AA_V%C4%83n_Duy%E1%BB%87t\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese general, mandarin (b. 1763-4)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%AA_V%C4%83n_Duy%E1%BB%87t\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese general, mandarin (b. 1763-4)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%AA_V%C4%83n_Duy%E1%BB%87t"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON>, Norwegian poet and journalist (b. 1818) ", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian poet and journalist (b. 1818) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian poet and journalist (b. 1818) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American general (b. 1825)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, England cricketer (b. 1846)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>olo<PERSON>\" title=\"<PERSON>olo<PERSON>\"><PERSON></a>, England cricketer (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>olo<PERSON>\" title=\"<PERSON> Absolom\"><PERSON></a>, England cricketer (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>olom"}]}, {"year": "1898", "text": "<PERSON>, German lawyer and politician, 1st Chancellor of Germany (b. 1815)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "1900", "text": "<PERSON>, Duke of Saxe-Coburg and Gotha (b. 1844)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxe-Coburg_and_Gotha\" title=\"<PERSON>, Duke of Saxe-Coburg and Gotha\"><PERSON>, Duke of Saxe-Coburg and Gotha</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxe-Coburg_and_Gotha\" title=\"<PERSON>, Duke of Saxe-Coburg and Gotha\"><PERSON>, Duke of Saxe-Coburg and Gotha</a> (b. 1844)", "links": [{"title": "<PERSON>, Duke of Saxe-Coburg and Gotha", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxe-Coburg_and_Gotha"}]}, {"year": "1912", "text": "Emperor <PERSON> of Japan (b. 1852)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor <PERSON>\">Emperor Meiji</a> of Japan (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor Meiji\">Emperor Meiji</a> of Japan (b. 1852)", "links": [{"title": "Emperor <PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American soldier, journalist, and poet (b. 1886)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and poet (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and poet (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Swedish executioner (b. 1848)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish executioner (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish executioner (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Swiss-Spanish footballer and businessman, founded FC Barcelona (b. 1877)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Spanish footballer and businessman, founded <a href=\"https://wikipedia.org/wiki/FC_Barcelona\" title=\"FC Barcelona\">FC Barcelona</a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Spanish footballer and businessman, founded <a href=\"https://wikipedia.org/wiki/FC_Barcelona\" title=\"FC Barcelona\">FC Barcelona</a> (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "FC Barcelona", "link": "https://wikipedia.org/wiki/FC_Barcelona"}]}, {"year": "1938", "text": "<PERSON>, English swimmer and water polo player (b. 1878)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, English swimmer and water polo player (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, English swimmer and water polo player (b. 1878)", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1941", "text": "<PERSON>, Latvian politician, former Prime Minister of Latvia (b. 1877)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%86%C5%A1\" title=\"<PERSON>\"><PERSON></a>, Latvian politician, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%86%C5%A1\" title=\"<PERSON>\"><PERSON></a>, Latvian politician, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a> (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>i%C5%86%C5%A1"}, {"title": "Prime Minister of Latvia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Latvia"}]}, {"year": "1947", "text": "<PERSON>, English-Australian miner and politician, 6th Prime Minister of Australia (b. 1860)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian miner and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian miner and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese author and playwright (b. 1886)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Jun%27ichir%C5%8D_Tan<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author and playwright (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jun%27ichir%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author and playwright (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jun%27ichir%C5%8D_<PERSON><PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Scottish-Australian academic (b. 1874)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian academic (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian academic (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Hungarian-American conductor and composer (b. 1897)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American conductor and composer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American conductor and composer (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian politician, 36th Premier of Victoria (b. 1906)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1975", "text": "<PERSON>, American author and critic (b. 1921)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American scholar, author, and educator (b. 1885)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American scholar, author, and educator (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American scholar, author, and educator (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American songwriter and publicist (b. 1896)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and publicist (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and publicist (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English actress (b. 1887)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American mathematician and theorist (b. 1919)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American professional bull rider (b. 1963)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lane Frost\"><PERSON></a>, American professional bull rider (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lane Frost\"><PERSON></a>, American professional bull rider (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, British Member of Parliament who was assassinated by the IRA (b. 1937)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Member of Parliament who was assassinated by the IRA (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Member of Parliament who was assassinated by the IRA (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Filipino-American actress and singer (b. 1915)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American actress and singer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American actress and singer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian-American illustrator, co-created <PERSON> (b. 1914)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American illustrator, co-created <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Superman\">Superman</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American illustrator, co-created <a href=\"https://wikipedia.org/wiki/Superman\" title=\"Superman\"><PERSON></a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, German-American film producer and advertising executive (b. 1920)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American film producer and advertising executive (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American film producer and advertising executive (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, French-American actress (b. 1903)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Vietnamese emperor (b. 1913)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i\" title=\"Bảo Đạ<PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese emperor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i\" title=\"Bảo Đại\"><PERSON><PERSON><PERSON></a>, Vietnamese emperor (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i"}]}, {"year": "1998", "text": "<PERSON>, American television host (b. 1917)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Buffalo <PERSON>\"><PERSON></a>, American television host (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Buffalo <PERSON>\"><PERSON></a>, American television host (b. 1917)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, German engineer (b. 1924)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Scottish motorcycle racer (b. 1962)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish motorcycle racer (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish motorcycle racer (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American record producer, founded Sun Records (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, founded <a href=\"https://wikipedia.org/wiki/Sun_Records\" title=\"Sun Records\">Sun Records</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, founded <a href=\"https://wikipedia.org/wiki/Sun_Records\" title=\"Sun Records\">Sun Records</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sun Records", "link": "https://wikipedia.org/wiki/Sun_Records"}]}, {"year": "2005", "text": "<PERSON>, American baseball player (b. 1905)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Sudanese colonel and politician, 6th President of South Sudan (b. 1945)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese colonel and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_South_Sudan\" class=\"mw-redirect\" title=\"List of Presidents of South Sudan\">President of South Sudan</a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese colonel and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_South_Sudan\" class=\"mw-redirect\" title=\"List of Presidents of South Sudan\">President of South Sudan</a> (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of South Sudan", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_South_Sudan"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and author(b. 1946)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and author(b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and author(b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Canadian golfer (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Al <PERSON>lding\"><PERSON></a>, Canadian golfer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Al <PERSON>lding\"><PERSON></a>, Canadian golfer (b. 1924)", "links": [{"title": "Al Balding", "link": "https://wikipedia.org/wiki/Al_Balding"}]}, {"year": "2006", "text": "<PERSON>, American philosopher and author (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Murray Bookchin\"><PERSON></a>, American philosopher and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Murray Bookchin\"><PERSON></a>, American philosopher and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Murray_<PERSON>chin"}]}, {"year": "2006", "text": "<PERSON>, American accordion player and composer (b. 1904)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player and composer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player and composer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Iranian activist (b. 1972)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(student)\" title=\"<PERSON> (student)\"><PERSON></a>, Iranian activist (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(student)\" title=\"<PERSON> (student)\"><PERSON></a>, Iranian activist (b. 1972)", "links": [{"title": "<PERSON> (student)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(student)"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Italian director and screenwriter (b. 1912)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Romanian patriarch (b. 1915)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Teoctist_Ar%C4%83pa%C8%99u\" title=\"Teoctist <PERSON><PERSON><PERSON>\"><PERSON>oc<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Romanian patriarch (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Teoctist_Ar%C4%83pa%C8%99u\" title=\"Teoctist <PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON>oc<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON></a>, Romanian patriarch (b. 1915)", "links": [{"title": "Te<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teoctist_Ar%C4%83pa%C8%99u"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Swedish director, producer, and screenwriter (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish director, producer, and screenwriter (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish director, producer, and screenwriter (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American football player and coach (b. 1931)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" class=\"mw-redirect\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" class=\"mw-redirect\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (b. 1931)", "links": [{"title": "<PERSON> (American football coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)"}]}, {"year": "2008", "text": "<PERSON>, American businesswoman and diplomat, United States Ambassador to the United Kingdom (b. 1927)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Kingdom", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom"}]}, {"year": "2009", "text": "<PERSON>, Nigerian militant leader, founded <PERSON><PERSON> (b. 1970)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Boko_Haram)\" title=\"<PERSON> (Boko Haram)\"><PERSON></a>, Nigerian militant leader, founded <a href=\"https://wikipedia.org/wiki/Bo<PERSON>_Haram\" title=\"Bo<PERSON> Haram\"><PERSON><PERSON></a> (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Boko_Haram)\" title=\"<PERSON> (Boko Haram)\"><PERSON></a>, Nigerian militant leader, founded <a href=\"https://wikipedia.org/wiki/Bo<PERSON>_Haram\" title=\"Boko Haram\"><PERSON><PERSON></a> (b. 1970)", "links": [{"title": "<PERSON> (Bo<PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Boko_Haram)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>m"}]}, {"year": "2009", "text": "<PERSON>, German director and screenwriter (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American basketball player (b. 1932)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1932)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Irish author, playwright, and journalist (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish author, playwright, and journalist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish author, playwright, and journalist (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ve_Binchy"}]}, {"year": "2012", "text": "<PERSON>, American singer and guitarist (b. 1968)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish actor, director, and screenwriter (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish actor, director, and screenwriter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Swedish actor, director, and screenwriter (b. 1923)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English footballer and manager (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Les_Green"}]}, {"year": "2012", "text": "<PERSON>, New Zealand-Australian actor and screenwriter (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian actor and screenwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian actor and screenwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian ice hockey player (b. 1960)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (b. 1960)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "2012", "text": "<PERSON>, American colonel (b. 1911)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American architect, designed the State of Georgia Building (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/State_of_Georgia_Building\" title=\"State of Georgia Building\">State of Georgia Building</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/State_of_Georgia_Building\" title=\"State of Georgia Building\">State of Georgia Building</a> (b. 1918)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)"}, {"title": "State of Georgia Building", "link": "https://wikipedia.org/wiki/State_of_Georgia_Building"}]}, {"year": "2013", "text": "<PERSON><PERSON>, German businessman (b. 1913)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German businessman (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German businessman (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American sociologist and author (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lieutenant, publisher, and politician (b. 1914)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American lieutenant, publisher, and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr<PERSON>\"><PERSON> Jr.</a>, American lieutenant, publisher, and politician (b. 1914)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2013", "text": "<PERSON><PERSON>, Spanish footballer and manager (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and manager (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American basketball player (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>he<PERSON>man\"><PERSON><PERSON></a>, American basketball player (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Indian-English author, poet, and playwright (b. 1913)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Indian-English author, poet, and playwright (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Indian-English author, poet, and playwright (b. 1913)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "2014", "text": "<PERSON>, American director and producer (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, German director, producer, and screenwriter (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director, producer, and screenwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director, producer, and screenwriter (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "2014", "text": "<PERSON>, Argentinian businessman (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian businessman (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian businessman (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julio_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English geographer, author, and academic (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(urbanist)\" title=\"<PERSON> (urbanist)\"><PERSON></a>, English geographer, author, and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(urbanist)\" title=\"<PERSON> (urbanist)\"><PERSON></a>, English geographer, author, and academic (b. 1932)", "links": [{"title": "<PERSON> (urbanist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(urbanist)"}]}, {"year": "2014", "text": "<PERSON>, American make-up artist (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(make-up_artist)\" title=\"<PERSON> (make-up artist)\"><PERSON></a>, American make-up artist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(make-up_artist)\" title=\"<PERSON> (make-up artist)\"><PERSON></a>, American make-up artist (b. 1922)", "links": [{"title": "<PERSON> (make-up artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(make-up_artist)"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American singer (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English businessman (b. 1988)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Estonian physicist (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ppma<PERSON>\"><PERSON><PERSON></a>, Estonian physicist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian physicist (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lipp<PERSON>a"}]}, {"year": "2015", "text": "<PERSON>, American historian and academic (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Czech figure skater (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vrz%C3%A1%C5%88ov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech figure skater (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vrz%C3%A1%C5%88ov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech figure skater (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alena_Vrz%C3%A1%C5%88ov%C3%A1"}]}, {"year": "2016", "text": "<PERSON>, American actress, singer, and dancer (b. 1925)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gloria_DeHaven"}]}, {"year": "2018", "text": "<PERSON>, American author, former government official and military officer (b. 1955)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, former government official and military officer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, former government official and military officer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Taiwanese politician, President (1988-2000), Vice President (1984-1988) and mayor of Taipei (1978-1981) (b. 1923)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hui\" title=\"<PERSON>h<PERSON>\"><PERSON></a>, Taiwanese politician, <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_the_Republic_of_China\" title=\"List of presidents of the Republic of China\">President</a> (1988-2000), <a href=\"https://wikipedia.org/wiki/List_of_vice_presidents_of_the_Republic_of_China\" title=\"List of vice presidents of the Republic of China\">Vice President</a> (1984-1988) and <a href=\"https://wikipedia.org/wiki/Mayor_of_Taipei\" title=\"Mayor of Taipei\">mayor of Taipei</a> (1978-1981) (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-h<PERSON>\" title=\"<PERSON>h<PERSON>\"><PERSON></a>, Taiwanese politician, <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_the_Republic_of_China\" title=\"List of presidents of the Republic of China\">President</a> (1988-2000), <a href=\"https://wikipedia.org/wiki/List_of_vice_presidents_of_the_Republic_of_China\" title=\"List of vice presidents of the Republic of China\">Vice President</a> (1984-1988) and <a href=\"https://wikipedia.org/wiki/Mayor_of_Taipei\" title=\"Mayor of Taipei\">mayor of Taipei</a> (1978-1981) (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hui"}, {"title": "List of presidents of the Republic of China", "link": "https://wikipedia.org/wiki/List_of_presidents_of_the_Republic_of_China"}, {"title": "List of vice presidents of the Republic of China", "link": "https://wikipedia.org/wiki/List_of_vice_presidents_of_the_Republic_of_China"}, {"title": "Mayor of Taipei", "link": "https://wikipedia.org/wiki/Mayor_of_Taipei"}]}, {"year": "2020", "text": "<PERSON>, American businessman and political activist (b. 1945) ", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and political activist (b. 1945) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and political activist (b. 1945) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Botswana-born, South African actor and executive producer (b. 1974)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Botswana-born, South African actor and executive producer (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Botswana-born, South African actor and executive producer (b. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American actress and comedian (b. 1927)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, American actress, singer and dancer (b. 1932)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, singer and dancer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, singer and dancer (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American actor and comedian (b. 1952)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Nigerian singer, actress and politician (b. 1952)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian singer, actress and politician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>wenu\"><PERSON><PERSON><PERSON></a>, Nigerian singer, actress and politician (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Onwenu"}]}]}}