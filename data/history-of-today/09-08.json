{"date": "September 8", "url": "https://wikipedia.org/wiki/September_8", "data": {"Events": [{"year": "70", "text": "After the capture of <PERSON><PERSON>'s Palace the previous day, a Roman army under <PERSON> secures and plunders the city of Jerusalem.", "html": "70 - After the capture of <a href=\"https://wikipedia.org/wiki/Hero<PERSON>%27s_Palace_(Jerusalem)\" title=\"<PERSON><PERSON>'s Palace (Jerusalem)\"><PERSON><PERSON>'s Palace</a> the previous day, a <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman</a> army under <a href=\"https://wikipedia.org/wiki/Titus\" title=\"<PERSON>\">Titus</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)\" class=\"mw-redirect\" title=\"Siege of Jerusalem (AD 70)\">secures and plunders</a> the city of <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "After the capture of <a href=\"https://wikipedia.org/wiki/Hero<PERSON>%27s_Palace_(Jerusalem)\" title=\"<PERSON><PERSON>'s Palace (Jerusalem)\"><PERSON><PERSON>'s Palace</a> the previous day, a <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman</a> army under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)\" class=\"mw-redirect\" title=\"Siege of Jerusalem (AD 70)\">secures and plunders</a> the city of <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "Hero<PERSON>'s Palace (Jerusalem)", "link": "https://wikipedia.org/wiki/Herod%27s_Palace_(Jerusalem)"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Titus"}, {"title": "Siege of Jerusalem (AD 70)", "link": "https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "617", "text": "Battle of Huoyi: <PERSON> defeats a Sui dynasty army, opening the path to his capture of the imperial capital Chang'an and the eventual establishment of the Tang dynasty.", "html": "617 - <a href=\"https://wikipedia.org/wiki/Battle_of_Huoyi\" title=\"Battle of Huoyi\">Battle of Huoyi</a>: <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON> of Tang\"><PERSON></a> defeats a <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui dynasty</a> army, opening the path to his capture of the imperial capital <a href=\"https://wikipedia.org/wiki/Chang%27an\" title=\"Chang'an\">Chang'an</a> and the eventual establishment of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Huoyi\" title=\"Battle of Huoyi\">Battle of Huoyi</a>: <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON> of Tang\"><PERSON></a> defeats a <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui dynasty</a> army, opening the path to his capture of the imperial capital <a href=\"https://wikipedia.org/wiki/Chang%27an\" title=\"Chang'an\">Chang'an</a> and the eventual establishment of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>.", "links": [{"title": "Battle of Huoyi", "link": "https://wikipedia.org/wiki/Battle_of_Huoyi"}, {"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_<PERSON>_Tang"}, {"title": "Sui dynasty", "link": "https://wikipedia.org/wiki/Sui_dynasty"}, {"title": "<PERSON>'<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>%27an"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "1100", "text": "Election of <PERSON><PERSON><PERSON>.", "html": "1100 - Election of <a href=\"https://wikipedia.org/wiki/Antipope_Theodoric\" title=\"Antipope Theodoric\">Antipop<PERSON></a>.", "no_year_html": "Election of <a href=\"https://wikipedia.org/wiki/Antipope_Theodoric\" title=\"Antipope Theodoric\">Antipop<PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antipop<PERSON>_<PERSON><PERSON>"}]}, {"year": "1198", "text": "<PERSON> of Swabia, Prince of Hohenstaufen, is crowned King of Germany (King of the Romans)", "html": "1198 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Swabia\" title=\"<PERSON> of Swabia\"><PERSON> of Swabia</a>, Prince of Hohenstaufen, is crowned King of Germany (King of the Romans)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Swabia\" title=\"<PERSON> of Swabia\"><PERSON> of Swabia</a>, Prince of Hohenstaufen, is crowned King of Germany (King of the Romans)", "links": [{"title": "Philip of Swabia", "link": "https://wikipedia.org/wiki/Philip_of_Swabia"}]}, {"year": "1253", "text": "Pope <PERSON> canonises <PERSON><PERSON><PERSON> of Szczepanów, killed by King <PERSON><PERSON><PERSON>.", "html": "1253 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_IV\" title=\"Pope Innocent IV\">Pope Innocent IV</a> canonises <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Szczepan%C3%B3w\" title=\"<PERSON><PERSON><PERSON> of Szczepanów\"><PERSON><PERSON><PERSON> of Szczepanów</a>, killed by King <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_II_the_Generous\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> II the Generous\"><PERSON><PERSON><PERSON> II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_IV\" title=\"Pope Innocent IV\">Pope Innocent IV</a> canonises <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Szczepan%C3%B3w\" title=\"<PERSON><PERSON><PERSON> of Szczepanów\"><PERSON><PERSON><PERSON> of Szczepanów</a>, killed by King <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_II_the_Generous\" class=\"mw-redirect\" title=\"Bolesław II the Generous\"><PERSON><PERSON><PERSON> II</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_IV"}, {"title": "<PERSON><PERSON><PERSON> of Szczepanów", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Szczepan%C3%B3w"}, {"title": "<PERSON><PERSON><PERSON> the Generous", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_II_the_Generous"}]}, {"year": "1264", "text": "The Statute of Kalisz, guaranteeing Jews safety and personal liberties and giving battei din jurisdiction over Jewish matters, is promulgated by <PERSON><PERSON><PERSON> the <PERSON>, Duke of Greater Poland.", "html": "1264 - The <a href=\"https://wikipedia.org/wiki/Statute_of_Kalisz\" title=\"Statute of Kalisz\">Statute of Kalisz</a>, guaranteeing Jews safety and personal liberties and giving <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Beth din\">battei din</a></i> jurisdiction over Jewish matters, is promulgated by <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_the_Pious\" title=\"<PERSON><PERSON><PERSON> the Pious\"><PERSON><PERSON><PERSON> the Pious</a>, Duke of Greater Poland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Statute_of_Kalisz\" title=\"Statute of Kalisz\">Statute of Kalisz</a>, guaranteeing Jews safety and personal liberties and giving <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Beth din\">battei din</a></i> jurisdiction over Jewish matters, is promulgated by <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_the_Pious\" title=\"<PERSON><PERSON><PERSON> the Pious\"><PERSON><PERSON><PERSON> the Pious</a>, Duke of Greater Poland.", "links": [{"title": "Statute of Kalisz", "link": "https://wikipedia.org/wiki/Statute_of_Kalisz"}, {"title": "<PERSON> din", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_the_<PERSON>ous"}]}, {"year": "1276", "text": "<PERSON> is elected <PERSON>.", "html": "1276 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John X<PERSON>\">Pope John <PERSON></a> is elected Pope.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XX<PERSON>\" title=\"Pope John XX<PERSON>\">Pope <PERSON></a> is elected Pope.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1331", "text": "<PERSON> declares himself king of Serbia.", "html": "1331 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1an\" title=\"<PERSON>\"><PERSON></a> declares himself king of Serbia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1an\" title=\"<PERSON>\"><PERSON></a> declares himself king of Serbia.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stefan_Du%C5%A1an"}]}, {"year": "1334", "text": "The Battle of Adramyttion begins in which a Christian naval league defeats a Turkish fleet in several encounters.", "html": "1334 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Adramyttion_(1334)\" title=\"Battle of Adramyttion (1334)\">Battle of Adramyttion</a> begins in which a <a href=\"https://wikipedia.org/wiki/Holy_League_(1332)\" title=\"Holy League (1332)\">Christian naval league</a> defeats a Turkish fleet in several encounters.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Adramyttion_(1334)\" title=\"Battle of Adramyttion (1334)\">Battle of Adramyttion</a> begins in which a <a href=\"https://wikipedia.org/wiki/Holy_League_(1332)\" title=\"Holy League (1332)\">Christian naval league</a> defeats a Turkish fleet in several encounters.", "links": [{"title": "Battle of Adramyttion (1334)", "link": "https://wikipedia.org/wiki/Battle_of_Adramyttion_(1334)"}, {"title": "Holy League (1332)", "link": "https://wikipedia.org/wiki/Holy_League_(1332)"}]}, {"year": "1380", "text": "Battle of Kulikovo: Russian forces defeat a mixed army of Tatars and Mongols, stopping their advance.", "html": "1380 - <a href=\"https://wikipedia.org/wiki/Battle_of_Kulikovo\" title=\"Battle of Kulikovo\">Battle of Kulikovo</a>: Russian forces defeat a mixed army of Tatars and Mongols, stopping their advance.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Kulikovo\" title=\"Battle of Kulikovo\">Battle of Kulikovo</a>: Russian forces defeat a mixed army of Tatars and Mongols, stopping their advance.", "links": [{"title": "Battle of Kulikovo", "link": "https://wikipedia.org/wiki/Battle_of_Kulikovo"}]}, {"year": "1504", "text": "<PERSON><PERSON>'s <PERSON> is unveiled in Piazza della Signoria in Florence.", "html": "1504 - <PERSON><PERSON>'s <a href=\"https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON>)\" title=\"<PERSON> (<PERSON><PERSON>)\"><i>David</i></a> is unveiled in <a href=\"https://wikipedia.org/wiki/Piazza_della_Signoria\" title=\"Piazza della Signoria\">Piazza della Signoria</a> in Florence.", "no_year_html": "<PERSON><PERSON>'s <a href=\"https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON>)\" title=\"<PERSON> (<PERSON><PERSON>)\"><i><PERSON></i></a> is unveiled in <a href=\"https://wikipedia.org/wiki/Piazza_della_Signoria\" title=\"Piazza della Signoria\">Piazza della Signoria</a> in Florence.", "links": [{"title": "<PERSON> (<PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON>)"}, {"title": "Piazza della Signoria", "link": "https://wikipedia.org/wiki/Pi<PERSON>_della_Signoria"}]}, {"year": "1514", "text": "Battle of Orsha: In one of the biggest battles of the century, Lithuanians and Poles defeat the Russian army.", "html": "1514 - <a href=\"https://wikipedia.org/wiki/Battle_of_Orsha\" title=\"Battle of Orsha\">Battle of Orsha</a>: In one of the biggest battles of the century, Lithuanians and Poles defeat the Russian army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Orsha\" title=\"Battle of Orsha\">Battle of Orsha</a>: In one of the biggest battles of the century, Lithuanians and Poles defeat the Russian army.", "links": [{"title": "Battle of Orsha", "link": "https://wikipedia.org/wiki/Battle_of_Orsha"}]}, {"year": "1522", "text": "Magellan-Elcano circumnavigation: Victoria arrives at Seville, completing the first circumnavigation.", "html": "1522 - <a href=\"https://wikipedia.org/wiki/Magellan%E2%80%93Elcano_circumnavigation\" class=\"mw-redirect\" title=\"Magellan-Elcano circumnavigation\">Magellan-Elcano circumnavigation</a>: <a href=\"https://wikipedia.org/wiki/Victoria_(ship)\" title=\"Victoria (ship)\"><i>Victoria</i></a> arrives at Seville, completing the first circumnavigation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magellan%E2%80%93Elcano_circumnavigation\" class=\"mw-redirect\" title=\"Magellan-Elcano circumnavigation\">Magellan-Elcano circumnavigation</a>: <a href=\"https://wikipedia.org/wiki/Victoria_(ship)\" title=\"Victoria (ship)\"><i>Victoria</i></a> arrives at Seville, completing the first circumnavigation.", "links": [{"title": "Magellan-Elcano circumnavigation", "link": "https://wikipedia.org/wiki/Magellan%E2%80%93Elcano_circumnavigation"}, {"title": "Victoria (ship)", "link": "https://wikipedia.org/wiki/Victoria_(ship)"}]}, {"year": "1565", "text": "St. Augustine, Florida is founded by Spanish admiral and Florida's first governor, <PERSON>.", "html": "1565 - <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine, Florida</a> is founded by Spanish admiral and Florida's first governor, <a href=\"https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine, Florida</a> is founded by Spanish admiral and Florida's first governor, <a href=\"https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "St. Augustine, Florida", "link": "https://wikipedia.org/wiki/St._Augustine,_Florida"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s"}]}, {"year": "1655", "text": "Warsaw falls without resistance to a small force under the command of <PERSON> of Sweden during The Deluge, making it the first time the city is captured by a foreign army.", "html": "1655 - <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a> falls without resistance to a small force under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of <PERSON>\"><PERSON> of Sweden</a> during <a href=\"https://wikipedia.org/wiki/Deluge_(history)\" title=\"Deluge (history)\">The Deluge</a>, making it the first time the city is captured by a foreign army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a> falls without resistance to a small force under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of <PERSON>\"><PERSON> of Sweden</a> during <a href=\"https://wikipedia.org/wiki/Deluge_(history)\" title=\"Deluge (history)\">The Deluge</a>, making it the first time the city is captured by a foreign army.", "links": [{"title": "Warsaw", "link": "https://wikipedia.org/wiki/Warsaw"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Sweden"}, {"title": "Deluge (history)", "link": "https://wikipedia.org/wiki/Deluge_(history)"}]}, {"year": "1727", "text": "A barn fire during a puppet show in the village of Burwell in Cambridgeshire, England kills 78 people, many of whom are children.", "html": "1727 - A barn fire during a puppet show in the village of <a href=\"https://wikipedia.org/wiki/Burwell,_Cambridgeshire\" title=\"Burwell, Cambridgeshire\">Burwell</a> in Cambridgeshire, England kills 78 people, many of whom are children.", "no_year_html": "A barn fire during a puppet show in the village of <a href=\"https://wikipedia.org/wiki/Burwell,_Cambridgeshire\" title=\"Burwell, Cambridgeshire\">Burwell</a> in Cambridgeshire, England kills 78 people, many of whom are children.", "links": [{"title": "Burwell, Cambridgeshire", "link": "https://wikipedia.org/wiki/Burwell,_Cambridgeshire"}]}, {"year": "1755", "text": "French and Indian War: Battle of Lake George.", "html": "1755 - <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Lake_George\" title=\"Battle of Lake George\">Battle of Lake George</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Lake_George\" title=\"Battle of Lake George\">Battle of Lake George</a>.", "links": [{"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}, {"title": "Battle of Lake George", "link": "https://wikipedia.org/wiki/Battle_of_Lake_George"}]}, {"year": "1756", "text": "French and Indian War: Kittanning Expedition.", "html": "1756 - French and Indian War: <a href=\"https://wikipedia.org/wiki/Kittanning_Expedition\" title=\"Kittanning Expedition\">Kittanning Expedition</a>.", "no_year_html": "French and Indian War: <a href=\"https://wikipedia.org/wiki/Kittanning_Expedition\" title=\"Kittanning Expedition\">Kittanning Expedition</a>.", "links": [{"title": "Kittanning Expedition", "link": "https://wikipedia.org/wiki/Kittanning_Expedition"}]}, {"year": "1760", "text": "French and Indian War: French surrender Montreal to the British, completing the latter's conquest of New France.", "html": "1760 - French and Indian War: <a href=\"https://wikipedia.org/wiki/Montreal_Campaign\" class=\"mw-redirect\" title=\"Montreal Campaign\">French surrender Montreal</a> to the British, completing the latter's <a href=\"https://wikipedia.org/wiki/Conquest_of_New_France_(1758%E2%80%931760)\" class=\"mw-redirect\" title=\"Conquest of New France (1758-1760)\">conquest of New France</a>.", "no_year_html": "French and Indian War: <a href=\"https://wikipedia.org/wiki/Montreal_Campaign\" class=\"mw-redirect\" title=\"Montreal Campaign\">French surrender Montreal</a> to the British, completing the latter's <a href=\"https://wikipedia.org/wiki/Conquest_of_New_France_(1758%E2%80%931760)\" class=\"mw-redirect\" title=\"Conquest of New France (1758-1760)\">conquest of New France</a>.", "links": [{"title": "Montreal Campaign", "link": "https://wikipedia.org/wiki/Montreal_Campaign"}, {"title": "Conquest of New France (1758-1760)", "link": "https://wikipedia.org/wiki/Conquest_of_New_France_(1758%E2%80%931760)"}]}, {"year": "1761", "text": "Marriage of King <PERSON> of the United Kingdom to Duchess <PERSON> of Mecklenburg-Strelitz.", "html": "1761 - Marriage of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> of the United Kingdom\"><PERSON> of the United Kingdom</a> to Duchess <a href=\"https://wikipedia.org/wiki/Charlotte_of_Mecklenburg-Strelitz\" title=\"<PERSON> of Mecklenburg-Strelitz\"><PERSON> of Mecklenburg-Strelitz</a>.", "no_year_html": "Marriage of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> of the United Kingdom\"><PERSON> of the United Kingdom</a> to Duchess <a href=\"https://wikipedia.org/wiki/Charlotte_of_Mecklenburg-Strelitz\" title=\"<PERSON> of Mecklenburg-Strelitz\"><PERSON> of Mecklenburg-Strelitz</a>.", "links": [{"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom"}, {"title": "<PERSON> of Mecklenburg-Strelitz", "link": "https://wikipedia.org/wiki/Charlotte_of_Mecklenburg-Strelitz"}]}, {"year": "1775", "text": "The unsuccessful Rising of the Priests in Malta.", "html": "1775 - The unsuccessful <a href=\"https://wikipedia.org/wiki/Rising_of_the_Priests\" title=\"Rising of the Priests\">Rising of the Priests</a> in Malta.", "no_year_html": "The unsuccessful <a href=\"https://wikipedia.org/wiki/Rising_of_the_Priests\" title=\"Rising of the Priests\">Rising of the Priests</a> in Malta.", "links": [{"title": "Rising of the Priests", "link": "https://wikipedia.org/wiki/Rising_of_the_Priests"}]}, {"year": "1781", "text": "American Revolutionary War: The Battle of Eutaw Springs in South Carolina, the war's last significant battle in the Southern theater, ends in a narrow British tactical victory.", "html": "1781 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Eutaw_Springs\" title=\"Battle of Eutaw Springs\">Battle of Eutaw Springs</a> in South Carolina, the war's last significant battle in the <a href=\"https://wikipedia.org/wiki/Southern_theater_of_the_American_Revolutionary_War\" title=\"Southern theater of the American Revolutionary War\">Southern theater</a>, ends in a narrow British tactical victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Eutaw_Springs\" title=\"Battle of Eutaw Springs\">Battle of Eutaw Springs</a> in South Carolina, the war's last significant battle in the <a href=\"https://wikipedia.org/wiki/Southern_theater_of_the_American_Revolutionary_War\" title=\"Southern theater of the American Revolutionary War\">Southern theater</a>, ends in a narrow British tactical victory.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Eutaw Springs", "link": "https://wikipedia.org/wiki/Battle_of_Eutaw_Springs"}, {"title": "Southern theater of the American Revolutionary War", "link": "https://wikipedia.org/wiki/Southern_theater_of_the_American_Revolutionary_War"}]}, {"year": "1793", "text": "French Revolutionary Wars: Battle of Hondschoote.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Hondschoote_(1793)\" class=\"mw-redirect\" title=\"Battle of Hondschoote (1793)\">Battle of Hondschoote</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Hondschoote_(1793)\" class=\"mw-redirect\" title=\"Battle of Hondschoote (1793)\">Battle of Hondschoote</a>.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "Battle of Hondschoote (1793)", "link": "https://wikipedia.org/wiki/Battle_of_Hondschoote_(1793)"}]}, {"year": "1796", "text": "French Revolutionary Wars: Battle of Bassano: French forces defeat Austrian troops at Bassano del Grappa.", "html": "1796 - French Revolutionary Wars: <a href=\"https://wikipedia.org/wiki/Battle_of_Bassano\" title=\"Battle of Bassano\">Battle of Bassano</a>: French forces defeat Austrian troops at Bassano del Grappa.", "no_year_html": "French Revolutionary Wars: <a href=\"https://wikipedia.org/wiki/Battle_of_Bassano\" title=\"Battle of Bassano\">Battle of Bassano</a>: French forces defeat Austrian troops at Bassano del Grappa.", "links": [{"title": "Battle of Bassano", "link": "https://wikipedia.org/wiki/Battle_of_Bassano"}]}, {"year": "1808", "text": "The Treaty of Paris is signed ending the French military occupation of Prussia.", "html": "1808 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1808)\" title=\"Treaty of Paris (1808)\">Treaty of Paris</a> is signed ending the French military occupation of <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1808)\" title=\"Treaty of Paris (1808)\">Treaty of Paris</a> is signed ending the French military occupation of <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>.", "links": [{"title": "Treaty of Paris (1808)", "link": "https://wikipedia.org/wiki/Treaty_of_Paris_(1808)"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}]}, {"year": "1810", "text": "The Tonquin sets sail from New York Harbor with 33 employees of <PERSON>'s newly created Pacific Fur Company on board.", "html": "1810 - The <i><a href=\"https://wikipedia.org/wiki/Tonquin_(1807)\" class=\"mw-redirect\" title=\"Tonquin (1807)\">Ton<PERSON></a></i> sets sail from New York Harbor with 33 employees of <PERSON>'s newly created <a href=\"https://wikipedia.org/wiki/Pacific_Fur_Company\" title=\"Pacific Fur Company\">Pacific Fur Company</a> on board.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Ton<PERSON>_(1807)\" class=\"mw-redirect\" title=\"Tonquin (1807)\">Ton<PERSON></a></i> sets sail from New York Harbor with 33 employees of <PERSON>'s newly created <a href=\"https://wikipedia.org/wiki/Pacific_Fur_Company\" title=\"Pacific Fur Company\">Pacific Fur Company</a> on board.", "links": [{"title": "<PERSON><PERSON><PERSON> (1807)", "link": "https://wikipedia.org/wiki/Ton<PERSON>_(1807)"}, {"title": "Pacific Fur Company", "link": "https://wikipedia.org/wiki/Pacific_Fur_Company"}]}, {"year": "1813", "text": "At the final stage of the Peninsular War, British-Portuguese troops capture the town of Donostia (now San Sebastián), resulting in a rampage and eventual destruction of the town.", "html": "1813 - At the final stage of the <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>, British-Portuguese troops capture the town of Donostia (now <a href=\"https://wikipedia.org/wiki/San_Sebasti%C3%A1n\" title=\"San Sebastián\">San Sebastián</a>), resulting in a <a href=\"https://wikipedia.org/wiki/Siege_of_San_Sebasti%C3%A1n\" title=\"Siege of San Sebastián\">rampage and eventual destruction</a> of the town.", "no_year_html": "At the final stage of the <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>, British-Portuguese troops capture the town of Donostia (now <a href=\"https://wikipedia.org/wiki/San_Sebasti%C3%A1n\" title=\"San Sebastián\">San Sebastián</a>), resulting in a <a href=\"https://wikipedia.org/wiki/Siege_of_San_Sebasti%C3%A1n\" title=\"Siege of San Sebastián\">rampage and eventual destruction</a> of the town.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "San Sebastián", "link": "https://wikipedia.org/wiki/San_Sebasti%C3%A1n"}, {"title": "Siege of San Sebastián", "link": "https://wikipedia.org/wiki/Siege_of_San_Sebasti%C3%A1n"}]}, {"year": "1819", "text": "1819 Balloon riot occurred at Vauxhall Garden in Philadelphia, PA and resulted in the destruction of the amusement park.", "html": "1819 - <a href=\"https://wikipedia.org/wiki/1819_Balloon_riot\" title=\"1819 Balloon riot\">1819 Balloon riot</a> occurred at Vauxhall Garden in Philadelphia, PA and resulted in the destruction of the amusement park.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1819_Balloon_riot\" title=\"1819 Balloon riot\">1819 Balloon riot</a> occurred at Vauxhall Garden in Philadelphia, PA and resulted in the destruction of the amusement park.", "links": [{"title": "1819 Balloon riot", "link": "https://wikipedia.org/wiki/1819_Balloon_riot"}]}, {"year": "1831", "text": "<PERSON> and <PERSON> of Saxe-Meiningen are crowned King and Queen of the United Kingdom of Great Britain and Ireland.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"William IV of the United Kingdom\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Adelaide_of_Saxe-Meiningen\" title=\"Adelaide of Saxe-Meiningen\"><PERSON> of Saxe-Meiningen</a> are crowned King and Queen of the United Kingdom of Great Britain and Ireland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"William IV of the United Kingdom\"><PERSON> IV</a> and <a href=\"https://wikipedia.org/wiki/Adelaide_of_Saxe-Meiningen\" title=\"Adelaide of Saxe-Meiningen\"><PERSON> of Saxe-Meiningen</a> are crowned King and Queen of the United Kingdom of Great Britain and Ireland.", "links": [{"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom"}, {"title": "Adelaide of Saxe-Meiningen", "link": "https://wikipedia.org/wiki/Adelaide_of_Saxe-Meiningen"}]}, {"year": "1831", "text": "November uprising: The Battle of Warsaw effectively ends the Polish insurrection.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/November_uprising\" class=\"mw-redirect\" title=\"November uprising\">November uprising</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1831)\" title=\"Battle of Warsaw (1831)\">Battle of Warsaw</a> effectively ends the Polish insurrection.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/November_uprising\" class=\"mw-redirect\" title=\"November uprising\">November uprising</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1831)\" title=\"Battle of Warsaw (1831)\">Battle of Warsaw</a> effectively ends the Polish insurrection.", "links": [{"title": "November uprising", "link": "https://wikipedia.org/wiki/November_uprising"}, {"title": "Battle of Warsaw (1831)", "link": "https://wikipedia.org/wiki/Battle_of_Warsaw_(1831)"}]}, {"year": "1855", "text": "Crimean War: The French assault the tower of Malakoff, leading to the capture of Sevastopol.", "html": "1855 - <a href=\"https://wikipedia.org/wiki/Crimean_War\" title=\"Crimean War\">Crimean War</a>: The French <a href=\"https://wikipedia.org/wiki/Battle_of_Malakoff\" title=\"Battle of Malakoff\">assault the tower of Malakoff</a>, leading to the capture of <a href=\"https://wikipedia.org/wiki/Sevastopol\" title=\"Sevastopol\">Sevastopol</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crimean_War\" title=\"Crimean War\">Crimean War</a>: The French <a href=\"https://wikipedia.org/wiki/Battle_of_Malakoff\" title=\"Battle of Malakoff\">assault the tower of Malakoff</a>, leading to the capture of <a href=\"https://wikipedia.org/wiki/Sevastopol\" title=\"Sevastopol\">Sevastopol</a>.", "links": [{"title": "Crimean War", "link": "https://wikipedia.org/wiki/Crimean_War"}, {"title": "Battle of Malakoff", "link": "https://wikipedia.org/wiki/Battle_of_Malakoff"}, {"title": "Sevastopol", "link": "https://wikipedia.org/wiki/Sevastopol"}]}, {"year": "1860", "text": "The steamship PS Lady Elgin sinks on Lake Michigan, with the loss of around 300 lives.", "html": "1860 - The <a href=\"https://wikipedia.org/wiki/Steamship\" title=\"Steamship\">steamship</a> <a href=\"https://wikipedia.org/wiki/PS_Lady_Elgin\" title=\"PS Lady Elgin\">PS <i><PERSON></i></a> sinks on Lake Michigan, with the loss of around 300 lives.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Steamship\" title=\"Steamship\">steamship</a> <a href=\"https://wikipedia.org/wiki/PS_Lady_Elgin\" title=\"PS <PERSON> Elgin\">PS <i><PERSON></i></a> sinks on Lake Michigan, with the loss of around 300 lives.", "links": [{"title": "Steamship", "link": "https://wikipedia.org/wiki/Steamship"}, {"title": "PS Lady Elgin", "link": "https://wikipedia.org/wiki/<PERSON>_Lady_<PERSON>"}]}, {"year": "1862", "text": "Millennium of Russia monument is unveiled in Novgorod.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Millennium_of_Russia\" title=\"Millennium of Russia\">Millennium of Russia</a> monument is unveiled in Novgorod.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Millennium_of_Russia\" title=\"Millennium of Russia\">Millennium of Russia</a> monument is unveiled in Novgorod.", "links": [{"title": "Millennium of Russia", "link": "https://wikipedia.org/wiki/Millennium_of_Russia"}]}, {"year": "1863", "text": "American Civil War: In the Second Battle of Sabine Pass, a small Confederate force thwarts a Union invasion of Texas.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: In the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Sabine_Pass\" title=\"Second Battle of Sabine Pass\">Second Battle of Sabine Pass</a>, a small Confederate force thwarts a Union invasion of Texas.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: In the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Sabine_Pass\" title=\"Second Battle of Sabine Pass\">Second Battle of Sabine Pass</a>, a small Confederate force thwarts a Union invasion of Texas.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Second Battle of Sabine Pass", "link": "https://wikipedia.org/wiki/Second_Battle_of_Sabine_Pass"}]}, {"year": "1883", "text": "The Northern Pacific Railway (reporting mark NP) was completed in a ceremony at Gold Creek, Montana. Former president <PERSON> drove in the final \"golden spike\" in an event attended by rail and political luminaries.", "html": "1883 - The <a href=\"https://wikipedia.org/wiki/Northern_Pacific_Railway\" title=\"Northern Pacific Railway\">Northern Pacific Railway</a> (reporting mark NP) was completed in a ceremony at Gold Creek, Montana. Former president <PERSON> drove in the final \"golden spike\" in an event attended by rail and political luminaries.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Northern_Pacific_Railway\" title=\"Northern Pacific Railway\">Northern Pacific Railway</a> (reporting mark NP) was completed in a ceremony at Gold Creek, Montana. Former president <PERSON> drove in the final \"golden spike\" in an event attended by rail and political luminaries.", "links": [{"title": "Northern Pacific Railway", "link": "https://wikipedia.org/wiki/Northern_Pacific_Railway"}]}, {"year": "1888", "text": "<PERSON>'s submarine is first tested.", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Peral_Submarine\" class=\"mw-redirect\" title=\"Peral Submarine\"><PERSON>'s submarine</a> is first tested.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peral_Submarine\" class=\"mw-redirect\" title=\"Peral Submarine\"><PERSON>'s submarine</a> is first tested.", "links": [{"title": "Peral Submarine", "link": "https://wikipedia.org/wiki/Peral_Submarine"}]}, {"year": "1888", "text": "The Great Herding (Spanish: El Gran Arreo) begins with thousands of sheep being herded from the Argentine outpost of Fortín Conesa to Santa Cruz near the Strait of Magellan.", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Patagonian_sheep_farming_boom\" title=\"Patagonian sheep farming boom\">The Great Herding</a> (<a href=\"https://wikipedia.org/wiki/Spanish_language\" title=\"Spanish language\">Spanish</a>: <i lang=\"es\">El Gran Arreo</i>) begins with thousands of sheep being herded from the Argentine outpost of <a href=\"https://wikipedia.org/wiki/General_<PERSON>,_R%C3%ADo_Negro\" title=\"General <PERSON>esa, Río Negro\">Fortín Conesa</a> to <a href=\"https://wikipedia.org/wiki/Santa_Cruz_Province,_Argentina\" title=\"Santa Cruz Province, Argentina\">Santa Cruz</a> near the <a href=\"https://wikipedia.org/wiki/Strait_of_Magellan\" title=\"Strait of Magellan\">Strait of Magellan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patagonian_sheep_farming_boom\" title=\"Patagonian sheep farming boom\">The Great Herding</a> (<a href=\"https://wikipedia.org/wiki/Spanish_language\" title=\"Spanish language\">Spanish</a>: <i lang=\"es\">El Gran Arreo</i>) begins with thousands of sheep being herded from the Argentine outpost of <a href=\"https://wikipedia.org/wiki/General_<PERSON>,_R%C3%ADo_Negro\" title=\"General <PERSON>, Río Negro\">Fortín Conesa</a> to <a href=\"https://wikipedia.org/wiki/Santa_Cruz_Province,_Argentina\" title=\"Santa Cruz Province, Argentina\">Santa Cruz</a> near the <a href=\"https://wikipedia.org/wiki/Strait_of_Magellan\" title=\"Strait of Magellan\">Strait of Magellan</a>.", "links": [{"title": "Patagonian sheep farming boom", "link": "https://wikipedia.org/wiki/Patagonian_sheep_farming_boom"}, {"title": "Spanish language", "link": "https://wikipedia.org/wiki/Spanish_language"}, {"title": "General <PERSON>, Río Negro", "link": "https://wikipedia.org/wiki/General_<PERSON><PERSON><PERSON>,_R%C3%ADo_Negro"}, {"title": "Santa Cruz Province, Argentina", "link": "https://wikipedia.org/wiki/Santa_Cruz_Province,_Argentina"}, {"title": "Strait of Magellan", "link": "https://wikipedia.org/wiki/Strait_of_Magellan"}]}, {"year": "1888", "text": "In London, the body of <PERSON>'s second murder victim, <PERSON>, is found.", "html": "1888 - In London, the body of <PERSON>ipper's second murder victim, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is found.", "no_year_html": "In London, the body of <PERSON>per's second murder victim, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is found.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "In England, the first six Football League matches are played.", "html": "1888 - In England, the first six <a href=\"https://wikipedia.org/wiki/English_Football_League\" title=\"English Football League\">Football League</a> matches are played.", "no_year_html": "In England, the first six <a href=\"https://wikipedia.org/wiki/English_Football_League\" title=\"English Football League\">Football League</a> matches are played.", "links": [{"title": "English Football League", "link": "https://wikipedia.org/wiki/English_Football_League"}]}, {"year": "1892", "text": "The Pledge of Allegiance is first recited.", "html": "1892 - The <a href=\"https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)\" class=\"mw-redirect\" title=\"Pledge of Allegiance (United States)\">Pledge of Allegiance</a> is first recited.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)\" class=\"mw-redirect\" title=\"Pledge of Allegiance (United States)\">Pledge of Allegiance</a> is first recited.", "links": [{"title": "Pledge of Allegiance (United States)", "link": "https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)"}]}, {"year": "1898", "text": "Seven hundred Greek civilians, 17 British guards and the British Consul of Crete are killed by a Turkish mob.", "html": "1898 - Seven hundred Greek civilians, 17 British guards and the British Consul of Crete are <a href=\"https://wikipedia.org/wiki/Candia_massacre\" title=\"Candia massacre\">killed</a> by a Turkish mob.", "no_year_html": "Seven hundred Greek civilians, 17 British guards and the British Consul of Crete are <a href=\"https://wikipedia.org/wiki/Candia_massacre\" title=\"Candia massacre\">killed</a> by a Turkish mob.", "links": [{"title": "Candia massacre", "link": "https://wikipedia.org/wiki/Candia_massacre"}]}, {"year": "1900", "text": "Galveston hurricane: A powerful hurricane hits Galveston, Texas killing about 8,000 people.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/1900_Galveston_hurricane\" title=\"1900 Galveston hurricane\">Galveston hurricane</a>: A powerful hurricane hits Galveston, Texas killing about 8,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1900_Galveston_hurricane\" title=\"1900 Galveston hurricane\">Galveston hurricane</a>: A powerful hurricane hits Galveston, Texas killing about 8,000 people.", "links": [{"title": "1900 Galveston hurricane", "link": "https://wikipedia.org/wiki/1900_Galveston_hurricane"}]}, {"year": "1905", "text": "The 7.2 Mw  Calabria earthquake shakes southern Italy with a maximum Mercalli intensity of XI (Extreme), killing between 557 and 2,500 people.", "html": "1905 - The 7.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1905_Calabria_earthquake\" title=\"1905 Calabria earthquake\">Calabria earthquake</a> shakes southern Italy with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>), killing between 557 and 2,500 people.", "no_year_html": "The 7.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1905_Calabria_earthquake\" title=\"1905 Calabria earthquake\">Calabria earthquake</a> shakes southern Italy with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>), killing between 557 and 2,500 people.", "links": [{"title": "1905 Calabria earthquake", "link": "https://wikipedia.org/wiki/1905_Calabria_earthquake"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1914", "text": "World War I: Private <PERSON> becomes the first British soldier to be executed for desertion during the war.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Private <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first British soldier to be executed for desertion during the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Private <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first British soldier to be executed for desertion during the war.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "In a bid to prove that women were capable of serving as military dispatch riders, <PERSON> and <PERSON><PERSON><PERSON> arrive in Los Angeles, completing a 60-day, 5,500 mile cross-country trip on motorcycles.", "html": "1916 - In a bid to prove that women were capable of serving as military dispatch riders, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_sisters\" title=\"<PERSON> sisters\"><PERSON> and <PERSON><PERSON><PERSON></a> arrive in Los Angeles, completing a 60-day, 5,500 mile cross-country trip on motorcycles.", "no_year_html": "In a bid to prove that women were capable of serving as military dispatch riders, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_sisters\" title=\"<PERSON> sisters\"><PERSON> and <PERSON><PERSON><PERSON></a> arrive in Los Angeles, completing a 60-day, 5,500 mile cross-country trip on motorcycles.", "links": [{"title": "<PERSON> sisters", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, a 16-year-old, wins the Atlantic City Pageant's Golden Mermaid trophy; pageant officials later dubbed her the first Miss America.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a 16-year-old, wins the Atlantic City Pageant's Golden Mermaid trophy; pageant officials later dubbed her the first <a href=\"https://wikipedia.org/wiki/Miss_America\" title=\"Miss America\">Miss America</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a 16-year-old, wins the Atlantic City Pageant's Golden Mermaid trophy; pageant officials later dubbed her the first <a href=\"https://wikipedia.org/wiki/Miss_America\" title=\"Miss America\">Miss America</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss America", "link": "https://wikipedia.org/wiki/Miss_America"}]}, {"year": "1923", "text": "Honda Point disaster: Nine US Navy destroyers run aground off the California coast. Seven are lost, and twenty-three sailors killed.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Honda_Point_disaster\" title=\"Honda Point disaster\">Honda Point disaster</a>: Nine US Navy destroyers run aground off the California coast. Seven are lost, and twenty-three sailors killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honda_Point_disaster\" title=\"Honda Point disaster\">Honda Point disaster</a>: Nine US Navy destroyers run aground off the California coast. Seven are lost, and twenty-three sailors killed.", "links": [{"title": "Honda Point disaster", "link": "https://wikipedia.org/wiki/Honda_Point_disaster"}]}, {"year": "1925", "text": "Rif War: Spanish forces including troops from the Foreign Legion under Colonel <PERSON> landing at Al Hoceima, Morocco.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Rif_War\" title=\"Rif War\">Rif War</a>: Spanish forces including troops from the <a href=\"https://wikipedia.org/wiki/Spanish_Legion\" title=\"Spanish Legion\">Foreign Legion</a> under Colonel <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a> landing at <a href=\"https://wikipedia.org/wiki/Al_Hoceima\" title=\"Al Hoceima\">Al Hoceima, Morocco</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rif_War\" title=\"Rif War\">Rif War</a>: Spanish forces including troops from the <a href=\"https://wikipedia.org/wiki/Spanish_Legion\" title=\"Spanish Legion\">Foreign Legion</a> under Colonel <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a> landing at <a href=\"https://wikipedia.org/wiki/Al_Hoceima\" title=\"Al Hoceima\">Al Hoceima, Morocco</a>.", "links": [{"title": "Rif War", "link": "https://wikipedia.org/wiki/Rif_War"}, {"title": "Spanish Legion", "link": "https://wikipedia.org/wiki/Spanish_Legion"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}, {"title": "Al Hoceima", "link": "https://wikipedia.org/wiki/Al_Hoceima"}]}, {"year": "1926", "text": "Germany is admitted to the League of Nations.", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Germany</a> is admitted to the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Germany</a> is admitted to the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "links": [{"title": "Weimar Republic", "link": "https://wikipedia.org/wiki/Weimar_Republic"}, {"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> became King of Iraq.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Ghazi_of_Iraq\" title=\"Gha<PERSON> of Iraq\"><PERSON><PERSON><PERSON> <PERSON></a> became King of Iraq.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ghazi_of_Iraq\" title=\"Gha<PERSON> of Iraq\"><PERSON><PERSON><PERSON> <PERSON></a> became King of Iraq.", "links": [{"title": "<PERSON><PERSON><PERSON> of Iraq", "link": "https://wikipedia.org/wiki/Ghazi_of_Iraq"}]}, {"year": "1934", "text": "Off the New Jersey coast, a fire aboard the passenger liner SS Morro Castle kills 137 people.", "html": "1934 - Off the New Jersey coast, a fire aboard the passenger liner <a href=\"https://wikipedia.org/wiki/SS_Morro_Castle_(1930)\" title=\"SS Morro Castle (1930)\">SS <i>Morro Castle</i></a> kills 137 people.", "no_year_html": "Off the New Jersey coast, a fire aboard the passenger liner <a href=\"https://wikipedia.org/wiki/SS_Morro_Castle_(1930)\" title=\"SS Morro Castle (1930)\">SS <i>Morro Castle</i></a> kills 137 people.", "links": [{"title": "SS Morro Castle (1930)", "link": "https://wikipedia.org/wiki/SS_Morro_Castle_(1930)"}]}, {"year": "1935", "text": "US Senator from Louisiana <PERSON><PERSON> is fatally shot in the Louisiana State Capitol building.", "html": "1935 - US Senator from Louisiana <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is fatally shot in the Louisiana State Capitol building.", "no_year_html": "US Senator from Louisiana <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is fatally shot in the Louisiana State Capitol building.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "World War II: German forces begin the Siege of Leningrad.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German forces begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Leningrad\" title=\"Siege of Leningrad\">Siege of Leningrad</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German forces begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Leningrad\" title=\"Siege of Leningrad\">Siege of Leningrad</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Siege of Leningrad", "link": "https://wikipedia.org/wiki/Siege_of_Leningrad"}]}, {"year": "1943", "text": "World War II: The Armistice of Cassibile is proclaimed by radio. OB Süd immediately implements plans to disarm the Italian forces.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/Armistice_of_Cassibile\" title=\"Armistice of Cassibile\">Armistice of Cassibile</a> is proclaimed by radio. <i><a href=\"https://wikipedia.org/wiki/OB_S%C3%BCd\" class=\"mw-redirect\" title=\"OB Süd\">OB Süd</a></i> immediately implements plans to <a href=\"https://wikipedia.org/wiki/Operation_Achse\" title=\"Operation Achse\">disarm the Italian forces</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Armistice_of_Cassibile\" title=\"Armistice of Cassibile\">Armistice of Cassibile</a> is proclaimed by radio. <i><a href=\"https://wikipedia.org/wiki/OB_S%C3%BCd\" class=\"mw-redirect\" title=\"OB Süd\">OB Süd</a></i> immediately implements plans to <a href=\"https://wikipedia.org/wiki/Operation_Achse\" title=\"Operation Achse\">disarm the Italian forces</a>.", "links": [{"title": "Armistice of Cassibile", "link": "https://wikipedia.org/wiki/Armistice_of_Cassibile"}, {"title": "OB Süd", "link": "https://wikipedia.org/wiki/OB_S%C3%BCd"}, {"title": "Operation Achse", "link": "https://wikipedia.org/wiki/Operation_Achse"}]}, {"year": "1944", "text": "World War II: London is hit by a V-2 rocket for the first time.", "html": "1944 - World War II: London is hit by a <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> for the first time.", "no_year_html": "World War II: London is hit by a <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> for the first time.", "links": [{"title": "V-2 rocket", "link": "https://wikipedia.org/wiki/V-2_rocket"}]}, {"year": "1945", "text": "The division of Korea begins when United States troops arrive to partition the southern part of Korea in response to Soviet troops occupying the northern part of the peninsula a month earlier.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Division_of_Korea\" title=\"Division of Korea\">division of Korea</a> begins when United States troops arrive to partition the southern part of Korea in response to Soviet troops occupying the northern part of the peninsula a month earlier.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Division_of_Korea\" title=\"Division of Korea\">division of Korea</a> begins when United States troops arrive to partition the southern part of Korea in response to Soviet troops occupying the northern part of the peninsula a month earlier.", "links": [{"title": "Division of Korea", "link": "https://wikipedia.org/wiki/Division_of_Korea"}]}, {"year": "1946", "text": "A referendum abolishes the monarchy in Bulgaria.", "html": "1946 - A referendum <a href=\"https://wikipedia.org/wiki/Bulgarian_republic_referendum,_1946\" class=\"mw-redirect\" title=\"Bulgarian republic referendum, 1946\">abolishes the monarchy</a> in Bulgaria.", "no_year_html": "A referendum <a href=\"https://wikipedia.org/wiki/Bulgarian_republic_referendum,_1946\" class=\"mw-redirect\" title=\"Bulgarian republic referendum, 1946\">abolishes the monarchy</a> in Bulgaria.", "links": [{"title": "Bulgarian republic referendum, 1946", "link": "https://wikipedia.org/wiki/Bulgarian_republic_referendum,_1946"}]}, {"year": "1952", "text": "The Canadian Broadcasting Corporation makes its first televised broadcast on the second escape of the Boyd Gang.", "html": "1952 - The <a href=\"https://wikipedia.org/wiki/Canadian_Broadcasting_Corporation\" title=\"Canadian Broadcasting Corporation\">Canadian Broadcasting Corporation</a> makes its first televised broadcast on the second escape of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gang\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Canadian_Broadcasting_Corporation\" title=\"Canadian Broadcasting Corporation\">Canadian Broadcasting Corporation</a> makes its first televised broadcast on the second escape of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Canadian Broadcasting Corporation", "link": "https://wikipedia.org/wiki/Canadian_Broadcasting_Corporation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "The Southeast Asia Treaty Organization (SEATO) is established.", "html": "1954 - The <a href=\"https://wikipedia.org/wiki/Southeast_Asia_Treaty_Organization\" title=\"Southeast Asia Treaty Organization\">Southeast Asia Treaty Organization</a> (SEATO) is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Southeast_Asia_Treaty_Organization\" title=\"Southeast Asia Treaty Organization\">Southeast Asia Treaty Organization</a> (SEATO) is established.", "links": [{"title": "Southeast Asia Treaty Organization", "link": "https://wikipedia.org/wiki/Southeast_Asia_Treaty_Organization"}]}, {"year": "1960", "text": "In Huntsville, Alabama, US President <PERSON> formally dedicates the Marshall Space Flight Center (NASA had already activated the facility on July 1).", "html": "1960 - In Huntsville, Alabama, US President <PERSON> formally dedicates the <a href=\"https://wikipedia.org/wiki/Marshall_Space_Flight_Center\" title=\"Marshall Space Flight Center\">Marshall Space Flight Center</a> (<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> had already activated the facility on July 1).", "no_year_html": "In Huntsville, Alabama, US President <PERSON> formally dedicates the <a href=\"https://wikipedia.org/wiki/Marshall_Space_Flight_Center\" title=\"Marshall Space Flight Center\">Marshall Space Flight Center</a> (<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> had already activated the facility on July 1).", "links": [{"title": "Marshall Space Flight Center", "link": "https://wikipedia.org/wiki/Marshall_Space_Flight_Center"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}]}, {"year": "1962", "text": "Last run of the famous Pines Express over the Somerset and Dorset Railway line (UK) fittingly using the last steam locomotive built by British Railways, BR Standard Class 9F 92220 Evening Star.", "html": "1962 - Last run of the famous <i><a href=\"https://wikipedia.org/wiki/Pines_Express\" title=\"Pines Express\">Pines Express</a></i> over the Somerset and Dorset Railway line (UK) fittingly using the last steam locomotive built by British Railways, <a href=\"https://wikipedia.org/wiki/BR_Standard_Class_9F_92220_Evening_Star\" title=\"BR Standard Class 9F 92220 Evening Star\">BR Standard Class 9F 92220 <i>Evening Star</i></a>.", "no_year_html": "Last run of the famous <i><a href=\"https://wikipedia.org/wiki/Pines_Express\" title=\"Pines Express\">Pines Express</a></i> over the Somerset and Dorset Railway line (UK) fittingly using the last steam locomotive built by British Railways, <a href=\"https://wikipedia.org/wiki/BR_Standard_Class_9F_92220_Evening_Star\" title=\"BR Standard Class 9F 92220 Evening Star\">BR Standard Class 9F 92220 <i>Evening Star</i></a>.", "links": [{"title": "Pines Express", "link": "https://wikipedia.org/wiki/Pines_Express"}, {"title": "BR Standard Class 9F 92220 Evening Star", "link": "https://wikipedia.org/wiki/BR_Standard_Class_9F_92220_Evening_Star"}]}, {"year": "1966", "text": "The landmark American science fiction television series Star Trek premieres with its first-aired episode, \"The Man Trap\".", "html": "1966 - The landmark American science fiction television series <a href=\"https://wikipedia.org/wiki/Star_Trek:_The_Original_Series\" title=\"Star Trek: The Original Series\"><i>Star Trek</i></a> premieres with its first-aired episode, \"<a href=\"https://wikipedia.org/wiki/The_Man_Trap\" title=\"The Man Trap\">The Man Trap</a>\".", "no_year_html": "The landmark American science fiction television series <a href=\"https://wikipedia.org/wiki/Star_Trek:_The_Original_Series\" title=\"Star Trek: The Original Series\"><i>Star Trek</i></a> premieres with its first-aired episode, \"<a href=\"https://wikipedia.org/wiki/The_Man_Trap\" title=\"The Man Trap\">The Man Trap</a>\".", "links": [{"title": "Star Trek: The Original Series", "link": "https://wikipedia.org/wiki/Star_Trek:_The_Original_Series"}, {"title": "The Man Trap", "link": "https://wikipedia.org/wiki/The_Man_Trap"}]}, {"year": "1970", "text": "Trans International Airlines Flight 863 crashes during takeoff from John F. Kennedy International Airport in New York City, killing all 11 aboard.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Trans_International_Airlines_Flight_863\" title=\"Trans International Airlines Flight 863\">Trans International Airlines Flight 863</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/John_<PERSON>._Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">John F. Kennedy International Airport</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>, killing all 11 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trans_International_Airlines_Flight_863\" title=\"Trans International Airlines Flight 863\">Trans International Airlines Flight 863</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/John_<PERSON>._Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">John F. Kennedy International Airport</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>, killing all 11 aboard.", "links": [{"title": "Trans International Airlines Flight 863", "link": "https://wikipedia.org/wiki/Trans_International_Airlines_Flight_863"}, {"title": "<PERSON> International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Kennedy_International_Airport"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1971", "text": "In Washington, D.C., the John <PERSON> Kennedy Center for the Performing Arts is inaugurated, with the opening feature being the premiere of <PERSON>'s Mass.", "html": "1971 - In Washington, D.C., the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Center_for_the_Performing_Arts\" title=\"John <PERSON> Center for the Performing Arts\">John <PERSON> Center for the Performing Arts</a> is inaugurated, with the opening feature being the premiere of <PERSON>'s <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(<PERSON>)\" title=\"Mass (<PERSON>)\">Mass</a></i>.", "no_year_html": "In Washington, D.C., the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Center_for_the_Performing_Arts\" title=\"John <PERSON> Kennedy Center for the Performing Arts\">John <PERSON> Center for the Performing Arts</a> is inaugurated, with the opening feature being the premiere of <PERSON>'s <i><a href=\"https://wikipedia.org/wiki/Mass_(<PERSON>)\" title=\"Mass (<PERSON>)\">Mass</a></i>.", "links": [{"title": "<PERSON> Center for the Performing Arts", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_Center_for_the_Performing_Arts"}, {"title": "Mass (Bernstein)", "link": "https://wikipedia.org/wiki/Mass_(<PERSON>)"}]}, {"year": "1973", "text": "World Airways Flight 802 crashes into Mount Dutton in King Cove, Alaska, killing six people.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/World_Airways_Flight_802\" title=\"World Airways Flight 802\">World Airways Flight 802</a> crashes into <a href=\"https://wikipedia.org/wiki/Mount_Dutton\" title=\"Mount Dutton\">Mount Dutton</a> in <a href=\"https://wikipedia.org/wiki/King_Cove,_Alaska\" title=\"King Cove, Alaska\">King Cove, Alaska</a>, killing six people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_Airways_Flight_802\" title=\"World Airways Flight 802\">World Airways Flight 802</a> crashes into <a href=\"https://wikipedia.org/wiki/Mount_Dutton\" title=\"Mount Dutton\">Mount Dutton</a> in <a href=\"https://wikipedia.org/wiki/King_Cove,_Alaska\" title=\"King Cove, Alaska\">King Cove, Alaska</a>, killing six people.", "links": [{"title": "World Airways Flight 802", "link": "https://wikipedia.org/wiki/World_Airways_Flight_802"}, {"title": "Mount Dutton", "link": "https://wikipedia.org/wiki/Mount_Dutton"}, {"title": "King Cove, Alaska", "link": "https://wikipedia.org/wiki/King_Cove,_Alaska"}]}, {"year": "1974", "text": "Watergate scandal: US President <PERSON> signs the pardon of <PERSON> for any crimes <PERSON> may have committed while in office.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: US President <PERSON> signs the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_<PERSON>_<PERSON>\" title=\"Pardon of <PERSON>\">pardon of <PERSON></a> for any crimes <PERSON> may have committed while in office.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: US President <PERSON> signs the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_<PERSON>_<PERSON>\" title=\"Pardon of <PERSON>\">pardon of <PERSON></a> for any crimes <PERSON> may have committed while in office.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "<PERSON><PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "Gays in the military: US Air Force Tech Sergeant <PERSON>, a decorated veteran of the Vietnam War, appears in his Air Force uniform on the cover of Time magazine with the headline \"I Am A Homosexual\". He is given a general discharge, later upgraded to honorable.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Sexual_orientation_and_gender_identity_in_military_service\" class=\"mw-redirect\" title=\"Sexual orientation and gender identity in military service\">Gays in the military</a>: US Air Force Tech Sergeant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a decorated veteran of the Vietnam War, appears in his Air Force uniform on the cover of <i>Time</i> magazine with the headline \"I Am A Homosexual\". He is given a general discharge, later upgraded to honorable.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sexual_orientation_and_gender_identity_in_military_service\" class=\"mw-redirect\" title=\"Sexual orientation and gender identity in military service\">Gays in the military</a>: US Air Force Tech Sergeant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a decorated veteran of the Vietnam War, appears in his Air Force uniform on the cover of <i>Time</i> magazine with the headline \"I Am A Homosexual\". He is given a general discharge, later upgraded to honorable.", "links": [{"title": "Sexual orientation and gender identity in military service", "link": "https://wikipedia.org/wiki/Sexual_orientation_and_gender_identity_in_military_service"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "Black Friday, a massacre by soldiers against protesters in Tehran, results in 88 deaths, it marks the beginning of the end of the monarchy in Iran.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Black_Friday_(1978)\" title=\"Black Friday (1978)\">Black Friday</a>, a massacre by soldiers against protesters in Tehran, results in 88 deaths, it marks the beginning of the end of the monarchy in Iran.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_Friday_(1978)\" title=\"Black Friday (1978)\">Black Friday</a>, a massacre by soldiers against protesters in Tehran, results in 88 deaths, it marks the beginning of the end of the monarchy in Iran.", "links": [{"title": "Black Friday (1978)", "link": "https://wikipedia.org/wiki/Black_Friday_(1978)"}]}, {"year": "1986", "text": "<PERSON>, a correspondent for U.S. News & World Report, is indicted on charges of espionage by the Soviet Union.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a correspondent for <i><a href=\"https://wikipedia.org/wiki/U.S._News_%26_World_Report\" title=\"U.S. News &amp; World Report\">U.S. News &amp; World Report</a></i>, is indicted on charges of <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a> by the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a correspondent for <i><a href=\"https://wikipedia.org/wiki/U.S._News_%26_World_Report\" title=\"U.S. News &amp; World Report\">U.S. News &amp; World Report</a></i>, is indicted on charges of <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a> by the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "U.S. News & World Report", "link": "https://wikipedia.org/wiki/U.S._News_%26_World_Report"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1988", "text": "Yellowstone National Park is closed for the first time in U.S. history due to ongoing fires.", "html": "1988 - Yellowstone National Park is closed for the first time in U.S. history due to <a href=\"https://wikipedia.org/wiki/Yellowstone_fires_of_1988\" title=\"Yellowstone fires of 1988\">ongoing fires</a>.", "no_year_html": "Yellowstone National Park is closed for the first time in U.S. history due to <a href=\"https://wikipedia.org/wiki/Yellowstone_fires_of_1988\" title=\"Yellowstone fires of 1988\">ongoing fires</a>.", "links": [{"title": "Yellowstone fires of 1988", "link": "https://wikipedia.org/wiki/Yellowstone_fires_of_1988"}]}, {"year": "1989", "text": "Partnair Flight 394 dives into the North Sea, killing 55 people. The investigation showed that the tail of the plane vibrated loose in flight due to sub-standard connecting bolts that had been fraudulently sold as aircraft-grade.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Partnair_Flight_394\" title=\"Partnair Flight 394\">Partnair Flight 394</a> dives into the North Sea, killing 55 people. The investigation showed that the tail of the plane vibrated loose in flight due to sub-standard connecting bolts that had been fraudulently sold as aircraft-grade.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Partnair_Flight_394\" title=\"Partnair Flight 394\">Partnair Flight 394</a> dives into the North Sea, killing 55 people. The investigation showed that the tail of the plane vibrated loose in flight due to sub-standard connecting bolts that had been fraudulently sold as aircraft-grade.", "links": [{"title": "Partnair Flight 394", "link": "https://wikipedia.org/wiki/Partnair_Flight_394"}]}, {"year": "1994", "text": "USAir Flight 427, on approach to Pittsburgh International Airport, suddenly crashes in clear weather killing all 132 aboard, resulting in the most extensive aviation investigation in world history and altering manufacturing practices in the industry.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/USAir_Flight_427\" title=\"USAir Flight 427\">USAir Flight 427</a>, on approach to Pittsburgh International Airport, suddenly crashes in clear weather killing all 132 aboard, resulting in the most extensive aviation investigation in world history and altering manufacturing practices in the industry.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USAir_Flight_427\" title=\"USAir Flight 427\">USAir Flight 427</a>, on approach to Pittsburgh International Airport, suddenly crashes in clear weather killing all 132 aboard, resulting in the most extensive aviation investigation in world history and altering manufacturing practices in the industry.", "links": [{"title": "USAir Flight 427", "link": "https://wikipedia.org/wiki/USAir_Flight_427"}]}, {"year": "2000", "text": "NASA launches Space Shuttle Atlantis on STS-106 to resupply the International Space Station.", "html": "2000 - NASA launches <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> on <a href=\"https://wikipedia.org/wiki/STS-106\" title=\"STS-106\">STS-106</a> to resupply the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "NASA launches <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> on <a href=\"https://wikipedia.org/wiki/STS-106\" title=\"STS-106\">STS-106</a> to resupply the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-106", "link": "https://wikipedia.org/wiki/STS-106"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2004", "text": "NASA's uncrewed spacecraft <PERSON> crash-lands when its parachute fails to open.", "html": "2004 - NASA's uncrewed spacecraft <i><a href=\"https://wikipedia.org/wiki/Genesis_(spacecraft)\" title=\"Genesis (spacecraft)\">Genesis</a></i> crash-lands when its parachute fails to open.", "no_year_html": "NASA's uncrewed spacecraft <i><a href=\"https://wikipedia.org/wiki/Genesis_(spacecraft)\" title=\"Genesis (spacecraft)\">Genesis</a></i> crash-lands when its parachute fails to open.", "links": [{"title": "Genesis (spacecraft)", "link": "https://wikipedia.org/wiki/Genesis_(spacecraft)"}]}, {"year": "2005", "text": "Two Ilyushin Il-76 aircraft from EMERCOM land at a disaster aid staging area at Little Rock Air Force Base; the first time Russia has flown such a mission to North America.", "html": "2005 - Two <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> aircraft from <a href=\"https://wikipedia.org/wiki/EMERCOM\" class=\"mw-redirect\" title=\"EMERCOM\">EMERCOM</a> land at a disaster aid staging area at <a href=\"https://wikipedia.org/wiki/Little_Rock_Air_Force_Base\" title=\"Little Rock Air Force Base\">Little Rock Air Force Base</a>; the first time Russia has flown such a mission to North America.", "no_year_html": "Two <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> aircraft from <a href=\"https://wikipedia.org/wiki/EMERCOM\" class=\"mw-redirect\" title=\"EMERCOM\">EMERCOM</a> land at a disaster aid staging area at <a href=\"https://wikipedia.org/wiki/Little_Rock_Air_Force_Base\" title=\"Little Rock Air Force Base\">Little Rock Air Force Base</a>; the first time Russia has flown such a mission to North America.", "links": [{"title": "Ilyushin Il-76", "link": "https://wikipedia.org/wiki/Ilyushin_Il-76"}, {"title": "EMERCOM", "link": "https://wikipedia.org/wiki/EMERCOM"}, {"title": "Little Rock Air Force Base", "link": "https://wikipedia.org/wiki/Little_Rock_Air_Force_Base"}]}, {"year": "2016", "text": "NASA launches OSIRIS-REx, its first asteroid sample return mission. The probe visited 101955 Bennu and returned with samples in September 2023.", "html": "2016 - NASA launches <a href=\"https://wikipedia.org/wiki/OSIRIS-REx\" title=\"OSIRIS-REx\">OSIRIS-REx</a>, its first asteroid sample return mission. The probe visited <a href=\"https://wikipedia.org/wiki/101955_Bennu\" title=\"101955 Bennu\">101955 Bennu</a> and returned with samples in September 2023.", "no_year_html": "NASA launches <a href=\"https://wikipedia.org/wiki/OSIRIS-REx\" title=\"OSIRIS-REx\">OSIRIS-REx</a>, its first asteroid sample return mission. The probe visited <a href=\"https://wikipedia.org/wiki/101955_<PERSON>nu\" title=\"101955 Bennu\">101955 <PERSON>nu</a> and returned with samples in September 2023.", "links": [{"title": "OSIRIS-REx", "link": "https://wikipedia.org/wiki/OSIRIS-REx"}, {"title": "101955 <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/101955_<PERSON>nu"}]}, {"year": "2017", "text": "Syrian civil war: The Syrian Democratic Forces (SDF) announce the beginning of the Deir ez-Zor campaign, with the stated aim of eliminating the Islamic State (IS) from all areas north and east of the Euphrates.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) announce the beginning of the <a href=\"https://wikipedia.org/wiki/Deir_ez-Zor_campaign_(2017%E2%80%932019)\" title=\"Deir ez-Zor campaign (2017-2019)\">Deir ez-Zor campaign</a>, with the stated aim of eliminating the <a href=\"https://wikipedia.org/wiki/Islamic_State\" title=\"Islamic State\">Islamic State</a> (IS) from all areas north and east of the <a href=\"https://wikipedia.org/wiki/Euphrates\" title=\"Euphrates\">Euphrates</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) announce the beginning of the <a href=\"https://wikipedia.org/wiki/Deir_ez-Zor_campaign_(2017%E2%80%932019)\" title=\"Deir ez-Zor campaign (2017-2019)\">Deir ez-Zor campaign</a>, with the stated aim of eliminating the <a href=\"https://wikipedia.org/wiki/Islamic_State\" title=\"Islamic State\">Islamic State</a> (IS) from all areas north and east of the <a href=\"https://wikipedia.org/wiki/Euphrates\" title=\"Euphrates\">Euphrates</a>.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Syrian Democratic Forces", "link": "https://wikipedia.org/wiki/Syrian_Democratic_Forces"}, {"title": "<PERSON><PERSON> ez<PERSON>Zor campaign (2017-2019)", "link": "https://wikipedia.org/wiki/Deir_<PERSON><PERSON>-<PERSON><PERSON>_campaign_(2017%E2%80%932019)"}, {"title": "Islamic State", "link": "https://wikipedia.org/wiki/Islamic_State"}, {"title": "Euphrates", "link": "https://wikipedia.org/wiki/Euphrates"}]}, {"year": "2022", "text": "Queen <PERSON> II of the United Kingdom dies at Balmoral Castle in Scotland after reigning for 70 years. Her son <PERSON>, Prince of Wales, ascends the throne upon her death as <PERSON> III.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"<PERSON> II\">Queen <PERSON> II</a> of the United Kingdom <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_II\" title=\"Death and state funeral of <PERSON> II\">dies</a> at <a href=\"https://wikipedia.org/wiki/Balmoral_Castle\" title=\"Balmoral Castle\">Balmoral Castle</a> in Scotland after reigning for 70 years. Her son <PERSON>, Prince of Wales, ascends the throne upon her death as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> III</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"<PERSON> II\">Queen <PERSON> II</a> of the United Kingdom <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_II\" title=\"Death and state funeral of <PERSON> II\">dies</a> at <a href=\"https://wikipedia.org/wiki/Balmoral_Castle\" title=\"Balmoral Castle\">Balmoral Castle</a> in Scotland after reigning for 70 years. Her son <PERSON>, Prince of Wales, ascends the throne upon her death as <a href=\"https://wikipedia.org/wiki/<PERSON>_III\" title=\"<PERSON>\"><PERSON> III</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "Death and state funeral of <PERSON> II", "link": "https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_II"}, {"title": "Balmoral Castle", "link": "https://wikipedia.org/wiki/Balmoral_Castle"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "A magnitude 6.9 earthquake strikes Morocco, killing nearly 3,000 people and damaging historic sites in Marrakesh.", "html": "2023 - A <a href=\"https://wikipedia.org/wiki/2023_Al_Haouz_earthquake\" title=\"2023 Al Haouz earthquake\">magnitude 6.9 earthquake</a> strikes Morocco, killing nearly 3,000 people and damaging historic sites in <a href=\"https://wikipedia.org/wiki/Marrakesh\" title=\"Marrakesh\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2023_Al_Haouz_earthquake\" title=\"2023 Al Haouz earthquake\">magnitude 6.9 earthquake</a> strikes Morocco, killing nearly 3,000 people and damaging historic sites in <a href=\"https://wikipedia.org/wiki/Marrakesh\" title=\"Marrakesh\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "2023 Al Haouz earthquake", "link": "https://wikipedia.org/wiki/2023_Al_Haouz_earthquake"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2023", "text": "The 2023 Rugby World Cup, the tenth men's Rugby World Cup is held in France. The opening ceremony, directed and written by <PERSON>, <PERSON> and <PERSON>, took place at the Stade de France in Saint-Denis, before the opening match between France and New Zealand, which saw the host nation winning 27 to 13.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/2023_Rugby_World_Cup\" title=\"2023 Rugby World Cup\">2023 Rugby World Cup</a>, the tenth men's <a href=\"https://wikipedia.org/wiki/Rugby_World_Cup\" title=\"Rugby World Cup\">Rugby World Cup</a> is held in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>. The opening ceremony, directed and written by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON> and <PERSON>, took place at the <a href=\"https://wikipedia.org/wiki/Stade_de_France\" title=\"Stade de France\">Stade de France</a> in <a href=\"https://wikipedia.org/wiki/Saint-Denis,_Seine-Saint-Denis\" title=\"Saint-Denis, Seine-Saint-Denis\">Saint-Denis</a>, before the opening match between <a href=\"https://wikipedia.org/wiki/France_national_rugby_union_team\" title=\"France national rugby union team\">France</a> and <a href=\"https://wikipedia.org/wiki/New_Zealand_national_rugby_union_team\" title=\"New Zealand national rugby union team\">New Zealand</a>, which saw the host nation winning 27 to 13.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2023_Rugby_World_Cup\" title=\"2023 Rugby World Cup\">2023 Rugby World Cup</a>, the tenth men's <a href=\"https://wikipedia.org/wiki/Rugby_World_Cup\" title=\"Rugby World Cup\">Rugby World Cup</a> is held in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>. The opening ceremony, directed and written by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON> and <PERSON>, took place at the <a href=\"https://wikipedia.org/wiki/Stade_de_France\" title=\"Stade de France\">Stade de France</a> in <a href=\"https://wikipedia.org/wiki/Saint-Denis,_Seine-Saint-Denis\" title=\"Saint-Denis, Seine-Saint-Denis\">Saint-Denis</a>, before the opening match between <a href=\"https://wikipedia.org/wiki/France_national_rugby_union_team\" title=\"France national rugby union team\">France</a> and <a href=\"https://wikipedia.org/wiki/New_Zealand_national_rugby_union_team\" title=\"New Zealand national rugby union team\">New Zealand</a>, which saw the host nation winning 27 to 13.", "links": [{"title": "2023 Rugby World Cup", "link": "https://wikipedia.org/wiki/2023_Rugby_World_Cup"}, {"title": "Rugby World Cup", "link": "https://wikipedia.org/wiki/Rugby_World_Cup"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Stade de France", "link": "https://wikipedia.org/wiki/Stade_de_France"}, {"title": "Saint-Denis, Seine-Saint-Denis", "link": "https://wikipedia.org/wiki/<PERSON>-Denis,_Seine-Saint-Denis"}, {"title": "France national rugby union team", "link": "https://wikipedia.org/wiki/France_national_rugby_union_team"}, {"title": "New Zealand national rugby union team", "link": "https://wikipedia.org/wiki/New_Zealand_national_rugby_union_team"}]}], "Births": [{"year": "685", "text": "Emperor <PERSON><PERSON><PERSON> of Tang (d. 762)", "html": "685 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON>zong of Tang\">Emperor <PERSON><PERSON><PERSON> of Tang</a> (d. 762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON>zong of Tang\">Emperor <PERSON><PERSON><PERSON> of Tang</a> (d. 762)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Tang"}]}, {"year": "801", "text": "<PERSON><PERSON><PERSON>, German archbishop and saint (d. 865)", "html": "801 - <a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German archbishop and saint (d. 865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German archbishop and saint (d. 865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ansgar"}]}, {"year": "828", "text": "<PERSON>, <PERSON><PERSON><PERSON> (Western Arabian), 10th of the Twelve Imams (d. 868)", "html": "828 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> (Western <a href=\"https://wikipedia.org/wiki/Arabia\" class=\"mw-redirect\" title=\"Arabia\">Arabian</a>), 10th of <a href=\"https://wikipedia.org/wiki/The_Twelve_Imams\" class=\"mw-redirect\" title=\"The Twelve Imams\">the Twelve Imams</a> (d. 868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> (Western <a href=\"https://wikipedia.org/wiki/Arabia\" class=\"mw-redirect\" title=\"Arabia\">Arabian</a>), 10th of <a href=\"https://wikipedia.org/wiki/The_Twelve_Imams\" class=\"mw-redirect\" title=\"The Twelve Imams\">the Twelve Imams</a> (d. 868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Arabia", "link": "https://wikipedia.org/wiki/Arabia"}, {"title": "The Twelve Imams", "link": "https://wikipedia.org/wiki/The_Twelve_Imams"}]}, {"year": "1157", "text": "<PERSON> of England (d. 1199)", "html": "1157 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1199)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1199)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1209", "text": "<PERSON><PERSON> of Portugal (d. 1248)", "html": "1209 - <a href=\"https://wikipedia.org/wiki/Sancho_II_of_Portugal\" title=\"Sancho II of Portugal\">Sancho II of Portugal</a> (d. 1248)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sancho_II_of_Portugal\" title=\"Sancho II of Portugal\">Sancho II of Portugal</a> (d. 1248)", "links": [{"title": "Sancho II of Portugal", "link": "https://wikipedia.org/wiki/Sancho_II_of_Portugal"}]}, {"year": "1271", "text": "<PERSON> of Anjou (d. 1295)", "html": "1271 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (d. 1295)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (d. 1295)", "links": [{"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Anjou"}]}, {"year": "1380", "text": "<PERSON> of Siena, Italian priest, missionary, and saint (d. 1444)", "html": "1380 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Siena\" title=\"Bernardino of Siena\"><PERSON> of Siena</a>, Italian priest, missionary, and saint (d. 1444)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Siena\" title=\"<PERSON> of Siena\"><PERSON> of Siena</a>, Italian priest, missionary, and saint (d. 1444)", "links": [{"title": "<PERSON> of Siena", "link": "https://wikipedia.org/wiki/Bernardino_of_Siena"}]}, {"year": "1413", "text": "<PERSON> of Bologna, Italian nun and saint (d. 1463)", "html": "1413 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bologna\" title=\"<PERSON> of Bologna\"><PERSON> of Bologna</a>, Italian nun and saint (d. 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bologna\" title=\"<PERSON> of Bologna\"><PERSON> of Bologna</a>, Italian nun and saint (d. 1463)", "links": [{"title": "Catherine of Bologna", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bologna"}]}, {"year": "1442", "text": "<PERSON>, 13th Earl of Oxford, English commander and politician, Lord Great Chamberlain of England (d. 1513)", "html": "1442 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_13th_Earl_of_Oxford\" title=\"<PERSON>, 13th Earl of Oxford\"><PERSON>, 13th Earl of Oxford</a>, English commander and politician, <a href=\"https://wikipedia.org/wiki/Lord_Great_Chamberlain\" title=\"Lord Great Chamberlain\">Lord Great Chamberlain of England</a> (d. 1513)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_13th_Earl_of_Oxford\" title=\"<PERSON>, 13th Earl of Oxford\"><PERSON>, 13th Earl of Oxford</a>, English commander and politician, <a href=\"https://wikipedia.org/wiki/Lord_Great_Chamberlain\" title=\"Lord Great Chamberlain\">Lord Great Chamberlain of England</a> (d. 1513)", "links": [{"title": "<PERSON>, 13th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>,_13th_Earl_of_Oxford"}, {"title": "Lord Great <PERSON>", "link": "https://wikipedia.org/wiki/Lord_Great_<PERSON>"}]}, {"year": "1462", "text": "<PERSON>, first known English vernacular dramatist (d. 1501)", "html": "1462 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first known English vernacular dramatist (d. 1501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first known English vernacular dramatist (d. 1501)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1474", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian playwright and poet (d. 1533)", "html": "1474 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian playwright and poet (d. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian playwright and poet (d. 1533)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1515", "text": "<PERSON>, Spanish priest and scholar (d. 1585)", "html": "1515 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish priest and scholar (d. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish priest and scholar (d. 1585)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfonso_<PERSON>"}]}, {"year": "1588", "text": "<PERSON>, French mathematician, philosopher, and theologian (d. 1648)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, philosopher, and theologian (d. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, philosopher, and theologian (d. 1648)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1593", "text": "<PERSON><PERSON><PERSON>, Japanese nobleman (d. 1615)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hideyori\" title=\"Toyoto<PERSON> Hideyori\"><PERSON><PERSON><PERSON></a>, Japanese nobleman (d. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hideyori\" title=\"Toyoto<PERSON> Hideyori\"><PERSON><PERSON><PERSON></a>, Japanese nobleman (d. 1615)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ri"}]}, {"year": "1611", "text": "<PERSON>, German scholar and critic (d. 1671)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and critic (d. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and critic (d. 1671)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, <PERSON>, French general (d. 1686)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Cond%C3%A9\" title=\"<PERSON>, Grand Condé\"><PERSON>, Grand <PERSON></a>, French general (d. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Cond%C3%A9\" title=\"<PERSON>, Grand Condé\"><PERSON>, Grand Condé</a>, French general (d. 1686)", "links": [{"title": "<PERSON>, Grand Condé", "link": "https://wikipedia.org/wiki/<PERSON>,_Grand_Cond%C3%A9"}]}, {"year": "1633", "text": "<PERSON>, King of the Romans (d. 1654)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_King_of_the_Romans\" title=\"<PERSON> IV, King of the Romans\"><PERSON> IV, King of the Romans</a> (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_King_of_the_Romans\" title=\"<PERSON> IV, King of the Romans\"><PERSON> IV, King of the Romans</a> (d. 1654)", "links": [{"title": "<PERSON>, King of the Romans", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_the_Romans"}]}, {"year": "1672", "text": "<PERSON>, French organist and composer (d. 1703)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1698", "text": "<PERSON>, French violinist and composer (d. 1787)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (d. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (d. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON><PERSON>, English painter and academic (d. 1810)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y\" title=\"<PERSON><PERSON> Hump<PERSON>\"><PERSON><PERSON></a>, English painter and academic (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Humphry\"><PERSON><PERSON></a>, English painter and academic (d. 1810)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1749", "text": "<PERSON><PERSON><PERSON>, French educator (d. 1793)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French educator (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Polastron\"><PERSON><PERSON><PERSON></a>, French educator (d. 1793)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ron"}]}, {"year": "1750", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 4th <PERSON><PERSON><PERSON><PERSON> (d. 1795)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kajinosuke\" title=\"<PERSON><PERSON><PERSON> Kajinosuke\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 4th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kajinosuke\" title=\"<PERSON><PERSON><PERSON> Kajinosuke\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 4th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1795)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>uke"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1752", "text": "<PERSON>, Swedish opera singer, actor, and director (d. 1813)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Carl <PERSON>\"><PERSON></a>, Swedish opera singer, actor, and director (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Carl <PERSON>\"><PERSON></a>, Swedish opera singer, actor, and director (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "<PERSON> <PERSON>, German poet and critic (d. 1845)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German poet and critic (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German poet and critic (d. 1845)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON>, German nun and mystic (d. 1824)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German nun and mystic (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German nun and mystic (d. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, Ottoman sultan (d. 1808)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" title=\"Mustafa IV\"><PERSON></a>, Ottoman sultan (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" title=\"Mustafa IV\"><PERSON></a>, Ottoman sultan (d. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_IV"}]}, {"year": "1783", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Danish pastor, philosopher, and author (d. 1872)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/N._F._S._Grundtvig\" title=\"N. F. S. Grundtvig\">N. F<PERSON> S<PERSON>rundtvig</a>, Danish pastor, philosopher, and author (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N._F._S._Grundtvig\" title=\"N. F. S. Grundtvig\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Danish pastor, philosopher, and author (d. 1872)", "links": [{"title": "N. F. S. Grundtvig", "link": "https://wikipedia.org/wiki/N._F._S._Grundtvig"}]}, {"year": "1804", "text": "<PERSON>, German pastor, poet, and academic (d. 1875)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rike\" title=\"<PERSON>\"><PERSON></a>, German pastor, poet, and academic (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rike\" title=\"<PERSON>\"><PERSON></a>, German pastor, poet, and academic (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_M%C3%B6rike"}]}, {"year": "1814", "text": "<PERSON>, French archaeologist, ethnographer, and historian (d. 1874)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89tien<PERSON>_<PERSON>eur_de_Bourbourg\" title=\"<PERSON> Bourbourg\"><PERSON></a>, French archaeologist, ethnographer, and historian (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89tien<PERSON>_<PERSON>eur_de_Bourbourg\" title=\"<PERSON> Bourbourg\"><PERSON></a>, French archaeologist, ethnographer, and historian (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%89<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian soprano and educator (d. 1897)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Strepponi\" title=\"<PERSON><PERSON><PERSON><PERSON> Strepponi\"><PERSON><PERSON><PERSON><PERSON></a>, Italian soprano and educator (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Strepponi\" title=\"<PERSON><PERSON><PERSON><PERSON> Strepponi\"><PERSON><PERSON><PERSON><PERSON></a>, Italian soprano and educator (d. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Strepponi"}]}, {"year": "1822", "text": "<PERSON>, German geologist and explorer (d. 1892)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geologist and explorer (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geologist and explorer (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, Spanish-American composer, conductor, and director (d. 1908)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Spanish-American composer, conductor, and director (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Spanish-American composer, conductor, and director (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3"}]}, {"year": "1828", "text": "<PERSON>, American general and politician, 32nd Governor of Maine (d. 1914)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Maine", "link": "https://wikipedia.org/wiki/Governor_of_Maine"}]}, {"year": "1828", "text": "<PERSON>, American author and critic (d. 1900)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French poet and lexicographer, Nobel Prize laureate (d. 1914)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French poet and lexicographer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French poet and lexicographer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Mistral"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1831", "text": "<PERSON>, German author and painter (d. 1910)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and painter (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and painter (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON><PERSON>, Czech composer and academic (d. 1904)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and academic (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and academic (d. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k"}]}, {"year": "1841", "text": "<PERSON>, American assassin of president <PERSON> (d. 1882)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assassin of president <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assassin of president <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Indian-Hong Kong businessman and politician (d. 1926)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Hong Kong businessman and politician (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Hong Kong businessman and politician (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, American-Australian businessman and politician, 22nd Premier of South Australia (d. 1923)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, American-Australian businessman and politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, American-Australian businessman and politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1923)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1852", "text": "<PERSON><PERSON> of Korea, 26th Emperor of the Joseon Kingdom and first emperor of Korea (d. 1919)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/Gojong_of_Korea\" title=\"Gojong of Korea\"><PERSON><PERSON> of Korea</a>, 26th Emperor of the Joseon Kingdom and first emperor of Korea (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gojong_of_Korea\" title=\"Gojong of Korea\"><PERSON><PERSON> of Korea</a>, 26th Emperor of the Joseon Kingdom and first emperor of Korea (d. 1919)", "links": [{"title": "Gojong of Korea", "link": "https://wikipedia.org/wiki/Gojong_of_Korea"}]}, {"year": "1857", "text": "<PERSON>, German academic and politician, 6th Chancellor of Germany (d. 1936)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician, 6th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician, 6th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany (German Reich)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)"}]}, {"year": "1863", "text": "<PERSON> of the Divine Heart, German nun and saint (d. 1899)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Divine_Heart\" title=\"Mary of the Divine Heart\">Mary of the Divine Heart</a>, German nun and saint (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Divine_Heart\" title=\"Mary of the Divine Heart\"><PERSON> of the Divine Heart</a>, German nun and saint (d. 1899)", "links": [{"title": "<PERSON> of the Divine Heart", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Divine_Heart"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON><PERSON>, English novelist and short story writer (d. 1943)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English novelist and short story writer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English novelist and short story writer (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W.<PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Belarusian-German theoretician and activist (d. 1924)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-German theoretician and activist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-German theoretician and activist (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, American mandolin player, composer, and bandleader (d. 1953)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mandolin player, composer, and bandleader (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mandolin player, composer, and bandleader (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, Mexican politician, Vice President of Mexico, murdered in a military coup (d. 1913)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Pino_Su%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Mexican politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_Mexico\" class=\"mw-redirect\" title=\"Vice President of Mexico\">Vice President of Mexico</a>, murdered in a military coup (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Pino_Su%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Mexican politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_Mexico\" class=\"mw-redirect\" title=\"Vice President of Mexico\">Vice President of Mexico</a>, murdered in a military coup (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Pino_Su%C3%A1rez"}, {"title": "Vice President of Mexico", "link": "https://wikipedia.org/wiki/Vice_President_of_Mexico"}]}, {"year": "1871", "text": "<PERSON>, Canadian businessman and philanthropist, founded the McLaughlin Carriage Company (d. 1972)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_Motor_Car_Company#McLaughlin\" title=\"McLaughlin Motor Car Company\">McLaughlin Carriage Company</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_Motor_Car_Company#McLaughlin\" title=\"McLaughlin Motor Car Company\">McLaughlin Carriage Company</a> (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "McLaughlin Motor Car Company", "link": "https://wikipedia.org/wiki/McLaughlin_Motor_Car_Company#McLaughlin"}]}, {"year": "1872", "text": "<PERSON>, American judge (d. 1939)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, French author and playwright (d. 1907)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, American religious leader, 9th President of The Church of Jesus Christ of Latter-day Saints (d. 1970)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 9th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 9th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Mormon missionary and Utah politician (d. 1937)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mormon missionary and Utah politician (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mormon missionary and Utah politician (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American runner and hurdler (d. 1945)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and hurdler (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and hurdler (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Turkish physician and politician, 5th Prime Minister of Turkey (d. 1942)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish physician and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish physician and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Refi<PERSON>_<PERSON>dam"}, {"title": "Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Turkey"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Belgian race car driver (d. 1921)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>\" title=\"Théod<PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, Belgian race car driver (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>\" title=\"Th<PERSON>od<PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, Belgian race car driver (d. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_Pilette"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, English captain, journalist, and poet (d. 1967)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English captain, journalist, and poet (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English captain, journalist, and poet (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON>, French soprano and actress (d. 1961)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French soprano and actress (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French soprano and actress (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Hindu monk, spiritual leader, physician, proponent of <PERSON><PERSON><PERSON>, etc. (d. 1963)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hindu monk, spiritual leader, physician, proponent of <a href=\"https://wikipedia.org/wiki/Vedanta\" title=\"Vedanta\">Veda<PERSON></a>, etc. (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hindu monk, spiritual leader, physician, proponent of <a href=\"https://wikipedia.org/wiki/Vedanta\" title=\"Vedanta\">Veda<PERSON></a>, etc. (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vedanta"}]}, {"year": "1888", "text": "<PERSON>, American broadcaster and designer of the flag of South Dakota (d. 1974)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster and designer of the <a href=\"https://wikipedia.org/wiki/Flag_of_South_Dakota\" title=\"Flag of South Dakota\">flag of South Dakota</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_M<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster and designer of the <a href=\"https://wikipedia.org/wiki/Flag_of_South_Dakota\" title=\"Flag of South Dakota\">flag of South Dakota</a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Flag of South Dakota", "link": "https://wikipedia.org/wiki/Flag_of_South_Dakota"}]}, {"year": "1889", "text": "<PERSON>, American lawyer and politician (d. 1953)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Canadian soldier and politician (d. 1974)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier and politician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier and politician (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Dutch composer and critic (d. 1947)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer and critic (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer and critic (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American publicist and songwriter (d. 1983)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publicist and songwriter (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publicist and songwriter (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1933)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_singer)\" class=\"mw-redirect\" title=\"<PERSON> (country singer)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_singer)\" class=\"mw-redirect\" title=\"<PERSON> (country singer)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1933)", "links": [{"title": "<PERSON> (country singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_singer)"}]}, {"year": "1900", "text": "<PERSON><PERSON>, English-Australian organised crime boss (d. 1970)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Australian organised crime boss (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Australian organised crime boss (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American lawyer and politician (d. 1989)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Dutch-South African journalist and politician, 7th Prime Minister of South Africa (d. 1966)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-South African journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-South African journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of South Africa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Africa"}]}, {"year": "1903", "text": "<PERSON>, English author (d. 1994)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Jane_Arbor\" title=\"Jane Arbor\"><PERSON></a>, English author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jane_Arbor\" title=\"Jane Arbor\"><PERSON></a>, English author (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jane_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Finnish politician (d. 1970)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Russian engineer and politician (d. 1990)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Russian engineer and politician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Russian engineer and politician (d. 1990)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "1907", "text": "<PERSON>, Australian economist and politician, 11th Australian Minister for Human Services (d. 2003)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian economist and politician, 11th <a href=\"https://wikipedia.org/wiki/Minister_for_Human_Services\" class=\"mw-redirect\" title=\"Minister for Human Services\">Australian Minister for Human Services</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian economist and politician, 11th <a href=\"https://wikipedia.org/wiki/Minister_for_Human_Services\" class=\"mw-redirect\" title=\"Minister for Human Services\">Australian Minister for Human Services</a> (d. 2003)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Minister for Human Services", "link": "https://wikipedia.org/wiki/Minister_for_Human_Services"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Polish runner (d. 1943)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish runner (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish runner (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>ji"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, French actor and director (d. 1994)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and director (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and director (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1914", "text": "Patriarch <PERSON><PERSON><PERSON><PERSON> of Constantinople (d. 1991)", "html": "1914 - Patriarch <a href=\"https://wikipedia.org/wiki/Demetrios_I_of_Constantinople\" title=\"Demetrios I of Constantinople\">Demetrios I of Constantinople</a> (d. 1991)", "no_year_html": "Patriarch <a href=\"https://wikipedia.org/wiki/Demetrios_I_of_Constantinople\" title=\"Demetrios I of Constantinople\">Demetrios I of Constantinople</a> (d. 1991)", "links": [{"title": "Demetrios I of Constantinople", "link": "https://wikipedia.org/wiki/Demetrios_I_of_Constantinople"}]}, {"year": "1914", "text": "<PERSON><PERSON>, English architect, designed the Royal National Theatre (d. 2001)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Royal_National_Theatre\" title=\"Royal National Theatre\">Royal National Theatre</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Royal_National_Theatre\" title=\"Royal National Theatre\">Royal National Theatre</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Den<PERSON>_Lasdun"}, {"title": "Royal National Theatre", "link": "https://wikipedia.org/wiki/Royal_National_Theatre"}]}, {"year": "1915", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Filipino novelist, poet, and writer (d. 1999)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/N._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Filipino novelist, poet, and writer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Filipino novelist, poet, and writer (d. 1999)", "links": [{"title": "<PERSON>. <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Czech-Australian violinist and educator (d. 2009)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Australian violinist and educator (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Australian violinist and educator (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English-American chemist and academic, Nobel Prize laureate (d. 1998)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Italian journalist and author (d. 1992)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and author (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and author (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Austrian painter and academic (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Welsh-English actor (d. 2001)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English actor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Croatian concentration camp commander (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Din<PERSON>_%C5%A0aki%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian concentration camp commander (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%A0aki%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian concentration camp commander (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dinko_%C5%A0aki%C4%87"}]}, {"year": "1922", "text": "<PERSON>, American comic actor and writer (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Caesar\" title=\"Sid Caesar\"><PERSON></a>, American comic actor and writer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Caesar\" title=\"Sid Caesar\"><PERSON></a>, American comic actor and writer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American politician and activist, founded the LaRouche movement (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and activist, founded the <a href=\"https://wikipedia.org/wiki/LaRouche_movement\" title=\"LaRou<PERSON> movement\">LaRouche movement</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and activist, founded the <a href=\"https://wikipedia.org/wiki/LaRouche_movement\" title=\"LaRou<PERSON> movement\">LaRouche movement</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "LaRouche movement", "link": "https://wikipedia.org/wiki/LaRouche_movement"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Russian poet (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, American double-bassist (d. 1979)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"Wil<PERSON>\"><PERSON><PERSON><PERSON></a>, American double-bassist (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American double-bassist (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American politician, 53rd Governor of Kentucky (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American politician, 53rd <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American politician, 53rd <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Kentucky", "link": "https://wikipedia.org/wiki/Governor_of_Kentucky"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, American-Canadian lawyer, judge, and politician (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Canadian lawyer, judge, and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Canadian lawyer, judge, and politician (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American author (d. 1964)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Grace_Metalious\" title=\"Grace Metalious\"><PERSON></a>, American author (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grace_Metalious\" title=\"Grace Metalious\"><PERSON></a>, American author (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Grace_Metalious"}]}, {"year": "1924", "text": "<PERSON>, Canadian-Swiss painter (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mimi Parent\"><PERSON></a>, Canadian-Swiss painter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mimi Parent\"><PERSON></a>, Canadian-Swiss painter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rent"}]}, {"year": "1925", "text": "<PERSON>, American activist, founded the Veteran Feminists of America", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/Veteran_Feminists_of_America\" title=\"Veteran Feminists of America\">Veteran Feminists of America</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/Veteran_Feminists_of_America\" title=\"Veteran Feminists of America\">Veteran Feminists of America</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Veteran Feminists of America", "link": "https://wikipedia.org/wiki/Veteran_Feminists_of_America"}]}, {"year": "1925", "text": "<PERSON>, English actor and comedian (d. 1980)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Indian singer-songwriter, poet, and director (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zar<PERSON>\" title=\"B<PERSON><PERSON> Hazarika\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter, poet, and director (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zar<PERSON>\" title=\"<PERSON><PERSON><PERSON> Hazar<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter, poet, and director (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ika"}]}, {"year": "1927", "text": "<PERSON>, American songwriter (d. 2002)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American politician, 42nd Lieutenant Governor of Indiana (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 42nd <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Indiana\" title=\"Lieutenant Governor of Indiana\">Lieutenant Governor of Indiana</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 42nd <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Indiana\" title=\"Lieutenant Governor of Indiana\">Lieutenant Governor of Indiana</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Indiana", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Indiana"}]}, {"year": "1927", "text": "<PERSON>, American-French mathematician (d. 2024)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French mathematician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French mathematician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German conductor", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi\" title=\"<PERSON>\"><PERSON></a>, German conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi\" title=\"<PERSON>\"><PERSON></a>, German conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese general and politician, 16th Prime Minister of the Republic of Vietnam (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Cao_K%E1%BB%B3\" title=\"Nguyễ<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese general and politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Vietnam\" class=\"mw-redirect\" title=\"Prime Minister of the Republic of Vietnam\">Prime Minister of the Republic of Vietnam</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Cao_K%E1%BB%B3\" title=\"Nguyễ<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese general and politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Vietnam\" class=\"mw-redirect\" title=\"Prime Minister of the Republic of Vietnam\">Prime Minister of the Republic of Vietnam</a> (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Cao_K%E1%BB%B3"}, {"title": "Prime Minister of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Vietnam"}]}, {"year": "1931", "text": "<PERSON>, American saxophonist and composer (d. 2010)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English politician (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician (d. 2007)", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(British_politician)"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American singer-songwriter and pianist (d. 1963)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sy_Cline"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Indian singer ", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English author and playwright", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Taiwanese banker and businessman (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese banker and businessman (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese banker and businessman (d. 2012)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1933", "text": "<PERSON>, American composer, producer, and critic (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, producer, and critic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, producer, and critic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Latvian basketball player and coach (d. 1999)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player and coach (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player and coach (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Canadian politician", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Rodrigue_Biron\" title=\"Rodrigue Biron\"><PERSON><PERSON><PERSON></a>, Canadian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rodrigue_Biron\" title=\"Rodrigue Biron\"><PERSON><PERSON><PERSON></a>, Canadian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rodrigue_Biron"}]}, {"year": "1934", "text": "<PERSON>, New Zealand rugby player (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 2014)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1934", "text": "<PERSON>, English composer and conductor (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Baron <PERSON>, English academic and politician", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English admiral", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Somaliland politician and activist", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Somaliland\" title=\"Somaliland\">Somaliland</a> politician and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Somaliland\" title=\"Somaliland\">Somaliland</a> politician and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Somaliland", "link": "https://wikipedia.org/wiki/Somaliland"}]}, {"year": "1937", "text": "<PERSON>, American-Canadian journalist (d. 1992)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American author and illustrator (d. 1998)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and illustrator (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and illustrator (d. 1998)", "links": [{"title": "<PERSON> (comics)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)"}]}, {"year": "1938", "text": "<PERSON>, American sergeant and radio host (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and radio host (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and radio host (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Japanese sailor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese sailor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1939", "text": "<PERSON><PERSON>, German field hockey player and coach", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON> <PERSON><PERSON>, American singer and guitarist (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Guitar_Shorty\" title=\"Guitar Shorty\">Guitar <PERSON><PERSON></a>, American singer and guitarist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guitar_Shorty\" title=\"Guitar Shorty\">Guitar Short<PERSON></a>, American singer and guitarist (d. 2022)", "links": [{"title": "Guitar Shorty", "link": "https://wikipedia.org/wiki/<PERSON>_Shorty"}]}, {"year": "1940", "text": "<PERSON>, American religious leader", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Polish historian and journalist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish historian and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish historian and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American author and poet", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American bass player (d. 1972)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bass_guitarist)\" class=\"mw-redirect\" title=\"<PERSON> (bass guitarist)\"><PERSON></a>, American bass player (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bass_guitarist)\" class=\"mw-redirect\" title=\"<PERSON> (bass guitarist)\"><PERSON></a>, American bass player (d. 1972)", "links": [{"title": "<PERSON> (bass guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>(bass_guitarist)"}]}, {"year": "1942", "text": "<PERSON>, English journalist and author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American rock singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sal_Valentino"}]}, {"year": "1943", "text": "<PERSON>, American academic and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Adelaide_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON><PERSON></a>, American academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_C<PERSON>_<PERSON>\" title=\"Adelaide C<PERSON>\"><PERSON> <PERSON><PERSON></a>, American academic and politician", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adelaide_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter (d. 1991)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Peter Bella<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English economist and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian cricketer and coach (d. 2011)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American football player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English bass player (d. 2009)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American singer-songwriter and keyboard player (d. 1973)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Pig<PERSON>%22_<PERSON><PERSON><PERSON><PERSON><PERSON>\" title='<PERSON>\" Mc<PERSON><PERSON><PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and keyboard player (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Pigpen%22_<PERSON><PERSON><PERSON><PERSON><PERSON>\" title='<PERSON> \"<PERSON>\" Mc<PERSON><PERSON><PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and keyboard player (d. 1973)", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Pigpen%22_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Croatian cardinal", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian cardinal", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vinko_Pulji%C4%87"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Rogie_Vachon\" title=\"Rogie Vachon\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rogie_Vachon\" title=\"Rogie Vachon\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rogie_<PERSON><PERSON>on"}]}, {"year": "1946", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/L._<PERSON><PERSON>_<PERSON>\" title=\"L. C<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L._<PERSON><PERSON>_<PERSON>\" title=\"L. C<PERSON> Greenwood\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player (d. 2013)", "links": [{"title": "L. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Turkish-American biologist and academic, Nobel Prize laureate", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1946", "text": "<PERSON>, Singaporean business executive, former Deputy Prime Minister of Singapore", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean business executive, former <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Singapore\" title=\"Deputy Prime Minister of Singapore\">Deputy Prime Minister of Singapore</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean business executive, former <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Singapore\" title=\"Deputy Prime Minister of Singapore\">Deputy Prime Minister of Singapore</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Singapore", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Singapore"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Russian pianist and conductor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian pianist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian pianist and conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic accountant and politician, 22nd Prime Minister of Iceland (d. 2015)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Halld%C3%B3r_%C3%81sgr%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic accountant and politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Halld%C3%B3r_%C3%81sgr%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic accountant and politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Halld%C3%B3r_%C3%81sgr%C3%<PERSON><PERSON>son"}, {"title": "Prime Minister of Iceland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iceland"}]}, {"year": "1947", "text": "<PERSON>, American novelist and short story writer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter and bass player (d. 2000)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ka<PERSON><PERSON>\" class=\"mw-redirect\" title=\"Great Kabuki\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Ka<PERSON><PERSON>\" class=\"mw-redirect\" title=\"Great Kabuki\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "Great Kabuki", "link": "https://wikipedia.org/wiki/Great_Ka<PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist (d. 1971)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A9"}]}, {"year": "1949", "text": "<PERSON>, English physicist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Scottish lawyer and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" class=\"mw-redirect\" title=\"<PERSON> (British politician)\"><PERSON></a>, Scottish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" class=\"mw-redirect\" title=\"<PERSON> (British politician)\"><PERSON></a>, Scottish lawyer and politician", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and poet", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American dentist and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American tennis player and coach (d. 1996)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American tennis player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian pianist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Dezs%C5%91_R%C3%A1nki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dezs%C5%91_R%C3%A1nki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian pianist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dezs%C5%91_R%C3%A1nki"}]}, {"year": "1952", "text": "<PERSON>, American bass player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON> (bassist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bassist)"}]}, {"year": "1952", "text": "<PERSON>, English cricketer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, New Zealand rugby player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, French actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Norwegian guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American civil rights activist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bridges\" title=\"<PERSON> Bridges\"><PERSON></a>, American civil rights activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bridges\" title=\"<PERSON> Bridges\"><PERSON></a>, American civil rights activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ruby_Bridges"}]}, {"year": "1954", "text": "<PERSON>, Swedish Olympic and world champion épée fencer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish Olympic and world champion épée fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish Olympic and world champion épée fencer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American historian, author, and academic, founded The Skeptics Society", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic, founded <a href=\"https://wikipedia.org/wiki/The_Skeptics_Society\" title=\"The Skeptics Society\">The Skeptics Society</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic, founded <a href=\"https://wikipedia.org/wiki/The_Skeptics_Society\" title=\"The Skeptics Society\">The Skeptics Society</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Skeptics Society", "link": "https://wikipedia.org/wiki/The_Skeptics_Society"}]}, {"year": "1955", "text": "<PERSON>, Australian footballer (d. 2013)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27H<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27H<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON><PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American environmentalist and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American drummer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1956", "text": "<PERSON>, American journalist and author (d. 2015)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (d. 2015)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>(journalist)"}]}, {"year": "1956", "text": "<PERSON>, American basketball player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Swedish race car driver", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American football player (d. 2013)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actress and activist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American wrestler", "html": "1958 - <a href=\"https://wikipedia.org/wiki/The_Batten_Twins\" class=\"mw-redirect\" title=\"The Batten Twins\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Batten_Twins\" class=\"mw-redirect\" title=\"The Batten Twins\"><PERSON></a>, American wrestler", "links": [{"title": "The Batten Twins", "link": "https://wikipedia.org/wiki/The_Batten_Twins"}]}, {"year": "1958", "text": "<PERSON>, American wrestler (d. 2014)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/The_Batten_Twins\" class=\"mw-redirect\" title=\"The Batten Twins\"><PERSON></a>, American wrestler (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Batten_Twins\" class=\"mw-redirect\" title=\"The Batten Twins\"><PERSON></a>, American wrestler (d. 2014)", "links": [{"title": "The Batten Twins", "link": "https://wikipedia.org/wiki/The_Batten_Twins"}]}, {"year": "1958", "text": "<PERSON>, American keyboard player, songwriter, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English bass player and songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English bass player and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Suzuki\" title=\"Aguri Suzuki\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Aguri Suzuki\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American wrestler (d. 2017)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, German actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>iou"}]}, {"year": "1963", "text": "<PERSON>, American scientist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American businessman and political activist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(policy_analyst)\" title=\"<PERSON> (policy analyst)\"><PERSON></a>, American businessman and political activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(policy_analyst)\" title=\"<PERSON> (policy analyst)\"><PERSON></a>, American businessman and political activist", "links": [{"title": "<PERSON> (policy analyst)", "link": "https://wikipedia.org/wiki/<PERSON>_(policy_analyst)"}]}, {"year": "1964", "text": "<PERSON>, Norwegian singer-songwriter and guitarist (d. 2000)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter and guitarist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter and guitarist (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American wrestler", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, German Benedictine monk and abbot", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German Benedictine monk and abbot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German Benedictine monk and abbot", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Australian singer-songwriter and pastor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and pastor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and pastor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian singer-songwriter, guitarist, and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Estonian politician and diplomat", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Estonian politician and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Estonian politician and diplomat", "links": [{"title": "Eerik<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eri<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian businessman", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, German footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1969", "text": "<PERSON>, Norwegian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Ecuadorian footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Welsh footballer and manager (d. 2011)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>\" title=\"Neko Case\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Neko Case\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "Neko Case", "link": "https://wikipedia.org/wiki/Neko_Case"}]}, {"year": "1970", "text": "<PERSON>, Canadian-Swiss ice hockey player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Swiss ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American soldier, psychiatrist, and mass murderer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier, psychiatrist, and mass murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier, psychiatrist, and mass murderer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rewell\" title=\"<PERSON><PERSON><PERSON>rewell\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rewell\" title=\"<PERSON><PERSON><PERSON>rewell\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ell"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American wrestler", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1970", "text": "<PERSON>, Irish rugby player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Irish rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Irish rugby player and coach", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1970", "text": "<PERSON>, Australian rugby player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor, director, producer, screenwriter, and wrestler", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, screenwriter, and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, screenwriter, and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress and television personality", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, English-Australian businessman", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Australian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Australian businessman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American pianist and composer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON>ran\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON>ran\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dustin_O%27H<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Bulgarian boxer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Pierre_<PERSON>%C3%A9<PERSON><PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pierre_<PERSON>%C3%A9<PERSON><PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/Pierre_S%C3%A9<PERSON><PERSON>_(ice_hockey)"}]}, {"year": "1972", "text": "<PERSON>, German footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, South African rugby player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, South African rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, South African rugby player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American radio and television host", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(commentator)\" title=\"<PERSON> (commentator)\"><PERSON></a>, American radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(commentator)\" title=\"<PERSON> (commentator)\"><PERSON></a>, American radio and television host", "links": [{"title": "<PERSON> (commentator)", "link": "https://wikipedia.org/wiki/<PERSON>_(commentator)"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Saudi Arabian footballer (d. 2020)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Al-Dosari\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Al<PERSON>Dosari\"><PERSON><PERSON><PERSON></a>, Saudi Arabian footballer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Al-Dosari\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>Dosari\"><PERSON><PERSON><PERSON></a>, Saudi Arabian footballer (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>is_Al-Dosari"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, American saxophonist, keyboard player, and composer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American saxophonist, keyboard player, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American saxophonist, keyboard player, and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Italian writer and journalist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian writer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian writer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matteo_<PERSON>rukul"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Cypriot footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Iranian-American director and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Tan<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tan<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-American director and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tan<PERSON>_<PERSON>ian"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"B<PERSON>uli<PERSON> Luna\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American wrestler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, South Korean footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-yong\" title=\"<PERSON>yong\"><PERSON></a>, South Korean footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-yo<PERSON>\" title=\"<PERSON>yo<PERSON>\"><PERSON></a>, South Korean footballer and manager", "links": [{"title": "<PERSON>yong", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-yong"}]}, {"year": "1975", "text": "<PERSON>, English drummer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1975", "text": "<PERSON>, Australian rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_(rugby_union)"}]}, {"year": "1975", "text": "<PERSON>, Russian tennis player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American actor, director, and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Costa Rican footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Costa Rican footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Dutch tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Sjeng_<PERSON>\" title=\"Sjeng Sc<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sjeng_<PERSON>\" title=\"Sjeng Sc<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch tennis player", "links": [{"title": "Sjeng Schalken", "link": "https://wikipedia.org/wiki/Sjeng_Sc<PERSON>ken"}]}, {"year": "1977", "text": "<PERSON>, American basketball player (d. 2005)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor and comedian", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Spanish footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Italian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian-American author and poet", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American wrestler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "1978", "text": "<PERSON>, German ice hockey player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter, producer, and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English journalist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Tunisian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tunisian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tunisian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Latvian basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/M%C4%81ris_%C4%BBaksa\" title=\"<PERSON><PERSON><PERSON> Ļaksa\"><PERSON><PERSON><PERSON> Ļaksa</a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C4%81ris_%C4%BBaksa\" title=\"<PERSON><PERSON><PERSON> Ļaksa\"><PERSON><PERSON><PERSON> Ļaksa</a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON> Ļaksa", "link": "https://wikipedia.org/wiki/M%C4%81ris_%C4%BBaksa"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Norwegian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>rten_Gamst_Pedersen\" title=\"Morten Gamst Pedersen\"><PERSON><PERSON> Gamst Pedersen</a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gamst_Pedersen\" title=\"Morten Gamst Pedersen\"><PERSON><PERSON> Gamst Pedersen</a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON> Gamst P<PERSON>rsen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian cartoonist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cartoonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Swiss footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Wali <PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>y"}]}, {"year": "1983", "text": "<PERSON>, American writer and conservative activist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and conservative activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and conservative activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>-<PERSON>, Australian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>-Thomson", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American writer and autism activist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and autism activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and autism activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Russian race car driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Austrian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_S%C3%A4umel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_S%C3%A4umel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCrgen_S%C3%A4umel"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>reichel\" title=\"Tia<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>reichel\" title=\"Tia<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>reichel"}]}, {"year": "1984", "text": "<PERSON>, English footballer (d. 2020)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Polish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jod%C5%82owiec\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jod%C5%82owiec\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>z_Jod%C5%82owiec"}]}, {"year": "1986", "text": "<PERSON>, Australian rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1986", "text": "<PERSON>, Colombian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Portuguese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>o"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Russian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian skier", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1987)\" title=\"<PERSON> (basketball, born 1987)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1987)\" title=\"<PERSON> (basketball, born 1987)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1987)"}]}, {"year": "1987", "text": "<PERSON>, Israeli high jumper", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American rapper and actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Wiz_<PERSON><PERSON>\" title=\"Wiz <PERSON>\"><PERSON><PERSON></a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wiz_<PERSON><PERSON>\" title=\"Wiz <PERSON>\"><PERSON><PERSON></a>, American rapper and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wiz_<PERSON><PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Ukrainian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German gymnast", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Arrelious_<PERSON>n\" title=\"Arrelious <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arrelious_<PERSON>n\" title=\"Arrelious <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arrelious_Benn"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Japanese swimmer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Icelandic footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_Sigur%C3%B<PERSON>son\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_Sigur%C3%B<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gylfi_Sigur%C3%B<PERSON>son"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Swedish electronic musician (d. 2018)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Avicii\" title=\"<PERSON>vic<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish electronic musician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avicii\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish electronic musician (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avicii"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, English cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Canadian actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n%C3%BD"}]}, {"year": "1990", "text": "<PERSON>, Turkish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Musa Nizam\"><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Musa Nizam\"><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, South African footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1991", "text": "<PERSON>, Mexican footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Espinoza\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Espinoza\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Espinoza"}]}, {"year": "1991", "text": "<PERSON>, British vlogger", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British vlogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British vlogger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Swiss ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>iter"}]}, {"year": "1992", "text": "<PERSON><PERSON>, German footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Za%27<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Za%27<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Za%27<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Australian cricketer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese rugby union player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese rugby union player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Italian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American internet personality", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cameron Dallas\"><PERSON></a>, American internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cameron Dallas\"><PERSON></a>, American internet personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cameron_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Portuguese footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Croatian-Bosnian handball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/%C4%86amila_Mi%C4%8Dijevi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-Bosnian handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%86amila_Mi%C4%8Dijevi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-Bosnian handball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%86amila_Mi%C4%8Dijevi%C4%87"}]}, {"year": "1995", "text": "<PERSON>, Canadian gymnast", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a>, Canadian gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Slovenian motocross racer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian motocross racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian motocross racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, German politician", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Brazilian race car driver", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Australian footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Singaporean footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American actor and singer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Gate<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gate<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gaten_<PERSON>razzo"}]}, {"year": "2003", "text": "<PERSON>, American actor and internet personality", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and internet personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, English cricketer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}], "Deaths": [{"year": "394", "text": "<PERSON><PERSON><PERSON><PERSON>, Frankish general", "html": "394 - <a href=\"https://wikipedia.org/wiki/Arbogast_(magister_militum)\" title=\"Arbogast (magister militum)\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arboga<PERSON>_(magister_militum)\" title=\"Arbogast (magister militum)\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish general", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (magister militum)", "link": "https://wikipedia.org/wiki/Arbogast_(magister_militum)"}]}, {"year": "701", "text": "<PERSON> <PERSON><PERSON><PERSON> (b. 650)", "html": "701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sergius_I\" title=\"Pope Sergius I\">Pope Sergius I</a> (b. 650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Sergius_I\" title=\"Pope Sergius I\">Pope Ser<PERSON>us I</a> (b. 650)", "links": [{"title": "<PERSON> Sergi<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_I"}]}, {"year": "780", "text": "<PERSON> <PERSON> the Khazar, Byzantine emperor (b. 750)", "html": "780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_the_Khazar\" title=\"<PERSON> IV the Khazar\"><PERSON> IV the Khazar</a>, Byzantine emperor (b. 750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_the_Khazar\" title=\"<PERSON> IV the Khazar\"><PERSON> IV the <PERSON>hazar</a>, Byzantine emperor (b. 750)", "links": [{"title": "<PERSON> the Khazar", "link": "https://wikipedia.org/wiki/<PERSON>_IV_the_Khazar"}]}, {"year": "869", "text": "<PERSON> ibn <PERSON><PERSON>, Muslim vizier", "html": "869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27il_al-Anbari\" title=\"<PERSON> ibn <PERSON>il al-Anbari\"><PERSON> ibn <PERSON></a>, Muslim <a href=\"https://wikipedia.org/wiki/Vizier\" title=\"Vizier\">vizier</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON>_al-Anbari\" title=\"<PERSON> ibn <PERSON>il al-Anbari\"><PERSON> ibn <PERSON></a>, Muslim <a href=\"https://wikipedia.org/wiki/Vizier\" title=\"Vizier\">vizier</a>", "links": [{"title": "<PERSON> ibn <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27<PERSON>_<PERSON>-<PERSON>"}, {"title": "Vizier", "link": "https://wikipedia.org/wiki/Vizier"}]}, {"year": "1100", "text": "<PERSON><PERSON><PERSON> (b. 1029)", "html": "1100 - <a href=\"https://wikipedia.org/wiki/Antipope_Clement_III\" title=\"Antipope Clement III\">Antipope Clement III</a> (b. 1029)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antipope_Clement_III\" title=\"Antipope Clement III\">Antipope Clement III</a> (b. 1029)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1306", "text": "Sir <PERSON>, Scottish knight, hung drawn and quartered by the English", "html": "1306 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(d._1306)\" class=\"mw-redirect\" title=\"<PERSON> (d. 1306)\">Sir <PERSON></a>, Scottish knight, hung drawn and quartered by the English", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(d._1306)\" class=\"mw-redirect\" title=\"<PERSON> (d. 1306)\">Sir <PERSON></a>, Scottish knight, hung drawn and quartered by the English", "links": [{"title": "<PERSON> (d. 1306)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(d._1306)"}]}, {"year": "1397", "text": "<PERSON> of Woodstock, 1st Duke of Gloucester, English politician, Lord High Constable of England (b. 1355)", "html": "1397 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock,_1st_Duke_of_Gloucester\" class=\"mw-redirect\" title=\"<PERSON> of Woodstock, 1st Duke of Gloucester\"><PERSON> of Woodstock, 1st Duke of Gloucester</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (b. 1355)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock,_1st_Duke_of_Gloucester\" class=\"mw-redirect\" title=\"<PERSON> of Woodstock, 1st Duke of Gloucester\"><PERSON> of Woodstock, 1st Duke of Gloucester</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (b. 1355)", "links": [{"title": "<PERSON> of Woodstock, 1st Duke of Gloucester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock,_1st_Duke_of_Gloucester"}, {"title": "Lord High Constable of England", "link": "https://wikipedia.org/wiki/Lord_High_Constable_of_England"}]}, {"year": "1425", "text": "<PERSON> of Navarre (b. 1361)", "html": "1425 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (b. 1361)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (b. 1361)", "links": [{"title": "<PERSON> of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Navarre"}]}, {"year": "1539", "text": "<PERSON>, English bishop (b. 1475)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1475)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1475)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1555", "text": "<PERSON> <PERSON> of Villanueva, Spanish bishop and saint (b. 1488)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Villanueva\" class=\"mw-redirect\" title=\"<PERSON> of Villanueva\">Saint <PERSON> of Villanueva</a>, Spanish bishop and saint (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_<PERSON>_of_Villanueva\" class=\"mw-redirect\" title=\"<PERSON> of Villanueva\">Saint <PERSON> of Villanueva</a>, Spanish bishop and saint (b. 1488)", "links": [{"title": "<PERSON> of Villanueva", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>eva"}]}, {"year": "1560", "text": "<PERSON>, English noblewoman (b. 1536)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (b. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (b. 1536)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1613", "text": "<PERSON>, Italian lute player and composer (b. 1566)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">lute</a> player and composer (b. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">lute</a> player and composer (b. 1566)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lute", "link": "https://wikipedia.org/wiki/Lute"}]}, {"year": "1637", "text": "<PERSON>, English physician, mathematician, and cosmologist (b. 1574)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, mathematician, and cosmologist (b. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, mathematician, and cosmologist (b. 1574)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1644", "text": "<PERSON>, English civil servant and politician (b. 1563)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and politician (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, English civil servant and politician (b. 1563)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1644", "text": "<PERSON>, English poet and author (b. 1592)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1592)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1645", "text": "<PERSON>, Spanish poet and politician (b. 1580)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>_Quevedo\" title=\"Francisco <PERSON> Quevedo\"><PERSON></a>, Spanish poet and politician (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>\" title=\"Francisco de Quevedo\"><PERSON></a>, Spanish poet and politician (b. 1580)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>"}]}, {"year": "1656", "text": "<PERSON>, English bishop (b. 1574)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (b. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (b. 1574)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop)"}]}, {"year": "1682", "text": "<PERSON>, Spanish mathematician and philosopher (b. 1606)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish mathematician and philosopher (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish mathematician and philosopher (b. 1606)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, Czech sculptor (b. 1686)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech sculptor (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech sculptor (b. 1686)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1755", "text": "<PERSON><PERSON><PERSON><PERSON>, American soldier and philanthropist (b. 1715)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American soldier and philanthropist (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American soldier and philanthropist (b. 1715)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON>, French mathematician and engineer (b. 1698)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9lidor\" title=\"<PERSON> de Bélidor\"><PERSON></a>, French mathematician and engineer (b. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_B%C3%A9lidor\" title=\"<PERSON> Bélidor\"><PERSON></a>, French mathematician and engineer (b. 1698)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernard_Forest_de_B%C3%A9lidor"}]}, {"year": "1780", "text": "<PERSON><PERSON>, American general (b. 1736)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/Enoch_Poor\" title=\"Enoch Poor\"><PERSON><PERSON></a>, American general (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Poor\" title=\"Enoch Poor\"><PERSON><PERSON></a>, American general (b. 1736)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enoch_Poor"}]}, {"year": "1784", "text": "<PERSON>, English-American religious leader (b. 1736)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American religious leader (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American religious leader (b. 1736)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, German zoologist and botanist (b. 1741)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German zoologist and botanist (b. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German zoologist and botanist (b. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON> of Austria, queen consort of Naples and Sicily (b. 1752)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Maria_Carolina_of_Austria\" title=\"Maria <PERSON> of Austria\"><PERSON> of Austria</a>, queen consort of Naples and Sicily (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_Carolina_of_Austria\" title=\"Maria Carolina of Austria\"><PERSON> of Austria</a>, queen consort of Naples and Sicily (b. 1752)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/Maria_Carolina_of_Austria"}]}, {"year": "1831", "text": "<PERSON>, Scottish-American publisher (b. 1745)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(music_publisher)\" title=\"<PERSON> (music publisher)\"><PERSON></a>, Scottish-American publisher (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(music_publisher)\" title=\"<PERSON> (music publisher)\"><PERSON></a>, Scottish-American publisher (b. 1745)", "links": [{"title": "<PERSON> (music publisher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(music_publisher)"}]}, {"year": "1853", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French scholar, co-founded the Society of Saint Vincent de <PERSON> (b. 1813)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Ozanam\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French scholar, co-founded the <a href=\"https://wikipedia.org/wiki/Society_of_Saint_<PERSON>_<PERSON>_<PERSON>\" title=\"Society of Saint Vincent de Paul\">Society of Saint Vincent de Paul</a> (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Ozanam\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French scholar, co-founded the <a href=\"https://wikipedia.org/wiki/Society_of_Saint_Vincent_<PERSON>_<PERSON>\" title=\"Society of Saint Vincent de Paul\">Society of Saint Vincent de Paul</a> (b. 1813)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Ozanam"}, {"title": "Society of Saint Vincent <PERSON>", "link": "https://wikipedia.org/wiki/Society_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Finnish priest and father of <PERSON><PERSON> <PERSON><PERSON>, the first President of Finland (b. 1832)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A5hlberg\" title=\"<PERSON>\"><PERSON></a>, Finnish priest and father of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A5hlberg\" title=\"<PERSON><PERSON><PERSON>\">K. J<PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A5hlberg\" title=\"<PERSON>\"><PERSON></a>, Finnish priest and father of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A5hlberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A5<PERSON>berg"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON>_Juho_St%C3%A5hlberg"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1882", "text": "<PERSON>, French mathematician and academic (b. 1809)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, German physician and physicist (b. 1821)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and physicist (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and physicist (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, German entrepreneur, founded <PERSON><PERSON> (b. 1837)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German entrepreneur, founded <a href=\"https://wikipedia.org/wiki/Opel\" title=\"Opel\">Opel</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adam Opel\"><PERSON></a>, German entrepreneur, founded <a href=\"https://wikipedia.org/wiki/Opel\" title=\"Opel\">Opel</a> (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Opel"}, {"title": "Opel", "link": "https://wikipedia.org/wiki/Opel"}]}, {"year": "1909", "text": "<PERSON><PERSON> <PERSON>, Irish tennis player (b. 1853)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Vere_St._Leger_Goold\" title=\"Vere St. Leger Goold\">Vere St. Leger Goold</a>, Irish tennis player (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vere_St._Leger_Goold\" title=\"Vere St. Leger Goold\">Vere St. Leger Goold</a>, Irish tennis player (b. 1853)", "links": [{"title": "Vere St. Leger Goold", "link": "https://wikipedia.org/wiki/Vere_St._Leger_Goold"}]}, {"year": "1912", "text": "<PERSON>, American motorcycle racer (b. 1890)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, German pianist, composer, and conductor (b. 1836)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON> Italian race car driver (b. 1885)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Italian race car driver (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Italian race car driver (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ugo_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON> of Iraq (b. 1883)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Faisal_I_of_Iraq\" title=\"Faisal I of Iraq\"><PERSON>aisal I of Iraq</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Faisal_I_of_Iraq\" title=\"Faisal I of Iraq\"><PERSON>aisal I of Iraq</a> (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON> I of Iraq", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Iraq"}]}, {"year": "1935", "text": "<PERSON>, American physician (b. 1906)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Finnish actor (b. 1863)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish actor (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish actor (b. 1863)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>o"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Turkish surgeon and politician (b. 1879)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/R%C4%B1za_Nur\" title=\"<PERSON><PERSON><PERSON> Nur\"><PERSON><PERSON><PERSON></a>, Turkish surgeon and politician (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C4%B1za_Nur\" title=\"<PERSON><PERSON><PERSON> Nur\"><PERSON><PERSON><PERSON></a>, Turkish surgeon and politician (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C4%B1za_Nur"}]}, {"year": "1943", "text": "<PERSON>, Czech journalist (b. 1903)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D%C3%<PERSON><PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Czech journalist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D%C3%<PERSON><PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Czech journalist (b. 1903)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/Julius_Fu%C4%8D%C3%<PERSON><PERSON>_(journalist)"}]}, {"year": "1944", "text": "<PERSON>, Dutch composer and conductor (b. 1881)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer and conductor (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer and conductor (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, German composer and manager (b. 1864)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and manager (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and manager (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, French painter and sculptor (b. 1880)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Derain"}]}, {"year": "1963", "text": "<PERSON>, English engineer and businessman (b. 1904)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress and singer (b. 1922)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (b. 1881)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1966", "text": "<PERSON>, American race car driver (b. 1933)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1933)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1969", "text": "<PERSON>, American game show host (b. 1908)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Belgian-French explorer and activist (b. 1868)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9el\" title=\"<PERSON>\"><PERSON></a>, Belgian-French explorer and activist (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9el\" title=\"<PERSON>\"><PERSON></a>, Belgian-French explorer and activist (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-N%C3%A9el"}]}, {"year": "1970", "text": "<PERSON>, American engineer, invented the microwave oven (b. 1894)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Microwave_oven\" title=\"Microwave oven\">microwave oven</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Microwave_oven\" title=\"Microwave oven\">microwave oven</a> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Microwave oven", "link": "https://wikipedia.org/wiki/Microwave_oven"}]}, {"year": "1974", "text": "<PERSON>, French-German tenor (b. 1914)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German tenor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German tenor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor and comedian (b. 1915)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mostel\"><PERSON></a>, American actor and comedian (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mostel\" title=\"<PERSON> Mostel\"><PERSON></a>, American actor and comedian (b. 1915)", "links": [{"title": "<PERSON>el", "link": "https://wikipedia.org/wiki/Zero_Mostel"}]}, {"year": "1980", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (b. 1908)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian guru, philosopher, and educator (b. 1897)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Nisarga<PERSON><PERSON>_Maharaj\" title=\"Nisargada<PERSON> Maharaj\"><PERSON><PERSON><PERSON><PERSON><PERSON> Maharaj</a>, Indian guru, philosopher, and educator (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nisarga<PERSON><PERSON>_Maharaj\" title=\"Nisargada<PERSON> Maharaj\"><PERSON><PERSON><PERSON><PERSON><PERSON> Maharaj</a>, Indian guru, philosopher, and educator (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Maharaj", "link": "https://wikipedia.org/wiki/Nisargada<PERSON>_Maharaj"}]}, {"year": "1981", "text": "<PERSON>, American journalist and activist (b. 1901)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Japanese physicist and academic, Nobel Prize laureate (b. 1907)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1983", "text": "<PERSON><PERSON>, French cyclist (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cyclist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cyclist (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American race car driver (b. 1918)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American virologist and academic, Nobel Prize laureate (b. 1887)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English author and illustrator (b. 1905)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and illustrator (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and illustrator (b. 1905)", "links": [{"title": "<PERSON><PERSON>-Pitchford", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>ford"}]}, {"year": "1991", "text": "<PERSON>, American composer and conductor (b. 1910)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Alex_North\" title=\"Alex North\"><PERSON></a>, American composer and conductor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alex_North\" title=\"Alex North\"><PERSON></a>, American composer and conductor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_North"}]}, {"year": "1991", "text": "<PERSON>, American actor (b. 1949)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1949)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1997", "text": "<PERSON>, English journalist and author (b. 1932)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American-German singer-songwriter, drummer, and poet (b. 1916)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>dog\" title=\"Moondog\"><PERSON><PERSON></a>, American-German singer-songwriter, drummer, and poet (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-German singer-songwriter, drummer, and poet (b. 1916)", "links": [{"title": "<PERSON>dog", "link": "https://wikipedia.org/wiki/<PERSON>dog"}]}, {"year": "2001", "text": "<PERSON>, Canadian entomologist and author (b. 1908)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian entomologist and author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian entomologist and author (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Jamaican cricketer (b. 1968)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Jamaican cricketer (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Jamaican cricketer (b. 1968)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "2003", "text": "<PERSON><PERSON>, German actress, director, producer, and screenwriter (b. 1902)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress, director, producer, and screenwriter (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress, director, producer, and screenwriter (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_R<PERSON>tahl"}]}, {"year": "2004", "text": "<PERSON>, American animator, voice actor, and screenwriter (b. 1913)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, American animator, voice actor, and screenwriter (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, American animator, voice actor, and screenwriter (b. 1913)", "links": [{"title": "<PERSON> (animator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(animator)"}]}, {"year": "2005", "text": "<PERSON>, Irish cricketer, footballer, and manager (b. 1932)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cricketer, footballer, and manager (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cricketer, footballer, and manager (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Australian journalist, author, and critic (b. 1921)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, author, and critic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, author, and critic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English-South African author and activist (b. 1915)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-South African author and activist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-South African author and activist (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Australian race car driver and sportscaster (b. 1945)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and sportscaster (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and sportscaster (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Australian ornithologist, conservationist, and author (b. 1916)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian ornithologist, conservationist, and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian ornithologist, conservationist, and author (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American explorer (b. 1927)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Danish physicist and academic, Nobel Prize laureate (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bohr\"><PERSON><PERSON></a>, Danish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bohr\"><PERSON><PERSON></a>, Danish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hr"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2009", "text": "<PERSON>, American-Italian television host (b. 1924)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian television host (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian television host (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian historian and academic (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and academic (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and academic (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English-American designer, author, and educator, co-founded IDEO (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American designer, author, and educator, co-founded <a href=\"https://wikipedia.org/wiki/IDEO\" title=\"IDEO\">IDE<PERSON></a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American designer, author, and educator, co-founded <a href=\"https://wikipedia.org/wiki/IDEO\" title=\"IDEO\">IDE<PERSON></a> (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "IDEO", "link": "https://wikipedia.org/wiki/IDEO"}]}, {"year": "2012", "text": "<PERSON>, Hungarian-American psychiatrist and academic (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American psychiatrist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American psychiatrist and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Go<PERSON>ulin\" title=\"<PERSON> Gonsoulin\"><PERSON></a>, American football player (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ulin\" title=\"<PERSON> Gonsoulin\"><PERSON></a>, American football player (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian painter and photographer (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and photographer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and photographer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, French linguist, computer scientist, and blogger (b. 1955)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Jean_<PERSON>%C3%A9ron<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French linguist, computer scientist, and blogger (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jean_<PERSON>%C3%A9ron<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French linguist, computer scientist, and blogger (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jean_V%C3%A9ronis"}]}, {"year": "2014", "text": "<PERSON>, American basketball player (b. 1952)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American businessman, founded Chick-fil-A (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Chick-fil-A\" title=\"Chick-fil-A\">Chick-fil-A</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Chick-fil-A\" title=\"Chick-fil-A\">Chick-fil-A</a> (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Chick-fil-A", "link": "https://wikipedia.org/wiki/Chick-fil-A"}]}, {"year": "2014", "text": "<PERSON>, American wrestler, mixed martial artist, and kick-boxer (b. 1971)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Haire\" title=\"<PERSON>\"><PERSON></a>, American wrestler, mixed martial artist, and kick-boxer (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Haire\" title=\"<PERSON>\"><PERSON></a>, American wrestler, mixed martial artist, and kick-boxer (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sean_O%27Haire"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Italian soprano (b. 1910)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Mag<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian soprano (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian soprano (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mag<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American trumpet player and composer (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Dominican baseball player (b. 1952)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_And%C3%BAjar\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_And%C3%BAjar\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_And%C3%BAjar"}]}, {"year": "2015", "text": "<PERSON>, American political scientist and academic (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American football player (b. 1988)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Dutch author and poet (b. 1963)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author and poet (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author and poet (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Austrian race pilot (b. 1967)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Hannes_Arch\" title=\"Hannes Arch\">Hannes Arch</a>, Austrian race pilot (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hannes_Arch\" title=\"Hannes Arch\"><PERSON><PERSON> Arch</a>, Austrian race pilot (b. 1967)", "links": [{"title": "Hannes Arch", "link": "https://wikipedia.org/wiki/Hannes_Arch"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Montenegrin politician, 5th Prime Minister of the Federal Republic of Yugoslavia (b. 1954)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Dragi%C5%A1a_Pe%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Montenegrin politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Serbia_and_Montenegro\" title=\"Prime Minister of Serbia and Montenegro\">Prime Minister of the Federal Republic of Yugoslavia</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dragi%C5%A1a_Pe%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Montenegrin politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Serbia_and_Montenegro\" title=\"Prime Minister of Serbia and Montenegro\">Prime Minister of the Federal Republic of Yugoslavia</a> (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dragi%C5%A1a_Pe%C5%A1i%C4%87"}, {"title": "Prime Minister of Serbia and Montenegro", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Serbia_and_Montenegro"}]}, {"year": "2016", "text": "<PERSON>, Jamaican singer-songwriter and producer (b. 1938)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Prince Buster\"><PERSON></a>, Jamaican singer-songwriter and producer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Buster\" title=\"Prince Buster\"><PERSON></a>, Jamaican singer-songwriter and producer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, French businessman (b. 1930)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French businessman (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French businessman (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "2017", "text": "<PERSON>, American actor (b. 1982)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "2017", "text": "<PERSON>, American author and journalist (b. 1933)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian actor and director (b. 1936)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Ljubi%C5%A1a_Samard%C5%BEi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian actor and director (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ljubi%C5%A1a_Samard%C5%BEi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian actor and director (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ljubi%C5%A1a_Samard%C5%BEi%C4%87"}]}, {"year": "2017", "text": "<PERSON>, American musician (b. 1939)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, Prime Minister of Abkhazia (b. 1948)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Gennadi_Gagulia\" title=\"Gennadi Gagulia\"><PERSON><PERSON><PERSON></a>, Prime Minister of Abkhazia (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gennadi_Gagulia\" title=\"Gennadi Gagulia\"><PERSON><PERSON><PERSON></a>, Prime Minister of Abkhazia (b. 1948)", "links": [{"title": "Gennadi <PERSON>", "link": "https://wikipedia.org/wiki/Gennadi_Gagulia"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, American singer and beauty pageant winner (b. 1973)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and beauty pageant winner (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and beauty pageant winner (b. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Indian cinematographer, film director, and actor (b. 1957)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cinematographer, film director, and actor (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cinematographer, film director, and actor (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Queen of the United Kingdom and other Commonwealth realms (b. 1926)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a>, Queen of the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> and other <a href=\"https://wikipedia.org/wiki/Commonwealth_realm\" title=\"Commonwealth realm\">Commonwealth realms</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Elizabeth II\"><PERSON> II</a>, Queen of the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> and other <a href=\"https://wikipedia.org/wiki/Commonwealth_realm\" title=\"Commonwealth realm\">Commonwealth realms</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Commonwealth realm", "link": "https://wikipedia.org/wiki/Commonwealth_realm"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, English actress (b. 1946)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gwyneth_Powell"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Salvadoran police officer (b. 1964)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aza_Chicas\" title=\"<PERSON><PERSON><PERSON> Chicas\"><PERSON><PERSON><PERSON></a>, Salvadoran police officer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>az<PERSON>_Chicas\" title=\"<PERSON><PERSON><PERSON> Chicas\"><PERSON><PERSON><PERSON></a>, Salvadoran police officer (b. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aza_Chicas"}]}, {"year": "2024", "text": "<PERSON>, American baseball player (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ed_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Norwegian actress (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/He<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian actress (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian actress (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, English musician (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Zoot_Money\" title=\"Zoot Money\"><PERSON><PERSON></a>, English musician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zoot_Money\" title=\"Zoot Money\"><PERSON><PERSON></a>, English musician (b. 1942)", "links": [{"title": "Zoot Money", "link": "https://wikipedia.org/wiki/Zoot_Money"}]}, {"year": "2024", "text": "<PERSON>, American voice actor (b. 1935)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Japanese voice actress and singer (b. 1963)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}]}}