{"date": "November 5", "url": "https://wikipedia.org/wiki/November_5", "data": {"Events": [{"year": "1138", "text": "<PERSON><PERSON> <PERSON> is enthroned as emperor of Vietnam at the age of two, beginning a 37-year reign.", "html": "1138 - <a href=\"https://wikipedia.org/wiki/L%C3%BD_Anh_T%C3%B4ng\" title=\"Lý Anh Tông\"><PERSON><PERSON> <PERSON><PERSON> Tông</a> is enthroned as emperor of <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> at the age of two, beginning a 37-year reign.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%BD_Anh_T%C3%B4ng\" title=\"Lý Anh Tông\"><PERSON><PERSON> <PERSON><PERSON> Tông</a> is enthroned as emperor of <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> at the age of two, beginning a 37-year reign.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%BD_Anh_T%C3%B4ng"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1499", "text": "The Catholicon, written in 1464 by <PERSON><PERSON> in Tréguier, is published; this is the first Breton dictionary as well as the first French dictionary.", "html": "1499 - The <i><a href=\"https://wikipedia.org/wiki/Catholicon_(trilingual_dictionary)\" title=\"Catholicon (trilingual dictionary)\">Catholicon</a></i>, written in 1464 by <PERSON><PERSON> in <a href=\"https://wikipedia.org/wiki/Tr%C3%A9guier\" title=\"Tréguier\">T<PERSON>guier</a>, is published; this is the first <a href=\"https://wikipedia.org/wiki/Breton_language\" title=\"Breton language\">Breton</a> dictionary as well as the first French dictionary.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Catholicon_(trilingual_dictionary)\" title=\"Catholicon (trilingual dictionary)\">Catholicon</a></i>, written in 1464 by <PERSON><PERSON> in <a href=\"https://wikipedia.org/wiki/Tr%C3%A9guier\" title=\"Tréguier\">T<PERSON>guier</a>, is published; this is the first <a href=\"https://wikipedia.org/wiki/Breton_language\" title=\"Breton language\">Breton</a> dictionary as well as the first French dictionary.", "links": [{"title": "Catholicon (trilingual dictionary)", "link": "https://wikipedia.org/wiki/Catholicon_(trilingual_dictionary)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%C3%A9guier"}, {"title": "Breton language", "link": "https://wikipedia.org/wiki/Breton_language"}]}, {"year": "1556", "text": "Second Battle of Panipat: Fighting begins between the forces of <PERSON><PERSON>, the Hindu king at Delhi and the forces of the Muslim emperor <PERSON>.", "html": "1556 - <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Panipat\" title=\"Second Battle of Panipat\">Second Battle of Panipat</a>: Fighting begins between the forces of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the Hindu king at Delhi and the forces of the Muslim emperor <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Battle_of_Panipat\" title=\"Second Battle of Panipat\">Second Battle of Panipat</a>: Fighting begins between the forces of <a href=\"https://wikipedia.org/wiki/He<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the Hindu king at Delhi and the forces of the Muslim emperor <PERSON>.", "links": [{"title": "Second Battle of Panipat", "link": "https://wikipedia.org/wiki/Second_Battle_of_Panipat"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "Gunpowder Plot: <PERSON> is arrested in the cellars of the Houses of Parliament, where he had planted gunpowder in an attempt to blow up the building and kill King <PERSON> of England.", "html": "1605 - <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested in the cellars of the Houses of Parliament, where he had planted gunpowder in an attempt to blow up the building and kill <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> I of England\"><PERSON> of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested in the cellars of the Houses of Parliament, where he had planted gunpowder in an attempt to blow up the building and kill King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> I of England\"><PERSON> of England</a>.", "links": [{"title": "Gunpowder Plot", "link": "https://wikipedia.org/wiki/Gunpowder_Plot"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1688", "text": "Prince <PERSON> of Orange lands with a Dutch fleet at Brixham to challenge the rule of King <PERSON> of England (<PERSON> of Scotland).", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\">Prince <PERSON> of Orange</a> lands with a Dutch fleet at <a href=\"https://wikipedia.org/wiki/Brixham\" title=\"Brixham\"><PERSON><PERSON><PERSON></a> to challenge the rule of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"James II of England\"><PERSON> of England</a> (<PERSON> of Scotland).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\">Prince <PERSON> of Orange</a> lands with a Dutch fleet at <a href=\"https://wikipedia.org/wiki/Brixham\" title=\"Brixham\"><PERSON><PERSON><PERSON></a> to challenge the rule of King <a href=\"https://wikipedia.org/wiki/James_II_of_England\" title=\"James II of England\"><PERSON> of England</a> (<PERSON> VII of Scotland).", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ham"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1757", "text": "Seven Years' War: <PERSON> the Great defeats the allied armies of France and the Holy Roman Empire at the Battle of Rossbach.", "html": "1757 - <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> defeats the allied armies of France and the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Rossbach\" title=\"Battle of Rossbach\">Battle of Rossbach</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> defeats the allied armies of France and the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Rossbach\" title=\"Battle of Rossbach\">Battle of Rossbach</a>.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "Battle of Rossbach", "link": "https://wikipedia.org/wiki/Battle_of_Rossbach"}]}, {"year": "1768", "text": "The Treaty of Fort Stanwix is signed, the purpose of which is to adjust the boundary line between Indian lands and white settlements set forth in the Royal Proclamation of 1763 in the Thirteen Colonies.", "html": "1768 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Fort_Stanwix\" class=\"mw-redirect\" title=\"Treaty of Fort Stanwix\">Treaty of Fort Stanwix</a> is signed, the purpose of which is to adjust the boundary line between Indian lands and white settlements set forth in the <a href=\"https://wikipedia.org/wiki/Royal_Proclamation_of_1763\" title=\"Royal Proclamation of 1763\">Royal Proclamation of 1763</a> in the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Fort_Stanwix\" class=\"mw-redirect\" title=\"Treaty of Fort Stanwix\">Treaty of Fort Stanwix</a> is signed, the purpose of which is to adjust the boundary line between Indian lands and white settlements set forth in the <a href=\"https://wikipedia.org/wiki/Royal_Proclamation_of_1763\" title=\"Royal Proclamation of 1763\">Royal Proclamation of 1763</a> in the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a>.", "links": [{"title": "Treaty of Fort Stanwix", "link": "https://wikipedia.org/wiki/Treaty_of_Fort_Stanwix"}, {"title": "Royal Proclamation of 1763", "link": "https://wikipedia.org/wiki/Royal_Proclamation_of_1763"}, {"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}]}, {"year": "1780", "text": "French-American forces under Colonel <PERSON><PERSON> are defeated by Miami Chief <PERSON>.", "html": "1780 - French-American forces under <a href=\"https://wikipedia.org/wiki/August<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\">Colonel <PERSON><PERSON><PERSON><PERSON></a> are <a href=\"https://wikipedia.org/wiki/<PERSON>_Balme%27s_Defeat\" title=\"<PERSON>'s Defeat\">defeated</a> by Miami Chief <a href=\"https://wikipedia.org/wiki/Little_Turtle\" title=\"Little Turtle\">Little Turtle</a>.", "no_year_html": "French-American forces under <a href=\"https://wikipedia.org/wiki/August<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\">Colonel <PERSON><PERSON><PERSON><PERSON></a> are <a href=\"https://wikipedia.org/wiki/La_Balme%27s_Defeat\" title=\"<PERSON>'s Defeat\">defeated</a> by Miami Chief <a href=\"https://wikipedia.org/wiki/Little_Turtle\" title=\"Little Turtle\"><PERSON> Turtle</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "La Balme's Defeat", "link": "https://wikipedia.org/wiki/La_Balme%27s_Defeat"}, {"title": "Little Turtle", "link": "https://wikipedia.org/wiki/<PERSON>_Turtle"}]}, {"year": "1811", "text": "Salvadoran priest <PERSON> rings the bells of La Merced church in San Salvador, calling for insurrection and launching the 1811 Independence Movement.", "html": "1811 - Salvadoran priest <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mat%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> rings the bells of La Merced church in <a href=\"https://wikipedia.org/wiki/San_Salvador\" title=\"San Salvador\">San Salvador</a>, calling for insurrection and launching the <a href=\"https://wikipedia.org/wiki/1811_Independence_Movement\" title=\"1811 Independence Movement\">1811 Independence Movement</a>.", "no_year_html": "Salvadoran priest <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mat%C3%ADas_<PERSON>\" title=\"<PERSON>\"><PERSON></a> rings the bells of La Merced church in <a href=\"https://wikipedia.org/wiki/San_Salvador\" title=\"San Salvador\">San Salvador</a>, calling for insurrection and launching the <a href=\"https://wikipedia.org/wiki/1811_Independence_Movement\" title=\"1811 Independence Movement\">1811 Independence Movement</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mat%C3%ADas_Delgado"}, {"title": "San Salvador", "link": "https://wikipedia.org/wiki/San_Salvador"}, {"title": "1811 Independence Movement", "link": "https://wikipedia.org/wiki/1811_Independence_Movement"}]}, {"year": "1828", "text": "Greek War of Independence: The French Morea expedition to recapture Morea (now the Peloponnese) ends when the last Ottoman forces depart the peninsula.", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: The French <a href=\"https://wikipedia.org/wiki/Morea_expedition\" title=\"Morea expedition\">Morea expedition</a> to recapture Morea (now the <a href=\"https://wikipedia.org/wiki/Peloponnese\" title=\"Peloponnese\">Peloponnese</a>) ends when the last Ottoman forces depart the peninsula.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: The French <a href=\"https://wikipedia.org/wiki/Morea_expedition\" title=\"Morea expedition\">Morea expedition</a> to recapture Morea (now the <a href=\"https://wikipedia.org/wiki/Peloponnese\" title=\"Peloponnese\">Peloponnese</a>) ends when the last Ottoman forces depart the peninsula.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "Morea expedition", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_expedition"}, {"title": "Peloponnese", "link": "https://wikipedia.org/wiki/Peloponnese"}]}, {"year": "1834", "text": "Founding of the Free University of Brussels by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "html": "1834 - Founding of the <a href=\"https://wikipedia.org/wiki/Free_University_of_Brussels_(1834%E2%80%931969)\" title=\"Free University of Brussels (1834-1969)\">Free University of Brussels</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>h%C3%A9od<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "Founding of the <a href=\"https://wikipedia.org/wiki/Free_University_of_Brussels_(1834%E2%80%931969)\" title=\"Free University of Brussels (1834-1969)\">Free University of Brussels</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>-Th%C3%A9od<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Free University of Brussels (1834-1969)", "link": "https://wikipedia.org/wiki/Free_University_of_Brussels_(1834%E2%80%931969)"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pierre-Th%C3%A9odore_<PERSON>n"}]}, {"year": "1862", "text": "American Civil War: <PERSON> removes <PERSON> as commander of the Army of the Potomac.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> removes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as commander of the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> removes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as commander of the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Army of the Potomac", "link": "https://wikipedia.org/wiki/Army_of_the_Potomac"}]}, {"year": "1862", "text": "American Indian Wars: In Minnesota, 303 Dakota warriors are found guilty of rape and murder of whites and are sentenced to death. Thirty-eight are ultimately hanged and the others reprieved.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: In <a href=\"https://wikipedia.org/wiki/Minnesota\" title=\"Minnesota\">Minnesota</a>, 303 <a href=\"https://wikipedia.org/wiki/Dakota_people\" title=\"Dakota people\">Dakota</a> warriors are found guilty of rape and murder of whites and are sentenced to death. Thirty-eight are ultimately hanged and the others reprieved.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: In <a href=\"https://wikipedia.org/wiki/Minnesota\" title=\"Minnesota\">Minnesota</a>, 303 <a href=\"https://wikipedia.org/wiki/Dakota_people\" title=\"Dakota people\">Dakota</a> warriors are found guilty of rape and murder of whites and are sentenced to death. Thirty-eight are ultimately hanged and the others reprieved.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Minnesota", "link": "https://wikipedia.org/wiki/Minnesota"}, {"title": "Dakota people", "link": "https://wikipedia.org/wiki/Dakota_people"}]}, {"year": "1872", "text": "Women's suffrage in the United States: In defiance of the law, suffragist <PERSON> votes for the first time, and is later fined $100.", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States\" title=\"Women's suffrage in the United States\">Women's suffrage in the United States</a>: In defiance of the law, suffragist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> votes for the first time, and is later fined $100.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States\" title=\"Women's suffrage in the United States\">Women's suffrage in the United States</a>: In defiance of the law, suffragist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> votes for the first time, and is later fined $100.", "links": [{"title": "Women's suffrage in the United States", "link": "https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "In New Zealand, 1600 armed volunteers and constabulary field forces led by Minister of Native Affairs <PERSON> march on the pacifist Māori settlement at Parihaka, evicting upwards of 2000 residents, and destroying the settlement in the context of the New Zealand land confiscations.", "html": "1881 - In <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>, 1600 armed volunteers and constabulary field forces led by Minister of Native Affairs <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> march on the pacifist <a href=\"https://wikipedia.org/wiki/M%C4%81ori_people\" title=\"Māori people\">Māori</a> settlement at <a href=\"https://wikipedia.org/wiki/Parihaka\" title=\"Parihaka\">Parihaka</a>, evicting upwards of 2000 residents, and destroying the settlement in the context of the <a href=\"https://wikipedia.org/wiki/New_Zealand_land_confiscations\" title=\"New Zealand land confiscations\">New Zealand land confiscations</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>, 1600 armed volunteers and constabulary field forces led by Minister of Native Affairs <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> march on the pacifist <a href=\"https://wikipedia.org/wiki/M%C4%81ori_people\" title=\"Māori people\">Māori</a> settlement at <a href=\"https://wikipedia.org/wiki/Parihaka\" title=\"Parihaka\">Parihaka</a>, evicting upwards of 2000 residents, and destroying the settlement in the context of the <a href=\"https://wikipedia.org/wiki/New_Zealand_land_confiscations\" title=\"New Zealand land confiscations\">New Zealand land confiscations</a>.", "links": [{"title": "New Zealand", "link": "https://wikipedia.org/wiki/New_Zealand"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Māori people", "link": "https://wikipedia.org/wiki/M%C4%81ori_people"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pa<PERSON>haka"}, {"title": "New Zealand land confiscations", "link": "https://wikipedia.org/wiki/New_Zealand_land_confiscations"}]}, {"year": "1895", "text": "<PERSON> is granted the first U.S. patent for an automobile.", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is granted the first U.S. <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for an automobile.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is granted the first U.S. <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for an automobile.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}]}, {"year": "1898", "text": "Negrese nationalists revolt against Spanish rule and establish the short-lived Republic of Negros.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Negrenses\" title=\"Negrenses\">Negrese</a> nationalists <a href=\"https://wikipedia.org/wiki/Negros_Revolution\" title=\"Negros Revolution\">revolt</a> against Spanish rule and establish the short-lived <a href=\"https://wikipedia.org/wiki/Republic_of_Negros\" title=\"Republic of Negros\">Republic of Negros</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Negrenses\" title=\"Negrenses\">Negrese</a> nationalists <a href=\"https://wikipedia.org/wiki/Negros_Revolution\" title=\"Negros Revolution\">revolt</a> against Spanish rule and establish the short-lived <a href=\"https://wikipedia.org/wiki/Republic_of_Negros\" title=\"Republic of Negros\">Republic of Negros</a>.", "links": [{"title": "Negrenses", "link": "https://wikipedia.org/wiki/Negrenses"}, {"title": "Negros Revolution", "link": "https://wikipedia.org/wiki/Negros_Revolution"}, {"title": "Republic of Negros", "link": "https://wikipedia.org/wiki/Republic_of_Negros"}]}, {"year": "1911", "text": "After declaring war on the Ottoman Empire on September 29, 1911, Italy annexes Tripoli and Cyrenaica.", "html": "1911 - After declaring war on the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> on <a href=\"https://wikipedia.org/wiki/September_29\" title=\"September 29\">September 29</a>, 1911, Italy annexes <a href=\"https://wikipedia.org/wiki/Tripoli,_Libya\" title=\"Tripoli, Libya\">Tripoli</a> and <a href=\"https://wikipedia.org/wiki/Cyrenaica\" title=\"Cyrenaica\">Cyrenaica</a>.", "no_year_html": "After declaring war on the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> on <a href=\"https://wikipedia.org/wiki/September_29\" title=\"September 29\">September 29</a>, 1911, Italy annexes <a href=\"https://wikipedia.org/wiki/Tripoli,_Libya\" title=\"Tripoli, Libya\">Tripoli</a> and <a href=\"https://wikipedia.org/wiki/Cyrenaica\" title=\"Cyrenaica\">Cyrenaica</a>.", "links": [{"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "September 29", "link": "https://wikipedia.org/wiki/September_29"}, {"title": "Tripoli, Libya", "link": "https://wikipedia.org/wiki/Tripoli,_Libya"}, {"title": "Cyrenaica", "link": "https://wikipedia.org/wiki/Cyrenaica"}]}, {"year": "1912", "text": "<PERSON> is elected the 28th President of the United States, defeating incumbent <PERSON>.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1912_United_States_presidential_election\" title=\"1912 United States presidential election\">elected</a> the 28th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, defeating incumbent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1912_United_States_presidential_election\" title=\"1912 United States presidential election\">elected</a> the 28th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, defeating incumbent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "1912 United States presidential election", "link": "https://wikipedia.org/wiki/1912_United_States_presidential_election"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "King <PERSON> of Bavaria is deposed by his cousin, Prince <PERSON>, who assumes the title <PERSON>.", "html": "1913 - King <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Bavaria\" title=\"<PERSON>, King of Bavaria\"><PERSON> of Bavaria</a> is deposed by his cousin, Prince <PERSON>, who assumes the title <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> III</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Bavaria\" title=\"<PERSON>, King of Bavaria\"><PERSON> of Bavaria</a> is deposed by his cousin, Prince <PERSON>, who assumes the title <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bavaria\" title=\"<PERSON> III of Bavaria\"><PERSON> III</a>.", "links": [{"title": "<PERSON>, King of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_Bavaria"}, {"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Ludwig_III_of_Bavaria"}]}, {"year": "1914", "text": "World War I: France and the British Empire declare war on the Ottoman Empire.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">France</a> and the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a> declare war on the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">France</a> and the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a> declare war on the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "French Third Republic", "link": "https://wikipedia.org/wiki/French_Third_Republic"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1916", "text": "The Kingdom of Poland is proclaimed by the Act of 5th November of the emperors of Germany and Austria-Hungary.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Poland_(1917%E2%80%9318)\" class=\"mw-redirect\" title=\"Kingdom of Poland (1917-18)\">Kingdom of Poland</a> is proclaimed by the <a href=\"https://wikipedia.org/wiki/Act_of_5th_November\" title=\"Act of 5th November\">Act of 5th November</a> of the emperors of Germany and <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Poland_(1917%E2%80%9318)\" class=\"mw-redirect\" title=\"Kingdom of Poland (1917-18)\">Kingdom of Poland</a> is proclaimed by the <a href=\"https://wikipedia.org/wiki/Act_of_5th_November\" title=\"Act of 5th November\">Act of 5th November</a> of the emperors of Germany and <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>.", "links": [{"title": "Kingdom of Poland (1917-18)", "link": "https://wikipedia.org/wiki/Kingdom_of_Poland_(1917%E2%80%9318)"}, {"title": "Act of 5th November", "link": "https://wikipedia.org/wiki/Act_of_5th_November"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}]}, {"year": "1916", "text": "The Everett massacre takes place in Everett, Washington as political differences lead to a shoot-out between the Industrial Workers of the World organizers and local police.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/Everett_massacre\" title=\"Everett massacre\">Everett massacre</a> takes place in <a href=\"https://wikipedia.org/wiki/Everett,_Washington\" title=\"Everett, Washington\">Everett, Washington</a> as political differences lead to a shoot-out between the <a href=\"https://wikipedia.org/wiki/Industrial_Workers_of_the_World\" title=\"Industrial Workers of the World\">Industrial Workers of the World</a> organizers and local police.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Everett_massacre\" title=\"Everett massacre\">Everett massacre</a> takes place in <a href=\"https://wikipedia.org/wiki/Everett,_Washington\" title=\"Everett, Washington\">Everett, Washington</a> as political differences lead to a shoot-out between the <a href=\"https://wikipedia.org/wiki/Industrial_Workers_of_the_World\" title=\"Industrial Workers of the World\">Industrial Workers of the World</a> organizers and local police.", "links": [{"title": "Everett massacre", "link": "https://wikipedia.org/wiki/Everett_massacre"}, {"title": "Everett, Washington", "link": "https://wikipedia.org/wiki/Everett,_Washington"}, {"title": "Industrial Workers of the World", "link": "https://wikipedia.org/wiki/Industrial_Workers_of_the_World"}]}, {"year": "1917", "text": "Lenin calls for the October Revolution.", "html": "1917 - Lenin calls for the <a href=\"https://wikipedia.org/wiki/October_Revolution\" title=\"October Revolution\">October Revolution</a>.", "no_year_html": "Lenin calls for the <a href=\"https://wikipedia.org/wiki/October_Revolution\" title=\"October Revolution\">October Revolution</a>.", "links": [{"title": "October Revolution", "link": "https://wikipedia.org/wiki/October_Revolution"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON> is elected the Patriarch of Moscow and of the Russian Orthodox Church.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON><PERSON>_of_Moscow\" title=\"Patriarch T<PERSON><PERSON> of Moscow\"><PERSON><PERSON><PERSON></a> is elected the <a href=\"https://wikipedia.org/wiki/Patriarch_of_Moscow\" class=\"mw-redirect\" title=\"Patriarch of Moscow\">Patriarch of Moscow</a> and of the <a href=\"https://wikipedia.org/wiki/Russian_Orthodox_Church\" title=\"Russian Orthodox Church\">Russian Orthodox Church</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patriarch_T<PERSON><PERSON>_of_Moscow\" title=\"Patriarch T<PERSON><PERSON> of Moscow\"><PERSON><PERSON><PERSON></a> is elected the <a href=\"https://wikipedia.org/wiki/Patriarch_of_Moscow\" class=\"mw-redirect\" title=\"Patriarch of Moscow\">Patriarch of Moscow</a> and of the <a href=\"https://wikipedia.org/wiki/Russian_Orthodox_Church\" title=\"Russian Orthodox Church\">Russian Orthodox Church</a>.", "links": [{"title": "Patriarch <PERSON><PERSON><PERSON> of Moscow", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_of_Moscow"}, {"title": "Patriarch of Moscow", "link": "https://wikipedia.org/wiki/Patriarch_of_Moscow"}, {"title": "Russian Orthodox Church", "link": "https://wikipedia.org/wiki/Russian_Orthodox_Church"}]}, {"year": "1925", "text": "Secret agent <PERSON>, the first \"super-spy\" of the 20th century, is executed by the OGPU, the secret police of the Soviet Union.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Secret_agent\" class=\"mw-redirect\" title=\"Secret agent\">Secret agent</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first \"super-spy\" of the 20th century, is executed by the <a href=\"https://wikipedia.org/wiki/State_Political_Directorate\" title=\"State Political Directorate\">OGPU</a>, the <a href=\"https://wikipedia.org/wiki/Secret_police\" title=\"Secret police\">secret police</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Secret_agent\" class=\"mw-redirect\" title=\"Secret agent\">Secret agent</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first \"super-spy\" of the 20th century, is executed by the <a href=\"https://wikipedia.org/wiki/State_Political_Directorate\" title=\"State Political Directorate\">OGPU</a>, the <a href=\"https://wikipedia.org/wiki/Secret_police\" title=\"Secret police\">secret police</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Secret agent", "link": "https://wikipedia.org/wiki/Secret_agent"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "State Political Directorate", "link": "https://wikipedia.org/wiki/State_Political_Directorate"}, {"title": "Secret police", "link": "https://wikipedia.org/wiki/Secret_police"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1940", "text": "World War II: The British armed merchant cruiser HMS Jervis Bay is sunk by the German pocket battleship Admiral <PERSON><PERSON><PERSON>.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The British <a href=\"https://wikipedia.org/wiki/Armed_merchant_cruiser\" class=\"mw-redirect\" title=\"Armed merchant cruiser\">armed merchant cruiser</a> <a href=\"https://wikipedia.org/wiki/HMS_Jervis_Bay\" title=\"HMS Jervis Bay\">HMS <i>Jervis Bay</i></a> is sunk by the German <a href=\"https://wikipedia.org/wiki/Pocket_battleship\" class=\"mw-redirect\" title=\"Pocket battleship\">pocket battleship</a> <a href=\"https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>\" title=\"German cruiser Admiral <PERSON>\"><i>Admiral <PERSON><PERSON></i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The British <a href=\"https://wikipedia.org/wiki/Armed_merchant_cruiser\" class=\"mw-redirect\" title=\"Armed merchant cruiser\">armed merchant cruiser</a> <a href=\"https://wikipedia.org/wiki/HMS_Jervis_Bay\" title=\"HMS Jervis Bay\">HMS <i>Jervis Bay</i></a> is sunk by the German <a href=\"https://wikipedia.org/wiki/Pocket_battleship\" class=\"mw-redirect\" title=\"Pocket battleship\">pocket battleship</a> <a href=\"https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>\" title=\"German cruiser Admiral <PERSON>\"><i>Admiral <PERSON></i></a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Armed merchant cruiser", "link": "https://wikipedia.org/wiki/Armed_merchant_cruiser"}, {"title": "HMS Jervis Bay", "link": "https://wikipedia.org/wiki/HMS_Jervis_Bay"}, {"title": "Pocket battleship", "link": "https://wikipedia.org/wiki/Pocket_battleship"}, {"title": "German cruiser Admiral <PERSON>", "link": "https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>"}]}, {"year": "1940", "text": "<PERSON> is the first and only President of the United States to be elected to a third term.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first and only President of the United States to be <a href=\"https://wikipedia.org/wiki/1940_United_States_presidential_election\" title=\"1940 United States presidential election\">elected</a> to a third term.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first and only President of the United States to be <a href=\"https://wikipedia.org/wiki/1940_United_States_presidential_election\" title=\"1940 United States presidential election\">elected</a> to a third term.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1940 United States presidential election", "link": "https://wikipedia.org/wiki/1940_United_States_presidential_election"}]}, {"year": "1943", "text": "World War II: Bombing of the Vatican.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Bombing_of_the_Vatican\" class=\"mw-redirect\" title=\"Bombing of the Vatican\">Bombing of the Vatican</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Bombing_of_the_Vatican\" class=\"mw-redirect\" title=\"Bombing of the Vatican\">Bombing of the Vatican</a>.", "links": [{"title": "Bombing of the Vatican", "link": "https://wikipedia.org/wiki/Bombing_of_the_Vatican"}]}, {"year": "1945", "text": "The three-day anti-Jewish riots in Tripolitania commence.", "html": "1945 - The three-day <a href=\"https://wikipedia.org/wiki/1945_anti-Jewish_riots_in_Tripolitania\" title=\"1945 anti-Jewish riots in Tripolitania\">anti-Jewish riots in Tripolitania</a> commence.", "no_year_html": "The three-day <a href=\"https://wikipedia.org/wiki/1945_anti-Jewish_riots_in_Tripolitania\" title=\"1945 anti-Jewish riots in Tripolitania\">anti-Jewish riots in Tripolitania</a> commence.", "links": [{"title": "1945 anti-Jewish riots in Tripolitania", "link": "https://wikipedia.org/wiki/1945_anti-Jewish_riots_in_Tripolitania"}]}, {"year": "1950", "text": "Korean War: British and Australian forces from the 27th British Commonwealth Brigade successfully halted the advancing Chinese 117th Division during the Battle of Pakchon.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: British and Australian forces from the <a href=\"https://wikipedia.org/wiki/27th_British_Commonwealth_Brigade\" class=\"mw-redirect\" title=\"27th British Commonwealth Brigade\">27th British Commonwealth Brigade</a> successfully halted the advancing Chinese <a href=\"https://wikipedia.org/wiki/117th_Division_(People%27s_Republic_of_China)\" class=\"mw-redirect\" title=\"117th Division (People's Republic of China)\">117th Division</a> during the <a href=\"https://wikipedia.org/wiki/Battle_of_Pakchon\" title=\"Battle of Pakchon\">Battle of Pakchon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: British and Australian forces from the <a href=\"https://wikipedia.org/wiki/27th_British_Commonwealth_Brigade\" class=\"mw-redirect\" title=\"27th British Commonwealth Brigade\">27th British Commonwealth Brigade</a> successfully halted the advancing Chinese <a href=\"https://wikipedia.org/wiki/117th_Division_(People%27s_Republic_of_China)\" class=\"mw-redirect\" title=\"117th Division (People's Republic of China)\">117th Division</a> during the <a href=\"https://wikipedia.org/wiki/Battle_of_Pakchon\" title=\"Battle of Pakchon\">Battle of Pakchon</a>.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "27th British Commonwealth Brigade", "link": "https://wikipedia.org/wiki/27th_British_Commonwealth_Brigade"}, {"title": "117th Division (People's Republic of China)", "link": "https://wikipedia.org/wiki/117th_Division_(People%27s_Republic_of_China)"}, {"title": "Battle of Pakchon", "link": "https://wikipedia.org/wiki/Battle_of_Pakchon"}]}, {"year": "1955", "text": "After being destroyed in World War II, the rebuilt Vienna State Opera reopens with a performance of <PERSON>'s <PERSON><PERSON><PERSON>.", "html": "1955 - After being destroyed in World War II, the rebuilt <a href=\"https://wikipedia.org/wiki/Vienna_State_Opera\" title=\"Vienna State Opera\">Vienna State Opera</a> reopens with a performance of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> van Beethoven\">Beethoven</a>'s <i><a href=\"https://wikipedia.org/wiki/Fidelio\" title=\"Fidelio\">Fidelio</a></i>.", "no_year_html": "After being destroyed in World War II, the rebuilt <a href=\"https://wikipedia.org/wiki/Vienna_State_Opera\" title=\"Vienna State Opera\">Vienna State Opera</a> reopens with a performance of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> van Beethoven\">Beethoven</a>'s <i><a href=\"https://wikipedia.org/wiki/Fidelio\" title=\"Fidelio\">Fidelio</a></i>.", "links": [{"title": "Vienna State Opera", "link": "https://wikipedia.org/wiki/Vienna_State_Opera"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fidelio"}]}, {"year": "1956", "text": "Suez Crisis: British and French paratroopers land in Egypt after a week-long bombing campaign.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>: British and French paratroopers land in Egypt after a week-long bombing campaign.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>: British and French paratroopers land in Egypt after a week-long bombing campaign.", "links": [{"title": "Suez Crisis", "link": "https://wikipedia.org/wiki/Suez_Crisis"}]}, {"year": "1968", "text": "<PERSON> is elected as 37th President of the United States.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1968_United_States_presidential_election\" title=\"1968 United States presidential election\">elected</a> as 37th President of the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1968_United_States_presidential_election\" title=\"1968 United States presidential election\">elected</a> as 37th President of the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1968 United States presidential election", "link": "https://wikipedia.org/wiki/1968_United_States_presidential_election"}]}, {"year": "1970", "text": "The Military Assistance Command, Vietnam reports the lowest weekly American soldier death toll in five years (24).", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/Military_Assistance_Command,_Vietnam\" title=\"Military Assistance Command, Vietnam\">Military Assistance Command, Vietnam</a> reports the lowest weekly American soldier death toll in five years (24).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Military_Assistance_Command,_Vietnam\" title=\"Military Assistance Command, Vietnam\">Military Assistance Command, Vietnam</a> reports the lowest weekly American soldier death toll in five years (24).", "links": [{"title": "Military Assistance Command, Vietnam", "link": "https://wikipedia.org/wiki/Military_Assistance_Command,_Vietnam"}]}, {"year": "1983", "text": "The Byford Dolphin diving bell accident kills five and leaves one severely injured.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/Byford_Dolphin\" title=\"Byford Dolphin\">Byford Dolphin</a> <a href=\"https://wikipedia.org/wiki/Diving_bell\" title=\"Diving bell\">diving bell</a> accident kills five and leaves one severely injured.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Byford_Dolphin\" title=\"Byford Dolphin\">Byford Dolphin</a> <a href=\"https://wikipedia.org/wiki/Diving_bell\" title=\"Diving bell\">diving bell</a> accident kills five and leaves one severely injured.", "links": [{"title": "Byford Dolphin", "link": "https://wikipedia.org/wiki/<PERSON>ford_Dolphin"}, {"title": "Diving bell", "link": "https://wikipedia.org/wiki/Diving_bell"}]}, {"year": "1986", "text": "USS <PERSON>tz, <PERSON> Reeves and <PERSON> visit Qingdao, China; the first US naval visit to China since 1949.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/USS_Rentz\" title=\"USS Rentz\">USS <i>Rentz</i></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Reeves_(DLG-24)\" title=\"<PERSON> Reeves (DLG-24)\">USS <i><PERSON></i></a> and <a href=\"https://wikipedia.org/wiki/USS_Oldendorf\" title=\"USS Oldendorf\">USS <i>Oldendorf</i></a> visit <a href=\"https://wikipedia.org/wiki/Qingdao\" title=\"Qingdao\">Qingdao</a>, China; the first US naval visit to China since 1949.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USS_Rentz\" title=\"USS Rentz\">USS <i>Rentz</i></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Reeves_(DLG-24)\" title=\"<PERSON> Reeves (DLG-24)\">USS <i><PERSON></i></a> and <a href=\"https://wikipedia.org/wiki/USS_Oldendorf\" title=\"USS Oldendorf\">USS <i>Oldendorf</i></a> visit <a href=\"https://wikipedia.org/wiki/Qingdao\" title=\"Qingdao\">Qingdao</a>, China; the first US naval visit to China since 1949.", "links": [{"title": "USS Rentz", "link": "https://wikipedia.org/wiki/USS_Rentz"}, {"title": "<PERSON> (DLG-24)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DLG-24)"}, {"title": "USS Oldendorf", "link": "https://wikipedia.org/wiki/USS_Oldendorf"}, {"title": "Qingdao", "link": "https://wikipedia.org/wiki/Qingdao"}]}, {"year": "1990", "text": "Rabbi <PERSON><PERSON>, founder of the far-right Kach movement, is shot dead after a speech at a New York City hotel.", "html": "1990 - Rabbi <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, founder of the far-right <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> and <PERSON><PERSON>\"><PERSON><PERSON> movement</a>, is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Assassination of <PERSON><PERSON>\">shot dead</a> after a speech at a New York City hotel.", "no_year_html": "Rabbi <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, founder of the far-right <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> and <PERSON><PERSON>\"><PERSON><PERSON> movement</a>, is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Assassination of <PERSON><PERSON>\">shot dead</a> after a speech at a New York City hotel.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> and <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "Tropical Storm Thelma causes flash floods in the Philippine city of Ormoc, killing more than 4,900 people.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Tropical_Storm_Thelma\" title=\"Tropical Storm Thelma\">Tropical Storm Thelma</a> causes flash floods in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippine</a> city of <a href=\"https://wikipedia.org/wiki/Ormoc\" title=\"Ormoc\">Ormoc</a>, killing more than 4,900 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tropical_Storm_Thelma\" title=\"Tropical Storm Thelma\">Tropical Storm Thelma</a> causes flash floods in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippine</a> city of <a href=\"https://wikipedia.org/wiki/Ormoc\" title=\"Ormoc\">Ormoc</a>, killing more than 4,900 people.", "links": [{"title": "Tropical Storm Thelma", "link": "https://wikipedia.org/wiki/Tropical_Storm_Thelma"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "Ormoc", "link": "https://wikipedia.org/wiki/Ormoc"}]}, {"year": "1995", "text": "<PERSON> attempts to assassinate Prime Minister <PERSON> of Canada. He is thwarted when the Prime Minister's wife locks the door.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> attempts to assassinate Prime Minister <a href=\"https://wikipedia.org/wiki/Jean_<PERSON>r%C3%A9tien\" title=\"<PERSON>\"><PERSON></a> of Canada. He is thwarted when the Prime Minister's wife locks the door.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> attempts to assassinate Prime Minister <a href=\"https://wikipedia.org/wiki/Jean_<PERSON>r%C3%A9tien\" title=\"<PERSON>\"><PERSON></a> of Canada. He is thwarted when the Prime Minister's wife locks the door.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9tien"}]}, {"year": "1996", "text": "Pakistani President <PERSON><PERSON><PERSON> dismisses the government of Prime Minister <PERSON><PERSON><PERSON> and dissolves the National Assembly.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">Pakistani President</a> <a href=\"https://wikipedia.org/wiki/Faroo<PERSON>_Le<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> dismisses the government of <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and dissolves the <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Pakistan\" title=\"National Assembly of Pakistan\">National Assembly</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">Pakistani President</a> <a href=\"https://wikipedia.org/wiki/Faroo<PERSON>_Le<PERSON>\" title=\"Far<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> dismisses the government of <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and dissolves the <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Pakistan\" title=\"National Assembly of Pakistan\">National Assembly</a>.", "links": [{"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "National Assembly of Pakistan", "link": "https://wikipedia.org/wiki/National_Assembly_of_Pakistan"}]}, {"year": "1996", "text": "<PERSON> is reelected President of the United States.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1996_United_States_presidential_election\" title=\"1996 United States presidential election\">reelected</a> President of the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1996_United_States_presidential_election\" title=\"1996 United States presidential election\">reelected</a> President of the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1996 United States presidential election", "link": "https://wikipedia.org/wiki/1996_United_States_presidential_election"}]}, {"year": "2006", "text": "<PERSON>, the former president of Iraq, and his co-defendants <PERSON><PERSON> and <PERSON><PERSON><PERSON>, are sentenced to death in the al-Dujail trial for their roles in the 1982 massacre of 148 Shia Muslims.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the former president of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, and his co-defendants <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>riti\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Awad_<PERSON><PERSON>_al-Bandar\" title=\"<PERSON><PERSON><PERSON> al-Bandar\"><PERSON><PERSON><PERSON><PERSON></a>, are sentenced to death in <a href=\"https://wikipedia.org/wiki/Trial_of_<PERSON>\" title=\"Trial of <PERSON>\">the al-Dujail trial</a> for their roles in <a href=\"https://wikipedia.org/wiki/Dujail_massacre\" title=\"Dujail massacre\">the 1982 massacre of 148 Shia Muslims</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the former president of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, and his co-defendants <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rit<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Awad_<PERSON><PERSON>_al-Bandar\" title=\"<PERSON><PERSON><PERSON><PERSON> al-Bandar\"><PERSON><PERSON><PERSON><PERSON></a>, are sentenced to death in <a href=\"https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>\" title=\"Trial of <PERSON>\">the al-Dujail trial</a> for their roles in <a href=\"https://wikipedia.org/wiki/Dujail_massacre\" title=\"Dujail massacre\">the 1982 massacre of 148 Shia Muslims</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Trial of <PERSON>", "link": "https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>"}, {"title": "Dujail massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}]}, {"year": "2007", "text": "China's first lunar satellite, <PERSON><PERSON><PERSON> <PERSON>, goes into orbit around the Moon.", "html": "2007 - China's first lunar satellite, <a href=\"https://wikipedia.org/wiki/Chang%27e_1\" title=\"Chang'e 1\">Chang'e 1</a>, goes into orbit around the Moon.", "no_year_html": "China's first lunar satellite, <a href=\"https://wikipedia.org/wiki/Chang%27e_1\" title=\"Chang'e 1\">Chang'e 1</a>, goes into orbit around the Moon.", "links": [{"title": "Chang'e 1", "link": "https://wikipedia.org/wiki/Chang%27e_1"}]}, {"year": "2007", "text": "The Android mobile operating system is unveiled by Google.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Android_(operating_system)\" title=\"Android (operating system)\">Android</a> mobile operating system is unveiled by <a href=\"https://wikipedia.org/wiki/Google\" title=\"Google\">Google</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Android_(operating_system)\" title=\"Android (operating system)\">Android</a> mobile operating system is unveiled by <a href=\"https://wikipedia.org/wiki/Google\" title=\"Google\">Google</a>.", "links": [{"title": "Android (operating system)", "link": "https://wikipedia.org/wiki/Android_(operating_system)"}, {"title": "Google", "link": "https://wikipedia.org/wiki/Google"}]}, {"year": "2009", "text": "U.S. Army Major <PERSON><PERSON> murders 13 and wounds 32 at Fort Hood, Texas in the deadliest mass shooting at a U.S. military installation.", "html": "2009 - U.S. Army Major <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> murders 13 and wounds 32 at <a href=\"https://wikipedia.org/wiki/Fort_Hood\" class=\"mw-redirect\" title=\"Fort Hood\">Fort Hood</a>, <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> in the deadliest <a href=\"https://wikipedia.org/wiki/2009_Fort_Hood_shooting\" title=\"2009 Fort Hood shooting\">mass shooting</a> at a U.S. military installation.", "no_year_html": "U.S. Army Major <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> murders 13 and wounds 32 at <a href=\"https://wikipedia.org/wiki/Fort_Hood\" class=\"mw-redirect\" title=\"Fort Hood\">Fort Hood</a>, <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> in the deadliest <a href=\"https://wikipedia.org/wiki/2009_Fort_Hood_shooting\" title=\"2009 Fort Hood shooting\">mass shooting</a> at a U.S. military installation.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Fort Hood", "link": "https://wikipedia.org/wiki/Fort_Hood"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "2009 Fort Hood shooting", "link": "https://wikipedia.org/wiki/2009_Fort_Hood_shooting"}]}, {"year": "2010", "text": "JS Air Flight 201 crashes after takeoff from Jinnah International Airport in Karachi, Pakistan, killing all 21 aboard.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/JS_Air_Flight_201\" title=\"JS Air Flight 201\">JS Air Flight 201</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Jinnah_International_Airport\" title=\"Jinnah International Airport\">Jinnah International Airport</a> in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>, Pakistan, killing all 21 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/JS_Air_Flight_201\" title=\"JS Air Flight 201\">JS Air Flight 201</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Jinnah_International_Airport\" title=\"Jinnah International Airport\">Jinnah International Airport</a> in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>, Pakistan, killing all 21 aboard.", "links": [{"title": "JS Air Flight 201", "link": "https://wikipedia.org/wiki/JS_Air_Flight_201"}, {"title": "Jinnah International Airport", "link": "https://wikipedia.org/wiki/Jinnah_International_Airport"}, {"title": "Karachi", "link": "https://wikipedia.org/wiki/Karachi"}]}, {"year": "2013", "text": "India launches the Mars Orbiter Mission, its first interplanetary probe.", "html": "2013 - India launches the <a href=\"https://wikipedia.org/wiki/Mars_Orbiter_Mission\" title=\"Mars Orbiter Mission\">Mars Orbiter Mission</a>, its <a href=\"https://wikipedia.org/wiki/List_of_Solar_System_probes\" title=\"List of Solar System probes\">first interplanetary probe</a>.", "no_year_html": "India launches the <a href=\"https://wikipedia.org/wiki/Mars_Orbiter_Mission\" title=\"Mars Orbiter Mission\">Mars Orbiter Mission</a>, its <a href=\"https://wikipedia.org/wiki/List_of_Solar_System_probes\" title=\"List of Solar System probes\">first interplanetary probe</a>.", "links": [{"title": "Mars Orbiter Mission", "link": "https://wikipedia.org/wiki/Mars_Orbiter_Mission"}, {"title": "List of Solar System probes", "link": "https://wikipedia.org/wiki/List_of_Solar_System_probes"}]}, {"year": "2015", "text": "An iron ore tailings dam bursts in the Brazilian state of Minas Gerais, flooding a valley, causing mudslides in the nearby village of Bento Rodrigues and causing at least 17 deaths and two missing.", "html": "2015 - An <a href=\"https://wikipedia.org/wiki/Iron_ore\" title=\"Iron ore\">iron ore</a> <a href=\"https://wikipedia.org/wiki/Tailings_dam\" title=\"Tailings dam\">tailings dam</a> <a href=\"https://wikipedia.org/wiki/Bento_Rodrigues_dam_disaster\" class=\"mw-redirect\" title=\"Bento Rodrigues dam disaster\">bursts</a> in the Brazilian state of <a href=\"https://wikipedia.org/wiki/Minas_Gerais\" title=\"Minas Gerais\">Minas Gerais</a>, flooding a valley, causing mudslides in the nearby village of Bento Rodrigues and causing at least 17 deaths and two missing.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Iron_ore\" title=\"Iron ore\">iron ore</a> <a href=\"https://wikipedia.org/wiki/Tailings_dam\" title=\"Tailings dam\">tailings dam</a> <a href=\"https://wikipedia.org/wiki/Bento_Rodrigues_dam_disaster\" class=\"mw-redirect\" title=\"Bento Rodrigues dam disaster\">bursts</a> in the Brazilian state of <a href=\"https://wikipedia.org/wiki/Minas_Gerais\" title=\"Minas Gerais\">Minas Gerais</a>, flooding a valley, causing mudslides in the nearby village of Bento Rodrigues and causing at least 17 deaths and two missing.", "links": [{"title": "Iron ore", "link": "https://wikipedia.org/wiki/Iron_ore"}, {"title": "Tailings dam", "link": "https://wikipedia.org/wiki/Tailings_dam"}, {"title": "Bento Rodrigues dam disaster", "link": "https://wikipedia.org/wiki/Bento_<PERSON>rig<PERSON>_dam_disaster"}, {"title": "Minas Gerais", "link": "https://wikipedia.org/wiki/Minas_Gerais"}]}, {"year": "2015", "text": "<PERSON><PERSON> takes over after <PERSON> as the Leader of the Conservative Party of Canada.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> takes over after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the <a href=\"https://wikipedia.org/wiki/List_of_Canadian_conservative_leaders\" title=\"List of Canadian conservative leaders\">Leader of the Conservative Party of Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> takes over after <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the <a href=\"https://wikipedia.org/wiki/List_of_Canadian_conservative_leaders\" title=\"List of Canadian conservative leaders\">Leader of the Conservative Party of Canada</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Canadian conservative leaders", "link": "https://wikipedia.org/wiki/List_of_Canadian_conservative_leaders"}]}, {"year": "2017", "text": "<PERSON> kills 26 and injures 22 in a church in Sutherland Springs, Texas.", "html": "2017 - <PERSON> <a href=\"https://wikipedia.org/wiki/Sutherland_Springs_church_shooting\" title=\"Sutherland Springs church shooting\">kills 26 and injures 22</a> in a church in <a href=\"https://wikipedia.org/wiki/Sutherland_Springs,_Texas\" title=\"Sutherland Springs, Texas\">Sutherland Springs, Texas</a>.", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/Sutherland_Springs_church_shooting\" title=\"Sutherland Springs church shooting\">kills 26 and injures 22</a> in a church in <a href=\"https://wikipedia.org/wiki/Sutherland_Springs,_Texas\" title=\"Sutherland Springs, Texas\">Sutherland Springs, Texas</a>.", "links": [{"title": "Sutherland Springs church shooting", "link": "https://wikipedia.org/wiki/Sutherland_Springs_church_shooting"}, {"title": "Sutherland Springs, Texas", "link": "https://wikipedia.org/wiki/Sutherland_Springs,_Texas"}]}, {"year": "2021", "text": "The Astroworld Festival crowd crush results in 10 deaths and 25 people being hospitalized", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/Astroworld_Festival_crowd_crush\" title=\"Astroworld Festival crowd crush\">Astroworld Festival crowd crush</a> results in 10 deaths and 25 people being hospitalized", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Astroworld_Festival_crowd_crush\" title=\"Astroworld Festival crowd crush\">Astroworld Festival crowd crush</a> results in 10 deaths and 25 people being hospitalized", "links": [{"title": "Astroworld Festival crowd crush", "link": "https://wikipedia.org/wiki/Astroworld_Festival_crowd_crush"}]}, {"year": "2024", "text": "<PERSON> becomes the first president of the United States to be elected to a non-consecutive second term in 132 years, since <PERSON><PERSON> won the 1892 election.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">president of the United States</a> to be <a href=\"https://wikipedia.org/wiki/2024_United_States_presidential_election\" title=\"2024 United States presidential election\">elected</a> to a non-consecutive second term in 132 years, since <a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a> won the <a href=\"https://wikipedia.org/wiki/1892_United_States_presidential_election\" title=\"1892 United States presidential election\">1892 election</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">president of the United States</a> to be <a href=\"https://wikipedia.org/wiki/2024_United_States_presidential_election\" title=\"2024 United States presidential election\">elected</a> to a non-consecutive second term in 132 years, since <a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a> won the <a href=\"https://wikipedia.org/wiki/1892_United_States_presidential_election\" title=\"1892 United States presidential election\">1892 election</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "2024 United States presidential election", "link": "https://wikipedia.org/wiki/2024_United_States_presidential_election"}, {"title": "Grover <PERSON>", "link": "https://wikipedia.org/wiki/Grover_Cleveland"}, {"title": "1892 United States presidential election", "link": "https://wikipedia.org/wiki/1892_United_States_presidential_election"}]}], "Births": [{"year": "1271", "text": "<PERSON><PERSON><PERSON>, Mongol ruler of the Ilkhanate (d. 1304)", "html": "1271 - <a href=\"https://wikipedia.org/wiki/Ghazan\" title=\"Gha<PERSON>\"><PERSON><PERSON><PERSON></a>, Mongol ruler of the Ilkhanate (d. 1304)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gha<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongol ruler of the Ilkhanate (d. 1304)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1436", "text": "<PERSON>, 3rd Earl of Tankerville, Earl of Tankerville, 1450-1460 (d. 1466)", "html": "1436 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Tankerville\" title=\"<PERSON>, 3rd Earl of Tankerville\"><PERSON>, 3rd Earl of Tankerville</a>, Earl of Tankerville, 1450-1460 (d. 1466)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Tankerville\" title=\"<PERSON>, 3rd Earl of Tankerville\"><PERSON>, 3rd Earl of Tankerville</a>, Earl of Tankerville, 1450-1460 (d. 1466)", "links": [{"title": "<PERSON>, 3rd Earl of Tankerville", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Tankerville"}]}, {"year": "1494", "text": "<PERSON>, German poet and playwright (d. 1576)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (d. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (d. 1576)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1549", "text": "<PERSON>, French theologian and author (d. 1623)", "html": "1549 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theologian and author (d. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theologian and author (d. 1623)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1592", "text": "<PERSON>, English-American pastor, theologian, and academic (d. 1672)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American pastor, theologian, and academic (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American pastor, theologian, and academic (d. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, Dutch painter (d. 1678)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1613", "text": "<PERSON>, French poet and educator (d. 1691)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and educator (d. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and educator (d. 1691)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1615", "text": "<PERSON> of the Ottoman Empire (d. 1648)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Ottoman_Empire\" title=\"Ibrahim of the Ottoman Empire\">Ibrahim of the Ottoman Empire</a> (d. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Ottoman_Empire\" title=\"Ibrahim of the Ottoman Empire\">Ibrahim of the Ottoman Empire</a> (d. 1648)", "links": [{"title": "<PERSON> of the Ottoman Empire", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Ottoman_Empire"}]}, {"year": "1666", "text": "<PERSON><PERSON><PERSON>, Italian viola player and composer (d. 1729)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian viola player and composer (d. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian viola player and composer (d. 1729)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Attilio_Ariosti"}]}, {"year": "1667", "text": "<PERSON>, German painter (d. 1719)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON>, French mathematician and philosopher (d. 1757)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (d. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1701", "text": "<PERSON>, Venetian painter and educator (d. 1785)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venetian painter and educator (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venetian painter and educator (d. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1705", "text": "<PERSON><PERSON><PERSON>, French violinist and composer (d. 1770)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and composer (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and composer (d. 1770)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, English author and playwright (d. 1766)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(essayist)\" title=\"<PERSON> (essayist)\"><PERSON></a>, English author and playwright (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(essayist)\" title=\"<PERSON> (essayist)\"><PERSON></a>, English author and playwright (d. 1766)", "links": [{"title": "<PERSON> (essayist)", "link": "https://wikipedia.org/wiki/<PERSON>_(essayist)"}]}, {"year": "1722", "text": "<PERSON>, 5th Baron <PERSON>, English lieutenant and politician (d. 1798)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Baron_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, English lieutenant and politician (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Baron_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, English lieutenant and politician (d. 1798)", "links": [{"title": "<PERSON>, 5th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Baron_<PERSON>"}]}, {"year": "1739", "text": "<PERSON>, 12th Earl of Eglinton, Scottish composer and politician, Lord Lieutenant of Ayrshire (d. 1819)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Eglinton\" title=\"<PERSON>, 12th Earl of Eglinton\"><PERSON>, 12th Earl of Eglinton</a>, Scottish composer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ayrshire\" title=\"Lord Lieutenant of Ayrshire\">Lord Lieutenant of Ayrshire</a> (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Eglinton\" title=\"<PERSON>, 12th Earl of Eglinton\"><PERSON>, 12th Earl of Eglinton</a>, Scottish composer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ayrshire\" title=\"Lord Lieutenant of Ayrshire\">Lord Lieutenant of Ayrshire</a> (d. 1819)", "links": [{"title": "<PERSON>, 12th Earl of Eglinton", "link": "https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Eglinton"}, {"title": "Lord Lieutenant of Ayrshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ayrshire"}]}, {"year": "1742", "text": "<PERSON>, English painter (d. 1821)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, Australian surgeon and politician (d. 1868)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surgeon and politician (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surgeon and politician (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, American general, lawyer, and politician, 33rd Governor of Massachusetts (d. 1893)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American general, lawyer, and politician, 33rd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American general, lawyer, and politician, 33rd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1893)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1835", "text": "<PERSON><PERSON>, Ukrainian-Austrian journalist and publisher (d. 1902)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Austrian journalist and publisher (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ze<PERSON>\"><PERSON><PERSON></a>, Ukrainian-Austrian journalist and publisher (d. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, English soldier, recipient of the Victoria Cross (d. 1869)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, recipient of the <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, recipient of the <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1850", "text": "<PERSON>, American author and poet (d. 1919)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, French academic and politician, 60th Prime Minister of France (d. 1923)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French academic and politician, 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French academic and politician, 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, Canadian journalist and businessman, co-founded Desjardins Group (d. 1920)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>phon<PERSON>_<PERSON>_(co-operator)\" title=\"<PERSON>phon<PERSON> (co-operator)\"><PERSON><PERSON><PERSON></a>, Canadian journalist and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Desjardins_Group\" title=\"Desjardins Group\">Desjardins Group</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>phon<PERSON>_<PERSON>_(co-operator)\" title=\"<PERSON>phon<PERSON> (co-operator)\"><PERSON><PERSON><PERSON></a>, Canadian journalist and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Desjardins_Group\" title=\"Desjardins Group\">Desjardins Group</a> (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON> (co-operator)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(co-operator)"}, {"title": "Desjardins Group", "link": "https://wikipedia.org/wiki/Desjardins_Group"}]}, {"year": "1854", "text": "<PERSON>, French chemist and academic, Nobel Prize laureate (d. 1941)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1941)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_(chemist)"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1855", "text": "<PERSON>, American union leader and politician (d. 1926)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American union leader and politician (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American union leader and politician (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_V._Debs"}]}, {"year": "1855", "text": "<PERSON>, French meteorologist and climatologist (d. 1913)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/L%C3%A9on_Teisserenc_de_<PERSON>rt\" title=\"<PERSON>\"><PERSON></a>, French meteorologist and climatologist (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9on_Teisserenc_de_Bort\" title=\"<PERSON>\"><PERSON></a>, French meteorologist and climatologist (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, American journalist, author, reformer, and educator (d. 1944)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ell\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, reformer, and educator (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, reformer, and educator (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_<PERSON>ell"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician (d. 1925)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (d. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Australian tennis player and runner (d. 1935)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and runner (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and runner (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Austrian-American swimmer and coach (d. 1963)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American swimmer and coach (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American swimmer and coach (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American lawyer and jurist (d. 1961)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Burmese author and translator (d. 1940)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON> <PERSON></a>, Burmese author and translator (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON> <PERSON></a>, Burmese author and translator (d. 1940)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>n"}]}, {"year": "1884", "text": "<PERSON>, English author, poet, and playwright (d. 1915)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American historian and philosopher (d. 1981)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and philosopher (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and philosopher (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON>, Japanese general (d. 1961)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Austrian-American pianist and educator (d. 1961)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American pianist and educator (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American pianist and educator (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Czech painter and illustrator (d. 1977)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech painter and illustrator (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech painter and illustrator (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>%C3%BD"}]}, {"year": "1892", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English-Indian geneticist and biologist (d. 1964)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/J._B._<PERSON><PERSON>_Haldane\" title=\"J. B. S. Haldane\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English-Indian geneticist and biologist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._B._<PERSON><PERSON>_<PERSON>dane\" title=\"J. B. S. Haldane\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English-Indian geneticist and biologist (d. 1964)", "links": [{"title": "J. B. S<PERSON>", "link": "https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_Hal<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, captain in the Royal Navy and the Royal Air Force (d. 1919)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, captain in the Royal Navy and the Royal Air Force (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, captain in the Royal Navy and the Royal Air Force (d. 1919)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)"}]}, {"year": "1893", "text": "<PERSON>, French-American engineer and designer (d. 1986)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American engineer and designer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American engineer and designer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON>, American economist and statistician (d. 1960)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>sley_Ruml\" title=\"Beardsley Ruml\"><PERSON><PERSON></a>, American economist and statistician (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sley_Ruml\" title=\"Beardsley Ruml\"><PERSON><PERSON></a>, American economist and statistician (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ruml"}]}, {"year": "1895", "text": "<PERSON>, French-German pianist and composer (d. 1956)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German pianist and composer (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German pianist and composer (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American playwright and screenwriter (d. 1956)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American historian and author (d. 1991)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American actress (d. 1991)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, British ichthyologist, over a dozen fish species named in her honor (d. 1993)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Trewavas\" title=\"<PERSON><PERSON><PERSON> Trewavas\"><PERSON><PERSON><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Ichthyologist\" class=\"mw-redirect\" title=\"Ichthyologist\">ichthyologist</a>, over a dozen fish species named in her honor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Trewavas\" title=\"<PERSON><PERSON><PERSON> Trewavas\"><PERSON><PERSON><PERSON>ava<PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Ichthyologist\" class=\"mw-redirect\" title=\"Ichthyologist\">ichthyologist</a>, over a dozen fish species named in her honor (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}, {"title": "Ichthyologist", "link": "https://wikipedia.org/wiki/Ichthyologist"}]}, {"year": "1901", "text": "<PERSON><PERSON>, American actress and singer (d. 2004)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Barnett"}]}, {"year": "1901", "text": "<PERSON>, Jr., American lawyer, judge and politician (d. 1972)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer, judge and politician (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer, judge and politician (d. 1972)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1901", "text": "<PERSON>, English cricketer (d. 1979)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach (d. 1985)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Cooney Weiland\"><PERSON><PERSON></a>, Canadian ice hockey player and coach (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Cooney Weiland\"><PERSON><PERSON></a>, Canadian ice hockey player and coach (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American actor (d. 1990)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French racing driver (d. 1956)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author and poet (d. 1973)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and poet (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and poet (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Hungarian fencer (d. 1944)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian fencer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian fencer (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American astronomer and academic (d. 2004)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Australian-English general and academic (d. 1997)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Australian-English general and academic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Australian-English general and academic (d. 1997)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "1911", "text": "<PERSON>, American actress and costume designer (d. 2010)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and costume designer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and costume designer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American singer, guitarist and actor (d. 1998)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, guitarist and actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, guitarist and actor (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American economist and statistician (d. 1998)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and statistician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and statistician (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English-American director, screenwriter and cinematographer (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, English-American director, screenwriter and cinematographer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, English-American director, screenwriter and cinematographer (d. 2005)", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Indian-British actress (d. 1967)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-British actress (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-British actress (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viv<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actor (d. 1975)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American painter and illustrator (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter and illustrator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter and illustrator (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, French pilot (d. 2000)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pilot (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pilot (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Indian activist and politician, 4th Chief Minister of Haryana (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist and politician, 4th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Haryana\" class=\"mw-redirect\" title=\"Chief Minister of Haryana\">Chief Minister of Haryana</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist and politician, 4th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Haryana\" class=\"mw-redirect\" title=\"Chief Minister of Haryana\">Chief Minister of Haryana</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of Haryana", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Haryana"}]}, {"year": "1917", "text": "<PERSON>, American brigadier general (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American <a href=\"https://wikipedia.org/wiki/Brigadier_general\" title=\"Brigadier general\">brigadier general</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American <a href=\"https://wikipedia.org/wiki/Brigadier_general\" title=\"Brigadier general\">brigadier general</a> (d. 2002)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Brigadier general", "link": "https://wikipedia.org/wiki/Brigadier_general"}]}, {"year": "1917", "text": "<PERSON>, Italian football player", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Pakistani linguist, scholar and critic (d. 1978)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Pakistani linguist, scholar and critic (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Pakistani linguist, scholar and critic (d. 1978)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American accordionist and pianist (d. 2005)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American accordionist and pianist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American accordionist and pianist (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1920", "text": "<PERSON>, American-English cyclist and coach (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist_born_1920)\" class=\"mw-redirect\" title=\"<PERSON> (cyclist born 1920)\"><PERSON></a>, American-English cyclist and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist_born_1920)\" class=\"mw-redirect\" title=\"<PERSON> (cyclist born 1920)\"><PERSON></a>, American-English cyclist and coach (d. 2012)", "links": [{"title": "<PERSON> (cyclist born 1920)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist_born_1920)"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American economist and academic, Nobel Prize laureate (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Douglass_North\" title=\"Douglass North\"><PERSON><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Douglass_North\" title=\"Douglass North\"><PERSON><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2015)", "links": [{"title": "Douglass North", "link": "https://wikipedia.org/wiki/Douglass_North"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1921", "text": "<PERSON>, Hungarian pianist and composer (d. 1994)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Hungarian pianist and composer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Hungarian pianist and composer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON> of Egypt (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Fawzia_Fuad_of_Egypt\" class=\"mw-redirect\" title=\"Fawzia Fuad of Egypt\">Fawzia Fuad of Egypt</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fawzia_Fuad_of_Egypt\" class=\"mw-redirect\" title=\"Fawzia Fuad of Egypt\">Fawzia Fuad of Egypt</a> (d. 2013)", "links": [{"title": "Fawzia Fuad of Egypt", "link": "https://wikipedia.org/wiki/Fawzia_Fuad_of_Egypt"}]}, {"year": "1922", "text": "<PERSON>, American illustrator (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Barclay\"><PERSON></a>, American illustrator (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, American-Israeli rabbi (d. 2021)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American-Israeli rabbi (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American-Israeli rabbi (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American educator and politician, 25th and 32nd Governor of West Virginia (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 25th and 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 25th and 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of West Virginia", "link": "https://wikipedia.org/wiki/Governor_of_West_Virginia"}]}, {"year": "1923", "text": "<PERSON>, German soldier and journalist, co-founder of Der Spiegel (d. 2002)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and journalist, co-founder of <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Spiegel\"><PERSON> S<PERSON>gel</a></i> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and journalist, co-founder of <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Spiegel\"><PERSON>gel</a></i> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Der Spiegel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English author, poet, painter and critic (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, painter and critic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, painter and critic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese statistician (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Hirotug<PERSON>_<PERSON>\" title=\"Hirotug<PERSON> Akaike\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese statistician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hirotug<PERSON>_<PERSON>\" title=\"Hirotug<PERSON> Akaike\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese statistician (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hirotug<PERSON>_<PERSON>ke"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Dutch footballer and manager (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON><PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON>, German historian and academic (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American immunologist, geneticist and academic (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American immunologist, geneticist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American immunologist, geneticist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor, police officer and politician (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gil Hill\"><PERSON></a>, American actor, police officer and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gil Hill\"><PERSON></a>, American actor, police officer and politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gil_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Jamaican-English saxophonist and flute player (d. 1971)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English saxophonist and flute player (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English saxophonist and flute player (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, British book editor and novelist (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British book editor and novelist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British book editor and novelist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist and producer (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist and producer (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian basketball player (d. 2001)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON><PERSON>_<PERSON>%C4%97nas\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C4%97nas\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Algirdas_Laurit%C4%97nas"}]}, {"year": "1933", "text": "<PERSON>, American actor (d. 1996)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American minister and civil servant (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Je<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>b <PERSON>\">Je<PERSON> <PERSON></a>, American minister and civil servant (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Je<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">Je<PERSON> <PERSON></a>, American minister and civil servant (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English flat racing jockey and trainer (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English flat racing jockey and trainer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English flat racing jockey and trainer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English author and screenwriter (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and screenwriter (d. 2015)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>(writer)"}]}, {"year": "1936", "text": "<PERSON>, Greek-American computer scientist and academic (d. 2001)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American computer scientist and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American computer scientist and academic (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, German footballer (d. 2022)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Seeler\" title=\"<PERSON><PERSON> Seeler\"><PERSON><PERSON></a>, German footballer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Seeler\" title=\"<PERSON><PERSON> Seeler\"><PERSON><PERSON></a>, German footballer (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Seeler"}]}, {"year": "1936", "text": "<PERSON>, American record producer, songwriter and arranger (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, songwriter and arranger (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, songwriter and arranger (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Singaporean lawyer, judge and politician, 3rd Chief Justice of Singapore", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean lawyer, judge and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Singapore\" title=\"Chief Justice of Singapore\">Chief Justice of Singapore</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean lawyer, judge and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Singapore\" title=\"Chief Justice of Singapore\">Chief Justice of Singapore</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Justice of Singapore", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Singapore"}]}, {"year": "1937", "text": "<PERSON>, American actor", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American-French singer-songwriter (d. 1980)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French singer-songwriter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French singer-songwriter (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American author and illustrator", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Tibetan religious leader", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zin\" title=\"Lobsang Tenzin\"><PERSON><PERSON><PERSON></a>, Tibetan religious leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tenzin\" title=\"Lobsang Tenzin\"><PERSON><PERSON><PERSON></a>, Tibetan religious leader", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zin"}]}, {"year": "1940", "text": "<PERSON>, American soldier, lawyer and politician, 36th Governor of Oregon", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1940", "text": "<PERSON><PERSON>, German actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Art_Garfunkel\" title=\"Art Garfunkel\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Garfunkel\" title=\"Art Garfunkel\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Garfunkel"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Japanese animator, director and screenwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese animator, director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese animator, director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American baseball player (d. 2023)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Italian singer-songwriter and guitarist (d. 2002)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German-American chef and author (d. 2007)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American chef and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American chef and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian high jumper (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, Australian high jumper (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, Australian high jumper (d. 2022)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1943", "text": "<PERSON>, American playwright and actor (d. 2017)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American general", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Greek accountant and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Al<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Papariga\"><PERSON><PERSON></a>, Greek accountant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al<PERSON>_<PERSON>\" title=\"Al<PERSON> Papariga\"><PERSON><PERSON></a>, Greek accountant and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleka_Papa<PERSON>a"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>, Russian fencer and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Svetlana_T%C5%A1irkova-Lozovaja\" title=\"<PERSON><PERSON><PERSON>-Lozo<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, Russian fencer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_T%C5%A1irkova-Lozovaja\" title=\"<PERSON><PERSON><PERSON>-Lozovaja\"><PERSON><PERSON><PERSON>-<PERSON></a>, Russian fencer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>-Lozovaja", "link": "https://wikipedia.org/wiki/Svetlana_T%C5%A1irkova-Lozovaja"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1973)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Parsons\"><PERSON></a>, American singer-songwriter and guitarist (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American director and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American director and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Quint_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American lawyer and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English singer-songwriter, guitarist and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, French philosopher and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9vy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9vy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9vy"}]}, {"year": "1948", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter (d. 1984)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian politician, 25th Prime Minister of Norway", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Thorbj%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian politician, 25th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway\" title=\"List of heads of government of Norway\">Prime Minister of Norway</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thorbj%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian politician, 25th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway\" title=\"List of heads of government of Norway\">Prime Minister of Norway</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thorbj%C3%B8rn_<PERSON>agland"}, {"title": "List of heads of government of Norway", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway"}]}, {"year": "1950", "text": "<PERSON>, American psychologist and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(social_psychologist)\" title=\"<PERSON> (social psychologist)\"><PERSON></a>, American psychologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(social_psychologist)\" title=\"<PERSON> (social psychologist)\"><PERSON></a>, American psychologist and author", "links": [{"title": "<PERSON> (social psychologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(social_psychologist)"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Ukrainian footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Indian philosopher and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American basketball player and sportscaster (d. 2024)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American journalist, author and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Italian footballer (d. 2023) ", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Amico\" title=\"<PERSON>Am<PERSON>\"><PERSON></a>, Italian footballer (d. 2023) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Amico\" title=\"<PERSON>Am<PERSON>\"><PERSON></a>, Italian footballer (d. 2023) ", "links": [{"title": "<PERSON>Am<PERSON>", "link": "https://wikipedia.org/wiki/Vincenzo_D%27Amico"}]}, {"year": "1954", "text": "<PERSON>, Argentine footballer and manager (d. 2020)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American economist and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, French computer scientist and academic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American talent manager and businesswoman", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talent manager and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talent manager and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>est<PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nestor_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indian journalist and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>par"}]}, {"year": "1956", "text": "<PERSON>, English keyboard player and songwriter (d. 1999)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, English keyboard player and songwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, English keyboard player and songwriter (d. 1999)", "links": [{"title": "<PERSON> (British musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)"}]}, {"year": "1956", "text": "<PERSON>, American journalist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek singer-songwriter and guitarist (d. 2019)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek singer-songwriter and guitarist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek singer-songwriter and guitarist (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian rugby league player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American guitarist and songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)"}]}, {"year": "1957", "text": "<PERSON>, English singer-songwriter and keyboard player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Score\"><PERSON></a>, English singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Score\" title=\"Mike Score\"><PERSON></a>, English singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American keyboard player, songwriter and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> G<PERSON>fney\"><PERSON></a>, American actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gaffney\"><PERSON></a>, American actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ney"}]}, {"year": "1958", "text": "<PERSON>, American actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian singer-songwriter, guitarist, producer and actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, producer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, producer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Slovenian mountaineer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Tomo_%C4%8Cesen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tomo_%C4%8Cesen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian mountaineer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tomo_%C4%8Cesen"}]}, {"year": "1960", "text": "<PERSON>, Dutch singer-songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>er"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Til<PERSON>_S<PERSON>ton\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Til<PERSON>_<PERSON><PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tilda_Swinton"}]}, {"year": "1960", "text": "<PERSON>, American basketball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1961", "text": "<PERSON>, American captain, pilot and astronaut (d. 2012)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot and astronaut (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot and astronaut (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Poindexter"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Norwegian businesswoman and politician, Norwegian Minister of Culture (d. 2015)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Turid_Birkeland\" title=\"Turid Birkela<PERSON>\"><PERSON><PERSON></a>, Norwegian businesswoman and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Culture_(Norway)\" class=\"mw-redirect\" title=\"Minister of Culture (Norway)\">Norwegian Minister of Culture</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turid_Birkeland\" title=\"<PERSON>rid <PERSON>\"><PERSON><PERSON></a>, Norwegian businesswoman and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Culture_(Norway)\" class=\"mw-redirect\" title=\"Minister of Culture (Norway)\">Norwegian Minister of Culture</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Turid_Birkeland"}, {"title": "Minister of Culture (Norway)", "link": "https://wikipedia.org/wiki/Minister_of_Culture_(Norway)"}]}, {"year": "1962", "text": "<PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Ghanaian footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ghanaian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ghanaian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ele"}]}, {"year": "1962", "text": "<PERSON>, American computer scientist and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Dutch footballer and scout", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and scout", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress and singer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American actress and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_O%27Neal\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Neal\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tatum_O%27Neal"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, French footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American bass player and songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American surgeon and journalist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Atul_Gawande\" title=\"Atul Gawande\"><PERSON><PERSON> Gawa<PERSON></a>, American surgeon and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atul_Gawande\" title=\"Atul Gawande\"><PERSON><PERSON> Gawande</a>, American surgeon and journalist", "links": [{"title": "Atul Gawande", "link": "https://wikipedia.org/wiki/Atul_Gawande"}]}, {"year": "1965", "text": "<PERSON>, American singer and musician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English journalist and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and sportscaster", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Spanish footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Nayim\" title=\"Nayim\"><PERSON><PERSON><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nayim\" title=\"Nayi<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nayim"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Estonian footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rs"}]}, {"year": "1967", "text": "<PERSON>, American actress and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Argentinian actor, director and businessman (d. 2013)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Ricardo_Fort\" title=\"Ricardo Fort\"><PERSON> Fort</a>, Argentinian actor, director and businessman (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ricardo_Fort\" title=\"Ricardo Fort\"><PERSON></a>, Argentinian actor, director and businessman (d. 2013)", "links": [{"title": "Ricardo Fort", "link": "https://wikipedia.org/wiki/Ricardo_Fort"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor, comedian, director and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American baseball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Javy_L%C3%B3pez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Javy_L%C3%B3pez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Javy_L%C3%B3pez"}]}, {"year": "1971", "text": "<PERSON>, Russian ice hockey player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, English guitarist and songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Welsh-English footballer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1971)\" title=\"<PERSON> (footballer, born 1971)\"><PERSON></a>, Welsh-English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1971)\" title=\"<PERSON> (footballer, born 1971)\"><PERSON></a>, Welsh-English footballer and coach", "links": [{"title": "<PERSON> (footballer, born 1971)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1971)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actor, producer and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nemec\" title=\"Corin Nemec\"><PERSON><PERSON></a>, American actor, producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nemec\" title=\"Corin Nemec\"><PERSON><PERSON></a>, American actor, producer and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rin_Nemec"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Swedish golfer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/M%C3%A5rten_Olander\" title=\"<PERSON><PERSON>rten Olander\"><PERSON><PERSON><PERSON></a>, Swedish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A5rten_Olander\" title=\"<PERSON><PERSON>rten Olander\"><PERSON><PERSON><PERSON></a>, Swedish golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A5rten_O<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Russian ice hockey player and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, German singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Croatian footballer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Dado_Pr%C5%A1o\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dado_Pr%C5%A1o\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dado_Pr%C5%A1o"}]}, {"year": "1974", "text": "<PERSON><PERSON>, New Zealand rugby player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Welsh singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "Mr. <PERSON><PERSON>, Finnish guitarist and songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Mr._Fastfinger\" title=\"Mr. Fastfinger\">Mr. <PERSON><PERSON></a>, Finnish guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mr._Fastfinger\" title=\"Mr. Fastfinger\">Mr. <PERSON><PERSON></a>, Finnish guitarist and songwriter", "links": [{"title": "Mr. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mr._<PERSON>finger"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Dutch cyclist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Maarten_T<PERSON>ii\" title=\"Maarten T<PERSON>llingii\"><PERSON><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maarte<PERSON>_Tja<PERSON>\" title=\"Maarten T<PERSON>llingii\"><PERSON><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ii"}]}, {"year": "1977", "text": "<PERSON>, English footballer and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1978", "text": "<PERSON>, Spanish cyclist (d. 2011)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American golfer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Japanese-American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Cypriot singer-songwriter and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Honduran footballer and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Brazilian racing driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Estonian computer programmer, businessman and politician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian computer programmer, businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian computer programmer, businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Orkun_U%C5%9Fak\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orkun_U%C5%9Fak\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Orkun_U%C5%9Fak"}]}, {"year": "1981", "text": "<PERSON>, Australian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(Australian_footballer)"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/%C3%9Cmit_Ergirdi\" title=\"<PERSON><PERSON> Ergirdi\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9Cmit_Ergirdi\" title=\"<PERSON><PERSON> Ergirdi\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%9Cmit_Ergirdi"}]}, {"year": "1982", "text": "<PERSON>, American computer scientist and programmer, co-founder of Pownce", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer, co-founder of <a href=\"https://wikipedia.org/wiki/Pownce\" title=\"Pownce\">Pownce</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer, co-founder of <a href=\"https://wikipedia.org/wiki/Pownce\" title=\"Pownce\">Pownce</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pownce", "link": "https://wikipedia.org/wiki/Pownce"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian singer-songwriter, guitarist and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>wire\" title=\"<PERSON> Swire\"><PERSON></a>, Australian singer-songwriter, guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Swire\" title=\"Rob Swire\"><PERSON></a>, Australian singer-songwriter, guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rob_Swire"}]}, {"year": "1982", "text": "<PERSON>, Welsh footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1983", "text": "<PERSON><PERSON>, English model and television host", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English model and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English model and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Dominican baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1984", "text": "<PERSON>, Canadian football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Jon_<PERSON>\" title=\"Jon <PERSON>\"><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jon_<PERSON>\" title=\"Jon <PERSON>\"><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Swedish ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tobias_Enstr%C3%B6m"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Estonian sumo wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian sumo wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>to"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Kenyan long-distance runner", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan long-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan long-distance runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1984", "text": "<PERSON>, English racing driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Ukrainian-Russian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>herdev\" class=\"mw-redirect\" title=\"<PERSON><PERSON>herdev\"><PERSON><PERSON></a>, Ukrainian-Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dev\" class=\"mw-redirect\" title=\"<PERSON><PERSON>dev\"><PERSON><PERSON></a>, Ukrainian-Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nikolay_Zherdev"}]}, {"year": "1985", "text": "<PERSON>, Dutch runner", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian singer-songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, South Korean singer-songwriter, producer and actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/BoA\" title=\"Bo<PERSON>\"><PERSON><PERSON></a>, South Korean singer-songwriter, producer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/BoA\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer-songwriter, producer and actress", "links": [{"title": "BoA", "link": "https://wikipedia.org/wiki/BoA"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Danish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Georgian singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter, guitarist and actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"O. J. Mayo\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"O. J. Mayo\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "O<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, French fencer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Yannick Borel\"><PERSON><PERSON></a>, French fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Yan<PERSON> Borel\"><PERSON><PERSON></a>, French fencer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Vira<PERSON>_<PERSON>\" title=\"<PERSON>ira<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vira<PERSON>_<PERSON>\" title=\"<PERSON>ira<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vira<PERSON>_<PERSON>i"}]}, {"year": "1989", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Australian DJ and producer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Australian DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Australian DJ and producer", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Sh%C5%8Ddai_Naoya\" title=\"Shōdai Naoya\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sh%C5%8D<PERSON>_Naoya\" title=\"Shōdai Naoya\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sh%C5%8Ddai_Naoya"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON>.</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON>.</a>, American football player", "links": [{"title": "<PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1992", "text": "<PERSON>, Italian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Japanese actor and model", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>da"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>-Fall, Senegalese-Spanish basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Astou_Ndour-Fall\" title=\"Asto<PERSON> Ndour-Fall\"><PERSON><PERSON><PERSON>-Fall</a>, Senegalese-Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Astou_Ndour-Fall\" title=\"Asto<PERSON> Ndour-Fall\"><PERSON><PERSON><PERSON>-Fall</a>, Senegalese-Spanish basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>-Fall", "link": "https://wikipedia.org/wiki/Astou_Ndour-Fall"}]}, {"year": "1995", "text": "<PERSON>, Canadian basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bone\" title=\"<PERSON> Bone\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Bone\" title=\"Jordan Bone\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Bone"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American ice hockey player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "425", "text": "<PERSON><PERSON>, archbishop of Constantinople", "html": "425 - <a href=\"https://wikipedia.org/wiki/Archbishop_Atticus_of_Constantinople\" class=\"mw-redirect\" title=\"Archbishop <PERSON><PERSON> of Constantinople\">Atticus</a>, <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a> of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archbishop_Atticus_of_Constantinople\" class=\"mw-redirect\" title=\"Archbishop <PERSON><PERSON> of Constantinople\">Atticus</a>, <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a> of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>", "links": [{"title": "Archbishop <PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Archbishop_<PERSON><PERSON>_of_Constantinople"}, {"title": "Archbishop", "link": "https://wikipedia.org/wiki/Archbishop"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}]}, {"year": "964", "text": "<PERSON>, chancellor of the Song Dynasty (b. 911)", "html": "964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, chancellor of the Song Dynasty (b. 911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, chancellor of the Song Dynasty (b. 911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hi"}]}, {"year": "1011", "text": "<PERSON><PERSON>, Abbess of Essen (b. 949)", "html": "1011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Abbess_of_Essen\" title=\"<PERSON><PERSON>, Abbess of Essen\"><PERSON><PERSON>, Abbess of Essen</a> (b. 949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Abbess_of_Essen\" title=\"<PERSON><PERSON>, Abbess of Essen\"><PERSON><PERSON>, Abbess of Essen</a> (b. 949)", "links": [{"title": "<PERSON><PERSON>, Abbess of Essen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Abbess_<PERSON>_<PERSON>"}]}, {"year": "1176", "text": "<PERSON>, Castilian nobleman", "html": "1176 - <a href=\"https://wikipedia.org/wiki/Diego_Mart%C3%ADnez_de_Villamayor\" title=\"<PERSON> de Villamayor\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castilian</a> nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_Mart%C3%ADnez_de_Villamayor\" title=\"<PERSON> de Villamayor\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castilian</a> nobleman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Mart%C3%ADnez_de_Villamayor"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}]}, {"year": "1235", "text": "<PERSON> of Swabia, queen consort of Castile and León (b. 1205)", "html": "1235 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Swabia\" title=\"Elisabeth of Swabia\"><PERSON> of Swabia</a>, queen consort of Castile and León (b. 1205)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Swabia\" title=\"Elisabeth of Swabia\">Elisabeth of Swabia</a>, queen consort of Castile and León (b. 1205)", "links": [{"title": "Elisabeth of Swabia", "link": "https://wikipedia.org/wiki/Elisabeth_of_Swabia"}]}, {"year": "1370", "text": "<PERSON> the Great, Polish king (b. 1310)", "html": "1370 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Great\" title=\"<PERSON> III the Great\"><PERSON> the Great</a>, Polish king (b. 1310)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_the_Great\" title=\"<PERSON> III the Great\"><PERSON> the Great</a>, Polish king (b. 1310)", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_III_the_Great"}]}, {"year": "1450", "text": "<PERSON> <PERSON>, Count of Armagnac (b. 1396)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Armagnac\" title=\"<PERSON>, Count of Armagnac\"><PERSON>, Count of Armagnac</a> (b. 1396)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Armagnac\" title=\"<PERSON>, Count of Armagnac\"><PERSON>, Count of Armagnac</a> (b. 1396)", "links": [{"title": "<PERSON>, Count of Armagnac", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>ag<PERSON>"}]}, {"year": "1459", "text": "<PERSON>, English soldier (b. 1380)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (b. 1380)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (b. 1380)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1515", "text": "<PERSON><PERSON>, Italian painter and educator (b. 1474)", "html": "1515 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian painter and educator (b. 1474)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian painter and educator (b. 1474)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1559", "text": "<PERSON><PERSON><PERSON>, Japanese painter and educator (b. 1476)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/Kan%C5%8D_Motonobu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter and educator (b. 1476)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kan%C5%8D_Motonobu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter and educator (b. 1476)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kan%C5%8D_<PERSON><PERSON><PERSON>u"}]}, {"year": "1605", "text": "<PERSON><PERSON><PERSON><PERSON>, Birmese king (b. 1555)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/Nyaungyan_Min\" title=\"Nyaungyan Min\"><PERSON><PERSON><PERSON><PERSON></a>, Birmese king (b. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nyaungyan_Min\" title=\"Nyaungyan Min\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Birmese king (b. 1555)", "links": [{"title": "Nyaungyan Min", "link": "https://wikipedia.org/wiki/Nyaungyan_Min"}]}, {"year": "1660", "text": "<PERSON>, French missionary and lexicographer (b. 1591)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and lexicographer (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and lexicographer (b. 1591)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1660", "text": "<PERSON>, Countess of Carlisle (b. 1599)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Carlisle\" title=\"<PERSON>, Countess of Carlisle\"><PERSON>, Countess of Carlisle</a> (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Carlisle\" title=\"<PERSON>, Countess of Carlisle\"><PERSON>, Countess of Carlisle</a> (b. 1599)", "links": [{"title": "<PERSON>, Countess of Carlisle", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Carlisle"}]}, {"year": "1701", "text": "<PERSON>, 2nd Earl of Macclesfield, French-English colonel and politician, Lord Lieutenant of Lancashire (b. 1659)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Macclesfield\" title=\"<PERSON>, 2nd Earl of Macclesfield\"><PERSON>, 2nd Earl of Macclesfield</a>, French-English colonel and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Lancashire\" title=\"Lord Lieutenant of Lancashire\">Lord Lieutenant of Lancashire</a> (b. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Macclesfield\" title=\"<PERSON>, 2nd Earl of Macclesfield\"><PERSON>, 2nd Earl of Macclesfield</a>, French-English colonel and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Lancashire\" title=\"Lord Lieutenant of Lancashire\">Lord Lieutenant of Lancashire</a> (b. 1659)", "links": [{"title": "<PERSON>, 2nd Earl of Macclesfield", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Macclesfield"}, {"title": "Lord Lieutenant of Lancashire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Lancashire"}]}, {"year": "1714", "text": "<PERSON>, Italian physician and academic (b. 1633)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and academic (b. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and academic (b. 1633)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, German scholar and jurist (b. 1670)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and jurist (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and jurist (b. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, Norwegian-Danish bishop and missionary (b. 1686)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Danish bishop and missionary (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Danish bishop and missionary (b. 1686)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, painter (b. 1741)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, painter (b. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, painter (b. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, English-American painter (b. 1783)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American painter (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American painter (b. 1783)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, German explorer and ornithologist (b. 1824)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German explorer and ornithologist (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German explorer and ornithologist (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Scottish physicist and mathematician (b. 1831)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physicist and mathematician (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physicist and mathematician (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, French author and poet (b. 1880)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Adelsw%C3%A4<PERSON>-<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Adelsw%C3%A4<PERSON>-<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, French author and poet (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Adelsw%C3%A4rd-<PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Greek general (b. 1872)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general (b. 1872)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Dutch physician and pathologist, Nobel Prize  laureate (b. 1858)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1930", "text": "<PERSON>, Italian politician, journalist and Prime Minister of Italy (b. 1861)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, journalist and Prime Minister of Italy (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, journalist and Prime Minister of Italy (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Swiss target shooter (b. 1866)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4heli\" title=\"<PERSON>\"><PERSON></a>, Swiss target shooter (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Konrad_<PERSON>%C3%A4heli\" title=\"<PERSON>\"><PERSON></a>, Swiss target shooter (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Konrad_St%C3%A4heli"}]}, {"year": "1933", "text": "<PERSON>, American actress and businesswoman (b. 1884)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Texas_Guinan\" title=\"Texas Guinan\"><PERSON> G<PERSON></a>, American actress and businesswoman (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texas_Guinan\" title=\"Texas Guinan\"><PERSON> G<PERSON></a>, American actress and businesswoman (b. 1884)", "links": [{"title": "Texas Guinan", "link": "https://wikipedia.org/wiki/Texas_Guinan"}]}, {"year": "1933", "text": "<PERSON><PERSON>, German mathematician and academic (b. 1856)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mathematician and academic (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mathematician and academic (b. 1856)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American painter and educator (b. 1851)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American physician and Baptist medical missionary (b. 1860)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and Baptist medical missionary (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and Baptist medical missionary (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Finnish activist (b. 1905)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish activist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish activist (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor, singer, composer, author and theatre manager/owner (b. 1878)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, composer, author and theatre manager/owner (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, composer, author and theatre manager/owner (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, French surgeon and biologist, Nobel Prize laureate (b. 1873)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French surgeon and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French surgeon and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1946", "text": "<PERSON>, Italian-American painter (b. 1877)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American painter (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American painter (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American suffragist (b. 1863)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American suffragist (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American suffragist (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, South African runner (b. 1889)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, South African runner (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, South African runner (b. 1889)", "links": [{"title": "<PERSON> (sprinter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter)"}]}, {"year": "1955", "text": "<PERSON>, French painter (b. 1883)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American pianist and composer (b. 1909)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Art_Tatum\" title=\"Art Tatum\"><PERSON></a>, American pianist and composer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Tatum\" title=\"Art Tatum\"><PERSON></a>, American pianist and composer (b. 1909)", "links": [{"title": "Art <PERSON>", "link": "https://wikipedia.org/wiki/Art_Tatum"}]}, {"year": "1960", "text": "<PERSON>, American actor (b. 1903)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bond\" title=\"<PERSON> Bond\"><PERSON></a>, American actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ward_Bond"}]}, {"year": "1960", "text": "<PERSON>, American pastor and theologian (b. 1895)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pastor and theologian (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pastor and theologian (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Estonian author and poet (b. 1891)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Estonian author and poet (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Estonian author and poet (b. 1891)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>it"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1925)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian-American actor, director, producer, and screenwriter (b. 1880)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, director, producer, and screenwriter (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, director, producer, and screenwriter (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Spanish poet and critic (b. 1902)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and critic (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and critic (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American pianist and conductor (b. 1916)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and conductor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and conductor (b. 1916)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American lieutenant, lawyer, and politician (b. 1893)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Lansdale_Ghiselin_Sasscer\" title=\"Lansdale Ghiselin Sasscer\">Lansdale Ghiselin <PERSON></a>, American lieutenant, lawyer, and politician (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lansdale_Ghiselin_Sasscer\" title=\"Lansdale Ghiselin Sasscer\">Lansdale Ghiselin Sasscer</a>, American lieutenant, lawyer, and politician (b. 1893)", "links": [{"title": "Lansdale Ghiselin <PERSON>", "link": "https://wikipedia.org/wiki/Lansdale_<PERSON><PERSON><PERSON>_<PERSON>cer"}]}, {"year": "1971", "text": "<PERSON>, American baseball player (b. 1925)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1925)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1972", "text": "<PERSON>, Estonian weightlifter (b. 1898)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weightlifter)\" title=\"<PERSON> (weightlifter)\"><PERSON></a>, Estonian weightlifter (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weightlifter)\" title=\"<PERSON> (weightlifter)\"><PERSON></a>, Estonian weightlifter (b. 1898)", "links": [{"title": "<PERSON> (weightlifter)", "link": "https://wikipedia.org/wiki/<PERSON>_(weightlifter)"}]}, {"year": "1975", "text": "<PERSON>, American geneticist and academic, Nobel Prize laureate (b. 1909)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1975", "text": "<PERSON>, American critic, essayist, short story writer, and educator (b. 1905)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic, essayist, short story writer, and educator (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic, essayist, short story writer, and educator (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lling"}]}, {"year": "1977", "text": "<PERSON>, French author and illustrator (b. 1926)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Goscinny\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Goscinny\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Goscinny"}]}, {"year": "1977", "text": "<PERSON>, Canadian-American violinist and conductor (b. 1902)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American violinist and conductor (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American violinist and conductor (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Russian-Soviet miner, the Stakhanovite movement has been named after him (b. 1906)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Soviet miner, the <a href=\"https://wikipedia.org/wiki/Stakhanovite_movement\" title=\"Stakhanovite movement\">Stakhanovite movement</a> has been named after him (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Soviet miner, the <a href=\"https://wikipedia.org/wiki/Stakhanovite_movement\" title=\"Stakhanovite movement\">Stakhanovite movement</a> has been named after him (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Stakhanovite movement", "link": "https://wikipedia.org/wiki/Stakhanovite_movement"}]}, {"year": "1979", "text": "<PERSON>, American cartoonist (b. 1909)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1909)", "links": [{"title": "Al <PERSON>", "link": "https://wikipedia.org/wiki/Al_Capp"}]}, {"year": "1980", "text": "<PERSON>, American musician (b. 1902)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, 16th Karmapa, Tibetan spiritual leader (b. 1924)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Rangjun<PERSON>_<PERSON><PERSON>_<PERSON>,_16th_Karmapa\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, 16th Karmapa\"><PERSON><PERSON><PERSON><PERSON>, 16th Karmapa</a>, Tibetan spiritual leader (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rang<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_16th_Karmapa\" class=\"mw-redirect\" title=\"<PERSON>ng<PERSON><PERSON>, 16th Karmapa\"><PERSON><PERSON><PERSON><PERSON>, 16th Karmapa</a>, Tibetan spiritual leader (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, 16th Karmapa", "link": "https://wikipedia.org/wiki/Rang<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_16th_<PERSON><PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Georgian linguist and philologist (b. 1898)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian linguist and philologist (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian linguist and philologist (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American religious leader, 12th President of The Church of Jesus Christ of Latter-day Saints (b. 1895)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 12th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 12th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1986", "text": "<PERSON>, German race car driver (b. 1899)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian actor, director, and screenwriter (b. 1930)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer (b. 1925)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doowop_musician)\" class=\"mw-redirect\" title=\"<PERSON> (doowop musician)\"><PERSON></a>, American singer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doowop_musician)\" class=\"mw-redirect\" title=\"<PERSON> (doowop musician)\"><PERSON></a>, American singer (b. 1925)", "links": [{"title": "<PERSON> (doowop musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doowop_musician)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Irish radio and television host (b. 1922)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish radio and television host (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish radio and television host (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Ukrainian-American pianist and composer (b. 1903)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American pianist and composer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American pianist and composer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Czech-English captain, publisher, and politician (b. 1923)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English captain, publisher, and politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English captain, publisher, and politician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor and businessman (b. 1908)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and businessman (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and businessman (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Russian-Turkish engineer and diplomat (b. 1912)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Turkish engineer and diplomat (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Turkish engineer and diplomat (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ad<PERSON>_<PERSON><PERSON>a"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American physicist and chess player (b. 1903)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Arpad_<PERSON>o\" title=\"Arp<PERSON>\"><PERSON><PERSON><PERSON></a>, American physicist and chess player (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arpad_<PERSON>o\" title=\"Arpad <PERSON>\"><PERSON><PERSON><PERSON></a>, American physicist and chess player (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arpad_Elo"}]}, {"year": "1992", "text": "<PERSON>, Dutch astronomer and academic (b. 1900)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and academic (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American saxophonist (b. 1934)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American author and screenwriter (b. 1946)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Latvian-English historian, author, and academic (b. 1909)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Isaiah_Berlin\" title=\"Isaiah Berlin\">Isaiah Berlin</a>, Latvian-English historian, author, and academic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isaiah_Berlin\" title=\"Isaiah Berlin\"><PERSON> Berlin</a>, Latvian-English historian, author, and academic (b. 1909)", "links": [{"title": "Isaiah Berlin", "link": "https://wikipedia.org/wiki/Isaiah_Berlin"}]}, {"year": "1997", "text": "<PERSON>, Australian rugby league player and sportscaster (b. 1964)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and sportscaster (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and sportscaster (b. 1964)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1999", "text": "<PERSON>, American director and screenwriter (b. 1931)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/James_Goldstone"}]}, {"year": "1999", "text": "<PERSON>, English-American architect, theorist and academic (b. 1920)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American architect, theorist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American architect, theorist and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American singer-songwriter and politician, 47th Governor of Louisiana (b. 1899)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Louisiana", "link": "https://wikipedia.org/wiki/Governor_of_Louisiana"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Tanzanian politician (b. 1926)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>_<PERSON>\" title=\"Bibi Titi Mohammed\"><PERSON><PERSON><PERSON></a>, Tanzanian politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>_<PERSON>\" title=\"Bibi Titi Mohammed\"><PERSON><PERSON><PERSON></a>, Tanzanian politician (b. 1926)", "links": [{"title": "Bibi Titi Mohammed", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Mohammed"}]}, {"year": "2001", "text": "<PERSON>, English director and producer (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English director and producer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English director and producer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American radio host, author, and activist (b. 1943)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host, author, and activist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host, author, and activist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter (b. 1940)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American-Dutch actor, singer, and dancer (b. 1932)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American-Dutch actor, singer, and dancer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American-Dutch actor, singer, and dancer (b. 1932)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "2005", "text": "<PERSON>, English novelist (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American soprano and actress (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Virginia_MacWatters\" title=\"Virginia MacWatters\"><PERSON></a>, American soprano and actress (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_MacWatters\" title=\"Virginia MacWatters\"><PERSON></a>, American soprano and actress (b. 1912)", "links": [{"title": "Virginia MacWatters", "link": "https://wikipedia.org/wiki/Virginia_MacWatters"}]}, {"year": "2005", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Wray\" title=\"<PERSON> Wray\"><PERSON></a>, American singer-songwriter and guitarist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Wray\" title=\"<PERSON> Wray\"><PERSON></a>, American singer-songwriter and guitarist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Link_Wray"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and politician, 16th Prime Minister of Turkey (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/B%C3%BClent_Ecevit\" title=\"<PERSON><PERSON><PERSON> Ecevit\"><PERSON><PERSON><PERSON></a>, Turkish journalist and politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%BClent_Ecevit\" title=\"<PERSON><PERSON><PERSON> Ecevit\"><PERSON><PERSON><PERSON></a>, Turkish journalist and politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>t", "link": "https://wikipedia.org/wiki/B%C3%BClent_Ecevit"}, {"title": "Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Turkey"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Swedish footballer and manager (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer and manager (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dholm"}]}, {"year": "2009", "text": "<PERSON>, Argentinian lawyer, historian, and academic (b. 1925)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Luna\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer, historian, and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Luna\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer, historian, and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Luna"}]}, {"year": "2010", "text": "<PERSON>, American actress and singer (b. 1944)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Romanian poet, journalist, and politician (b. 1943)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet, journalist, and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet, journalist, and politician (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adrian_P%C4%83unescu"}]}, {"year": "2010", "text": "<PERSON>, American soprano and actress (b. 1931)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Indian singer-songwriter, director, and poet (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zar<PERSON>\" title=\"B<PERSON><PERSON> Hazarika\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter, director, and poet (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zar<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ha<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter, director, and poet (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ika"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, French-American actress and dancer (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Oly<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ly<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-American actress and dancer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ly<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ly<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-American actress and dancer (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Olympe_<PERSON>na"}]}, {"year": "2012", "text": "<PERSON>, American composer and academic (b. 1908)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Argentinian actor, singer, director and screenwriter (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> F<PERSON>o\"><PERSON></a>, Argentinian actor, singer, director and screenwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> F<PERSON>o\"><PERSON></a>, Argentinian actor, singer, director and screenwriter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>o"}]}, {"year": "2012", "text": "<PERSON>, Canadian lawyer and politician, 30th Solicitor General of Canada (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Canada\" title=\"Solicitor General of Canada\">Solicitor General of Canada</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Canada\" title=\"Solicitor General of Canada\">Solicitor General of Canada</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Solicitor General of Canada", "link": "https://wikipedia.org/wiki/Solicitor_General_of_Canada"}]}, {"year": "2012", "text": "<PERSON>, South African lawyer and diplomat, Minister of Internal Affairs (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Home_Affairs_(South_Africa)\" title=\"Minister of Home Affairs (South Africa)\">Minister of Internal Affairs</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Home_Affairs_(South_Africa)\" title=\"Minister of Home Affairs (South Africa)\">Minister of Internal Affairs</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Home Affairs (South Africa)", "link": "https://wikipedia.org/wiki/Minister_of_Home_Affairs_(South_Africa)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian politician (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Habibollah_Asgaroladi\" title=\"Habibollah Asgaroladi\">Habibollah Asgaroladi</a>, Iranian politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Habibollah_Asgaroladi\" title=\"Habibollah Asgaroladi\">Habibollah Asgaroladi</a>, Iranian politician (b. 1932)", "links": [{"title": "Habibollah Asgaroladi", "link": "https://wikipedia.org/wiki/Habibollah_Asgaroladi"}]}, {"year": "2013", "text": "<PERSON>, Argentinian actor and screenwriter (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor and screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor and screenwriter (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3"}]}, {"year": "2013", "text": "<PERSON>, English soldier and pilot (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and pilot (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and pilot (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English genealogist and author (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(genealogist)\" title=\"<PERSON> (genealogist)\"><PERSON></a>, English genealogist and author (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(genealogist)\" title=\"<PERSON> (genealogist)\"><PERSON></a>, English genealogist and author (b. 1948)", "links": [{"title": "<PERSON> (genealogist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(genealogist)"}]}, {"year": "2013", "text": "<PERSON>, American chef and author (b. 1959)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Welsh footballer and manager (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer and manager (b. 1930)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2014", "text": "<PERSON><PERSON> Plata, French guitarist (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Manitas_de_Plata\" title=\"Manitas de Plata\"><PERSON><PERSON> de Plata</a>, French guitarist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manitas_de_Plata\" title=\"Manitas de Plata\">Man<PERSON> de Plata</a>, French guitarist (b. 1921)", "links": [{"title": "Manitas de Plata", "link": "https://wikipedia.org/wiki/Manitas_de_Plata"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and politician (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American ice hockey player (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" class=\"mw-redirect\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" class=\"mw-redirect\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player (b. 1927)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Tunisian-French author, poet, and scholar (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tunisian-French author, poet, and scholar (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tunisian-French author, poet, and scholar (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American engineer and car designer (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auto_customizer)\" title=\"<PERSON> (auto customizer)\"><PERSON></a>, American engineer and car designer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(auto_customizer)\" title=\"<PERSON> (auto customizer)\"><PERSON></a>, American engineer and car designer (b. 1925)", "links": [{"title": "<PERSON> (auto customizer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auto_customizer)"}]}, {"year": "2015", "text": "<PERSON>, Norwegian singer (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Sri Lankan businesswoman and philanthropist (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan businesswoman and philanthropist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan businesswoman and philanthropist (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ediri<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Polish general and politician, 11th Prime Minister of the People's Republic of Poland (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Czes%C5%82aw_Ki<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish general and politician, 11th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland\" class=\"mw-redirect\" title=\"List of Prime Ministers of Poland\">Prime Minister of the People's Republic of Poland</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Czes%C5%82aw_Ki<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish general and politician, 11th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland\" class=\"mw-redirect\" title=\"List of Prime Ministers of Poland\">Prime Minister of the People's Republic of Poland</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Czes%C5%82aw_<PERSON><PERSON><PERSON>ak"}, {"title": "List of Prime Ministers of Poland", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland"}]}, {"year": "2015", "text": "<PERSON>, German historian and academic (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, English actor (b. 1927)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1927)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Brazilian singer (b. 1995)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADlia_Mendon%C3%A7a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer (b. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADlia_Mendon%C3%A7a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer (b. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADlia_Mendon%C3%A7a"}]}, {"year": "2022", "text": "<PERSON>, American singer-songwriter, rapper, dancer and actor (b. 1987)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, rapper, dancer and actor (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, rapper, dancer and actor (b. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American martial artist and actor (b. 1939)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American martial artist and actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American martial artist and actor (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American writer (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American economist and business executive (b. 1961)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and business executive (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and business executive (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American voice actor (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Edwards\"><PERSON><PERSON></a>, American voice actor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Edwards\"><PERSON><PERSON></a>, American voice actor (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}