{"date": "September 3", "url": "https://wikipedia.org/wiki/September_3", "data": {"Events": [{"year": "36 BC", "text": "In the Battle of Naulochus, <PERSON><PERSON>, admiral of Octavian, defeats <PERSON><PERSON>, son of <PERSON><PERSON><PERSON>, thus ending Pompeian resistance to the Second Triumvirate.", "html": "36 BC - 36 BC - In the <a href=\"https://wikipedia.org/wiki/Battle_of_Naulochus\" title=\"Battle of Naulochus\">Battle of Naulochus</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_V<PERSON>sanius_Agrippa\" title=\"<PERSON> V<PERSON>sanius Agrippa\"><PERSON>ius <PERSON>rippa</a>, <a href=\"https://wikipedia.org/wiki/Admiral\" title=\"Admiral\">admiral</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_Augustus\" class=\"mw-redirect\" title=\"<PERSON> Augustus\"><PERSON><PERSON><PERSON></a>, defeats <a href=\"https://wikipedia.org/wiki/Sextus_Pompey\" title=\"Sextus Po<PERSON>ey\"><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Pompey\" title=\"Pompey\">Pompey</a>, thus ending Pompeian resistance to the <a href=\"https://wikipedia.org/wiki/Second_Triumvirate\" title=\"Second Triumvirate\">Second Triumvirate</a>.", "no_year_html": "36 BC - In the <a href=\"https://wikipedia.org/wiki/Battle_of_Naulochus\" title=\"Battle of Naulochus\">Battle of Naulochus</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_V<PERSON>sanius_Agrippa\" title=\"<PERSON> V<PERSON>sanius Agrippa\"><PERSON>ius <PERSON>rippa</a>, <a href=\"https://wikipedia.org/wiki/Admiral\" title=\"Admiral\">admiral</a> of <a href=\"https://wikipedia.org/wiki/Caesar_Augustus\" class=\"mw-redirect\" title=\"<PERSON> Augustus\"><PERSON><PERSON><PERSON></a>, defeats <a href=\"https://wikipedia.org/wiki/Sextus_Pompey\" title=\"Sextus Pompey\"><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Pompey\" title=\"Pompey\">Pompey</a>, thus ending Pompeian resistance to the <a href=\"https://wikipedia.org/wiki/Second_Triumvirate\" title=\"Second Triumvirate\">Second Triumvirate</a>.", "links": [{"title": "Battle of Naulochus", "link": "https://wikipedia.org/wiki/Battle_of_Naulochus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Admiral", "link": "https://wikipedia.org/wiki/Admiral"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sextus_Pompey"}, {"title": "Pompey", "link": "https://wikipedia.org/wiki/Pompey"}, {"title": "Second Triumvirate", "link": "https://wikipedia.org/wiki/Second_Triumvirate"}]}, {"year": "301", "text": "San Marino, one of the smallest nations in the world and the world's oldest republic still in existence, is founded by <PERSON> <PERSON>.", "html": "301 - <a href=\"https://wikipedia.org/wiki/San_Marino\" title=\"San Marino\">San Marino</a>, one of the <a href=\"https://wikipedia.org/wiki/List_of_countries_by_area\" class=\"mw-redirect\" title=\"List of countries by area\">smallest nations in the world</a> and the world's oldest <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> still in existence, is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_Marin<PERSON>\" title=\"<PERSON> Marin<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/San_Marino\" title=\"San Marino\">San Marino</a>, one of the <a href=\"https://wikipedia.org/wiki/List_of_countries_by_area\" class=\"mw-redirect\" title=\"List of countries by area\">smallest nations in the world</a> and the world's oldest <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> still in existence, is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Marin<PERSON>\"><PERSON> Marin<PERSON></a>.", "links": [{"title": "San Marino", "link": "https://wikipedia.org/wiki/San_Marino"}, {"title": "List of countries by area", "link": "https://wikipedia.org/wiki/List_of_countries_by_area"}, {"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "590", "text": "Consecration of <PERSON> (<PERSON> the Great).", "html": "590 - Consecration of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON> I</a> (<PERSON> the Great).", "no_year_html": "Consecration of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory I\">Pope <PERSON> I</a> (<PERSON> the Great).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "673", "text": "King <PERSON><PERSON><PERSON> of the Visigoths puts down a revolt by <PERSON><PERSON><PERSON>, governor of Nîmes (France) and rival for the throne.", "html": "673 - King <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_(king)\" title=\"<PERSON><PERSON><PERSON> (king)\">W<PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> puts down a revolt by <a href=\"https://wikipedia.org/wiki/Hilderic_of_N%C3%AEmes\" title=\"Hilder<PERSON> of Nîmes\"><PERSON><PERSON><PERSON></a>, governor of <a href=\"https://wikipedia.org/wiki/N%C3%AEmes\" title=\"Nîmes\"><PERSON><PERSON><PERSON></a> (France) and rival for the throne.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_(king)\" title=\"<PERSON><PERSON><PERSON> (king)\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> puts down a revolt by <a href=\"https://wikipedia.org/wiki/Hilderic_of_N%C3%AEmes\" title=\"Hilder<PERSON> of Nîmes\"><PERSON><PERSON><PERSON></a>, governor of <a href=\"https://wikipedia.org/wiki/N%C3%AEmes\" title=\"Nîmes\">N<PERSON><PERSON></a> (France) and rival for the throne.", "links": [{"title": "<PERSON><PERSON><PERSON> (king)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(king)"}, {"title": "Visigoths", "link": "https://wikipedia.org/wiki/Visigoths"}, {"title": "<PERSON><PERSON><PERSON> of Nîmes", "link": "https://wikipedia.org/wiki/Hilderic_of_N%C3%AEmes"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%AEmes"}]}, {"year": "863", "text": "Major Byzantine victory at the Battle of Lalakaon against an Arab raid.", "html": "863 - Major <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lalakaon\" title=\"Battle of Lalakaon\">Battle of Lalakaon</a> against an Arab raid.", "no_year_html": "Major <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lalakaon\" title=\"Battle of Lalakaon\">Battle of Lalakaon</a> against an Arab raid.", "links": [{"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Battle of Lalakaon", "link": "https://wikipedia.org/wiki/Battle_of_Lalakaon"}]}, {"year": "1189", "text": "<PERSON> of England (a.k.a. <PERSON> \"the Lionheart\") is crowned at Westminster.", "html": "1189 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (a.k.a. <PERSON> \"the Lionheart\") is crowned at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (a.k.a. <PERSON> \"the Lionheart\") is crowned at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1260", "text": "The Mamluks defeat the Mongols at the Battle of Ain Jalut in Palestine, marking their first decisive defeat and the point of maximum expansion of the Mongol Empire.", "html": "1260 - The <a href=\"https://wikipedia.org/wiki/Mamluk\" title=\"Mamluk\">Mamluks</a> defeat the <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongols</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ain_Jalut\" title=\"Battle of Ain Jalut\">Battle of Ain Jalut</a> in <a href=\"https://wikipedia.org/wiki/Palestine_(region)\" title=\"Palestine (region)\">Palestine</a>, marking their first decisive defeat and the point of maximum expansion of the <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol Empire</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mamluk\" title=\"Mamluk\">Mamluks</a> defeat the <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongols</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ain_Jalut\" title=\"Battle of Ain Jalut\">Battle of Ain Jalut</a> in <a href=\"https://wikipedia.org/wiki/Palestine_(region)\" title=\"Palestine (region)\">Palestine</a>, marking their first decisive defeat and the point of maximum expansion of the <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol Empire</a>.", "links": [{"title": "Mamluk", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Mongols", "link": "https://wikipedia.org/wiki/Mongols"}, {"title": "Battle of Ain Jalut", "link": "https://wikipedia.org/wiki/Battle_of_Ain_Jalut"}, {"title": "Palestine (region)", "link": "https://wikipedia.org/wiki/Palestine_(region)"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}]}, {"year": "1335", "text": "At the congress of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Hungary mediates a reconciliation between two neighboring monarchs, <PERSON> of Bohemia and <PERSON> of Poland.", "html": "1335 - At the congress of Visegrád <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> mediates a reconciliation between two neighboring monarchs, <a href=\"https://wikipedia.org/wiki/John_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Poland\" class=\"mw-redirect\" title=\"<PERSON> of Poland\"><PERSON> of Poland</a>.", "no_year_html": "At the congress of Visegrád <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> mediates a reconciliation between two neighboring monarchs, <a href=\"https://wikipedia.org/wiki/John_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Poland\" class=\"mw-redirect\" title=\"<PERSON> of Poland\"><PERSON> of Poland</a>.", "links": [{"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary"}, {"title": "<PERSON> of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON>_of_Bohemia"}, {"title": "<PERSON> of Poland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Poland"}]}, {"year": "1411", "text": "The Treaty of Selymbria is concluded between the Ottoman Empire and the Republic of Venice.", "html": "1411 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Selymbria\" title=\"Treaty of Selymbria\">Treaty of Selymbria</a> is concluded between the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> and the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Selymbria\" title=\"Treaty of Selymbria\">Treaty of Selymbria</a> is concluded between the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> and the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>.", "links": [{"title": "Treaty of Selymbria", "link": "https://wikipedia.org/wiki/Treaty_of_Selymbria"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}]}, {"year": "1650", "text": "Victory over the royalists in the Battle of Dunbar opens the way to Edinburgh for the New Model Army in the Third English Civil War.", "html": "1650 - Victory over the royalists in the <a href=\"https://wikipedia.org/wiki/Battle_of_Dunbar_(1650)\" title=\"Battle of Dunbar (1650)\">Battle of Dunbar</a> opens the way to Edinburgh for the New Model Army in the Third English Civil War.", "no_year_html": "Victory over the royalists in the <a href=\"https://wikipedia.org/wiki/Battle_of_Dunbar_(1650)\" title=\"Battle of Dunbar (1650)\">Battle of Dunbar</a> opens the way to Edinburgh for the New Model Army in the Third English Civil War.", "links": [{"title": "Battle of Dunbar (1650)", "link": "https://wikipedia.org/wiki/Battle_of_Dunbar_(1650)"}]}, {"year": "1651", "text": "The Battle of Worcester is the last significant action in the Wars of the Three Kingdoms.", "html": "1651 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Worcester\" title=\"Battle of Worcester\">Battle of Worcester</a> is the last significant action in the Wars of the Three Kingdoms.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Worcester\" title=\"Battle of Worcester\">Battle of Worcester</a> is the last significant action in the Wars of the Three Kingdoms.", "links": [{"title": "Battle of Worcester", "link": "https://wikipedia.org/wiki/Battle_of_Worcester"}]}, {"year": "1658", "text": "The death of <PERSON>; <PERSON> becomes Lord Protector of England.", "html": "1658 - The death of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>; <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Lord_Protector\" title=\"Lord Protector\">Lord Protector</a> of England.", "no_year_html": "The death of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>; <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Lord_Protector\" title=\"Lord Protector\">Lord Protector</a> of England.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Pro<PERSON>tor", "link": "https://wikipedia.org/wiki/<PERSON>_Protector"}]}, {"year": "1666", "text": "The Royal Exchange burns down in the Great Fire of London.", "html": "1666 - The <a href=\"https://wikipedia.org/wiki/Royal_Exchange,_London\" title=\"Royal Exchange, London\">Royal Exchange</a> burns down in the <a href=\"https://wikipedia.org/wiki/Great_Fire_of_London\" title=\"Great Fire of London\">Great Fire of London</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Exchange,_London\" title=\"Royal Exchange, London\">Royal Exchange</a> burns down in the <a href=\"https://wikipedia.org/wiki/Great_Fire_of_London\" title=\"Great Fire of London\">Great Fire of London</a>.", "links": [{"title": "Royal Exchange, London", "link": "https://wikipedia.org/wiki/Royal_Exchange,_London"}, {"title": "Great Fire of London", "link": "https://wikipedia.org/wiki/Great_Fire_of_London"}]}, {"year": "1777", "text": "American Revolutionary War: During the Battle of Cooch's Bridge, the Flag of the United States is flown in battle for the first time.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Cooch%27s_Bridge\" title=\"Battle of Cooch's Bridge\">Battle of Cooch's Bridge</a>, the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">Flag of the United States</a> is flown in battle for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Cooch%27s_Bridge\" title=\"Battle of Cooch's Bridge\">Battle of Cooch's Bridge</a>, the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">Flag of the United States</a> is flown in battle for the first time.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Cooch's Bridge", "link": "https://wikipedia.org/wiki/Battle_of_Cooch%27s_Bridge"}, {"title": "Flag of the United States", "link": "https://wikipedia.org/wiki/Flag_of_the_United_States"}]}, {"year": "1783", "text": "American Revolutionary War: The war ends with the signing of the Treaty of Paris by the United States and the Kingdom of Great Britain.", "html": "1783 - American Revolutionary War: The war ends with the signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1783)\" title=\"Treaty of Paris (1783)\">Treaty of Paris</a> by the United States and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a>.", "no_year_html": "American Revolutionary War: The war ends with the signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1783)\" title=\"Treaty of Paris (1783)\">Treaty of Paris</a> by the United States and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a>.", "links": [{"title": "Treaty of Paris (1783)", "link": "https://wikipedia.org/wiki/Treaty_of_Paris_(1783)"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}]}, {"year": "1798", "text": "The week long battle of St. George's Caye begins between Spain and Britain off the coast of Belize.", "html": "1798 - The week long battle of <a href=\"https://wikipedia.org/wiki/Battle_of_St._George%27s_Caye\" title=\"Battle of St. George's Caye\">St. George's Caye</a> begins between Spain and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Britain</a> off the coast of <a href=\"https://wikipedia.org/wiki/Belize\" title=\"Belize\">Belize</a>.", "no_year_html": "The week long battle of <a href=\"https://wikipedia.org/wiki/Battle_of_St._George%27s_Caye\" title=\"Battle of St. George's Caye\">St. George's Caye</a> begins between Spain and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Britain</a> off the coast of <a href=\"https://wikipedia.org/wiki/Belize\" title=\"Belize\">Belize</a>.", "links": [{"title": "Battle of St. George's Caye", "link": "https://wikipedia.org/wiki/Battle_of_St._George%27s_<PERSON><PERSON>e"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Belize", "link": "https://wikipedia.org/wiki/Belize"}]}, {"year": "1812", "text": "Twenty-four settlers are killed in the Pigeon Roost Massacre in Indiana.", "html": "1812 - Twenty-four settlers are killed in the <a href=\"https://wikipedia.org/wiki/Pigeon_Roost_State_Historic_Site#Pigeon_Roost_Massacre\" title=\"Pigeon Roost State Historic Site\">Pigeon Roost Massacre</a> in <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a>.", "no_year_html": "Twenty-four settlers are killed in the <a href=\"https://wikipedia.org/wiki/Pigeon_Roost_State_Historic_Site#Pigeon_Roost_Massacre\" title=\"Pigeon Roost State Historic Site\">Pigeon Roost Massacre</a> in <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a>.", "links": [{"title": "Pigeon Roost State Historic Site", "link": "https://wikipedia.org/wiki/Pigeon_Roost_State_Historic_Site#Pigeon_Roost_Massacre"}, {"title": "Indiana", "link": "https://wikipedia.org/wiki/Indiana"}]}, {"year": "1838", "text": "Future abolitionist <PERSON> escapes from slavery.", "html": "1838 - Future <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">abolitionist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes from <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a>.", "no_year_html": "Future <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">abolitionist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes from <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a>.", "links": [{"title": "Abolitionism in the United States", "link": "https://wikipedia.org/wiki/Abolitionism_in_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}]}, {"year": "1843", "text": "King <PERSON> of Greece is forced to grant a constitution following an uprising in Athens.", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Greece\" title=\"Otto of Greece\">King <PERSON> of Greece</a> is forced to grant a <a href=\"https://wikipedia.org/wiki/Greek_Constitution_of_1844\" title=\"Greek Constitution of 1844\">constitution</a> following an <a href=\"https://wikipedia.org/wiki/3_September_1843_Revolution\" title=\"3 September 1843 Revolution\">uprising</a> in Athens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Greece\" title=\"<PERSON> of Greece\">King <PERSON> of Greece</a> is forced to grant a <a href=\"https://wikipedia.org/wiki/Greek_Constitution_of_1844\" title=\"Greek Constitution of 1844\">constitution</a> following an <a href=\"https://wikipedia.org/wiki/3_September_1843_Revolution\" title=\"3 September 1843 Revolution\">uprising</a> in Athens.", "links": [{"title": "Otto of Greece", "link": "https://wikipedia.org/wiki/Otto_of_Greece"}, {"title": "Greek Constitution of 1844", "link": "https://wikipedia.org/wiki/Greek_Constitution_of_1844"}, {"title": "3 September 1843 Revolution", "link": "https://wikipedia.org/wiki/3_September_1843_Revolution"}]}, {"year": "1855", "text": "American Indian Wars: In Nebraska, 700 soldiers under United States General <PERSON> avenge the Grattan massacre by attacking a Sioux village and killing 100 men, women and children.", "html": "1855 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: In <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a>, 700 soldiers under United States General <a href=\"https://wikipedia.org/wiki/William_<PERSON>_<PERSON>\" title=\"William <PERSON>\"><PERSON></a> avenge the <a href=\"https://wikipedia.org/wiki/Grattan_massacre\" title=\"Grattan massacre\">Grattan massacre</a> by attacking a <a href=\"https://wikipedia.org/wiki/Sioux\" title=\"Sioux\">Sioux</a> village and killing 100 men, women and children.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: In <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a>, 700 soldiers under United States General <a href=\"https://wikipedia.org/wiki/William_<PERSON>_<PERSON>\" title=\"William <PERSON>\"><PERSON></a> avenge the <a href=\"https://wikipedia.org/wiki/Grattan_massacre\" title=\"Grattan massacre\">Grattan massacre</a> by attacking a <a href=\"https://wikipedia.org/wiki/Sioux\" title=\"Sioux\">Sioux</a> village and killing 100 men, women and children.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Nebraska", "link": "https://wikipedia.org/wiki/Nebraska"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Grattan massacre", "link": "https://wikipedia.org/wiki/Grattan_massacre"}, {"title": "Sioux", "link": "https://wikipedia.org/wiki/Sioux"}]}, {"year": "1861", "text": "American Civil War: Confederate General <PERSON><PERSON> invades neutral Kentucky, prompting the state legislature to ask for Union assistance.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Leonidas Polk\"><PERSON><PERSON></a> invades neutral <a href=\"https://wikipedia.org/wiki/Kentucky_in_the_American_Civil_War\" title=\"Kentucky in the American Civil War\">Kentucky</a>, prompting the state legislature to ask for <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> assistance.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Polk\" title=\"Leon<PERSON> Polk\"><PERSON><PERSON></a> invades neutral <a href=\"https://wikipedia.org/wiki/Kentucky_in_the_American_Civil_War\" title=\"Kentucky in the American Civil War\">Kentucky</a>, prompting the state legislature to ask for <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> assistance.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leonidas_Polk"}, {"title": "Kentucky in the American Civil War", "link": "https://wikipedia.org/wiki/Kentucky_in_the_American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}]}, {"year": "1870", "text": "Franco-Prussian War: The Siege of Metz begins, resulting in a decisive Prussian victory on October 23.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Metz_(1870)\" title=\"Siege of Metz (1870)\">Siege of Metz</a> begins, resulting in a decisive <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussian</a> victory on October 23.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Metz_(1870)\" title=\"Siege of Metz (1870)\">Siege of Metz</a> begins, resulting in a decisive <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussian</a> victory on October 23.", "links": [{"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}, {"title": "Siege of Metz (1870)", "link": "https://wikipedia.org/wiki/Siege_of_Metz_(1870)"}, {"title": "Kingdom of Prussia", "link": "https://wikipedia.org/wiki/Kingdom_of_Prussia"}]}, {"year": "1875", "text": "The first official game of polo is played in Argentina after being introduced by British ranchers.", "html": "1875 - The first official game of <a href=\"https://wikipedia.org/wiki/Polo\" title=\"Polo\">polo</a> is played in <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a> after being introduced by British ranchers.", "no_year_html": "The first official game of <a href=\"https://wikipedia.org/wiki/Polo\" title=\"Polo\">polo</a> is played in <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a> after being introduced by British ranchers.", "links": [{"title": "Polo", "link": "https://wikipedia.org/wiki/Polo"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}]}, {"year": "1878", "text": "Over 640 die when the crowded pleasure boat Princess <PERSON> collides with the Bywell Castle in the River Thames.", "html": "1878 - Over 640 die when the crowded pleasure boat <a href=\"https://wikipedia.org/wiki/Sinking_of_SS_Princess_<PERSON>\" title=\"Sinking of SS Princess <PERSON>\"><i>Princess <PERSON></i></a> collides with the <a href=\"https://wikipedia.org/wiki/SS_Bywell_Castle_(1869)\" title=\"SS Bywell Castle (1869)\"><i>Bywell Castle</i></a> in the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a>.", "no_year_html": "Over 640 die when the crowded pleasure boat <a href=\"https://wikipedia.org/wiki/Sinking_of_SS_Princess_<PERSON>\" title=\"Sinking of SS Princess <PERSON>\"><i>Princess <PERSON></i></a> collides with the <a href=\"https://wikipedia.org/wiki/SS_Bywell_Castle_(1869)\" title=\"SS Bywell Castle (1869)\"><i>Bywell Castle</i></a> in the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a>.", "links": [{"title": "Sinking of SS Princess <PERSON>", "link": "https://wikipedia.org/wiki/Sinking_of_SS_Princess_<PERSON>"}, {"title": "SS Bywell Castle (1869)", "link": "https://wikipedia.org/wiki/SS_Bywell_Castle_(1869)"}, {"title": "River Thames", "link": "https://wikipedia.org/wiki/River_Thames"}]}, {"year": "1879", "text": "Siege of the British Residency in Kabul: British envoy Sir <PERSON> and 72 men of the Guides are massacred by Afghan troops while defending the British Residency in Kabul. Their heroism and loyalty became famous and revered throughout the British Empire.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Siege_of_the_British_Residency_in_Kabul\" title=\"Siege of the British Residency in Kabul\">Siege of the British Residency in Kabul</a>: British envoy Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pierre <PERSON>\"><PERSON></a> and 72 men of the <a href=\"https://wikipedia.org/wiki/Corps_of_Guides_(India)\" title=\"Corps of Guides (India)\">Guides</a> are massacred by Afghan troops while defending the British Residency in <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>. Their heroism and loyalty became famous and revered throughout the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_the_British_Residency_in_Kabul\" title=\"Siege of the British Residency in Kabul\">Siege of the British Residency in Kabul</a>: British envoy Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pierre <PERSON>\"><PERSON></a> and 72 men of the <a href=\"https://wikipedia.org/wiki/Corps_of_Guides_(India)\" title=\"Corps of Guides (India)\">Guides</a> are massacred by Afghan troops while defending the British Residency in <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>. Their heroism and loyalty became famous and revered throughout the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "links": [{"title": "Siege of the British Residency in Kabul", "link": "https://wikipedia.org/wiki/Siege_of_the_British_Residency_in_Kabul"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Corps of Guides (India)", "link": "https://wikipedia.org/wiki/Corps_of_Guides_(India)"}, {"title": "Kabul", "link": "https://wikipedia.org/wiki/Kabul"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}]}, {"year": "1895", "text": "<PERSON> becomes the first openly paid professional American football player, when he was paid US$10 by <PERSON>, to play for the Latrobe Athletic Association in a 12-0 win over the Jeanette Athletic Association.", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first openly paid professional <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> player, when he was paid US$10 by <a href=\"https://wikipedia.org/wiki/<PERSON>(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, to play for the <a href=\"https://wikipedia.org/wiki/Latrobe_Athletic_Association\" title=\"Latrobe Athletic Association\">Latrobe Athletic Association</a> in a 12-0 win over the <a href=\"https://wikipedia.org/wiki/Jeanette_Athletic_Association\" class=\"mw-redirect\" title=\"Jeanette Athletic Association\">Jeanette Athletic Association</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first openly paid professional <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> player, when he was paid US$10 by <a href=\"https://wikipedia.org/wiki/<PERSON>(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, to play for the <a href=\"https://wikipedia.org/wiki/Latrobe_Athletic_Association\" title=\"Latrobe Athletic Association\">Latrobe Athletic Association</a> in a 12-0 win over the <a href=\"https://wikipedia.org/wiki/Jeanette_Athletic_Association\" class=\"mw-redirect\" title=\"Jeanette Athletic Association\">Jeanette Athletic Association</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American football", "link": "https://wikipedia.org/wiki/American_football"}, {"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}, {"title": "Latrobe Athletic Association", "link": "https://wikipedia.org/wiki/Latrobe_Athletic_Association"}, {"title": "Jeanette Athletic Association", "link": "https://wikipedia.org/wiki/Jeanette_Athletic_Association"}]}, {"year": "1911", "text": "A fire that started on Fraser's Million Dollar Pier destroys six to eight square blocks of Ocean Park, California.", "html": "1911 - A fire that started on <a href=\"https://wikipedia.org/wiki/Fraser%27s_Million_Dollar_Pier\" title=\"Fraser's Million Dollar Pier\">Fraser's Million Dollar Pier</a> destroys six to eight square blocks of <a href=\"https://wikipedia.org/wiki/Ocean_Park,_Santa_Monica\" title=\"Ocean Park, Santa Monica\">Ocean Park, California</a>.", "no_year_html": "A fire that started on <a href=\"https://wikipedia.org/wiki/Fraser%27s_Million_Dollar_Pier\" title=\"Fraser's Million Dollar Pier\">Fraser's Million Dollar Pier</a> destroys six to eight square blocks of <a href=\"https://wikipedia.org/wiki/Ocean_Park,_Santa_Monica\" title=\"Ocean Park, Santa Monica\">Ocean Park, California</a>.", "links": [{"title": "Fraser's Million Dollar Pier", "link": "https://wikipedia.org/wiki/Fraser%27s_Million_Dollar_Pier"}, {"title": "Ocean Park, Santa Monica", "link": "https://wikipedia.org/wiki/Ocean_Park,_Santa_Monica"}]}, {"year": "1914", "text": "<PERSON>, Prince of Albania leaves the country after just six months due to opposition to his rule.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Albania\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Albania\"><PERSON>, Prince of Albania</a> leaves the country after just six months due to opposition to his rule.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Albania\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Albania\"><PERSON>, Prince of Albania</a> leaves the country after just six months due to opposition to his rule.", "links": [{"title": "<PERSON>, Prince of Albania", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Albania"}]}, {"year": "1914", "text": "French composer <PERSON><PERSON><PERSON><PERSON> is killed defending his estate against invading German soldiers.", "html": "1914 - French composer <a href=\"https://wikipedia.org/wiki/Alb%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is killed defending his estate against invading German soldiers.", "no_year_html": "French composer <a href=\"https://wikipedia.org/wiki/Alb%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is killed defending his estate against invading German soldiers.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alb%C3%A9ric_Magnard"}]}, {"year": "1914", "text": "World War I: Start of the Battle of Grand Couronné, a German assault against French positions on high ground near the city of Nancy.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Start of the <a href=\"https://wikipedia.org/wiki/Battle_of_Grand_Couronn%C3%A9\" title=\"Battle of Grand Couronné\">Battle of Grand Couronné</a>, a German assault against French positions on high ground near the city of <a href=\"https://wikipedia.org/wiki/Nancy,_France\" title=\"Nancy, France\">Nancy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Start of the <a href=\"https://wikipedia.org/wiki/Battle_of_Grand_Couronn%C3%A9\" title=\"Battle of Grand Couronné\">Battle of Grand Couronné</a>, a German assault against French positions on high ground near the city of <a href=\"https://wikipedia.org/wiki/Nancy,_France\" title=\"Nancy, France\">Nancy</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Grand Couronné", "link": "https://wikipedia.org/wiki/Battle_of_Grand_Couronn%C3%A9"}, {"title": "Nancy, France", "link": "https://wikipedia.org/wiki/Nancy,_France"}]}, {"year": "1916", "text": "World War I: <PERSON><PERSON> destroys the German airship Schütte-Lanz SL 11 over Cuffley, north of London; the first German airship to be shot down on British soil.", "html": "1916 - World War I: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> destroys the German <a href=\"https://wikipedia.org/wiki/Airship\" title=\"Airship\">airship</a> <a href=\"https://wikipedia.org/wiki/Sch%C3%BCtte-Lanz_SL_11\" title=\"Schütte-Lanz SL 11\">Schütte-Lanz SL 11</a> over <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Cuffley</a>, north of London; the first German airship to be shot down on British soil.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> destroys the German <a href=\"https://wikipedia.org/wiki/Airship\" title=\"Airship\">airship</a> <a href=\"https://wikipedia.org/wiki/Sch%C3%BCtte-Lanz_SL_11\" title=\"Schütte-Lanz SL 11\">Schütte-Lanz SL 11</a> over <a href=\"https://wikipedia.org/wiki/C<PERSON>ley\" title=\"C<PERSON><PERSON>\">Cuffley</a>, north of London; the first German airship to be shot down on British soil.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Airship", "link": "https://wikipedia.org/wiki/Airship"}, {"title": "Schütte-Lanz SL 11", "link": "https://wikipedia.org/wiki/Sch%C3%<PERSON>tte-Lanz_SL_11"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1925", "text": "USS Shenandoah, the United States' first American-built rigid airship, was destroyed in a squall line over Noble County, Ohio. Fourteen of her 42-man crew perished, including her commander, <PERSON>.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/USS_Shenandoah_(ZR-1)\" title=\"USS Shenandoah (ZR-1)\">USS <i>Shenandoah</i></a>, the United States' first American-built rigid airship, was destroyed in a <a href=\"https://wikipedia.org/wiki/Squall_line\" title=\"Squall line\">squall line</a> over <a href=\"https://wikipedia.org/wiki/Noble_County,_Ohio\" title=\"Noble County, Ohio\">Noble County, Ohio</a>. Fourteen of her 42-man crew perished, including her commander, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USS_Shenandoah_(ZR-1)\" title=\"USS Shenandoah (ZR-1)\">USS <i>Shenandoah</i></a>, the United States' first American-built rigid airship, was destroyed in a <a href=\"https://wikipedia.org/wiki/Squall_line\" title=\"Squall line\">squall line</a> over <a href=\"https://wikipedia.org/wiki/Noble_County,_Ohio\" title=\"Noble County, Ohio\">Noble County, Ohio</a>. Fourteen of her 42-man crew perished, including her commander, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "USS Shenandoah (ZR-1)", "link": "https://wikipedia.org/wiki/USS_Shenandoah_(ZR-1)"}, {"title": "Squall line", "link": "https://wikipedia.org/wiki/Squall_line"}, {"title": "Noble County, Ohio", "link": "https://wikipedia.org/wiki/Noble_County,_Ohio"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> is the first man to reach the highest point in the Soviet Union, Communism Peak (now called Ismoil Somoni Peak and situated in Tajikistan) (7495 m).", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is the first man to reach the highest point in the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, Communism Peak (now called <a href=\"https://wikipedia.org/wiki/Ismoil_Somoni_Peak\" title=\"Ismoil Somoni Peak\">Ismoil Somoni Peak</a> and situated in <a href=\"https://wikipedia.org/wiki/Tajikistan\" title=\"Tajikistan\">Tajikistan</a>) (7495 m).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is the first man to reach the highest point in the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, Communism Peak (now called <a href=\"https://wikipedia.org/wiki/Ismoil_Somoni_Peak\" title=\"Ismoil Somoni Peak\">Ismoil Somoni Peak</a> and situated in <a href=\"https://wikipedia.org/wiki/Tajikistan\" title=\"Tajikistan\">Tajikistan</a>) (7495 m).", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Is<PERSON>il <PERSON>", "link": "https://wikipedia.org/wiki/Ismoil_Somoni_Peak"}, {"title": "Tajikistan", "link": "https://wikipedia.org/wiki/Tajikistan"}]}, {"year": "1935", "text": "Sir <PERSON> reaches a speed of 304.331 miles per hour on the Bonneville Salt Flats in Utah, becoming the first person to drive an automobile over 300 mph.", "html": "1935 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reaches a speed of 304.331 miles per hour on the <a href=\"https://wikipedia.org/wiki/Bonneville_Salt_Flats\" title=\"Bonneville Salt Flats\">Bonneville Salt Flats</a> in <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a>, becoming the first person to drive an <a href=\"https://wikipedia.org/wiki/Automobile\" class=\"mw-redirect\" title=\"Automobile\">automobile</a> over 300 mph.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reaches a speed of 304.331 miles per hour on the <a href=\"https://wikipedia.org/wiki/Bonneville_Salt_Flats\" title=\"Bonneville Salt Flats\">Bonneville Salt Flats</a> in <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a>, becoming the first person to drive an <a href=\"https://wikipedia.org/wiki/Automobile\" class=\"mw-redirect\" title=\"Automobile\">automobile</a> over 300 mph.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bonneville Salt Flats", "link": "https://wikipedia.org/wiki/Bonneville_Salt_Flats"}, {"title": "Utah", "link": "https://wikipedia.org/wiki/Utah"}, {"title": "Automobile", "link": "https://wikipedia.org/wiki/Automobile"}]}, {"year": "1939", "text": "World War II: France, the United Kingdom, New Zealand and Australia declare war on Germany after the invasion of Poland, forming the Allied nations. The Viceroy of India also declares war, but without consulting the provincial legislatures.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: France, the United Kingdom, New Zealand and Australia declare war on Germany after the invasion of Poland, forming the Allied nations. The Viceroy of India also <a href=\"https://wikipedia.org/wiki/India_in_World_War_II\" title=\"India in World War II\">declares war</a>, but without consulting the provincial legislatures.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: France, the United Kingdom, New Zealand and Australia declare war on Germany after the invasion of Poland, forming the Allied nations. The Viceroy of India also <a href=\"https://wikipedia.org/wiki/India_in_World_War_II\" title=\"India in World War II\">declares war</a>, but without consulting the provincial legislatures.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "India in World War II", "link": "https://wikipedia.org/wiki/India_in_World_War_II"}]}, {"year": "1939", "text": "World War II: The United Kingdom and France begin a naval blockade of Germany that lasts until the end of the war. This also marks the beginning of the Battle of the Atlantic.", "html": "1939 - World War II: The United Kingdom and France begin a <a href=\"https://wikipedia.org/wiki/Blockade_of_Germany_(1939%E2%80%9345)\" class=\"mw-redirect\" title=\"Blockade of Germany (1939-45)\">naval blockade of Germany</a> that lasts until the end of the war. This also marks the beginning of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Atlantic\" title=\"Battle of the Atlantic\">Battle of the Atlantic</a>.", "no_year_html": "World War II: The United Kingdom and France begin a <a href=\"https://wikipedia.org/wiki/Blockade_of_Germany_(1939%E2%80%9345)\" class=\"mw-redirect\" title=\"Blockade of Germany (1939-45)\">naval blockade of Germany</a> that lasts until the end of the war. This also marks the beginning of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Atlantic\" title=\"Battle of the Atlantic\">Battle of the Atlantic</a>.", "links": [{"title": "Blockade of Germany (1939-45)", "link": "https://wikipedia.org/wiki/Blockade_of_Germany_(1939%E2%80%9345)"}, {"title": "Battle of the Atlantic", "link": "https://wikipedia.org/wiki/Battle_of_the_Atlantic"}]}, {"year": "1941", "text": "The Holocaust: <PERSON>, deputy camp commandant of the Auschwitz concentration camp, experiments with the use of Zyklon B in the gassing of Soviet POWs.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, deputy camp commandant of the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>, experiments with the use of <a href=\"https://wikipedia.org/wiki/Zyklon_B\" title=\"Zyklon B\">Zyklon B</a> in the gassing of Soviet POWs.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, deputy camp commandant of the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>, experiments with the use of <a href=\"https://wikipedia.org/wiki/Zyklon_B\" title=\"Zyklon B\">Zyklon B</a> in the gassing of Soviet POWs.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}, {"title": "Zyklon B", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>lon_<PERSON>"}]}, {"year": "1942", "text": "World War II: In response to news of its coming liquidation, <PERSON><PERSON> leads an uprising in the Ghetto of Lakhva (present-day Belarus).", "html": "1942 - World War II: In response to news of its coming liquidation, <a href=\"https://wikipedia.org/wiki/Dov_Lopatyn\" class=\"mw-redirect\" title=\"Dov Lopatyn\"><PERSON><PERSON></a> leads an uprising in the <a href=\"https://wikipedia.org/wiki/%C5%81achwa_Ghetto#Uprising_and_massacre\" title=\"Łachwa Ghetto\">Ghetto</a> of <a href=\"https://wikipedia.org/wiki/Lakhva\" title=\"Lakhva\"><PERSON>kh<PERSON></a> (present-day <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a>).", "no_year_html": "World War II: In response to news of its coming liquidation, <a href=\"https://wikipedia.org/wiki/Dov_Lopatyn\" class=\"mw-redirect\" title=\"Dov Lopatyn\"><PERSON><PERSON></a> leads an uprising in the <a href=\"https://wikipedia.org/wiki/%C5%81achwa_Ghetto#Uprising_and_massacre\" title=\"Łachwa Ghetto\">Ghetto</a> of <a href=\"https://wikipedia.org/wiki/Lakhva\" title=\"Lakhva\"><PERSON><PERSON><PERSON></a> (present-day <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a>).", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dov_<PERSON>tyn"}, {"title": "Łachwa Ghetto", "link": "https://wikipedia.org/wiki/%C5%81achwa_Ghetto#Uprising_and_massacre"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Belarus", "link": "https://wikipedia.org/wiki/Belarus"}]}, {"year": "1943", "text": "World War II: British and Canadian troops land on the Italian mainland. On the same day, <PERSON> and <PERSON> sign the Armistice of Cassibile, although it is not announced for another five days.", "html": "1943 - World War II: British and Canadian troops <a href=\"https://wikipedia.org/wiki/Operation_Baytown\" title=\"Operation Baytown\">land on the Italian mainland</a>. On the same day, <PERSON> and <PERSON> sign the <a href=\"https://wikipedia.org/wiki/Armistice_of_Cassibile\" title=\"Armistice of Cassibile\">Armistice of Cassibile</a>, although it is not announced for another five days.", "no_year_html": "World War II: British and Canadian troops <a href=\"https://wikipedia.org/wiki/Operation_Baytown\" title=\"Operation Baytown\">land on the Italian mainland</a>. On the same day, <PERSON> and <PERSON> sign the <a href=\"https://wikipedia.org/wiki/Armistice_of_Cassibile\" title=\"Armistice of Cassibile\">Armistice of Cassibile</a>, although it is not announced for another five days.", "links": [{"title": "Operation Baytown", "link": "https://wikipedia.org/wiki/Operation_Baytown"}, {"title": "Armistice of Cassibile", "link": "https://wikipedia.org/wiki/Armistice_of_Cassibile"}]}, {"year": "1944", "text": "Holocaust: Di<PERSON>t <PERSON> and her family are placed on the last transport train from the Westerbork transit camp to the Auschwitz concentration camp, arriving three days later.", "html": "1944 - Holocaust: <a href=\"https://wikipedia.org/wiki/Diarist\" class=\"mw-redirect\" title=\"Diarist\">Diarist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and her family are placed on the last transport train from the <a href=\"https://wikipedia.org/wiki/Westerbork_transit_camp\" title=\"Westerbork transit camp\">Westerbork transit camp</a> to the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>, arriving three days later.", "no_year_html": "Holocaust: <a href=\"https://wikipedia.org/wiki/Diarist\" class=\"mw-redirect\" title=\"Diarist\">Diarist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and her family are placed on the last transport train from the <a href=\"https://wikipedia.org/wiki/Westerbork_transit_camp\" title=\"Westerbork transit camp\">Westerbork transit camp</a> to the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>, arriving three days later.", "links": [{"title": "Diarist", "link": "https://wikipedia.org/wiki/Diarist"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Westerbork transit camp", "link": "https://wikipedia.org/wiki/Westerbork_transit_camp"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}]}, {"year": "1945", "text": "A three-day celebration begins in China, following the Victory over Japan Day on September 2.", "html": "1945 - A three-day celebration begins in China, following the <a href=\"https://wikipedia.org/wiki/Victory_over_Japan_Day#China\" title=\"Victory over Japan Day\">Victory over Japan Day</a> on September 2.", "no_year_html": "A three-day celebration begins in China, following the <a href=\"https://wikipedia.org/wiki/Victory_over_Japan_Day#China\" title=\"Victory over Japan Day\">Victory over Japan Day</a> on September 2.", "links": [{"title": "Victory over Japan Day", "link": "https://wikipedia.org/wiki/Victory_over_Japan_Day#China"}]}, {"year": "1950", "text": "\"<PERSON><PERSON>\" <PERSON><PERSON> becomes the first Formula One Drivers' champion after winning the 1950 Italian Grand Prix.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">\"<PERSON><PERSON>\" <PERSON><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">Formula One Drivers' champion</a> after winning the <a href=\"https://wikipedia.org/wiki/1950_Italian_Grand_Prix\" title=\"1950 Italian Grand Prix\">1950 Italian Grand Prix</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Far<PERSON>\">\"<PERSON><PERSON>\" <PERSON><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">Formula One Drivers' champion</a> after winning the <a href=\"https://wikipedia.org/wiki/1950_Italian_Grand_Prix\" title=\"1950 Italian Grand Prix\">1950 Italian Grand Prix</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Formula One World Drivers' Champions", "link": "https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions"}, {"title": "1950 Italian Grand Prix", "link": "https://wikipedia.org/wiki/1950_Italian_Grand_Prix"}]}, {"year": "1954", "text": "The People's Liberation Army begins shelling the Republic of China-controlled islands of Quemoy, starting the First Taiwan Strait Crisis.", "html": "1954 - The <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a> begins shelling the <a href=\"https://wikipedia.org/wiki/Republic_of_China\" class=\"mw-redirect\" title=\"Republic of China\">Republic of China</a>-controlled islands of <a href=\"https://wikipedia.org/wiki/Quemoy\" class=\"mw-redirect\" title=\"Quemoy\">Que<PERSON>y</a>, starting the <a href=\"https://wikipedia.org/wiki/First_Taiwan_Strait_Crisis\" title=\"First Taiwan Strait Crisis\">First Taiwan Strait Crisis</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a> begins shelling the <a href=\"https://wikipedia.org/wiki/Republic_of_China\" class=\"mw-redirect\" title=\"Republic of China\">Republic of China</a>-controlled islands of <a href=\"https://wikipedia.org/wiki/Quemoy\" class=\"mw-redirect\" title=\"Quemoy\"><PERSON><PERSON><PERSON></a>, starting the <a href=\"https://wikipedia.org/wiki/First_Taiwan_Strait_Crisis\" title=\"First Taiwan Strait Crisis\">First Taiwan Strait Crisis</a>.", "links": [{"title": "People's Liberation Army", "link": "https://wikipedia.org/wiki/People%27s_Liberation_Army"}, {"title": "Republic of China", "link": "https://wikipedia.org/wiki/Republic_of_China"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>moy"}, {"title": "First Taiwan Strait Crisis", "link": "https://wikipedia.org/wiki/First_Taiwan_Strait_Crisis"}]}, {"year": "1967", "text": "Dagen H in Sweden: Traffic changes from driving on the left to driving on the right overnight.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Dagen_H\" title=\"Dagen H\">Dagen H</a> in Sweden: Traffic changes from driving on the left to driving on the right overnight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dagen_H\" title=\"Dagen H\">Dagen H</a> in Sweden: Traffic changes from driving on the left to driving on the right overnight.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dagen_H"}]}, {"year": "1971", "text": "Qatar becomes an independent state.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Qatar\" title=\"Qatar\">Qatar</a> becomes an independent state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qatar\" title=\"Qatar\">Qatar</a> becomes an independent state.", "links": [{"title": "Qatar", "link": "https://wikipedia.org/wiki/Qatar"}]}, {"year": "1976", "text": "Viking program: The American Viking 2 spacecraft lands at Utopia Planitia on Mars.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Viking_program\" title=\"Viking program\">Viking program</a>: The American <i><a href=\"https://wikipedia.org/wiki/Viking_2\" title=\"Viking 2\">Viking 2</a></i> spacecraft lands at <a href=\"https://wikipedia.org/wiki/Utopia_Planitia\" title=\"Utopia Planitia\">Utopia Planitia</a> on <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viking_program\" title=\"Viking program\">Viking program</a>: The American <i><a href=\"https://wikipedia.org/wiki/Viking_2\" title=\"Viking 2\">Viking 2</a></i> spacecraft lands at <a href=\"https://wikipedia.org/wiki/Utopia_Planitia\" title=\"Utopia Planitia\">Utopia Planitia</a> on <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "Viking program", "link": "https://wikipedia.org/wiki/Viking_program"}, {"title": "Viking 2", "link": "https://wikipedia.org/wiki/Viking_2"}, {"title": "Utopia Planitia", "link": "https://wikipedia.org/wiki/Utopia_Planitia"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "1978", "text": "During the Rhodesian Bush War a group of ZIPRA guerrillas shot down civilian Vickers Viscount aircraft (Air Rhodesia Flight 825) with a Soviet-made SAM Strela-2; of 56 passengers and crew 38 people died in crash, 10 were massacred by the guerrillas at the site.", "html": "1978 - During the <a href=\"https://wikipedia.org/wiki/Rhodesian_Bush_War\" title=\"Rhodesian Bush War\">Rhodesian Bush War</a> a group of <a href=\"https://wikipedia.org/wiki/Zimbabwe_People%27s_Revolutionary_Army\" title=\"Zimbabwe People's Revolutionary Army\">ZIPRA</a> guerrillas shot down civilian <a href=\"https://wikipedia.org/wiki/Vickers_Viscount\" title=\"Vickers Viscount\">Vickers Viscount</a> aircraft (<a href=\"https://wikipedia.org/wiki/Air_Rhodesia_Flight_825\" title=\"Air Rhodesia Flight 825\">Air Rhodesia Flight 825</a>) with a Soviet-made <a href=\"https://wikipedia.org/wiki/9K32_Strela-2\" title=\"9K32 Strela-2\">SAM Strela-2</a>; of 56 passengers and crew 38 people died in crash, 10 were massacred by the guerrillas at the site.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Rhodesian_Bush_War\" title=\"Rhodesian Bush War\">Rhodesian Bush War</a> a group of <a href=\"https://wikipedia.org/wiki/Zimbabwe_People%27s_Revolutionary_Army\" title=\"Zimbabwe People's Revolutionary Army\">ZIPRA</a> guerrillas shot down civilian <a href=\"https://wikipedia.org/wiki/Vickers_Viscount\" title=\"Vickers Viscount\">Vickers Viscount</a> aircraft (<a href=\"https://wikipedia.org/wiki/Air_Rhodesia_Flight_825\" title=\"Air Rhodesia Flight 825\">Air Rhodesia Flight 825</a>) with a Soviet-made <a href=\"https://wikipedia.org/wiki/9K32_Strela-2\" title=\"9K32 Strela-2\">SAM Strela-2</a>; of 56 passengers and crew 38 people died in crash, 10 were massacred by the guerrillas at the site.", "links": [{"title": "Rhodesian Bush War", "link": "https://wikipedia.org/wiki/Rhodesian_Bush_War"}, {"title": "Zimbabwe People's Revolutionary Army", "link": "https://wikipedia.org/wiki/Zimbabwe_People%27s_Revolutionary_Army"}, {"title": "Vickers Viscount", "link": "https://wikipedia.org/wiki/Vickers_Viscount"}, {"title": "Air Rhodesia Flight 825", "link": "https://wikipedia.org/wiki/Air_Rhodesia_Flight_825"}, {"title": "9K32 Strela-2", "link": "https://wikipedia.org/wiki/9K32_Strela-2"}]}, {"year": "1981", "text": "The Convention on the Elimination of All Forms of Discrimination Against Women, an international bill of rights for women, is instituted by the United Nations.", "html": "1981 - The <a href=\"https://wikipedia.org/wiki/Convention_on_the_Elimination_of_All_Forms_of_Discrimination_Against_Women\" title=\"Convention on the Elimination of All Forms of Discrimination Against Women\">Convention on the Elimination of All Forms of Discrimination Against Women</a>, an international <a href=\"https://wikipedia.org/wiki/Bill_of_rights\" title=\"Bill of rights\">bill of rights</a> for women, is instituted by the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Convention_on_the_Elimination_of_All_Forms_of_Discrimination_Against_Women\" title=\"Convention on the Elimination of All Forms of Discrimination Against Women\">Convention on the Elimination of All Forms of Discrimination Against Women</a>, an international <a href=\"https://wikipedia.org/wiki/Bill_of_rights\" title=\"Bill of rights\">bill of rights</a> for women, is instituted by the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Convention on the Elimination of All Forms of Discrimination Against Women", "link": "https://wikipedia.org/wiki/Convention_on_the_Elimination_of_All_Forms_of_Discrimination_Against_Women"}, {"title": "Bill of rights", "link": "https://wikipedia.org/wiki/Bill_of_rights"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1987", "text": "In a coup d'état in Burundi, President <PERSON><PERSON><PERSON> is deposed by Major <PERSON>.", "html": "1987 - In a <a href=\"https://wikipedia.org/wiki/1987_Burundian_coup_d%27%C3%A9tat\" title=\"1987 Burundian coup d'état\">coup d'état in Burundi</a>, President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a> is deposed by Major <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "In a <a href=\"https://wikipedia.org/wiki/1987_Burundian_coup_d%27%C3%A9tat\" title=\"1987 Burundian coup d'état\">coup d'état in Burundi</a>, President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is deposed by Major <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "1987 Burundian coup d'état", "link": "https://wikipedia.org/wiki/1987_Burundian_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "Cubana de Aviación Flight 9046 crashes into a residential area of Havana shortly after takeoff from José Martí International Airport, killing 150.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_9046\" title=\"Cubana de Aviación Flight 9046\">Cubana de Aviación Flight 9046</a> crashes into a residential area of <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a> shortly after takeoff from <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD_International_Airport\" title=\"José Martí International Airport\">José Martí International Airport</a>, killing 150.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_9046\" title=\"Cubana de Aviación Flight 9046\">Cubana de Aviación Flight 9046</a> crashes into a residential area of <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a> shortly after takeoff from <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD_International_Airport\" title=\"José Martí International Airport\">José Martí International Airport</a>, killing 150.", "links": [{"title": "Cubana de Aviación Flight 9046", "link": "https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_9046"}, {"title": "Havana", "link": "https://wikipedia.org/wiki/Havana"}, {"title": "José Martí International Airport", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD_International_Airport"}]}, {"year": "1989", "text": "Varig Flight 254 crashes in the Amazon rainforest near São José do Xingu in Brazil, killing 12.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Varig_Flight_254\" title=\"Varig Flight 254\">Varig Flight 254</a> crashes in the <a href=\"https://wikipedia.org/wiki/Amazon_rainforest\" title=\"Amazon rainforest\">Amazon rainforest</a> near <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Jos%C3%A9_do_Xingu\" title=\"São José do Xingu\">São <PERSON> Xing<PERSON></a> in Brazil, killing 12.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Varig_Flight_254\" title=\"Varig Flight 254\">Varig Flight 254</a> crashes in the <a href=\"https://wikipedia.org/wiki/Amazon_rainforest\" title=\"Amazon rainforest\">Amazon rainforest</a> near <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Jos%C3%A9_do_Xingu\" title=\"São José do Xingu\">São José do Xingu</a> in Brazil, killing 12.", "links": [{"title": "Varig Flight 254", "link": "https://wikipedia.org/wiki/Varig_Flight_254"}, {"title": "Amazon rainforest", "link": "https://wikipedia.org/wiki/Amazon_rainforest"}, {"title": "São José do Xingu", "link": "https://wikipedia.org/wiki/S%C3%A3o_Jos%C3%A9_do_Xingu"}]}, {"year": "1997", "text": "Vietnam Airlines Flight 815 (Tupolev Tu-134) crashes on approach into Phnom Penh airport, killing 64.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Vietnam_Airlines\" title=\"Vietnam Airlines\">Vietnam Airlines</a> <a href=\"https://wikipedia.org/wiki/Vietnam_Airlines_Flight_815\" title=\"Vietnam Airlines Flight 815\">Flight 815</a> (<a href=\"https://wikipedia.org/wiki/Tupolev_Tu-134\" title=\"Tupolev Tu-134\">Tupolev Tu-134</a>) crashes on approach into <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a> airport, killing 64.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_Airlines\" title=\"Vietnam Airlines\">Vietnam Airlines</a> <a href=\"https://wikipedia.org/wiki/Vietnam_Airlines_Flight_815\" title=\"Vietnam Airlines Flight 815\">Flight 815</a> (<a href=\"https://wikipedia.org/wiki/Tupolev_Tu-134\" title=\"Tupolev Tu-134\">Tupolev Tu-134</a>) crashes on approach into <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a> airport, killing 64.", "links": [{"title": "Vietnam Airlines", "link": "https://wikipedia.org/wiki/Vietnam_Airlines"}, {"title": "Vietnam Airlines Flight 815", "link": "https://wikipedia.org/wiki/Vietnam_Airlines_Flight_815"}, {"title": "Tupolev Tu-134", "link": "https://wikipedia.org/wiki/Tupolev_Tu-134"}, {"title": "Phnom Penh", "link": "https://wikipedia.org/wiki/Phnom_Penh"}]}, {"year": "2001", "text": "In Belfast, Protestant loyalists begin a picket of Holy Cross, a Catholic primary school for girls.", "html": "2001 - In <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, Protestant <a href=\"https://wikipedia.org/wiki/Ulster_loyalism\" title=\"Ulster loyalism\">loyalists</a> begin a <a href=\"https://wikipedia.org/wiki/Holy_Cross_dispute\" title=\"Holy Cross dispute\">picket of Holy Cross</a>, a Catholic <a href=\"https://wikipedia.org/wiki/Primary_school\" title=\"Primary school\">primary school</a> for girls.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, Protestant <a href=\"https://wikipedia.org/wiki/Ulster_loyalism\" title=\"Ulster loyalism\">loyalists</a> begin a <a href=\"https://wikipedia.org/wiki/Holy_Cross_dispute\" title=\"Holy Cross dispute\">picket of Holy Cross</a>, a Catholic <a href=\"https://wikipedia.org/wiki/Primary_school\" title=\"Primary school\">primary school</a> for girls.", "links": [{"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}, {"title": "Ulster loyalism", "link": "https://wikipedia.org/wiki/Ulster_loyalism"}, {"title": "Holy Cross dispute", "link": "https://wikipedia.org/wiki/Holy_Cross_dispute"}, {"title": "Primary school", "link": "https://wikipedia.org/wiki/Primary_school"}]}, {"year": "2004", "text": "Beslan school siege results in over 330 fatalities, including 186 children.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Beslan_school_siege\" title=\"Beslan school siege\">Beslan school siege</a> results in over 330 fatalities, including 186 children.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beslan_school_siege\" title=\"Beslan school siege\">Beslan school siege</a> results in over 330 fatalities, including 186 children.", "links": [{"title": "Beslan school siege", "link": "https://wikipedia.org/wiki/Beslan_school_siege"}]}, {"year": "2010", "text": "After taking off from Dubai International Airport, UPS Airlines Flight 6 develops an in-flight fire in the cargo hold and crashes near Nad Al Sheba, killing both crew members on board.", "html": "2010 - After taking off from <a href=\"https://wikipedia.org/wiki/Dubai_International_Airport\" title=\"Dubai International Airport\">Dubai International Airport</a>, <a href=\"https://wikipedia.org/wiki/UPS_Airlines_Flight_6\" title=\"UPS Airlines Flight 6\">UPS Airlines Flight 6</a> develops an in-flight fire in the cargo hold and crashes near <a href=\"https://wikipedia.org/wiki/Nad_Al_Sheba\" title=\"Nad Al Sheba\">Nad <PERSON> She<PERSON></a>, killing both crew members on board.", "no_year_html": "After taking off from <a href=\"https://wikipedia.org/wiki/Dubai_International_Airport\" title=\"Dubai International Airport\">Dubai International Airport</a>, <a href=\"https://wikipedia.org/wiki/UPS_Airlines_Flight_6\" title=\"UPS Airlines Flight 6\">UPS Airlines Flight 6</a> develops an in-flight fire in the cargo hold and crashes near <a href=\"https://wikipedia.org/wiki/Nad_Al_Sheba\" title=\"Nad Al Sheba\">Nad <PERSON></a>, killing both crew members on board.", "links": [{"title": "Dubai International Airport", "link": "https://wikipedia.org/wiki/Dubai_International_Airport"}, {"title": "UPS Airlines Flight 6", "link": "https://wikipedia.org/wiki/UPS_Airlines_Flight_6"}, {"title": "Nad <PERSON>", "link": "https://wikipedia.org/wiki/Nad_<PERSON>_She<PERSON>"}]}, {"year": "2016", "text": "The U.S. and China, together responsible for 40% of the world's carbon emissions, both formally ratify the Paris global climate agreement.", "html": "2016 - The U.S. and <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, together responsible for <a href=\"https://wikipedia.org/wiki/List_of_countries_by_greenhouse_gas_emissions\" title=\"List of countries by greenhouse gas emissions\">40% of the world's carbon emissions</a>, both formally ratify the <a href=\"https://wikipedia.org/wiki/Paris_Agreement\" title=\"Paris Agreement\">Paris global climate agreement</a>.", "no_year_html": "The U.S. and <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, together responsible for <a href=\"https://wikipedia.org/wiki/List_of_countries_by_greenhouse_gas_emissions\" title=\"List of countries by greenhouse gas emissions\">40% of the world's carbon emissions</a>, both formally ratify the <a href=\"https://wikipedia.org/wiki/Paris_Agreement\" title=\"Paris Agreement\">Paris global climate agreement</a>.", "links": [{"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "List of countries by greenhouse gas emissions", "link": "https://wikipedia.org/wiki/List_of_countries_by_greenhouse_gas_emissions"}, {"title": "Paris Agreement", "link": "https://wikipedia.org/wiki/Paris_Agreement"}]}, {"year": "2017", "text": "North Korea conducts its sixth and most powerful nuclear test.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> conducts its sixth and most powerful <a href=\"https://wikipedia.org/wiki/2017_North_Korean_nuclear_test\" title=\"2017 North Korean nuclear test\">nuclear test</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> conducts its sixth and most powerful <a href=\"https://wikipedia.org/wiki/2017_North_Korean_nuclear_test\" title=\"2017 North Korean nuclear test\">nuclear test</a>.", "links": [{"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "2017 North Korean nuclear test", "link": "https://wikipedia.org/wiki/2017_North_Korean_nuclear_test"}]}], "Births": [{"year": "1034", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 1073)", "html": "1034 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Sanj%C5%8D\" title=\"Emperor Go-Sanjō\">Emperor Go-Sanjō</a> of Japan (d. 1073)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Sanj%C5%8D\" title=\"Emperor Go-Sanjō\">Emperor <PERSON>-Sanjō</a> of Japan (d. 1073)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-Sanj%C5%8D"}]}, {"year": "1568", "text": "<PERSON><PERSON>, Italian organist and composer (d. 1634)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian organist and composer (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian organist and composer (d. 1634)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1675", "text": "<PERSON>, American lawyer and jurist (d. 1751)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jurist)\" title=\"<PERSON> (jurist)\"><PERSON></a>, American lawyer and jurist (d. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jurist)\" title=\"<PERSON> (jurist)\"><PERSON></a>, American lawyer and jurist (d. 1751)", "links": [{"title": "<PERSON> (jurist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(jurist)"}]}, {"year": "1693", "text": "<PERSON>, English captain and politician (d. 1746)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and politician (d. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and politician (d. 1746)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1695", "text": "<PERSON>, Italian violin player and composer (d. 1764)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violin player and composer (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violin player and composer (d. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, French explorer, geographer, and mathematician, (d. 1779)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer, geographer, and mathematician, (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer, geographer, and mathematician, (d. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, Swiss biologist and zoologist (d. 1784)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss biologist and zoologist (d. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss biologist and zoologist (d. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, 1st Baron <PERSON>, Irish-English general and politician, 21st Governor General of Canada (d. 1808)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_Dorchester\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Irish-English general and politician, 21st <a href=\"https://wikipedia.org/wiki/List_of_Governors_General_of_Canada\" class=\"mw-redirect\" title=\"List of Governors General of Canada\">Governor General of Canada</a> (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_Do<PERSON>ester\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Irish-English general and politician, 21st <a href=\"https://wikipedia.org/wiki/List_of_Governors_General_of_Canada\" class=\"mw-redirect\" title=\"List of Governors General of Canada\">Governor General of Canada</a> (d. 1808)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "List of Governors General of Canada", "link": "https://wikipedia.org/wiki/List_of_Governors_General_of_Canada"}]}, {"year": "1781", "text": "<PERSON>, French general and politician (d. 1824)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>_Beauharnais\" title=\"<PERSON>\"><PERSON></a>, French general and politician (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>_<PERSON>harnais\" title=\"<PERSON>\"><PERSON></a>, French general and politician (d. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_de_Beauharnais"}]}, {"year": "1803", "text": "<PERSON><PERSON>, American educator (d. 1890)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Prudence_C<PERSON>all\" title=\"Prudence Crandall\"><PERSON><PERSON></a>, American educator (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prudence_C<PERSON>all\" title=\"Prudence Crandall\"><PERSON><PERSON></a>, American educator (d. 1890)", "links": [{"title": "P<PERSON>", "link": "https://wikipedia.org/wiki/Prudence_<PERSON><PERSON>all"}]}, {"year": "1810", "text": "<PERSON>, Irish-Canadian painter (d. 1871)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian painter (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian painter (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, American activist, founded the Oneida Community (d. 1886)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/Oneida_Community\" title=\"Oneida Community\">Oneida Community</a> (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/Oneida_Community\" title=\"Oneida Community\">Oneida Community</a> (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Oneida Community", "link": "https://wikipedia.org/wiki/Oneida_Community"}]}, {"year": "1814", "text": "<PERSON>, English mathematician and academic (d. 1897)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, American businessman and politician  (d. 1891)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, Danish composer (d. 1919)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Jacob <PERSON>\"><PERSON></a>, Danish composer (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Jacob <PERSON>\"><PERSON></a>, Danish composer (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, English cricketer (d. 1904)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, American novelist, short story writer and poet (d. 1909)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer and poet (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer and poet (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON> of Russia, Queen consort of the Hellenes (d. 1926)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a>, Queen consort of the Hellenes (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a>, Queen consort of the Hellenes (d. 1926)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_Con<PERSON>ino<PERSON>na_of_Russia"}]}, {"year": "1854", "text": "<PERSON>, American fencer (d. 1939)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, American fencer (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, American fencer (d. 1939)", "links": [{"title": "<PERSON> (fencer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)"}]}, {"year": "1856", "text": "<PERSON>, American architect and educator, designed the Carson, Pirie, Scott and Company Building (d. 1924)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and educator, designed the <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>,_<PERSON>_and_Company_Building\" class=\"mw-redirect\" title=\"Carson, Pirie, Scott and Company Building\">Carson, Pirie, Scott and Company Building</a> (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and educator, designed the <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>,_<PERSON>_and_Company_Building\" class=\"mw-redirect\" title=\"Carson, Pirie, Scott and Company Building\">Carson, Pirie, Scott and Company Building</a> (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Carson, Pirie, Scott and Company Building", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>,_<PERSON>_and_Company_Building"}]}, {"year": "1869", "text": "<PERSON>, Slovenian chemist and physician, Nobel Prize laureate (d. 1930)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian chemist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian chemist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1875", "text": "<PERSON>, Austrian-German engineer and businessman, founded Porsche (d. 1951)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Porsche\" title=\"Ferdinand Porsche\"><PERSON></a>, Austrian-German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Porsche\" title=\"Porsche\">Porsche</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Porsche\" title=\"Ferdinand Porsche\"><PERSON></a>, Austrian-German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Porsche\" title=\"Porsche\">Porsche</a> (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Porsche", "link": "https://wikipedia.org/wiki/Porsche"}]}, {"year": "1878", "text": "<PERSON>, English tennis player (d. 1960)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, English cricketer and boxer (d. 1930)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and boxer (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and boxer (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American trumpet player (d. 1973)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)\" title=\"<PERSON> (trumpeter)\"><PERSON></a>, American trumpet player (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)\" title=\"<PERSON> (trumpeter)\"><PERSON></a>, American trumpet player (d. 1973)", "links": [{"title": "<PERSON> (trumpeter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)"}]}, {"year": "1897", "text": "<PERSON>, American author and screenwriter (d. 1972)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Australian virologist and academic, Nobel Prize laureate (d. 1985)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Burn<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Burnet\"><PERSON></a>, Australian virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Burn<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Burnet\"><PERSON></a>, Australian virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>et"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1900", "text": "<PERSON>, English cricketer (d. 1961)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Finnish journalist, lawyer, and politician, 8th President of Finland (d. 1986)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish journalist, lawyer, and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish journalist, lawyer, and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ur<PERSON>_<PERSON>en"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1901", "text": "<PERSON>, Dutch violinist, pianist, and conductor (d. 1959)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch violinist, pianist, and conductor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch violinist, pianist, and conductor (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 1991)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1905", "text": "<PERSON>, New Zealand cricketer (d. 1972)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1905)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1905)\"><PERSON></a>, New Zealand cricketer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1905)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1905)\"><PERSON></a>, New Zealand cricketer (d. 1972)", "links": [{"title": "<PERSON> (cricketer, born 1905)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer,_born_1905)"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American anthropologist, philosopher, and author (d. 1977)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American anthropologist, philosopher, and author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American anthropologist, philosopher, and author (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Russian mathematician and academic (d. 1988)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actress, singer, socialite, and game show panelist (d. 2007)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, socialite, and game show panelist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, socialite, and game show panelist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Austrian Roman Catholic archbishop (d. 1984)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Franz_J%C3%A1chym\" title=\"<PERSON>\"><PERSON></a>, Austrian Roman Catholic archbishop (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franz_J%C3%A1chym\" title=\"<PERSON>\"><PERSON></a>, Austrian Roman Catholic archbishop (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_J%C3%A1chym"}]}, {"year": "1910", "text": "<PERSON>, French civil servant (d. 2007)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French civil servant (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French civil servant (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American cyclist and sergeant (d. 2000)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist and sergeant (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist and sergeant (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actor and producer (d. 1964)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American biologist and politician, 17th Governor of Washington (d. 1994)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist and politician, 17th <a href=\"https://wikipedia.org/wiki/Governor_of_Washington\" class=\"mw-redirect\" title=\"Governor of Washington\">Governor of Washington</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist and politician, 17th <a href=\"https://wikipedia.org/wiki/Governor_of_Washington\" class=\"mw-redirect\" title=\"Governor of Washington\">Governor of Washington</a> (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dixy_<PERSON>_<PERSON>"}, {"title": "Governor of Washington", "link": "https://wikipedia.org/wiki/Governor_of_Washington"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Norwegian organist and composer (d. 2014)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON> N<PERSON>\"><PERSON><PERSON></a>, Norwegian organist and composer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t\" title=\"K<PERSON> Nystedt\"><PERSON><PERSON></a>, Norwegian organist and composer (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Nystedt"}]}, {"year": "1915", "text": "<PERSON>, American singer-songwriter and pianist (d. 1988)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Memphis_Slim\" title=\"Memphis Slim\"><PERSON> Slim</a>, American singer-songwriter and pianist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Memphis_Slim\" title=\"Memphis Slim\"><PERSON> Slim</a>, American singer-songwriter and pianist (d. 1988)", "links": [{"title": "Memphis Slim", "link": "https://wikipedia.org/wiki/Memphis_Slim"}]}, {"year": "1916", "text": "<PERSON>, American baseball player, coach, and manager (d. 1999)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actress (d. 2010)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American soldier and photographer (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and photographer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and photographer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_Stern"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, French soldier and author (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Tereska_Torr%C3%A8s\" title=\"<PERSON><PERSON><PERSON> Torrès\"><PERSON><PERSON><PERSON></a>, French soldier and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tereska_Torr%C3%A8s\" title=\"<PERSON><PERSON><PERSON> Torrès\"><PERSON><PERSON><PERSON></a>, French soldier and author (d. 2012)", "links": [{"title": "Tereska Torrès", "link": "https://wikipedia.org/wiki/Tereska_Torr%C3%A8s"}]}, {"year": "1921", "text": "<PERSON>, English footballer (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, English footballer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Sr.\"><PERSON>.</a>, English footballer (d. 2003)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, English pianist, conductor, and musicologist (d. 1971)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Thurston_Dart\" title=\"Thurston Dart\">T<PERSON><PERSON></a>, English pianist, conductor, and musicologist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thurston_Dart\" title=\"Thurston Dart\">T<PERSON><PERSON></a>, English pianist, conductor, and musicologist (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thurston_<PERSON>t"}]}, {"year": "1921", "text": "<PERSON>, American journalist and author (d. 1966)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American businessman, founded Taco Bell (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bell\" title=\"Glen Bell\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Taco_Bell\" title=\"Taco Bell\">Taco Bell</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bell\" title=\"Glen Bell\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Taco_Bell\" title=\"Taco Bell\">Taco Bell</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glen_Bell"}, {"title": "Taco Bell", "link": "https://wikipedia.org/wiki/Taco_Bell"}]}, {"year": "1923", "text": "<PERSON>, Belizean chief librarian and educator (d. 2021)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belizean chief librarian and educator (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belizean chief librarian and educator (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American golfer (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2014)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American cartoonist (d. 2018)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cartoonist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cartoonist (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actress (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Swedish painter and sculptor (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish painter and sculptor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish painter and sculptor (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bengt_Lindstr%C3%B6m"}]}, {"year": "1925", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2007)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1926", "text": "<PERSON>, American author and academic (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Greek actress (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Indian Bengali actor, director, producer, singer, composer and playback singer (d. 1980)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Bengali actor, director, producer, singer, composer and playback singer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Bengali actor, director, producer, singer, composer and playback singer (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Luxembourg lawyer and politician, 8th Prime Minister of Luxembourg (d. 2007)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourg lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Luxembourg\" class=\"mw-redirect\" title=\"List of Prime Ministers of Luxembourg\">Prime Minister of Luxembourg</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourg lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Luxembourg\" class=\"mw-redirect\" title=\"List of Prime Ministers of Luxembourg\">Prime Minister of Luxembourg</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thorn"}, {"title": "List of Prime Ministers of Luxembourg", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Luxembourg"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American organized crime boss (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bulger\" title=\"<PERSON><PERSON> Bulger\"><PERSON><PERSON></a>, American organized crime boss (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bulger\" title=\"<PERSON><PERSON> Bulger\"><PERSON><PERSON></a>, American organized crime boss (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Swiss cyclist (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cyclist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cyclist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, New Zealand-Australian wrestler, trainer, and promoter (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian wrestler, trainer, and promoter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian wrestler, trainer, and promoter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian sculptor and painter", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, New Zealand author and poet (d. 2002)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author and poet (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wilder\"><PERSON></a>, New Zealand author and poet (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American serial killer known as the <PERSON> Strangler (d. 1973)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer known as the <a href=\"https://wikipedia.org/wiki/Boston_Strangler\" title=\"Boston Strangler\">Boston Strangler</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer known as the <a href=\"https://wikipedia.org/wiki/Boston_Strangler\" title=\"Boston Strangler\">Boston Strangler</a> (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Boston Strangler", "link": "https://wikipedia.org/wiki/Boston_Strangler"}]}, {"year": "1931", "text": "<PERSON>, American basketball player and coach", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Belgian academic and politician, 7th Minister-President of Wallonia (d. 2012)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian academic and politician, 7th <a href=\"https://wikipedia.org/wiki/Minister-President_of_Wallonia\" title=\"Minister-President of Wallonia\">Minister-President of Wallonia</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian academic and politician, 7th <a href=\"https://wikipedia.org/wiki/Minister-President_of_Wallonia\" title=\"Minister-President of Wallonia\">Minister-President of Wallonia</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister-President of Wallonia", "link": "https://wikipedia.org/wiki/Minister-President_of_Wallonia"}]}, {"year": "1932", "text": "<PERSON>, American actress and singer (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Guyanese cricketer (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>laser\" title=\"<PERSON><PERSON><PERSON> Glaser\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lase<PERSON>\" title=\"<PERSON><PERSON><PERSON> Glaser\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1976)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, German-Canadian motorcycle racer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Tunisian soldier and politician, 2nd President of Tunisia (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>ine_El_Abidine_<PERSON>_<PERSON>\" title=\"Zine El Abidine <PERSON>\"><PERSON><PERSON></a>, Tunisian soldier and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Tunisia\" title=\"President of Tunisia\">President of Tunisia</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Abid<PERSON>_<PERSON>\" title=\"Zine El Abidine <PERSON>\"><PERSON><PERSON></a>, Tunisian soldier and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Tunisia\" title=\"President of Tunisia\">President of Tunisia</a> (d. 2019)", "links": [{"title": "<PERSON>ine <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Tunisia", "link": "https://wikipedia.org/wiki/President_of_Tunisia"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Peruvian-American actress", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>lle<PERSON>\"><PERSON><PERSON></a>, Peruvian-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>llete"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, French microbiologist, community leader, writer, and lecturer (d. 2007)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French microbiologist, community leader, writer, and lecturer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French microbiologist, community leader, writer, and lecturer (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English historian and author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, English-Canadian playwright", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Canadian playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Canadian playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English architect, founded MJP Architects (d. 2014)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, founded <a href=\"https://wikipedia.org/wiki/MJP_Architects\" title=\"MJP Architects\">MJP Architects</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, founded <a href=\"https://wikipedia.org/wiki/MJP_Architects\" title=\"MJP Architects\">MJP Architects</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "MJP Architects", "link": "https://wikipedia.org/wiki/MJP_Architects"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Japanese chemist and academic, Nobel Prize laureate", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Ry%C5%8D<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ry%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ry%C5%8Dji_<PERSON><PERSON>ri"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1940", "text": "<PERSON>, English architect", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(architect)"}]}, {"year": "1940", "text": "<PERSON>, English actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Uruguayan journalist and author (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan journalist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan journalist and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, New Zealand rugby player and coach (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and coach (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Russian-American journalist and author (d. 1990)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American journalist and author (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American journalist and author (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Al_<PERSON>\" title=\"Al <PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_J<PERSON>ine\" title=\"Al <PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "Al Jardine", "link": "https://wikipedia.org/wiki/Al_Jardine"}]}, {"year": "1943", "text": "<PERSON>, American model and actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English cricketer and coach", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian footballer, lawyer, and politician, 39th Premier of Tasmania", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, lawyer, and politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, lawyer, and politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_Groom"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1945", "text": "<PERSON>, American bass player and songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English physicist and mathematician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, English physicist and mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, English physicist and mathematician", "links": [{"title": "<PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON>_(physicist)"}]}, {"year": "1945", "text": "<PERSON>, American Historian (d. 2023)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Historian (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Historian (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Norwegian minister and politician, 26th Prime Minister of Norway", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian minister and politician, 26th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway\" title=\"List of heads of government of Norway\">Prime Minister of Norway</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian minister and politician, 26th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway\" title=\"List of heads of government of Norway\">Prime Minister of Norway</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of government of Norway", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway"}]}, {"year": "1947", "text": "<PERSON>, Scottish educator and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Italian banker and economist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian banker and economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian banker and economist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, French footballer and coach (d. 2020)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer and coach (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English flute player and composer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Susan_<PERSON>\" title=\"Susan <PERSON>\"><PERSON></a>, English flute player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Susan_<PERSON>\" title=\"Susan <PERSON>\"><PERSON></a>, English flute player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Susan_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American drummer and singer-songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ukrainian astronomer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian astronomer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian astronomer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Greek lawyer and politician, Greek Minister of Justice", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>ot<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice,_Transparency_and_Human_Rights_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Justice, Transparency and Human Rights (Greece)\">Greek Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice,_Transparency_and_Human_Rights_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Justice, Transparency and Human Rights (Greece)\">Greek Minister of Justice</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ot<PERSON>_<PERSON>"}, {"title": "Ministry of Justice, Transparency and Human Rights (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Justice,_Transparency_and_Human_Rights_(Greece)"}]}, {"year": "1948", "text": "<PERSON>, Zambian lawyer and politician, 3rd President of Zambia (d. 2008)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Zambia", "link": "https://wikipedia.org/wiki/President_of_Zambia"}]}, {"year": "1949", "text": "<PERSON>, Argentine footballer, coach, and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_P%C3%A9kerman\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_P%C3%A9kerman\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_P%C3%A9kerman"}]}, {"year": "1949", "text": "Patriarch <PERSON> of Alexandria (d. 2004)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON>_<PERSON>_of_Alexandria\" title=\"Patriarch <PERSON> of Alexandria\">Patriarch <PERSON> of Alexandria</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON>_<PERSON>_of_Alexandria\" title=\"Patriarch <PERSON> of Alexandria\">Patriarch <PERSON> of Alexandria</a> (d. 2004)", "links": [{"title": "Patriarch <PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/Patriarch_<PERSON>_<PERSON>_of_Alexandria"}]}, {"year": "1950", "text": "<PERSON>, American rock singer-songwriter and bass player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, South African cricketer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Indian actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, French director, producer, and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Greek-Australian rugby league player and physician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Australian rugby league player and physician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Australian rugby league player and physician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Estonian triple jumper and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Jaa<PERSON>_Uudm%C3%A4e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian triple jumper and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaa<PERSON>_Uudm%C3%A4e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian triple jumper and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaak_Uudm%C3%A4e"}]}, {"year": "1955", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Indian actor and director (d. 2012)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor and director (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor and director (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pta"}]}, {"year": "1956", "text": "<PERSON>, Irish republican activist (d. 1996)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish republican activist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish republican activist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English director and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American businessman", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Garth An<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Garth An<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American basketball player and coach (d. 2024)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actor and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian yogi, mystic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Sad<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian yogi, mystic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian yogi, mystic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sad<PERSON><PERSON><PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Governor of the National Bank of Slovakia", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0<PERSON>ko\" title=\"<PERSON>\"><PERSON></a>, Governor of the <a href=\"https://wikipedia.org/wiki/National_Bank_of_Slovakia\" title=\"National Bank of Slovakia\">National Bank of Slovakia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0ramko\" title=\"<PERSON>\"><PERSON></a>, Governor of the <a href=\"https://wikipedia.org/wiki/National_Bank_of_Slovakia\" title=\"National Bank of Slovakia\">National Bank of Slovakia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ivan_%C5%A0ramko"}, {"title": "National Bank of Slovakia", "link": "https://wikipedia.org/wiki/National_Bank_of_Slovakia"}]}, {"year": "1960", "text": "<PERSON>, English accountant and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Australian author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Australian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Australian author", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1962", "text": "<PERSON>, English computer scientist and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American politician, 51st Mayor of Portland", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Oregon_politician)\" title=\"<PERSON> (Oregon politician)\"><PERSON></a>, American politician, 51st <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Portland,_Oregon\" class=\"mw-redirect\" title=\"List of mayors of Portland, Oregon\">Mayor of Portland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Oregon_politician)\" title=\"<PERSON> (Oregon politician)\"><PERSON></a>, American politician, 51st <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Portland,_Oregon\" class=\"mw-redirect\" title=\"List of mayors of Portland, Oregon\">Mayor of Portland</a>", "links": [{"title": "<PERSON> (Oregon politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Oregon_politician)"}, {"title": "List of mayors of Portland, Oregon", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Portland,_Oregon"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Emirati footballer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Emirati footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Emirati footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian journalist, essayist, and critic", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, essayist, and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, essayist, and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American-Dutch businessman and television host, co-founded mevio", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Dutch businessman and television host, co-founded <a href=\"https://wikipedia.org/wiki/Me<PERSON>\" title=\"<PERSON><PERSON>\">mevio</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Dutch businessman and television host, co-founded <a href=\"https://wikipedia.org/wiki/Me<PERSON>\" title=\"<PERSON><PERSON>\">mevio</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mevio"}]}, {"year": "1964", "text": "<PERSON>, American screenwriter and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Feresten"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Pakistani singer-songwriter, guitarist and na<PERSON> k<PERSON>an (d. 2016)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Junaid Jamshed\"><PERSON><PERSON></a>, Pakistani singer-songwriter, guitarist and <a href=\"https://wikipedia.org/wiki/Na%CA%BDat\" title=\"Naʽat\">naat khawan</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Junaid Jamshed\"><PERSON><PERSON></a>, Pakistani singer-songwriter, guitarist and <a href=\"https://wikipedia.org/wiki/Na%CA%BDat\" title=\"Naʽat\">naat khawan</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jun<PERSON>_<PERSON>shed"}, {"title": "Naʽat", "link": "https://wikipedia.org/wiki/Na%CA%BDat"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, British journalist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Australian actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sheen\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sheen\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sheen"}]}, {"year": "1966", "text": "<PERSON>, American painter and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Russian historian and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Vladimir_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian historian and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladimir_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian historian and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American basketball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder,_born_1967)\" title=\"<PERSON> (outfielder, born 1967)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder,_born_1967)\" title=\"<PERSON> (outfielder, born 1967)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (outfielder, born 1967)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder,_born_1967)"}]}, {"year": "1968", "text": "<PERSON>, Filipino educator and politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American comedian, actor, and talk show host", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Swedish golfer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Canadian bodybuilder, model, and wrestler (d. 2004)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian bodybuilder, model, and wrestler (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian bodybuilder, model, and wrestler (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English journalist and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American artist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American businessman (d. 2001)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Glick\"><PERSON></a>, American businessman (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1970", "text": "<PERSON>, English footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gareth_<PERSON>gate"}]}, {"year": "1971", "text": "<PERSON>, Indian-American author", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian swimmer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Portuguese-Spanish journalist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese-Spanish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese-Spanish journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chabeli_<PERSON>glesias"}]}, {"year": "1971", "text": "<PERSON>, Uruguayan footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian speed skater", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American wrestler and trainer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler and trainer", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Davis\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Davis\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Davis"}]}, {"year": "1972", "text": "<PERSON>, Czech ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Swiss ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress, producer, and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Hong Kong singer-songwriter and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, French ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, producer, and dancer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Redfoo\" title=\"Redfo<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, producer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Redfoo\" title=\"Redfo<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, producer, and dancer", "links": [{"title": "Redfoo", "link": "https://wikipedia.org/wiki/Redfoo"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Russian ice hockey player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>ry_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ry_<PERSON><PERSON>_<PERSON>\" title=\"Valery V<PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American football player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Angolan footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Angolan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Angolan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Swedish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Norwegian singer-songwriter (d. 2004)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian singer-songwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian singer-songwriter (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ken"}]}, {"year": "1978", "text": "<PERSON>, English footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1978)\" title=\"<PERSON> (footballer, born 1978)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1978)\" title=\"<PERSON> (footballer, born 1978)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1978)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1978)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ltz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s%C3%ADval\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s%C3%ADval\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Rozs%C3%ADval"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/J%C3%BAlio_C%C3%A9sar_(football_goalkeeper,_born_1979)\" title=\"<PERSON><PERSON><PERSON> (football goalkeeper, born 1979)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BAlio_C%C3%A9<PERSON>_(football_goalkeeper,_born_1979)\" title=\"<PERSON><PERSON><PERSON> (football goalkeeper, born 1979)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (football goalkeeper, born 1979)", "link": "https://wikipedia.org/wiki/J%C3%BAlio_C%C3%A9sar_(football_goalkeeper,_born_1979)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Bosnian-American guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Devi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian-American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Devi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian-American guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom<PERSON>_Mili%C4%8Devi%C4%87"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper and actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON><PERSON><PERSON> (rapper)\">B.G.</a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B.G<PERSON>_(rapper)\" title=\"<PERSON><PERSON><PERSON><PERSON> (rapper)\">B.G<PERSON></a>, American rapper and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(rapper)"}]}, {"year": "1980", "text": "<PERSON>, Argentine footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Dutch footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1980", "text": "<PERSON>, Canadian singer-songwriter, bass player, and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, English television and radio presenter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English television and radio presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English television and radio presenter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian skier (d. 2012)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American singer-songwriter, pianist, and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Japanese singer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>nnow\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>nnow\" title=\"Tia<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>nnow"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Brazilian race car driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunt\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nicky Hunt\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Lithuanian basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Val<PERSON>_Vasylius\" title=\"Val<PERSON> Vasylius\"><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vasylius\" title=\"Valdas Vasylius\"><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>das_Vas<PERSON>ius"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON> <PERSON><PERSON>, Filipino-American wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Filipino-American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Filipino-American wrestler", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American snowboarder, skateboarder, and guitarist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American snowboarder, skateboarder, and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American snowboarder, skateboarder, and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Jamaican singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Jamaican singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Jamaican singer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1987", "text": "<PERSON>, Canadian wrestler", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Malian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Modibo_Ma%C3%AFga\" title=\"Modibo Maïga\"><PERSON><PERSON><PERSON></a>, Malian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Modibo_Ma%C3%AFga\" title=\"Modibo Maïga\"><PERSON><PERSON><PERSON></a>, Malian footballer", "links": [{"title": "Mo<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Modibo_Ma%C3%AFga"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, English cricketer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Ghanaian-German footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ghanaian-German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ghanaian-German footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_Boateng"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Iranian director and producer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian director and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Indian Cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian Cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian Cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>\" title=\"August <PERSON>sina\">August <PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>\" title=\"August <PERSON><PERSON>a\">August <PERSON></a>, American singer-songwriter", "links": [{"title": "August <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>a"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Indian wrestler", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, South Korean singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ung\" title=\"<PERSON>ung\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ung\" title=\"<PERSON>ung\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ung"}]}, {"year": "1993", "text": "<PERSON>, Austrian tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>em"}]}, {"year": "1993", "text": "<PERSON>, South Korean singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ong\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ong"}]}, {"year": "1994", "text": "<PERSON>, New Zealand rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English-Irish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Re<PERSON>\" title=\"Glen Re<PERSON>\"><PERSON></a>, English-Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Glen Rea\"><PERSON></a>, English-Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glen_<PERSON>a"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jack\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jack\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, German footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Niklas_S%C3%BCle\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niklas_S%C3%BCle\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niklas_S%C3%BCle"}]}, {"year": "1996", "text": "<PERSON>, South Korean idol and actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean <a href=\"https://wikipedia.org/wiki/Korean_pop_idol\" class=\"mw-redirect\" title=\"Korean pop idol\">idol</a> and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean <a href=\"https://wikipedia.org/wiki/Korean_pop_idol\" class=\"mw-redirect\" title=\"Korean pop idol\">idol</a> and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}, {"title": "Korean pop idol", "link": "https://wikipedia.org/wiki/Korean_pop_idol"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Burkinabé footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burkinabé footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burkinabé footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adama_Barro"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American swimmer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Slovenian athlete", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian athlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Swedish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Ho<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Ho<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Burmese footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Nanda_Kyaw\" title=\"Nanda Kyaw\"><PERSON><PERSON></a>, Burmese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nan<PERSON>_Kyaw\" title=\"Nanda Kyaw\"><PERSON><PERSON></a>, Burmese footballer", "links": [{"title": "Nanda Kyaw", "link": "https://wikipedia.org/wiki/Nanda_Kyaw"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, French cyclist", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American cyclist", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Powless\" title=\"<PERSON>son Powless\"><PERSON><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Powless\" title=\"<PERSON>son Powless\"><PERSON><PERSON></a>, American cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Powless"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Irish tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Osgar_O%27Hoisin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Osgar_O%27Hoisin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Osgar_O%27Hoisin"}]}, {"year": "1996", "text": "<PERSON>, Chinese handball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese handball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1996", "text": "<PERSON><PERSON>, French footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Irish cricketer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Irish cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Irish cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Norwegian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Sulayman_Bojang\" title=\"Sulayman Bojang\"><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sulayman_Bojang\" title=\"Sulayman Bojang\"><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "Sulayman Bojang", "link": "https://wikipedia.org/wiki/Sulayman_Bojang"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, West Indian cricketer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, West Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, West Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Macedonian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C4%87_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Macedonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Macedonian footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Petar_Krsti%C4%87_(footballer)"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Georgian rhythmic gymnast", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ha<PERSON>\"><PERSON><PERSON></a>, Georgian rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pazhava\"><PERSON><PERSON></a>, Georgian rhythmic gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sal<PERSON>_Pazhava"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Ghanaian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Nigerian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Belgian politician", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American model and actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ber"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Pakistani-Canadian actress", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-Canadian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>razer\" title=\"Jack <PERSON> Grazer\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>razer\" title=\"Jack <PERSON> Grazer\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian-American chess player", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Tanitoluwa_Adewumi\" title=\"Tanitoluwa Adewumi\">Tanitolu<PERSON>ewumi</a>, Nigerian-American chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tanitoluwa_<PERSON>umi\" title=\"Tanitoluwa Adewumi\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian-American chess player", "links": [{"title": "Tanitoluwa Adewumi", "link": "https://wikipedia.org/wiki/Tanitoluwa_Adewumi"}]}], "Deaths": [{"year": "264", "text": "<PERSON>, Chinese emperor (b. 235)", "html": "264 - <a href=\"https://wikipedia.org/wiki/Sun_Xiu\" title=\"<PERSON> Xi<PERSON>\"><PERSON></a>, Chinese emperor (b. 235)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Xiu\" title=\"<PERSON> Xi<PERSON>\"><PERSON></a>, Chinese emperor (b. 235)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sun_Xiu"}]}, {"year": "618", "text": "<PERSON><PERSON>, emperor of Qin", "html": "618 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Qin", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Qin", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "863", "text": "<PERSON><PERSON>, Arab emir", "html": "863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_al-<PERSON>\" title=\"Umar al-A<PERSON>ta\"><PERSON><PERSON></a>, Arab emir", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_al-<PERSON>\" title=\"Umar al-Aqta\"><PERSON><PERSON></a>, Arab emir", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "931", "text": "<PERSON><PERSON>, emperor of Japan (b. 867)", "html": "931 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Japan (b. 867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Japan (b. 867)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1120", "text": "<PERSON> (The Blessed <PERSON>), founder of the Knights Hospitaller (b. c. 1040)", "html": "1120 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gerard\" title=\"Blessed <PERSON>\"><PERSON></a> (The Blessed <PERSON>), founder of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a> (b. c. 1040)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gerard\" title=\"Blessed <PERSON>\"><PERSON></a> (The Blessed <PERSON>), founder of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a> (b. c. 1040)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Knights Hospitaller", "link": "https://wikipedia.org/wiki/Knights_Hospitaller"}]}, {"year": "1189", "text": "<PERSON> of Orléans, French Jewish scholar", "html": "1189 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Orl%C3%A9ans\" title=\"<PERSON> of Orléans\"><PERSON> Orléans</a>, French Jewish scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Or<PERSON>%C3%A9ans\" title=\"<PERSON> of Orléans\"><PERSON> of Orléans</a>, French Jewish scholar", "links": [{"title": "<PERSON> of Orléans", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Or<PERSON>%C3%A9ans"}]}, {"year": "1301", "text": "<PERSON>, Lord of Verona", "html": "1301 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lord of Verona", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lord of Verona", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1313", "text": "<PERSON> of Bohemia (b. 1290)", "html": "1313 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bohemia_(1290%E2%80%931313)\" title=\"<PERSON> of Bohemia (1290-1313)\"><PERSON> of Bohemia</a> (b. 1290)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bohemia_(1290%E2%80%931313)\" title=\"<PERSON> of Bohemia (1290-1313)\"><PERSON> of Bohemia</a> (b. 1290)", "links": [{"title": "<PERSON> of Bohemia (1290-1313)", "link": "https://wikipedia.org/wiki/<PERSON>_of_Bohemia_(1290%E2%80%931313)"}]}, {"year": "1354", "text": "<PERSON><PERSON><PERSON>, Serbian patriarch and saint", "html": "1354 - <a href=\"https://wikipedia.org/wiki/Joan<PERSON>je_II\" title=\"Joanikije II\"><PERSON><PERSON><PERSON> <PERSON></a>, Serbian patriarch and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joan<PERSON><PERSON>_II\" title=\"Joanikije II\"><PERSON><PERSON><PERSON> <PERSON></a>, Serbian patriarch and saint", "links": [{"title": "Joanikije II", "link": "https://wikipedia.org/wiki/Joanikije_II"}]}, {"year": "1400", "text": "<PERSON>, 1st Duke of Exeter (b. c. 1352)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Exeter\" title=\"<PERSON>, 1st Duke of Exeter\"><PERSON>, 1st Duke of Exeter</a> (b. c. 1352)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Exeter\" title=\"<PERSON>, 1st Duke of Exeter\"><PERSON>, 1st Duke of Exeter</a> (b. c. 1352)", "links": [{"title": "<PERSON>, 1st Duke of Exeter", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Exeter"}]}, {"year": "1402", "text": "<PERSON><PERSON>, Italian son of <PERSON><PERSON><PERSON> (b. 1351)", "html": "1402 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_V<PERSON>nti\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian son of <a href=\"https://wikipedia.org/wiki/<PERSON>az<PERSON>_II_Visconti\" title=\"Galeazzo II Visconti\">Galeazzo II Visconti</a> (b. 1351)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>nti\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian son of <a href=\"https://wikipedia.org/wiki/<PERSON>azzo_II_Visconti\" title=\"Galeazzo II Visconti\">Galeazzo II Visconti</a> (b. 1351)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Galeazzo II Visconti", "link": "https://wikipedia.org/wiki/<PERSON>azzo_<PERSON>_<PERSON>nti"}]}, {"year": "1420", "text": "<PERSON>, Duke of Albany (b. 1340)", "html": "1420 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Albany\" title=\"<PERSON>, Duke of Albany\"><PERSON>, Duke of Albany</a> (b. 1340)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Albany\" title=\"<PERSON>, Duke of Albany\"><PERSON>, Duke of Albany</a> (b. 1340)", "links": [{"title": "<PERSON>, Duke of Albany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Albany"}]}, {"year": "1467", "text": "<PERSON> of Portugal, Holy Roman Empress (b. 1434)", "html": "1467 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Holy_Roman_Empress\" title=\"<PERSON> of Portugal, Holy Roman Empress\"><PERSON> of Portugal, Holy Roman Empress</a> (b. 1434)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Holy_Roman_Empress\" title=\"<PERSON> of Portugal, Holy Roman Empress\"><PERSON> of Portugal, Holy Roman Empress</a> (b. 1434)", "links": [{"title": "<PERSON> of Portugal, Holy Roman Empress", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Portugal,_Holy_Roman_Empress"}]}, {"year": "1592", "text": "<PERSON>, English author and playwright (b. 1558)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)\" title=\"<PERSON> (dramatist)\"><PERSON></a>, English author and playwright (b. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)\" title=\"<PERSON> (dramatist)\"><PERSON></a>, English author and playwright (b. 1558)", "links": [{"title": "<PERSON> (dramatist)", "link": "https://wikipedia.org/wiki/<PERSON>_(dramatist)"}]}, {"year": "1609", "text": "<PERSON>, Belgian diplomat (b. 1540)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian diplomat (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian diplomat (b. 1540)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1634", "text": "<PERSON>, English lawyer, judge, and politician, Lord Chief Justice of England and Wales (b. 1552)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales\" title=\"Lord Chief Justice of England and Wales\">Lord Chief Justice of England and Wales</a> (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales\" title=\"Lord Chief Justice of England and Wales\">Lord Chief Justice of England and Wales</a> (b. 1552)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Chief Justice of England and Wales", "link": "https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales"}]}, {"year": "1653", "text": "<PERSON><PERSON><PERSON>, French scholar and author (b. 1588)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French scholar and author (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French scholar and author (b. 1588)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1658", "text": "<PERSON>, English general and politician (b. 1599)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (b. 1599)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON>, Earl of Galway, French general and diplomat (b. 1648)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Galway\" title=\"<PERSON>, Earl of Galway\"><PERSON>, Earl of Galway</a>, French general and diplomat (b. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Earl_of_Galway\" title=\"<PERSON>, Earl of Galway\"><PERSON>, Earl of Galway</a>, French general and diplomat (b. 1648)", "links": [{"title": "<PERSON>, Earl of Galway", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Earl_of_Galway"}]}, {"year": "1729", "text": "<PERSON>, French historian and scholar (b. 1646)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and scholar (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and scholar (b. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, Scottish historian and author (b. 1686)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and author (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and author (b. 1686)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, American merchant and politician (b. 1722)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)\" title=\"<PERSON> (Continental Congress)\"><PERSON></a>, American merchant and politician (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)\" title=\"<PERSON> (Continental Congress)\"><PERSON></a>, American merchant and politician (b. 1722)", "links": [{"title": "<PERSON> (Continental Congress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)"}]}, {"year": "1857", "text": "<PERSON>, Canadian-American businessman (b. 1784)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman (b. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Russian painter (b. 1830)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, French historian and politician, 2nd President of France (b. 1797)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1797)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1883", "text": "<PERSON>, Russian author and playwright (b. 1818)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and playwright (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and playwright (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American lawyer and politician (b. 1812)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Scottish-Australian engineer, journalist, and politician (b. 1816)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, Scottish-Australian engineer, journalist, and politician (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, Scottish-Australian engineer, journalist, and politician (b. 1816)", "links": [{"title": "<PERSON> (engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)"}]}, {"year": "1901", "text": "<PERSON>, English classical scholar (b. 1843)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English classical scholar (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English classical scholar (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian author and poet (b. 1846)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Mih%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author and poet (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mih%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author and poet (b. 1846)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mih%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, French composer and educator (b. 1865)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Alb%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French composer and educator (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alb%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French composer and educator (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alb%C3%A9ric_Magnard"}]}, {"year": "1929", "text": "<PERSON>, 1st Viscount <PERSON>, English jurist and politician (b. 1840)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English jurist and politician (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English jurist and politician (b. 1840)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Armenian-Russian puppeteer and director (b. 1876)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian-Russian puppeteer and director (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian-Russian puppeteer and director (b. 1876)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>eff"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Serbian Orthodox hegumen and painter (b. 1875)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dilovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Serbian_Orthodox_Church\" title=\"Serbian Orthodox Church\">Serbian Orthodox</a> hegumen and painter (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dilovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Serbian_Orthodox_Church\" title=\"Serbian Orthodox Church\">Serbian Orthodox</a> hegumen and painter (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra<PERSON><PERSON>_Mom%C4%8Dilovi%C4%87"}, {"title": "Serbian Orthodox Church", "link": "https://wikipedia.org/wiki/Serbian_Orthodox_Church"}]}, {"year": "1942", "text": "<PERSON>, Canadian-American author and illustrator (b. 1892)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Canadian-American author and illustrator (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Canadian-American author and illustrator (b. 1892)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, French painter (b. 1864)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1864)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9raphine_Louis"}]}, {"year": "1944", "text": "<PERSON>, Irish physician, founded the St. John Ambulance Brigade of Ireland (b. 1869)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physician, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>._John_Ambulance_Brigade_of_Ireland\" class=\"mw-redirect\" title=\"St. John Ambulance Brigade of Ireland\">St. John Ambulance Brigade of Ireland</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physician, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON>_Ambulance_Brigade_of_Ireland\" class=\"mw-redirect\" title=\"St. John Ambulance Brigade of Ireland\">St. John Ambulance Brigade of Ireland</a> (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "St. John Ambulance Brigade of Ireland", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_John_Ambulance_Brigade_of_Ireland"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Czech academic and politician, 2nd President of Czechoslovakia (b. 1884)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech academic and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Czechoslovakia\" class=\"mw-redirect\" title=\"List of Presidents of Czechoslovakia\">President of Czechoslovakia</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech academic and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Czechoslovakia\" class=\"mw-redirect\" title=\"List of Presidents of Czechoslovakia\">President of Czechoslovakia</a> (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edvard_Bene%C5%A1"}, {"title": "List of Presidents of Czechoslovakia", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Czechoslovakia"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Greek actress (b. 1887)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actress (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actress (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mari<PERSON>_<PERSON>ouli"}]}, {"year": "1961", "text": "<PERSON>, American businessman (b. 1897)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman (b. 1897)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(businessman)"}]}, {"year": "1962", "text": "<PERSON><PERSON> <PERSON><PERSON>, American poet and playwright (b. 1894)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet and playwright (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet and playwright (b. 1894)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Irish poet and playwright (b. 1907)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and playwright (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and playwright (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American golfer and banker (b. 1893)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and banker (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and banker (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American cricketer and soccer player (b. 1871)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cricketer and soccer player (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cricketer and soccer player (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Bulgarian actor, director, and screenwriter (b. 1891)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>dov\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian actor, director, and screenwriter (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian actor, director, and screenwriter (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>dov"}]}, {"year": "1970", "text": "<PERSON>, American football player and coach (b. 1913)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1943)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1943)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1974", "text": "<PERSON>, American composer and theorist (b. 1901)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and theorist (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and theorist (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Maltese artist (b. 1885)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese artist (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese artist (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress (b. 1910)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neil\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neil\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barbara_O%27Neil"}]}, {"year": "1980", "text": "<PERSON>, Romanian-American actor, producer, and screenwriter (b. 1904)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American actor, producer, and screenwriter (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American actor, producer, and screenwriter (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English soldier and author (b. 1898)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and author (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and author (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American songwriter (b. 1909)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American composer and educator (b. 1926)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Turkish civil servant and politician, 14th Prime Minister of Turkey (b. 1906)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish civil servant and politician, 14th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish civil servant and politician, 14th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>en"}, {"title": "List of Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Italian footballer (b. 1953)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Gaetano_Scirea\" title=\"Gaetano <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gaetano_<PERSON>\" title=\"Gaetano <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gaetano_Scirea"}]}, {"year": "1991", "text": "<PERSON>, Italian-American director, producer, and screenwriter (b. 1897)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American director, producer, and screenwriter (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American director, producer, and screenwriter (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English businessman (b. 1904)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entrepreneur)\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, English businessman (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entrepreneur)\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, English businessman (b. 1904)", "links": [{"title": "<PERSON> (entrepreneur)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entrepreneur)"}]}, {"year": "1994", "text": "<PERSON>, Jr., American screenwriter and producer (b. 1918)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American screenwriter and producer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American screenwriter and producer (b. 1918)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1994", "text": "<PERSON>, English footballer and manager (b. 1924)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1924)\" title=\"<PERSON> (footballer, born 1924)\"><PERSON></a>, English footballer and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1924)\" title=\"<PERSON> (footballer, born 1924)\"><PERSON></a>, English footballer and manager (b. 1924)", "links": [{"title": "<PERSON> (footballer, born 1924)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1924)"}]}, {"year": "1995", "text": "<PERSON>, English painter (b. 1904)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian painter (b. 1910)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American auctioneer and author (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American auctioneer and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American auctioneer and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor, producer, and screenwriter (b. 1914)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American film critic and author (b. 1919)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film critic and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film critic and author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian climatologist and academic (b. 1919)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian climatologist and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian climatologist and academic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American businessman, philanthropist, and author (b. 1902)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman, philanthropist, and author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman, philanthropist, and author (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American soldier and poet (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and poet (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and poet (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, German businessman (b. 1914)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English biologist and author (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/R._S._R<PERSON>_Fitter\" title=\"R. S. R. Fitter\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English biologist and author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R._S._R<PERSON>_Fitter\" title=\"R. S. R. Fitter\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>tter</a>, English biologist and author (b. 1913)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>tter"}]}, {"year": "2005", "text": "<PERSON>, American lawyer and jurist, 16th Chief Justice of the United States (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, 16th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, 16th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "2007", "text": "<PERSON>, American keyboard player and guitarist (b. 1973)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and guitarist (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and guitarist (b. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, New Zealand trade union leader and activist (b. 1939)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(M%C4%81ori_activist)\" title=\"<PERSON><PERSON> (Māori activist)\"><PERSON><PERSON></a>, New Zealand trade union leader and activist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(M%C4%81ori_activist)\" title=\"<PERSON><PERSON> (Māori activist)\"><PERSON><PERSON></a>, New Zealand trade union leader and activist (b. 1939)", "links": [{"title": "<PERSON><PERSON> (Māori activist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(M%C4%81ori_activist)"}]}, {"year": "2007", "text": "<PERSON>, English runner (b. 1964)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American aviator (b. 1944)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aviator (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aviator (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American colonel and pilot (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American saxophonist (b. 1943)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American comedian, actor, and screenwriter (b. 1950)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and screenwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and screenwriter (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Colombian drug lord (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian drug lord (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian drug lord (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American race car driver and pilot (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and pilot (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and pilot (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American actor (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Russian-German soldier and pilot (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-German soldier and pilot (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-German soldier and pilot (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Korean religious leader and businessman, founded the Unification Church (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Sun_Myung_Moon\" title=\"Sun Myung Moon\"><PERSON> Myung <PERSON></a>, Korean religious leader and businessman, founded the <a href=\"https://wikipedia.org/wiki/Unification_Church\" title=\"Unification Church\">Unification Church</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sun_Myung_Moon\" title=\"Sun Myung Moon\"><PERSON> Myung Moon</a>, Korean religious leader and businessman, founded the <a href=\"https://wikipedia.org/wiki/Unification_Church\" title=\"Unification Church\">Unification Church</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Unification Church", "link": "https://wikipedia.org/wiki/Unification_Church"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(congressman)\" class=\"mw-redirect\" title=\"<PERSON> (congressman)\"><PERSON></a>, American lawyer and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(congressman)\" class=\"mw-redirect\" title=\"<PERSON> (congressman)\"><PERSON></a>, American lawyer and politician (b. 1939)", "links": [{"title": "<PERSON> (congressman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(congressman)"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and judge (b. 1914)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Mexican-American journalist (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ruz\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican-American journalist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican-American journalist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>_<PERSON>cruz"}]}, {"year": "2013", "text": "<PERSON>, Spanish director and screenwriter (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%B3n_<PERSON>z\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%B3n_La<PERSON>z\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%B3n_Larraz"}]}, {"year": "2013", "text": "<PERSON>, American author and scholar (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and scholar (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and scholar (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American basketball player (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Hong Kong-Australian photographer (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-Australian photographer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-Australian photographer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Finnish singer-songwriter and pianist (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish singer-songwriter and pianist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish singer-songwriter and pianist (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian soldier and politician, 14th Foreign Secretary of India (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/A._<PERSON>._Venkateswaran\" title=\"A<PERSON> <PERSON><PERSON> Venkateswaran\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian soldier and politician, 14th <a href=\"https://wikipedia.org/wiki/Foreign_Secretary_(India)\" title=\"Foreign Secretary (India)\">Foreign Secretary of India</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A._<PERSON><PERSON>_Venkateswaran\" title=\"<PERSON><PERSON> <PERSON><PERSON> V<PERSON>kat<PERSON>waran\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian soldier and politician, 14th <a href=\"https://wikipedia.org/wiki/Foreign_Secretary_(India)\" title=\"Foreign Secretary (India)\">Foreign Secretary of India</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A._<PERSON><PERSON>_Venkateswaran"}, {"title": "Foreign Secretary (India)", "link": "https://wikipedia.org/wiki/Foreign_Secretary_(India)"}]}, {"year": "2015", "text": "<PERSON>, English rower and businessman (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower and businessman (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower and businessman (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English actress and comedian (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and comedian (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and comedian (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American businessman and philanthropist (b. 1971)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lay\"><PERSON></a>, American businessman and philanthropist (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lay\"><PERSON></a>, American businessman and philanthropist (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Chinese general and politician (b. 1914)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, Chinese general and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, Chinese general and politician (b. 1914)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "2015", "text": "<PERSON>, world record holder for shortest man (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, world record holder for shortest man (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, world record holder for shortest man (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American musician, songwriter, and record producer  (b. 1950)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, songwriter, and record producer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, songwriter, and record producer (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American poet (b. 1927)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, 21st <PERSON>, Scottish peer (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_21st_<PERSON>_<PERSON>\" title=\"<PERSON>, 21st <PERSON>\"><PERSON>, 21st <PERSON></a>, Scottish peer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_21st_<PERSON>_<PERSON>\" title=\"<PERSON>, 21st <PERSON> Salt<PERSON>\"><PERSON>, 21st <PERSON></a>, Scottish peer (b. 1930)", "links": [{"title": "<PERSON>, 21st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_21st_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American baseball player and coach (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American football player (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}