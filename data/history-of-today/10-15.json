{"date": "October 15", "url": "https://wikipedia.org/wiki/October_15", "data": {"Events": [{"year": "1066", "text": "Following the death of <PERSON> at the Battle of Hastings, <PERSON> the <PERSON> is proclaimed King of England by the Witan; he is never crowned, and concedes power to <PERSON> the Conqueror two months later.", "html": "1066 - Following the death of <PERSON> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Hastings\" title=\"Battle of Hastings\">Battle of Hastings</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_the_%C3%86theling\" class=\"mw-redirect\" title=\"<PERSON> the Ætheling\"><PERSON> the Ætheling</a> is proclaimed King of England by the <a href=\"https://wikipedia.org/wiki/Witan\" title=\"Witan\">Witan</a>; he is never crowned, and concedes power to <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Conqueror\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a> two months later.", "no_year_html": "Following the death of <PERSON> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Hastings\" title=\"Battle of Hastings\">Battle of Hastings</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_the_%C3%86theling\" class=\"mw-redirect\" title=\"<PERSON> the Ætheling\"><PERSON> the Ætheling</a> is proclaimed King of England by the <a href=\"https://wikipedia.org/wiki/Witan\" title=\"Witan\">Witan</a>; he is never crowned, and concedes power to <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Conqueror\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a> two months later.", "links": [{"title": "Battle of Hastings", "link": "https://wikipedia.org/wiki/Battle_of_Hastings"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_the_%C3%86theling"}, {"title": "Witan", "link": "https://wikipedia.org/wiki/Witan"}, {"title": "<PERSON> the Conqueror", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1211", "text": "Battle of the Rhyndacus: The Latin emperor <PERSON> of Flanders defeats the Nicaean emperor <PERSON>.", "html": "1211 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Rhyndacus_(1211)\" title=\"Battle of the Rhyndacus (1211)\">Battle of the Rhyndacus</a>: The <a href=\"https://wikipedia.org/wiki/Latin_emperor\" class=\"mw-redirect\" title=\"Latin emperor\">Latin emperor</a> <a href=\"https://wikipedia.org/wiki/Henry_of_Flanders\" title=\"Henry of Flanders\"><PERSON> of Flanders</a> defeats the <a href=\"https://wikipedia.org/wiki/Nicaean_emperor\" class=\"mw-redirect\" title=\"Nicaean emperor\">Nicaean emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Rhyndacus_(1211)\" title=\"Battle of the Rhyndacus (1211)\">Battle of the Rhyndacus</a>: The <a href=\"https://wikipedia.org/wiki/Latin_emperor\" class=\"mw-redirect\" title=\"Latin emperor\">Latin emperor</a> <a href=\"https://wikipedia.org/wiki/Henry_of_Flanders\" title=\"Henry of Flanders\"><PERSON> of Flanders</a> defeats the <a href=\"https://wikipedia.org/wiki/Nicaean_emperor\" class=\"mw-redirect\" title=\"Nicaean emperor\">Nicaean emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Battle of the Rhyndacus (1211)", "link": "https://wikipedia.org/wiki/Battle_of_the_Rhyndacus_(1211)"}, {"title": "Latin emperor", "link": "https://wikipedia.org/wiki/Latin_emperor"}, {"title": "<PERSON> of Flanders", "link": "https://wikipedia.org/wiki/Henry_of_Flanders"}, {"title": "Nicaean emperor", "link": "https://wikipedia.org/wiki/Nicaean_emperor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1529", "text": "The Siege of Vienna ends when Austria routs the invading Ottoman forces, ending its European expansion.", "html": "1529 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Vienna_(1529)\" title=\"Siege of Vienna (1529)\">Siege of Vienna</a> ends when Austria routs the invading Ottoman forces, ending its European expansion.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Vienna_(1529)\" title=\"Siege of Vienna (1529)\">Siege of Vienna</a> ends when Austria routs the invading Ottoman forces, ending its European expansion.", "links": [{"title": "Siege of Vienna (1529)", "link": "https://wikipedia.org/wiki/Siege_of_Vienna_(1529)"}]}, {"year": "1582", "text": "Adoption of the Gregorian calendar begins, eventually leading to near-universal adoption.", "html": "1582 - <a href=\"https://wikipedia.org/wiki/Adoption_of_the_Gregorian_calendar\" title=\"Adoption of the Gregorian calendar\">Adoption of the Gregorian calendar</a> begins, eventually leading to near-universal adoption.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adoption_of_the_Gregorian_calendar\" title=\"Adoption of the Gregorian calendar\">Adoption of the Gregorian calendar</a> begins, eventually leading to near-universal adoption.", "links": [{"title": "Adoption of the Gregorian calendar", "link": "https://wikipedia.org/wiki/Adoption_of_the_Gregorian_calendar"}]}, {"year": "1651", "text": "Qing forces capture the island of Zhoushan. <PERSON>, Prince of Lu, resident of the island and regent of the Southern Ming, flees to Kinmen.", "html": "1651 - <a href=\"https://wikipedia.org/wiki/Qing_Dynasty\" class=\"mw-redirect\" title=\"Qing Dynasty\">Qing</a> forces capture the <a href=\"https://wikipedia.org/wiki/Zhoushan_Island\" title=\"Zhoushan Island\">island of Zhoushan</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>, Prince of Lu</a>, resident of the island and <a href=\"https://wikipedia.org/wiki/List_of_emperors_of_the_Ming_dynasty#Southern_Ming\" title=\"List of emperors of the Ming dynasty\">regent</a> of the <a href=\"https://wikipedia.org/wiki/Southern_Ming\" title=\"Southern Ming\">Southern Ming</a>, flees to <a href=\"https://wikipedia.org/wiki/Kinmen\" title=\"Kinmen\">Kinmen</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qing_Dynasty\" class=\"mw-redirect\" title=\"Qing Dynasty\">Qing</a> forces capture the <a href=\"https://wikipedia.org/wiki/Zhoushan_Island\" title=\"Zhoushan Island\">island of Zhoushan</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>, Prince of Lu</a>, resident of the island and <a href=\"https://wikipedia.org/wiki/List_of_emperors_of_the_Ming_dynasty#Southern_Ming\" title=\"List of emperors of the Ming dynasty\">regent</a> of the <a href=\"https://wikipedia.org/wiki/Southern_Ming\" title=\"Southern Ming\">Southern Ming</a>, flees to <a href=\"https://wikipedia.org/wiki/Kinmen\" title=\"Kinmen\">Kinmen</a>.", "links": [{"title": "Qing Dynasty", "link": "https://wikipedia.org/wiki/Qing_Dynasty"}, {"title": "Zhoushan Island", "link": "https://wikipedia.org/wiki/Zhoushan_Island"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of emperors of the Ming dynasty", "link": "https://wikipedia.org/wiki/List_of_emperors_of_the_Ming_dynasty#Southern_Ming"}, {"title": "Southern Ming", "link": "https://wikipedia.org/wiki/Southern_Ming"}, {"title": "Kinmen", "link": "https://wikipedia.org/wiki/Kinmen"}]}, {"year": "1781", "text": "The Battle of Raft Swamp marks the last battle fought in North Carolina during the American Revolutionary War with a Patriot victory. It occurred four days before the British surrender at Yorktown.", "html": "1781 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Raft_Swamp\" title=\"Battle of Raft Swamp\">Battle of Raft Swamp</a> marks the last battle fought in North Carolina during the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a> with a Patriot victory. It occurred four days before the British surrender at Yorktown.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Raft_Swamp\" title=\"Battle of Raft Swamp\">Battle of Raft Swamp</a> marks the last battle fought in North Carolina during the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a> with a Patriot victory. It occurred four days before the British surrender at Yorktown.", "links": [{"title": "Battle of Raft Swamp", "link": "https://wikipedia.org/wiki/Battle_of_Raft_Swamp"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1783", "text": "The Montgolfier brothers' hot air balloon makes the first human ascent, piloted by <PERSON><PERSON><PERSON>.", "html": "1783 - The <a href=\"https://wikipedia.org/wiki/Montgolfier_brothers\" title=\"Montgolfier brothers\">Montgolfier brothers</a>' hot air balloon makes the first human ascent, piloted by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A7ois_Pil%C3%<PERSON><PERSON>_de_Rozier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Montgolfier_brothers\" title=\"Montgolfier brothers\">Montgolfier brothers</a>' hot air balloon makes the first human ascent, piloted by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A7ois_Pil%C3%<PERSON><PERSON>_de_Rozier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Montgolfier brothers", "link": "https://wikipedia.org/wiki/Montgol<PERSON>r_brothers"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A7ois_Pil%C3%<PERSON><PERSON>_<PERSON>_R<PERSON>"}]}, {"year": "1793", "text": "Queen <PERSON> of France is tried and convicted of treason.", "html": "1793 - Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of France is tried and convicted of treason.", "no_year_html": "Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of France is tried and convicted of treason.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON> begins his exile on Saint Helena in the South Atlantic Ocean.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his exile on Saint Helena in the South Atlantic Ocean.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his exile on Saint Helena in the South Atlantic Ocean.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}]}, {"year": "1864", "text": "American Civil War: The Union garrison of Glasgow, Missouri surrenders to Confederate forces.", "html": "1864 - American Civil War: The Union garrison of Glasgow, Missouri <a href=\"https://wikipedia.org/wiki/Battle_of_Glasgow,_Missouri\" title=\"Battle of Glasgow, Missouri\">surrenders</a> to Confederate forces.", "no_year_html": "American Civil War: The Union garrison of Glasgow, Missouri <a href=\"https://wikipedia.org/wiki/Battle_of_Glasgow,_Missouri\" title=\"Battle of Glasgow, Missouri\">surrenders</a> to Confederate forces.", "links": [{"title": "Battle of Glasgow, Missouri", "link": "https://wikipedia.org/wiki/Battle_of_Glasgow,_Missouri"}]}, {"year": "1878", "text": "The Edison Electric Light Company begins operation.", "html": "1878 - The <a href=\"https://wikipedia.org/wiki/Edison_Electric_Light_Company\" class=\"mw-redirect\" title=\"Edison Electric Light Company\">Edison Electric Light Company</a> begins operation.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Edison_Electric_Light_Company\" class=\"mw-redirect\" title=\"Edison Electric Light Company\">Edison Electric Light Company</a> begins operation.", "links": [{"title": "Edison Electric Light Company", "link": "https://wikipedia.org/wiki/Edison_Electric_Light_Company"}]}, {"year": "1888", "text": "The \"From Hell\" letter allegedly sent by <PERSON> the Ripper is received by investigators.", "html": "1888 - The <a href=\"https://wikipedia.org/wiki/From_Hell_letter\" title=\"From Hell letter\">\"From Hell\" letter</a> allegedly sent by <PERSON> the Ripper is received by investigators.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/From_Hell_letter\" title=\"From Hell letter\">\"From Hell\" letter</a> allegedly sent by <PERSON> the Ripper is received by investigators.", "links": [{"title": "From Hell letter", "link": "https://wikipedia.org/wiki/From_Hell_letter"}]}, {"year": "1910", "text": "Airship America is launched from New Jersey in the first attempt to cross the Atlantic by a powered aircraft.", "html": "1910 - <a href=\"https://wikipedia.org/wiki/America_(airship)\" title=\"America (airship)\">Airship <i>America</i></a> is launched from New Jersey in the first attempt to cross the Atlantic by a powered aircraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/America_(airship)\" title=\"America (airship)\">Airship <i>America</i></a> is launched from New Jersey in the first attempt to cross the Atlantic by a powered aircraft.", "links": [{"title": "America (airship)", "link": "https://wikipedia.org/wiki/America_(airship)"}]}, {"year": "1923", "text": "The German Rentenmark is introduced in Germany to counter hyperinflation in the Weimar Republic.", "html": "1923 - The <a href=\"https://wikipedia.org/wiki/German_Rentenmark\" class=\"mw-redirect\" title=\"German Rentenmark\">German Rentenmark</a> is introduced in Germany to counter <a href=\"https://wikipedia.org/wiki/Hyperinflation_in_the_Weimar_Republic\" title=\"Hyperinflation in the Weimar Republic\">hyperinflation in the Weimar Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/German_Rentenmark\" class=\"mw-redirect\" title=\"German Rentenmark\">German Rentenmark</a> is introduced in Germany to counter <a href=\"https://wikipedia.org/wiki/Hyperinflation_in_the_Weimar_Republic\" title=\"Hyperinflation in the Weimar Republic\">hyperinflation in the Weimar Republic</a>.", "links": [{"title": "German Rentenmark", "link": "https://wikipedia.org/wiki/German_Rentenmark"}, {"title": "Hyperinflation in the Weimar Republic", "link": "https://wikipedia.org/wiki/Hyperinflation_in_the_Weimar_Republic"}]}, {"year": "1928", "text": "The airship Graf Zeppelin completes its first trans-Atlantic flight, landing at Lakehurst, New Jersey, United States.", "html": "1928 - The airship <i><a href=\"https://wikipedia.org/wiki/LZ_127_Graf_Zeppelin\" title=\"LZ 127 Graf Zeppelin\">Graf Zeppelin</a></i> completes its first trans-Atlantic flight, landing at <a href=\"https://wikipedia.org/wiki/Lakehurst,_New_Jersey\" title=\"Lakehurst, New Jersey\">Lakehurst, New Jersey</a>, United States.", "no_year_html": "The airship <i><a href=\"https://wikipedia.org/wiki/LZ_127_Graf_Zeppelin\" title=\"LZ 127 Graf Zeppelin\">Graf Zeppelin</a></i> completes its first trans-Atlantic flight, landing at <a href=\"https://wikipedia.org/wiki/Lakehurst,_New_Jersey\" title=\"Lakehurst, New Jersey\">Lakehurst, New Jersey</a>, United States.", "links": [{"title": "LZ 127 Graf Zeppelin", "link": "https://wikipedia.org/wiki/LZ_127_Graf_Zeppelin"}, {"title": "Lakehurst, New Jersey", "link": "https://wikipedia.org/wiki/Lakehurst,_New_Jersey"}]}, {"year": "1932", "text": "Tata Airlines (later to become Air India) makes its first flight.", "html": "1932 - Tata Airlines (later to become <a href=\"https://wikipedia.org/wiki/Air_India\" title=\"Air India\">Air India</a>) makes its first flight.", "no_year_html": "Tata Airlines (later to become <a href=\"https://wikipedia.org/wiki/Air_India\" title=\"Air India\">Air India</a>) makes its first flight.", "links": [{"title": "Air India", "link": "https://wikipedia.org/wiki/Air_India"}]}, {"year": "1939", "text": "The New York Municipal Airport (later renamed LaGuardia Airport) is dedicated.", "html": "1939 - The New York Municipal Airport (later renamed <a href=\"https://wikipedia.org/wiki/LaGuardia_Airport\" title=\"LaGuardia Airport\">LaGuardia Airport</a>) is dedicated.", "no_year_html": "The New York Municipal Airport (later renamed <a href=\"https://wikipedia.org/wiki/LaGuardia_Airport\" title=\"LaGuardia Airport\">LaGuardia Airport</a>) is dedicated.", "links": [{"title": "LaGuardia Airport", "link": "https://wikipedia.org/wiki/LaGuardia_Airport"}]}, {"year": "1940", "text": "President <PERSON><PERSON><PERSON> of Catalonia is executed by the Francoist government.", "html": "1940 - President <a href=\"https://wikipedia.org/wiki/Llu%C3%ADs_Companys\" title=\"Lluís Companys\">Lluís Companys</a> of Catalonia is executed by the <a href=\"https://wikipedia.org/wiki/Francoist\" class=\"mw-redirect\" title=\"Francoist\">Francoist</a> government.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/Llu%C3%ADs_Companys\" title=\"Lluís Companys\">Lluís Companys</a> of Catalonia is executed by the <a href=\"https://wikipedia.org/wiki/Francoist\" class=\"mw-redirect\" title=\"Francoist\">Francoist</a> government.", "links": [{"title": "Lluís Companys", "link": "https://wikipedia.org/wiki/Llu%C3%ADs_Companys"}, {"title": "Francoist", "link": "https://wikipedia.org/wiki/Francoist"}]}, {"year": "1944", "text": "World War II: Germany replaces the Hungarian government after Hungary announces an armistice with the Soviet Union.", "html": "1944 - World War II: Germany <a href=\"https://wikipedia.org/wiki/Operation_Panzerfaust\" title=\"Operation Panzerfaust\">replaces the Hungarian government</a> after Hungary announces an armistice with the Soviet Union.", "no_year_html": "World War II: Germany <a href=\"https://wikipedia.org/wiki/Operation_Panzerfaust\" title=\"Operation Panzerfaust\">replaces the Hungarian government</a> after Hungary announces an armistice with the Soviet Union.", "links": [{"title": "Operation Panzerfaust", "link": "https://wikipedia.org/wiki/Operation_Panzerfaust"}]}, {"year": "1951", "text": "Mexican chemist <PERSON> completes the synthesis of norethisterone, the basis of an early oral contraceptive.", "html": "1951 - Mexican chemist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Miramontes\" title=\"<PERSON>\"><PERSON></a> completes the synthesis of <a href=\"https://wikipedia.org/wiki/Norethisterone\" title=\"Norethisterone\">norethisterone</a>, the basis of an early oral contraceptive.", "no_year_html": "Mexican chemist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> completes the synthesis of <a href=\"https://wikipedia.org/wiki/Norethisterone\" title=\"Norethisterone\">norethisterone</a>, the basis of an early oral contraceptive.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>._Miramontes"}, {"title": "Norethisterone", "link": "https://wikipedia.org/wiki/Norethisterone"}]}, {"year": "1954", "text": "Hurricane Hazel devastates the eastern seaboard of North America, killing 95 and causing massive floods as far north as Toronto.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Hurricane_Hazel\" title=\"Hurricane Hazel\">Hurricane <PERSON></a> devastates the eastern seaboard of North America, killing 95 and causing massive floods as far north as Toronto.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Hazel\" title=\"Hurricane Hazel\">Hurricane <PERSON></a> devastates the eastern seaboard of North America, killing 95 and causing massive floods as far north as Toronto.", "links": [{"title": "Hurricane Hazel", "link": "https://wikipedia.org/wiki/Hurricane_Hazel"}]}, {"year": "1956", "text": "FORTRAN, the first modern computer language, is first shared with the coding community.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/FORTRAN\" class=\"mw-redirect\" title=\"FORTRAN\">FORTRAN</a>, the first modern computer language, is first shared with the coding community.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/FORTRAN\" class=\"mw-redirect\" title=\"FORTRAN\">FORTRAN</a>, the first modern computer language, is first shared with the coding community.", "links": [{"title": "FORTRAN", "link": "https://wikipedia.org/wiki/FORTRAN"}]}, {"year": "1965", "text": "Vietnam War: A draft card is burned during an anti-war rally by the Catholic Worker Movement, resulting in the first arrest under a new law.", "html": "1965 - Vietnam War: A draft card is burned during an anti-war rally by the <a href=\"https://wikipedia.org/wiki/Catholic_Worker_Movement\" title=\"Catholic Worker Movement\">Catholic Worker Movement</a>, resulting in the first arrest under a new law.", "no_year_html": "Vietnam War: A draft card is burned during an anti-war rally by the <a href=\"https://wikipedia.org/wiki/Catholic_Worker_Movement\" title=\"Catholic Worker Movement\">Catholic Worker Movement</a>, resulting in the first arrest under a new law.", "links": [{"title": "Catholic Worker Movement", "link": "https://wikipedia.org/wiki/Catholic_Worker_Movement"}]}, {"year": "1966", "text": "The Black Panther Party is created by <PERSON><PERSON> and <PERSON>.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a> is created by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a> is created by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Black Panther Party", "link": "https://wikipedia.org/wiki/Black_Panther_Party"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "During the construction of Australia's West Gate Bridge, a span of the bridge falls and kills 35 workers. The incident is the country's worst industrial accident to this day.", "html": "1970 - During the construction of Australia's <a href=\"https://wikipedia.org/wiki/West_Gate_Bridge\" title=\"West Gate Bridge\">West Gate Bridge</a>, a span of the bridge falls and kills 35 workers. The incident is the country's worst industrial accident to this day.", "no_year_html": "During the construction of Australia's <a href=\"https://wikipedia.org/wiki/West_Gate_Bridge\" title=\"West Gate Bridge\">West Gate Bridge</a>, a span of the bridge falls and kills 35 workers. The incident is the country's worst industrial accident to this day.", "links": [{"title": "West Gate Bridge", "link": "https://wikipedia.org/wiki/West_Gate_Bridge"}]}, {"year": "1979", "text": "Supporters of the Malta Labour Party ransack and destroy the Times of Malta building and other locations associated with the Nationalist Party.", "html": "1979 - Supporters of the Malta Labour Party <a href=\"https://wikipedia.org/wiki/Black_Monday_(Malta)\" title=\"Black Monday (Malta)\">ransack and destroy</a> the <i>Times of Malta</i> building and other locations associated with the Nationalist Party.", "no_year_html": "Supporters of the Malta Labour Party <a href=\"https://wikipedia.org/wiki/Black_Monday_(Malta)\" title=\"Black Monday (Malta)\">ransack and destroy</a> the <i>Times of Malta</i> building and other locations associated with the Nationalist Party.", "links": [{"title": "Black Monday (Malta)", "link": "https://wikipedia.org/wiki/Black_Monday_(Malta)"}]}, {"year": "1979", "text": "A coup d'état in El Salvador overthrows President <PERSON> and begins the 12 year-long Salvadoran Civil War.", "html": "1979 - A <a href=\"https://wikipedia.org/wiki/1979_Salvadoran_coup_d%27%C3%A9tat\" title=\"1979 Salvadoran coup d'état\">coup d'état</a> in El Salvador overthrows President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and begins the 12 year-long <a href=\"https://wikipedia.org/wiki/Salvadoran_Civil_War\" title=\"Salvadoran Civil War\">Salvadoran Civil War</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1979_Salvadoran_coup_d%27%C3%A9tat\" title=\"1979 Salvadoran coup d'état\">coup d'état</a> in El Salvador overthrows President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and begins the 12 year-long <a href=\"https://wikipedia.org/wiki/Salvadoran_Civil_War\" title=\"Salvadoran Civil War\">Salvadoran Civil War</a>.", "links": [{"title": "1979 Salvadoran coup d'état", "link": "https://wikipedia.org/wiki/1979_Salvadoran_coup_d%27%C3%A9tat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Salvadoran Civil War", "link": "https://wikipedia.org/wiki/Salvadoran_Civil_War"}]}, {"year": "1987", "text": "Aero Trasporti Italiani Flight 460 crashes near Conca di Crezzo, Italy, killing all 37 people on board.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Aero_Trasporti_Italiani_Flight_460\" title=\"Aero Trasporti Italiani Flight 460\">Aero Trasporti Italiani Flight 460</a> crashes near Conca di Crezzo, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>, killing all 37 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aero_Trasporti_Italiani_Flight_460\" title=\"Aero Trasporti Italiani Flight 460\">Aero Trasporti Italiani Flight 460</a> crashes near Conca di Crezzo, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>, killing all 37 people on board.", "links": [{"title": "Aero Trasporti Italiani Flight 460", "link": "https://wikipedia.org/wiki/Aero_Trasporti_Italiani_Flight_460"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}]}, {"year": "1987", "text": "A coup d'état in Burkina Faso overthrows and kills then President <PERSON>.", "html": "1987 - A <a href=\"https://wikipedia.org/wiki/1987_Burkina_Faso_coup_d%27%C3%A9tat\" title=\"1987 Burkina Faso coup d'état\">coup d'état</a> in Burkina Faso overthrows and kills then President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1987_Burkina_Faso_coup_d%27%C3%A9tat\" title=\"1987 Burkina Faso coup d'état\">coup d'état</a> in Burkina Faso overthrows and kills then President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "1987 Burkina Faso coup d'état", "link": "https://wikipedia.org/wiki/1987_Burkina_Faso_coup_d%27%C3%A9tat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON> becomes the all-time leading points scorer in the NHL.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the all-time leading points scorer in the <a href=\"https://wikipedia.org/wiki/NHL\" class=\"mw-redirect\" title=\"NHL\">NHL</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the all-time leading points scorer in the <a href=\"https://wikipedia.org/wiki/NHL\" class=\"mw-redirect\" title=\"NHL\">NHL</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "NHL", "link": "https://wikipedia.org/wiki/NHL"}]}, {"year": "1990", "text": "Soviet Union leader <PERSON> is awarded the Nobel Peace Prize for his efforts to lessen Cold War tensions and open up his nation.", "html": "1990 - Soviet Union leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize</a> for his efforts to lessen Cold War tensions and open up his nation.", "no_year_html": "Soviet Union leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize</a> for his efforts to lessen Cold War tensions and open up his nation.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1991", "text": "The \"Oh-My-God particle\", an ultra-high-energy cosmic ray measured at 40,000,000 times that of the highest energy protons produced in a particle accelerator, is observed at the University of Utah HiRes observatory in Dugway Proving Ground, Utah.", "html": "1991 - The \"<a href=\"https://wikipedia.org/wiki/Oh-My-God_particle\" title=\"Oh-My-God particle\">Oh-My-God particle</a>\", an <a href=\"https://wikipedia.org/wiki/Ultra-high-energy_cosmic_ray\" title=\"Ultra-high-energy cosmic ray\">ultra-high-energy cosmic ray</a> measured at 40,000,000 times that of the highest energy protons produced in a <a href=\"https://wikipedia.org/wiki/Particle_accelerator\" title=\"Particle accelerator\">particle accelerator</a>, is observed at the <a href=\"https://wikipedia.org/wiki/University_of_Utah\" title=\"University of Utah\">University of Utah</a> <a href=\"https://wikipedia.org/wiki/High_Resolution_Fly%27s_Eye_Cosmic_Ray_Detector\" title=\"High Resolution Fly's Eye Cosmic Ray Detector\">HiRes</a> observatory in <a href=\"https://wikipedia.org/wiki/Dugway_Proving_Ground\" title=\"Dugway Proving Ground\">Dugway Proving Ground</a>, <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a>.", "no_year_html": "The \"<a href=\"https://wikipedia.org/wiki/Oh-My-God_particle\" title=\"Oh-My-God particle\">Oh-My-God particle</a>\", an <a href=\"https://wikipedia.org/wiki/Ultra-high-energy_cosmic_ray\" title=\"Ultra-high-energy cosmic ray\">ultra-high-energy cosmic ray</a> measured at 40,000,000 times that of the highest energy protons produced in a <a href=\"https://wikipedia.org/wiki/Particle_accelerator\" title=\"Particle accelerator\">particle accelerator</a>, is observed at the <a href=\"https://wikipedia.org/wiki/University_of_Utah\" title=\"University of Utah\">University of Utah</a> <a href=\"https://wikipedia.org/wiki/High_Resolution_Fly%27s_Eye_Cosmic_Ray_Detector\" title=\"High Resolution Fly's Eye Cosmic Ray Detector\">HiRes</a> observatory in <a href=\"https://wikipedia.org/wiki/Dugway_Proving_Ground\" title=\"Dugway Proving Ground\">Dugway Proving Ground</a>, <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a>.", "links": [{"title": "Oh-My-God particle", "link": "https://wikipedia.org/wiki/Oh-My-God_particle"}, {"title": "Ultra-high-energy cosmic ray", "link": "https://wikipedia.org/wiki/Ultra-high-energy_cosmic_ray"}, {"title": "Particle accelerator", "link": "https://wikipedia.org/wiki/Particle_accelerator"}, {"title": "University of Utah", "link": "https://wikipedia.org/wiki/University_of_Utah"}, {"title": "High Resolution Fly's Eye Cosmic Ray Detector", "link": "https://wikipedia.org/wiki/High_Resolution_Fly%27s_Eye_Cosmic_Ray_Detector"}, {"title": "Dugway Proving Ground", "link": "https://wikipedia.org/wiki/Dugway_Proving_Ground"}, {"title": "Utah", "link": "https://wikipedia.org/wiki/Utah"}]}, {"year": "1991", "text": "The leaders of the Baltic States, <PERSON> of Estonia, <PERSON><PERSON><PERSON><PERSON><PERSON> of Latvia and <PERSON><PERSON><PERSON><PERSON> of Lithuania, signed the OSCE Final Act in Helsinki, Finland.", "html": "1991 - The leaders of the <a href=\"https://wikipedia.org/wiki/Baltic_States\" class=\"mw-redirect\" title=\"Baltic States\">Baltic States</a>, <a href=\"https://wikipedia.org/wiki/Arnold_R%C3%BC%C3%BCtel\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Anatolijs_Gorbunovs\" title=\"Anatolijs Gorbunovs\">Anato<PERSON><PERSON><PERSON> Gorbunovs</a> of <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> and <a href=\"https://wikipedia.org/wiki/Vytautas_Landsbergis\" title=\"Vytautas Landsbergis\">Vytaut<PERSON></a> of <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, signed the <a href=\"https://wikipedia.org/wiki/OSCE\" class=\"mw-redirect\" title=\"OSCE\">OSCE</a> Final Act in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, Finland.", "no_year_html": "The leaders of the <a href=\"https://wikipedia.org/wiki/Baltic_States\" class=\"mw-redirect\" title=\"Baltic States\">Baltic States</a>, <a href=\"https://wikipedia.org/wiki/Arnold_R%C3%BC%C3%BCtel\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Anatolijs_Gorbunovs\" title=\"Anatolijs Gorbunovs\">Anato<PERSON><PERSON><PERSON> Gorbunovs</a> of <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> and <a href=\"https://wikipedia.org/wiki/Vytautas_<PERSON>bergis\" title=\"Vytautas Landsbergis\">Vyt<PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, signed the <a href=\"https://wikipedia.org/wiki/OSCE\" class=\"mw-redirect\" title=\"OSCE\">OSCE</a> Final Act in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, Finland.", "links": [{"title": "Baltic States", "link": "https://wikipedia.org/wiki/Baltic_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arnold_R%C3%BC%C3%BCtel"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>s"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vytaut<PERSON>_<PERSON>bergis"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "OSCE", "link": "https://wikipedia.org/wiki/OSCE"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}]}, {"year": "1994", "text": "The United States, under the <PERSON> administration, returns Haiti's first democratically elected president, <PERSON><PERSON><PERSON>, to the island.", "html": "1994 - The United States, under the <a href=\"https://wikipedia.org/wiki/<PERSON>_administration\" class=\"mw-redirect\" title=\"<PERSON> administration\"><PERSON> administration</a>, returns Haiti's first democratically elected president, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, to the island.", "no_year_html": "The United States, under the <a href=\"https://wikipedia.org/wiki/<PERSON>_administration\" class=\"mw-redirect\" title=\"<PERSON> administration\"><PERSON> administration</a>, returns Haiti's first democratically elected president, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, to the island.", "links": [{"title": "<PERSON> administration", "link": "https://wikipedia.org/wiki/<PERSON>_administration"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "The Cassini probe launches from Cape Canaveral on its way to Saturn.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Cassini%E2%80%93Huygens\" title=\"Cassini-Huygens\">Cassini probe</a> launches from Cape Canaveral on its way to Saturn.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cassini%E2%80%93Huygens\" title=\"Cassini-Huygens\">Cassini probe</a> launches from Cape Canaveral on its way to Saturn.", "links": [{"title": "Cassini-Huygens", "link": "https://wikipedia.org/wiki/Cassini%E2%80%93Huygens"}]}, {"year": "2001", "text": "NASA's Galileo spacecraft passes within 112 miles (180 km) of Jupiter's moon Io.", "html": "2001 - NASA's <a href=\"https://wikipedia.org/wiki/Galileo_spacecraft\" class=\"mw-redirect\" title=\"Galileo spacecraft\">Galileo spacecraft</a> passes within 112 miles (180 km) of Jupiter's moon Io.", "no_year_html": "NASA's <a href=\"https://wikipedia.org/wiki/Galileo_spacecraft\" class=\"mw-redirect\" title=\"Galileo spacecraft\">Galileo spacecraft</a> passes within 112 miles (180 km) of Jupiter's moon Io.", "links": [{"title": "Galileo spacecraft", "link": "https://wikipedia.org/wiki/Galileo_spacecraft"}]}, {"year": "2003", "text": "China launches Shenzhou 5, its first crewed space mission.", "html": "2003 - China launches <a href=\"https://wikipedia.org/wiki/Shenzhou_5\" title=\"Shenzhou 5\">Shenzhou 5</a>, its first crewed space mission.", "no_year_html": "China launches <a href=\"https://wikipedia.org/wiki/Shenzhou_5\" title=\"Shenzhou 5\">Shenzhou 5</a>, its first crewed space mission.", "links": [{"title": "Shenzhou 5", "link": "https://wikipedia.org/wiki/Shenzhou_5"}]}, {"year": "2006", "text": "The 6.7 .mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}Mw Kiholo Bay earthquake rocks Hawaii, causing property damage, injuries, landslides, power outages, and the closure of Honolulu International Airport.", "html": "2006 - The 6.7 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><style data-mw-deduplicate=\"TemplateStyles:r1038841319\">.mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}</style>\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/2006_Kiholo_Bay_earthquake\" title=\"2006 Kiholo Bay earthquake\">Kiholo Bay earthquake</a> rocks Hawaii, causing property damage, injuries, landslides, power outages, and the closure of Honolulu International Airport.", "no_year_html": "The 6.7 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><style data-mw-deduplicate=\"TemplateStyles:r1038841319\">.mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}</style>\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/2006_Kiholo_Bay_earthquake\" title=\"2006 Kiholo Bay earthquake\">Kiholo Bay earthquake</a> rocks Hawaii, causing property damage, injuries, landslides, power outages, and the closure of Honolulu International Airport.", "links": [{"title": "Seismic magnitude scales", "link": "https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw"}, {"title": "2006 Kiholo Bay earthquake", "link": "https://wikipedia.org/wiki/2006_Kiholo_Bay_earthquake"}]}, {"year": "2007", "text": "Seventeen activists in New Zealand are arrested in the country's first post-9/11 anti-terrorism raids.", "html": "2007 - Seventeen activists in New Zealand are arrested in the country's first post-9/11 <a href=\"https://wikipedia.org/wiki/2007_New_Zealand_police_raids\" title=\"2007 New Zealand police raids\">anti-terrorism raids</a>.", "no_year_html": "Seventeen activists in New Zealand are arrested in the country's first post-9/11 <a href=\"https://wikipedia.org/wiki/2007_New_Zealand_police_raids\" title=\"2007 New Zealand police raids\">anti-terrorism raids</a>.", "links": [{"title": "2007 New Zealand police raids", "link": "https://wikipedia.org/wiki/2007_New_Zealand_police_raids"}]}, {"year": "2008", "text": "The Dow Jones Industrial Average closes down 733.08 points, or 7.87%, the second worst percentage drop in the Dow's history.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/Dow_Jones_Industrial_Average\" title=\"Dow Jones Industrial Average\">Dow Jones Industrial Average</a> closes down 733.08 points, or 7.87%, the <a href=\"https://wikipedia.org/wiki/Black_Monday_(1987)\" title=\"Black Monday (1987)\">second worst percentage drop</a> in the Dow's history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dow_Jones_Industrial_Average\" title=\"Dow Jones Industrial Average\">Dow Jones Industrial Average</a> closes down 733.08 points, or 7.87%, the <a href=\"https://wikipedia.org/wiki/Black_Monday_(1987)\" title=\"Black Monday (1987)\">second worst percentage drop</a> in the Dow's history.", "links": [{"title": "Dow Jones Industrial Average", "link": "https://wikipedia.org/wiki/<PERSON>_Jones_Industrial_Average"}, {"title": "Black Monday (1987)", "link": "https://wikipedia.org/wiki/Black_Monday_(1987)"}]}, {"year": "2013", "text": "The 7.2 Mw Bohol earthquake strikes the Philippines. At least 215 were killed.", "html": "2013 - The 7.2 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><link rel=\"mw-deduplicated-inline-style\" href=\"mw-data:TemplateStyles:r1038841319\">\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/2013_Bohol_earthquake\" title=\"2013 Bohol earthquake\">Bohol earthquake</a> strikes the Philippines. At least 215 were killed.", "no_year_html": "The 7.2 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><link rel=\"mw-deduplicated-inline-style\" href=\"mw-data:TemplateStyles:r1038841319\">\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/2013_Bohol_earthquake\" title=\"2013 Bohol earthquake\">Bohol earthquake</a> strikes the Philippines. At least 215 were killed.", "links": [{"title": "Seismic magnitude scales", "link": "https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw"}, {"title": "2013 Bohol earthquake", "link": "https://wikipedia.org/wiki/2013_Bohol_earthquake"}]}, {"year": "2016", "text": "One hundred and ninety-seven nations amend the Montreal Protocol to include a phase-out of hydrofluorocarbons.", "html": "2016 - One hundred and ninety-seven nations amend the <a href=\"https://wikipedia.org/wiki/Montreal_Protocol\" title=\"Montreal Protocol\">Montreal Protocol</a> to include a phase-out of hydrofluorocarbons.", "no_year_html": "One hundred and ninety-seven nations amend the <a href=\"https://wikipedia.org/wiki/Montreal_Protocol\" title=\"Montreal Protocol\">Montreal Protocol</a> to include a phase-out of hydrofluorocarbons.", "links": [{"title": "Montreal Protocol", "link": "https://wikipedia.org/wiki/Montreal_Protocol"}]}, {"year": "2018", "text": "13-year-old American girl, <PERSON><PERSON>, is kidnapped from her Barron, Wisconsin home after her parents were both murdered.", "html": "2018 - 13-year-old American girl, <a href=\"https://wikipedia.org/wiki/Kidnapping_of_<PERSON><PERSON>_<PERSON>\" title=\"Kidnapping of <PERSON><PERSON>\"><PERSON><PERSON></a>, is kidnapped from her <a href=\"https://wikipedia.org/wiki/<PERSON>,_Wisconsin\" title=\"Barron, Wisconsin\">Barron, Wisconsin</a> home after her parents were both murdered.", "no_year_html": "13-year-old American girl, <a href=\"https://wikipedia.org/wiki/Kidnapping_of_<PERSON><PERSON>_<PERSON>\" title=\"Kidnapping of <PERSON><PERSON>\"><PERSON><PERSON></a>, is kidnapped from her <a href=\"https://wikipedia.org/wiki/<PERSON>,_Wisconsin\" title=\"Barron, Wisconsin\">Barron, Wisconsin</a> home after her parents were both murdered.", "links": [{"title": "Kidnapping of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kidnapping_of_<PERSON><PERSON>_<PERSON>loss"}, {"title": "Barron, Wisconsin", "link": "https://wikipedia.org/wiki/Barron,_Wisconsin"}]}], "Births": [{"year": "99 (probable)[16]", "text": "<PERSON><PERSON><PERSON>, Roman poet and philosopher (d. 55 BCE)", "html": "99 (probable)[16] - <a href=\"https://wikipedia.org/wiki/99_BC\" title=\"99 BC\">99 BC</a> (probable) - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman poet and philosopher (d. 55 BCE)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/99_BC\" title=\"99 BC\">99 BC</a> (probable) - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman poet and philosopher (d. 55 BCE)", "links": [{"title": "99 BC", "link": "https://wikipedia.org/wiki/99_BC"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "70 BC", "text": "<PERSON>, Roman poet (d. 19 BC)", "html": "70 BC - 70 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman poet (d. 19 BC)", "no_year_html": "70 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman poet (d. 19 BC)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virgil"}]}, {"year": "1265", "text": "<PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan (d. 1307)", "html": "1265 - <a href=\"https://wikipedia.org/wiki/Tem%C3%<PERSON><PERSON>_<PERSON>,_Emperor_<PERSON><PERSON>_of_Yuan\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON> of Yuan\"><PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan</a> (d. 1307)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tem%C3%<PERSON><PERSON>_<PERSON>,_Emperor_<PERSON><PERSON>_of_Yuan\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON> of Yuan\"><PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan</a> (d. 1307)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan", "link": "https://wikipedia.org/wiki/Tem%C3%<PERSON><PERSON>_<PERSON>,_Emperor_<PERSON><PERSON>_of_Yuan"}]}, {"year": "1440", "text": "<PERSON>, Landgrave of Upper Hesse, German noble (d. 1483)", "html": "1440 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Upper_Hesse\" title=\"<PERSON>, Landgrave of Upper Hesse\"><PERSON>, Landgrave of Upper Hesse</a>, German noble (d. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Upper_Hesse\" title=\"<PERSON>, Landgrave of Upper Hesse\"><PERSON>, Landgrave of Upper Hesse</a>, German noble (d. 1483)", "links": [{"title": "<PERSON>, Landgrave of Upper Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Upper_Hesse"}]}, {"year": "1471", "text": "<PERSON>, German epigrammatist and academic (d. 1526)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German epigrammatist and academic (d. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German epigrammatist and academic (d. 1526)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1542", "text": "<PERSON>, Mughal emperor (d. 1605)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mughal emperor (d. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mughal emperor (d. 1605)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Akbar"}]}, {"year": "1561", "text": "<PERSON>, English cathedral dean (d. 1616)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English cathedral dean (d. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English cathedral dean (d. 1616)", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)"}]}, {"year": "1564", "text": "<PERSON>, Duke of Brunswick-Lüneburg (d. 1613)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> (d. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> (d. 1613)", "links": [{"title": "<PERSON>, Duke of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1599", "text": "<PERSON><PERSON><PERSON>, Dutch mayor and regent of Amsterdam (d. 1664)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch mayor and regent of Amsterdam (d. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch mayor and regent of Amsterdam (d. 1664)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON><PERSON><PERSON>, Italian physicist and mathematician (d. 1647)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physicist and mathematician (d. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physicist and mathematician (d. 1647)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON>, Swedish statesman and military man (d. 1686)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish statesman and military man (d. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish statesman and military man (d. 1686)", "links": [{"title": "<PERSON>ardie", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, Scottish poet and playwright (d. 1758)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and playwright (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and playwright (d. 1758)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1701", "text": "<PERSON><PERSON><PERSON>, Canadian nun and saint, founded Grey Nuns (d. 1771)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%27Youville\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian nun and saint, founded <a href=\"https://wikipedia.org/wiki/Grey_Nuns\" title=\"Grey Nuns\">Grey Nuns</a> (d. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Youville\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian nun and saint, founded <a href=\"https://wikipedia.org/wiki/Grey_Nuns\" title=\"Grey Nuns\">Grey Nuns</a> (d. 1771)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%27Youville"}, {"title": "Grey Nuns", "link": "https://wikipedia.org/wiki/Grey_Nuns"}]}, {"year": "1711", "text": "<PERSON> of Lorraine (d. 1741)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Lorraine\" title=\"<PERSON> of Lorraine\"><PERSON> of Lorraine</a> (d. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Lorraine\" title=\"<PERSON> of Lorraine\"><PERSON> of Lorraine</a> (d. 1741)", "links": [{"title": "<PERSON> of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_Lorraine"}]}, {"year": "1762", "text": "<PERSON>, American composer and educator (d. 1820)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "<PERSON>, French-born American Roman Catholic priest, missionary, educator, and politician (d. 1832)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-born American <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic</a> priest, missionary, educator, and politician (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-born American <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic</a> priest, missionary, educator, and politician (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "1775", "text": "<PERSON>, Finnish composer (d. 1838)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish composer (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish composer (d. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1784", "text": "<PERSON>, French general and politician, Governor-General of Algeria (d. 1849)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/List_of_rulers_of_Algeria_before_independence\" class=\"mw-redirect\" title=\"List of rulers of Algeria before independence\">Governor-General of Algeria</a> (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/List_of_rulers_of_Algeria_before_independence\" class=\"mw-redirect\" title=\"List of rulers of Algeria before independence\">Governor-General of Algeria</a> (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of rulers of Algeria before independence", "link": "https://wikipedia.org/wiki/List_of_rulers_of_Algeria_before_independence"}]}, {"year": "1785", "text": "<PERSON>, Chilean general and politician (d. 1821)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean general and politician (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean general and politician (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, Danish chemist who prepared <PERSON><PERSON><PERSON>'s salt, one of the first organometallic compounds (d. 1847)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish chemist who prepared <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_salt\" title=\"<PERSON><PERSON><PERSON>'s salt\"><PERSON><PERSON><PERSON>'s salt</a>, one of the first organometallic compounds (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish chemist who prepared <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_salt\" title=\"<PERSON><PERSON><PERSON>'s salt\"><PERSON><PERSON><PERSON>'s salt</a>, one of the first organometallic compounds (d. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>'s salt", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_salt"}]}, {"year": "1802", "text": "<PERSON><PERSON><PERSON>, French general and politician, head of state of France in 1848 (d. 1857)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_France#French_Second_Republic_(1848-1852)\" title=\"List of heads of state of France\">head of state of France</a> in 1848 (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_France#French_Second_Republic_(1848-1852)\" title=\"List of heads of state of France\">head of state of France</a> in 1848 (d. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Louis-Eug%C3%A8ne_<PERSON>ignac"}, {"title": "List of heads of state of France", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_France#French_Second_Republic_(1848-1852)"}]}, {"year": "1814", "text": "<PERSON>, Russian author, poet, and painter (d. 1841)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author, poet, and painter (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author, poet, and painter (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, English-Australian politician, 5th Premier of New South Wales (d. 1891)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>(premier)\" title=\"<PERSON> (premier)\"><PERSON></a>, English-Australian politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(premier)\" title=\"<PERSON> (premier)\"><PERSON></a>, English-Australian politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1891)", "links": [{"title": "<PERSON> (premier)", "link": "https://wikipedia.org/wiki/<PERSON>_(premier)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1818", "text": "<PERSON>, Czech pianist and composer (d. 1869)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech pianist and composer (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech pianist and composer (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON> of Prussia (d. 1889)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/Marie_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marie_<PERSON>_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (d. 1889)", "links": [{"title": "Marie of Prussia", "link": "https://wikipedia.org/wiki/Marie_of_Prussia"}]}, {"year": "1829", "text": "<PERSON><PERSON>, American astronomer and academic (d. 1907)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Asaph_Hall\" title=\"Asaph Hall\">Asaph Hall</a>, American astronomer and academic (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asaph_Hall\" title=\"Asaph Hall\">As<PERSON> Hall</a>, American astronomer and academic (d. 1907)", "links": [{"title": "Asaph Hall", "link": "https://wikipedia.org/wiki/Asaph_Hall"}]}, {"year": "1833", "text": "<PERSON>, Australian politician, 7th Premier of Victoria (d. 1894)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1836", "text": "<PERSON>, French painter and illustrator (d. 1902)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON>, Canadian journalist, lawyer, and politician, 9th Premier of Quebec (d. 1894)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_Me<PERSON>ier\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist, lawyer, and politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_Me<PERSON>ier\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist, lawyer, and politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Honor%C3%A9_Me<PERSON>ier"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1844", "text": "<PERSON>, German composer, poet, and philosopher (d. 1900)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, poet, and philosopher (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, poet, and philosopher (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, American boxer, actor, and journalist (d. 1918)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer, actor, and journalist (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer, actor, and journalist (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American singer and educator (d. 1925)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and educator (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and educator (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Austrian educator and politician, 3rd President of Austria (d. 1956)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Austria\" title=\"President of Austria\">President of Austria</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Austria\" title=\"President of Austria\">President of Austria</a> (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Austria", "link": "https://wikipedia.org/wiki/President_of_Austria"}]}, {"year": "1872", "text": "<PERSON>, Swedish pole vaulter, shot putter, and tug of war competitor (d. 1921)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Swedish pole vaulter, shot putter, and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Swedish pole vaulter, shot putter, and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>"}, {"title": "Tug of war", "link": "https://wikipedia.org/wiki/Tug_of_war"}]}, {"year": "1874", "text": "<PERSON>, Hereditary Prince of Saxe-Coburg and Gotha (d. 1899)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Hereditary_Prince_of_Saxe-Coburg_and_Gotha\" title=\"<PERSON>, Hereditary Prince of Saxe-Coburg and Gotha\"><PERSON>, Hereditary Prince of Saxe-Coburg and Gotha</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Hereditary_Prince_of_Saxe-Coburg_and_Gotha\" title=\"<PERSON>, Hereditary Prince of Saxe-Coburg and Gotha\"><PERSON>, Hereditary Prince of Saxe-Coburg and Gotha</a> (d. 1899)", "links": [{"title": "<PERSON>, Hereditary Prince of Saxe-Coburg and Gotha", "link": "https://wikipedia.org/wiki/<PERSON>,_Hereditary_Prince_of_Saxe-Coburg_and_Gotha"}]}, {"year": "1878", "text": "<PERSON>, French lawyer and politician, 118th Prime Minister of France (d. 1966)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 118th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 118th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1879", "text": "<PERSON>, American actress (d. 1967)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON> <PERSON><PERSON>, English novelist and playwright (d. 1975)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/P._G._Wodehouse\" title=\"P. G. Wodehouse\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist and playwright (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P._G<PERSON>_Wodehouse\" title=\"P. G. Wodehouse\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist and playwright (d. 1975)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P._G._Wodehouse"}]}, {"year": "1882", "text": "<PERSON>, American baseball player and coach (d. 1941)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary"}]}, {"year": "1884", "text": "<PERSON>, American pilot (d. 1910)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, English sailor (d. 1965)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Frederick_Fleet\" title=\"Frederick Fleet\"><PERSON></a>, English sailor (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frederick_Fleet\" title=\"Frederick Fleet\"><PERSON></a>, English sailor (d. 1965)", "links": [{"title": "Frederick <PERSON>", "link": "https://wikipedia.org/wiki/Frederick_Fleet"}]}, {"year": "1888", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and critic (d. 1939)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"S. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and critic (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._S<PERSON>_<PERSON>_<PERSON>\" title=\"S. S<PERSON> Van <PERSON>\"><PERSON>. <PERSON><PERSON></a>, American author and critic (d. 1939)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Portuguese poet and engineer (d. 1935)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese poet and engineer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese poet and engineer (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON> of Romania (d. 1953)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Romania\" title=\"Carol II of Romania\"><PERSON> of Romania</a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Romania\" title=\"Carol II of Romania\"><PERSON> of Romania</a> (d. 1953)", "links": [{"title": "<PERSON> of Romania", "link": "https://wikipedia.org/wiki/Carol_II_of_Romania"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Ukrainian-Israeli lieutenant and politician, 2nd Prime Minister of Israel (d. 1965)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Israeli lieutenant and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Israeli lieutenant and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tt"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}]}, {"year": "1897", "text": "<PERSON>, Estonian soldier and politician, Prime Minister of Estonia in exile (d. 1960)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian soldier and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian soldier and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Estonia in exile", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile"}]}, {"year": "1899", "text": "<PERSON>, Polish-German racing driver (d. 1986)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German racing driver (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German racing driver (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, American actor, director, and producer (d. 1987)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Spanish playwright and novelist (d. 1952)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish playwright and novelist (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish playwright and novelist (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON> <PERSON><PERSON>, English chemist and author (d. 1980)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>\" title=\"C. P. Snow\"><PERSON><PERSON> <PERSON><PERSON></a>, English chemist and author (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._P<PERSON>_<PERSON>\" title=\"C. P. Snow\"><PERSON><PERSON> <PERSON><PERSON></a>, English chemist and author (d. 1980)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, American soldier and politician (d. 2004)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and politician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and politician (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ong"}]}, {"year": "1906", "text": "<PERSON>, American journalist and publisher, co-founded Newsday (d. 1963)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/Newsday\" title=\"Newsday\">Newsday</a></i> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/Newsday\" title=\"Newsday\">Newsday</a></i> (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Newsday", "link": "https://wikipedia.org/wiki/Newsday"}]}, {"year": "1906", "text": "<PERSON>, American singer-songwriter and pianist (d. 1976)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Victoria_Spivey\" title=\"<PERSON> S<PERSON>vey\"><PERSON></a>, American singer-songwriter and pianist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Spivey\" title=\"<PERSON>vey\"><PERSON></a>, American singer-songwriter and pianist (d. 1976)", "links": [{"title": "<PERSON> Spivey", "link": "https://wikipedia.org/wiki/Victoria_Spivey"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American journalist and author (d. 1967)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>arian_Fry\" title=\"Varian Fry\"><PERSON><PERSON></a>, American journalist and author (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>arian_Fry\" title=\"Varian Fry\"><PERSON><PERSON></a>, American journalist and author (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Varian_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American pianist (d. 1967)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Canadian-American economist and diplomat, 7th United States Ambassador to India (d. 2006)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American economist and diplomat, 7th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_India\" class=\"mw-redirect\" title=\"United States Ambassador to India\">United States Ambassador to India</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American economist and diplomat, 7th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_India\" class=\"mw-redirect\" title=\"United States Ambassador to India\">United States Ambassador to India</a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to India", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_India"}]}, {"year": "1909", "text": "<PERSON>, American astronomer and academic (d. 2002)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American journalist (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Japanese-American scholar and diplomat, United States Ambassador to Japan (d. 1990)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American scholar and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Japan\" class=\"mw-redirect\" title=\"United States Ambassador to Japan\">United States Ambassador to Japan</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American scholar and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Japan\" class=\"mw-redirect\" title=\"United States Ambassador to Japan\">United States Ambassador to Japan</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to Japan", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Japan"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American singer and pianist (d. 2007)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and pianist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and pianist (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1913", "text": "<PERSON>, German commander (d. 1945)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCth\" title=\"<PERSON>\"><PERSON></a>, German commander (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCth\" title=\"<PERSON>\"><PERSON></a>, German commander (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wolfgang_L%C3%BCth"}]}, {"year": "1914", "text": "<PERSON>, Afghan king (d. 2007)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Afghan king (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Afghan king (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American trumpet player and bandleader (d. 1950)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Australian author and critic (d. 1997)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Australian author and critic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Australian author and critic (d. 1997)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1917", "text": "<PERSON>, American actress (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American historian and critic (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American historian and critic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American historian and critic (d. 2007)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1917", "text": "<PERSON>, American trombonist and educator (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist and educator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American captain, balloonist, and physicist (d. 1985)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(balloonist)\" title=\"<PERSON> (balloonist)\"><PERSON></a>, American captain, balloonist, and physicist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(balloonist)\" title=\"<PERSON> (balloonist)\"><PERSON></a>, American captain, balloonist, and physicist (d. 1985)", "links": [{"title": "<PERSON> (balloonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(balloonist)"}]}, {"year": "1919", "text": "<PERSON>, American race car driver (d. 1995)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American sportscaster and actor (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and actor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Hong Kong-English actress (d. 1968)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-English actress (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-English actress (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American industrial engineer and wood scientist (d. 1998)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wood_scientist)\" title=\"<PERSON> (wood scientist)\"><PERSON></a>, American industrial engineer and wood scientist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wood_scientist)\" title=\"<PERSON> (wood scientist)\"><PERSON></a>, American industrial engineer and wood scientist (d. 1998)", "links": [{"title": "<PERSON> (wood scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wood_scientist)"}]}, {"year": "1920", "text": "<PERSON>, American author and screenwriter (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Turkish-French director, producer, and screenwriter (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-French director, producer, and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-French director, producer, and screenwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Romanian-Israeli table tennis player (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Israeli table tennis player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Israeli table tennis player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Portuguese author (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>%C3%ADs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>%C3%ADs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese author (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>-Lu%C3%ADs"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Norwegian economist and politician, State Conciliator of Norway (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Preben_Munthe\" title=\"Preben Munthe\"><PERSON><PERSON></a>, Norwegian economist and politician, <a href=\"https://wikipedia.org/wiki/State_Conciliator_of_Norway\" title=\"State Conciliator of Norway\">State Conciliator of Norway</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Preben_Munthe\" title=\"Preben Munthe\"><PERSON><PERSON></a>, Norwegian economist and politician, <a href=\"https://wikipedia.org/wiki/State_Conciliator_of_Norway\" title=\"State Conciliator of Norway\">State Conciliator of Norway</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Preben_<PERSON>he"}, {"title": "State Conciliator of Norway", "link": "https://wikipedia.org/wiki/State_Conciliator_of_Norway"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Italian novelist, short story writer, and journalist (d. 1985)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Italo_Calvino\" title=\"Italo Calvino\"><PERSON><PERSON></a>, Italian novelist, short story writer, and journalist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italo_Calvino\" title=\"Italo Calvino\"><PERSON><PERSON></a>, Italian novelist, short story writer, and journalist (d. 1985)", "links": [{"title": "Italo <PERSON>", "link": "https://wikipedia.org/wiki/Italo_<PERSON>o"}]}, {"year": "1923", "text": "<PERSON>, Spanish journalist and politician (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist and politician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist and politician (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_Font%C3%A1n"}]}, {"year": "1923", "text": "<PERSON>, American journalist and activist (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Australian politician, 40th Premier of Victoria (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 40th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 40th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1924", "text": "<PERSON>, German-Canadian author and educator (d. 2022)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian author and educator (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian author and educator (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American businessman and author (d. 2019)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American director and screenwriter (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American director and screenwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American director and screenwriter (d. 2018)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1925", "text": "<PERSON>, American-French guitarist (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French guitarist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French guitarist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Spanish actress (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Aurora_Bautista\" title=\"Aurora Bautista\"><PERSON></a>, Spanish actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aurora_Bautista\" title=\"Aurora Bautista\"><PERSON></a>, Spanish actress (d. 2012)", "links": [{"title": "Aurora Bautista", "link": "https://wikipedia.org/wiki/Aurora_Bautista"}]}, {"year": "1925", "text": "<PERSON>, English painter and television host (d. 2009)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and television host (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hart\"><PERSON></a>, English painter and television host (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American soldier and diplomat, United States Ambassador to Saudi Arabia (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Saudi_Arabia\" class=\"mw-redirect\" title=\"United States Ambassador to Saudi Arabia\">United States Ambassador to Saudi Arabia</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Saudi_Arabia\" class=\"mw-redirect\" title=\"United States Ambassador to Saudi Arabia\">United States Ambassador to Saudi Arabia</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to Saudi Arabia", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Saudi_Arabia"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish philosopher and poet (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Garc%C3%ADa_Calvo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish philosopher and poet (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Garc%C3%ADa_Calvo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish philosopher and poet (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_Garc%C3%ADa_Calvo"}]}, {"year": "1926", "text": "<PERSON>, French historian and philosopher (d. 1984)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and philosopher (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and philosopher (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American author and screenwriter (d. 2005)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actress (d. 2000)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, German organist and conductor (d. 1981)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, German organist and conductor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, German organist and conductor (d. 1981)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_(conductor)"}]}, {"year": "1927", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian businessman and philanthropist (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/B<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian businessman and philanthropist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian businessman and philanthropist (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American painter and architect (d. 2011)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and architect (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and architect (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "FM-2030, Belgian-Iranian basketball player, philosopher and diplomat (d. 2000)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/FM-2030\" title=\"FM-2030\">FM-2030</a>, Belgian-Iranian basketball player, philosopher and diplomat (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/FM-2030\" title=\"FM-2030\">FM-2030</a>, Belgian-Iranian basketball player, philosopher and diplomat (d. 2000)", "links": [{"title": "FM-2030", "link": "https://wikipedia.org/wiki/FM-2030"}]}, {"year": "1930", "text": "<PERSON>, American politician, 46th Governor of Tennessee (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Tennessee", "link": "https://wikipedia.org/wiki/Governor_of_Tennessee"}]}, {"year": "1931", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Indian engineer, academic, and politician, 11th President of India (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian engineer, academic, and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian engineer, academic, and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1931", "text": "<PERSON>, Baroness <PERSON> of Southwark, English academic and politician", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Southwark\" title=\"<PERSON>, Baroness <PERSON> of Southwark\"><PERSON>, Baroness <PERSON> of Southwark</a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Southwark\" title=\"<PERSON>, Baroness <PERSON> of Southwark\"><PERSON>, Baroness <PERSON> of Southwark</a>, English academic and politician", "links": [{"title": "<PERSON>, Baroness <PERSON> of Southwark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Southwark"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Estonian guitarist and composer (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>aan_R%C3%A4%C3%A4ts\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian guitarist and composer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaan_R%C3%A4%C3%A4ts\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian guitarist and composer (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaan_R%C3%A4%C3%A4ts"}]}, {"year": "1933", "text": "<PERSON>, American drug lord (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drug lord (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drug lord (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English trumpet player (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpet player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpet player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Indian flute player (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian flute player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian flute player (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Scottish boxer (d. 2025)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish boxer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish boxer (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American sprinter (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian ice hockey player, first black player in the National Hockey League (NHL)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ree\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, first black player in the <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a> (NHL)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ree\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, first black player in the <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a> (NHL)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ree"}, {"title": "National Hockey League", "link": "https://wikipedia.org/wiki/National_Hockey_League"}]}, {"year": "1936", "text": "<PERSON>, French actor (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, 3rd Baron <PERSON>, South African-English businessman (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, South African-English businessman (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, South African-English businessman (d. 2019)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actress and singer (d. 2024)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American singer-songwriter and pianist (d. 1993)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marv_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Nigerian musician and activist (d. 1997)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian musician and activist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian musician and activist (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American painter (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>den\"><PERSON><PERSON></a>, American painter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>den\"><PERSON><PERSON></a>, American painter (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B<PERSON>_<PERSON>den"}]}, {"year": "1938", "text": "<PERSON>, American guitarist and songwriter (d. 2008)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blues_musician)\" title=\"<PERSON> (blues musician)\"><PERSON></a>, American guitarist and songwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(blues_musician)\" title=\"<PERSON> (blues musician)\"><PERSON></a>, American guitarist and songwriter (d. 2008)", "links": [{"title": "<PERSON> (blues musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(blues_musician)"}]}, {"year": "1940", "text": "<PERSON>, English rugby league player and coach", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian surgeon and immunologist, Nobel Prize laureate", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian surgeon and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian surgeon and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1941", "text": "<PERSON>, Australian rugby league coach, journalist, and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league coach, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league coach, journalist, and author", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Taiwanese-American painter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese-American painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese-American painter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Jr., American admiral", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American admiral", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American admiral", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and drummer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actress, director, and producer (d. 2018)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Albanian cardiologist and politician, 2nd President of Albania", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian cardiologist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Albania\" title=\"President of Albania\">President of Albania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian cardiologist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Albania\" title=\"President of Albania\">President of Albania</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Albania", "link": "https://wikipedia.org/wiki/President_of_Albania"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Sri Lankan Tamil merchant seaman and politician (d. 2005)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan Tamil merchant seaman and politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan Tamil merchant seaman and politician (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Israeli-American businessman, co-founded Saban Entertainment", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Haim_<PERSON>\" title=\"Haim Saban\"><PERSON><PERSON></a>, Israeli-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Saban_Entertainment\" class=\"mw-redirect\" title=\"Saban Entertainment\">Saban Entertainment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hai<PERSON>_<PERSON>\" title=\"Haim <PERSON>\"><PERSON><PERSON></a>, Israeli-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Saban_Entertainment\" class=\"mw-redirect\" title=\"Saban Entertainment\">Saban Entertainment</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ban"}, {"title": "Saban Entertainment", "link": "https://wikipedia.org/wiki/Saban_Entertainment"}]}, {"year": "1944", "text": "<PERSON>, Northern Irish lawyer and politician, 3rd First Minister of Northern Ireland, Nobel Prize laureate (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland\" class=\"mw-redirect\" title=\"First Minister of Northern Ireland\">First Minister of Northern Ireland</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland\" class=\"mw-redirect\" title=\"First Minister of Northern Ireland\">First Minister of Northern Ireland</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Minister of Northern Ireland", "link": "https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1945", "text": "<PERSON>, Guyanese cricketer (d. 2015)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Spanish cardinal", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Antonio_<PERSON>a%C3%B1iza<PERSON>_Llovera\" title=\"Antonio C<PERSON>ñiza<PERSON> Llover<PERSON>\"><PERSON></a>, Spanish cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antonio_Ca%C3%B1izares_Llovera\" title=\"Antonio C<PERSON>ñiza<PERSON> Llover<PERSON>\"><PERSON></a>, Spanish cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_Ca%C3%B1iza<PERSON>_Llovera"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON> of Bulgaria, Bulgarian patriarch (d. 2024)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Neophyte_of_Bulgaria\" title=\"Neophyte of Bulgaria\"><PERSON><PERSON><PERSON> of Bulgaria</a>, Bulgarian patriarch (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Neophyte_of_Bulgaria\" title=\"Neophyte of Bulgaria\"><PERSON><PERSON><PERSON> of Bulgaria</a>, Bulgarian patriarch (d. 2024)", "links": [{"title": "Neophyte of Bulgaria", "link": "https://wikipedia.org/wiki/Neophyte_of_Bulgaria"}]}, {"year": "1945", "text": "<PERSON>, American baseball player and sportscaster", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Indian actor and director", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Swedish bassist and composer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish bassist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish bassist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Scottish engineer and politician, Minister for Environment and Climate Change", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Environment_and_Climate_Change\" class=\"mw-redirect\" title=\"Minister for Environment and Climate Change\">Minister for Environment and Climate Change</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Environment_and_Climate_Change\" class=\"mw-redirect\" title=\"Minister for Environment and Climate Change\">Minister for Environment and Climate Change</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Environment and Climate Change", "link": "https://wikipedia.org/wiki/Minister_for_Environment_and_Climate_Change"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish singer-songwriter and actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/H%C3%BCmeyra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%BCmeyra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter and actress", "links": [{"title": "Hümeyra", "link": "https://wikipedia.org/wiki/H%C3%BCmeyra"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Czech drummer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_%C5%A0ediv%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_%C5%A0ediv%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech drummer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_%C5%A0ediv%C3%BD"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Filipino lawyer and jurist, 23rd Chief Justice of the Supreme Court of the Philippines (d. 2016)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ren<PERSON> Corona\"><PERSON><PERSON></a>, Filipino lawyer and jurist, 23rd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines\" class=\"mw-redirect\" title=\"Chief Justice of the Supreme Court of the Philippines\">Chief Justice of the Supreme Court of the Philippines</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>rona\"><PERSON><PERSON></a>, Filipino lawyer and jurist, 23rd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines\" class=\"mw-redirect\" title=\"Chief Justice of the Supreme Court of the Philippines\">Chief Justice of the Supreme Court of the Philippines</a> (d. 2016)", "links": [{"title": "Renato Corona", "link": "https://wikipedia.org/wiki/Renato_Corona"}, {"title": "Chief Justice of the Supreme Court of the Philippines", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines"}]}, {"year": "1948", "text": "<PERSON>, British-Irish singer-songwriter and pianist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Irish singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Irish singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Indian journalist, economist, and broadcaster, founded NDTV", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist, economist, and broadcaster, founded <a href=\"https://wikipedia.org/wiki/NDTV\" title=\"NDTV\">NDTV</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist, economist, and broadcaster, founded <a href=\"https://wikipedia.org/wiki/NDTV\" title=\"NDTV\">NDTV</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "NDTV", "link": "https://wikipedia.org/wiki/NDTV"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, American porn actress, director, and producer (d. 2015)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>di<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>di<PERSON>\"><PERSON><PERSON><PERSON></a>, American porn actress, director, and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>di<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>di<PERSON>\"><PERSON><PERSON><PERSON></a>, American porn actress, director, and producer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Candida_<PERSON>le"}]}, {"year": "1951", "text": "<PERSON>, English actor, director, and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_director)\" class=\"mw-redirect\" title=\"<PERSON> (English director)\"><PERSON></a>, English actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(English_director)\" class=\"mw-redirect\" title=\"<PERSON> (English director)\"><PERSON></a>, English actor, director, and screenwriter", "links": [{"title": "<PERSON> (English director)", "link": "https://wikipedia.org/wiki/<PERSON>_(English_director)"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American tennis player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Armenian chess player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian skier", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2024)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor and comedian", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_(comedian)"}]}, {"year": "1953", "text": "<PERSON>, English conductor and musicologist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor and musicologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor and musicologist", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_(conductor)"}]}, {"year": "1954", "text": "<PERSON>, Australian poet and educator", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian politician, 44th Premier of Victoria", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 44th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 44th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English physicist and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Indian field hockey player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English author and illustrator", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actress (d. 2021)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>-<PERSON>, Scottish actor, director, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Indian-American actress, director, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nair\"><PERSON></a>, Indian-American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American skateboarder, director, producer, and businessman, co-founded <PERSON>", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder, director, producer, and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder, director, producer, and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English-French journalist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English-French journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English-French journalist and author", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>(writer)"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, American chef and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Em<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American chef and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emeril_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English keyboard player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Duchess of York", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_York\" title=\"<PERSON>, Duchess of York\"><PERSON>, Duchess of York</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_York\" title=\"<PERSON>, Duchess of York\"><PERSON>, Duchess of York</a>", "links": [{"title": "<PERSON>, Duchess of York", "link": "https://wikipedia.org/wiki/<PERSON>,_Duchess_of_York"}]}, {"year": "1959", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Dutch footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Italian colonel, pilot, and astronaut", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian colonel, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, German bodybuilder and trainer (d. 2013)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>sser <PERSON>\"><PERSON><PERSON></a>, German bodybuilder and trainer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>sser <PERSON>\"><PERSON><PERSON></a>, German bodybuilder and trainer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9t\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9t\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9t"}]}, {"year": "1966", "text": "<PERSON>, Mexican footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American pianist and composer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Dutch singer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English drummer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American politician, 34th Lieutenant Governor of North Carolina", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Dan_Forest\" title=\"Dan Forest\"><PERSON></a>, American politician, 34th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_North_Carolina\" title=\"Lieutenant Governor of North Carolina\">Lieutenant Governor of North Carolina</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dan_Forest\" title=\"Dan Forest\"><PERSON></a>, American politician, 34th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_North_Carolina\" title=\"Lieutenant Governor of North Carolina\">Lieutenant Governor of North Carolina</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dan_Forest"}, {"title": "Lieutenant Governor of North Carolina", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_North_Carolina"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, German actor and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actor and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6tz_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, French footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian rugby league player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/V%C3%ADtor_Ba%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%ADtor_Ba%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%ADtor_Ba%C3%ADa"}]}, {"year": "1969", "text": "<PERSON>, English actor and director", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dominic_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, American singer-songwriter, dancer, and actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Ginuwine\" title=\"Ginuwine\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ginuwine\" title=\"Ginuwine\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter, dancer, and actor", "links": [{"title": "Ginuwine", "link": "https://wikipedia.org/wiki/Ginuwine"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Swedish skier", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>iberg\" title=\"<PERSON><PERSON><PERSON>iber<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>iberg\" title=\"<PERSON><PERSON><PERSON>iber<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wiberg"}]}, {"year": "1971", "text": "<PERSON>, American wrestler", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joey Abs\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_A<PERSON>\" title=\"Joey Abs\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>bs"}]}, {"year": "1971", "text": "<PERSON>, English footballer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Estonian author and translator", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and translator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American basketball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, German-Congolese footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Mich%C3%A9l_Mazingu-Dinzey\" title=\"<PERSON><PERSON><PERSON><PERSON> Mazingu-Dinzey\"><PERSON><PERSON><PERSON><PERSON></a>, German-Congolese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mich%C3%A9l_Mazingu-Dinzey\" title=\"<PERSON><PERSON><PERSON><PERSON> Mazingu-Dinzey\"><PERSON><PERSON><PERSON><PERSON></a>, German-Congolese footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mich%C3%A9l_<PERSON><PERSON><PERSON>-Dinzey"}]}, {"year": "1973", "text": "<PERSON>, Russian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Swedish golfer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/%C3%96mer_%C3%87atk%C4%B1%C3%A7\" title=\"<PERSON>mer Çatkıç\"><PERSON><PERSON>kıç</a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96mer_%C3%87atk%C4%B1%C3%A7\" title=\"<PERSON><PERSON> Çatkıç\"><PERSON><PERSON>kıç</a>, Turkish footballer", "links": [{"title": "<PERSON>mer <PERSON>", "link": "https://wikipedia.org/wiki/%C3%96mer_%C3%87atk%C4%B1%C3%A7"}]}, {"year": "1974", "text": "<PERSON>, Brazilian actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Italian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Little\" title=\"Glen Little\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Little\" title=\"Glen Little\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glen_Little"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Spanish basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Italian rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Japanese racing driver", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Ecuadorian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Patric<PERSON>_Urrutia\" title=\"<PERSON><PERSON><PERSON>rr<PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pat<PERSON><PERSON>_Urrutia\" title=\"<PERSON><PERSON><PERSON>rruti<PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patric<PERSON>_Urrutia"}]}, {"year": "1978", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Devon_Gummersall\" class=\"mw-redirect\" title=\"Devon Gummersall\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Devon_Gummersall\" class=\"mw-redirect\" title=\"Devon Gummersall\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "Devon Gummersall", "link": "https://wikipedia.org/wiki/Devon_Gummersall"}]}, {"year": "1978", "text": "<PERSON>, American politician, author, nonprofit executive, and television producer, 63rd Governor of Maryland", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, author, nonprofit executive, and television producer, 63rd <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, author, nonprofit executive, and television producer, 63rd <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Maryland", "link": "https://wikipedia.org/wiki/Governor_of_Maryland"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Blue_Adams\" title=\"Blue Adams\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Blue_Adams\" title=\"Blue Adams\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Adams"}]}, {"year": "1979", "text": "Bohemia, Pakistani-American rapper and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Bohemia_(rapper)\" title=\"Bohemia (rapper)\">Bohemia</a>, Pakistani-American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bohemia_(rapper)\" title=\"Bohemia (rapper)\">Bohemia</a>, Pakistani-American rapper and producer", "links": [{"title": "Bohemia (rapper)", "link": "https://wikipedia.org/wiki/Bohemia_(rapper)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Estonian figure skater", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Latvian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/M%C4%81ris_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C4%81<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C4%81<PERSON>_<PERSON>s"}]}, {"year": "1980", "text": "<PERSON>, Belgian cyclist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cole\" title=\"Keys<PERSON> Cole\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cole\" title=\"Keys<PERSON> Cole\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Keys<PERSON>_Cole"}]}, {"year": "1981", "text": "<PERSON>, Russian tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Hong Kong singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Brazilian ex-F1 Driver ", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian ex-F1 Driver ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian ex-F1 Driver ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Dutch footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Uruguayan footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3pez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3pez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3pez"}]}, {"year": "1985", "text": "<PERSON>, Spanish racing driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Marcos_Mart%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Spanish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marcos_Mart%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Spanish racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/Marcos_Mart%C3%<PERSON><PERSON><PERSON>_(racing_driver)"}]}, {"year": "1986", "text": "<PERSON>, South Korean singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Swiss skier", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Nolito\" title=\"Nolito\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nolito\" title=\"Nolit<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nolito"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Estonian racing driver", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ott_T%C3%A4nak\" title=\"<PERSON>tt Tänak\"><PERSON><PERSON></a>, Estonian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ott_T%C3%A4nak\" title=\"<PERSON>tt Täna<PERSON>\"><PERSON><PERSON></a>, Estonian racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ott_T%C3%A4nak"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Congolese writer and politician", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese writer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese writer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Mesut_%C3%96zil\" title=\"Mesut Özil\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mesut_%C3%96zil\" title=\"Mesut Özil\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "Me<PERSON>t <PERSON>", "link": "https://wikipedia.org/wiki/Mesut_%C3%96zil"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, British professional boxer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British professional boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British professional boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Argentinian-Italian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Lea<PERSON><PERSON>_<PERSON>_Mart%C3%ADnez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian-Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lea<PERSON><PERSON>_<PERSON>_Mart%C3%ADnez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian-Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leandro_Antonio_Mart%C3%ADnez"}]}, {"year": "1990", "text": "<PERSON><PERSON>, South Korean singer-songwriter and dancer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-yoon\" title=\"<PERSON><PERSON> Ji-yoon\"><PERSON><PERSON></a>, South Korean singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-yoon\" title=\"<PERSON><PERSON>-yoon\"><PERSON><PERSON></a>, South Korean singer-songwriter and dancer", "links": [{"title": "<PERSON><PERSON>yoon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-yoon"}]}, {"year": "1991", "text": "<PERSON>, American ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON> <PERSON>, Dutch rapper ", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>%27_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Dutch rapper ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%27_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Dutch rapper ", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Lil%27_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Rwandan-Scottish actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Ncuti_Gatwa\" title=\"Ncuti Gatwa\"><PERSON><PERSON><PERSON></a>, Rwandan-Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ncuti_Gatwa\" title=\"Ncuti Gatwa\"><PERSON><PERSON><PERSON></a>, Rwandan-Scottish actor", "links": [{"title": "Ncuti Gatwa", "link": "https://wikipedia.org/wiki/Ncuti_Gatwa"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hern%C3%A1ndez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hern%C3%A1ndez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teoscar_Hern%C3%A1ndez"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Pakistani cricket player ", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Azam\"><PERSON><PERSON></a>, Pakistani cricket player ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Azam\"><PERSON><PERSON></a>, Pakistani cricket player ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American baseball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Austrian basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ltl\" title=\"<PERSON>\"><PERSON></a>, Austrian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ltl\" title=\"<PERSON>\"><PERSON></a>, Austrian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jakob_P%C3%B6ltl"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1996)\" title=\"<PERSON><PERSON> (footballer, born 1996)\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1996)\" title=\"<PERSON><PERSON> (footballer, born 1996)\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1996)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1996)"}]}, {"year": "1996", "text": "<PERSON>, American actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, South Korean rapper and dancer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean rapper and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean rapper and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Indonesian activist and politician", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>iza_<PERSON>_<PERSON>nandar\" title=\"Teuku Wariza <PERSON> Mu<PERSON>\"><PERSON><PERSON></a>, Indonesian activist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Teuku Wariza <PERSON>\"><PERSON><PERSON></a>, Indonesian activist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Aris_Munandar"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American-Canadian actress and singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Madison\"><PERSON><PERSON></a>, American-Canadian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Madison\"><PERSON><PERSON></a>, American-Canadian actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Indonesian activist and sex offender", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian activist and <a href=\"https://wikipedia.org/wiki/Sex_offender\" title=\"Sex offender\">sex offender</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian activist and <a href=\"https://wikipedia.org/wiki/Sex_offender\" title=\"Sex offender\">sex offender</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Sex offender", "link": "https://wikipedia.org/wiki/Sex_offender"}]}, {"year": "2005", "text": "<PERSON>, Crown Prince of Denmark", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Crown_Prince_of_Denmark\" title=\"<PERSON>, Crown Prince of Denmark\"><PERSON>, Crown Prince of Denmark</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Crown_Prince_of_Denmark\" title=\"<PERSON>, Crown Prince of Denmark\"><PERSON>, Crown Prince of Denmark</a>", "links": [{"title": "<PERSON>, Crown Prince of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>,_Crown_Prince_of_Denmark"}]}], "Deaths": [{"year": "412", "text": "<PERSON><PERSON><PERSON>, Patriarch of Alexandria", "html": "412 - <a href=\"https://wikipedia.org/wiki/Theophilus_I_of_Alexandria\" title=\"Theophilus I of Alexandria\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Patriarch_of_Alexandria\" title=\"Patriarch of Alexandria\">Patriarch of Alexandria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Theophi<PERSON>_<PERSON>_of_Alexandria\" title=\"Theophilus I of Alexandria\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Patriarch_of_Alexandria\" title=\"Patriarch of Alexandria\">Patriarch of Alexandria</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/Theophilus_I_of_Alexandria"}, {"title": "Patriarch of Alexandria", "link": "https://wikipedia.org/wiki/Patriarch_of_Alexandria"}]}, {"year": "892", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> caliph", "html": "892 - <a href=\"https://wikipedia.org/wiki/Al-Mu%27tamid\" title=\"Al-Mu'tamid\"><PERSON><PERSON><PERSON>mi<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Caliphate\" title=\"Caliphate\">caliph</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Mu%27tamid\" title=\"Al-Mu'tamid\"><PERSON><PERSON><PERSON>tami<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Caliphate\" title=\"Caliphate\">caliph</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Mu%27tamid"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abbasid_Caliphate"}, {"title": "Caliphate", "link": "https://wikipedia.org/wiki/Caliphate"}]}, {"year": "898", "text": "<PERSON> of Italy (b. 880)", "html": "898 - <a href=\"https://wikipedia.org/wiki/Lambert_of_Italy\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a> (b. 880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lambert_of_Italy\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a> (b. 880)", "links": [{"title": "Lambert of Italy", "link": "https://wikipedia.org/wiki/Lambert_of_Italy"}]}, {"year": "912", "text": "<PERSON> ibn <PERSON>, Spanish emir (b. 844)", "html": "912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Spanish emir (b. 844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Spanish emir (b. 844)", "links": [{"title": "<PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "925", "text": "<PERSON><PERSON><PERSON>, Persian polymath (b. 864)", "html": "925 - <a href=\"https://wikipedia.org/wiki/Rhazes\" class=\"mw-redirect\" title=\"Rhazes\"><PERSON><PERSON><PERSON></a>, Persian polymath (b. 864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rhazes\" class=\"mw-redirect\" title=\"Rhazes\"><PERSON><PERSON><PERSON></a>, Persian polymath (b. 864)", "links": [{"title": "R<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rhazes"}]}, {"year": "961", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> III, caliph of Córdoba", "html": "961 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_III\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON><PERSON> III</a>, caliph of Córdoba", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_III\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON><PERSON> III</a>, caliph of Córdoba", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1002", "text": "<PERSON><PERSON><PERSON>, Duke of Burgundy (b. 946)", "html": "1002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Burgundy\" title=\"Duke of Burgundy\">Duke of Burgundy</a> (b. 946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Burgundy\" title=\"Duke of Burgundy\">Duke of Burgundy</a> (b. 946)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}, {"title": "Duke of Burgundy", "link": "https://wikipedia.org/wiki/Duke_of_Burgundy"}]}, {"year": "1080", "text": "<PERSON> of Rheinfelden (b. 1025)", "html": "1080 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rheinfelden\" title=\"<PERSON> of Rheinfelden\"><PERSON> of Rheinfelden</a> (b. 1025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rheinfelden\" title=\"<PERSON> of Rheinfelden\"><PERSON> of Rheinfelden</a> (b. 1025)", "links": [{"title": "<PERSON> of Rheinfelden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rheinfelden"}]}, {"year": "1173", "text": "<PERSON><PERSON><PERSON> of Aragon (b. 1135)", "html": "1173 - <a href=\"https://wikipedia.org/wiki/Petronilla_of_Aragon\" title=\"Petronilla of Aragon\">Petronilla of Aragon</a> (b. 1135)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petronilla_of_Aragon\" title=\"Petronilla of Aragon\">Petronilla of Aragon</a> (b. 1135)", "links": [{"title": "Petronilla of Aragon", "link": "https://wikipedia.org/wiki/Petronilla_of_Aragon"}]}, {"year": "1240", "text": "<PERSON><PERSON>, sultan of Delhi (b. 1205)", "html": "1240 - <a href=\"https://wikipedia.org/wiki/Razia_Sultana\" title=\"Razia Sultana\"><PERSON><PERSON> Sultana</a>, sultan of <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a> (b. 1205)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Razia_Sultana\" title=\"Razia Sultana\"><PERSON><PERSON> Sultana</a>, sultan of <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a> (b. 1205)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Razia_Sultana"}, {"title": "Delhi", "link": "https://wikipedia.org/wiki/Delhi"}]}, {"year": "1243", "text": "<PERSON><PERSON><PERSON> of Silesia, Polish saint (b. 1174)", "html": "1243 - <a href=\"https://wikipedia.org/wiki/Hedwig_of_Silesia\" title=\"<PERSON><PERSON><PERSON> of Silesia\"><PERSON><PERSON><PERSON> of Silesia</a>, Polish saint (b. 1174)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hedwig_of_Silesia\" title=\"<PERSON><PERSON><PERSON> of Silesia\"><PERSON><PERSON><PERSON> of Silesia</a>, Polish saint (b. 1174)", "links": [{"title": "<PERSON><PERSON><PERSON> of Silesia", "link": "https://wikipedia.org/wiki/Hedwig_of_Silesia"}]}, {"year": "1326", "text": "<PERSON>, bishop and Lord High Treasurer of England, and his brother Sir <PERSON>, judge and politician.", "html": "1326 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop and <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> of England, and his brother <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, judge and politician.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop and <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> of England, and his brother <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, judge and politician.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1385", "text": "<PERSON><PERSON><PERSON>, Metropolitan of Moscow", "html": "1385 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Metropolitan_of_Moscow\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Metropolitan of Moscow\"><PERSON><PERSON><PERSON>, Metropolitan of Moscow</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Metropolitan_of_Moscow\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Metropolitan of Moscow\"><PERSON><PERSON><PERSON>, Metropolitan of Moscow</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, Metropolitan of Moscow", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Metropolitan_of_Moscow"}]}, {"year": "1389", "text": "<PERSON> (b. 1318)", "html": "1389 - <a href=\"https://wikipedia.org/wiki/Pope_Urban_VI\" title=\"Pope Urban VI\"><PERSON> Urban VI</a> (b. 1318)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Urban_VI\" title=\"Pope Urban VI\"><PERSON> VI</a> (b. 1318)", "links": [{"title": "<PERSON> VI", "link": "https://wikipedia.org/wiki/Pope_Urban_VI"}]}, {"year": "1404", "text": "<PERSON>, French princess (b. 1344)", "html": "1404 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Val<PERSON>_(1344-1404)\" class=\"mw-redirect\" title=\"<PERSON> of Valois (1344-1404)\"><PERSON></a>, French princess (b. 1344)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(1344-1404)\" class=\"mw-redirect\" title=\"<PERSON> of Valois (1344-1404)\"><PERSON></a>, French princess (b. 1344)", "links": [{"title": "<PERSON> of Valois (1344-1404)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(1344-1404)"}]}, {"year": "1496", "text": "<PERSON>, Count of Montpensier (b. 1443)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Montpensier\" title=\"<PERSON>, Count of Montpensier\"><PERSON>, Count of Montpensier</a> (b. 1443)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Montpensier\" title=\"<PERSON>, Count of Montpensier\"><PERSON>, Count of Montpensier</a> (b. 1443)", "links": [{"title": "<PERSON>, Count of Montpensier", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1564", "text": "<PERSON>, Belgian-Greek anatomist, physician, and author (b. 1514)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-Greek anatomist, physician, and author (b. 1514)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-Greek anatomist, physician, and author (b. 1514)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1674", "text": "<PERSON>, English poet (b. 1591)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet (b. 1591)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1684", "text": "<PERSON><PERSON><PERSON>, French historian, philosopher and lawyer (b. 1626)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>_Cordemoy\" title=\"<PERSON><PERSON><PERSON> Cordemoy\"><PERSON><PERSON><PERSON></a>, French historian, philosopher and lawyer (b. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>rdemoy\" title=\"<PERSON><PERSON><PERSON> Cordemoy\"><PERSON><PERSON><PERSON></a>, French historian, philosopher and lawyer (b. 1626)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9ra<PERSON>_<PERSON>_<PERSON>rdemoy"}]}, {"year": "1690", "text": "<PERSON>, Spanish painter and illustrator (b. 1622)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9s_Leal\" title=\"<PERSON>\"><PERSON></a>, Spanish painter and illustrator (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9s_Leal\" title=\"<PERSON>d<PERSON>\"><PERSON></a>, Spanish painter and illustrator (b. 1622)", "links": [{"title": "<PERSON> Vald<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9s_Leal"}]}, {"year": "1715", "text": "<PERSON><PERSON><PERSON><PERSON>, English mathematician and  philosopher (b. 1675)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English mathematician and philosopher (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English mathematician and philosopher (b. 1675)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, Scottish-Russian admiral (b. 1735)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Russian admiral (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Russian admiral (b. 1735)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, American captain and judge (b. 1755)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and judge (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and judge (b. 1755)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, English painter and politician (b. 1735)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>Holland\"><PERSON></a>, English painter and politician (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dance-Holland\"><PERSON></a>, English painter and politician (b. 1735)", "links": [{"title": "<PERSON>-Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON>, Polish-Lithuanian general and engineer (b. 1746)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Tad<PERSON><PERSON>_Ko%C5%9B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Lithuanian general and engineer (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%9B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Lithuanian general and engineer (b. 1746)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadeusz_Ko%C5%9B<PERSON><PERSON><PERSON>"}]}, {"year": "1819", "text": "<PERSON>, Russian general and politician, War Governor of Saint Petersburg (b. 1744)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, <a href=\"https://wikipedia.org/wiki/Saint_Petersburg_Governorate\" title=\"Saint Petersburg Governorate\">War Governor of Saint Petersburg</a> (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, <a href=\"https://wikipedia.org/wiki/Saint_Petersburg_Governorate\" title=\"Saint Petersburg Governorate\">War Governor of Saint Petersburg</a> (b. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Saint Petersburg Governorate", "link": "https://wikipedia.org/wiki/Saint_Petersburg_Governorate"}]}, {"year": "1820", "text": "<PERSON>, Prince of Schwarzenberg (b. 1771)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Schwarzenberg\" title=\"<PERSON>, Prince of Schwarzenberg\"><PERSON>, Prince of Schwarzenberg</a> (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Schwarzenberg\" title=\"<PERSON>, Prince of Schwarzenberg\"><PERSON>, Prince of Schwarzenberg</a> (b. 1771)", "links": [{"title": "<PERSON>, Prince of Schwarzenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_<PERSON><PERSON>"}]}, {"year": "1837", "text": "<PERSON>, Russian poet and politician, Russian Minister of Justice (b. 1760)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Justice_of_Imperial_Russia\" class=\"mw-redirect\" title=\"List of Ministers of Justice of Imperial Russia\">Russian Minister of Justice</a> (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Justice_of_Imperial_Russia\" class=\"mw-redirect\" title=\"List of Ministers of Justice of Imperial Russia\">Russian Minister of Justice</a> (b. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Ministers of Justice of Imperial Russia", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Justice_of_Imperial_Russia"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON>, English poet and novelist (b. 1802)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Letitia_<PERSON>_<PERSON>\" title=\"Letiti<PERSON>\">Letiti<PERSON></a>, English poet and novelist (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>_<PERSON>\" title=\"Letiti<PERSON>\">Letitia <PERSON></a>, English poet and novelist (b. 1802)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, English author and songwriter (b. 1837)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%A0_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and songwriter (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%A0_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and songwriter (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%A0_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech pianist and composer (b. 1850)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Zden%C4%9Bk_<PERSON>h\" title=\"<PERSON>den<PERSON><PERSON> Fi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech pianist and composer (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zden%C4%9Bk_<PERSON>\" title=\"<PERSON>den<PERSON><PERSON> Fi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech pianist and composer (b. 1850)", "links": [{"title": "Zdeněk Fi<PERSON>", "link": "https://wikipedia.org/wiki/Zden%C4%9Bk_Fi<PERSON>h"}]}, {"year": "1910", "text": "<PERSON>, American boxer (b. 1886)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Dutch dancer and spy (b. 1876)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mata Hari\"><PERSON></a>, Dutch dancer and spy (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mata Hari\"><PERSON></a>, Dutch dancer and spy (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mata_Hari"}]}, {"year": "1918", "text": "<PERSON> of Shirdi, Indian guru and saint (b. 1838)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Shirdi\" title=\"<PERSON> of Shirdi\"><PERSON> of Shirdi</a>, Indian guru and saint (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Shirdi\" title=\"<PERSON> of Shirdi\"><PERSON> of Shirdi</a>, Indian guru and saint (b. 1838)", "links": [{"title": "Sai Baba of Shirdi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Shirdi"}]}, {"year": "1925", "text": "<PERSON>, Mexican revolutionary (b. 1848?)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nez_y_Muro\" title=\"<PERSON>\"><PERSON></a>, Mexican revolutionary (b. 1848?)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nez_y_Muro\" title=\"<PERSON>\"><PERSON></a>, Mexican revolutionary (b. 1848?)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nez_y_Muro"}]}, {"year": "1930", "text": "<PERSON>, Canadian-American businessman, founded the Dow Chemical Company (b. 1866)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman, founded the <a href=\"https://wikipedia.org/wiki/Dow_Chemical_Company\" title=\"Dow Chemical Company\">Dow Chemical Company</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman, founded the <a href=\"https://wikipedia.org/wiki/Dow_Chemical_Company\" title=\"Dow Chemical Company\">Dow Chemical Company</a> (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Dow Chemical Company", "link": "https://wikipedia.org/wiki/Dow_Chemical_Company"}]}, {"year": "1934", "text": "<PERSON>, French lawyer and politician, 10th President of France (b. 1860)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Raymond_Poincar%C3%A9"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Catalan lawyer and politician, President of Catalonia (b. 1882)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Llu%C3%ADs_Companys\" title=\"Lluís Companys\"><PERSON>luís Companys</a>, Catalan lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Catalonia\" class=\"mw-redirect\" title=\"President of Catalonia\">President of Catalonia</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Llu%C3%ADs_Companys\" title=\"Lluís Companys\">Lluís Companys</a>, Catalan lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Catalonia\" class=\"mw-redirect\" title=\"President of Catalonia\">President of Catalonia</a> (b. 1882)", "links": [{"title": "Lluís Companys", "link": "https://wikipedia.org/wiki/Llu%C3%ADs_Companys"}, {"title": "President of Catalonia", "link": "https://wikipedia.org/wiki/President_of_Catalonia"}]}, {"year": "1945", "text": "<PERSON>, French lawyer and politician, 101st Prime Minister of France (b. 1883)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 101st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 101st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1946", "text": "<PERSON>, German general and politician (b. 1893)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ring\" title=\"<PERSON>\"><PERSON></a>, German general and politician (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ring\" title=\"<PERSON>\"><PERSON></a>, German general and politician (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hermann_G%C3%B6ring"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1863)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1863)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Japanese composer (b. 1914)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fu<PERSON> Hayasaka\"><PERSON><PERSON></a>, Japanese composer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fu<PERSON> Hayasaka\"><PERSON><PERSON></a>, Japanese composer (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Turkish poet and author (b. 1907)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t_%C3%87elebi\" title=\"<PERSON><PERSON> Çelebi\"><PERSON><PERSON></a>, Turkish poet and author (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t_%C3%87elebi\" title=\"<PERSON><PERSON> Çelebi\"><PERSON><PERSON> <PERSON><PERSON></a>, Turkish poet and author (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asaf_Halet_%C3%87elebi"}]}, {"year": "1958", "text": "<PERSON>, British geologist, academic, and physicist (b. 1908)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, British geologist, academic, and physicist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, British geologist, academic, and physicist (b. 1908)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Ukrainian soldier and politician (b. 1909)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian soldier and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian soldier and politician (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>era"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian mathematician and academic (b. 1880)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Lip%C3%B3t_Fej%C3%A9r\" title=\"<PERSON><PERSON><PERSON><PERSON>jér\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian mathematician and academic (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lip%C3%B3t_Fej%C3%A9r\" title=\"<PERSON><PERSON><PERSON><PERSON> Fejér\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian mathematician and academic (b. 1880)", "links": [{"title": "Lip<PERSON>t Fejér", "link": "https://wikipedia.org/wiki/Lip%C3%B3t_Fej%C3%A9r"}]}, {"year": "1960", "text": "<PERSON>, American actress and producer (b. 1890)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON>', Indian poet and author (b. 1896)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Suryakant_Tripathi_%27Nirala%27\" class=\"mw-redirect\" title=\"<PERSON>yakan<PERSON>athi 'Nirala'\"><PERSON><PERSON><PERSON><PERSON> '<PERSON>'</a>, Indian poet and author (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suryakant_Tripathi_%27Nirala%27\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> 'Nirala'\"><PERSON><PERSON><PERSON><PERSON> '<PERSON>'</a>, Indian poet and author (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON>'", "link": "https://wikipedia.org/wiki/Suryakant_Tripathi_%27Nirala%27"}]}, {"year": "1963", "text": "<PERSON>, American golfer and captain (b. 1908)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and captain (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and captain (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American composer and songwriter (b. 1891)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Porter\"><PERSON></a>, American composer and songwriter (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Porter\"><PERSON></a>, American composer and songwriter (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German-Israeli mathematician and academic (b. 1891)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli mathematician and academic (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli mathematician and academic (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, 1st Baron <PERSON>, English lieutenant and politician (b. 1876)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English lieutenant and politician (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English lieutenant and politician (b. 1876)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American author and illustrator (b. 1909)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Virginia_Lee_<PERSON>\" title=\"Virginia Lee Burton\"><PERSON></a>, American author and illustrator (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Lee_<PERSON>\" title=\"Virginia Lee Burton\"><PERSON></a>, American author and illustrator (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virginia_Lee_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Italian-American mob boss (b. 1902)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American photojournalist (b. 1918)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American photojournalist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American photojournalist (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Norwegian businessman (b. 1899)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian businessman (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian businessman (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Russian physicist and mathematician (b. 1900)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and mathematician (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and mathematician (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer and volleyball player (b. 1896)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(athlete)\" title=\"<PERSON><PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer and volleyball player (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(athlete)\" title=\"<PERSON><PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer and volleyball player (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_(athlete)"}]}, {"year": "1983", "text": "<PERSON>, American actor (b. 1899)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1899)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/Pat_<PERSON>%27B<PERSON>_(actor)"}]}, {"year": "1987", "text": "<PERSON>, Burkinabe captain and politician, 5th President of Burkina Faso (b. 1949)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burkinabe captain and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Burkina_Faso\" class=\"mw-redirect\" title=\"President of Burkina Faso\">President of Burkina Faso</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burkinabe captain and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Burkina_Faso\" class=\"mw-redirect\" title=\"President of Burkina Faso\">President of Burkina Faso</a> (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Burkina Faso", "link": "https://wikipedia.org/wiki/President_of_Burkina_Faso"}]}, {"year": "1987", "text": "<PERSON>, American author and poet (b. 1908)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, English composer, music critic, pianist and writer (b. 1892)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Kai<PERSON><PERSON><PERSON>_<PERSON>ji_Sorabji\" title=\"<PERSON><PERSON><PERSON><PERSON>ji Sorabji\"><PERSON><PERSON><PERSON><PERSON></a>, English composer, music critic, pianist and writer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ji_Sorabji\" title=\"<PERSON><PERSON><PERSON><PERSON> Sorabji\"><PERSON><PERSON><PERSON><PERSON></a>, English composer, music critic, pianist and writer (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>ji"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Serbian novelist, short story writer, essayist and translator. (b. 1935)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian novelist, short story writer, essayist and translator. (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian novelist, short story writer, essayist and translator. (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Danilo_Ki%C5%A1"}]}, {"year": "1990", "text": "<PERSON><PERSON>, French actress and director (b. 1932)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress and director (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress and director (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish historian and academic (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Ayd%C4%B1n_Say%C4%B1l%C4%B1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish historian and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ayd%C4%B1n_Say%C4%B1l%C4%B1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish historian and academic (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>n <PERSON>", "link": "https://wikipedia.org/wiki/Ayd%C4%B1n_Say%C4%B1l%C4%B1"}]}, {"year": "1994", "text": "<PERSON>, French philosopher and academic (b. 1934)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Swedish ice hockey player (b. 1967)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Bengt_%C3%85kerblom\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ben<PERSON>_%C3%85kerblom\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player (b. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bengt_%C3%85kerblom"}]}, {"year": "1995", "text": "<PERSON>, Brazilian racing driver, only driver ever killed in the International Formula 3000 series (b. 1976)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver, only driver ever killed in the International Formula 3000 series (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver, only driver ever killed in the International Formula 3000 series (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, British-Irish soldier, policeman, tenor and actor (b. 1917)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Irish soldier, policeman, tenor and actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Irish soldier, policeman, tenor and actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Polish-American biochemist and academic, Nobel Prize laureate (b. 1912)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2000", "text": "<PERSON>, American journalist and critic (b. 1924)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Chinese general and warlord (b. 1901)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and warlord (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and warlord (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Canadian journalist and activist (b. 1919)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and activist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and activist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Danish poet (b. 1928)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Per_H%C3%B8jholt\" title=\"<PERSON>\"><PERSON></a>, Danish poet (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Per_H%C3%B8jholt\" title=\"<PERSON>\"><PERSON></a>, Danish poet (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Per_H%C3%B8jholt"}]}, {"year": "2005", "text": "<PERSON>, American basketball player (b. 1977)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Finnish lawyer and politician (b. 1945)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish lawyer and politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish lawyer and politician (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Dutch jurist and politician (b. 1933)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bo<PERSON>\"><PERSON><PERSON></a>, Dutch jurist and politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch jurist and politician (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Piet_Boukema"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American actress and singer (b. 1927)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish soldier and poet (b. 1914)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Faz%C4%B1l_H%C3%BCsn%C3%BC_Da%C4%9Flarca\" title=\"Fazıl Hüsnü Dağlarca\"><PERSON>az<PERSON><PERSON> Hüsnü Dağlarca</a>, Turkish soldier and poet (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Faz%C4%B1l_H%C3%BCsn%C3%BC_Da%C4%9Flarca\" title=\"Fazıl Hüsnü Dağlarca\"><PERSON><PERSON><PERSON><PERSON> Hüsnü Dağlarca</a>, Turkish soldier and poet (b. 1914)", "links": [{"title": "Fazıl Hüsnü Dağlarca", "link": "https://wikipedia.org/wiki/Faz%C4%B1l_H%C3%BCsn%C3%BC_Da%C4%9Flarca"}]}, {"year": "2008", "text": "<PERSON>, American game show host and announcer (b. 1922)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and announcer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and announcer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Dutch-German footballer (b. 1939)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-German footballer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-German footballer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American photographer (b. 1912)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American physician and activist (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and activist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and activist (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actor (b. 1931)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English actress, singer, and author (b. 1920)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Driver\" title=\"Betty Driver\"><PERSON> Driver</a>, English actress, singer, and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Driver\" title=\"Betty Driver\"><PERSON></a>, English actress, singer, and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French lieutenant and politician, French Minister of Foreign Affairs (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (France)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Turkish actor and screenwriter (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Erol_G%C3%BCnayd%C4%B1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actor and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erol_G%C3%BCnayd%C4%B1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actor and screenwriter (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erol_G%C3%BCnayd%C4%B1n"}]}, {"year": "2012", "text": "<PERSON>, Greek-English computer scientist and academic (b. 1953)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-English computer scientist and academic (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-English computer scientist and academic (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Cambodian politician, 1st Prime Minister of Cambodia (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cambodian politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Cambodia\" title=\"Prime Minister of Cambodia\">Prime Minister of Cambodia</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cambodian politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Cambodia\" title=\"Prime Minister of Cambodia\">Prime Minister of Cambodia</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norodom_<PERSON>"}, {"title": "Prime Minister of Cambodia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Cambodia"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician (b. 1957)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "2013", "text": "<PERSON>, American drummer (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer (b. 1933)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, English-Australian journalist and publisher (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Nev<PERSON>_Dr<PERSON>\" title=\"Nevill Drury\"><PERSON><PERSON><PERSON></a>, English-Australian journalist and publisher (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dr<PERSON>\" title=\"Nevill Drury\"><PERSON><PERSON><PERSON></a>, English-Australian journalist and publisher (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nevill_<PERSON>ury"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Filipino lawyer and jurist (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and jurist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and jurist (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American singer (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and coach (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, German businessman (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Italian philosopher and historian (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Giovanni_<PERSON>\" title=\"Giovanni Reale\"><PERSON></a>, Italian philosopher and historian (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giovanni_Reale\" title=\"Giovanni Reale\"><PERSON></a>, Italian philosopher and historian (b. 1931)", "links": [{"title": "Giovanni Reale", "link": "https://wikipedia.org/wiki/Giovanni_Reale"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Czech poet and graphic designer (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Reynek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech poet and graphic designer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Reynek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech poet and graphic designer (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Reynek"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and politician (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American-Canadian football player and coach (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian football player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian football player and coach (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Russian footballer and manager (b. 1971)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player (b. 1975)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American baseball player (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian businessman and diplomat (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and diplomat (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and diplomat (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American football player (b. 1960)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Filipino theater, movie & television actor (b. 1950)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Ching<PERSON>y_Alonzo\" title=\"Chinggoy Alonzo\"><PERSON><PERSON><PERSON> Alonzo</a>, Filipino theater, movie &amp; television actor (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ching<PERSON><PERSON>_Alonzo\" title=\"Chinggoy Alonzo\"><PERSON><PERSON><PERSON></a>, Filipino theater, movie &amp; television actor (b. 1950)", "links": [{"title": "Chinggoy Alonzo", "link": "https://wikipedia.org/wiki/Chinggoy_Alonzo"}]}, {"year": "2018", "text": "<PERSON>, co-founder of Microsoft, philanthropist, owner of the Seattle Seahawks (b. 1953)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, co-founder of <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a>, philanthropist, owner of the <a href=\"https://wikipedia.org/wiki/Seattle_Seahawks\" title=\"Seattle Seahawks\">Seattle Seahawks</a> (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, co-founder of <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a>, philanthropist, owner of the <a href=\"https://wikipedia.org/wiki/Seattle_Seahawks\" title=\"Seattle Seahawks\">Seattle Seahawks</a> (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Microsoft", "link": "https://wikipedia.org/wiki/Microsoft"}, {"title": "Seattle Seahawks", "link": "https://wikipedia.org/wiki/Seattle_Seahawks"}]}, {"year": "2021", "text": "<PERSON>, British politician, member of Parliament for Southend West (b. 1952)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, member of Parliament for <a href=\"https://wikipedia.org/wiki/Southend_West_(UK_Parliament_constituency)\" class=\"mw-redirect\" title=\"Southend West (UK Parliament constituency)\">Southend West</a> (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, member of Parliament for <a href=\"https://wikipedia.org/wiki/Southend_West_(UK_Parliament_constituency)\" class=\"mw-redirect\" title=\"Southend West (UK Parliament constituency)\">Southend West</a> (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Southend West (UK Parliament constituency)", "link": "https://wikipedia.org/wiki/Southend_West_(UK_Parliament_constituency)"}]}, {"year": "2024", "text": "<PERSON>, English general (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (b. 1944)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}]}}