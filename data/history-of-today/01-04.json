{"date": "January 4", "url": "https://wikipedia.org/wiki/January_4", "data": {"Events": [{"year": "46 BC", "text": "<PERSON> fights <PERSON> in the Battle of Ruspina.", "html": "46 BC - 46 BC - <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\"><PERSON> Caesar</a> fights <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Ruspina\" title=\"Battle of Ruspina\">Battle of Ruspina</a>.", "no_year_html": "46 BC - <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\"><PERSON> Caesar</a> fights <a href=\"https://wikipedia.org/wiki/<PERSON>_Lab<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Ruspina\" title=\"Battle of Ruspina\">Battle of Ruspina</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Labienus"}, {"title": "Battle of Ruspina", "link": "https://wikipedia.org/wiki/Battle_of_R<PERSON>pina"}]}, {"year": "871", "text": "Battle of Reading: <PERSON><PERSON><PERSON><PERSON> of Wessex and his brother <PERSON> are defeated by a Danish invasion army.", "html": "871 - <a href=\"https://wikipedia.org/wiki/Battle_of_Reading_(871)\" title=\"Battle of Reading (871)\">Battle of Reading</a>: <a href=\"https://wikipedia.org/wiki/%C3%86thelred_of_Wessex\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> of Wessex\"><PERSON><PERSON><PERSON><PERSON> of Wessex</a> and his brother <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON></a> are defeated by a Danish invasion army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Reading_(871)\" title=\"Battle of Reading (871)\">Battle of Reading</a>: <a href=\"https://wikipedia.org/wiki/%C3%86thelred_of_Wessex\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> of Wessex\"><PERSON><PERSON><PERSON><PERSON> of Wessex</a> and his brother <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON></a> are defeated by a Danish invasion army.", "links": [{"title": "Battle of Reading (871)", "link": "https://wikipedia.org/wiki/Battle_of_Reading_(871)"}, {"title": "<PERSON><PERSON><PERSON><PERSON> of Wessex", "link": "https://wikipedia.org/wiki/%C3%86thelred_of_Wessex"}, {"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}]}, {"year": "1642", "text": "English Civil War: King <PERSON>, accompanied by 400 soldiers, attempts to arrest five members of Parliament for treason, only to discover the men had been tipped off and fled.", "html": "1642 - English Civil War: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON> I</a>, accompanied by 400 soldiers, attempts to arrest five members of <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">Parliament</a> for treason, only to discover the men had been tipped off and fled.", "no_year_html": "English Civil War: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England\" title=\"<PERSON> I of England\"><PERSON></a>, accompanied by 400 soldiers, attempts to arrest five members of <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">Parliament</a> for treason, only to discover the men had been tipped off and fled.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Parliament of England", "link": "https://wikipedia.org/wiki/Parliament_of_England"}]}, {"year": "1649", "text": "English Civil War: The Rump Parliament votes to put <PERSON> on trial.", "html": "1649 - English Civil War: The <a href=\"https://wikipedia.org/wiki/Rump_Parliament\" title=\"Rump Parliament\">Rump Parliament</a> votes to put <PERSON> on trial.", "no_year_html": "English Civil War: The <a href=\"https://wikipedia.org/wiki/Rump_Parliament\" title=\"Rump Parliament\">Rump Parliament</a> votes to put <PERSON> on trial.", "links": [{"title": "Rump Parliament", "link": "https://wikipedia.org/wiki/Rump_Parliament"}]}, {"year": "1717", "text": "The Netherlands, Great Britain, and France sign the Triple Alliance.", "html": "1717 - The <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Netherlands</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a>, and France sign the <a href=\"https://wikipedia.org/wiki/Triple_Alliance_(1717)\" title=\"Triple Alliance (1717)\">Triple Alliance</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Netherlands</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a>, and France sign the <a href=\"https://wikipedia.org/wiki/Triple_Alliance_(1717)\" title=\"Triple Alliance (1717)\">Triple Alliance</a>.", "links": [{"title": "Dutch Republic", "link": "https://wikipedia.org/wiki/Dutch_Republic"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Triple Alliance (1717)", "link": "https://wikipedia.org/wiki/Triple_Alliance_(1717)"}]}, {"year": "1762", "text": "Great Britain declares war on Spain, which meant the entry of Spain into the Seven Years' War.", "html": "1762 - Great Britain declares war on <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>, which meant the entry of Spain into the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>.", "no_year_html": "Great Britain declares war on <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>, which meant the entry of Spain into the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>.", "links": [{"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}, {"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}]}, {"year": "1798", "text": "<PERSON> arrives in Bucharest, Wallachia, as its new Prince, invested by the Ottoman Empire.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Constantine_Hang<PERSON>li\" title=\"Constantine Hang<PERSON>li\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>, <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a>, as its new <a href=\"https://wikipedia.org/wiki/Prince_of_Wallachia\" class=\"mw-redirect\" title=\"Prince of Wallachia\">Prince</a>, invested by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>, <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a>, as its new <a href=\"https://wikipedia.org/wiki/Prince_of_Wallachia\" class=\"mw-redirect\" title=\"Prince of Wallachia\">Prince</a>, invested by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bucharest", "link": "https://wikipedia.org/wiki/Bucharest"}, {"title": "Wallachia", "link": "https://wikipedia.org/wiki/Wallachia"}, {"title": "Prince of Wallachia", "link": "https://wikipedia.org/wiki/Prince_of_Wallachia"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1844", "text": "The first issue of the Swedish-languaged Saima newspaper founded by <PERSON><PERSON> <PERSON><PERSON> is published in Kuopio, Finland.", "html": "1844 - The first issue of the Swedish-languaged <a href=\"https://wikipedia.org/wiki/Saima_(newspaper)\" title=\"Saima (newspaper)\"><i>Saima</i> newspaper</a> founded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> is published in <a href=\"https://wikipedia.org/wiki/Ku<PERSON>io\" title=\"<PERSON><PERSON><PERSON>\">Kuopio, Finland</a>.", "no_year_html": "The first issue of the Swedish-languaged <a href=\"https://wikipedia.org/wiki/Saima_(newspaper)\" title=\"Saima (newspaper)\"><i>Saima</i> newspaper</a> founded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> is published in <a href=\"https://wikipedia.org/wiki/Kuopio\" title=\"Ku<PERSON><PERSON>\">Kuopio, Finland</a>.", "links": [{"title": "Saima (newspaper)", "link": "https://wikipedia.org/wiki/<PERSON>ma_(newspaper)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>opio"}]}, {"year": "1853", "text": "After having been kidnapped and sold into slavery in the American South, <PERSON> regains his freedom; his memoir Twelve Years a Slave later becomes a national bestseller.", "html": "1853 - After having been kidnapped and sold into slavery in the American South, <a href=\"https://wikipedia.org/wiki/Solomon_Northup\" title=\"Solomon Northup\"><PERSON></a> regains his freedom; his memoir <i><a href=\"https://wikipedia.org/wiki/Twelve_Years_a_Slave\" title=\"Twelve Years a Slave\">Twelve Years a Slave</a></i> later becomes a national bestseller.", "no_year_html": "After having been kidnapped and sold into slavery in the American South, <a href=\"https://wikipedia.org/wiki/Solomon_Northup\" title=\"Solomon Northup\"><PERSON></a> regains his freedom; his memoir <i><a href=\"https://wikipedia.org/wiki/Twelve_Years_a_Slave\" title=\"Twelve Years a Slave\">Twelve Years a Slave</a></i> later becomes a national bestseller.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Solomon_Northup"}, {"title": "Twelve Years a Slave", "link": "https://wikipedia.org/wiki/Twelve_Years_a_Slave"}]}, {"year": "1854", "text": "The McDonald Islands are discovered by Captain <PERSON> aboard the Samarang.", "html": "1854 - The <a href=\"https://wikipedia.org/wiki/Heard_Island_and_McDonald_Islands\" title=\"Heard Island and McDonald Islands\">McDonald Islands</a> are discovered by Captain <PERSON> aboard the <i>Samarang</i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Heard_Island_and_McDonald_Islands\" title=\"Heard Island and McDonald Islands\">McDonald Islands</a> are discovered by Captain <PERSON> aboard the <i>Samarang</i>.", "links": [{"title": "Heard Island and McDonald Islands", "link": "https://wikipedia.org/wiki/Heard_Island_and_McDonald_Islands"}]}, {"year": "1863", "text": "The New Apostolic Church, a Christian and chiliastic church, is established in Hamburg, Germany.", "html": "1863 - The <a href=\"https://wikipedia.org/wiki/New_Apostolic_Church\" title=\"New Apostolic Church\">New Apostolic Church</a>, a Christian and <a href=\"https://wikipedia.org/wiki/Millennialism\" title=\"Millennialism\">chiliastic</a> church, is established in <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a>, Germany.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_Apostolic_Church\" title=\"New Apostolic Church\">New Apostolic Church</a>, a Christian and <a href=\"https://wikipedia.org/wiki/Millennialism\" title=\"Millennialism\">chiliastic</a> church, is established in <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a>, Germany.", "links": [{"title": "New Apostolic Church", "link": "https://wikipedia.org/wiki/New_Apostolic_Church"}, {"title": "Millennialism", "link": "https://wikipedia.org/wiki/Millennialism"}, {"title": "Hamburg", "link": "https://wikipedia.org/wiki/Hamburg"}]}, {"year": "1878", "text": "Russo-Turkish War (1877-78): Sofia is liberated from Ottoman rule.", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1877%E2%80%9378)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1877-78)\">Russo-Turkish War (1877-78)</a>: <a href=\"https://wikipedia.org/wiki/Sofia\" title=\"Sofia\">Sofia</a> is <a href=\"https://wikipedia.org/wiki/Battle_of_Sofia\" title=\"Battle of Sofia\">liberated</a> from <a href=\"https://wikipedia.org/wiki/Ottoman_Bulgaria\" title=\"Ottoman Bulgaria\">Ottoman rule</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1877%E2%80%9378)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1877-78)\">Russo-Turkish War (1877-78)</a>: <a href=\"https://wikipedia.org/wiki/Sofia\" title=\"Sofia\">Sofia</a> is <a href=\"https://wikipedia.org/wiki/Battle_of_Sofia\" title=\"Battle of Sofia\">liberated</a> from <a href=\"https://wikipedia.org/wiki/Ottoman_Bulgaria\" title=\"Ottoman Bulgaria\">Ottoman rule</a>.", "links": [{"title": "Russo-Turkish War (1877-78)", "link": "https://wikipedia.org/wiki/Russo-Turkish_War_(1877%E2%80%9378)"}, {"title": "Sofia", "link": "https://wikipedia.org/wiki/Sofia"}, {"title": "Battle of Sofia", "link": "https://wikipedia.org/wiki/Battle_of_Sofia"}, {"title": "Ottoman Bulgaria", "link": "https://wikipedia.org/wiki/Ottoman_Bulgaria"}]}, {"year": "1884", "text": "The Fabian Society is founded in London, United Kingdom.", "html": "1884 - The <a href=\"https://wikipedia.org/wiki/Fabian_Society\" title=\"Fabian Society\">Fabian Society</a> is founded in London, United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fabian_Society\" title=\"Fabian Society\">Fabian Society</a> is founded in London, United Kingdom.", "links": [{"title": "Fabian Society", "link": "https://wikipedia.org/wiki/Fabian_Society"}]}, {"year": "1885", "text": "Sino-French War: French troops under General <PERSON> defeat a numerically superior Qing force at Núi Bop in northern Vietnam.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Sino-French_War\" title=\"Sino-French War\">Sino-French War</a>: French troops under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9grier\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_N%C3%BAi_Bop\" title=\"Battle of Núi Bop\">defeat</a> a numerically superior <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing</a> force at Núi Bop in northern Vietnam.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sino-French_War\" title=\"Sino-French War\">Sino-French War</a>: French troops under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_N%C3%A9grier\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_N%C3%BAi_Bop\" title=\"Battle of Núi Bop\">defeat</a> a numerically superior <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing</a> force at Núi Bop in northern Vietnam.", "links": [{"title": "Sino-French War", "link": "https://wikipedia.org/wiki/Sino-French_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oscar_de_N%C3%A9grier"}, {"title": "Battle of Núi Bop", "link": "https://wikipedia.org/wiki/Battle_of_N%C3%BAi_Bop"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}]}, {"year": "1896", "text": "Utah is admitted as the 45th U.S. state.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a> is admitted as the 45th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a> is admitted as the 45th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Utah", "link": "https://wikipedia.org/wiki/Utah"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1903", "text": "<PERSON><PERSON>, an elephant, is electrocuted by the owners of Luna Park, Coney Island. The Edison film company records the film Electrocuting an Elephant of <PERSON><PERSON>'s death.", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(elephant)\" title=\"<PERSON><PERSON> (elephant)\"><PERSON><PERSON></a>, an <a href=\"https://wikipedia.org/wiki/Elephant\" title=\"Elephant\">elephant</a>, is electrocuted by the owners of <a href=\"https://wikipedia.org/wiki/Luna_Park,_Coney_Island_(1903)\" class=\"mw-redirect\" title=\"Luna Park, Coney Island (1903)\">Luna Park, Coney Island</a>. The <a href=\"https://wikipedia.org/wiki/Edison_Studios\" title=\"Edison Studios\">Edison film company</a> records the film <i><a href=\"https://wikipedia.org/wiki/Electrocuting_an_Elephant\" title=\"Electrocuting an Elephant\">Electrocuting an Elephant</a></i> of <PERSON><PERSON>'s death.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(elephant)\" title=\"<PERSON><PERSON> (elephant)\"><PERSON><PERSON></a>, an <a href=\"https://wikipedia.org/wiki/Elephant\" title=\"Elephant\">elephant</a>, is electrocuted by the owners of <a href=\"https://wikipedia.org/wiki/Luna_Park,_Coney_Island_(1903)\" class=\"mw-redirect\" title=\"Luna Park, Coney Island (1903)\">Luna Park, Coney Island</a>. The <a href=\"https://wikipedia.org/wiki/Edison_Studios\" title=\"Edison Studios\">Edison film company</a> records the film <i><a href=\"https://wikipedia.org/wiki/Electrocuting_an_Elephant\" title=\"Electrocuting an Elephant\">Electrocuting an Elephant</a></i> of <PERSON><PERSON>'s death.", "links": [{"title": "<PERSON><PERSON> (elephant)", "link": "https://wikipedia.org/wiki/Top<PERSON>_(elephant)"}, {"title": "Elephant", "link": "https://wikipedia.org/wiki/Elephant"}, {"title": "Luna Park, Coney Island (1903)", "link": "https://wikipedia.org/wiki/Luna_Park,_Coney_Island_(1903)"}, {"title": "Edison Studios", "link": "https://wikipedia.org/wiki/Edison_Studios"}, {"title": "Electrocuting an Elephant", "link": "https://wikipedia.org/wiki/Electrocuting_an_Elephant"}]}, {"year": "1909", "text": "Explorer <PERSON><PERSON><PERSON> of the Imperial Trans-Antarctic Expedition escaped death by fleeing across ice floes.", "html": "1909 - Explorer <a href=\"https://wikipedia.org/wiki/Aeneas_Mack<PERSON>osh\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition\" title=\"Imperial Trans-Antarctic Expedition\">Imperial Trans-Antarctic Expedition</a> escaped death by fleeing across <a href=\"https://wikipedia.org/wiki/Drift_ice\" title=\"Drift ice\">ice floes</a>.", "no_year_html": "Explorer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>osh\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition\" title=\"Imperial Trans-Antarctic Expedition\">Imperial Trans-Antarctic Expedition</a> escaped death by fleeing across <a href=\"https://wikipedia.org/wiki/Drift_ice\" title=\"Drift ice\">ice floes</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>as_<PERSON>osh"}, {"title": "Imperial Trans-Antarctic Expedition", "link": "https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition"}, {"title": "Drift ice", "link": "https://wikipedia.org/wiki/Drift_ice"}]}, {"year": "1912", "text": "The Scout Association is incorporated throughout the British Empire by royal charter.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/The_Scout_Association\" title=\"The Scout Association\">The Scout Association</a> is incorporated throughout the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a> by <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Scout_Association\" title=\"The Scout Association\">The Scout Association</a> is incorporated throughout the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a> by <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a>.", "links": [{"title": "The Scout Association", "link": "https://wikipedia.org/wiki/The_Scout_Association"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "Royal charter", "link": "https://wikipedia.org/wiki/Royal_charter"}]}, {"year": "1918", "text": "The Finnish Declaration of Independence is recognized by Russia, Sweden, Germany and France.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Finnish_Declaration_of_Independence\" title=\"Finnish Declaration of Independence\">Finnish Declaration of Independence</a> is recognized by Russia, Sweden, Germany and France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Finnish_Declaration_of_Independence\" title=\"Finnish Declaration of Independence\">Finnish Declaration of Independence</a> is recognized by Russia, Sweden, Germany and France.", "links": [{"title": "Finnish Declaration of Independence", "link": "https://wikipedia.org/wiki/Finnish_Declaration_of_Independence"}]}, {"year": "1944", "text": "World War II: Operation Carpetbagger, involving the dropping of arms and supplies to resistance fighters in Europe, begins.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Carpetbagger\" title=\"Operation Carpetbagger\">Operation Carpetbagger</a>, involving the dropping of arms and supplies to resistance fighters in Europe, begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Carpetbagger\" title=\"Operation Carpetbagger\">Operation Carpetbagger</a>, involving the dropping of arms and supplies to resistance fighters in Europe, begins.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Carpetbagger", "link": "https://wikipedia.org/wiki/Operation_Carpetbagger"}]}, {"year": "1946", "text": "The first day of a three-day \"disastrous\" tornado outbreak across the south-central United States leaves 41 people dead and at least 412 others injured.", "html": "1946 - The first day of a <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_January_4%E2%80%936,_1946\" title=\"Tornado outbreak of January 4-6, 1946\">three-day \"disastrous\" tornado outbreak</a> across the south-central United States leaves 41 people dead and at least 412 others injured.", "no_year_html": "The first day of a <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_January_4%E2%80%936,_1946\" title=\"Tornado outbreak of January 4-6, 1946\">three-day \"disastrous\" tornado outbreak</a> across the south-central United States leaves 41 people dead and at least 412 others injured.", "links": [{"title": "Tornado outbreak of January 4-6, 1946", "link": "https://wikipedia.org/wiki/Tornado_outbreak_of_January_4%E2%80%936,_1946"}]}, {"year": "1948", "text": "Burma gains its independence from the United Kingdom, becoming an independent republic.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a> gains its independence from the United Kingdom, becoming an independent republic.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a> gains its independence from the United Kingdom, becoming an independent republic.", "links": [{"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "1951", "text": "Korean War: Chinese and North Korean forces capture Seoul for the second time.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: Chinese and <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korean</a> forces <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Seoul\" title=\"Third Battle of Seoul\">capture Seoul</a> for the second time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: Chinese and <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korean</a> forces <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Seoul\" title=\"Third Battle of Seoul\">capture Seoul</a> for the second time.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Third Battle of Seoul", "link": "https://wikipedia.org/wiki/Third_Battle_of_Seoul"}]}, {"year": "1956", "text": "The Greek National Radical Union is formed by <PERSON><PERSON>.", "html": "1956 - The Greek <a href=\"https://wikipedia.org/wiki/National_Radical_Union\" title=\"National Radical Union\">National Radical Union</a> is formed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "The Greek <a href=\"https://wikipedia.org/wiki/National_Radical_Union\" title=\"National Radical Union\">National Radical Union</a> is formed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "National Radical Union", "link": "https://wikipedia.org/wiki/National_Radical_Union"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "Sputnik 1, the first artificial Earth satellite, launched by the Soviet Union in 1957, falls to Earth from orbit.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Sputnik_1\" title=\"Sputnik 1\">Sputnik 1</a>, the first artificial Earth satellite, launched by the Soviet Union in 1957, falls to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> from orbit.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sputnik_1\" title=\"Sputnik 1\">Sputnik 1</a>, the first artificial Earth satellite, launched by the Soviet Union in 1957, falls to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> from orbit.", "links": [{"title": "Sputnik 1", "link": "https://wikipedia.org/wiki/Sputnik_1"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}]}, {"year": "1959", "text": "Luna 1 becomes the first spacecraft to reach the vicinity of the Moon.", "html": "1959 - <i><a href=\"https://wikipedia.org/wiki/Luna_1\" title=\"Luna 1\">Luna 1</a></i> becomes the first <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> to reach the vicinity of the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Luna_1\" title=\"Luna 1\">Luna 1</a></i> becomes the first <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> to reach the vicinity of the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "links": [{"title": "Luna 1", "link": "https://wikipedia.org/wiki/Luna_1"}, {"title": "Spacecraft", "link": "https://wikipedia.org/wiki/Spacecraft"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "1965", "text": "Aeroflot Flight 101/X-20 crashes on approach to Alma-Ata Airport, killing 64 people.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_101/X-20\" title=\"Aeroflot Flight 101/X-20\">Aeroflot Flight 101/X-20</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Almaty_International_Airport\" title=\"Almaty International Airport\">Alma-Ata Airport</a>, killing 64 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_101/X-20\" title=\"Aeroflot Flight 101/X-20\">Aeroflot Flight 101/X-20</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Almaty_International_Airport\" title=\"Almaty International Airport\">Alma-Ata Airport</a>, killing 64 people.", "links": [{"title": "Aeroflot Flight 101/X-20", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_101/X-20"}, {"title": "Almaty International Airport", "link": "https://wikipedia.org/wiki/Almaty_International_Airport"}]}, {"year": "1972", "text": "<PERSON> becomes the first female judge to sit at the Old Bailey in London, UK.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female judge to sit at the <a href=\"https://wikipedia.org/wiki/<PERSON>_Bailey\" title=\"<PERSON> Bailey\"><PERSON></a> in London, UK.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female judge to sit at the <a href=\"https://wikipedia.org/wiki/<PERSON>_Bailey\" title=\"<PERSON>\"><PERSON></a> in London, UK.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_He<PERSON>bron"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bailey"}]}, {"year": "1975", "text": "This date overflowed the 12-bit field that had been used in TOPS-10. There were numerous problems and crashes related to this bug while an alternative format was developed.", "html": "1975 - This date overflowed the 12-bit field that had been used in <a href=\"https://wikipedia.org/wiki/TOPS-10\" title=\"TOPS-10\">TOPS-10</a>. There were numerous problems and crashes related to this bug while an alternative format was developed.", "no_year_html": "This date overflowed the 12-bit field that had been used in <a href=\"https://wikipedia.org/wiki/TOPS-10\" title=\"TOPS-10\">TOPS-10</a>. There were numerous problems and crashes related to this bug while an alternative format was developed.", "links": [{"title": "TOPS-10", "link": "https://wikipedia.org/wiki/TOPS-10"}]}, {"year": "1976", "text": "The Troubles: The Ulster Volunteer Force shoots dead six Irish Catholic civilians in County Armagh, Northern Ireland. The next day, gunmen would shoot dead ten Protestant civilians nearby in retaliation.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Ulster_Volunteer_Force\" title=\"Ulster Volunteer Force\">Ulster Volunteer Force</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>y_and_O%27Dowd_killings\" title=\"<PERSON><PERSON><PERSON> and O'Dowd killings\">shoots dead six Irish Catholic civilians</a> in <a href=\"https://wikipedia.org/wiki/County_Armagh\" title=\"County Armagh\">County Armagh</a>, Northern Ireland. The next day, gunmen <a href=\"https://wikipedia.org/wiki/Kingsmill_massacre\" title=\"Kingsmill massacre\">would shoot dead ten Protestant civilians</a> nearby in retaliation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Ulster_Volunteer_Force\" title=\"Ulster Volunteer Force\">Ulster Volunteer Force</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>y_and_O%27Dowd_killings\" title=\"<PERSON><PERSON><PERSON> and O'Dowd killings\">shoots dead six Irish Catholic civilians</a> in <a href=\"https://wikipedia.org/wiki/County_Armagh\" title=\"County Armagh\">County Armagh</a>, Northern Ireland. The next day, gunmen <a href=\"https://wikipedia.org/wiki/Kingsmill_massacre\" title=\"Kingsmill massacre\">would shoot dead ten Protestant civilians</a> nearby in retaliation.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Ulster Volunteer Force", "link": "https://wikipedia.org/wiki/Ulster_Volunteer_Force"}, {"title": "<PERSON><PERSON><PERSON> and <PERSON> killings", "link": "https://wikipedia.org/wiki/Re<PERSON><PERSON>_and_O%27Dowd_killings"}, {"title": "County Armagh", "link": "https://wikipedia.org/wiki/County_Armagh"}, {"title": "Kingsmill massacre", "link": "https://wikipedia.org/wiki/Kingsmill_massacre"}]}, {"year": "1987", "text": "The Maryland train collision: An Amtrak train en route to Boston from Washington, D.C., collides with Conrail engines in Chase, Maryland, United States, killing 16 people.", "html": "1987 - The <a href=\"https://wikipedia.org/wiki/1987_Maryland_train_collision\" title=\"1987 Maryland train collision\">Maryland train collision</a>: An <a href=\"https://wikipedia.org/wiki/Amtrak\" title=\"Amtrak\">Amtrak</a> train <i>en route</i> to <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a> from Washington, D.C., collides with <a href=\"https://wikipedia.org/wiki/Conrail\" title=\"Conrail\">Conrail</a> engines in <a href=\"https://wikipedia.org/wiki/Chase,_Maryland\" title=\"Chase, Maryland\">Chase, Maryland</a>, United States, killing 16 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1987_Maryland_train_collision\" title=\"1987 Maryland train collision\">Maryland train collision</a>: An <a href=\"https://wikipedia.org/wiki/Amtrak\" title=\"Amtrak\">Amtrak</a> train <i>en route</i> to <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a> from Washington, D.C., collides with <a href=\"https://wikipedia.org/wiki/Conrail\" title=\"Conrail\">Conrail</a> engines in <a href=\"https://wikipedia.org/wiki/Chase,_Maryland\" title=\"Chase, Maryland\">Chase, Maryland</a>, United States, killing 16 people.", "links": [{"title": "1987 Maryland train collision", "link": "https://wikipedia.org/wiki/1987_Maryland_train_collision"}, {"title": "Amtrak", "link": "https://wikipedia.org/wiki/Amtrak"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}, {"title": "Conrail", "link": "https://wikipedia.org/wiki/Conrail"}, {"title": "Chase, Maryland", "link": "https://wikipedia.org/wiki/Chase,_Maryland"}]}, {"year": "1989", "text": "Second Gulf of Sidra incident: A pair of Libyan MiG-23 \"Floggers\" are shot down by a pair of US Navy F-14 Tomcats during an air-to-air confrontation.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/1989_air_battle_near_Tobruk\" title=\"1989 air battle near Tobruk\">Second Gulf of Sidra incident</a>: A pair of Libyan <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-23\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>vich MiG-23\">MiG-23 \"Floggers\"</a> are shot down by a pair of US Navy <a href=\"https://wikipedia.org/wiki/Grumman_F-14_Tomcat\" title=\"Grumman F-14 Tomcat\">F-14 Tomcats</a> during an air-to-air confrontation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1989_air_battle_near_Tobruk\" title=\"1989 air battle near Tobruk\">Second Gulf of Sidra incident</a>: A pair of Libyan <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-23\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>vich MiG-23\">MiG-23 \"Floggers\"</a> are shot down by a pair of US Navy <a href=\"https://wikipedia.org/wiki/Grumman_F-14_Tomcat\" title=\"Grumman F-14 Tomcat\">F-14 Tomcats</a> during an air-to-air confrontation.", "links": [{"title": "1989 air battle near Tobruk", "link": "https://wikipedia.org/wiki/1989_air_battle_near_Tobruk"}, {"title": "Mikoyan<PERSON><PERSON><PERSON><PERSON> MiG-23", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-23"}, {"title": "Grumman F-14 Tomcat", "link": "https://wikipedia.org/wiki/G<PERSON>man_F-14_<PERSON><PERSON>"}]}, {"year": "1990", "text": "In Pakistan's deadliest train accident an overloaded passenger train collides with an empty freight train, resulting in 307 deaths and 700 injuries.", "html": "1990 - In Pakistan's deadliest train accident an overloaded passenger train <a href=\"https://wikipedia.org/wiki/Sukkur_rail_disaster\" title=\"Sukkur rail disaster\">collides with an empty freight train</a>, resulting in 307 deaths and 700 injuries.", "no_year_html": "In Pakistan's deadliest train accident an overloaded passenger train <a href=\"https://wikipedia.org/wiki/Sukkur_rail_disaster\" title=\"Sukkur rail disaster\">collides with an empty freight train</a>, resulting in 307 deaths and 700 injuries.", "links": [{"title": "Sukkur rail disaster", "link": "https://wikipedia.org/wiki/Sukkur_rail_disaster"}]}, {"year": "1998", "text": "A massive ice storm hits eastern Canada and the northeastern United States, continuing through January 10 and causing widespread destruction.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/North_American_Ice_Storm_of_1998\" class=\"mw-redirect\" title=\"North American Ice Storm of 1998\">A massive ice storm</a> hits eastern Canada and the northeastern United States, continuing through <a href=\"https://wikipedia.org/wiki/January_10\" title=\"January 10\">January 10</a> and causing widespread destruction.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_American_Ice_Storm_of_1998\" class=\"mw-redirect\" title=\"North American Ice Storm of 1998\">A massive ice storm</a> hits eastern Canada and the northeastern United States, continuing through <a href=\"https://wikipedia.org/wiki/January_10\" title=\"January 10\">January 10</a> and causing widespread destruction.", "links": [{"title": "North American Ice Storm of 1998", "link": "https://wikipedia.org/wiki/North_American_Ice_Storm_of_1998"}, {"title": "January 10", "link": "https://wikipedia.org/wiki/January_10"}]}, {"year": "1999", "text": "Former professional wrestler <PERSON> is sworn in as governor of Minnesota, United States.", "html": "1999 - Former professional wrestler <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as governor of <a href=\"https://wikipedia.org/wiki/Minnesota\" title=\"Minnesota\">Minnesota</a>, United States.", "no_year_html": "Former professional wrestler <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as governor of <a href=\"https://wikipedia.org/wiki/Minnesota\" title=\"Minnesota\">Minnesota</a>, United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minnesota", "link": "https://wikipedia.org/wiki/Minnesota"}]}, {"year": "2000", "text": "A Norwegian passenger train departing from Trondheim, collides with a local train coming from Hamar in Åsta, Åmot; 19 people are killed and 68 injured in the accident.", "html": "2000 - A Norwegian passenger train departing from <a href=\"https://wikipedia.org/wiki/Trondheim\" title=\"Trondheim\">Trondheim</a>, <a href=\"https://wikipedia.org/wiki/%C3%85sta_accident\" title=\"Åsta accident\">collides with a local train</a> coming from <a href=\"https://wikipedia.org/wiki/Hamar\" title=\"<PERSON>ar\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/%C3%85sta\" title=\"Åsta\">Åsta</a>, <a href=\"https://wikipedia.org/wiki/%C3%85mot\" title=\"Å<PERSON>\">Åmot</a>; 19 people are killed and 68 injured in the accident.", "no_year_html": "A Norwegian passenger train departing from <a href=\"https://wikipedia.org/wiki/Trondheim\" title=\"Trondheim\">Trondheim</a>, <a href=\"https://wikipedia.org/wiki/%C3%85sta_accident\" title=\"Åsta accident\">collides with a local train</a> coming from <a href=\"https://wikipedia.org/wiki/Hamar\" title=\"Hamar\"><PERSON>ar</a> in <a href=\"https://wikipedia.org/wiki/%C3%85sta\" title=\"Åsta\">Åsta</a>, <a href=\"https://wikipedia.org/wiki/%C3%85mot\" title=\"Åmot\">Å<PERSON></a>; 19 people are killed and 68 injured in the accident.", "links": [{"title": "Trondheim", "link": "https://wikipedia.org/wiki/Trondheim"}, {"title": "Åsta accident", "link": "https://wikipedia.org/wiki/%C3%85sta_accident"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ar"}, {"title": "Å<PERSON>", "link": "https://wikipedia.org/wiki/%C3%85sta"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%85mot"}]}, {"year": "2004", "text": "Spirit, a NASA Mars rover, lands successfully on Mars at 04:35 UTC.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Spirit_(rover)\" title=\"Spirit (rover)\"><i>Spirit</i></a>, a <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> <a href=\"https://wikipedia.org/wiki/Mars_rover\" title=\"Mars rover\">Mars rover</a>, lands successfully on <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a> at 04:35 <a href=\"https://wikipedia.org/wiki/Coordinated_Universal_Time\" title=\"Coordinated Universal Time\">UTC</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spirit_(rover)\" title=\"Spirit (rover)\"><i>Spirit</i></a>, a <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> <a href=\"https://wikipedia.org/wiki/Mars_rover\" title=\"Mars rover\">Mars rover</a>, lands successfully on <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a> at 04:35 <a href=\"https://wikipedia.org/wiki/Coordinated_Universal_Time\" title=\"Coordinated Universal Time\">UTC</a>.", "links": [{"title": "Spirit (rover)", "link": "https://wikipedia.org/wiki/<PERSON>_(rover)"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mars rover", "link": "https://wikipedia.org/wiki/Mars_rover"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}, {"title": "Coordinated Universal Time", "link": "https://wikipedia.org/wiki/Coordinated_Universal_Time"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON> is elected President of Georgia following the November 2003 Rose Revolution.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Georgian_presidential_election,_2004\" class=\"mw-redirect\" title=\"Georgian presidential election, 2004\">elected</a> <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President of Georgia</a> following the November 2003 <a href=\"https://wikipedia.org/wiki/Rose_Revolution\" title=\"Rose Revolution\">Rose Revolution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Georgian_presidential_election,_2004\" class=\"mw-redirect\" title=\"Georgian presidential election, 2004\">elected</a> <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President of Georgia</a> following the November 2003 <a href=\"https://wikipedia.org/wiki/Rose_Revolution\" title=\"Rose Revolution\">Rose Revolution</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Georgian presidential election, 2004", "link": "https://wikipedia.org/wiki/Georgian_presidential_election,_2004"}, {"title": "President of Georgia", "link": "https://wikipedia.org/wiki/President_of_Georgia"}, {"title": "Rose Revolution", "link": "https://wikipedia.org/wiki/Rose_Revolution"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON> becomes acting Prime Minister of Israel after the incumbent, <PERSON>, suffers a second, apparently more serious stroke.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes acting <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> after the incumbent, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, suffers a second, apparently more serious stroke.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes acting <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> after the incumbent, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, suffers a second, apparently more serious stroke.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "The 110th United States Congress convenes, electing <PERSON> as the first female Speaker of the House in U.S. history.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/110th_United_States_Congress\" title=\"110th United States Congress\">110th United States Congress</a> convenes, electing <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the first female <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the House</a> in U.S. history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/110th_United_States_Congress\" title=\"110th United States Congress\">110th United States Congress</a> convenes, electing <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the first female <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the House</a> in U.S. history.", "links": [{"title": "110th United States Congress", "link": "https://wikipedia.org/wiki/110th_United_States_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "2008", "text": "A Let L-410 Turbolet crashes in the Los Roques Archipelago in Venezuela, killing 14 people.", "html": "2008 - A <a href=\"https://wikipedia.org/wiki/Let_L-410_Turbolet\" title=\"Let L-410 Turbolet\">Let L-410 Turbolet</a> <a href=\"https://wikipedia.org/wiki/2008_Los_Roques_archipelago_Transaven_Let_L-410_crash\" title=\"2008 Los Roques archipelago Transaven Let L-410 crash\">crashes</a> in the <a href=\"https://wikipedia.org/wiki/Los_Roques_Archipelago\" title=\"Los Roques Archipelago\">Los Roques Archipelago</a> in <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>, killing 14 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Let_L-410_Turbolet\" title=\"Let L-410 Turbolet\">Let L-410 Turbolet</a> <a href=\"https://wikipedia.org/wiki/2008_Los_Roques_archipelago_Transaven_Let_L-410_crash\" title=\"2008 Los Roques archipelago Transaven Let L-410 crash\">crashes</a> in the <a href=\"https://wikipedia.org/wiki/Los_Roques_Archipelago\" title=\"Los Roques Archipelago\">Los Roques Archipelago</a> in <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>, killing 14 people.", "links": [{"title": "Let L-410 Turbolet", "link": "https://wikipedia.org/wiki/Let_L-410_Turbolet"}, {"title": "2008 Los Roques archipelago Transaven Let L-410 crash", "link": "https://wikipedia.org/wiki/2008_Los_Roques_archipelago_Transaven_Let_L-410_crash"}, {"title": "Los Roques Archipelago", "link": "https://wikipedia.org/wiki/Los_Roques_Archipelago"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}]}, {"year": "2010", "text": "The Burj Khalifa, the current tallest building in the world, officially opens in Dubai.", "html": "2010 - The <a href=\"https://wikipedia.org/wiki/<PERSON>urj_Khali<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khali<PERSON>\"><PERSON><PERSON><PERSON></a>, the current tallest building in the world, officially opens in <a href=\"https://wikipedia.org/wiki/Dubai\" title=\"Dubai\">Dubai</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the current tallest building in the world, officially opens in <a href=\"https://wikipedia.org/wiki/Dubai\" title=\"Dubai\">Dubai</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Dubai", "link": "https://wikipedia.org/wiki/Dubai"}]}, {"year": "2013", "text": "A gunman kills eight people in a house-to-house rampage in Kawit, Cavite, Philippines.", "html": "2013 - A gunman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_shooting\" title=\"<PERSON><PERSON><PERSON> shooting\">kills eight people</a> in a house-to-house rampage in <a href=\"https://wikipedia.org/wiki/Kawit\" title=\"<PERSON>wi<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cavite\" title=\"Cavite\">Cavite</a>, Philippines.", "no_year_html": "A gunman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_shooting\" title=\"<PERSON><PERSON><PERSON> shooting\">kills eight people</a> in a house-to-house rampage in <a href=\"https://wikipedia.org/wiki/Kawit\" title=\"<PERSON>wit\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cavite\" title=\"Cavite\">Cavite</a>, Philippines.", "links": [{"title": "<PERSON><PERSON><PERSON> shooting", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>t_shooting"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wit"}, {"title": "Cavite", "link": "https://wikipedia.org/wiki/Cavite"}]}, {"year": "2018", "text": "Hennenman-Kroonstad train crash: A passenger train operated by Shosholoza Meyl collides with a truck on a level crossing at Geneva Station between Hennenman and Kroonstad, Free State, South Africa. Twenty people are killed and 260 injured.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Hennenman%E2%80%93Kroonstad_train_crash\" title=\"He<PERSON><PERSON><PERSON>-Kroonstad train crash\"><PERSON><PERSON><PERSON><PERSON>-Kroonstad train crash</a>: A passenger train operated by <a href=\"https://wikipedia.org/wiki/<PERSON>hoshol<PERSON><PERSON>_<PERSON>yl\" title=\"<PERSON>hosholoz<PERSON> Me<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> collides with a truck on a level crossing at Geneva Station between <a href=\"https://wikipedia.org/wiki/Hennenman\" title=\"Henne<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Kroonstad\" title=\"Kroonstad\">Kroonstad</a>, Free State, South Africa. Twenty people are killed and 260 injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hennenman%E2%80%93Kroonstad_train_crash\" title=\"<PERSON><PERSON><PERSON><PERSON>-Kroonstad train crash\"><PERSON><PERSON><PERSON><PERSON>-Kroonstad train crash</a>: A passenger train operated by <a href=\"https://wikipedia.org/wiki/<PERSON>hos<PERSON><PERSON><PERSON>_<PERSON>yl\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> collides with a truck on a level crossing at Geneva Station between <a href=\"https://wikipedia.org/wiki/Hennenman\" title=\"Henne<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Kroonstad\" title=\"Kroonstad\">Kroonstad</a>, Free State, South Africa. Twenty people are killed and 260 injured.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>-Kroonstad train crash", "link": "https://wikipedia.org/wiki/Hennenman%E2%80%93Kroonstad_train_crash"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>holoz<PERSON>_Meyl"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Kroonstad", "link": "https://wikipedia.org/wiki/Kroonstad"}]}, {"year": "2019", "text": "A fire in an escape room in Koszalin, Poland, kills five teenagers through carbon monoxide poisoning.", "html": "2019 - A <a href=\"https://wikipedia.org/wiki/ToNiePok%C3%B3j_escape_room_fire\" title=\"ToNiePokój escape room fire\">fire in an escape room</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Poland, kills five teenagers through <a href=\"https://wikipedia.org/wiki/Carbon_monoxide_poisoning\" title=\"Carbon monoxide poisoning\">carbon monoxide poisoning</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/ToNiePok%C3%B3j_escape_room_fire\" title=\"ToNiePokój escape room fire\">fire in an escape room</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Poland, kills five teenagers through <a href=\"https://wikipedia.org/wiki/Carbon_monoxide_poisoning\" title=\"Carbon monoxide poisoning\">carbon monoxide poisoning</a>.", "links": [{"title": "ToNiePokój escape room fire", "link": "https://wikipedia.org/wiki/ToNiePok%C3%B3j_escape_room_fire"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>n"}, {"title": "Carbon monoxide poisoning", "link": "https://wikipedia.org/wiki/Carbon_monoxide_poisoning"}]}], "Births": [{"year": "659", "text": "<PERSON> ibn <PERSON><PERSON> (d.680)", "html": "659 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>-A<PERSON>in\" class=\"mw-redirect\" title=\"<PERSON> ibn <PERSON>Abidin\"><PERSON> ibn <PERSON></a> (d.680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> ibn <PERSON>-Abidin\"><PERSON> ibn <PERSON></a> (d.680)", "links": [{"title": "<PERSON> ibn <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1077", "text": "Emperor <PERSON><PERSON><PERSON> of China (d. 1100)", "html": "1077 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of China (d. 1100)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of China (d. 1100)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1334", "text": "<PERSON><PERSON><PERSON>, Count of Savoy (d. 1383)", "html": "1334 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VI,_Count_of_Savoy\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Savoy\"><PERSON><PERSON><PERSON> VI, Count of Savoy</a> (d. 1383)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VI,_Count_of_Savoy\" title=\"<PERSON><PERSON><PERSON> VI, Count of Savoy\"><PERSON><PERSON><PERSON> VI, Count of Savoy</a> (d. 1383)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>ade<PERSON>_<PERSON>,_Count_of_Savoy"}]}, {"year": "1467", "text": "<PERSON><PERSON> <PERSON>, Count of Stolberg-Wernigerode (d. 1538)", "html": "1467 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_VIII,_Count_of_Stolberg-Wernigerode\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>, Count of Stolberg-Wernigerode\"><PERSON><PERSON>, Count of Stolberg-Wernigerode</a> (d. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_VIII,_Count_of_Stolberg-Wernigerode\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>, Count of Stolberg-Wernigerode\"><PERSON><PERSON>, Count of Stolberg-Wernigerode</a> (d. 1538)", "links": [{"title": "<PERSON><PERSON> <PERSON>, Count of Stolberg-Wernigerode", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_Stolberg-Wernigerode"}]}, {"year": "1581", "text": "<PERSON>, Irish archbishop and historian (d. 1656)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop and historian (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop and historian (d. 1656)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1643 (NS)", "text": "<PERSON>, English mathematician and physicist (d. 1726/27)", "html": "1643 (NS) - <a href=\"https://wikipedia.org/wiki/1643\" title=\"1643\">1643</a> (<a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">NS</a>) - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Newton\"><PERSON></a>, English mathematician and physicist (d. 1726/27)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1643\" title=\"1643\">1643</a> (<a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">NS</a>) - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON> Newton\"><PERSON></a>, English mathematician and physicist (d. 1726/27)", "links": [{"title": "1643", "link": "https://wikipedia.org/wiki/1643"}, {"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1654", "text": "<PERSON>, Swedish physician and academic (d. 1742)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physician and academic (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physician and academic (d. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1672", "text": "<PERSON>, English-Irish archbishop (d. 1742)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish archbishop (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish archbishop (d. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, Italian composer, violinist, and organist (d. 1736)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer, violinist, and organist (d. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer, violinist, and organist (d. 1736)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON>, German organist and composer (d. 1774)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1731", "text": "<PERSON>, Prussian minister of education (d. 1793)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian minister of education (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian minister of education (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, German philologist and mythologist (d. 1863)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and mythologist (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and mythologist (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, French educator, invented <PERSON><PERSON><PERSON> (d. 1852)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French educator, invented <a href=\"https://wikipedia.org/wiki/Braille\" title=\"Brail<PERSON>\"><PERSON><PERSON><PERSON></a> (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French educator, invented <a href=\"https://wikipedia.org/wiki/Braille\" title=\"Brail<PERSON>\"><PERSON><PERSON><PERSON></a> (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}, {"title": "Braille", "link": "https://wikipedia.org/wiki/Braille"}]}, {"year": "1813", "text": "<PERSON>, English linguist and educator (d. 1897)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist and educator (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist and educator (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, English admiral (d. 1893)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "General <PERSON>, American circus performer (d. 1883)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/General_<PERSON>_<PERSON>\" title=\"General <PERSON>\">General <PERSON></a>, American circus performer (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/General_<PERSON>_<PERSON>\" title=\"General <PERSON>\">General <PERSON></a>, American circus performer (d. 1883)", "links": [{"title": "General <PERSON>", "link": "https://wikipedia.org/wiki/General_<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, German archaeologist, architect, and engineer (d. 1896)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist, architect, and engineer (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist, architect, and engineer (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON>, Japanese general and politician, 6th Prime Minister of Japan (d. 1913)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Katsura_Tar%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Katsura_Tar%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Katsura_Tar%C5%8D"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1858", "text": "<PERSON>, American publisher and politician, 47th United States Secretary of the Treasury (d. 1946)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Carter Glass\"><PERSON></a>, American publisher and politician, 47th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Carter Glass\"><PERSON></a>, American publisher and politician, 47th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1864", "text": "<PERSON>, Swedish doctor and author (d. 1928)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish doctor and author (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish doctor and author (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>t"}]}, {"year": "1869", "text": "<PERSON>, American baseball player and umpire (d. 1960)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and umpire (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and umpire (d. 1960)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1874", "text": "<PERSON>, Czech violinist and composer (d. 1935)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Czech violinist and composer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Czech violinist and composer (d. 1935)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1877", "text": "<PERSON>, English film actor (d. 1951)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gowland\" title=\"Gibson Gowland\"><PERSON></a>, English film actor (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Go<PERSON>\" title=\"Gibson Gowland\"><PERSON></a>, English film actor (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}]}, {"year": "1877", "text": "<PERSON><PERSON>, American painter and poet (d. 1943)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Mars<PERSON>_Hartley\" title=\"Mars<PERSON> Hartley\"><PERSON><PERSON></a>, American painter and poet (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marsden_Hartley\" title=\"Mars<PERSON> Hartley\"><PERSON><PERSON></a>, American painter and poet (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hartley"}]}, {"year": "1878", "text": "<PERSON><PERSON> <PERSON><PERSON>, English poet and short story writer (d. 1957)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet and short story writer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet and short story writer (d. 1957)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Welsh painter and illustrator (d. 1961)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh painter and illustrator (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh painter and illustrator (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, German sculptor (d. 1919)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American author and poet (d. 1969)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Eastman\"><PERSON></a>, American author and poet (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Dutch pathologist and academic (d. 1961)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pathologist and academic (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pathologist and academic (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American painter, critic, and educator (d. 1958)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, critic, and educator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, critic, and educator (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guy_P%C3%A8<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, Indian lawyer and jurist, 2nd Chief Justice of India (d. 1963)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ri\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and jurist, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and jurist, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a> (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>ri"}, {"title": "Chief Justice of India", "link": "https://wikipedia.org/wiki/Chief_Justice_of_India"}]}, {"year": "1891", "text": "<PERSON>, English-Australian sergeant and politician, 31st Premier of Tasmania (d. 1948)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian sergeant and politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian sergeant and politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1895", "text": "<PERSON>, American engineer and businessman, co-founded Grumman Aeronautical Engineering Co. (d. 1982)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>rum<PERSON>\">Grumman Aeronautical Engineering Co.</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>rum<PERSON>\">Grumman Aeronautical Engineering Co.</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American politician (d. 1969)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, French painter and illustrator (d. 1987)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>on"}]}, {"year": "1897", "text": "<PERSON>, Chinese politician, Vice President of the Republic of China (d. 1965)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Republic_of_China\" title=\"Vice President of the Republic of China\">Vice President of the Republic of China</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Republic_of_China\" title=\"Vice President of the Republic of China\">Vice President of the Republic of China</a> (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the Republic of China", "link": "https://wikipedia.org/wiki/Vice_President_of_the_Republic_of_China"}]}, {"year": "1900", "text": "<PERSON>, American ornithologist and zoologist (d. 1989)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ornithologist)\" title=\"<PERSON> (ornithologist)\"><PERSON></a>, American ornithologist and zoologist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ornithologist)\" title=\"<PERSON> (ornithologist)\"><PERSON></a>, American ornithologist and zoologist (d. 1989)", "links": [{"title": "<PERSON> (ornithologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(ornithologist)"}]}, {"year": "1901", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Trinidadian journalist and theorist (d. 1989)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Trinidadian journalist and theorist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C. L<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Trinidadian journalist and theorist (d. 1989)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American businessman and politician, 6th Director of Central Intelligence (d. 1991)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Director of Central Intelligence", "link": "https://wikipedia.org/wiki/Director_of_Central_Intelligence"}]}, {"year": "1903", "text": "<PERSON>, German carpenter and attempted assassin of <PERSON> (d. 1945)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German carpenter and attempted assassin of <PERSON> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German carpenter and attempted assassin of <PERSON> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Samoan ruler (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Malietoa_Tanumafili_II\" title=\"Malietoa Tanumafili II\"><PERSON><PERSON><PERSON> Tanumafili II</a>, Samoan ruler (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malietoa_Tanumafili_II\" title=\"Malietoa Tanumafili II\"><PERSON>etoa Tanumafili II</a>, Samoan ruler (d. 2007)", "links": [{"title": "Malietoa Tanumafili II", "link": "https://wikipedia.org/wiki/Malietoa_Tanumafili_II"}]}, {"year": "1916", "text": "<PERSON>, American pianist and composer (d. 1989)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actor and director (d. 1995)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American intelligence officer, 10th Director of Central Intelligence (d. 1996)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American intelligence officer, 10th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American intelligence officer, 10th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Director of Central Intelligence", "link": "https://wikipedia.org/wiki/Director_of_Central_Intelligence"}]}, {"year": "1920", "text": "<PERSON><PERSON>, British actress (d. 1997)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actress (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actress (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, German shot putter (d. 2023)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German shot putter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German shot putter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish skier and technician (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish skier and technician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish skier and technician (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1927", "text": "<PERSON>, Canadian businessman and philanthropist (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actress (d. 2024)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rush\"><PERSON></a>, American actress (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, German journalist and politician (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German journalist and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German journalist and politician (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American actor and director (d. 1994)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON> Booke\"><PERSON><PERSON></a>, American actor and director (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>e\"><PERSON><PERSON></a>, American actor and director (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1930", "text": "<PERSON>, American football player and coach (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Australian judge and politician, 22nd Governor-General of Australia", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian judge and politician, 22nd <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian judge and politician, 22nd <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}]}, {"year": "1931", "text": "<PERSON>, Romanian poet, writer and translator", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet, writer and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet, writer and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nora_<PERSON>uga"}]}, {"year": "1932", "text": "<PERSON>, Spanish director and screenwriter (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American Secret Service agent (d. 2025)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Secret_Service)\" title=\"<PERSON> (Secret Service)\"><PERSON></a>, American Secret Service agent (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Secret_Service)\" title=\"<PERSON> (Secret Service)\"><PERSON></a>, American Secret Service agent (d. 2025)", "links": [{"title": "<PERSON> (Secret Service)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Secret_Service)"}]}, {"year": "1934", "text": "<PERSON>, Slovak politician, 2nd President of Slovakia", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Slovakia\" title=\"President of Slovakia\">President of Slovakia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Slovakia\" title=\"President of Slovakia\">President of Slovakia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Slovakia", "link": "https://wikipedia.org/wiki/President_of_Slovakia"}]}, {"year": "1935", "text": "<PERSON>, American boxer (d. 2006)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American operatic soprano (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic soprano (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic soprano (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American actress, director, producer, and screenwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Irish stage, film and television actor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_actor)\" title=\"<PERSON> (Irish actor)\"><PERSON></a>, Irish stage, film and television actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_actor)\" title=\"<PERSON> (Irish actor)\"><PERSON></a>, Irish stage, film and television actor", "links": [{"title": "<PERSON> (Irish actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_actor)"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, German academic, linguist and author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German academic, linguist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German academic, linguist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Welsh physicist and academic, Nobel Prize laureate", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1940", "text": "<PERSON>, Chinese novelist, playwright, and critic, Nobel Prize laureate", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese novelist, playwright, and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese novelist, playwright, and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1941", "text": "<PERSON>, Italian-Canadian director and screenwriter (d. 2005)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Canadian director and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Canadian director and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Co<PERSON>tos"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian politician (d. 1999)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Rai"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Nigerian political scientist, academic, and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian political scientist, academic, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian political scientist, academic, and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American race car driver and inventor", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and inventor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and inventor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English guitarist and songwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1943", "text": "<PERSON>, American historian and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, South Korean author and educator", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-yong\" title=\"<PERSON><PERSON>-yong\"><PERSON><PERSON>-yong</a>, South Korean author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-yong\" title=\"<PERSON><PERSON>-yong\"><PERSON><PERSON>-yo<PERSON></a>, South Korean author and educator", "links": [{"title": "<PERSON><PERSON>ng", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-yong"}]}, {"year": "1944", "text": "<PERSON>, Australian rugby league player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1944", "text": "<PERSON>, New Zealand rugby player (d. 2020)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)\" class=\"mw-redirect\" title=\"<PERSON> (rugby player)\"><PERSON></a>, New Zealand rugby player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)\" class=\"mw-redirect\" title=\"<PERSON> (rugby player)\"><PERSON></a>, New Zealand rugby player (d. 2020)", "links": [{"title": "<PERSON> (rugby player)", "link": "https://wikipedia.org/wiki/<PERSON>_(rugby_player)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish actor, musician and comedian (d. 2022)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Ves<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"Vesa-<PERSON><PERSON>\">V<PERSON><PERSON>-<PERSON><PERSON></a>, Finnish actor, musician and comedian (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ves<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"Vesa-<PERSON><PERSON>\">V<PERSON><PERSON>-<PERSON><PERSON></a>, Finnish actor, musician and comedian (d. 2022)", "links": [{"title": "Vesa-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vesa-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter (d. 2003)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English percussionist, lyricist and music theorist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English percussionist, lyricist and music theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English percussionist, lyricist and music theorist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French sociologist and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>-Th%C3%A9r%C3%A8se_Letablier\" title=\"Marie-Thér<PERSON>e Letablier\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French sociologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-Th%C3%A9r%C3%A8se_Letablier\" title=\"Marie<PERSON>Thér<PERSON>e Letablier\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French sociologist and academic", "links": [{"title": "Marie-Thérèse Letablier", "link": "https://wikipedia.org/wiki/Marie-Th%C3%A9r%C3%A8se_Letablier"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Greek footballer (d. 1992)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Malian civil servant and politician, Prime Minister of Mali (d. 2021)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Ciss%C3%A9_Mariam_Ka%C3%AFdama_Sidib%C3%A9\" title=\"Cissé Maria<PERSON> Kaïdama Sidibé\"><PERSON><PERSON><PERSON></a>, Malian civil servant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mali\" class=\"mw-redirect\" title=\"Prime Minister of Mali\">Prime Minister of Mali</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ciss%C3%A9_Mariam_Ka%C3%AFdama_Sidib%C3%A9\" title=\"Cissé <PERSON> Kaïdama Sidibé\"><PERSON><PERSON><PERSON></a>, Malian civil servant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mali\" class=\"mw-redirect\" title=\"Prime Minister of Mali\">Prime Minister of Mali</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ciss%C3%A9_Mariam_Ka%C3%AFdama_Sidib%C3%A9"}, {"title": "Prime Minister of Mali", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Mali"}]}, {"year": "1949", "text": "<PERSON>, English footballer and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Congolese footballer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tshi<PERSON>\" title=\"Bwang<PERSON> Tshimen\"><PERSON><PERSON><PERSON></a>, Congolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>shi<PERSON>\" title=\"Bwang<PERSON> Tshimen\"><PERSON><PERSON><PERSON></a>, Congolese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladesh poet and academic (d. 2013)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Khondakar_<PERSON><PERSON>_<PERSON>\" title=\"Khondakar Ash<PERSON>f <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladesh poet and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Khondakar_<PERSON><PERSON>_<PERSON>\" title=\"Khondakar Ash<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladesh poet and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Argentinian footballer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian politician, 43rd Premier of South Australia", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 43rd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 43rd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1954", "text": "<PERSON>, American fashion designer, founded House of Deréon", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/House_of_Der%C3%A9on\" title=\"House of Deréon\">House of Deréon</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/House_of_Der%C3%A9on\" title=\"House of Deréon\">House of Deréon</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "House of Deréon", "link": "https://wikipedia.org/wiki/House_of_Der%C3%A9on"}]}, {"year": "1956", "text": "<PERSON>, American jazz saxophonist, songwriter and composer (d. 2011)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz saxophonist, songwriter and composer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz saxophonist, songwriter and composer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Israeli politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Zehava_Gal-On\" class=\"mw-redirect\" title=\"Zehava Gal-On\"><PERSON><PERSON><PERSON>-<PERSON></a>, Israeli politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zehava_Gal-On\" class=\"mw-redirect\" title=\"Zehava Gal-On\"><PERSON><PERSON><PERSON> Gal-On</a>, Israeli politician", "links": [{"title": "Zehava Gal-On", "link": "https://wikipedia.org/wiki/<PERSON>eh<PERSON>_Gal-On"}]}, {"year": "1956", "text": "<PERSON>, American actress and performance artist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and performance artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and performance artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Loveless\" title=\"<PERSON> Loveless\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Loveless\" title=\"Patty Loveless\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American-Canadian actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English actor (d. 2023)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian rugby league player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American basketball player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1961", "text": "<PERSON>, American basketball player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Scottish actor and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian comedian, actor, director, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian, actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian, actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, German singer, songwriter, and poet", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer, songwriter, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer, songwriter, and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, German diver", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, New Zealand squash player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand squash player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand squash player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American actress and athlete", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and athlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, New Zealand rugby league player (d. 2003)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, French tennis player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Guy Forget\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian-English dancer, choreographer, and director", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English dancer, choreographer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English dancer, choreographer, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English actress and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English boxer and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American golfer and philanthropist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian rugby player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1967)\" title=\"<PERSON> (rugby union, born 1967)\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1967)\" title=\"<PERSON> (rugby union, born 1967)\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON> (rugby union, born 1967)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1967)"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lount\" title=\"<PERSON><PERSON> Blount\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Blount\" title=\"<PERSON><PERSON> Blount\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nt"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Dutch footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1971)\" title=\"<PERSON> (rugby league, born 1971)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1971)\" title=\"<PERSON> (rugby league, born 1971)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league, born 1971)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1971)"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1973", "text": "<PERSON>, Danish cyclist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_H%C3%B8j\" title=\"<PERSON>\"><PERSON></a>, Danish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8j\" title=\"<PERSON>\"><PERSON></a>, Danish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_H%C3%B8j"}]}, {"year": "1974", "text": "<PERSON><PERSON>, German cyclist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>do"}]}, {"year": "1975", "text": "<PERSON>, American mixed martial artist and wrestler", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English footballer and physiotherapist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1975)\" title=\"<PERSON> (footballer, born 1975)\"><PERSON></a>, English footballer and physiotherapist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1975)\" title=\"<PERSON> (footballer, born 1975)\"><PERSON></a>, English footballer and physiotherapist", "links": [{"title": "<PERSON> (footballer, born 1975)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1975)"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Slovak tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Domini<PERSON>_Hrbat%C3%BD\" title=\"<PERSON><PERSON><PERSON> Hrbatý\"><PERSON><PERSON><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dom<PERSON><PERSON>_Hrbat%C3%BD\" title=\"<PERSON><PERSON><PERSON> Hrbatý\"><PERSON><PERSON><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dominik_Hrbat%C3%BD"}]}, {"year": "1979", "text": "<PERSON><PERSON>, German footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/She<PERSON>_Biran\" title=\"She<PERSON> Biran\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/She<PERSON>_Biran\" title=\"Shergo Biran\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/She<PERSON>_<PERSON>n"}]}, {"year": "1979", "text": "<PERSON>, French racing driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress and comedian", "html": "1980 - <a href=\"https://wikipedia.org/wiki/D%27A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%27A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%27A<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Portuguese footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, South African cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>tong"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1982)\" title=\"<PERSON> (footballer, born 1982)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1982)\" title=\"<PERSON> (footballer, born 1982)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1982)"}]}, {"year": "1982", "text": "<PERSON>, Australian rugby league player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1982", "text": "<PERSON>, South Korean actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jung\" title=\"<PERSON>ung\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jung\" title=\"<PERSON>jung\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>ung", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jung"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Will Bynum\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_By<PERSON>\" title=\"Will Bynum\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bynum"}]}, {"year": "1983", "text": "<PERSON>, Scottish film, television and theatre actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish film, television and theatre actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish film, television and theatre actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Hudler\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Hudler\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Hudler"}]}, {"year": "1985", "text": "<PERSON><PERSON>, British actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Norwegian handball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Grimsb%C3%B8\" title=\"<PERSON><PERSON> Grimsbø\"><PERSON><PERSON></a>, Norwegian handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Grimsb%C3%B8\" title=\"<PERSON><PERSON>al<PERSON> Grimsbø\"><PERSON><PERSON></a>, Norwegian handball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Grimsb%C3%B8"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/G%C3%B6khan_G%C3%B6n%C3%BCl\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6khan_G%C3%B6n%C3%BCl\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6khan_G%C3%B6n%C3%BCl"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Jefferson"}]}, {"year": "1985", "text": "<PERSON>, South Korean footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yong\" title=\"<PERSON>-ryong\"><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yong\" title=\"<PERSON>yon<PERSON>\"><PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yong"}]}, {"year": "1985", "text": "<PERSON>, English footballer and coach", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Youn%C3%A8s_Kaboul\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Youn%C3%A8s_Kaboul\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Youn%C3%A8s_<PERSON><PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Belarusian decathlete", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Scottish footballer and manager", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, American actor, comedian, musician, and writer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Charly<PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, comedian, musician, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, comedian, musician, and writer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Przemys%C5%82aw_Tyto%C5%84\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Przemys%C5%82aw_Tyto%C5%84\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Przemys%C5%82aw_Tyto%C5%84"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Swiss footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Anest<PERSON>_<PERSON>rgy<PERSON>u\" title=\"Anestis Argyriou\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>est<PERSON> Argyriou\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anestis_Argyriou"}]}, {"year": "1988", "text": "<PERSON>, German footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maximilian_Riedm%C3%BCller"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American race car driver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>ago_Falque\" title=\"Iago Falque\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ago_<PERSON>al<PERSON>\" title=\"Iago Falque\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "Iago <PERSON>", "link": "https://wikipedia.org/wiki/Iago_Falque"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Cuban baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Igles<PERSON>\" title=\"Raisel Iglesias\"><PERSON><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Igles<PERSON>\" title=\"Raisel Iglesias\"><PERSON><PERSON></a>, Cuban baseball player", "links": [{"title": "Raisel Iglesias", "link": "https://wikipedia.org/wiki/Raisel_Iglesias"}]}, {"year": "1990", "text": "<PERSON>, German footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Italian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Dutch footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Quincy_Promes\" title=\"Quincy Promes\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quincy_Promes\" title=\"Quincy Promes\"><PERSON></a>, Dutch footballer", "links": [{"title": "Quincy Promes", "link": "https://wikipedia.org/wiki/Quincy_Promes"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Egyptian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Canadian ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1995)\" title=\"<PERSON> (footballer, born 1995)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1995)\" title=\"<PERSON> (footballer, born 1995)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1995)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1995)"}]}, {"year": "1996", "text": "<PERSON>, Australian gridiron football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, Australian gridiron football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, Australian gridiron football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1996", "text": "<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Danish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Italian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Angeli%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Angeli%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Angeli%C3%B1o"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Croatian basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Ante_%C5%BDi%C5%BEi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ante_%C5%BDi%C5%BEi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ante_%C5%BDi%C5%BEi%C4%87"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Lithuanian basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Argentine footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Filipina actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipina actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipina actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Iranian-Australian footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian-Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian-Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Swiss ice hockey player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_Salmon\" title=\"Jaeman Salmon\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jae<PERSON>_Salmon\" title=\"Jaeman Salmon\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "Jae<PERSON> Salmon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Salmon"}]}, {"year": "1999", "text": "<PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Aaron<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Ivorian footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, English singer and songwriter", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer and songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ell"}]}, {"year": "2004", "text": "<PERSON>, French basketball player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, British-Spanish actress", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British-Spanish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British-Spanish actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>en"}]}, {"year": "2005", "text": "<PERSON>, American basketball player", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Spanish footballer", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "871", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Saxon ealdorman", "html": "871 - <a href=\"https://wikipedia.org/wiki/%C3%86thelwu<PERSON>_of_Berkshire\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Berkshire\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Saxon <a href=\"https://wikipedia.org/wiki/Ealdorman\" title=\"<PERSON>aldo<PERSON>\">ealdo<PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON><PERSON>_of_Berkshire\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Berkshire\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Saxon <a href=\"https://wikipedia.org/wiki/Ealdorman\" title=\"<PERSON>aldorman\">ealdo<PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Berkshire", "link": "https://wikipedia.org/wiki/%C3%86thelwulf_of_Berkshire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "874", "text": "<PERSON>, eleventh of the Twelve Imams (probable; b. 846)", "html": "874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, eleventh of <a href=\"https://wikipedia.org/wiki/The_Twelve_Imams\" class=\"mw-redirect\" title=\"The Twelve Imams\">the Twelve Imams</a> (probable; b. 846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, eleventh of <a href=\"https://wikipedia.org/wiki/The_Twelve_Imams\" class=\"mw-redirect\" title=\"The Twelve Imams\">the Twelve Imams</a> (probable; b. 846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Twelve Imams", "link": "https://wikipedia.org/wiki/The_Twelve_Imams"}]}, {"year": "1248", "text": "<PERSON><PERSON> of Portugal (b. 1209)", "html": "1248 - <a href=\"https://wikipedia.org/wiki/Sancho_II_of_Portugal\" title=\"Sancho II of Portugal\">Sancho II of Portugal</a> (b. 1209)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sancho_II_of_Portugal\" title=\"Sancho II of Portugal\">Sancho II of Portugal</a> (b. 1209)", "links": [{"title": "Sancho II of Portugal", "link": "https://wikipedia.org/wiki/Sancho_II_of_Portugal"}]}, {"year": "1344", "text": "<PERSON>, 1st Baron <PERSON>, English peer (b. 1288)", "html": "1344 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English peer (b. 1288)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English peer (b. 1288)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1399", "text": "<PERSON>, Catalan theologian and inquisitor", "html": "1399 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catalan theologian and inquisitor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catalan theologian and inquisitor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1424", "text": "<PERSON><PERSON>, Italian condottiero (b. 1369)", "html": "1424 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rz<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>rz<PERSON>\"><PERSON><PERSON></a>, Italian condottiero (b. 1369)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rz<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Sforz<PERSON>\"><PERSON><PERSON></a>, Italian condottiero (b. 1369)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1428", "text": "<PERSON>, Elector of Saxony (b. 1370)", "html": "1428 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1370)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1370)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1584", "text": "<PERSON>, Swiss painter and illustrator (b. 1539)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and illustrator (b. 1539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and illustrator (b. 1539)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1604", "text": "<PERSON><PERSON><PERSON>, Hungarian noble (b. 1555)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/Ferenc_N%C3%A1dasdy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian noble (b. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferenc_N%C3%A1dasdy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian noble (b. 1555)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_N%C3%A1dasdy"}]}, {"year": "1695", "text": "<PERSON><PERSON><PERSON>, duc de <PERSON>, French general (b. 1628)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>-<PERSON>,_duc_de_Luxembourg\" title=\"<PERSON><PERSON><PERSON>, duc de Luxembourg\"><PERSON><PERSON><PERSON>, duc de Luxembourg</a>, French general (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>-<PERSON>,_duc_de_Luxembourg\" title=\"<PERSON><PERSON><PERSON>, duc de Luxembourg\"><PERSON><PERSON><PERSON>, duc de <PERSON></a>, French general (b. 1628)", "links": [{"title": "<PERSON><PERSON><PERSON>, duc de Luxembourg", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>,_duc_de_Luxembourg"}]}, {"year": "1761", "text": "<PERSON>, English clergyman and physiologist (b. 1677)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clergyman and physiologist (b. 1677)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clergyman and physiologist (b. 1677)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON><PERSON><PERSON><PERSON>, French architect, designed École Militaire (b. 1698)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French architect, designed <a href=\"https://wikipedia.org/wiki/%C3%89cole_Militaire\" class=\"mw-redirect\" title=\"École Militaire\">École Militaire</a> (b. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French architect, designed <a href=\"https://wikipedia.org/wiki/%C3%89cole_Militaire\" class=\"mw-redirect\" title=\"École Militaire\">École Militaire</a> (b. 1698)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}, {"title": "École Militaire", "link": "https://wikipedia.org/wiki/%C3%89cole_Militaire"}]}, {"year": "1786", "text": "<PERSON>, German philosopher and theologian (b. 1729)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and theologian (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and theologian (b. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, English author and poet (b. 1730)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (b. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charlotte Lennox\"><PERSON></a>, English author and poet (b. 1730)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, American nun and saint (b. 1774)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nun and saint (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nun and saint (b. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON> of the Two Sicilies (b. 1751)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_the_Two_Sicilies\" title=\"<PERSON> I of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies\" title=\"<PERSON> I of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (b. 1751)", "links": [{"title": "<PERSON> of the Two Sicilies", "link": "https://wikipedia.org/wiki/<PERSON>_I_of_the_Two_Sicilies"}]}, {"year": "1863", "text": "<PERSON>, American general (b. 1827)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, English-Australian lawyer and politician, 2nd Premier of Tasmania (b. 1798)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1877", "text": "<PERSON>, American businessman and philanthropist (b. 1794)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, German painter and educator (b. 1829)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>sel<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter and educator (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sel<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter and educator (b. 1829)", "links": [{"title": "<PERSON>sel<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sel<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, English painter and illustrator (b. 1811)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, English-American physician, chemist, and photographer (b. 1811)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physician, chemist, and photographer (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physician, chemist, and photographer (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, French general (b. 1823)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Canadian priest (b. 1833)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian priest (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian priest (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, German bishop and academic (b. 1821)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bishop and academic (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bishop and academic (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Polish-born politician, writer, historian and member of the Imperial Council of Austria (b. 1827)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-born politician, writer, historian and member of the Imperial Council of Austria (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-born politician, writer, historian and member of the Imperial Council of Austria (b. 1827)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American astronomer and academic (b. 1857)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, French pilot and sculptor (b. 1873)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/L%C3%A9on_Delagrange\" title=\"<PERSON>\"><PERSON></a>, French pilot and sculptor (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9on_Delagrange\" title=\"<PERSON>\"><PERSON></a>, French pilot and sculptor (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Delagrange"}]}, {"year": "1912", "text": "<PERSON>, American geologist and soldier (b. 1841)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and soldier (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and soldier (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, German academic and politician, 7th Chancellor of the German Empire (b. 1843)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician, 7th <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_German_Empire\" class=\"mw-redirect\" title=\"Chancellor of the German Empire\">Chancellor of the German Empire</a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician, 7th <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_German_Empire\" class=\"mw-redirect\" title=\"Chancellor of the German Empire\">Chancellor of the German Empire</a> (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the German Empire", "link": "https://wikipedia.org/wiki/Chancellor_of_the_German_Empire"}]}, {"year": "1920", "text": "<PERSON>, Spanish author and playwright (b. 1843)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>%C3%B3s\" title=\"<PERSON>\"><PERSON></a>, Spanish author and playwright (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON>%C3%B3s\" title=\"<PERSON>\"><PERSON></a>, Spanish author and playwright (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Benito_P%C3%A9<PERSON>_Gald%C3%B3s"}]}, {"year": "1924", "text": "<PERSON>, Austrian pianist and composer (b. 1852)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%BCn<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_Gr%C3%BCnfeld"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American nurse, restaurateur, entrepreneur, and gold prospector (b. 1845)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American nurse, restaurateur, entrepreneur, and gold prospector (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American nurse, restaurateur, entrepreneur, and gold prospector (b. 1845)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish poet and civil servant (b. 1870)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_Nazif\" title=\"Süleyman Nazif\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish poet and civil servant (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_Nazif\" title=\"Süleyman Nazif\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish poet and civil servant (b. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%BCleyman_Nazif"}]}, {"year": "1931", "text": "<PERSON>, American actor and stuntman (b. 1890)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Art_Acord\" title=\"Art Acord\">Art <PERSON>d</a>, American actor and stuntman (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Acord\" title=\"Art Acord\"><PERSON>d</a>, American actor and stuntman (b. 1890)", "links": [{"title": "Art Acord", "link": "https://wikipedia.org/wiki/Art_Acord"}]}, {"year": "1931", "text": "<PERSON>, Princess <PERSON> of the United Kingdom (b. 1867)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_<PERSON>\" title=\"<PERSON>, Princess <PERSON>\"><PERSON>, Princess <PERSON></a> of the United Kingdom (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_<PERSON>\" title=\"<PERSON>, Princess <PERSON>\"><PERSON>, Princess <PERSON></a> of the United Kingdom (b. 1867)", "links": [{"title": "<PERSON>, Princess <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Indian Muslim activist (b. 1878)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian Muslim activist (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian Muslim activist (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, French philosopher and academic, Nobel Prize laureate (b. 1859)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Greek-Polish swimmer and water polo player (b. 1911)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>icz\" title=\"<PERSON><PERSON><PERSON>owicz\"><PERSON><PERSON><PERSON></a>, Greek-Polish swimmer and water polo player (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Polish swimmer and water polo player (b. 1911)", "links": [{"title": "Jerzy Iwanow-Szajnowicz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Iwanow-<PERSON>owicz"}]}, {"year": "1943", "text": "<PERSON>, Russian pilot and navigator (b. 1912)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Marina_Ra<PERSON>va\" title=\"<PERSON>\"><PERSON></a>, Russian pilot and navigator (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marina_Ra<PERSON>va\" title=\"<PERSON>\"><PERSON></a>, Russian pilot and navigator (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Raskova"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Danish playwright and pastor (b. 1898)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish playwright and pastor (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish playwright and pastor (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON>, French novelist, philosopher, and journalist, Nobel Prize laureate (b. 1913)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, philosopher, and journalist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, philosopher, and journalist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1961", "text": "<PERSON>, Austrian physicist and academic, Nobel Prize laureate (b. 1887)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6dinger\" title=\"<PERSON>\"><PERSON></a>, Austrian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6dinger\" title=\"<PERSON>\"><PERSON></a>, Austrian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Erwin_Schr%C3%B6dinger"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1965", "text": "<PERSON><PERSON> <PERSON><PERSON>, American-English poet, playwright, and critic, Nobel Prize laureate (b. 1888)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"T. S. Eliot\"><PERSON><PERSON> <PERSON><PERSON></a>, American-English poet, playwright, and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"T. S<PERSON> Eliot\"><PERSON><PERSON> <PERSON><PERSON></a>, American-English poet, playwright, and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1888)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1967", "text": "<PERSON>, English racing driver and world speed record holder (b. 1921)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and world speed record holder (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and world speed record holder (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American bassist and composer (b. 1935)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Italian painter, author, and activist (b. 1902)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter, author, and activist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter, author, and activist (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Indian-English general (b. 1895)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English general (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English general (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English-American author and academic (b. 1904)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author and academic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author and academic (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Irish singer-songwriter, bass player, and producer (b. 1949)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, bass player, and producer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, bass player, and producer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, French harp player (b. 1893)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French harp player (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French harp player (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American engineer and academic (b. 1903)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian politician, 38th Premier of Victoria (b. 1908)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 38th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 38th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1994", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian film composer and music director (b. 1939)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/R._<PERSON>._Burman\" title=\"R. D. Burman\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian film composer and music director (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R._<PERSON>._Burman\" title=\"R. D. Burman\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian film composer and music director (b. 1939)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1995", "text": "<PERSON>, Mexican conductor and composer (b. 1942)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican conductor and composer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican conductor and composer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American anthropologist and academic (b. 1907)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Sol_Tax\" title=\"Sol Tax\">Sol Tax</a>, American anthropologist and academic (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sol_Tax\" title=\"Sol Tax\">Sol Tax</a>, American anthropologist and academic (b. 1907)", "links": [{"title": "Sol Tax", "link": "https://wikipedia.org/wiki/Sol_Tax"}]}, {"year": "1997", "text": "<PERSON>, American businessman (b. 1909)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actress (b. 1908)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Questel\"><PERSON></a>, American actress (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mae Questel\"><PERSON></a>, American actress (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mae_Questel"}]}, {"year": "1999", "text": "<PERSON> Eyes <PERSON>, American actor and stuntman (b. 1904)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Iron_Eyes_Cody\" title=\"Iron Eyes Cody\"><PERSON> Eyes <PERSON></a>, American actor and stuntman (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iron_Eyes_Cody\" title=\"Iron Eyes Cody\"><PERSON> Eyes <PERSON></a>, American actor and stuntman (b. 1904)", "links": [{"title": "Iron Eyes Cody", "link": "https://wikipedia.org/wiki/Iron_Eyes_Cody"}]}, {"year": "2001", "text": "<PERSON>, American bandleader and composer (b. 1912)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)\" title=\"<PERSON> (bandleader)\"><PERSON></a>, American bandleader and composer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)\" title=\"<PERSON> (bandleader)\"><PERSON></a>, American bandleader and composer (b. 1912)", "links": [{"title": "<PERSON> (bandleader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)"}]}, {"year": "2004", "text": "<PERSON>, English director and screenwriter (b. 1944)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and screenwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and screenwriter (b. 1944)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_(director)"}]}, {"year": "2004", "text": "<PERSON>, English author (b. 1924)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American historian and author (b. 1912)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, American historian and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, American historian and author (b. 1912)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)"}]}, {"year": "2005", "text": "<PERSON>, Canadian ice hockey player, coach, and manager (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bud Poile\"><PERSON></a>, Canadian ice hockey player, coach, and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bud Poile\"><PERSON></a>, Canadian ice hockey player, coach, and manager (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bud_Poile"}]}, {"year": "2005", "text": "<PERSON>, American mathematician and academic (b. 1921)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English radio host and author (b. 1946)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio host and author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio host and author (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American economist and historian (b. 1919)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and historian (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and historian (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Romanian-Canadian poet and academic (b. 1912)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Canadian poet and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Canadian poet and academic (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Emirati politician, 1st Prime Minister of the United Arab Emirates (b. 1946)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_bin_<PERSON>_<PERSON>ktou<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON><PERSON> bin <PERSON></a>, Emirati politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Arab_Emirates\" class=\"mw-redirect\" title=\"Prime Minister of the United Arab Emirates\">Prime Minister of the United Arab Emirates</a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_bin_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON><PERSON> bin <PERSON></a>, Emirati politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Arab_Emirates\" class=\"mw-redirect\" title=\"Prime Minister of the United Arab Emirates\">Prime Minister of the United Arab Emirates</a> (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_bin_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Arab Emirates", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Arab_Emirates"}]}, {"year": "2006", "text": "<PERSON>, American sociographer, author, and academic (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociographer, author, and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociographer, author, and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>farb"}]}, {"year": "2007", "text": "<PERSON>, American director and producer (b. 1970)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, South African politician, 5th State President of South Africa (b. 1915)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African politician, 5th <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African politician, 5th <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>il<PERSON>en"}, {"title": "State President of South Africa", "link": "https://wikipedia.org/wiki/State_President_of_South_Africa"}]}, {"year": "2008", "text": "<PERSON>, Nicaraguan journalist (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan journalist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan journalist (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Xavier_<PERSON>ro_Cardenal"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Austrian poet, playwright, and author (b. 1946)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian poet, playwright, and author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian poet, playwright, and author (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Surinamese educator and politician, 1st President of Suriname (b. 1910)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese educator and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Suriname\" title=\"President of Suriname\">President of Suriname</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese educator and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Suriname\" title=\"President of Suriname\">President of Suriname</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Suriname", "link": "https://wikipedia.org/wiki/President_of_Suriname"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese engineer (b. 1916)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese engineer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese engineer (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Dutch footballer (b. 1937)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Scottish singer-songwriter (b. 1947)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Pakistani businessman and politician, 26th Governor of Punjab, Pakistan (b. 1944)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani businessman and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Punjab,_Pakistan\" title=\"Governor of Punjab, Pakistan\">Governor of Punjab, Pakistan</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani businessman and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Punjab,_Pakistan\" title=\"Governor of Punjab, Pakistan\">Governor of Punjab, Pakistan</a> (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r"}, {"title": "Governor of Punjab, Pakistan", "link": "https://wikipedia.org/wiki/Governor_of_Punjab,_Pakistan"}]}, {"year": "2012", "text": "<PERSON>, American photographer and journalist (b. 1912)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English-Canadian architect, designed the Canadian Pavilion and Rogers Centre (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian architect, designed the <a href=\"https://wikipedia.org/wiki/Canadian_Pavilion\" title=\"Canadian Pavilion\">Canadian Pavilion</a> and <a href=\"https://wikipedia.org/wiki/Rogers_Centre\" title=\"Rogers Centre\">Rogers Centre</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian architect, designed the <a href=\"https://wikipedia.org/wiki/Canadian_Pavilion\" title=\"Canadian Pavilion\">Canadian Pavilion</a> and <a href=\"https://wikipedia.org/wiki/Rogers_Centre\" title=\"Rogers Centre\">Rogers Centre</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Canadian Pavilion", "link": "https://wikipedia.org/wiki/Canadian_Pavilion"}, {"title": "Rogers Centre", "link": "https://wikipedia.org/wiki/Rogers_Centre"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Pakistani general (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani general (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani general (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Montenegrin politician, 4th Prime Minister of the Federal Republic of Yugoslavia (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>oran_%C5%BDi%C5%BEi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Montenegrin politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Prime Minister of the Federal Republic of Yugoslavia\">Prime Minister of the Federal Republic of Yugoslavia</a> (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>oran_%C5%BDi%C5%BEi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Montenegrin politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Prime Minister of the Federal Republic of Yugoslavia\">Prime Minister of the Federal Republic of Yugoslavia</a> (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zoran_%C5%BDi%C5%BEi%C4%87"}, {"title": "Prime Minister of the Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Federal_Republic_of_Yugoslavia"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Italian singer-songwriter and guitarist (b. 1955)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (b. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>no_Daniele"}]}, {"year": "2016", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer, judge, and politician, 38th Chief Justice of India (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON>\" title=\"S. <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer, judge, and politician, 38th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON>\" title=\"S. <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer, judge, and politician, 38th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a> (b. 1947)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON><PERSON>ia"}, {"title": "Chief Justice of India", "link": "https://wikipedia.org/wiki/Chief_Justice_of_India"}]}, {"year": "2016", "text": "<PERSON>, American academic and diplomat, United States Ambassador to South Korea (b. 1939)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_South_Korea\" class=\"mw-redirect\" title=\"United States Ambassador to South Korea\">United States Ambassador to South Korea</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_South_Korea\" class=\"mw-redirect\" title=\"United States Ambassador to South Korea\">United States Ambassador to South Korea</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to South Korea", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_South_Korea"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Canadian ice hockey player, coach and general manager (b. 1918)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, coach and general manager (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, coach and general manager (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, French orchestral and opera conductor (b. 1924)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%AAtre\" title=\"<PERSON>\"><PERSON></a>, French orchestral and opera conductor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%AAtre\" title=\"<PERSON>\"><PERSON></a>, French orchestral and opera conductor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georges_Pr%C3%AAtre"}]}, {"year": "2019", "text": "<PERSON>, 14th United States Secretary of Defense (b. 1927)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Secretary_of_Defense)\" title=\"<PERSON> (Secretary of Defense)\"><PERSON></a>, 14th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Secretary_of_Defense)\" title=\"<PERSON> (Secretary of Defense)\"><PERSON></a>, 14th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (b. 1927)", "links": [{"title": "<PERSON> (Secretary of Defense)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Secretary_of_Defense)"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "2020", "text": "<PERSON>, Australian actor (b. 1968)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Australian actor (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Australian actor (b. 1968)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2021", "text": "<PERSON>, American actress (b. 1949)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, German alpine skier and Olympic champion (b. 1950)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German alpine skier and Olympic champion (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German alpine skier and Olympic champion (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, British actress and singer (b. 1923)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British actress and singer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British actress and singer (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American-British actor and singer (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David Soul\"><PERSON></a>, American-British actor and singer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David Soul\"><PERSON></a>, American-British actor and singer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, German actor (b. 1972)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Oliver\"><PERSON></a>, German actor (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}