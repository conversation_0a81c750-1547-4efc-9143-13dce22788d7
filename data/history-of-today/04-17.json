{"date": "April 17", "url": "https://wikipedia.org/wiki/April_17", "data": {"Events": [{"year": "1080", "text": "<PERSON> of Denmark dies and is succeeded by <PERSON><PERSON>, who would later be the first Dane to be canonized.", "html": "1080 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Denmark\" class=\"mw-redirect\" title=\"<PERSON> III of Denmark\"><PERSON> of Denmark</a> dies and is succeeded by <a href=\"https://wikipedia.org/wiki/Canute_IV_of_Denmark\" title=\"Canute IV of Denmark\"><PERSON><PERSON> IV</a>, who would later be the first Dane to be <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonized</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Denmark\" class=\"mw-redirect\" title=\"Harald III of Denmark\"><PERSON> of Denmark</a> dies and is succeeded by <a href=\"https://wikipedia.org/wiki/Canute_IV_of_Denmark\" title=\"Canute IV of Denmark\"><PERSON><PERSON> IV</a>, who would later be the first Dane to be <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonized</a>.", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark"}, {"title": "Canute IV of Denmark", "link": "https://wikipedia.org/wiki/Canute_IV_of_Denmark"}, {"title": "Canonization", "link": "https://wikipedia.org/wiki/Canonization"}]}, {"year": "1349", "text": "The rule of the Bavand dynasty in Mazandaran is brought to an end by the murder of <PERSON>.", "html": "1349 - The rule of the <a href=\"https://wikipedia.org/wiki/Bavand_dynasty\" title=\"Bavand dynasty\">Bavand dynasty</a> in <a href=\"https://wikipedia.org/wiki/Mazandaran\" class=\"mw-redirect\" title=\"Mazandaran\">Mazandaran</a> is brought to an end by the murder of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bavandid_ruler)\" title=\"<PERSON> II (Bavandid ruler)\"><PERSON> II</a>.", "no_year_html": "The rule of the <a href=\"https://wikipedia.org/wiki/Bavand_dynasty\" title=\"Bavand dynasty\">Bavand dynasty</a> in <a href=\"https://wikipedia.org/wiki/Mazandaran\" class=\"mw-redirect\" title=\"Mazandaran\">Mazandaran</a> is brought to an end by the murder of <a href=\"https://wikipedia.org/wiki/<PERSON>_II_(Bavandid_ruler)\" title=\"<PERSON> II (Bavandid ruler)\"><PERSON> II</a>.", "links": [{"title": "Bavand dynasty", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_dynasty"}, {"title": "Mazandaran", "link": "https://wikipedia.org/wiki/Mazandaran"}, {"title": "<PERSON> (Bavandid ruler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON><PERSON><PERSON><PERSON>_ruler)"}]}, {"year": "1362", "text": "Kaunas Castle falls to the Teutonic Order after a month-long siege.", "html": "1362 - <a href=\"https://wikipedia.org/wiki/Kaunas_Castle\" title=\"Kaunas Castle\">Kaunas Castle</a> falls to the <a href=\"https://wikipedia.org/wiki/Teutonic_Order\" title=\"Teutonic Order\">Teutonic Order</a> after a <a href=\"https://wikipedia.org/wiki/Siege_of_Kaunas_(1362)\" title=\"Siege of Kaunas (1362)\">month-long siege</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kaunas_Castle\" title=\"Kaunas Castle\">Kaunas Castle</a> falls to the <a href=\"https://wikipedia.org/wiki/Teutonic_Order\" title=\"Teutonic Order\">Teutonic Order</a> after a <a href=\"https://wikipedia.org/wiki/Siege_of_Kaunas_(1362)\" title=\"Siege of Kaunas (1362)\">month-long siege</a>.", "links": [{"title": "Kaunas Castle", "link": "https://wikipedia.org/wiki/Kaunas_Castle"}, {"title": "Teutonic Order", "link": "https://wikipedia.org/wiki/Teutonic_Order"}, {"title": "Siege of Kaunas (1362)", "link": "https://wikipedia.org/wiki/Siege_of_Kaunas_(1362)"}]}, {"year": "1492", "text": "Spain and <PERSON> sign the Capitulations of Santa Fe for his voyage to Asia to acquire spices.", "html": "1492 - Spain and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Capitulations_of_Santa_Fe\" title=\"Capitulations of Santa Fe\">Capitulations of Santa Fe</a> for his voyage to Asia to acquire <a href=\"https://wikipedia.org/wiki/Spice\" title=\"Spice\">spices</a>.", "no_year_html": "Spain and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Capitulations_of_Santa_Fe\" title=\"Capitulations of Santa Fe\">Capitulations of Santa Fe</a> for his voyage to Asia to acquire <a href=\"https://wikipedia.org/wiki/Spice\" title=\"Spice\">spices</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Capitulations of Santa Fe", "link": "https://wikipedia.org/wiki/Capitulations_of_Santa_Fe"}, {"title": "Spice", "link": "https://wikipedia.org/wiki/Spice"}]}, {"year": "1521", "text": "Trial of <PERSON> over his teachings begins during the assembly of the Diet of Worms. Initially intimidated, he asks for time to reflect before answering and is given a stay of one day.", "html": "1521 - Trial of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> over his <a href=\"https://wikipedia.org/wiki/Lutheranism\" title=\"Lutheranism\">teachings</a> begins during the assembly of the <a href=\"https://wikipedia.org/wiki/Diet_of_Worms\" title=\"Diet of Worms\">Diet of Worms</a>. Initially intimidated, he asks for time to reflect before answering and is given a stay of one day.", "no_year_html": "Trial of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> over his <a href=\"https://wikipedia.org/wiki/Lutheranism\" title=\"Lutheranism\">teachings</a> begins during the assembly of the <a href=\"https://wikipedia.org/wiki/Diet_of_Worms\" title=\"Diet of Worms\">Diet of Worms</a>. Initially intimidated, he asks for time to reflect before answering and is given a stay of one day.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lutheranism", "link": "https://wikipedia.org/wiki/Lutheranism"}, {"title": "Diet of Worms", "link": "https://wikipedia.org/wiki/Diet_of_Worms"}]}, {"year": "1524", "text": "<PERSON>errazzano reaches New York harbor.", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Verrazzano\"><PERSON> Verrazzano</a> reaches New York harbor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Verrazzano\"><PERSON>errazzano</a> reaches New York harbor.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "Sir <PERSON> attacks San Juan, Puerto Rico, in what would be one of the largest invasions of the Spanish territories in the Americas.", "html": "1797 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_San_Juan_(1797)\" title=\"Battle of San Juan (1797)\">attacks</a> <a href=\"https://wikipedia.org/wiki/San_Juan,_Puerto_Rico\" title=\"San Juan, Puerto Rico\">San Juan, Puerto Rico</a>, in what would be one of the largest invasions of the Spanish territories in the Americas.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_San_Juan_(1797)\" title=\"Battle of San Juan (1797)\">attacks</a> <a href=\"https://wikipedia.org/wiki/San_Juan,_Puerto_Rico\" title=\"San Juan, Puerto Rico\">San Juan, Puerto Rico</a>, in what would be one of the largest invasions of the Spanish territories in the Americas.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Battle of San Juan (1797)", "link": "https://wikipedia.org/wiki/Battle_of_San_Juan_(1797)"}, {"title": "San Juan, Puerto Rico", "link": "https://wikipedia.org/wiki/San_Juan,_Puerto_Rico"}]}, {"year": "1797", "text": "Citizens of Verona begin an unsuccessful eight-day rebellion against the French occupying forces.", "html": "1797 - Citizens of <a href=\"https://wikipedia.org/wiki/Verona\" title=\"Verona\">Verona</a> begin an unsuccessful <a href=\"https://wikipedia.org/wiki/Veronese_Easter\" title=\"Veronese Easter\">eight-day rebellion</a> against the <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French</a> occupying forces.", "no_year_html": "Citizens of <a href=\"https://wikipedia.org/wiki/Verona\" title=\"Verona\">Verona</a> begin an unsuccessful <a href=\"https://wikipedia.org/wiki/Veronese_Easter\" title=\"Veronese Easter\">eight-day rebellion</a> against the <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French</a> occupying forces.", "links": [{"title": "Verona", "link": "https://wikipedia.org/wiki/Verona"}, {"title": "Veronese <PERSON>", "link": "https://wikipedia.org/wiki/Veron<PERSON>_Easter"}, {"title": "French First Republic", "link": "https://wikipedia.org/wiki/French_First_Republic"}]}, {"year": "1861", "text": "The state of Virginia's secession convention votes to secede from the United States; Virginia later becomes the eighth state to join the Confederate States of America.", "html": "1861 - The state of <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>'s <a href=\"https://wikipedia.org/wiki/Virginia_in_the_American_Civil_War#Secession_convention\" title=\"Virginia in the American Civil War\">secession convention</a> votes to secede from the United States; Virginia later becomes the eighth state to join the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate States of America</a>.", "no_year_html": "The state of <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>'s <a href=\"https://wikipedia.org/wiki/Virginia_in_the_American_Civil_War#Secession_convention\" title=\"Virginia in the American Civil War\">secession convention</a> votes to secede from the United States; Virginia later becomes the eighth state to join the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate States of America</a>.", "links": [{"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "Virginia in the American Civil War", "link": "https://wikipedia.org/wiki/Virginia_in_the_American_Civil_War#Secession_convention"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1863", "text": "American Civil War: <PERSON><PERSON><PERSON>'s Raid begins: Troops under Union Army Colonel <PERSON> attack central Mississippi.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_Raid\" title=\"<PERSON><PERSON><PERSON>'s Raid\"><PERSON><PERSON><PERSON>'s Raid</a> begins: Troops under <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a> Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> attack central <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_Raid\" title=\"<PERSON><PERSON><PERSON>'s Raid\"><PERSON><PERSON><PERSON>'s Raid</a> begins: Troops under <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a> Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> attack central <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON><PERSON><PERSON>'s Raid", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_Raid"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}]}, {"year": "1864", "text": "American Civil War: The Battle of Plymouth begins: Confederate forces attack Plymouth, North Carolina.", "html": "1864 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Plymouth_(1864)\" title=\"Battle of Plymouth (1864)\">Battle of Plymouth</a> begins: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces attack <a href=\"https://wikipedia.org/wiki/Plymouth,_North_Carolina\" title=\"Plymouth, North Carolina\">Plymouth, North Carolina</a>.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Plymouth_(1864)\" title=\"Battle of Plymouth (1864)\">Battle of Plymouth</a> begins: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces attack <a href=\"https://wikipedia.org/wiki/Plymouth,_North_Carolina\" title=\"Plymouth, North Carolina\">Plymouth, North Carolina</a>.", "links": [{"title": "Battle of Plymouth (1864)", "link": "https://wikipedia.org/wiki/Battle_of_Plymouth_(1864)"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Plymouth, North Carolina", "link": "https://wikipedia.org/wiki/Plymouth,_North_Carolina"}]}, {"year": "1869", "text": "Morelos is admitted as the 27th state of Mexico.", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Morelos\" title=\"Morelos\">Morelos</a> is admitted as the 27th state of <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexico</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morelos\" title=\"Morelos\">Morelos</a> is admitted as the 27th state of <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexico</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>los"}, {"title": "Mexico", "link": "https://wikipedia.org/wiki/Mexico"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON> rescue: The rescue of six Fenian prisoners from Fremantle Prison in Western Australia.", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Catalpa_rescue\" title=\"Catalpa rescue\">Catalpa rescue</a>: The rescue of six Fenian prisoners from <a href=\"https://wikipedia.org/wiki/Fremantle_Prison\" title=\"Fremantle Prison\">Fremantle Prison</a> in Western Australia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Catalpa_rescue\" title=\"Catalpa rescue\">Catalpa rescue</a>: The rescue of six Fenian prisoners from <a href=\"https://wikipedia.org/wiki/Fremantle_Prison\" title=\"Fremantle Prison\">Fremantle Prison</a> in Western Australia.", "links": [{"title": "Catalpa rescue", "link": "https://wikipedia.org/wiki/Catalpa_rescue"}, {"title": "Fremantle Prison", "link": "https://wikipedia.org/wiki/Fremantle_Prison"}]}, {"year": "1895", "text": "The Treaty of Shimonoseki between China and Japan is signed. This marks the end of the First Sino-Japanese War, and the defeated Qing Empire is forced to renounce its claims on Korea and to concede the southern portion of the Fengtian province, Taiwan and the Penghu to Japan.", "html": "1895 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Shimonoseki\" title=\"Treaty of Shimonoseki\">Treaty of Shimonoseki</a> between China and Japan is signed. This marks the end of the <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a>, and the defeated <a href=\"https://wikipedia.org/wiki/Qing_Empire\" class=\"mw-redirect\" title=\"Qing Empire\">Qing Empire</a> is forced to renounce its claims on Korea and to concede the southern portion of the <a href=\"https://wikipedia.org/wiki/Liaoning\" title=\"Liaoning\">Fengtian</a> province, <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> and the <a href=\"https://wikipedia.org/wiki/Penghu\" title=\"Penghu\">Penghu</a> to Japan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Shimonoseki\" title=\"Treaty of Shimonoseki\">Treaty of Shimonoseki</a> between China and Japan is signed. This marks the end of the <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a>, and the defeated <a href=\"https://wikipedia.org/wiki/Qing_Empire\" class=\"mw-redirect\" title=\"Qing Empire\">Qing Empire</a> is forced to renounce its claims on Korea and to concede the southern portion of the <a href=\"https://wikipedia.org/wiki/Liaoning\" title=\"Liaoning\">Fengtian</a> province, <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> and the <a href=\"https://wikipedia.org/wiki/Penghu\" title=\"Penghu\">Penghu</a> to Japan.", "links": [{"title": "Treaty of Shimonoseki", "link": "https://wikipedia.org/wiki/Treaty_of_Shimonoseki"}, {"title": "First Sino-Japanese War", "link": "https://wikipedia.org/wiki/First_Sino-Japanese_War"}, {"title": "Qing Empire", "link": "https://wikipedia.org/wiki/Qing_Empire"}, {"title": "Liaoning", "link": "https://wikipedia.org/wiki/Liaoning"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Penghu"}]}, {"year": "1905", "text": "The Supreme Court of the United States decides <PERSON><PERSON> v. New York, which holds that the \"right to free contract\" is implicit in the due process clause of the Fourteenth Amendment to the United States Constitution.", "html": "1905 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> decides <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v._New_York\" title=\"<PERSON><PERSON> v. New York\"><PERSON><PERSON> v. New York</a></i>, which holds that the \"<a href=\"https://wikipedia.org/wiki/Freedom_of_contract\" title=\"Freedom of contract\">right to free contract</a>\" is implicit in the <a href=\"https://wikipedia.org/wiki/Due_process\" title=\"Due process\">due process clause</a> of the <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">Fourteenth Amendment to the United States Constitution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> decides <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v._New_York\" title=\"<PERSON><PERSON> v. New York\"><PERSON><PERSON> v. New York</a></i>, which holds that the \"<a href=\"https://wikipedia.org/wiki/Freedom_of_contract\" title=\"Freedom of contract\">right to free contract</a>\" is implicit in the <a href=\"https://wikipedia.org/wiki/Due_process\" title=\"Due process\">due process clause</a> of the <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">Fourteenth Amendment to the United States Constitution</a>.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "<PERSON><PERSON> v. New York", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_v._New_York"}, {"title": "Freedom of contract", "link": "https://wikipedia.org/wiki/Freedom_of_contract"}, {"title": "Due process", "link": "https://wikipedia.org/wiki/Due_process"}, {"title": "Fourteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution"}]}, {"year": "1907", "text": "The Ellis Island immigration center processes 11,747 people, more than on any other day.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/Ellis_Island\" title=\"Ellis Island\">Ellis Island</a> immigration center processes 11,747 people, more than on any other day.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ellis_Island\" title=\"Ellis Island\">Ellis Island</a> immigration center processes 11,747 people, more than on any other day.", "links": [{"title": "Ellis Island", "link": "https://wikipedia.org/wiki/Ellis_Island"}]}, {"year": "1912", "text": "Russian troops open fire on striking goldfield workers in northeast Siberia, killing at least 150.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian</a> troops <a href=\"https://wikipedia.org/wiki/Lena_massacre\" title=\"Lena massacre\">open fire</a> on striking goldfield workers in northeast <a href=\"https://wikipedia.org/wiki/Siberia\" title=\"Siberia\">Siberia</a>, killing at least 150.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian</a> troops <a href=\"https://wikipedia.org/wiki/Lena_massacre\" title=\"Lena massacre\">open fire</a> on striking goldfield workers in northeast <a href=\"https://wikipedia.org/wiki/Siberia\" title=\"Siberia\">Siberia</a>, killing at least 150.", "links": [{"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Lena massacre", "link": "https://wikipedia.org/wiki/Lena_massacre"}, {"title": "Siberia", "link": "https://wikipedia.org/wiki/Siberia"}]}, {"year": "1931", "text": "After negotiations between Catalan and Spanish provisional governments, the Catalan Republic proclaimed in April 14 becomes the Generalitat de Catalunya, the autonomous government of Catalonia within the Spanish Republic.", "html": "1931 - After negotiations between Catalan and Spanish provisional governments, the <a href=\"https://wikipedia.org/wiki/Catalan_Republic_(1931)\" title=\"Catalan Republic (1931)\">Catalan Republic</a> proclaimed in April 14 becomes the <a href=\"https://wikipedia.org/wiki/Generalitat_de_Catalunya\" title=\"Generalitat de Catalunya\">Generalitat de Catalunya</a>, the autonomous government of <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a> within the <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spanish Republic</a>.", "no_year_html": "After negotiations between Catalan and Spanish provisional governments, the <a href=\"https://wikipedia.org/wiki/Catalan_Republic_(1931)\" title=\"Catalan Republic (1931)\">Catalan Republic</a> proclaimed in April 14 becomes the <a href=\"https://wikipedia.org/wiki/Generalitat_de_Catalunya\" title=\"Generalitat de Catalunya\">Generalitat de Catalunya</a>, the autonomous government of <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a> within the <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spanish Republic</a>.", "links": [{"title": "Catalan Republic (1931)", "link": "https://wikipedia.org/wiki/Catalan_Republic_(1931)"}, {"title": "Generalitat de Catalunya", "link": "https://wikipedia.org/wiki/Generalitat_de_Catalunya"}, {"title": "Catalonia", "link": "https://wikipedia.org/wiki/Catalonia"}, {"title": "Second Spanish Republic", "link": "https://wikipedia.org/wiki/Second_Spanish_Republic"}]}, {"year": "1941", "text": "World War II: The Axis powers invasion of Yugoslavia is completed when it signs an armistice with Germany and Italy.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Axis powers <a href=\"https://wikipedia.org/wiki/Invasion_of_Yugoslavia\" title=\"Invasion of Yugoslavia\">invasion of Yugoslavia</a> is completed when it signs an armistice with Germany and Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Axis powers <a href=\"https://wikipedia.org/wiki/Invasion_of_Yugoslavia\" title=\"Invasion of Yugoslavia\">invasion of Yugoslavia</a> is completed when it signs an armistice with Germany and Italy.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Invasion of Yugoslavia", "link": "https://wikipedia.org/wiki/Invasion_of_Yugoslavia"}]}, {"year": "1942", "text": "French prisoner of war General <PERSON> escapes from his castle prison in Königstein Fortress.", "html": "1942 - French <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">prisoner of war</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes from his castle prison in <a href=\"https://wikipedia.org/wiki/K%C3%B6nigstein_Fortress\" title=\"Königstein Fortress\">Königstein Fortress</a>.", "no_year_html": "French <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">prisoner of war</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes from his castle prison in <a href=\"https://wikipedia.org/wiki/K%C3%B6nigstein_Fortress\" title=\"Königstein Fortress\">Königstein Fortress</a>.", "links": [{"title": "Prisoner of war", "link": "https://wikipedia.org/wiki/Prisoner_of_war"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Königstein Fortress", "link": "https://wikipedia.org/wiki/K%C3%B6nigstein_Fortress"}]}, {"year": "1944", "text": "Forces of the Communist-controlled Greek People's Liberation Army attack the smaller National and Social Liberation resistance group, which surrenders. Its leader <PERSON><PERSON> is murdered.", "html": "1944 - Forces of the Communist-controlled <a href=\"https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army\" class=\"mw-redirect\" title=\"Greek People's Liberation Army\">Greek People's Liberation Army</a> attack the smaller <a href=\"https://wikipedia.org/wiki/National_and_Social_Liberation\" title=\"National and Social Liberation\">National and Social Liberation</a> resistance group, which surrenders. Its leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is murdered.", "no_year_html": "Forces of the Communist-controlled <a href=\"https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army\" class=\"mw-redirect\" title=\"Greek People's Liberation Army\">Greek People's Liberation Army</a> attack the smaller <a href=\"https://wikipedia.org/wiki/National_and_Social_Liberation\" title=\"National and Social Liberation\">National and Social Liberation</a> resistance group, which surrenders. Its leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is murdered.", "links": [{"title": "Greek People's Liberation Army", "link": "https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army"}, {"title": "National and Social Liberation", "link": "https://wikipedia.org/wiki/National_and_Social_Liberation"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "World War II: Montese, Italy, is liberated from Nazi forces.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">Montese</a>, Italy, is liberated from <a href=\"https://wikipedia.org/wiki/Nazi\" class=\"mw-redirect\" title=\"Nazi\">Nazi</a> forces.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">Montese</a>, Italy, is liberated from <a href=\"https://wikipedia.org/wiki/Nazi\" class=\"mw-redirect\" title=\"Nazi\">Nazi</a> forces.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Nazi", "link": "https://wikipedia.org/wiki/Nazi"}]}, {"year": "1945", "text": "Historian <PERSON><PERSON> is appointed the Prime Minister of the Empire of Vietnam.", "html": "1945 - Historian <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rong_<PERSON>\" class=\"mw-redirect\" title=\"Tran Trong Kim\"><PERSON><PERSON></a> is appointed the Prime Minister of the <a href=\"https://wikipedia.org/wiki/Empire_of_Vietnam\" title=\"Empire of Vietnam\">Empire of Vietnam</a>.", "no_year_html": "Historian <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rong_<PERSON>\" class=\"mw-redirect\" title=\"Tran Trong Kim\"><PERSON><PERSON></a> is appointed the Prime Minister of the <a href=\"https://wikipedia.org/wiki/Empire_of_Vietnam\" title=\"Empire of Vietnam\">Empire of Vietnam</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON>_Trong_<PERSON>"}, {"title": "Empire of Vietnam", "link": "https://wikipedia.org/wiki/Empire_of_Vietnam"}]}, {"year": "1946", "text": "The last French troops are withdrawn from Syria.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Evacuation_Day_(Syria)\" title=\"Evacuation Day (Syria)\">last French troops are withdrawn</a> from <a href=\"https://wikipedia.org/wiki/Mandatory_Syrian_Republic\" class=\"mw-redirect\" title=\"Mandatory Syrian Republic\">Syria</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Evacuation_Day_(Syria)\" title=\"Evacuation Day (Syria)\">last French troops are withdrawn</a> from <a href=\"https://wikipedia.org/wiki/Mandatory_Syrian_Republic\" class=\"mw-redirect\" title=\"Mandatory Syrian Republic\">Syria</a>.", "links": [{"title": "Evacuation Day (Syria)", "link": "https://wikipedia.org/wiki/Evacuation_Day_(Syria)"}, {"title": "Mandatory Syrian Republic", "link": "https://wikipedia.org/wiki/Mandatory_Syrian_Republic"}]}, {"year": "1951", "text": "The Peak District becomes the United Kingdom's first National Park.", "html": "1951 - The <a href=\"https://wikipedia.org/wiki/Peak_District\" title=\"Peak District\">Peak District</a> becomes the United Kingdom's first <a href=\"https://wikipedia.org/wiki/National_parks_of_England_and_Wales\" class=\"mw-redirect\" title=\"National parks of England and Wales\">National Park</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peak_District\" title=\"Peak District\">Peak District</a> becomes the United Kingdom's first <a href=\"https://wikipedia.org/wiki/National_parks_of_England_and_Wales\" class=\"mw-redirect\" title=\"National parks of England and Wales\">National Park</a>.", "links": [{"title": "Peak District", "link": "https://wikipedia.org/wiki/Peak_District"}, {"title": "National parks of England and Wales", "link": "https://wikipedia.org/wiki/National_parks_of_England_and_Wales"}]}, {"year": "1961", "text": "Bay of Pigs Invasion: A group of Cuban exiles financed and trained by the CIA lands at the Bay of Pigs in Cuba with the aim of ousting <PERSON><PERSON>.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Bay_of_Pigs_Invasion\" title=\"Bay of Pigs Invasion\">Bay of Pigs Invasion</a>: A group of <a href=\"https://wikipedia.org/wiki/Cuban_exile\" title=\"Cuban exile\">Cuban exiles</a> financed and trained by the <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">CIA</a> lands at the <a href=\"https://wikipedia.org/wiki/Bay_of_Pigs\" title=\"Bay of Pigs\">Bay of Pigs</a> in Cuba with the aim of ousting <a href=\"https://wikipedia.org/wiki/Fidel_<PERSON>\" title=\"Fidel Castro\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bay_of_Pigs_Invasion\" title=\"Bay of Pigs Invasion\">Bay of Pigs Invasion</a>: A group of <a href=\"https://wikipedia.org/wiki/Cuban_exile\" title=\"Cuban exile\">Cuban exiles</a> financed and trained by the <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">CIA</a> lands at the <a href=\"https://wikipedia.org/wiki/Bay_of_Pigs\" title=\"Bay of Pigs\">Bay of Pigs</a> in Cuba with the aim of ousting <a href=\"https://wikipedia.org/wiki/Fidel_<PERSON>\" title=\"Fidel Castro\"><PERSON><PERSON></a>.", "links": [{"title": "Bay of Pigs Invasion", "link": "https://wikipedia.org/wiki/Bay_of_Pigs_Invasion"}, {"title": "Cuban exile", "link": "https://wikipedia.org/wiki/Cuban_exile"}, {"title": "Central Intelligence Agency", "link": "https://wikipedia.org/wiki/Central_Intelligence_Agency"}, {"title": "Bay of Pigs", "link": "https://wikipedia.org/wiki/Bay_of_Pigs"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON> completes the first around-the-world airplane flight by a woman. Her solo flight in the Spirit of Columbus, which took 29 1/2 days, took off and landed at the Port Columbus International Airport in Ohio. ", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> completes the first around-the-world airplane flight by a woman. Her solo flight in the <i><a href=\"https://wikipedia.org/wiki/Spirit_of_Columbus\" title=\"Spirit of Columbus\">Spirit of Columbus</a></i>, which took 29 1/2 days, took off and landed at the <a href=\"https://wikipedia.org/wiki/Port_Columbus_International_Airport\" class=\"mw-redirect\" title=\"Port Columbus International Airport\">Port Columbus International Airport</a> in Ohio. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> completes the first around-the-world airplane flight by a woman. Her solo flight in the <i><a href=\"https://wikipedia.org/wiki/Spirit_of_Columbus\" title=\"Spirit of Columbus\">Spirit of Columbus</a></i>, which took 29 1/2 days, took off and landed at the <a href=\"https://wikipedia.org/wiki/Port_Columbus_International_Airport\" class=\"mw-redirect\" title=\"Port Columbus International Airport\">Port Columbus International Airport</a> in Ohio. ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Spirit of Columbus", "link": "https://wikipedia.org/wiki/<PERSON>_of_Columbus"}, {"title": "Port Columbus International Airport", "link": "https://wikipedia.org/wiki/Port_Columbus_International_Airport"}]}, {"year": "1969", "text": "<PERSON><PERSON> is convicted of assassinating <PERSON>.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is convicted of assassinating <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is convicted of assassinating <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "Communist Party of Czechoslovakia chairman <PERSON> is deposed.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Czechoslovakia\" title=\"Communist Party of Czechoslovakia\">Communist Party of Czechoslovakia</a> chairman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a> is deposed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Communist_Party_of_Czechoslovakia\" title=\"Communist Party of Czechoslovakia\">Communist Party of Czechoslovakia</a> chairman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a> is deposed.", "links": [{"title": "Communist Party of Czechoslovakia", "link": "https://wikipedia.org/wiki/Communist_Party_of_Czechoslovakia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexander_Dub%C4%8Dek"}]}, {"year": "1970", "text": "Apollo program: The damaged Apollo 13 spacecraft returns to Earth safely.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: The damaged <a href=\"https://wikipedia.org/wiki/Apollo_13\" title=\"Apollo 13\">Apollo 13</a> spacecraft returns to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> safely.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: The damaged <a href=\"https://wikipedia.org/wiki/Apollo_13\" title=\"Apollo 13\">Apollo 13</a> spacecraft returns to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> safely.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 13", "link": "https://wikipedia.org/wiki/Apollo_13"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}]}, {"year": "1971", "text": "The Provisional Government of Bangladesh is formed.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_Bangladesh\" title=\"Provisional Government of Bangladesh\">Provisional Government of Bangladesh</a> is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_Bangladesh\" title=\"Provisional Government of Bangladesh\">Provisional Government of Bangladesh</a> is formed.", "links": [{"title": "Provisional Government of Bangladesh", "link": "https://wikipedia.org/wiki/Provisional_Government_of_Bangladesh"}]}, {"year": "1975", "text": "The Cambodian Civil War ends. The Khmer Rouge captures the capital Phnom Penh and Cambodian government forces surrender.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Cambodian_Civil_War\" title=\"Cambodian Civil War\">Cambodian Civil War</a> ends. The <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> <a href=\"https://wikipedia.org/wiki/Fall_of_Phnom_Penh\" title=\"Fall of Phnom Penh\">captures</a> the capital <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a> and <a href=\"https://wikipedia.org/wiki/Khmer_National_Armed_Forces\" title=\"Khmer National Armed Forces\">Cambodian government forces</a> surrender.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cambodian_Civil_War\" title=\"Cambodian Civil War\">Cambodian Civil War</a> ends. The <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> <a href=\"https://wikipedia.org/wiki/Fall_of_Phnom_Penh\" title=\"Fall of Phnom Penh\">captures</a> the capital <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a> and <a href=\"https://wikipedia.org/wiki/Khmer_National_Armed_Forces\" title=\"Khmer National Armed Forces\">Cambodian government forces</a> surrender.", "links": [{"title": "Cambodian Civil War", "link": "https://wikipedia.org/wiki/Cambodian_Civil_War"}, {"title": "Khmer Rouge", "link": "https://wikipedia.org/wiki/Khmer_Rouge"}, {"title": "Fall of Phnom Penh", "link": "https://wikipedia.org/wiki/Fall_of_Phnom_Penh"}, {"title": "Phnom Penh", "link": "https://wikipedia.org/wiki/Phnom_Penh"}, {"title": "Khmer National Armed Forces", "link": "https://wikipedia.org/wiki/Khmer_National_Armed_Forces"}]}, {"year": "1978", "text": "<PERSON> is assassinated, provoking the Saur Revolution in Afghanistan.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Mir <PERSON></a> is assassinated, provoking the <a href=\"https://wikipedia.org/wiki/Saur_Revolution\" title=\"Saur Revolution\">Saur Revolution</a> in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Mir <PERSON></a> is assassinated, provoking the <a href=\"https://wikipedia.org/wiki/Saur_Revolution\" title=\"Saur Revolution\">Saur Revolution</a> in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Saur Revolution", "link": "https://wikipedia.org/wiki/Saur_Revolution"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "1982", "text": "Constitution Act, 1982 Patriation of the Canadian constitution in Ottawa by Proclamation of <PERSON>, Queen of Canada.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Constitution_Act,_1982\" title=\"Constitution Act, 1982\">Constitution Act, 1982</a> <a href=\"https://wikipedia.org/wiki/Patriation\" title=\"Patriation\">Patriation</a> of the <a href=\"https://wikipedia.org/wiki/Canadian_constitution\" class=\"mw-redirect\" title=\"Canadian constitution\">Canadian constitution</a> in <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a> by <a href=\"https://wikipedia.org/wiki/Proclamation\" title=\"Proclamation\">Proclamation</a> of <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a>, <a href=\"https://wikipedia.org/wiki/Queen_of_Canada\" class=\"mw-redirect\" title=\"Queen of Canada\">Queen of Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constitution_Act,_1982\" title=\"Constitution Act, 1982\">Constitution Act, 1982</a> <a href=\"https://wikipedia.org/wiki/Patriation\" title=\"Patriation\">Patriation</a> of the <a href=\"https://wikipedia.org/wiki/Canadian_constitution\" class=\"mw-redirect\" title=\"Canadian constitution\">Canadian constitution</a> in <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a> by <a href=\"https://wikipedia.org/wiki/Proclamation\" title=\"Proclamation\">Proclamation</a> of <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a>, <a href=\"https://wikipedia.org/wiki/Queen_of_Canada\" class=\"mw-redirect\" title=\"Queen of Canada\">Queen of Canada</a>.", "links": [{"title": "Constitution Act, 1982", "link": "https://wikipedia.org/wiki/Constitution_Act,_1982"}, {"title": "Patriation", "link": "https://wikipedia.org/wiki/Patriation"}, {"title": "Canadian constitution", "link": "https://wikipedia.org/wiki/Canadian_constitution"}, {"title": "Ottawa", "link": "https://wikipedia.org/wiki/Ottawa"}, {"title": "Proclamation", "link": "https://wikipedia.org/wiki/Proclamation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "Queen of Canada", "link": "https://wikipedia.org/wiki/Queen_of_Canada"}]}, {"year": "1986", "text": "An alleged state of war lasting 335 years between the Netherlands and the Isles of Scilly declared peace bringing an end to any hypothetical war that may have been legally considered to exist.", "html": "1986 - An alleged state of <a href=\"https://wikipedia.org/wiki/Three_Hundred_and_Thirty_Five_Years%27_War\" title=\"Three Hundred and Thirty Five Years' War\">war lasting 335 years</a> between the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a> and the <a href=\"https://wikipedia.org/wiki/Isles_of_Scilly\" title=\"Isles of Scilly\">Isles of Scilly</a> declared peace bringing an end to any hypothetical war that may have been legally considered to exist.", "no_year_html": "An alleged state of <a href=\"https://wikipedia.org/wiki/Three_Hundred_and_Thirty_Five_Years%27_War\" title=\"Three Hundred and Thirty Five Years' War\">war lasting 335 years</a> between the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a> and the <a href=\"https://wikipedia.org/wiki/Isles_of_Scilly\" title=\"Isles of Scilly\">Isles of Scilly</a> declared peace bringing an end to any hypothetical war that may have been legally considered to exist.", "links": [{"title": "Three Hundred and Thirty Five Years' War", "link": "https://wikipedia.org/wiki/Three_Hundred_and_Thirty_Five_Years%27_War"}, {"title": "Netherlands", "link": "https://wikipedia.org/wiki/Netherlands"}, {"title": "Isles of Scilly", "link": "https://wikipedia.org/wiki/Isles_of_Scilly"}]}, {"year": "1992", "text": "The Katina P is deliberately run aground off Maputo, Mozambique, and 60,000 tons of crude oil spill into the ocean.", "html": "1992 - The <i><a href=\"https://wikipedia.org/wiki/Katina_P\" title=\"Katina P\">Katina P</a></i> is deliberately run aground off <a href=\"https://wikipedia.org/wiki/Maputo\" title=\"Maputo\">Maputo</a>, <a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a>, and 60,000 tons of crude oil spill into the ocean.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Katina_P\" title=\"Katina P\">Katina P</a></i> is deliberately run aground off <a href=\"https://wikipedia.org/wiki/Maputo\" title=\"Maputo\">Maputo</a>, <a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a>, and 60,000 tons of crude oil spill into the ocean.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Maputo", "link": "https://wikipedia.org/wiki/Maputo"}, {"title": "Mozambique", "link": "https://wikipedia.org/wiki/Mozambique"}]}, {"year": "1998", "text": "Space Shuttle Columbia is launched on STS-90, the final Spacelab mission.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-90\" title=\"STS-90\">STS-90</a>, the final <a href=\"https://wikipedia.org/wiki/Spacelab\" title=\"Spacelab\">Spacelab</a> mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-90\" title=\"STS-90\">STS-90</a>, the final <a href=\"https://wikipedia.org/wiki/Spacelab\" title=\"Spacelab\">Spacelab</a> mission.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-90", "link": "https://wikipedia.org/wiki/STS-90"}, {"title": "Spacelab", "link": "https://wikipedia.org/wiki/Spacelab"}]}, {"year": "2003", "text": "<PERSON><PERSON> takes office as the first female prime minister of Finland.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_J%C3%A4%C3%A4tteenm%C3%A4ki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> takes office as the first female <a href=\"https://wikipedia.org/wiki/Prime_minister_of_Finland\" class=\"mw-redirect\" title=\"Prime minister of Finland\">prime minister of Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_J%C3%A4%C3%A4tteenm%C3%A4ki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> takes office as the first female <a href=\"https://wikipedia.org/wiki/Prime_minister_of_Finland\" class=\"mw-redirect\" title=\"Prime minister of Finland\">prime minister of Finland</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anneli_J%C3%A4%C3%A4tteenm%C3%A4ki"}, {"title": "Prime minister of Finland", "link": "https://wikipedia.org/wiki/Prime_minister_of_Finland"}]}, {"year": "2006", "text": "A Palestinian suicide bomber detonates an explosive device in a Tel Aviv restaurant, killing 11 people and injuring 70.", "html": "2006 - A <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> suicide bomber <a href=\"https://wikipedia.org/wiki/2nd_Rosh_Ha%27ir_restaurant_bombing\" title=\"2nd Rosh Ha'ir restaurant bombing\">detonates an explosive device in a Tel Aviv restaurant</a>, killing 11 people and injuring 70.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> suicide bomber <a href=\"https://wikipedia.org/wiki/2nd_Rosh_Ha%27ir_restaurant_bombing\" title=\"2nd Rosh Ha'ir restaurant bombing\">detonates an explosive device in a Tel Aviv restaurant</a>, killing 11 people and injuring 70.", "links": [{"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}, {"title": "2nd Rosh Ha'ir restaurant bombing", "link": "https://wikipedia.org/wiki/2nd_Rosh_Ha%27ir_restaurant_bombing"}]}, {"year": "2013", "text": "An explosion at a fertilizer plant in the city of West, Texas, kills 15 people and injures 160 others.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/West_Fertilizer_Company_explosion\" title=\"West Fertilizer Company explosion\">An explosion</a> at a fertilizer plant in the city of <a href=\"https://wikipedia.org/wiki/West,_Texas\" title=\"West, Texas\">West, Texas</a>, kills 15 people and injures 160 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/West_Fertilizer_Company_explosion\" title=\"West Fertilizer Company explosion\">An explosion</a> at a fertilizer plant in the city of <a href=\"https://wikipedia.org/wiki/West,_Texas\" title=\"West, Texas\">West, Texas</a>, kills 15 people and injures 160 others.", "links": [{"title": "West Fertilizer Company explosion", "link": "https://wikipedia.org/wiki/West_Fertilizer_Company_explosion"}, {"title": "West, Texas", "link": "https://wikipedia.org/wiki/West,_Texas"}]}, {"year": "2014", "text": "NASA's Kepler space telescope confirms the discovery of the first Earth-size planet in the habitable zone of another star.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Kepler_space_telescope\" title=\"Kepler space telescope\">Kepler space telescope</a> confirms the discovery of <a href=\"https://wikipedia.org/wiki/Kepler-186f\" title=\"Kepler-186f\">the first Earth-size planet</a> in the <a href=\"https://wikipedia.org/wiki/Circumstellar_habitable_zone\" class=\"mw-redirect\" title=\"Circumstellar habitable zone\">habitable zone</a> of another star.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Kepler_space_telescope\" title=\"Kepler space telescope\">Kepler space telescope</a> confirms the discovery of <a href=\"https://wikipedia.org/wiki/Kepler-186f\" title=\"Kepler-186f\">the first Earth-size planet</a> in the <a href=\"https://wikipedia.org/wiki/Circumstellar_habitable_zone\" class=\"mw-redirect\" title=\"Circumstellar habitable zone\">habitable zone</a> of another star.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Kepler space telescope", "link": "https://wikipedia.org/wiki/Kepler_space_telescope"}, {"title": "Kepler-186f", "link": "https://wikipedia.org/wiki/Ke<PERSON>-186f"}, {"title": "Circumstellar habitable zone", "link": "https://wikipedia.org/wiki/Circumstellar_habitable_zone"}]}, {"year": "2021", "text": "The funeral of <PERSON>, Duke of Edinburgh, takes place at St George's Chapel, Windsor Castle.", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/Death_and_funeral_of_Prince_<PERSON>,_Duke_of_Edinburgh\" title=\"Death and funeral of Prince <PERSON>, Duke of Edinburgh\">funeral of <PERSON>, Duke of Edinburgh</a>, takes place at <a href=\"https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle\" title=\"St George's Chapel, Windsor Castle\">St George's Chapel, Windsor Castle</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Death_and_funeral_of_Prince_<PERSON>,_Duke_of_Edinburgh\" title=\"Death and funeral of Prince <PERSON>, Duke of Edinburgh\">funeral of <PERSON>, Duke of Edinburgh</a>, takes place at <a href=\"https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle\" title=\"St George's Chapel, Windsor Castle\">St George's Chapel, Windsor Castle</a>.", "links": [{"title": "Death and funeral of <PERSON>, Duke of Edinburgh", "link": "https://wikipedia.org/wiki/Death_and_funeral_of_Prince_<PERSON>,_Duke_of_Edinburgh"}, {"title": "St George's Chapel, Windsor Castle", "link": "https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle"}]}], "Births": [{"year": "1277", "text": "<PERSON>, Byzantine emperor (d. 1320)", "html": "1277 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (d. 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>laiologos\"><PERSON></a>, Byzantine emperor (d. 1320)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1455", "text": "<PERSON>, <PERSON><PERSON> of Venice (d. 1538)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON> of Venice (d. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON> of Venice (d. 1538)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1497", "text": "<PERSON>, Spanish conquistador, conquered northern Chile (d. 1553)", "html": "1497 - <a href=\"https://wikipedia.org/wiki/Pedro_<PERSON>_Valdivia\" title=\"<PERSON> de Valdivia\"><PERSON> Valdivia</a>, Spanish conquistador, conquered northern Chile (d. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_<PERSON>_Valdivia\" title=\"Pedro <PERSON> Valdivia\"><PERSON></a>, Spanish conquistador, conquered northern Chile (d. 1553)", "links": [{"title": "<PERSON> Valdi<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>_<PERSON>"}]}, {"year": "1573", "text": "<PERSON>, Elector of Bavaria (d. 1651)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria\" title=\"<PERSON>, Elector of Bavaria\"><PERSON>, Elector of Bavaria</a> (d. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria\" title=\"<PERSON>, Elector of Bavaria\"><PERSON>, Elector of Bavaria</a> (d. 1651)", "links": [{"title": "<PERSON>, Elector of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria"}]}, {"year": "1586", "text": "<PERSON>, English poet and playwright (d. 1639)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)\" title=\"<PERSON> (dramatist)\"><PERSON></a>, English poet and playwright (d. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)\" title=\"<PERSON> (dramatist)\"><PERSON></a>, English poet and playwright (d. 1639)", "links": [{"title": "<PERSON> (dramatist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)"}]}, {"year": "1598", "text": "<PERSON>, Italian priest and astronomer (d. 1671)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and astronomer (d. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and astronomer (d. 1671)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1620", "text": "<PERSON>, French-Canadian nun and saint, founded the Congregation of Notre Dame of Montreal (d. 1700)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_Notre_Dame_of_Montreal\" title=\"Congregation of Notre Dame of Montreal\">Congregation of Notre Dame of Montreal</a> (d. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_Notre_Dame_of_Montreal\" title=\"Congregation of Notre Dame of Montreal\">Congregation of Notre Dame of Montreal</a> (d. 1700)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Congregation of Notre Dame of Montreal", "link": "https://wikipedia.org/wiki/Congregation_of_Notre_Dame_of_Montreal"}]}, {"year": "1635", "text": "<PERSON>, British theologian and scholar (d. 1699)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British theologian and scholar (d. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British theologian and scholar (d. 1699)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON>, German composer and theorist (d. 1729)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (d. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (d. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, 10th Earl of Buchan, Scottish politician (d. 1767)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Buchan\" title=\"<PERSON>, 10th Earl of Buchan\"><PERSON>, 10th Earl of Buchan</a>, Scottish politician (d. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Earl_of_Buchan\" title=\"<PERSON>, 10th Earl of Buchan\"><PERSON>, 10th Earl of Buchan</a>, Scottish politician (d. 1767)", "links": [{"title": "<PERSON>, 10th Earl of Buchan", "link": "https://wikipedia.org/wiki/<PERSON>,_10th_Earl_of_Buch<PERSON>"}]}, {"year": "1734", "text": "<PERSON><PERSON><PERSON>, King of Thailand (d. 1782)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, King of Thailand (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, King of Thailand (d. 1782)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taksin"}]}, {"year": "1741", "text": "<PERSON>, American lawyer and jurist (d. 1811)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, French academic and politician, French Minister of the Interior (d. 1828)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_de_Neufch%C3%A2teau\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French academic and politician, <a href=\"https://wikipedia.org/wiki/List_of_Interior_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Interior Ministers of France\">French Minister of the Interior</a> (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_de_Neufch%C3%A2teau\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French academic and politician, <a href=\"https://wikipedia.org/wiki/List_of_Interior_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Interior Ministers of France\">French Minister of the Interior</a> (d. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_de_Neufch%C3%A2teau"}, {"title": "List of Interior Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Interior_Ministers_of_France"}]}, {"year": "1756", "text": "<PERSON><PERSON><PERSON>, Indian commander (d. 1805)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian commander (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian commander (d. 1805)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1766", "text": "<PERSON>, American surveyor, merchant, and politician (d. 1861)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surveyor, merchant, and politician (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surveyor, merchant, and politician (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, German botanist and explorer (d. 1868)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and explorer (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and explorer (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, French mathematician and academic (d. 1840)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_<PERSON>illier"}]}, {"year": "1799", "text": "<PERSON>, English food writer and poet (d. 1859)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English food writer and poet (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English food writer and poet (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON><PERSON>, Serbian botanist and academic (d. 1888)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian botanist and academic (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian botanist and academic (d. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Josif_Pan%C4%8Di%C4%87"}]}, {"year": "1816", "text": "<PERSON>, English architect and philanthropist (d. 1876)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chapel_builder)\" title=\"<PERSON> (chapel builder)\"><PERSON></a>, English architect and philanthropist (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chapel_builder)\" title=\"<PERSON> (chapel builder)\"><PERSON></a>, English architect and philanthropist (d. 1876)", "links": [{"title": "<PERSON> (chapel builder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chapel_builder)"}]}, {"year": "1820", "text": "<PERSON>, American firefighter and (disputed) inventor of baseball (d. 1892)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American firefighter and (disputed) inventor of <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American firefighter and (disputed) inventor of <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a> (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Baseball", "link": "https://wikipedia.org/wiki/Baseball"}]}, {"year": "1833", "text": "<PERSON><PERSON><PERSON>, Belgian violinist, composer, and conductor (d. 1900)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian violinist, composer, and conductor (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian violinist, composer, and conductor (d. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON><PERSON> <PERSON><PERSON>, American banker and financier, founded J.P. Morgan & Co. (d. 1913)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. P. Morgan\"><PERSON><PERSON> <PERSON><PERSON></a>, American banker and financier, founded <a href=\"https://wikipedia.org/wiki/J.P._Morgan_%26_Co.\" title=\"J.P. Morgan &amp; Co.\">J.P. Morgan &amp; Co.</a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. P. Morgan\"><PERSON><PERSON> <PERSON><PERSON></a>, American banker and financier, founded <a href=\"https://wikipedia.org/wiki/J.P._Morgan_%26_Co.\" title=\"J.P. Morgan &amp; Co.\">J.P. Morgan &amp; Co.</a> (d. 1913)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "J.P. Morgan & Co.", "link": "https://wikipedia.org/wiki/J.P._Morgan_%26_Co."}]}, {"year": "1842", "text": "<PERSON>, French businessman and politician, 53rd Prime Minister of France (d. 1911)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and politician, 53rd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Prime Minister of France</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and politician, 53rd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Prime Minister of France</a> (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Prime Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France"}]}, {"year": "1849", "text": "<PERSON>, American jurist and politician, 36th United States Secretary of State (d. 1923)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, 36th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, 36th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1852", "text": "<PERSON>, American baseball player and manager (d. 1922)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1863", "text": "<PERSON>, English mathematician and theorist (d. 1940)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and theorist (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and theorist (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Polish-Austrian nun and saint, founded the Congregation of the Ursulines of the Agonizing Heart of Jesus (d. 1939)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3chowska\" title=\"<PERSON>\"><PERSON></a>, Polish-Austrian nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_the_Ursulines_of_the_Agonizing_Heart_of_Jesus\" title=\"Congregation of the Ursulines of the Agonizing Heart of Jesus\">Congregation of the Ursulines of the Agonizing Heart of Jesus</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3chowska\" title=\"<PERSON>\"><PERSON></a>, Polish-Austrian nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_the_Ursulines_of_the_Agonizing_Heart_of_Jesus\" title=\"Congregation of the Ursulines of the Agonizing Heart of Jesus\">Congregation of the Ursulines of the Agonizing Heart of Jesus</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Led%C3%B3chowska"}, {"title": "Congregation of the Ursulines of the Agonizing Heart of Jesus", "link": "https://wikipedia.org/wiki/Congregation_of_the_Ursulines_of_the_Agonizing_Heart_of_Jesus"}]}, {"year": "1866", "text": "<PERSON>, English physiologist and academic (d. 1927)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and academic (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and academic (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian general and politician, 5th Estonian Minister of War (d. 1941)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Aleksander_T%C3%B5<PERSON>son\" title=\"Aleksan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian general and politician, 5th <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_War\" class=\"mw-redirect\" title=\"Estonian Minister of War\">Estonian Minister of War</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksander_T%C3%B5<PERSON>son\" title=\"<PERSON>eksan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian general and politician, 5th <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_War\" class=\"mw-redirect\" title=\"Estonian Minister of War\">Estonian Minister of War</a> (d. 1941)", "links": [{"title": "Aleksander <PERSON>", "link": "https://wikipedia.org/wiki/Aleksander_T%C3%B5<PERSON>son"}, {"title": "Estonian Minister of War", "link": "https://wikipedia.org/wiki/Estonian_Minister_of_War"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese diplomat (d. 1949)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Matsudaira_Tsuneo\" class=\"mw-redirect\" title=\"Matsudaira Tsuneo\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese diplomat (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matsudaira_Tsuneo\" class=\"mw-redirect\" title=\"Matsudaira Tsuneo\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese diplomat (d. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, German-American lawyer and businessman (d. 1961)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, German-American lawyer and businessman (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, German-American lawyer and businessman (d. 1961)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek tennis player (d. 1942)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON><PERSON>_Petrok<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Petrok<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek tennis player (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Petrok<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek tennis player (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demetrios_Petrokokkinos"}]}, {"year": "1879", "text": "<PERSON>, French hurdler (d. 1918)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French hurdler (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French hurdler (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Polish pianist and composer (d. 1951)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pianist and composer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pianist and composer (d. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON>, German soldier, trombonist, and composer (d. 1954)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Her<PERSON>el\"><PERSON><PERSON></a>, German soldier, trombonist, and composer (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German soldier, trombonist, and composer (d. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Her<PERSON>_<PERSON>el"}]}, {"year": "1891", "text": "<PERSON>, Polish-American ufologist and author (d. 1965)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American ufologist and author (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American ufologist and author (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American soldier and author (d. 1948)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Spanish-American ventriloquist (d. 1999)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Se%C3%B1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American ventriloquist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%B1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American ventriloquist (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%B1or_<PERSON><PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian philosopher and educator (d. 1981)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Nisarga<PERSON><PERSON>_Maharaj\" title=\"Nisargada<PERSON> Maharaj\"><PERSON><PERSON><PERSON><PERSON><PERSON> Maharaj</a>, Indian philosopher and educator (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nisarga<PERSON><PERSON>_Maharaj\" title=\"Nisargada<PERSON> Maharaj\"><PERSON><PERSON><PERSON><PERSON><PERSON> Maharaj</a>, Indian philosopher and educator (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Maharaj", "link": "https://wikipedia.org/wiki/Nisargada<PERSON>_Maharaj"}]}, {"year": "1897", "text": "<PERSON>, American novelist and playwright (d. 1975)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and playwright (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wilder\"><PERSON></a>, American novelist and playwright (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss physician and mountaineer (d. 1983)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Edouard_<PERSON><PERSON><PERSON>-Dunant\" title=\"Edouard W<PERSON>-Dunant\">Edo<PERSON></a>, Swiss physician and mountaineer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edouard_<PERSON><PERSON><PERSON>-Du<PERSON>\" title=\"Edouard Wys<PERSON>-Dunant\">Edo<PERSON></a>, Swiss physician and mountaineer (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edouard_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian decathlete and coach (d. 1958)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>eksan<PERSON>lumberg\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian decathlete and coach (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ek<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian decathlete and coach (d. 1958)", "links": [{"title": "<PERSON>ek<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Russian-American composer and educator (d. 1978)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American composer and educator (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American composer and educator (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Ukrainian-American cellist and educator (d. 1976)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American cellist and educator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American cellist and educator (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American hurdler and coach (d. 1975)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American journalist and actor (d. 1960)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American actor (d. 1987)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1987)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1906", "text": "<PERSON>, American physician, co-founded Kaiser Permanente (d. 1984)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, co-founded <a href=\"https://wikipedia.org/wiki/Kaiser_Permanente\" title=\"Kaiser Permanente\">Kaiser Permanente</a> (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, co-founded <a href=\"https://wikipedia.org/wiki/Kaiser_Permanente\" title=\"Kaiser Permanente\">Kaiser Permanente</a> (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kaiser Permanente", "link": "https://wikipedia.org/wiki/Kaiser_Permanente"}]}, {"year": "1909", "text": "<PERSON>, French politician, President of France (d. 1996)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Greek historian and politician, Greek Minister of Defence (d. 1990)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek historian and politician, <a href=\"https://wikipedia.org/wiki/List_of_defence_ministers_of_Greece\" title=\"List of defence ministers of Greece\">Greek Minister of Defence</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek historian and politician, <a href=\"https://wikipedia.org/wiki/List_of_defence_ministers_of_Greece\" title=\"List of defence ministers of Greece\">Greek Minister of Defence</a> (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of defence ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_defence_ministers_of_Greece"}]}, {"year": "1910", "text": "<PERSON>, Australian screenwriter and producer (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian screenwriter and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian screenwriter and producer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, French footballer and manager (d. 1997)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer and manager (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer and manager (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, French author and poet (d. 1996)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Herv%C3%A9_Ba<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and poet (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Herv%C3%A9_Ba<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and poet (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Herv%C3%A9_<PERSON><PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American soldier and journalist (d. 2009)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and journalist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and journalist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Hungarian-American actress and singer (d. 2013)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actress and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American art director (d. 1984)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_director)\" title=\"<PERSON> (art director)\"><PERSON></a>, American art director (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_director)\" title=\"<PERSON> (art director)\"><PERSON></a>, American art director (d. 1984)", "links": [{"title": "<PERSON> (art director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_director)"}]}, {"year": "1914", "text": "<PERSON>, American illustrator (d. 1967)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ra<PERSON>\" title=\"Mac Raboy\"><PERSON></a>, American illustrator (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Raboy\" title=\"Mac Raboy\"><PERSON></a>, American illustrator (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Scottish soldier (d. 2009)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American general and politician, 20th Governor of South Dakota (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Dakota\" title=\"Governor of South Dakota\">Governor of South Dakota</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Dakota\" title=\"Governor of South Dakota\">Governor of South Dakota</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of South Dakota", "link": "https://wikipedia.org/wiki/Governor_of_South_Dakota"}]}, {"year": "1915", "text": "<PERSON>, Armenian painter (d. 1999)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian painter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian painter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regina_Ghazaryan"}]}, {"year": "1916", "text": "<PERSON>, 3rd President of Union of Myanmar (d. 1989)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Union_of_Myanmar\" class=\"mw-redirect\" title=\"President of Union of Myanmar\">President of Union of Myanmar</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Union_of_Myanmar\" class=\"mw-redirect\" title=\"President of Union of Myanmar\">President of Union of Myanmar</a> (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Union of Myanmar", "link": "https://wikipedia.org/wiki/President_of_Union_of_Myanmar"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Sri Lankan educator and politician (d. 1981)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/A._Thiagarajah\" title=\"A. Thiagarajah\"><PERSON><PERSON></a>, Sri Lankan educator and politician (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A._Thiagarajah\" title=\"A. <PERSON>\"><PERSON><PERSON></a>, Sri Lankan educator and politician (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A._Thiagarajah"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Prime Minister of Sri Lanka, world's first female prime minister (d. 2000)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Sirimavo_Bandaranaike\" title=\"Sirimavo Bandaranaike\">Sirimavo Bandaranaike</a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka\" title=\"Prime Minister of Sri Lanka\">Prime Minister of Sri Lanka</a>, world's first female prime minister (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sirimavo_Bandaranaike\" title=\"Sirimavo Bandaranaike\">Sirimavo Bandaranaike</a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka\" title=\"Prime Minister of Sri Lanka\">Prime Minister of Sri Lanka</a>, world's first female prime minister (d. 2000)", "links": [{"title": "Sirimavo Bandaranaike", "link": "https://wikipedia.org/wiki/Sirimavo_Bandaranaike"}, {"title": "Prime Minister of Sri Lanka", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka"}]}, {"year": "1918", "text": "<PERSON>, American actor (d. 1981)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Canadian lieutenant and politician, 24th Lieutenant Governor of Quebec (d. 2016)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lieutenant and politician, 24th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lieutenant and politician, 24th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Costa Rican-Mexican singer-songwriter and actress (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Costa Rican-Mexican singer-songwriter and actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Costa Rican-Mexican singer-songwriter and actress (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, French journalist and author (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French journalist and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French journalist and author (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English actor, director, and screenwriter (d. 1994)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American baseball player, coach, and manager (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Australian air marshal (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian air marshal (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian air marshal (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Italian lyric tenor (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian lyric tenor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian lyric tenor (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American soldier and journalist (d. 1991)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and journalist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and journalist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Australian public servant (d. 2022)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American-Japanese author and critic (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Japanese author and critic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Japanese author and critic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Lebanese lawyer and politician, 13th President of Lebanon (d. 1989)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>d\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_Lebanon\" title=\"President of Lebanon\">President of Lebanon</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_Lebanon\" title=\"President of Lebanon\">President of Lebanon</a> (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Moawad"}, {"title": "President of Lebanon", "link": "https://wikipedia.org/wiki/President_of_Lebanon"}]}, {"year": "1926", "text": "<PERSON>, British actress (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Canadian ice hockey player and manager (d. 2004)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, East German politician and First Lady (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, East German politician and First Lady (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, East German politician and First Lady (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American businessman (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American short story writer, novelist, and essayist", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, novelist, and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, novelist, and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Austrian fencer", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian fencer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Canadian accountant and politician (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian accountant and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian accountant and politician (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>abi<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German-American bassist, composer, and bandleader (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James Last\"><PERSON></a>, German-American bassist, composer, and bandleader (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Last\" title=\"James Last\"><PERSON></a>, German-American bassist, composer, and bandleader (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English trombonist and bandleader (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trombonist and bandleader (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Barber\"><PERSON></a>, English trombonist and bandleader (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English tennis player and sportscaster", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, English tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, English tennis player and sportscaster", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1931", "text": "<PERSON>, American journalist and photographer (d. 2012)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and photographer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and photographer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American songwriter and producer (d. 2011)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Australian-English surgeon and academic (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, Australian-English surgeon and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, Australian-English surgeon and academic (d. 2022)", "links": [{"title": "<PERSON> (surgeon)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surgeon)"}]}, {"year": "1935", "text": "<PERSON>, American broadcaster, founded Home Shopping Network and Pax TV (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster, founded <a href=\"https://wikipedia.org/wiki/Home_Shopping_Network\" class=\"mw-redirect\" title=\"Home Shopping Network\">Home Shopping Network</a> and <a href=\"https://wikipedia.org/wiki/Pax_TV\" class=\"mw-redirect\" title=\"Pax TV\">Pax TV</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster, founded <a href=\"https://wikipedia.org/wiki/Home_Shopping_Network\" class=\"mw-redirect\" title=\"Home Shopping Network\">Home Shopping Network</a> and <a href=\"https://wikipedia.org/wiki/Pax_TV\" class=\"mw-redirect\" title=\"Pax TV\">Pax TV</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Home Shopping Network", "link": "https://wikipedia.org/wiki/Home_Shopping_Network"}, {"title": "Pax TV", "link": "https://wikipedia.org/wiki/Pax_TV"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Swiss chemist (d. 2022)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Urs_Wild\" title=\"Urs Wild\"><PERSON><PERSON> <PERSON></a>, Swiss chemist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Urs_Wild\" title=\"Urs Wild\"><PERSON><PERSON> <PERSON></a>, Swiss chemist (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Urs_Wild"}]}, {"year": "1937", "text": "<PERSON>, Canadian historian and academic (d. 2012)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Austrian-German engineer and businessman (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ABch\" title=\"<PERSON>\"><PERSON></a>, Austrian-German engineer and businessman (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ABch\" title=\"<PERSON>\"><PERSON></a>, Austrian-German engineer and businessman (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_Pi%C3%ABch"}]}, {"year": "1938", "text": "<PERSON>, American businessman and politician, 36th Lieutenant Governor of Texas", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Texas_politician)\" title=\"<PERSON> (Texas politician)\"><PERSON></a>, American businessman and politician, 36th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Texas\" title=\"Lieutenant Governor of Texas\">Lieutenant Governor of Texas</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Texas_politician)\" title=\"<PERSON> (Texas politician)\"><PERSON></a>, American businessman and politician, 36th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Texas\" title=\"Lieutenant Governor of Texas\">Lieutenant Governor of Texas</a>", "links": [{"title": "<PERSON> (Texas politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Texas_politician)"}, {"title": "Lieutenant Governor of Texas", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Texas"}]}, {"year": "1938", "text": "<PERSON>, Canadian lawyer and politician, 41st Canadian Minister of Justice", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1938", "text": "<PERSON>, American theologian, author, and academic (d. 2011)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian, author, and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian, author, and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American theorist and author (d. 1988)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and author (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and author (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American art dealer (d. 2011)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_dealer)\" title=\"<PERSON> (art dealer)\"><PERSON></a>, American art dealer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_dealer)\" title=\"<PERSON> (art dealer)\"><PERSON></a>, American art dealer (d. 2011)", "links": [{"title": "<PERSON> (art dealer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_dealer)"}]}, {"year": "1940", "text": "<PERSON>, English businessman and politician, Lord Lieutenant of Devon", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Devon\" title=\"Lord Lieutenant of Devon\">Lord Lieutenant of Devon</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Devon\" title=\"Lord Lieutenant of Devon\">Lord Lieutenant of Devon</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Devon", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Devon"}]}, {"year": "1940", "text": "<PERSON>, English singer-songwriter (d. 1983)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Billy Fury\"><PERSON></a>, English singer-songwriter (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English journalist (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American animator and screenwriter (d. 1992)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chuck Menville\"><PERSON></a>, American animator and screenwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chuck Menville\"><PERSON></a>, American animator and screenwriter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, German soprano and actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German soprano and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Italian cardinal and vicar general of Rome", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal and <a href=\"https://wikipedia.org/wiki/Vicar_General_of_Rome\" class=\"mw-redirect\" title=\"Vicar General of Rome\">vicar general of Rome</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal and <a href=\"https://wikipedia.org/wiki/Vicar_General_of_Rome\" class=\"mw-redirect\" title=\"Vicar General of Rome\">vicar general of Rome</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lini"}, {"title": "Vicar General of Rome", "link": "https://wikipedia.org/wiki/Vicar_General_of_Rome"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Estonian architect and politician, Estonian Minister of the Interior", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lagle Parek\"><PERSON><PERSON></a>, Estonian architect and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"La<PERSON> Parek\"><PERSON><PERSON></a>, Estonian architect and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/La<PERSON>_<PERSON>"}, {"title": "Minister of the Interior (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)"}]}, {"year": "1942", "text": "<PERSON>, American jazz bassist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz bassist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz bassist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian businessman and cricketer (d. 2009)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/D<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman and cricketer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman and cricketer (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American lawyer, author, and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English sailor and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Lord <PERSON>, Scottish lawyer and judge", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "links": [{"title": "<PERSON>, Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English lawyer and judge", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American photographer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese baseball player, coach, and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Tsutom<PERSON>_<PERSON>aka<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsutom<PERSON>_<PERSON>aka<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player, coach, and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>su"}]}, {"year": "1948", "text": "<PERSON>, Czech pianist, composer, and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech pianist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech pianist, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American educator and politician (d. 2012)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Finnish runner", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> V<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> V<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>la"}]}, {"year": "1951", "text": "<PERSON>, Argentinian-English actress (d. 2024)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-English actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-English actress (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish ice hockey player and businessman (d. 2022)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/B%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish ice hockey player and businessman (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish ice hockey player and businessman (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%B6r<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American voice actor (d. 2016)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian ice hockey player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1952", "text": "<PERSON>, English general and politician, Lieutenant Governor of Jersey", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Jersey\" title=\"Lieutenant Governor of Jersey\">Lieutenant Governor of Jersey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Jersey\" title=\"Lieutenant Governor of Jersey\">Lieutenant Governor of Jersey</a>", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}, {"title": "Lieutenant Governor of Jersey", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Jersey"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian commander \"Arkan\" (d. 2000)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/%C5%BDeljko_Ra%C5%BEnatovi%C4%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian commander \"<PERSON><PERSON>\" (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%BDeljko_Ra%C5%BEnatovi%C4%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian commander \"<PERSON><PERSON>\" (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%BDeljko_Ra%C5%BEnatovi%C4%87"}]}, {"year": "1952", "text": "<PERSON>, Scottish businessman and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Glasgow_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Glasgow politician)\"><PERSON></a>, Scottish businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Glasgow_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Glasgow politician)\"><PERSON></a>, Scottish businessman and politician", "links": [{"title": "<PERSON> (Glasgow politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Glasgow_politician)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian race car driver", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Canadian professional wrestler and actor (d. 2015)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian professional wrestler and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian professional wrestler and actor (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American basketball player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>r"}]}, {"year": "1955", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2018)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English physician and explorer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician and explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician and explorer", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}]}, {"year": "1956", "text": "<PERSON>, <PERSON>, Scottish lawyer and judge", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Canadian actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, American disc jockey", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Afrika_Bambaataa\" title=\"Afrika Bambaataa\"><PERSON><PERSON><PERSON></a>, American disc jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Afrika_Bambaataa\" title=\"Afrika Bambaataa\"><PERSON><PERSON><PERSON></a>, American disc jockey", "links": [{"title": "Afrika Bambaataa", "link": "https://wikipedia.org/wiki/Afrika_Bambaataa"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, American basketball coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English novelist, essayist, lyricist, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, essayist, lyricist, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, essayist, lyricist, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English lawyer and judge", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, British historian", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British historian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Canadian javelin thrower (d. 2013)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Babits\" title=\"Laslo Babits\"><PERSON><PERSON></a>, Canadian javelin thrower (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Babits\" title=\"Laslo Babits\"><PERSON><PERSON></a>, Canadian javelin thrower (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Babits"}]}, {"year": "1959", "text": "<PERSON>, English actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bean\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian ice hockey player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1959", "text": "<PERSON>, Chinese shot putter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Meisu\"><PERSON></a>, Chinese shot putter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Russian pole vaulter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pole_vaulter)\" title=\"<PERSON> (pole vaulter)\"><PERSON></a>, Russian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pole_vaulter)\" title=\"<PERSON> (pole vaulter)\"><PERSON></a>, Russian pole vaulter", "links": [{"title": "<PERSON> (pole vaulter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pole_vaulter)"}]}, {"year": "1961", "text": "<PERSON>, Jamaican-English cricketer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American football player and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>er_<PERSON>\" title=\"Boom<PERSON>\"><PERSON><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Boom<PERSON>\"><PERSON><PERSON></a>, American football player and sportscaster", "links": [{"title": "Boomer <PERSON>", "link": "https://wikipedia.org/wiki/Boom<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English fashion designer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Freud\" title=\"Bella Freud\"><PERSON></a>, English fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Freud\" title=\"Bella Freud\"><PERSON></a>, English fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English jockey and trainer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(horse_racing)\" title=\"<PERSON> (horse racing)\"><PERSON></a>, English jockey and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(horse_racing)\" title=\"<PERSON> (horse racing)\"><PERSON></a>, English jockey and trainer", "links": [{"title": "<PERSON> (horse racing)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(horse_racing)"}]}, {"year": "1964", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>on"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Indian actor and singer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Indian actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Indian actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(actor)"}]}, {"year": "1967", "text": "<PERSON>, Peruvian-Scottish actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, New Zealand rugby player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1967)\" title=\"<PERSON> (rugby union, born 1967)\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1967)\" title=\"<PERSON> (rugby union, born 1967)\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON> (rugby union, born 1967)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1967)"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Australian politician, 17th Deputy Prime Minister of Australia", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia\" title=\"Deputy Prime Minister of Australia\">Deputy Prime Minister of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia\" title=\"Deputy Prime Minister of Australia\">Deputy Prime Minister of Australia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Danish fashion designer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player and coach (d. 2013)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (d. 2013)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1968", "text": "<PERSON>, Canadian jockey", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian jockey", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, New Zealand cricketer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English boxer and trainer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer and trainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American rapper, producer, and actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper, producer, and actor", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1971", "text": "<PERSON>, English actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1972", "text": "<PERSON>, American football player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Sri Lankan cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Japanese footballer and referee", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer and referee", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Terran_Sandwith\" title=\"Terran Sand<PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Terran_Sandwith\" title=\"Terran Sand<PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Terran_Sandwith"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Estonian architect", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian basketball player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Australian basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Australian basketball player and sportscaster", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1973", "text": "<PERSON>, American basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Swedish singer-songwriter, guitarist, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Mi<PERSON><PERSON>_%C3%85kerfeldt\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mi<PERSON><PERSON>_%C3%85kerfeldt\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON><PERSON>_%C3%85kerfeldt"}]}, {"year": "1974", "text": "<PERSON>, English singer and fashion designer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Victoria_Beckham\" title=\"<PERSON>\"><PERSON></a>, English singer and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Beckham\" title=\"<PERSON>\"><PERSON></a>, English singer and fashion designer", "links": [{"title": "<PERSON>ham", "link": "https://wikipedia.org/wiki/Victoria_Beckham"}]}, {"year": "1975", "text": "<PERSON>, English politician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American ice hockey player (d. 2020)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Jamaican hurdler and long jumper", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican hurdler and long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican hurdler and long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American speed skater", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Danish composer, organist, and pianist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish composer, organist, and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish composer, organist, and pianist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, German skier", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German skier", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Scottish rugby player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Scottish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Scottish rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Serbian-Slovenian triple jumper", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Mari<PERSON>_%C5%A0estak\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-Slovenian triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mari<PERSON>_%C5%A0estak\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-Slovenian triple jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marija_%C5%A0estak"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Colombian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_Vargas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_Vargas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabi%C3%A1n_Vargas"}]}, {"year": "1980", "text": "<PERSON>, English footballer, boxer, and manager", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, boxer, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, boxer, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English runner", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Finnish singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English runner", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, English runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, English runner", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1981", "text": "<PERSON>, Chinese footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American mixed martial artist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ty<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Peruvian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>_(footballer,_born_1983)\" title=\"<PERSON> (footballer, born 1983)\"><PERSON></a>, Peruvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>_(footballer,_born_1983)\" title=\"<PERSON> (footballer, born 1983)\"><PERSON></a>, Peruvian footballer", "links": [{"title": "<PERSON> (footballer, born 1983)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>_(footballer,_born_1983)"}]}, {"year": "1983", "text": "<PERSON>, Italian rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Argentinian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_%C3%81l<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_%C3%81l<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sebasti%C3%A1n_%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian actor and model", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, French race car driver", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Greek triple jumper", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Papachristo<PERSON>\" title=\"Paraskevi Papachristou\"><PERSON><PERSON><PERSON></a>, Greek triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Paraske<PERSON> Papachristou\"><PERSON><PERSON><PERSON></a>, Greek triple jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "1989", "text": "<PERSON><PERSON>, singer and songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Welsh footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_footballer)\" title=\"<PERSON> (Welsh footballer)\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_footballer)\" title=\"<PERSON> (Welsh footballer)\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON> (Welsh footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_footballer)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Australian rugby league footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, South Korean singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ho"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Canadian fencer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian fencer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean singer and actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Hongseok\" title=\"Hongseok\"><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hongseok\" title=\"Hongseok\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "Hongseok", "link": "https://wikipedia.org/wiki/Hongseok"}]}, {"year": "1995", "text": "<PERSON>, South Korean singer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Wheein\" title=\"Wheein\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wheein\" title=\"Wheein\"><PERSON></a>, South Korean singer", "links": [{"title": "Wheein", "link": "https://wikipedia.org/wiki/Wheein"}]}, {"year": "1996", "text": "<PERSON><PERSON>, British actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Australian boxer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian boxer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Norwegian dancer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pilling"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>), Thai actor and singer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Suppapong_Udomkaewkanjana\" title=\"Suppapong Udomkaewkanjana\"><PERSON><PERSON><PERSON><PERSON></a> (<PERSON>), Thai actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suppapong_Udomkaewkanjana\" title=\"Suppapong Udomkaewkanjana\"><PERSON><PERSON><PERSON><PERSON></a> (<PERSON>), Thai actor and singer", "links": [{"title": "Suppapong <PERSON>dom<PERSON>ewkanjana", "link": "https://wikipedia.org/wiki/Suppapong_Udomkaewkanjana"}]}, {"year": "2001", "text": "<PERSON>, South Korean rapper, singer and dancer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON><PERSON> (rapper)\"><PERSON></a>, South Korean rapper, singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON><PERSON> (rapper)\"><PERSON></a>, South Korean rapper, singer and dancer", "links": [{"title": "<PERSON><PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(rapper)"}]}], "Deaths": [{"year": "485", "text": "<PERSON><PERSON><PERSON>, Greek mathematician and philosopher (b. 412)", "html": "485 - <a href=\"https://wikipedia.org/wiki/Proclus\" title=\"Pro<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek mathematician and philosopher (b. 412)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Proclus\" title=\"Proclus\"><PERSON><PERSON><PERSON></a>, Greek mathematician and philosopher (b. 412)", "links": [{"title": "Proclus", "link": "https://wikipedia.org/wiki/Proclus"}]}, {"year": "617", "text": "<PERSON><PERSON><PERSON> of Eigg, Irish priest and saint", "html": "617 - <a href=\"https://wikipedia.org/wiki/Donn%C3%A1n_of_Eigg\" title=\"<PERSON><PERSON><PERSON> of Eigg\"><PERSON><PERSON><PERSON> of Eigg</a>, Irish priest and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Donn%C3%A1n_of_Eigg\" title=\"<PERSON><PERSON><PERSON> of Eigg\"><PERSON><PERSON><PERSON> of Eigg</a>, Irish priest and saint", "links": [{"title": "<PERSON><PERSON><PERSON> of Eigg", "link": "https://wikipedia.org/wiki/Donn%C3%A1n_of_Eigg"}]}, {"year": "648", "text": "<PERSON>, empress of the Sui dynasty", "html": "648 - <a href=\"https://wikipedia.org/wiki/Empress_Xiao_(Sui_dynasty)\" title=\"Empress Xiao (Sui dynasty)\"><PERSON></a>, empress of the <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_Xiao_(Sui_dynasty)\" title=\"Empress Xiao (Sui dynasty)\"><PERSON></a>, empress of the <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui dynasty</a>", "links": [{"title": "<PERSON> <PERSON> (Sui dynasty)", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_(Sui_dynasty)"}, {"title": "Sui dynasty", "link": "https://wikipedia.org/wiki/Sui_dynasty"}]}, {"year": "744", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Umayya<PERSON> caliph (b. 706)", "html": "744 - <a href=\"https://wikipedia.org/wiki/Al-Walid_II\" title=\"Al-Walid II\">Al-Walid II</a>, <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad</a> caliph (b. 706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Walid_II\" title=\"Al-Walid II\">Al-Walid II</a>, <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad</a> caliph (b. 706)", "links": [{"title": "Al-Walid II", "link": "https://wikipedia.org/wiki/Al-Walid_II"}, {"title": "Umayyad Caliphate", "link": "https://wikipedia.org/wiki/Umayyad_Caliphate"}]}, {"year": "818", "text": "<PERSON> of Italy, Frankish king (b. 797)", "html": "818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Italy\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a>, Frankish king (b. 797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Italy\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a>, Frankish king (b. 797)", "links": [{"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/Bernard_of_Italy"}]}, {"year": "858", "text": "<PERSON>, pope of the Catholic Church", "html": "858 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_III\" title=\"Pope Benedict III\"><PERSON> III</a>, pope of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_III\" title=\"Pope Benedict III\"><PERSON> III</a>, pope of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "1071", "text": "<PERSON>, Byzantine military commander (b. c. 1045)", "html": "1071 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(kouropalates)\" title=\"<PERSON> (kouropalates)\"><PERSON></a>, Byzantine military commander (b. c. 1045)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(kouropalates)\" title=\"<PERSON> (kouropalates)\"><PERSON></a>, Byzantine military commander (b. c. 1045)", "links": [{"title": "<PERSON> (kouropalates)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(kouropalates)"}]}, {"year": "1080", "text": "<PERSON> of Denmark (b. 1041)", "html": "1080 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" class=\"mw-redirect\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1041)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" class=\"mw-redirect\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1041)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark"}]}, {"year": "1111", "text": "<PERSON> of Molesme, Christian saint and abbot (b. 1027)", "html": "1111 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mo<PERSON>me\" title=\"<PERSON> of Molesme\"><PERSON> of Molesme</a>, Christian saint and abbot (b. 1027)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Molesme\"><PERSON> of Molesme</a>, Christian saint and abbot (b. 1027)", "links": [{"title": "<PERSON> Molesme", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1298", "text": "<PERSON><PERSON><PERSON>, Icelandic bishop (b. 1237)", "html": "1298 - <a href=\"https://wikipedia.org/wiki/%C3%81rni_%C3%9Eorl%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic bishop (b. 1237)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81rni_%C3%9Eorl%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic bishop (b. 1237)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81rni_%C3%9Eorl%C3%A1<PERSON>son"}]}, {"year": "1321", "text": "<PERSON><PERSON><PERSON> of Portugal, daughter of King <PERSON><PERSON><PERSON> of Portugal (b. 1259)", "html": "1321 - <a href=\"https://wikipedia.org/wiki/Blanche_of_Portugal_(1259%E2%80%931321)\" title=\"<PERSON> of Portugal (1259-1321)\"><PERSON><PERSON><PERSON> of Portugal</a>, daughter of King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Portugal\" title=\"<PERSON><PERSON><PERSON> III of Portugal\"><PERSON><PERSON><PERSON> of Portugal</a> (b. 1259)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Blanche_of_Portugal_(1259%E2%80%931321)\" title=\"Blanche of Portugal (1259-1321)\"><PERSON><PERSON><PERSON> of Portugal</a>, daughter of King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Portugal\" title=\"<PERSON><PERSON><PERSON> III of Portugal\"><PERSON><PERSON><PERSON> of Portugal</a> (b. 1259)", "links": [{"title": "Blanche of Portugal (1259-1321)", "link": "https://wikipedia.org/wiki/Blanche_of_Portugal_(1259%E2%80%931321)"}, {"title": "<PERSON><PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/A<PERSON>nso_III_of_Portugal"}]}, {"year": "1331", "text": "<PERSON>, 6th Earl of Oxford, English nobleman (b. 1257)", "html": "1331 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Oxford\" title=\"<PERSON>, 6th Earl of Oxford\"><PERSON>, 6th Earl of Oxford</a>, English nobleman (b. 1257)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_6th_Earl_of_Oxford\" title=\"<PERSON>, 6th Earl of Oxford\"><PERSON>, 6th Earl of Oxford</a>, English nobleman (b. 1257)", "links": [{"title": "<PERSON>, 6th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Oxford"}]}, {"year": "1344", "text": "<PERSON>, King of Armenia", "html": "1344 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_King_of_Armenia\" title=\"<PERSON> II, King of Armenia\"><PERSON> II, King of Armenia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II,_King_of_Armenia\" title=\"<PERSON> II, King of Armenia\"><PERSON> II, King of Armenia</a>", "links": [{"title": "<PERSON>, King of Armenia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_Armenia"}]}, {"year": "1355", "text": "<PERSON>, <PERSON><PERSON> of Venice (b. 1285)", "html": "1355 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Doge_of_Venice\" title=\"<PERSON><PERSON> of Venice\"><PERSON><PERSON> of Venice</a> (b. 1285)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Doge_of_Venice\" title=\"<PERSON><PERSON> of Venice\"><PERSON><PERSON> of Venice</a> (b. 1285)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> of Venice", "link": "https://wikipedia.org/wiki/Doge_of_Venice"}]}, {"year": "1427", "text": "<PERSON>, Duke of Brabant (b. 1403)", "html": "1427 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (b. 1403)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (b. 1403)", "links": [{"title": "<PERSON>, Duke of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1539", "text": "<PERSON>, Duke of Saxony (b. 1471)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxony\" title=\"<PERSON>, Duke of Saxony\"><PERSON>, Duke of Saxony</a> (b. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxony\" title=\"<PERSON>, Duke of Saxony\"><PERSON>, Duke of Saxony</a> (b. 1471)", "links": [{"title": "<PERSON>, Duke of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxony"}]}, {"year": "1574", "text": "<PERSON>, German scholar and translator (b. 1500)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and translator (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and translator (b. 1500)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1669", "text": "<PERSON>, Italian violinist and composer (b. 1605)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1605)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON><PERSON>, Mohawk-born Native American saint (b. 1656)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Mohawk_people\" title=\"Mohawk people\">Mohawk</a>-born <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native American</a> saint (b. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Mohawk_people\" title=\"Mohawk people\">Mohawk</a>-born <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native American</a> saint (b. 1656)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>a"}, {"title": "Mohawk people", "link": "https://wikipedia.org/wiki/Mohawk_people"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}]}, {"year": "1695", "text": "<PERSON><PERSON>, Mexican poet and scholar (b. 1651)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/Juana_In%C3%A9s_de_la_Cruz\" title=\"Juana Inés de la Cruz\"><PERSON><PERSON></a>, Mexican poet and scholar (b. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juana_In%C3%A9s_de_la_Cruz\" title=\"Juana Inés de la Cruz\"><PERSON><PERSON> In<PERSON> Cruz</a>, Mexican poet and scholar (b. 1651)", "links": [{"title": "Juana Inés de la Cruz", "link": "https://wikipedia.org/wiki/Juana_In%C3%A9s_de_la_Cruz"}]}, {"year": "1696", "text": "<PERSON><PERSON>, marquise <PERSON>, French author (b. 1626)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_marquise_de_<PERSON>%C3%A9vign%C3%A9\" title=\"<PERSON>, marquise <PERSON>\"><PERSON>, marquise <PERSON></a>, French author (b. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_marquise_de_<PERSON>%C3%A9vign%C3%A9\" title=\"<PERSON>, marquise <PERSON>\"><PERSON>, marquise <PERSON></a>, French author (b. 1626)", "links": [{"title": "<PERSON><PERSON><PERSON>, marquise <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>,_marquise_de_S%C3%A9vign%C3%A9"}]}, {"year": "1711", "text": "<PERSON>, Holy Roman Emperor (b. 1678)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1678)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1713", "text": "<PERSON>, Polish pastor and theologian (b. 1648)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dogmatician)\" title=\"<PERSON> (dogmatician)\"><PERSON></a>, Polish pastor and theologian (b. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dogmatician)\" title=\"<PERSON> (dogmatician)\"><PERSON></a>, Polish pastor and theologian (b. 1648)", "links": [{"title": "<PERSON> (dogmatician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dogmatician)"}]}, {"year": "1764", "text": "<PERSON>, German lexicographer and composer (b. 1681)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lexicographer and composer (b. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lexicographer and composer (b. 1681)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, American inventor, publisher, and politician, 6th President of Pennsylvania (b. 1706)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, publisher, and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Pennsylvania\" class=\"mw-redirect\" title=\"List of Governors of Pennsylvania\">President of Pennsylvania</a> (b. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, publisher, and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Pennsylvania\" class=\"mw-redirect\" title=\"List of Governors of Pennsylvania\">President of Pennsylvania</a> (b. 1706)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Pennsylvania", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Pennsylvania"}]}, {"year": "1799", "text": "<PERSON>, English surveyor and architect (b. 1728)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surveyor and architect (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surveyor and architect (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, American journalist and author (b. 1758)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American engineer (b. 1762)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (b. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, English engineer and plumber, invented the flush toilet (b. 1810)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and plumber, invented the <a href=\"https://wikipedia.org/wiki/Flush_toilet\" title=\"Flush toilet\">flush toilet</a> (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and plumber, invented the <a href=\"https://wikipedia.org/wiki/Flush_toilet\" title=\"Flush toilet\">flush toilet</a> (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Flush toilet", "link": "https://wikipedia.org/wiki/Flush_toilet"}]}, {"year": "1888", "text": "<PERSON><PERSON> <PERSON><PERSON>, American archaeologist and journalist (b. 1821)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American archaeologist and journalist (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American archaeologist and journalist (b. 1821)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Scottish-Canadian politician, 2nd Prime Minister of Canada (b. 1822)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish-Canadian politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish-Canadian politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1822)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian novelist (b. 1875)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Svetozar_%C4%86orovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian novelist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Svetozar_%C4%86orovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian novelist (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Svetozar_%C4%86orovi%C4%87"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Maltese journalist, author, and philosopher (b. 1860)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Man<PERSON><PERSON>_<PERSON>\" title=\"Man<PERSON><PERSON> Di<PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese journalist, author, and philosopher (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Man<PERSON><PERSON> Di<PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese journalist, author, and philosopher (b. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Di<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Irish lawyer and politician (b. 1852)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Russian painter and stage designer (b. 1863)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Russian painter and stage designer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Russian painter and stage designer (b. 1863)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Georgian director and playwright (b. 1872)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian director and playwright (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian director and playwright (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Dutch lawyer and politician, 28th Prime Minister of the Netherlands (b. 1873)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>uck\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1942", "text": "<PERSON>, French-American physicist and chemist, Nobel Prize laureate (b. 1870)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1944", "text": "<PERSON><PERSON> <PERSON><PERSON>, English cricketer and coach (b. 1867)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_Hearne\" title=\"J. <PERSON><PERSON> Hearne\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and coach (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_Hearne\" title=\"J. <PERSON>. Hearne\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and coach (b. 1867)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>ne"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Greek lieutenant, founded the National and Social Liberation (b. 1893)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lieutenant, founded the <a href=\"https://wikipedia.org/wiki/National_and_Social_Liberation\" title=\"National and Social Liberation\">National and Social Liberation</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lieutenant, founded the <a href=\"https://wikipedia.org/wiki/National_and_Social_Liberation\" title=\"National and Social Liberation\">National and Social Liberation</a> (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "National and Social Liberation", "link": "https://wikipedia.org/wiki/National_and_Social_Liberation"}]}, {"year": "1946", "text": "<PERSON>, Nicaraguan medical doctor, politician and 20th President of Nicaragua (b. 1874)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan medical doctor, politician and 20th <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan medical doctor, politician and 20th <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a> (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Nicaragua", "link": "https://wikipedia.org/wiki/President_of_Nicaragua"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese admiral and politician, 42nd Prime Minister of Japan (b. 1868)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Kantar%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese admiral and politician, 42nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kantar%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese admiral and politician, 42nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kantar%C5%8D_Suzuki"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian lawyer and politician, Romanian Minister of Justice (b. 1900)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Lucre%C8%9Biu_P%C4%83tr%C4%83%C8%99canu\" title=\"Lucrețiu Pătrășcanu\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(Romania)\" class=\"mw-redirect\" title=\"Ministry of Justice (Romania)\">Romanian Minister of Justice</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucre%C8%9Biu_P%C4%83tr%C4%83%C8%99canu\" title=\"Lucrețiu Pătrășcanu\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(Romania)\" class=\"mw-redirect\" title=\"Ministry of Justice (Romania)\">Romanian Minister of Justice</a> (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucre%C8%9Biu_P%C4%83tr%C4%83%C8%99canu"}, {"title": "Ministry of Justice (Romania)", "link": "https://wikipedia.org/wiki/Ministry_of_Justice_(Romania)"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1938)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American physicist and health researcher (b. 1899)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and health researcher (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and health researcher (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer and trumpet player (b. 1908)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and trumpet player (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Allen\"><PERSON></a>, American singer and trumpet player (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Allen"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian philosopher and politician, 2nd President of India (b. 1888)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian philosopher and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian philosopher and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1976", "text": "<PERSON>, Danish biochemist and physiologist, Nobel Prize laureate (b. 1895)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish biochemist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish biochemist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1977", "text": "<PERSON>, Irish cardinal (b. 1913)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, Irish cardinal (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, Irish cardinal (b. 1913)", "links": [{"title": "<PERSON> (cardinal)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cardinal)"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter, bass player, and producer (b. 1939)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, bass player, and producer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, bass player, and producer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian-American ice hockey player (b. 1933)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, French businessman, founded Dassault Aviation (b. 1892)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman, founded <a href=\"https://wikipedia.org/wiki/Dassault_Aviation\" title=\"Dassault Aviation\">Dassault Aviation</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman, founded <a href=\"https://wikipedia.org/wiki/Dassault_Aviation\" title=\"Dassault Aviation\">Dassault Aviation</a> (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Dassault Aviation", "link": "https://wikipedia.org/wiki/Dassault_Aviation"}]}, {"year": "1987", "text": "<PERSON>, English publisher (b. 1901)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English publisher (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English publisher (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor (b. 1923)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Ukrainian-American sculptor and educator (b. 1900)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American sculptor and educator (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American sculptor and educator (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American minister and activist (b. 1936)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Turkish engineer and politician, 8th president of Turkey (b. 1927)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Turgut_%C3%96zal\" title=\"Turg<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish engineer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">president of Turkey</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turgut_%C3%96zal\" title=\"Turg<PERSON> Özal\"><PERSON><PERSON><PERSON></a>, Turkish engineer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">president of Turkey</a> (b. 1927)", "links": [{"title": "Turgut Ö<PERSON>", "link": "https://wikipedia.org/wiki/Turgut_%C3%96zal"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Egyptian scholar and geographer (b. 1928)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Gamal_<PERSON>dan\" title=\"Gama<PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian scholar and geographer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gama<PERSON>_<PERSON>dan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian scholar and geographer (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>dan"}]}, {"year": "1994", "text": "<PERSON>, American psychologist and biologist, Nobel Prize laureate (b. 1913)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1995", "text": "<PERSON>, American sergeant and businessman (b. 1928)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and businessman (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and businessman (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>nik"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Danish poet and mathematician (b. 1905)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(scientist)\" title=\"<PERSON><PERSON> (scientist)\"><PERSON><PERSON></a>, Danish poet and mathematician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(scientist)\" title=\"<PERSON><PERSON> (scientist)\"><PERSON><PERSON></a>, Danish poet and mathematician (b. 1905)", "links": [{"title": "<PERSON><PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(scientist)"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Israeli general, lawyer, and politician, 6th President of Israel (b. 1918)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "1998", "text": "<PERSON>, American photographer, activist, and musician (b. 1941)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer, activist, and musician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer, activist, and musician (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American physician and cardiologist, created the <PERSON> diet (b. 1930)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(nutritionist)\" class=\"mw-redirect\" title=\"<PERSON> (nutritionist)\"><PERSON></a>, American physician and cardiologist, created the <a href=\"https://wikipedia.org/wiki/<PERSON>_diet\" title=\"Atkins diet\">Atkins diet</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(nutritionist)\" class=\"mw-redirect\" title=\"<PERSON> (nutritionist)\"><PERSON></a>, American physician and cardiologist, created the <a href=\"https://wikipedia.org/wiki/Atkins_diet\" title=\"Atkins diet\">Atkins diet</a> (b. 1930)", "links": [{"title": "<PERSON> (nutritionist)", "link": "https://wikipedia.org/wiki/<PERSON>_(nutritionist)"}, {"title": "Atkins diet", "link": "https://wikipedia.org/wiki/Atkins_diet"}]}, {"year": "2003", "text": "<PERSON><PERSON> <PERSON><PERSON>, American race car driver (b. 1936)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American race car driver (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American race car driver (b. 1936)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Jr., American-English philanthropist (b. 1932)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American-English philanthropist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American-English philanthropist (b. 1932)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2003", "text": "<PERSON>, American blues singer, guitarist and songwriter (b. 1934)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues singer, guitarist and songwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues singer, guitarist and songwriter (b. 1934)", "links": [{"title": "Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Greek businessman (b. 1910)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Swiss author and poet (b. 1908)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and poet (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and poet (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, French physician and haematologist (b. 1907)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, French physician and haematologist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, French physician and haematologist (b. 1907)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(physician)"}]}, {"year": "2006", "text": "<PERSON>, American director and producer (b. 1955)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Scott_<PERSON>\" title=\"Scott Brazil\"><PERSON></a>, American director and producer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scott_Brazil\" title=\"Scott Brazil\"><PERSON></a>, American director and producer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Scott_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actor (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>the\"><PERSON></a>, American actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Forsythe"}]}, {"year": "2007", "text": "<PERSON>, American actress, singer, socialite and game show panelist (b. 1910)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, socialite and game show panelist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, socialite and game show panelist (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Caribbean-French poet and politician (b. 1913)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Aim%C3%A9_C%C3%A9saire\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Caribbean-French poet and politician (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aim%C3%A9_C%C3%A9saire\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Caribbean-French poet and politician (b. 1913)", "links": [{"title": "Aimé <PERSON>", "link": "https://wikipedia.org/wiki/Aim%C3%A9_C%C3%A9saire"}]}, {"year": "2008", "text": "<PERSON>, American organist and accordion player (b. 1950)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and accordion player (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and accordion player (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Austrian-Australian pianist and composer (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Australian pianist and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Australian pianist and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Canadian actor (b. 1940)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American artist and author (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, English journalist and author (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and author (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American educator and politician (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Greek singer (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Mi<PERSON>pan<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian journalist, poet, and politician (b. 1912)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Mohapatra\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist, poet, and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist, poet, and politician (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Mo<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American rabbi and author (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American rabbi and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American rabbi and author (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American soldier, lawyer, and politician, 9th United States Secretary of the Army (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Army\" title=\"United States Secretary of the Army\">United States Secretary of the Army</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Army\" title=\"United States Secretary of the Army\">United States Secretary of the Army</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Army", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Army"}]}, {"year": "2013", "text": "<PERSON>, São Toméan politician, Prime Minister of São Tomé and <PERSON><PERSON><PERSON><PERSON><PERSON> (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7a\" title=\"<PERSON>\"><PERSON></a>, São Toméan politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_S%C3%A3o_Tom%C3%A9_and_Pr%C3%ADncipe\" class=\"mw-redirect\" title=\"Prime Minister of São Tomé and Príncipe\">Prime Minister of São Tomé and <PERSON>r<PERSON><PERSON><PERSON></a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7a\" title=\"<PERSON>\"><PERSON></a>, São Toméan politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_S%C3%A3o_Tom%C3%A9_and_Pr%C3%ADncipe\" class=\"mw-redirect\" title=\"Prime Minister of São Tomé and Príncipe\">Prime Minister of São Tomé and Príncipe</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Gra%C3%A7a"}, {"title": "Prime Minister of São Tomé and <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prime_Minister_of_S%C3%A3o_Tom%C3%A9_and_Pr%C3%ADncipe"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Tanzanian <PERSON> singer (b. ≈1910)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Tanzania\" title=\"Tanzania\">Tanzanian</a> <a href=\"https://wikipedia.org/wiki/Taarab\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> singer (b. ≈1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Tanzania\" title=\"Tanzania\">Tanzanian</a> <a href=\"https://wikipedia.org/wiki/Taarab\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> singer (b. ≈1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Tanzania", "link": "https://wikipedia.org/wiki/Tanzania"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taarab"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Norwegian bass player and songwriter (b. 1957)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Yng<PERSON>_<PERSON>\" title=\"<PERSON>ng<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian bass player and songwriter (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yng<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian bass player and songwriter (b. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yng<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian politician, 13th Governor of Karnataka (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"V. S<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_Karnataka\" class=\"mw-redirect\" title=\"Governor of Karnataka\">Governor of Karnataka</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_Karnataka\" class=\"mw-redirect\" title=\"Governor of Karnataka\">Governor of Karnataka</a> (b. 1934)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Karnataka", "link": "https://wikipedia.org/wiki/Governor_of_Karnataka"}]}, {"year": "2014", "text": "<PERSON>, Colombian journalist and author, Nobel Prize laureate (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_M%C3%A1rquez\" title=\"<PERSON>\"><PERSON></a>, Colombian journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_M%C3%A1rquez\" title=\"<PERSON>\"><PERSON></a>, Colombian journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gabriel_Garc%C3%ADa_M%C3%A1rquez"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Serbian-Scottish fashion designer and painter (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-Scottish fashion designer and painter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-Scottish fashion designer and painter (b. 1922)", "links": [{"title": "Bernat Klein", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Klein"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish-American architect and academic (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Wojciech_Le%C5%9B<PERSON><PERSON>\" title=\"Woj<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish-American architect and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wojciech_Le%C5%9B<PERSON><PERSON>\" title=\"Woj<PERSON><PERSON>\">Wo<PERSON><PERSON><PERSON></a>, Polish-American architect and academic (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wojciech_Le%C5%9B<PERSON>owski"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Malaysian lawyer and politician (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian lawyer and politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian lawyer and politician (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American soldier, lawyer, and politician (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American businessman and philanthropist (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and philanthropist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and philanthropist (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Scotty_Probasco"}]}, {"year": "2015", "text": "<PERSON>, American general (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON>, American businessman and philanthropist (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and philanthropist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and philanthropist (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, American wrestler (b. 1969)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Chyna\" title=\"Chyna\"><PERSON><PERSON></a>, American wrestler (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chyna\" title=\"<PERSON>yna\"><PERSON><PERSON></a>, American wrestler (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chyna"}]}, {"year": "2016", "text": "<PERSON>, American actress (b. 1925)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American political matriarch and literacy advocate, First Lady of the United States (1989-1993), and Second Lady of the United States (1981-1989) (b. 1925)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political matriarch and literacy advocate, <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (1989-1993), and <a href=\"https://wikipedia.org/wiki/Second_ladies_and_gentlemen_of_the_United_States\" title=\"Second ladies and gentlemen of the United States\">Second Lady of the United States</a> (1981-1989) (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political matriarch and literacy advocate, <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (1989-1993), and <a href=\"https://wikipedia.org/wiki/Second_ladies_and_gentlemen_of_the_United_States\" title=\"Second ladies and gentlemen of the United States\">Second Lady of the United States</a> (1981-1989) (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}, {"title": "Second ladies and gentlemen of the United States", "link": "https://wikipedia.org/wiki/Second_ladies_and_gentlemen_of_the_United_States"}]}, {"year": "2018", "text": "<PERSON>, American radio personality (b. 1934)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio personality (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio personality (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Peruvian lawyer and politician, 61st and 64th President of Peru (b. 1949)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Alan_<PERSON>c%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Peruvian lawyer and politician, 61st and 64th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Peruvian lawyer and politician, 61st and 64th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alan_Garc%C3%ADa"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Romanian pianist (b. 1945)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian pianist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian pianist (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}