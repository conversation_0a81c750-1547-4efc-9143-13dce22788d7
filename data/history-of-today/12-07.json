{"date": "December 7", "url": "https://wikipedia.org/wiki/December_7", "data": {"Events": [{"year": "43 BC", "text": "<PERSON> is assassinated in Formia on orders of <PERSON>.", "html": "43 BC - 43 BC - <a href=\"https://wikipedia.org/wiki/Cicero\" title=\"Cicero\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Formia\" title=\"Formia\">Formia</a> on orders of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "43 BC - <a href=\"https://wikipedia.org/wiki/Cicero\" title=\"Cicero\">Marcus <PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Formia\" title=\"Formia\">Formia</a> on orders of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Cicero", "link": "https://wikipedia.org/wiki/Cicero"}, {"title": "Formia", "link": "https://wikipedia.org/wiki/Formia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "574", "text": "Byzantine Emperor <PERSON>, suffering recurring seizures of insanity, adopts his general <PERSON><PERSON><PERSON> and proclaims him as Caesar.", "html": "574 - Byzantine Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"<PERSON> II\"><PERSON> II</a>, suffering recurring seizures of insanity, adopts his general <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_II_Constantine\" title=\"<PERSON><PERSON><PERSON> II Constantine\"><PERSON><PERSON><PERSON></a> and proclaims him as <i><a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\">Caesar</a></i>.", "no_year_html": "Byzantine Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"<PERSON> II\"><PERSON> II</a>, suffering recurring seizures of insanity, adopts his general <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_Constantine\" title=\"<PERSON><PERSON><PERSON> II Constantine\"><PERSON><PERSON><PERSON></a> and proclaims him as <i><a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\">Caesar</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> II <PERSON>", "link": "https://wikipedia.org/wiki/Tiberius_II_Constantine"}, {"title": "<PERSON> (title)", "link": "https://wikipedia.org/wiki/<PERSON>_(title)"}]}, {"year": "927", "text": "The Sajid emir of Adharbayjan, <PERSON> is defeated and captured by the Qarmatians near Kufa.", "html": "927 - The <a href=\"https://wikipedia.org/wiki/Sajid_dynasty\" title=\"Sajid dynasty\">Sajid</a> emir of <a href=\"https://wikipedia.org/wiki/Adharbayjan\" class=\"mw-redirect\" title=\"Adharbayjan\">Adharbayjan</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27l-Saj\" title=\"<PERSON> ibn <PERSON>-Sa<PERSON>\"><PERSON> ibn <PERSON></a> is defeated and captured by the <a href=\"https://wikipedia.org/wiki/Qarmatians\" title=\"Qarmatians\">Qarmatians</a> near <a href=\"https://wikipedia.org/wiki/Kufa\" title=\"Kufa\">Kufa</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sajid_dynasty\" title=\"Sajid dynasty\">Sajid</a> emir of <a href=\"https://wikipedia.org/wiki/Adharbayjan\" class=\"mw-redirect\" title=\"Adharbayjan\">Adharbayjan</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27l-Saj\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a> is defeated and captured by the <a href=\"https://wikipedia.org/wiki/Qarmatians\" title=\"Qarmatians\">Qarmatians</a> near <a href=\"https://wikipedia.org/wiki/Kufa\" title=\"Kufa\">Kufa</a>.", "links": [{"title": "Sajid dynasty", "link": "https://wikipedia.org/wiki/Sajid_dynasty"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adharbayjan"}, {"title": "<PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27l-Saj"}, {"title": "Qarmatians", "link": "https://wikipedia.org/wiki/Qarmatians"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>fa"}]}, {"year": "1703", "text": "The Great Storm of 1703, the greatest windstorm ever recorded in the southern part of Great Britain, makes landfall. Winds gust up to 120 mph, and 9,000 people die.", "html": "1703 - The <a href=\"https://wikipedia.org/wiki/Great_Storm_of_1703\" class=\"mw-redirect\" title=\"Great Storm of 1703\">Great Storm of 1703</a>, the greatest windstorm ever recorded in the southern part of Great Britain, makes landfall. Winds gust up to 120 mph, and 9,000 people die.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Storm_of_1703\" class=\"mw-redirect\" title=\"Great Storm of 1703\">Great Storm of 1703</a>, the greatest windstorm ever recorded in the southern part of Great Britain, makes landfall. Winds gust up to 120 mph, and 9,000 people die.", "links": [{"title": "Great Storm of 1703", "link": "https://wikipedia.org/wiki/Great_Storm_of_1703"}]}, {"year": "1724", "text": "Tumult of Thorn: Religious unrest is followed by the execution of nine Protestant citizens and the mayor of Thorn (Toruń) by Polish authorities.", "html": "1724 - <a href=\"https://wikipedia.org/wiki/Tumult_of_Thorn_(Toru%C5%84)\" title=\"Tumult of Thorn (Toruń)\">Tumult of Thorn</a>: Religious unrest is followed by the execution of nine <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestant</a> citizens and the mayor of <a href=\"https://wikipedia.org/wiki/Toru%C5%84\" title=\"Toruń\">Thorn (Toruń)</a> by Polish authorities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>mu<PERSON>_of_Thorn_(Toru%C5%84)\" title=\"Tumult of Thorn (Toruń)\">Tumu<PERSON> of Thorn</a>: Religious unrest is followed by the execution of nine <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestant</a> citizens and the mayor of <a href=\"https://wikipedia.org/wiki/Toru%C5%84\" title=\"Toruń\">Thorn (Toruń)</a> by Polish authorities.", "links": [{"title": "Tumult of Thorn (Toruń)", "link": "https://wikipedia.org/wiki/Tumult_of_Thorn_(Toru%C5%84)"}, {"title": "Protestantism", "link": "https://wikipedia.org/wiki/Protestantism"}, {"title": "Toruń", "link": "https://wikipedia.org/wiki/Toru%C5%84"}]}, {"year": "1732", "text": "The Royal Opera House opens at Covent Garden, London, England.", "html": "1732 - The <a href=\"https://wikipedia.org/wiki/Royal_Opera_House\" title=\"Royal Opera House\">Royal Opera House</a> opens at <a href=\"https://wikipedia.org/wiki/Covent_Garden\" title=\"Covent Garden\">Covent Garden</a>, London, England.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Opera_House\" title=\"Royal Opera House\">Royal Opera House</a> opens at <a href=\"https://wikipedia.org/wiki/Covent_Garden\" title=\"Covent Garden\">Covent Garden</a>, London, England.", "links": [{"title": "Royal Opera House", "link": "https://wikipedia.org/wiki/Royal_Opera_House"}, {"title": "Covent Garden", "link": "https://wikipedia.org/wiki/Covent_Garden"}]}, {"year": "1776", "text": "<PERSON>, <PERSON>, arranges to enter the American military as a major general.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, arranges to enter the American military as a major general.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Marquis <PERSON></a>, arranges to enter the American military as a major general.", "links": [{"title": "<PERSON>, Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "Delaware becomes the first state to ratify the United States Constitution.", "html": "1787 - <a href=\"https://wikipedia.org/wiki/Delaware\" title=\"Delaware\">Delaware</a> becomes the first state to ratify the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Delaware\" title=\"Delaware\">Delaware</a> becomes the first state to ratify the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a>.", "links": [{"title": "Delaware", "link": "https://wikipedia.org/wiki/Delaware"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}]}, {"year": "1837", "text": "The Battle of Montgomery's Tavern, the only battle of the Upper Canada Rebellion, takes place in Toronto, where the rebels are quickly defeated.", "html": "1837 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Montgomery%27s_Tavern\" title=\"Battle of Montgomery's Tavern\">Battle of Montgomery's Tavern</a>, the only battle of the <a href=\"https://wikipedia.org/wiki/Upper_Canada_Rebellion\" title=\"Upper Canada Rebellion\">Upper Canada Rebellion</a>, takes place in <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a>, where the rebels are quickly defeated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Montgomery%27s_Tavern\" title=\"Battle of Montgomery's Tavern\">Battle of Montgomery's Tavern</a>, the only battle of the <a href=\"https://wikipedia.org/wiki/Upper_Canada_Rebellion\" title=\"Upper Canada Rebellion\">Upper Canada Rebellion</a>, takes place in <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a>, where the rebels are quickly defeated.", "links": [{"title": "Battle of Montgomery's Tavern", "link": "https://wikipedia.org/wiki/Battle_of_Montgomery%27s_Tavern"}, {"title": "Upper Canada Rebellion", "link": "https://wikipedia.org/wiki/Upper_Canada_Rebellion"}, {"title": "Toronto", "link": "https://wikipedia.org/wiki/Toronto"}]}, {"year": "1842", "text": "First concert of the New York Philharmonic, founded by <PERSON><PERSON><PERSON>.", "html": "1842 - First concert of the <a href=\"https://wikipedia.org/wiki/New_York_Philharmonic\" title=\"New York Philharmonic\">New York Philharmonic</a>, founded by <a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Urel<PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "First concert of the <a href=\"https://wikipedia.org/wiki/New_York_Philharmonic\" title=\"New York Philharmonic\">New York Philharmonic</a>, founded by <a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Urel<PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "New York Philharmonic", "link": "https://wikipedia.org/wiki/New_York_Philharmonic"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "Comparative fuel trials begin between warships HMS Spiteful and HMS Peterel: Spiteful was the first warship powered solely by fuel oil, and the trials led to the obsolescence of coal in ships of the Royal Navy.", "html": "1904 - Comparative fuel trials begin between <a href=\"https://wikipedia.org/wiki/Warship\" title=\"Warship\">warships</a> <a href=\"https://wikipedia.org/wiki/HMS_Spiteful_(1899)\" title=\"HMS Spiteful (1899)\">HMS <i>Spiteful</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Peterel_(1899)\" title=\"HMS Peterel (1899)\">HMS <i>Peterel</i></a>: <i>Spiteful</i> was the first warship powered solely by <a href=\"https://wikipedia.org/wiki/Fuel_oil\" title=\"Fuel oil\">fuel oil</a>, and the trials led to the obsolescence of coal in ships of the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>.", "no_year_html": "Comparative fuel trials begin between <a href=\"https://wikipedia.org/wiki/Warship\" title=\"Warship\">warships</a> <a href=\"https://wikipedia.org/wiki/HMS_Spiteful_(1899)\" title=\"HMS Spiteful (1899)\">HMS <i>Spiteful</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Peterel_(1899)\" title=\"HMS Peterel (1899)\">HMS <i>Peterel</i></a>: <i>Spiteful</i> was the first warship powered solely by <a href=\"https://wikipedia.org/wiki/Fuel_oil\" title=\"Fuel oil\">fuel oil</a>, and the trials led to the obsolescence of coal in ships of the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>.", "links": [{"title": "Warship", "link": "https://wikipedia.org/wiki/Warship"}, {"title": "HMS Spiteful (1899)", "link": "https://wikipedia.org/wiki/HMS_Spiteful_(1899)"}, {"title": "HMS Peterel (1899)", "link": "https://wikipedia.org/wiki/HMS_Peterel_(1899)"}, {"title": "Fuel oil", "link": "https://wikipedia.org/wiki/Fuel_oil"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}]}, {"year": "1917", "text": "World War I: The United States declares war on Austria-Hungary.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The United States declares war on <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The United States declares war on <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}]}, {"year": "1922", "text": "The Parliament of Northern Ireland votes to remain a part of the United Kingdom and not unify with Southern Ireland.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_Northern_Ireland\" title=\"Parliament of Northern Ireland\">Parliament of Northern Ireland</a> votes to remain a part of the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> and not unify with <a href=\"https://wikipedia.org/wiki/Southern_Ireland_(1921%E2%80%9322)\" class=\"mw-redirect\" title=\"Southern Ireland (1921-22)\">Southern Ireland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_Northern_Ireland\" title=\"Parliament of Northern Ireland\">Parliament of Northern Ireland</a> votes to remain a part of the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> and not unify with <a href=\"https://wikipedia.org/wiki/Southern_Ireland_(1921%E2%80%9322)\" class=\"mw-redirect\" title=\"Southern Ireland (1921-22)\">Southern Ireland</a>.", "links": [{"title": "Parliament of Northern Ireland", "link": "https://wikipedia.org/wiki/Parliament_of_Northern_Ireland"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Southern Ireland (1921-22)", "link": "https://wikipedia.org/wiki/Southern_Ireland_(1921%E2%80%9322)"}]}, {"year": "1930", "text": "W1XAV in Boston, Massachusetts telecasts video from the CBS radio orchestra program, The Fox Trappers. The telecast also includes the first television advertisement in the United States, for I.J. Fox Furriers, which also sponsored the radio show.", "html": "1930 - <a href=\"https://wikipedia.org/wiki/W1WX\" title=\"W1WX\">W1XAV</a> in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston, Massachusetts</a> telecasts video from the <a href=\"https://wikipedia.org/wiki/CBS\" title=\"CBS\">CBS</a> radio orchestra program, <i>The Fox Trappers</i>. The telecast also includes the first <a href=\"https://wikipedia.org/wiki/Television_advertisement\" title=\"Television advertisement\">television advertisement</a> in the United States, for I.J. Fox Furriers, which also sponsored the radio show.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W1WX\" title=\"W1WX\">W1XAV</a> in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston, Massachusetts</a> telecasts video from the <a href=\"https://wikipedia.org/wiki/CBS\" title=\"CBS\">CBS</a> radio orchestra program, <i>The Fox Trappers</i>. The telecast also includes the first <a href=\"https://wikipedia.org/wiki/Television_advertisement\" title=\"Television advertisement\">television advertisement</a> in the United States, for I.J. Fox Furriers, which also sponsored the radio show.", "links": [{"title": "W1WX", "link": "https://wikipedia.org/wiki/W1WX"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}, {"title": "CBS", "link": "https://wikipedia.org/wiki/CBS"}, {"title": "Television advertisement", "link": "https://wikipedia.org/wiki/Television_advertisement"}]}, {"year": "1932", "text": "German-born Swiss physicist <PERSON> is granted an American visa.", "html": "1932 - German-born Swiss <a href=\"https://wikipedia.org/wiki/Physicist\" title=\"Physicist\">physicist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is granted an American <a href=\"https://wikipedia.org/wiki/Travel_visa\" title=\"Travel visa\">visa</a>.", "no_year_html": "German-born Swiss <a href=\"https://wikipedia.org/wiki/Physicist\" title=\"Physicist\">physicist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is granted an American <a href=\"https://wikipedia.org/wiki/Travel_visa\" title=\"Travel visa\">visa</a>.", "links": [{"title": "Physicist", "link": "https://wikipedia.org/wiki/Physicist"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Travel visa", "link": "https://wikipedia.org/wiki/Travel_visa"}]}, {"year": "1936", "text": "Australian cricketer <PERSON> becomes the first player to score centuries in four consecutive Test innings.", "html": "1936 - Australian <a href=\"https://wikipedia.org/wiki/Cricket\" title=\"Cricket\">cricketer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first player to score <a href=\"https://wikipedia.org/wiki/Century_(cricket)\" title=\"Century (cricket)\">centuries</a> in four consecutive <a href=\"https://wikipedia.org/wiki/Test_cricket\" title=\"Test cricket\">Test</a> innings.", "no_year_html": "Australian <a href=\"https://wikipedia.org/wiki/Cricket\" title=\"Cricket\">cricketer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first player to score <a href=\"https://wikipedia.org/wiki/Century_(cricket)\" title=\"Century (cricket)\">centuries</a> in four consecutive <a href=\"https://wikipedia.org/wiki/Test_cricket\" title=\"Test cricket\">Test</a> innings.", "links": [{"title": "Cricket", "link": "https://wikipedia.org/wiki/Cricket"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (cricket)", "link": "https://wikipedia.org/wiki/Century_(cricket)"}, {"title": "Test cricket", "link": "https://wikipedia.org/wiki/Test_cricket"}]}, {"year": "1941", "text": "World War II: Attack on Pearl Harbor: The Imperial Japanese Navy carries out a surprise attack on the United States Pacific Fleet and its defending Army and Marine air forces at Pearl Harbor, Hawaii. (For Japan's near-simultaneous attacks on Eastern Hemisphere targets, see December 8.)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Attack_on_Pearl_Harbor\" title=\"Attack on Pearl Harbor\">Attack on Pearl Harbor</a>: The <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> carries out a surprise attack on the <a href=\"https://wikipedia.org/wiki/United_States_Pacific_Fleet\" title=\"United States Pacific Fleet\">United States Pacific Fleet</a> and its defending <a href=\"https://wikipedia.org/wiki/United_States_Army_Air_Forces\" title=\"United States Army Air Forces\">Army</a> and Marine air forces at <a href=\"https://wikipedia.org/wiki/Naval_Station_Pearl_Harbor\" title=\"Naval Station Pearl Harbor\">Pearl Harbor</a>, Hawaii. (For Japan's near-simultaneous attacks on <a href=\"https://wikipedia.org/wiki/Eastern_Hemisphere\" title=\"Eastern Hemisphere\">Eastern Hemisphere</a> targets, see <a href=\"https://wikipedia.org/wiki/December_8\" title=\"December 8\">December 8</a>.)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Attack_on_Pearl_Harbor\" title=\"Attack on Pearl Harbor\">Attack on Pearl Harbor</a>: The <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> carries out a surprise attack on the <a href=\"https://wikipedia.org/wiki/United_States_Pacific_Fleet\" title=\"United States Pacific Fleet\">United States Pacific Fleet</a> and its defending <a href=\"https://wikipedia.org/wiki/United_States_Army_Air_Forces\" title=\"United States Army Air Forces\">Army</a> and Marine air forces at <a href=\"https://wikipedia.org/wiki/Naval_Station_Pearl_Harbor\" title=\"Naval Station Pearl Harbor\">Pearl Harbor</a>, Hawaii. (For Japan's near-simultaneous attacks on <a href=\"https://wikipedia.org/wiki/Eastern_Hemisphere\" title=\"Eastern Hemisphere\">Eastern Hemisphere</a> targets, see <a href=\"https://wikipedia.org/wiki/December_8\" title=\"December 8\">December 8</a>.)", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Attack on Pearl Harbor", "link": "https://wikipedia.org/wiki/Attack_on_Pearl_Harbor"}, {"title": "Imperial Japanese Navy", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Navy"}, {"title": "United States Pacific Fleet", "link": "https://wikipedia.org/wiki/United_States_Pacific_Fleet"}, {"title": "United States Army Air Forces", "link": "https://wikipedia.org/wiki/United_States_Army_Air_Forces"}, {"title": "Naval Station Pearl Harbor", "link": "https://wikipedia.org/wiki/Naval_Station_Pearl_Harbor"}, {"title": "Eastern Hemisphere", "link": "https://wikipedia.org/wiki/Eastern_Hemisphere"}, {"title": "December 8", "link": "https://wikipedia.org/wiki/December_8"}]}, {"year": "1942", "text": "World War II: British commandos conduct Operation Frankton, a raid on shipping in Bordeaux harbour.", "html": "1942 - World War II: British commandos conduct <a href=\"https://wikipedia.org/wiki/Operation_Frankton\" title=\"Operation Frankton\">Operation Frankton</a>, a raid on shipping in <a href=\"https://wikipedia.org/wiki/Bordeaux\" title=\"Bordeaux\">Bordeaux</a> harbour.", "no_year_html": "World War II: British commandos conduct <a href=\"https://wikipedia.org/wiki/Operation_Frankton\" title=\"Operation Frankton\">Operation Frankton</a>, a raid on shipping in <a href=\"https://wikipedia.org/wiki/Bordeaux\" title=\"Bordeaux\">Bordeaux</a> harbour.", "links": [{"title": "Operation Frankton", "link": "https://wikipedia.org/wiki/Operation_<PERSON><PERSON>"}, {"title": "Bordeaux", "link": "https://wikipedia.org/wiki/Bordeaux"}]}, {"year": "1944", "text": "An earthquake along the coast of Wakayama Prefecture in Japan causes a tsunami which kills 1,223 people.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/1944_T%C5%8Dnankai_earthquake\" title=\"1944 Tōnankai earthquake\">An earthquake along the coast of Wakayama Prefecture</a> in Japan causes a tsunami which kills 1,223 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1944_T%C5%8Dnankai_earthquake\" title=\"1944 Tōnankai earthquake\">An earthquake along the coast of Wakayama Prefecture</a> in Japan causes a tsunami which kills 1,223 people.", "links": [{"title": "1944 Tōnankai earthquake", "link": "https://wikipedia.org/wiki/1944_T%C5%8Dnankai_earthquake"}]}, {"year": "1946", "text": "A fire at the Winecoff Hotel in Atlanta, Georgia kills 119 people, the deadliest hotel fire in U.S. history.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Winecoff_Hotel_fire\" title=\"Winecoff Hotel fire\">A fire at the Winecoff Hotel</a> in <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, Georgia</a> kills 119 people, the deadliest hotel fire in U.S. history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Winecoff_Hotel_fire\" title=\"Winecoff Hotel fire\">A fire at the Winecoff Hotel</a> in <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, Georgia</a> kills 119 people, the deadliest hotel fire in U.S. history.", "links": [{"title": "Winecoff Hotel fire", "link": "https://wikipedia.org/wiki/Winecoff_Hotel_fire"}, {"title": "Atlanta", "link": "https://wikipedia.org/wiki/Atlanta"}]}, {"year": "1949", "text": "Chinese Civil War: The Government of the Republic of China moves from Nanjing to Taipei, Taiwan.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Chinese_Civil_War\" title=\"Chinese Civil War\">Chinese Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Government_of_the_Republic_of_China\" title=\"Government of the Republic of China\">Government of the Republic of China</a> <a href=\"https://wikipedia.org/wiki/Retreat_of_the_government_of_the_Republic_of_China_to_Taiwan\" title=\"Retreat of the government of the Republic of China to Taiwan\">moves</a> from <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a> to <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a>, <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chinese_Civil_War\" title=\"Chinese Civil War\">Chinese Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Government_of_the_Republic_of_China\" title=\"Government of the Republic of China\">Government of the Republic of China</a> <a href=\"https://wikipedia.org/wiki/Retreat_of_the_government_of_the_Republic_of_China_to_Taiwan\" title=\"Retreat of the government of the Republic of China to Taiwan\">moves</a> from <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a> to <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a>, <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>.", "links": [{"title": "Chinese Civil War", "link": "https://wikipedia.org/wiki/Chinese_Civil_War"}, {"title": "Government of the Republic of China", "link": "https://wikipedia.org/wiki/Government_of_the_Republic_of_China"}, {"title": "Retreat of the government of the Republic of China to Taiwan", "link": "https://wikipedia.org/wiki/Retreat_of_the_government_of_the_Republic_of_China_to_Taiwan"}, {"title": "Nanjing", "link": "https://wikipedia.org/wiki/Nanjing"}, {"title": "Taipei", "link": "https://wikipedia.org/wiki/Taipei"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}]}, {"year": "1962", "text": "<PERSON> <PERSON> of Monaco revises the principality's constitution, devolving some of his power to advisory and legislative councils.", "html": "1962 - Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON><PERSON>, Prince of Monaco\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Monaco\" title=\"Monaco\">Monaco</a> revises the <a href=\"https://wikipedia.org/wiki/Principality\" title=\"Principality\">principality</a>'s <a href=\"https://wikipedia.org/wiki/Constitution_of_Monaco\" title=\"Constitution of Monaco\">constitution</a>, devolving some of his power to advisory and legislative councils.", "no_year_html": "Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON><PERSON>, Prince of Monaco\"><PERSON><PERSON> III</a> of <a href=\"https://wikipedia.org/wiki/Monaco\" title=\"Monaco\">Monaco</a> revises the <a href=\"https://wikipedia.org/wiki/Principality\" title=\"Principality\">principality</a>'s <a href=\"https://wikipedia.org/wiki/Constitution_of_Monaco\" title=\"Constitution of Monaco\">constitution</a>, devolving some of his power to advisory and legislative councils.", "links": [{"title": "<PERSON><PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco"}, {"title": "Monaco", "link": "https://wikipedia.org/wiki/Monaco"}, {"title": "Principality", "link": "https://wikipedia.org/wiki/Principality"}, {"title": "Constitution of Monaco", "link": "https://wikipedia.org/wiki/Constitution_of_Monaco"}]}, {"year": "1963", "text": "Instant replay makes its debut during the Army-Navy football game in Philadelphia, Pennsylvania, United States.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Instant_replay\" title=\"Instant replay\">Instant replay</a> makes its debut during the <a href=\"https://wikipedia.org/wiki/Army%E2%80%93Navy_Game\" title=\"Army-Navy Game\">Army-Navy football game</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia, Pennsylvania</a>, United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Instant_replay\" title=\"Instant replay\">Instant replay</a> makes its debut during the <a href=\"https://wikipedia.org/wiki/Army%E2%80%93Navy_Game\" title=\"Army-Navy Game\">Army-Navy football game</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia, Pennsylvania</a>, United States.", "links": [{"title": "Instant replay", "link": "https://wikipedia.org/wiki/Instant_replay"}, {"title": "Army-Navy Game", "link": "https://wikipedia.org/wiki/Army%E2%80%93Navy_Game"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1965", "text": "Pope <PERSON> and Patriarch <PERSON><PERSON><PERSON> I simultaneously revoke mutual excommunications that had been in place since 1054.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> VI\">Pope <PERSON> VI</a> and <a href=\"https://wikipedia.org/wiki/Athena<PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> I of Constantinople\">Patriarch <PERSON><PERSON><PERSON> I</a> <a href=\"https://wikipedia.org/wiki/Catholic%E2%80%93Orthodox_Joint_Declaration_of_1965\" title=\"Catholic-Orthodox Joint Declaration of 1965\">simultaneously revoke</a> mutual <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunications</a> that had been in place since <a href=\"https://wikipedia.org/wiki/1054\" title=\"1054\">1054</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Paul <PERSON>\">Pope <PERSON> VI</a> and <a href=\"https://wikipedia.org/wiki/Athena<PERSON>as_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> I of Constantinople\">Patriarch <PERSON><PERSON><PERSON> I</a> <a href=\"https://wikipedia.org/wiki/Catholic%E2%80%93Orthodox_Joint_Declaration_of_1965\" title=\"Catholic-Orthodox Joint Declaration of 1965\">simultaneously revoke</a> mutual <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunications</a> that had been in place since <a href=\"https://wikipedia.org/wiki/1054\" title=\"1054\">1054</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Athenagoras_I_of_Constantinople"}, {"title": "Catholic-Orthodox Joint Declaration of 1965", "link": "https://wikipedia.org/wiki/Catholic%E2%80%93Orthodox_Joint_Declaration_of_1965"}, {"title": "Excommunication", "link": "https://wikipedia.org/wiki/Excommunication"}, {"title": "1054", "link": "https://wikipedia.org/wiki/1054"}]}, {"year": "1971", "text": "The Battle of Sylhet is fought between the Pakistani military and the Indian Army.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Sylhet\" title=\"Battle of Sylhet\">Battle of Sylhet</a> is fought between the <a href=\"https://wikipedia.org/wiki/Pakistan_Armed_Forces\" title=\"Pakistan Armed Forces\">Pakistani military</a> and the <a href=\"https://wikipedia.org/wiki/Indian_Army\" title=\"Indian Army\">Indian Army</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Sylhet\" title=\"Battle of Sylhet\">Battle of Sylhet</a> is fought between the <a href=\"https://wikipedia.org/wiki/Pakistan_Armed_Forces\" title=\"Pakistan Armed Forces\">Pakistani military</a> and the <a href=\"https://wikipedia.org/wiki/Indian_Army\" title=\"Indian Army\">Indian Army</a>.", "links": [{"title": "Battle of Sylhet", "link": "https://wikipedia.org/wiki/Battle_of_Sylhet"}, {"title": "Pakistan Armed Forces", "link": "https://wikipedia.org/wiki/Pakistan_Armed_Forces"}, {"title": "Indian Army", "link": "https://wikipedia.org/wiki/Indian_Army"}]}, {"year": "1971", "text": "Pakistan President <PERSON><PERSON> announces the formation of a coalition government with <PERSON><PERSON><PERSON> as Prime Minister and <PERSON><PERSON><PERSON><PERSON> as Deputy Prime Minister.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> announces the formation of a coalition government with <a href=\"https://wikipedia.org/wiki/<PERSON>uru<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> as Prime Minister and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> as Deputy Prime Minister.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> announces the formation of a coalition government with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> as Prime Minister and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> as Deputy Prime Minister.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nuru<PERSON>_Amin"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "Apollo 17, the last Apollo Moon mission, is launched. The crew takes the photograph known as The Blue Marble as they leave the Earth.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Apollo_17\" title=\"Apollo 17\">Apollo 17</a>, the last <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo Moon mission</a>, is launched. The crew takes the photograph known as <i><a href=\"https://wikipedia.org/wiki/The_Blue_Marble\" title=\"The Blue Marble\">The Blue Marble</a></i> as they leave the Earth.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_17\" title=\"Apollo 17\">Apollo 17</a>, the last <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo Moon mission</a>, is launched. The crew takes the photograph known as <i><a href=\"https://wikipedia.org/wiki/The_Blue_Marble\" title=\"The Blue Marble\">The Blue Marble</a></i> as they leave the Earth.", "links": [{"title": "Apollo 17", "link": "https://wikipedia.org/wiki/Apollo_17"}, {"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "The Blue Marble", "link": "https://wikipedia.org/wiki/The_Blue_Marble"}]}, {"year": "1982", "text": "In Texas, <PERSON>, becomes the first person to be executed by lethal injection in the United States.", "html": "1982 - In <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, becomes the first person to be <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">executed</a> by <a href=\"https://wikipedia.org/wiki/Lethal_injection\" title=\"Lethal injection\">lethal injection</a> in the United States.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON> Jr.</a>, becomes the first person to be <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">executed</a> by <a href=\"https://wikipedia.org/wiki/Lethal_injection\" title=\"Lethal injection\">lethal injection</a> in the United States.", "links": [{"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Capital punishment", "link": "https://wikipedia.org/wiki/Capital_punishment"}, {"title": "Lethal injection", "link": "https://wikipedia.org/wiki/Lethal_injection"}]}, {"year": "1982", "text": "The Senior Road Tower collapses in less than 17 seconds.  Five workers on the tower are killed and three workers on a building nearby are injured.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/Senior_Road_Tower\" title=\"Senior Road Tower\">Senior Road Tower</a> collapses in less than 17 seconds. Five workers on the tower are killed and three workers on a building nearby are injured.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Senior_Road_Tower\" title=\"Senior Road Tower\">Senior Road Tower</a> collapses in less than 17 seconds. Five workers on the tower are killed and three workers on a building nearby are injured.", "links": [{"title": "Senior Road Tower", "link": "https://wikipedia.org/wiki/Senior_Road_Tower"}]}, {"year": "1983", "text": "An Iberia Airlines Boeing 727 collides with an Aviaco DC-9 in dense fog while the two airliners are taxiing down the runway at Madrid-Barajas Airport, killing 93 people.", "html": "1983 - An <a href=\"https://wikipedia.org/wiki/Iberia_(airline)\" title=\"Iberia (airline)\">Iberia Airlines</a> <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a> <a href=\"https://wikipedia.org/wiki/Madrid_runway_disaster\" class=\"mw-redirect\" title=\"Madrid runway disaster\">collides</a> with an <a href=\"https://wikipedia.org/wiki/Aviaco\" title=\"Aviaco\">Aviaco</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">DC-9</a> in dense fog while the two airliners are taxiing down the runway at <a href=\"https://wikipedia.org/wiki/Madrid%E2%80%93Barajas_Airport\" title=\"Madrid-Barajas Airport\">Madrid-Barajas Airport</a>, killing 93 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Iberia_(airline)\" title=\"Iberia (airline)\">Iberia Airlines</a> <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a> <a href=\"https://wikipedia.org/wiki/Madrid_runway_disaster\" class=\"mw-redirect\" title=\"Madrid runway disaster\">collides</a> with an <a href=\"https://wikipedia.org/wiki/Aviaco\" title=\"Aviaco\">Aviaco</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">DC-9</a> in dense fog while the two airliners are taxiing down the runway at <a href=\"https://wikipedia.org/wiki/Madrid%E2%80%93Barajas_Airport\" title=\"Madrid-Barajas Airport\">Madrid-Barajas Airport</a>, killing 93 people.", "links": [{"title": "Iberia (airline)", "link": "https://wikipedia.org/wiki/Iberia_(airline)"}, {"title": "Boeing 727", "link": "https://wikipedia.org/wiki/Boeing_727"}, {"title": "Madrid runway disaster", "link": "https://wikipedia.org/wiki/Madrid_runway_disaster"}, {"title": "Aviaco", "link": "https://wikipedia.org/wiki/Aviaco"}, {"title": "McDonnell Douglas DC-9", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_DC-9"}, {"title": "Madrid-Barajas Airport", "link": "https://wikipedia.org/wiki/Madrid%E2%80%93Barajas_Airport"}]}, {"year": "1987", "text": "Pacific Southwest Airlines Flight 1771, a British Aerospace 146-200A, crashes near Paso Robles, California, killing all 43 on board, after a disgruntled passenger shoots his ex-boss traveling on the flight, then shoots both pilots and steers the plane into the ground.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Pacific_Southwest_Airlines_Flight_1771\" title=\"Pacific Southwest Airlines Flight 1771\">Pacific Southwest Airlines Flight 1771</a>, a <a href=\"https://wikipedia.org/wiki/British_Aerospace_146\" title=\"British Aerospace 146\">British Aerospace 146-200A</a>, crashes near <a href=\"https://wikipedia.org/wiki/Paso_Robles,_California\" title=\"Paso Robles, California\">Paso Robles, California</a>, killing all 43 on board, after a disgruntled passenger shoots his ex-boss traveling on the flight, then shoots both pilots and steers the plane into the ground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pacific_Southwest_Airlines_Flight_1771\" title=\"Pacific Southwest Airlines Flight 1771\">Pacific Southwest Airlines Flight 1771</a>, a <a href=\"https://wikipedia.org/wiki/British_Aerospace_146\" title=\"British Aerospace 146\">British Aerospace 146-200A</a>, crashes near <a href=\"https://wikipedia.org/wiki/Paso_Robles,_California\" title=\"Paso Robles, California\">Paso Robles, California</a>, killing all 43 on board, after a disgruntled passenger shoots his ex-boss traveling on the flight, then shoots both pilots and steers the plane into the ground.", "links": [{"title": "Pacific Southwest Airlines Flight 1771", "link": "https://wikipedia.org/wiki/Pacific_Southwest_Airlines_Flight_1771"}, {"title": "British Aerospace 146", "link": "https://wikipedia.org/wiki/British_Aerospace_146"}, {"title": "Paso Robles, California", "link": "https://wikipedia.org/wiki/Paso_Robles,_California"}]}, {"year": "1988", "text": "The 6.8 Ms  Armenian earthquake shakes the northern part of the country with a maximum MSK intensity of X (Devastating), killing 25,000-50,000 and injuring 31,000-130,000.", "html": "1988 - The 6.8 M<sub>s</sub>  <a href=\"https://wikipedia.org/wiki/1988_Armenian_earthquake\" title=\"1988 Armenian earthquake\">Armenian earthquake</a> shakes the northern part of the country with a maximum <a href=\"https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale\" title=\"Medvedev-Sponheuer-Karnik scale\">MSK</a> intensity of X (<i>Devastating</i>), killing 25,000-50,000 and injuring 31,000-130,000.", "no_year_html": "The 6.8 M<sub>s</sub>  <a href=\"https://wikipedia.org/wiki/1988_Armenian_earthquake\" title=\"1988 Armenian earthquake\">Armenian earthquake</a> shakes the northern part of the country with a maximum <a href=\"https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale\" title=\"Medvedev-Sponheuer-Karnik scale\">MSK</a> intensity of X (<i>Devastating</i>), killing 25,000-50,000 and injuring 31,000-130,000.", "links": [{"title": "1988 Armenian earthquake", "link": "https://wikipedia.org/wiki/1988_Armenian_earthquake"}, {"title": "<PERSON>d<PERSON><PERSON>-Sponheuer-Karnik scale", "link": "https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale"}]}, {"year": "1993", "text": "Long Island Rail Road shooting: Passenger <PERSON> murders six people and injures 19 others on the LIRR in Nassau County, New York.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/1993_Long_Island_Rail_Road_shooting\" title=\"1993 Long Island Rail Road shooting\">Long Island Rail Road shooting</a>: Passenger <PERSON> murders six people and injures 19 others on the <a href=\"https://wikipedia.org/wiki/Long_Island_Rail_Road\" title=\"Long Island Rail Road\">LIRR</a> in <a href=\"https://wikipedia.org/wiki/Nassau_County,_New_York\" title=\"Nassau County, New York\">Nassau County, New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1993_Long_Island_Rail_Road_shooting\" title=\"1993 Long Island Rail Road shooting\">Long Island Rail Road shooting</a>: Passenger <PERSON> murders six people and injures 19 others on the <a href=\"https://wikipedia.org/wiki/Long_Island_Rail_Road\" title=\"Long Island Rail Road\">LIRR</a> in <a href=\"https://wikipedia.org/wiki/Nassau_County,_New_York\" title=\"Nassau County, New York\">Nassau County, New York</a>.", "links": [{"title": "1993 Long Island Rail Road shooting", "link": "https://wikipedia.org/wiki/1993_Long_Island_Rail_Road_shooting"}, {"title": "Long Island Rail Road", "link": "https://wikipedia.org/wiki/Long_Island_Rail_Road"}, {"title": "Nassau County, New York", "link": "https://wikipedia.org/wiki/Nassau_County,_New_York"}]}, {"year": "1995", "text": "The Galileo spacecraft arrives at Jupiter, a little more than six years after it was launched by Space Shuttle Atlantis during Mission STS-34.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/Galileo_(spacecraft)\" title=\"<PERSON> (spacecraft)\"><i>Galileo</i> spacecraft</a> arrives at <a href=\"https://wikipedia.org/wiki/Jupiter\" title=\"Jupiter\">Jupiter</a>, a little more than six years after it was launched by <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> during <a href=\"https://wikipedia.org/wiki/STS-34\" title=\"STS-34\">Mission STS-34</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_(spacecraft)\" title=\"<PERSON> (spacecraft)\"><i>Galileo</i> spacecraft</a> arrives at <a href=\"https://wikipedia.org/wiki/Jupiter\" title=\"Jupiter\">Jupiter</a>, a little more than six years after it was launched by <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> during <a href=\"https://wikipedia.org/wiki/STS-34\" title=\"STS-34\">Mission STS-34</a>.", "links": [{"title": "<PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON>_(spacecraft)"}, {"title": "Jupiter", "link": "https://wikipedia.org/wiki/Jupiter"}, {"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-34", "link": "https://wikipedia.org/wiki/STS-34"}]}, {"year": "1995", "text": "Khabarovsk United Air Group Flight 3949 crashes into the Bo-Dzhausa Mountain, killing 98.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Khabarovsk_United_Air_Group_Flight_3949\" title=\"Khabarovsk United Air Group Flight 3949\">Khabarovsk United Air Group Flight 3949</a> crashes into the <a href=\"https://wikipedia.org/wiki/Bo-Dzhausa_Mountain\" title=\"Bo-Dzhausa Mountain\">Bo-Dzhausa Mountain</a>, killing 98.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Khabarovsk_United_Air_Group_Flight_3949\" title=\"Khabarovsk United Air Group Flight 3949\">Khabarovsk United Air Group Flight 3949</a> crashes into the <a href=\"https://wikipedia.org/wiki/Bo-Dzhausa_Mountain\" title=\"Bo-Dzhausa Mountain\">Bo-Dzhausa Mountain</a>, killing 98.", "links": [{"title": "Khabarovsk United Air Group Flight 3949", "link": "https://wikipedia.org/wiki/Khabarovsk_United_Air_Group_Flight_3949"}, {"title": "Bo-Dzhausa Mountain", "link": "https://wikipedia.org/wiki/Bo-<PERSON>hausa_Mountain"}]}, {"year": "1995", "text": "An Air Saint Martin (now Air Caraïbes) Beechcraft 1900 crashes near the Haitian commune of Belle Anse, killing 20.", "html": "1995 - An Air Saint Martin (now <a href=\"https://wikipedia.org/wiki/Air_Cara%C3%AFbes\" title=\"Air Caraïbes\">Air Caraïbes</a>) <a href=\"https://wikipedia.org/wiki/Beechcraft_1900\" title=\"Beechcraft 1900\">Beechcraft 1900</a> <a href=\"https://wikipedia.org/wiki/1995_Air_St._Martin_Beech_1900_crash\" class=\"mw-redirect\" title=\"1995 Air St. Martin Beech 1900 crash\">crashes</a> near the Haitian commune of <a href=\"https://wikipedia.org/wiki/Belle-<PERSON><PERSON>\" title=\"Belle-<PERSON><PERSON>\"><PERSON></a>, killing 20.", "no_year_html": "An Air Saint Martin (now <a href=\"https://wikipedia.org/wiki/Air_Cara%C3%AFbes\" title=\"Air Caraïbes\">Air Caraïbes</a>) <a href=\"https://wikipedia.org/wiki/Beechcraft_1900\" title=\"Beechcraft 1900\">Beechcraft 1900</a> <a href=\"https://wikipedia.org/wiki/1995_Air_St._Martin_Beech_1900_crash\" class=\"mw-redirect\" title=\"1995 Air St. Martin Beech 1900 crash\">crashes</a> near the Haitian commune of <a href=\"https://wikipedia.org/wiki/Belle-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON></a>, killing 20.", "links": [{"title": "Air Caraïbes", "link": "https://wikipedia.org/wiki/Air_Cara%C3%AFbes"}, {"title": "Beechcraft 1900", "link": "https://wikipedia.org/wiki/Beechcraft_1900"}, {"title": "1995 Air St. Martin Beech 1900 crash", "link": "https://wikipedia.org/wiki/1995_Air_St._<PERSON>_<PERSON>_1900_crash"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2003", "text": "The Conservative Party of Canada is officially registered, following the merger of the Canadian Alliance and the Progressive Conservative Party of Canada.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Conservative_Party_of_Canada\" title=\"Conservative Party of Canada\">Conservative Party of Canada</a> is officially registered, following the merger of the <a href=\"https://wikipedia.org/wiki/Canadian_Alliance\" title=\"Canadian Alliance\">Canadian Alliance</a> and the <a href=\"https://wikipedia.org/wiki/Progressive_Conservative_Party_of_Canada\" title=\"Progressive Conservative Party of Canada\">Progressive Conservative Party of Canada</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Conservative_Party_of_Canada\" title=\"Conservative Party of Canada\">Conservative Party of Canada</a> is officially registered, following the merger of the <a href=\"https://wikipedia.org/wiki/Canadian_Alliance\" title=\"Canadian Alliance\">Canadian Alliance</a> and the <a href=\"https://wikipedia.org/wiki/Progressive_Conservative_Party_of_Canada\" title=\"Progressive Conservative Party of Canada\">Progressive Conservative Party of Canada</a>.", "links": [{"title": "Conservative Party of Canada", "link": "https://wikipedia.org/wiki/Conservative_Party_of_Canada"}, {"title": "Canadian Alliance", "link": "https://wikipedia.org/wiki/Canadian_Alliance"}, {"title": "Progressive Conservative Party of Canada", "link": "https://wikipedia.org/wiki/Progressive_Conservative_Party_of_Canada"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, a passenger on American Airlines Flight 924 who allegedly claimed to have a bomb, is shot and killed by a team of U.S. federal air marshals at Miami International Airport.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>zar\"><PERSON><PERSON><PERSON></a>, a passenger on American Airlines Flight 924 who allegedly claimed to have a bomb, is shot and killed by a team of U.S. <a href=\"https://wikipedia.org/wiki/Federal_Air_Marshal_Service\" title=\"Federal Air Marshal Service\">federal air marshals</a> at <a href=\"https://wikipedia.org/wiki/Miami_International_Airport\" title=\"Miami International Airport\">Miami International Airport</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a passenger on American Airlines Flight 924 who allegedly claimed to have a bomb, is shot and killed by a team of U.S. <a href=\"https://wikipedia.org/wiki/Federal_Air_Marshal_Service\" title=\"Federal Air Marshal Service\">federal air marshals</a> at <a href=\"https://wikipedia.org/wiki/Miami_International_Airport\" title=\"Miami International Airport\">Miami International Airport</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Federal Air Marshal Service", "link": "https://wikipedia.org/wiki/Federal_Air_Marshal_Service"}, {"title": "Miami International Airport", "link": "https://wikipedia.org/wiki/Miami_International_Airport"}]}, {"year": "2015", "text": "The JAXA probe Akatsuki successfully enters orbit around Venus five years after the first attempt.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/JAXA\" title=\"JAXA\">JAXA</a> probe <i><a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON><PERSON><PERSON> (spacecraft)\"><PERSON><PERSON><PERSON><PERSON></a></i> successfully enters orbit around <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> five years after the first attempt.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/JAXA\" title=\"JAXA\">JAXA</a> probe <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON><PERSON><PERSON> (spacecraft)\"><PERSON><PERSON><PERSON><PERSON></a></i> successfully enters orbit around <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> five years after the first attempt.", "links": [{"title": "JAXA", "link": "https://wikipedia.org/wiki/JAXA"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(spacecraft)"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}]}, {"year": "2016", "text": "Pakistan International Airlines Flight 661, a domestic passenger flight from Chitral to Islamabad, operated by an ATR-42-500 crashes near Havelian, killing all 47 on board.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_661\" title=\"Pakistan International Airlines Flight 661\">Pakistan International Airlines Flight 661</a>, a domestic passenger flight from <a href=\"https://wikipedia.org/wiki/Chitral\" title=\"Chitral\">Chitral</a> to <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad</a>, operated by an <a href=\"https://wikipedia.org/wiki/ATR_42\" title=\"ATR 42\">ATR-42-500</a> crashes near <a href=\"https://wikipedia.org/wiki/Havelian\" title=\"Have<PERSON>\"><PERSON><PERSON></a>, killing all 47 on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_661\" title=\"Pakistan International Airlines Flight 661\">Pakistan International Airlines Flight 661</a>, a domestic passenger flight from <a href=\"https://wikipedia.org/wiki/Chitral\" title=\"Chitral\">Chitral</a> to <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad</a>, operated by an <a href=\"https://wikipedia.org/wiki/ATR_42\" title=\"ATR 42\">ATR-42-500</a> crashes near <a href=\"https://wikipedia.org/wiki/Havelian\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, killing all 47 on board.", "links": [{"title": "Pakistan International Airlines Flight 661", "link": "https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_661"}, {"title": "Chitral", "link": "https://wikipedia.org/wiki/Chitral"}, {"title": "Islamabad", "link": "https://wikipedia.org/wiki/Islamabad"}, {"title": "ATR 42", "link": "https://wikipedia.org/wiki/ATR_42"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lian"}]}, {"year": "2017", "text": "Aztec High School shooting: Former student <PERSON> opens fire on former high school, killing 2.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Aztec_High_School_shooting\" class=\"mw-redirect\" title=\"Aztec High School shooting\">Aztec High School shooting</a>: Former student <PERSON> opens fire on former high school, killing 2.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aztec_High_School_shooting\" class=\"mw-redirect\" title=\"Aztec High School shooting\">Aztec High School shooting</a>: Former student <PERSON> opens fire on former high school, killing 2.", "links": [{"title": "Aztec High School shooting", "link": "https://wikipedia.org/wiki/Aztec_High_School_shooting"}]}, {"year": "2024", "text": "Battle of Damascus (2024): Syrian opposition forces enter the Rif Dimashq Governorate, reaching within 20 km of the capital Damascus. ", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Battle_of_Damascus_(2024)\" class=\"mw-redirect\" title=\"Battle of Damascus (2024)\">Battle of Damascus (2024)</a>: Syrian opposition forces enter the <a href=\"https://wikipedia.org/wiki/Rif_Dimashq_Governorate\" title=\"Rif Dimashq Governorate\">Rif Dimashq Governorate</a>, reaching within 20 km of the capital <a href=\"https://wikipedia.org/wiki/Damascus\" title=\"Damascus\">Damascus</a>. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Damascus_(2024)\" class=\"mw-redirect\" title=\"Battle of Damascus (2024)\">Battle of Damascus (2024)</a>: Syrian opposition forces enter the <a href=\"https://wikipedia.org/wiki/Rif_Dimashq_Governorate\" title=\"Rif Dimashq Governorate\">Rif Dimashq Governorate</a>, reaching within 20 km of the capital <a href=\"https://wikipedia.org/wiki/Damascus\" title=\"Damascus\">Damascus</a>. ", "links": [{"title": "Battle of Damascus (2024)", "link": "https://wikipedia.org/wiki/Battle_of_Damascus_(2024)"}, {"title": "Rif <PERSON>", "link": "https://wikipedia.org/wiki/Rif_Dimashq_Governorate"}, {"title": "Damascus", "link": "https://wikipedia.org/wiki/Damascus"}]}], "Births": [{"year": "521", "text": "<PERSON><PERSON>, Irish missionary, monk, and saint (d. 597)", "html": "521 - <a href=\"https://wikipedia.org/wiki/Columba\" title=\"Columba\"><PERSON><PERSON></a>, Irish missionary, monk, and saint (d. 597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Columba\" title=\"Columba\"><PERSON><PERSON></a>, Irish missionary, monk, and saint (d. 597)", "links": [{"title": "Columba", "link": "https://wikipedia.org/wiki/Columba"}]}, {"year": "903", "text": "<PERSON> <PERSON><PERSON>, Persian astronomer and author (d. 986)", "html": "903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> al<PERSON>\"><PERSON></a>, Persian astronomer and author (d. 986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> al<PERSON>\"><PERSON><PERSON></a>, Persian astronomer and author (d. 986)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "967", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Persian Sufi poet (d. 1049)", "html": "967 - <a href=\"https://wikipedia.org/wiki/Ab%C5%AB-Sa%27%C4%ABd_<PERSON>-<PERSON>hayr\" class=\"mw-redirect\" title=\"Ab<PERSON>-<PERSON>'īd <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ī<PERSON></a>, Persian Sufi poet (d. 1049)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ab%C5%AB-Sa%27%C4%ABd_<PERSON><PERSON>-<PERSON>hayr\" class=\"mw-redirect\" title=\"A<PERSON><PERSON>-<PERSON>'īd <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ī<PERSON></a>, Persian Sufi poet (d. 1049)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ab%C5%AB-Sa%27%C4%ABd_Abu<PERSON>-Khayr"}]}, {"year": "1302", "text": "<PERSON><PERSON><PERSON>, Italian nobleman (d. 1339)", "html": "1302 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian nobleman (d. 1339)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian nobleman (d. 1339)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1532", "text": "<PERSON>, German nobleman and politician (d. 1605)", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Sayn-Wittgenstein\" title=\"<PERSON>, Count of Sayn-Wittgenstein\"><PERSON></a>, German nobleman and politician (d. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Sayn-Wittgenstein\" title=\"<PERSON>, Count of Sayn-Wittgenstein\"><PERSON></a>, German nobleman and politician (d. 1605)", "links": [{"title": "<PERSON>, Count of Sayn-Wittgenstein", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1545", "text": "<PERSON>, English-Scottish husband of <PERSON>, Queen of Scots (d. 1567)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_Dar<PERSON>ley\" title=\"<PERSON>, Lord Darnley\"><PERSON></a>, English-Scottish husband of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a> (d. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord Darnley\"><PERSON></a>, English-Scottish husband of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a> (d. 1567)", "links": [{"title": "<PERSON>, Lord Dar<PERSON>ley", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>"}, {"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}]}, {"year": "1561", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (d. 1625)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/Kikkawa_Hiroie\" title=\"Kikka<PERSON> Hiroie\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kikka<PERSON>_<PERSON>roi<PERSON>\" title=\"Kikka<PERSON> Hiroie\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1625)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kikka<PERSON>_<PERSON>e"}]}, {"year": "1595", "text": "<PERSON><PERSON> of Joseon, Korean king (d. 1649)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/Injo_of_Joseon\" title=\"Injo of Joseon\"><PERSON><PERSON> of Joseon</a>, Korean king (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Injo_of_Joseon\" title=\"Injo of Joseon\"><PERSON><PERSON> of Joseon</a>, Korean king (d. 1649)", "links": [{"title": "<PERSON><PERSON> of Joseon", "link": "https://wikipedia.org/wiki/Injo_of_Joseon"}]}, {"year": "1598", "text": "<PERSON><PERSON>, Italian sculptor and painter (d. 1680)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian sculptor and painter (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian sculptor and painter (d. 1680)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON>, Italian architect and engraver (d. 1678)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and engraver (d. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and engraver (d. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1637", "text": "<PERSON>, Italian organist and composer (d. 1710)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1756", "text": "<PERSON>, American sheriff and Methodist preacher (d. 1836)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(preacher)\" title=\"<PERSON> (preacher)\"><PERSON></a>, American sheriff and Methodist preacher (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(preacher)\" title=\"<PERSON> (preacher)\"><PERSON></a>, American sheriff and Methodist preacher (d. 1836)", "links": [{"title": "<PERSON> (preacher)", "link": "https://wikipedia.org/wiki/<PERSON>_(preacher)"}]}, {"year": "1764", "text": "<PERSON>, French general and politician (d. 1841)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON>\"><PERSON></a>, French general and politician (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON>\"><PERSON></a>, French general and politician (d. 1841)", "links": [{"title": "<PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1784", "text": "<PERSON>, Scottish author and poet (d. 1842)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Scottish author and poet (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Scottish author and poet (d. 1842)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1791", "text": "<PERSON><PERSON><PERSON>, Hungarian-Slovene priest and poet (d. 1836)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/Ferenc_Nov%C3%<PERSON><PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON></a>, Hungarian-Slovene priest and poet (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferenc_Nov%C3%<PERSON><PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON></a>, Hungarian-Slovene priest and poet (d. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/Ferenc_Nov%C3%A1k_(writer)"}]}, {"year": "1792", "text": "<PERSON>, Dutch author and academic (d. 1857)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and academic (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and academic (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>a"}]}, {"year": "1801", "text": "<PERSON>, Austrian actor and playwright (d. 1862)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor and playwright (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor and playwright (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, Hungarian-Austrian anatomist and biologist (d. 1894)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Austrian anatomist and biologist (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Austrian anatomist and biologist (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, German physiologist and biologist (d. 1882)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and biologist (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and biologist (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, Polish-German mathematician and academic (d. 1891)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German mathematician and academic (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German mathematician and academic (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, Australian businessman and politician, 22nd Premier of Victoria (d. 1909)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1860", "text": "<PERSON>, English-born Australian politician, 6th Prime Minister of Australia (d. 1947)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1861", "text": "<PERSON>, French general during World War I (d. 1931)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general during World War I (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general during World War I (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, French author (d. 1920)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(French_novelist)\" title=\"<PERSON> (French novelist)\"><PERSON></a>, French author (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(French_novelist)\" title=\"<PERSON> (French novelist)\"><PERSON></a>, French author (d. 1920)", "links": [{"title": "<PERSON> (French novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(French_novelist)"}]}, {"year": "1863", "text": "<PERSON>, Swiss soldier and politician, 36th President of the Swiss Confederation (d. 1952)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1863", "text": "<PERSON>, Italian composer and conductor (d. 1945)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and conductor (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and conductor (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American businessman, co-founded Sears (d. 1914)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sears\" title=\"Sears\">Sears</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sears\" title=\"<PERSON>\">Sears</a> (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sears", "link": "https://wikipedia.org/wiki/Sears"}]}, {"year": "1869", "text": "<PERSON>, Australian cricketer (d. 1919)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON>, American novelist, short story writer, and poet (d. 1947)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist, short story writer, and poet (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist, short story writer, and poet (d. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>,  Japanese author, poet, pioneering feminist, pacifist, and social reformer (d. 1942)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author, poet, pioneering feminist, pacifist, and social reformer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author, poet, pioneering feminist, pacifist, and social reformer (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1879", "text": "<PERSON>, Czech-American pianist, composer, and academic (d. 1972)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American pianist, composer, and academic (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American pianist, composer, and academic (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American sprinter (d. 1933)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter (d. 1933)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1885", "text": "<PERSON>, American golfer (d. 1945)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American boxer and painter (d. 1919)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and painter (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and painter (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Austrian-American composer and songwriter (d. 1964)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American composer and songwriter (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American composer and songwriter (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Irish novelist (d. 1957)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American captain and politician (d. 1991)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Hamilton_Fish_III\" title=\"Hamilton Fish III\"><PERSON></a>, American captain and politician (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hamilton_Fish_III\" title=\"Hamilton Fish III\"><PERSON></a>, American captain and politician (d. 1991)", "links": [{"title": "Hamilton Fish III", "link": "https://wikipedia.org/wiki/Hamilton_Fish_III"}]}, {"year": "1892", "text": "<PERSON>, American painter and academic (d. 1964)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter and academic (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter and academic (d. 1964)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1893", "text": "<PERSON>, American actress (d. 1968)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, German general (d. 1982)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English author and illustrator (d. 1986)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Ukrainian folk artist (d. 1961)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Vasylivna_Bilokur\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Vasyl<PERSON>na Bilokur\"><PERSON><PERSON><PERSON></a>, Ukrainian folk artist (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>asylivna_Bilokur\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Vasylivna Bilokur\"><PERSON><PERSON><PERSON></a>, Ukrainian folk artist (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Bilokur"}]}, {"year": "1902", "text": "<PERSON>, Estonian architect, author, and educator (d. 1967)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect, author, and educator (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect, author, and educator (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Croatian mathematician, physicist, and academic (d. 1987)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lanu%C5%A1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian mathematician, physicist, and academic (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u%C5%A1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian mathematician, physicist, and academic (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Danilo_Blanu%C5%A1a"}]}, {"year": "1904", "text": "<PERSON>, American voice actor and singer (d. 1985)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and singer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and singer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Dutch-American astronomer and academic (d. 1973)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American astronomer and academic (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American astronomer and academic (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, German translator (d. 2005)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German translator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German translator (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Polish-Canadian politician and spy (d. 1983)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Polish-Canadian politician and spy (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Polish-Canadian politician and spy (d. 1983)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1909", "text": "<PERSON>, Bulgarian poet and author (d. 1942)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian poet and author (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian poet and author (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Canadian high jumper and geologist (d. 1998)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian high jumper and geologist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian high jumper and geologist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American singer-songwriter, trumpet player, and actor (d. 1978)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, trumpet player, and actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis Prima\"><PERSON></a>, American singer-songwriter, trumpet player, and actor (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Welsh captain and composer (d. 1993)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Welsh captain and composer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Welsh captain and composer (d. 1993)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Estonian author and poet (d. 1986)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian author and poet (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian author and poet (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1915", "text": "<PERSON>, American author and screenwriter (d. 1978)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actor (d. 2014)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, South African poet and author (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Tatamkhulu_Afrika\" title=\"Tatamkhulu Afrika\">Tatamkh<PERSON> Afrika</a>, South African poet and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tatamkhulu_Afrika\" title=\"Tatamkhulu Afrika\">Tatamkhulu Afrika</a>, South African poet and author (d. 2002)", "links": [{"title": "Tatamkhulu Afrika", "link": "https://wikipedia.org/wiki/Tatamkhulu_Afrika"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian cyclist (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cyclist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cyclist (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Austrian-German soldier and pilot (d. 1944)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German soldier and pilot (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German soldier and pilot (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian guru and scholar (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Swami_<PERSON>j\" title=\"<PERSON><PERSON><PERSON><PERSON> Maharaj\"><PERSON><PERSON><PERSON><PERSON></a>, Indian guru and scholar (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Swami_<PERSON>j\" title=\"<PERSON><PERSON><PERSON><PERSON> Swami Maharaj\"><PERSON><PERSON><PERSON><PERSON></a>, Indian guru and scholar (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>j"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Indian-Pakistani author and scholar (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani author and scholar (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani author and scholar (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actor and comedian (d. 1986)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Danish pianist and composer (d. 2020)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ab<PERSON>\" title=\"<PERSON><PERSON>ab<PERSON>\"><PERSON><PERSON></a>, Danish pianist and composer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ab<PERSON>\" title=\"<PERSON><PERSON>ab<PERSON>\"><PERSON><PERSON></a>, Danish pianist and composer (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>t_Fabric"}]}, {"year": "1924", "text": "<PERSON>, Zimbabwean race car driver (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Zimbabwean race car driver (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Zimbabwean race car driver (d. 2005)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1924", "text": "<PERSON>, American mathematician (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Portuguese historian, lawyer, and politician, 17th President of Portugal (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/M%C3%A1rio_Soares\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese historian, lawyer, and politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese historian, lawyer, and politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1rio_Soares"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1925", "text": "<PERSON><PERSON>, French-Brazilian race car driver", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Brazilian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American basketball player and coach (d. 1985)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American bishop (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American businessman and philanthropist (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Welsh opera singer (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh opera singer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh opera singer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American linguist and philosopher", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American linguist and philosopher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American linguist and philosopher", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American race car driver (d. 1988)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Guyanese-English author (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-English author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-English author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American baseball player (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)\" class=\"mw-redirect\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(infielder)\" class=\"mw-redirect\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player (d. 2020)", "links": [{"title": "<PERSON> (infielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)"}]}, {"year": "1931", "text": "<PERSON>, American game designer, created <PERSON><PERSON> (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, created <i><a href=\"https://wikipedia.org/wiki/Diplomacy_(game)\" title=\"Diplomacy (game)\">Diplomacy</a></i> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, created <i><a href=\"https://wikipedia.org/wiki/Diplomacy_(game)\" title=\"Diplomacy (game)\">Diplomacy</a></i> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Diplomacy (game)", "link": "https://wikipedia.org/wiki/Diplomacy_(game)"}]}, {"year": "1931", "text": "<PERSON>, American bluegrass singer and musician (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bluegrass singer and musician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bluegrass singer and musician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actress", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and politician", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Oktay_Ek%C5%9Fi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oktay_Ek%C5%9Fi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and politician", "links": [{"title": "Ok<PERSON>y <PERSON>", "link": "https://wikipedia.org/wiki/Oktay_Ek%C5%9Fi"}]}, {"year": "1932", "text": "<PERSON>, American journalist and author (d. 2019)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indonesian economist and politician, 17th Indonesian Minister of Finance (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indonesian economist and politician, 17th <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Finance_of_Indonesia\" class=\"mw-redirect\" title=\"List of Ministers of Finance of Indonesia\">Indonesian Minister of Finance</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indonesian economist and politician, 17th <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Finance_of_Indonesia\" class=\"mw-redirect\" title=\"List of Ministers of Finance of Indonesia\">Indonesian Minister of Finance</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "List of Ministers of Finance of Indonesia", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Finance_of_Indonesia"}]}, {"year": "1932", "text": "<PERSON>, Australian rugby league player (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Croatian director and screenwriter (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian director and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian director and screenwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Krsto_Papi%C4%87"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Mexican musician, singer and composer (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican musician, singer and composer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican musician, singer and composer (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English comedian", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American lawyer and politician (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Thad_<PERSON>\" title=\"Thad <PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON>\" title=\"Thad <PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thad_<PERSON><PERSON>ran"}]}, {"year": "1940", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American journalist and activist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Be<PERSON>\"><PERSON><PERSON></a>, American journalist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Be<PERSON>\"><PERSON><PERSON></a>, American journalist and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1981)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American baseball player (d. 2015)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American businessman (d. 1993)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American game show host and producer (d. 2006)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and producer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and producer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American author and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/J%C3%B3hann_%C3%81rs%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3hann_%C3%81rs%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3hann_%C3%81rs%C3%A6lsson"}]}, {"year": "1943", "text": "<PERSON>, American mathematician and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American police officer and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernard_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American businessman and pilot", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and pilot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and pilot", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American organist and composer (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Czech dentist and politician (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech dentist and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech dentist and politician (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Macek"}]}, {"year": "1947", "text": "<PERSON>, American baseball player and sportscaster", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor, producer, and director", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American country singer-songwriter and actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American screenwriter and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "1949", "text": "<PERSON>, Italian sculptor and jeweler", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and jeweler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>i%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and jeweler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/James_Rivi%C3%A8re"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter, guitarist, and actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Waits\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Waits\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian singer-songwriter and guitarist (d. 2015)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A4rzke\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A4rzke\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eckhard_M%C3%A4rzke"}]}, {"year": "1954", "text": "<PERSON>, American businesswoman and politician, 27th Governor of Oklahoma", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Oklahoma\" title=\"Governor of Oklahoma\">Governor of Oklahoma</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Oklahoma\" title=\"Governor of Oklahoma\">Governor of Oklahoma</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Oklahoma", "link": "https://wikipedia.org/wiki/Governor_of_Oklahoma"}]}, {"year": "1955", "text": "<PERSON>, Australian educator and politician, 14th Deputy Premier of New South Wales", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian educator and politician, 14th <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_New_South_Wales\" title=\"Deputy Premier of New South Wales\">Deputy Premier of New South Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian educator and politician, 14th <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_New_South_Wales\" title=\"Deputy Premier of New South Wales\">Deputy Premier of New South Wales</a>", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Deputy Premier of New South Wales", "link": "https://wikipedia.org/wiki/Deputy_Premier_of_New_South_Wales"}]}, {"year": "1956", "text": "<PERSON>, American basketball player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Mexican-American comedian and actor (d. 2019)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American comedian and actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American comedian and actor (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, British politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian cricketer, coach, and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer, coach, and sportscaster", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Nigerian career-diplomat, President of the United Nations General Assembly (2019)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Ti<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ti<PERSON><PERSON><PERSON>\">Ti<PERSON><PERSON><PERSON></a>, Nigerian career-diplomat, <a href=\"https://wikipedia.org/wiki/President_of_the_United_Nations_General_Assembly\" title=\"President of the United Nations General Assembly\">President of the United Nations General Assembly</a> (2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tij<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"Ti<PERSON><PERSON><PERSON>\">Ti<PERSON><PERSON><PERSON></a>, Nigerian career-diplomat, <a href=\"https://wikipedia.org/wiki/President_of_the_United_Nations_General_Assembly\" title=\"President of the United Nations General Assembly\">President of the United Nations General Assembly</a> (2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ti<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "President of the United Nations General Assembly", "link": "https://wikipedia.org/wiki/President_of_the_United_Nations_General_Assembly"}]}, {"year": "1957", "text": "<PERSON>, English lawyer and civil servant", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and civil servant", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and civil servant", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English bass player and songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American wrestler and sportscaster (d. 1999)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and sportscaster (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and sportscaster (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Yousuf\" title=\"<PERSON>em Yousuf\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>suf\" title=\"<PERSON>em Yousuf\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saleem_Yousuf"}]}, {"year": "1960", "text": "<PERSON>, English guitarist and songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, French decathlete", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Northern Irish politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Lebanese activist (d. 2008)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese activist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese activist (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Dutch footballer and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Japanese illustrator", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American R&B/soul singer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/soul singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/soul singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English filmmaker", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blick\"><PERSON></a>, English filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hugo Blick\"><PERSON></a>, English filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American ice hockey player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian rower", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Scottish footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American actor, director, producer, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Japanese motorcycle racer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese motorcycle racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Japanese actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>oh"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Canadian-Estonian historian and academic", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-Estonian historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-Estonian historian and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American baseball player, coach, and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Azerbaijani-Armenian chess player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Azerbaijani-Armenian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Azerbaijani-Armenian chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Austrian skier", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American wrestler and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Turkish basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/%C4%B0<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%B0<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American wrestler and trainer (d. 2015)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and trainer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and trainer (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American football player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, French rugby player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Fabien_Pelous\" title=\"Fabien Pelous\"><PERSON><PERSON><PERSON></a>, French rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabien_Pelous\" title=\"Fabien Pelous\"><PERSON><PERSON><PERSON></a>, French rugby player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>abi<PERSON>_<PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Irish singer-songwriter, musician and record producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, musician and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, musician and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian singer and actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Spanish shot putter and actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Manuel_<PERSON>%C3%ADnez_Guti%C3%A9rrez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish shot putter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manuel_<PERSON>%C3%ADnez_Guti%C3%A9rrez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish shot putter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manuel_Mart%C3%ADnez_Guti%C3%A9rrez"}]}, {"year": "1975", "text": "<PERSON>, English footballer and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Italian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player and politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Filipino-British actor, model and television personality", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-British actor, model and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-British actor, model and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, French race car driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_Tr%C3%A9luyer\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_Tr%C3%A9luyer\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beno%C3%AEt_Tr%C3%A9luyer"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Belgian politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1977", "text": "<PERSON>, American baseball player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English golfer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English drummer and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American actress, director, and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, English historian, academic and television presenter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English historian, academic and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English historian, academic and television presenter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter, pianist, and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sara_<PERSON>les"}]}, {"year": "1979", "text": "<PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Greek-Italian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Japanese actress and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American poker player and internet celebrity", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player and <a href=\"https://wikipedia.org/wiki/Internet_celebrity\" title=\"Internet celebrity\">internet celebrity</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player and <a href=\"https://wikipedia.org/wiki/Internet_celebrity\" title=\"Internet celebrity\">internet celebrity</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dan_<PERSON>ian"}, {"title": "Internet celebrity", "link": "https://wikipedia.org/wiki/Internet_celebrity"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American mixed martial artist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Polish race car driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Czech ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Milan_Mich%C3%A1lek\" title=\"Milan Michálek\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Mich%C3%A1lek\" title=\"Milan Michálek\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON> Mi<PERSON>á<PERSON>k", "link": "https://wikipedia.org/wiki/Milan_Mich%C3%A1lek"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American golfer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American guitarist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter, rapper, dancer, and actor (d. 2022)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, rapper, dancer, and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, rapper, dancer, and actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American swimmer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Russian tennis player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Danish ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Italian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1989", "text": "<PERSON>, French basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9raphin\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9raphin\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kevin_S%C3%A9raphin"}]}, {"year": "1990", "text": "<PERSON>, Australian basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Belgian tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Russian long jumper", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Cuban baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Yasie<PERSON>_<PERSON>uig\" title=\"Yasie<PERSON>uig\"><PERSON><PERSON><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yasie<PERSON>_<PERSON>uig\" title=\"Yasie<PERSON>uig\"><PERSON><PERSON><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>g"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Urszula_Radwa%C5%84ska\" title=\"<PERSON><PERSON><PERSON><PERSON> Radwańska\"><PERSON><PERSON><PERSON><PERSON></a>, Polish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Urs<PERSON>la_Radwa%C5%84ska\" title=\"<PERSON><PERSON><PERSON><PERSON> Radwańska\"><PERSON><PERSON><PERSON><PERSON></a>, Polish tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Radwańska", "link": "https://wikipedia.org/wiki/Urszula_Radwa%C5%84ska"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Italian race car driver", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, New Zealand footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)\" title=\"<PERSON> (footballer, born 1991)\"><PERSON></a>, New Zealand footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1991)\" title=\"<PERSON> (footballer, born 1991)\"><PERSON></a>, New Zealand footballer", "links": [{"title": "<PERSON> (footballer, born 1991)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1991)"}]}, {"year": "1992", "text": "<PERSON>, American-Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Nigerian actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linebacker)\" title=\"<PERSON> (linebacker)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linebacker)\" title=\"<PERSON> (linebacker)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (linebacker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linebacker)"}]}, {"year": "1994", "text": "<PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American politician", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Japanese figure skater", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yu"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Henry\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Scottish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1998", "text": "<PERSON>, Canadian pianist", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Pavol_Regenda\" title=\"Pavol Regenda\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pavol_Regenda\" title=\"Pavol Regenda\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "Pavol Regenda", "link": "https://wikipedia.org/wiki/Pavol_Regenda"}]}, {"year": "2000", "text": "<PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American swimmer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "43 BC", "text": "<PERSON>, Roman philosopher, lawyer, and politician (b. 106 BC)", "html": "43 BC - 43 BC - <a href=\"https://wikipedia.org/wiki/Cicero\" title=\"Cicero\">Cicero</a>, Roman philosopher, lawyer, and politician (b. 106 BC)", "no_year_html": "43 BC - <a href=\"https://wikipedia.org/wiki/Cicero\" title=\"Cicero\">Cicero</a>, Roman philosopher, lawyer, and politician (b. 106 BC)", "links": [{"title": "Cicero", "link": "https://wikipedia.org/wiki/Cicero"}]}, {"year": "283", "text": "<PERSON><PERSON><PERSON><PERSON>, pope of the Catholic Church", "html": "283 - <a href=\"https://wikipedia.org/wiki/Pope_Eutychian\" title=\"Pope Eutychian\"><PERSON><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_E<PERSON>ychian\" title=\"Pope Eutychian\"><PERSON><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Eutychian"}]}, {"year": "881", "text": "<PERSON><PERSON><PERSON>, archbishop of Milan", "html": "881 - <a href=\"https://wikipedia.org/wiki/Anspert\" title=\"Ansper<PERSON>\"><PERSON><PERSON><PERSON></a>, archbishop of Milan", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ansper<PERSON>\" title=\"An<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, archbishop of Milan", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anspert"}]}, {"year": "983", "text": "<PERSON>, Holy Roman Emperor (b. 955)", "html": "983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (b. 955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (b. 955)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1254", "text": "<PERSON>, pope of the Catholic Church (b. 1195)", "html": "1254 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_IV\" title=\"Pope Innocent IV\"><PERSON> IV</a>, pope of the Catholic Church (b. 1195)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Innocent_IV\" title=\"Pope Innocent IV\"><PERSON> IV</a>, pope of the Catholic Church (b. 1195)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_IV"}]}, {"year": "1279", "text": "<PERSON><PERSON><PERSON>, High Duke of Poland (b. 1226)", "html": "1279 - <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_V_the_Chaste\" title=\"<PERSON><PERSON><PERSON> V the Chaste\"><PERSON><PERSON><PERSON></a>, High Duke of Poland (b. 1226)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_V_the_Chaste\" title=\"<PERSON><PERSON><PERSON> V the Chaste\"><PERSON><PERSON><PERSON></a>, High Duke of Poland (b. 1226)", "links": [{"title": "<PERSON><PERSON><PERSON> V the Chaste", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_V_the_Chaste"}]}, {"year": "1295", "text": "<PERSON>, 7th Earl of Gloucester, English officer (b. 1243)", "html": "1295 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_7th_Earl_of_Gloucester\" title=\"<PERSON>, 7th Earl of Gloucester\"><PERSON>, 7th Earl of Gloucester</a>, English officer (b. 1243)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_7th_Earl_of_Gloucester\" title=\"<PERSON>, 7th Earl of Gloucester\"><PERSON>, 7th Earl of Gloucester</a>, English officer (b. 1243)", "links": [{"title": "<PERSON>, 7th Earl of Gloucester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_7th_Earl_of_Gloucester"}]}, {"year": "1312", "text": "<PERSON> of Antioch, Syriac Orthodox patriarch of Antioch (r. 1292-1312)", "html": "1312 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Antioch\" title=\"<PERSON> of Antioch\"><PERSON> of Antioch</a>, Syriac Orthodox patriarch of Antioch (<span style=\"white-space:nowrap;\"><abbr title=\"reigned\">r.</abbr> 1292-1312</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Antioch\" title=\"<PERSON> of Antioch\"><PERSON> of Antioch</a>, Syriac Orthodox patriarch of Antioch (<span style=\"white-space:nowrap;\"><abbr title=\"reigned\">r.</abbr> 1292-1312</span>)", "links": [{"title": "<PERSON> of Antioch", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Antioch"}]}, {"year": "1383", "text": "<PERSON><PERSON><PERSON>, duke of Luxembourg (b. 1337)", "html": "1383 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Luxembourg\" title=\"<PERSON><PERSON><PERSON>, Duke of Luxembourg\"><PERSON><PERSON><PERSON></a>, duke of Luxembourg (b. 1337)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Luxembourg\" title=\"<PERSON><PERSON><PERSON>, Duke of Luxembourg\"><PERSON><PERSON><PERSON></a>, duke of Luxembourg (b. 1337)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Luxembourg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Luxembourg"}]}, {"year": "1498", "text": "<PERSON>, German poet (b. 1433)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet (b. 1433)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet (b. 1433)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1562", "text": "<PERSON>, Dutch-Italian composer and educator (b. 1490)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Italian composer and educator (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Italian composer and educator (b. 1490)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON>, French missionary and saint (b. 1606)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, French missionary and saint (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, French missionary and saint (b. 1606)", "links": [{"title": "<PERSON> (missionary)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(missionary)"}]}, {"year": "1672", "text": "<PERSON>, English-American lawyer and politician, 8th Governor of the Massachusetts Bay Colony (b. 1592)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts#Massachusetts_Bay_Colony:_1629-86,_1689-92\" title=\"List of colonial governors of Massachusetts\">Governor of the Massachusetts Bay Colony</a> (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts#Massachusetts_Bay_Colony:_1629-86,_1689-92\" title=\"List of colonial governors of Massachusetts\">Governor of the Massachusetts Bay Colony</a> (b. 1592)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Massachusetts", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts#Massachusetts_Bay_Colony:_1629-86,_1689-92"}]}, {"year": "1680", "text": "<PERSON>, Dutch-English painter (b. 1618)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-English painter (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-English painter (b. 1618)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON><PERSON><PERSON>, English philosopher and politician, Lord Warden of the Cinque Ports (b. 1623)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English philosopher and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English philosopher and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1623)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1723", "text": "<PERSON>, Czech architect, designed the Pilgrimage Church of Saint John of Nepomuk and <PERSON><PERSON> (b. 1677)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech architect, designed the <a href=\"https://wikipedia.org/wiki/Pilgrimage_Church_of_Saint_John_of_Nepomuk\" title=\"Pilgrimage Church of Saint John of Nepomuk\">Pilgrimage Church of Saint John of Nepomuk</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Koruna_Chateau\" title=\"<PERSON><PERSON> Koruna Chateau\"><PERSON><PERSON></a> (b. 1677)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech architect, designed the <a href=\"https://wikipedia.org/wiki/Pilgrimage_Church_of_Saint_John_of_Nepomuk\" title=\"Pilgrimage Church of Saint John of Nepomuk\">Pilgrimage Church of Saint John of Nepomuk</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Korun<PERSON>_Chateau\" title=\"Karlova Koruna Chateau\"><PERSON><PERSON></a> (b. 1677)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Pilgrimage Church of Saint John of Nepomuk", "link": "https://wikipedia.org/wiki/Pilgrimage_Church_of_Saint_<PERSON>_of_Nepomuk"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1725", "text": "Flore<PERSON>, French actor and playwright (b. 1661)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/Florent_Carton_Dancourt\" class=\"mw-redirect\" title=\"Florent Carton Dancourt\">Florent <PERSON><PERSON></a>, French actor and playwright (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florent_Carton_Dancourt\" class=\"mw-redirect\" title=\"Florent Carton Dancourt\">Florent <PERSON></a>, French actor and playwright (b. 1661)", "links": [{"title": "Florent <PERSON>", "link": "https://wikipedia.org/wiki/Florent_Car<PERSON>_Dancourt"}]}, {"year": "1772", "text": "<PERSON>, Spanish monk, scholar, and author (b. 1695)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Sarmiento\" title=\"<PERSON>\"><PERSON></a>, Spanish monk, scholar, and author (b. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Sarmiento\" title=\"<PERSON>\"><PERSON></a>, Spanish monk, scholar, and author (b. 1695)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Sarmiento"}]}, {"year": "1775", "text": "<PERSON>, English admiral and politician (b. 1715)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral and politician (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral and politician (b. 1715)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1793", "text": "<PERSON>, French soldier and drummer (b. 1779)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and drummer (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and drummer (b. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish admiral and politician (b. 1757)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/K%C3%BC%C3%A7%C3%BCk_H%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish admiral and politician (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%BC%C3%A7%C3%BCk_H%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish admiral and politician (b. 1757)", "links": [{"title": "Küçük <PERSON>", "link": "https://wikipedia.org/wiki/K%C3%BC%C3%A7%C3%BCk_H%C3%BCsey<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, German-French general (b. 1769)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French general (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French general (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, English admiral and politician, 4th Governor of New South Wales (b. 1745)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor_of_New_South_Wales\" title=\"Governor of New South Wales\">Governor of New South Wales</a> (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor_of_New_South_Wales\" title=\"Governor of New South Wales\">Governor of New South Wales</a> (b. 1745)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New South Wales", "link": "https://wikipedia.org/wiki/Governor_of_New_South_Wales"}]}, {"year": "1837", "text": "<PERSON>, Scottish poet (b. 1814)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, Scottish philosopher and author (b. 1789)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Scottish philosopher and author (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Scottish philosopher and author (b. 1789)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1874", "text": "Con<PERSON><PERSON> <PERSON>, German theologian, scholar, and academic (b. 1815)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_von_Tischendorf\" title=\"<PERSON><PERSON><PERSON> von Tischendorf\">Con<PERSON><PERSON> von <PERSON></a>, German theologian, scholar, and academic (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_von_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von Tischendorf\">Con<PERSON><PERSON> <PERSON></a>, German theologian, scholar, and academic (b. 1815)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantin_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Icelandic scholar and politician, 1st Speaker of the Parliament of Iceland (b. 1811)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/J%C3%B3n_Sigur%C3%B<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic scholar and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Speakers_of_the_Parliament_of_Iceland\" class=\"mw-redirect\" title=\"List of Speakers of the Parliament of Iceland\">Speaker of the Parliament of Iceland</a> (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3n_Sigur%C3%B<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic scholar and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Speakers_of_the_Parliament_of_Iceland\" class=\"mw-redirect\" title=\"List of Speakers of the Parliament of Iceland\">Speaker of the Parliament of Iceland</a> (b. 1811)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3n_Sigur%C3%B0sson"}, {"title": "List of Speakers of the Parliament of Iceland", "link": "https://wikipedia.org/wiki/List_of_Speakers_of_the_Parliament_of_Iceland"}]}, {"year": "1891", "text": "<PERSON>, English-Australian politician, 9th Premier of South Australia (b. 1823)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1894", "text": "<PERSON>, French businessman and diplomat, co-developed the Suez Canal (b. 1805)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and diplomat, co-developed the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and diplomat, co-developed the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}]}, {"year": "1899", "text": "<PERSON>, Filipino painter and sculptor (b. 1857)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Juan Luna\"><PERSON></a>, Filipino painter and sculptor (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Juan Luna\"><PERSON></a>, Filipino painter and sculptor (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, German-American cartoonist (b. 1840)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American cartoonist (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American cartoonist (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Swiss journalist and educator, Nobel Prize laureate (b. 1833)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/%C3%89lie_Ducommun\" title=\"<PERSON><PERSON> Ducommu<PERSON>\"><PERSON><PERSON></a>, Swiss journalist and educator, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lie_Ducommun\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss journalist and educator, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1833)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lie_Ducommun"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1913", "text": "<PERSON>, Italian cardinal (b. 1828)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Luigi <PERSON> Stefano\"><PERSON> Stefano</a>, Italian cardinal (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Santo_Stefano"}]}, {"year": "1917", "text": "<PERSON>, Austrian violinist and composer (b. 1826)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English-Australian politician, 9th Premier of Western Australia (b. 1859)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1859)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1941", "text": "Attack on Pearl Harbor:\n<PERSON><PERSON><PERSON>, American captain (b. 1887)\n<PERSON>, American sailor (b. 1915)\n<PERSON>, American sailor (b. 1921)\n<PERSON>, American sailor (b. 1920)\n<PERSON>, American sailor (b. 1894)\n<PERSON>, American sailor (b. 1906)\n<PERSON>, American sailor (b. 1918)\n<PERSON>, American admiral (b. 1884)\n<PERSON>, American sailor (b. 1916)\n<PERSON>, American sailor (b. 1917)\n<PERSON>, American sailor (b. 1895)\n<PERSON><PERSON><PERSON>, American priest and sailor (b. 1909)\n<PERSON>, American sailor (b. 1915)\n<PERSON>, American sailor (b. 1893)\n<PERSON>, American sailor (b. 1919)\n<PERSON>, American captain (b. 1888)\n<PERSON><PERSON>, American sailor (b. 1917)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Attack_on_Pearl_Harbor\" title=\"Attack on Pearl Harbor\">Attack on Pearl Harbor</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American captain (b. 1887)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>#Namesake\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1915)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1921)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/John_C._England\" title=\"John C. England\">John <PERSON></a>, American sailor (b. 1920)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1894)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>llis\" class=\"mw-redirect\" title=\"Ralph Hollis\">Ralph Hollis</a>, American sailor (b. 1906)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Herbert_C._Jones\" title=\"Herbert C. Jones\">Herbert C. Jones</a>, American sailor (b. 1918)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Isaac_C._Kidd\" title=\"Isaac C. Kidd\">Isaac C. Kidd</a>, American admiral (b. 1884)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Robert_Lawrence_Leopold\" class=\"mw-redirect\" title=\"Robert Lawrence Leopold\">Robert Lawrence Leopold</a>, American sailor (b. 1916)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Herbert_Hugo_Menges\" class=\"mw-redirect\" title=\"Herbert Hugo Menges\">Herbert Hugo Menges</a>, American sailor (b. 1917)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Thomas_James_Reeves\" title=\"Thomas James Reeves\">Thomas James Reeves</a>, American sailor (b. 1895)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Aloysius_Schmitt\" title=\"Aloysius Schmitt\">Aloysius Schmitt</a>, American priest and sailor (b. 1909)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Robert_R._Scott\" title=\"Robert R. Scott\">Robert R. Scott</a>, American sailor (b. 1915)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Peter_Tomich\" title=\"Peter Tomich\">Peter Tomich</a>, American sailor (b. 1893)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Robert_W._Uhlmann\" class=\"mw-redirect\" title=\"Robert W. Uhlmann\">Robert Uhlmann</a>, American sailor (b. 1919)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Franklin_Van_Valkenburgh\" title=\"Franklin Van Valkenburgh\">Franklin Van Valkenburgh</a>, American captain (b. 1888)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Eldon_P._Wyman\" class=\"mw-redirect\" title=\"Eldon P. Wyman\">Eldon P. Wyman</a>, American sailor (b. 1917)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Attack_on_Pearl_Harbor\" title=\"Attack on Pearl Harbor\">Attack on Pearl Harbor</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American captain (b. 1887)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>#Namesake\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1915)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1921)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/John_C._England\" title=\"John C. England\"><PERSON></a>, American sailor (b. 1920)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1894)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Hollis\">Ralph Hollis</a>, American sailor (b. 1906)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Herbert_C._Jones\" title=\"Herbert C. Jones\">Herbert C. Jones</a>, American sailor (b. 1918)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Isaac_C._Kidd\" title=\"Isaac C. Kidd\">Isaac C. Kidd</a>, American admiral (b. 1884)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Robert_Lawrence_Leopold\" class=\"mw-redirect\" title=\"Robert Lawrence Leopold\">Robert Lawrence Leopold</a>, American sailor (b. 1916)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Herbert_Hugo_Menges\" class=\"mw-redirect\" title=\"Herbert Hugo Menges\">Herbert Hugo Menges</a>, American sailor (b. 1917)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Thomas_James_Reeves\" title=\"Thomas James Reeves\">Thomas James Reeves</a>, American sailor (b. 1895)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Aloysius_Schmitt\" title=\"Aloysius Schmitt\">Aloysius Schmitt</a>, American priest and sailor (b. 1909)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Robert_R._Scott\" title=\"Robert R. Scott\">Robert R. Scott</a>, American sailor (b. 1915)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Peter_Tomich\" title=\"Peter Tomich\">Peter Tomich</a>, American sailor (b. 1893)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Robert_W._Uhlmann\" class=\"mw-redirect\" title=\"Robert W. Uhlmann\">Robert Uhlmann</a>, American sailor (b. 1919)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Franklin_Van_Valkenburgh\" title=\"Franklin Van Valkenburgh\">Franklin Van Valkenburgh</a>, American captain (b. 1888)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Eldon_P._Wyman\" class=\"mw-redirect\" title=\"Eldon P. Wyman\">Eldon P. Wyman</a>, American sailor (b. 1917)</li>\n</ul>", "links": [{"title": "Attack on Pearl Harbor", "link": "https://wikipedia.org/wiki/Attack_on_Pearl_Harbor"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>r<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_C<PERSON><PERSON>_Davis#Namesake"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_El<PERSON>berry"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_C._England"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_P._Wyman"}]}, {"year": "<PERSON><PERSON><PERSON>, American captain (b. 1887)", "text": null, "html": "<PERSON><PERSON><PERSON>, American captain (b. 1887) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Mer<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American captain (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Me<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American captain (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>r<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American sailor (b. 1915)", "text": null, "html": "<PERSON>, American sailor (b. 1915) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>#Namesake\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>#Namesake\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Frederick_<PERSON><PERSON>_Davis#Namesake"}]}, {"year": "<PERSON>, American sailor (b. 1921)", "text": null, "html": "<PERSON>, American sailor (b. 1921) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American sailor (b. 1920)", "text": null, "html": "<PERSON>, American sailor (b. 1920) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/John_C._England\" title=\"John C. England\"><PERSON></a>, American sailor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/John_C._England\" title=\"John C. England\"><PERSON></a>, American sailor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/John_<PERSON>._England"}]}, {"year": "<PERSON>, American sailor (b. 1894)", "text": null, "html": "<PERSON>, American sailor (b. 1894) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American sailor (b. 1906)", "text": null, "html": "<PERSON>, American sailor (b. 1906) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American sailor (b. 1918)", "text": null, "html": "<PERSON>, American sailor (b. 1918) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American admiral (b. 1884)", "text": null, "html": "<PERSON>, American admiral (b. 1884) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American sailor (b. 1916)", "text": null, "html": "<PERSON>, American sailor (b. 1916) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American sailor (b. 1917)", "text": null, "html": "<PERSON>, American sailor (b. 1917) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American sailor (b. 1895)", "text": null, "html": "<PERSON>, American sailor (b. 1895) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, American priest and sailor (b. 1909)", "text": null, "html": "<PERSON><PERSON><PERSON>, American priest and sailor (b. 1909) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American priest and sailor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American priest and sailor (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American sailor (b. 1915)", "text": null, "html": "<PERSON>, American sailor (b. 1915) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American sailor (b. 1893)", "text": null, "html": "<PERSON>, American sailor (b. 1893) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American sailor (b. 1919)", "text": null, "html": "<PERSON>, American sailor (b. 1919) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sailor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American captain (b. 1888)", "text": null, "html": "<PERSON>, American captain (b. 1888) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, American sailor (b. 1917)", "text": null, "html": "<PERSON><PERSON>, American sailor (b. 1917) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Eldon_P._Wyman\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sailor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Eldon_P._Wyman\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sailor (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Wyman"}]}, {"year": "1947", "text": "<PERSON>, French author and playwright (b. 1866)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American philosopher and academic, Nobel Prize laureate (b. 1862)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1949", "text": "<PERSON>, American author, playwright, and water polo player (b. 1877)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Rex_Beach\" title=\"Rex Beach\"><PERSON></a>, American author, playwright, and water polo player (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rex_Beach\" title=\"Rex Beach\"><PERSON></a>, American author, playwright, and water polo player (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rex_Beach"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Canadian-American actor (b. 1887)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gordon\"><PERSON><PERSON></a>, Canadian-American actor (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gordon\"><PERSON><PERSON></a>, Canadian-American actor (b. 1887)", "links": [{"title": "Huntley Gordon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Turkish author and playwright (b. 1889)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Re%C5%9Fat_Nuri_G%C3%BCntekin\" title=\"Reşat Nuri Güntekin\"><PERSON><PERSON><PERSON></a>, Turkish author and playwright (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Re%C5%9Fat_Nuri_G%C3%BCntekin\" title=\"Reşat Nuri Güntekin\"><PERSON><PERSON><PERSON></a>, Turkish author and playwright (b. 1889)", "links": [{"title": "Reşat N<PERSON>", "link": "https://wikipedia.org/wiki/Re%C5%9Fat_Nuri_G%C3%BCntekin"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Greek admiral and politician (b. 1882)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek admiral and politician (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek admiral and politician (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Norwegian opera singer (b. 1895)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian opera singer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian opera singer (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American baseball player and manager (b. 1897)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Lefty_O%27Doul\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lefty_O%27Doul\" title=\"<PERSON><PERSON> O<PERSON>ul\"><PERSON><PERSON></a>, American baseball player and manager (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lefty_O%27Doul"}]}, {"year": "1969", "text": "<PERSON>, English actor (b. 1903)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American cartoonist, sculptor, and author (b. 1883)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American cartoonist, sculptor, and author (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American cartoonist, sculptor, and author (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American novelist and playwright (b. 1897)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and playwright (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wilder\"><PERSON></a>, American novelist and playwright (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American actor (b. 1903)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American nutritionist (b. 1895)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nutritionist (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nutritionist (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English cricketer and umpire (b. 1913)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Hungarian-American engineer (b. 1906)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American engineer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American engineer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French race car driver (b. 1905)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American ornithologist and paleontologist (b. 1886)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and paleontologist (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and paleontologist (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English-American astronomer and astrophysicist (b. 1900)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American astronomer and astrophysicist (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American astronomer and astrophysicist (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American punk rock vocalist and songwriter (b. 1958)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Crash\" title=\"Darby Crash\"><PERSON><PERSON></a>, American punk rock vocalist and songwriter (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Crash\" title=\"Darby Crash\"><PERSON><PERSON></a>, American punk rock vocalist and songwriter (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American voice actor (b. 1910)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American race car driver (b. 1938)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>rbrough\"><PERSON><PERSON><PERSON></a>, American race car driver (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Yarbrough\"><PERSON><PERSON><PERSON></a>, American race car driver (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON> <PERSON><PERSON>, American photographer and journalist (b. 1906)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American photographer and journalist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American photographer and journalist (b. 1906)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English poet, novelist, critic (b. 1895)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, novelist, critic (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, novelist, critic (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American soldier and jurist (b. 1915)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and jurist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and jurist (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "Hay<PERSON><PERSON> Calhoun, American wrestler and actor (b. 1934)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Haystacks_Calhoun\" title=\"Haystacks Calhoun\"><PERSON><PERSON><PERSON> <PERSON></a>, American wrestler and actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haystacks_Calhoun\" title=\"Haystacks Calhoun\"><PERSON><PERSON><PERSON> Calhoun</a>, American wrestler and actor (b. 1934)", "links": [{"title": "Haystacks Calhoun", "link": "https://wikipedia.org/wiki/Haystacks_Calhoun"}]}, {"year": "1989", "text": "<PERSON>, French-German painter (b. 1904)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German painter (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German painter (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actress (b. 1910)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian painter and educator (b. 1904)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and educator (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and educator (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American politician, 45th Governor of New Jersey, and Chief Justice of the New Jersey Supreme Court (b. 1909)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>, and Chief Justice of the New Jersey Supreme Court (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>, and Chief Justice of the New Jersey Supreme Court (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Turkish-French painter and illustrator (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>bid<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish-French painter and illustrator (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish-French painter and illustrator (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abid<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Ivoirian physician and politician, 1st President of Ivory Coast (b. 1905)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Houphou%C3%ABt-Boigny\" title=\"<PERSON>\"><PERSON></a>, Ivoirian physician and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ivory_Coast\" title=\"List of heads of state of Ivory Coast\">President of Ivory Coast</a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Houphou%C3%ABt-Boigny\" title=\"<PERSON>ign<PERSON>\"><PERSON></a>, Ivoirian physician and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ivory_Coast\" title=\"List of heads of state of Ivory Coast\">President of Ivory Coast</a> (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Houphou%C3%ABt-Boigny"}, {"title": "List of heads of state of Ivory Coast", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Ivory_Coast"}]}, {"year": "1995", "text": "<PERSON>, English actress (b. 1892)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Scottish footballer and manager (b. 1942)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English-American composer and conductor (b. 1920)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American composer and conductor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American composer and conductor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American biochemist and endocrinologist, Nobel Prize laureate (b. 1925)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and endocrinologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and endocrinologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2003", "text": "<PERSON><PERSON> journalist and theologian (b. 1913)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American journalist and theologian (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American journalist and theologian (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American educator and politician, 36th Treasurer of the United States (b. 1933)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician, 36th <a href=\"https://wikipedia.org/wiki/Treasurer_of_the_United_States\" title=\"Treasurer of the United States\">Treasurer of the United States</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician, 36th <a href=\"https://wikipedia.org/wiki/Treasurer_of_the_United_States\" title=\"Treasurer of the United States\">Treasurer of the United States</a> (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Treasurer of the United States", "link": "https://wikipedia.org/wiki/Treasurer_of_the_United_States"}]}, {"year": "2004", "text": "<PERSON>, American conductor and educator (b. 1914)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American singer and guitarist (b. 1913)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American businessman and philanthropist, co-founded Am<PERSON> (b. 1924)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Amway\" title=\"Amway\">Am<PERSON></a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Amway\" title=\"Amway\">Amway</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Amway", "link": "https://wikipedia.org/wiki/Amway"}]}, {"year": "2005", "text": "<PERSON>, American football player and coach (b. 1931)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Carson\"><PERSON></a>, American football player and coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American academic and diplomat, 16th United States Ambassador to the United Nations (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and diplomat, 16th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and diplomat, 16th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "2008", "text": "<PERSON>, American banker and lawyer (b. 1908)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and lawyer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and lawyer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American lawyer and author (b. 1949)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Finnish singer (b. 1945)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American actor (b. 1915)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Dutch physician and politician, Dutch Minister of Defence (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physician and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Defence_of_the_Netherlands\" class=\"mw-redirect\" title=\"List of Ministers of Defence of the Netherlands\">Dutch Minister of Defence</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physician and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Defence_of_the_Netherlands\" class=\"mw-redirect\" title=\"List of Ministers of Defence of the Netherlands\">Dutch Minister of Defence</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Ministers of Defence of the Netherlands", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Defence_of_the_Netherlands"}]}, {"year": "2012", "text": "<PERSON>, American colonel and pilot (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American table tennis player and author (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American table tennis player and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American table tennis player and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American businessman and financier (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman and financier (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman and financier (b. 1939)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, French actor, director, producer, and screenwriter (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor, director, producer, and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor, director, producer, and screenwriter (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American singer and guitarist (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American author and educator (b. 1954)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(storyteller)\" title=\"<PERSON> (storyteller)\"><PERSON></a>, American author and educator (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(storyteller)\" title=\"<PERSON> (storyteller)\"><PERSON></a>, American author and educator (b. 1954)", "links": [{"title": "<PERSON> (storyteller)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(storyteller)"}]}, {"year": "2015", "text": "<PERSON>, American sociologist and academic (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, American astronomer and academic (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Spinrad\" title=\"<PERSON>yr<PERSON> Spinrad\"><PERSON><PERSON><PERSON></a>, American astronomer and academic (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Spinrad\" title=\"<PERSON>yr<PERSON> Spinrad\"><PERSON><PERSON><PERSON></a>, American astronomer and academic (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yron_Spinrad"}]}, {"year": "2015", "text": "<PERSON>, English race car driver (b. 1938)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Peter_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Pakistani recording artist, television personality, fashion designer, occasional actor, singer-songwriter and preacher. (b. 1964)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>shed\"><PERSON><PERSON></a>, Pakistani recording artist, television personality, fashion designer, occasional actor, singer-songwriter and preacher. (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>shed\"><PERSON><PERSON></a>, Pakistani recording artist, television personality, fashion designer, occasional actor, singer-songwriter and preacher. (b. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jun<PERSON>_<PERSON>shed"}]}, {"year": "2015", "text": "<PERSON>, English actress (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English musician (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Native American actor (b. 1962)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American actor (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American actor (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, English football player and manager (b. 1932)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football player and manager (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football player and manager (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American baseball player and tenor (b. 1942)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and tenor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and tenor (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American aviator (b. 1923)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aviator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aviator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, British writer and dub poet (b.1958)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer and <a href=\"https://wikipedia.org/wiki/Dub_poetry\" title=\"Dub poetry\">dub poet</a> (b.1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer and <a href=\"https://wikipedia.org/wiki/Dub_poetry\" title=\"Dub poetry\">dub poet</a> (b.1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dub poetry", "link": "https://wikipedia.org/wiki/Dub_poetry"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Palestinian professor and writer (b.1979)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>at_<PERSON>areer\" title=\"<PERSON><PERSON>at Alareer\"><PERSON><PERSON><PERSON></a>, Palestinian professor and writer (b.1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Refaat Alareer\"><PERSON><PERSON><PERSON></a>, Palestinian professor and writer (b.1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Refaat_Alareer"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Japanese volleyball player (b. 1937)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese volleyball player (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese volleyball player (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Congolese <PERSON> and composer (b. 1965)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese <PERSON> and composer (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese <PERSON> and composer (b. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}