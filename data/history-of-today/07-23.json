{"date": "July 23", "url": "https://wikipedia.org/wiki/July_23", "data": {"Events": [{"year": "811", "text": "Byzantine emperor <PERSON><PERSON><PERSON><PERSON> I plunders the Bulgarian capital of Pliska and captures Khan Krum's treasury.", "html": "811 - <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> emperor <a href=\"https://wikipedia.org/wiki/<PERSON>ph<PERSON>s_I\" title=\"<PERSON><PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON><PERSON> I</a> plunders the <a href=\"https://wikipedia.org/wiki/First_Bulgarian_Empire\" title=\"First Bulgarian Empire\">Bulgarian</a> capital of <a href=\"https://wikipedia.org/wiki/Pliska\" title=\"Pliska\">Pliska</a> and captures <PERSON> <a href=\"https://wikipedia.org/wiki/Krum\" title=\"Krum\">Krum</a>'s treasury.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> emperor <a href=\"https://wikipedia.org/wiki/<PERSON>ph<PERSON>s_I\" title=\"<PERSON><PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON><PERSON> I</a> plunders the <a href=\"https://wikipedia.org/wiki/First_Bulgarian_Empire\" title=\"First Bulgarian Empire\">Bulgarian</a> capital of <a href=\"https://wikipedia.org/wiki/Pliska\" title=\"Pliska\">Pliska</a> and captures <PERSON> <a href=\"https://wikipedia.org/wiki/Krum\" title=\"Krum\">Krum</a>'s treasury.", "links": [{"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I"}, {"title": "First Bulgarian Empire", "link": "https://wikipedia.org/wiki/First_Bulgarian_Empire"}, {"title": "Pliska", "link": "https://wikipedia.org/wiki/<PERSON>liska"}, {"title": "Krum", "link": "https://wikipedia.org/wiki/Krum"}]}, {"year": "1319", "text": "A Knights Hospitaller fleet scores a crushing victory over an Aydinid fleet off Chios.", "html": "1319 - A <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a> fleet scores a <a href=\"https://wikipedia.org/wiki/Battle_of_Chios_(1319)\" title=\"Battle of Chios (1319)\">crushing victory</a> over an <a href=\"https://wikipedia.org/wiki/Aydinid\" class=\"mw-redirect\" title=\"Aydinid\">Aydinid</a> fleet off <a href=\"https://wikipedia.org/wiki/Chios\" title=\"Chios\">Chios</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a> fleet scores a <a href=\"https://wikipedia.org/wiki/Battle_of_Chios_(1319)\" title=\"Battle of Chios (1319)\">crushing victory</a> over an <a href=\"https://wikipedia.org/wiki/Aydinid\" class=\"mw-redirect\" title=\"Aydinid\">Aydinid</a> fleet off <a href=\"https://wikipedia.org/wiki/Chios\" title=\"Chios\">Chios</a>.", "links": [{"title": "Knights Hospitaller", "link": "https://wikipedia.org/wiki/Knights_Hospitaller"}, {"title": "Battle of Chios (1319)", "link": "https://wikipedia.org/wiki/Battle_of_Chios_(1319)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aydinid"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chios"}]}, {"year": "1632", "text": "Three hundred colonists bound for New France depart from Dieppe, France.", "html": "1632 - Three hundred colonists bound for <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a> depart from <a href=\"https://wikipedia.org/wiki/Dieppe\" title=\"Dieppe\">Dieppe</a>, France.", "no_year_html": "Three hundred colonists bound for <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a> depart from <a href=\"https://wikipedia.org/wiki/Dieppe\" title=\"Dieppe\">Dieppe</a>, France.", "links": [{"title": "New France", "link": "https://wikipedia.org/wiki/New_France"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dieppe"}]}, {"year": "1677", "text": "Scanian War: Denmark-Norway captures the harbor town of Marstrand from Sweden.", "html": "1677 - <a href=\"https://wikipedia.org/wiki/Scanian_War\" title=\"Scanian War\">Scanian War</a>: <a href=\"https://wikipedia.org/wiki/Denmark%E2%80%93Norway\" title=\"Denmark-Norway\">Denmark-Norway</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Marstrand\" title=\"Battle of Marstrand\">captures</a> the harbor town of <a href=\"https://wikipedia.org/wiki/Marstrand\" title=\"Marstrand\">Marstrand</a> from Sweden.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scanian_War\" title=\"Scanian War\">Scanian War</a>: <a href=\"https://wikipedia.org/wiki/Denmark%E2%80%93Norway\" title=\"Denmark-Norway\">Denmark-Norway</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Marstrand\" title=\"Battle of Marstrand\">captures</a> the harbor town of <a href=\"https://wikipedia.org/wiki/Marstrand\" title=\"Marstrand\">Marstrand</a> from Sweden.", "links": [{"title": "Scanian War", "link": "https://wikipedia.org/wiki/Scanian_War"}, {"title": "Denmark-Norway", "link": "https://wikipedia.org/wiki/Denmark%E2%80%93Norway"}, {"title": "Battle of Marstrand", "link": "https://wikipedia.org/wiki/Battle_of_Marstrand"}, {"title": "Marstrand", "link": "https://wikipedia.org/wiki/Marstrand"}]}, {"year": "1793", "text": "Kingdom of Prussia re-conquers Mainz from France.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Kingdom of Prussia</a> re-conquers <a href=\"https://wikipedia.org/wiki/Mainz\" title=\"Mainz\">Mainz</a> from France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Kingdom of Prussia</a> re-conquers <a href=\"https://wikipedia.org/wiki/Mainz\" title=\"Mainz\">Mainz</a> from France.", "links": [{"title": "Kingdom of Prussia", "link": "https://wikipedia.org/wiki/Kingdom_of_Prussia"}, {"title": "Mainz", "link": "https://wikipedia.org/wiki/Mainz"}]}, {"year": "1813", "text": "Sir <PERSON> is appointed as the first Governor of Malta, transforming the island from a British protectorate to a de facto colony.", "html": "1813 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a> is appointed as the first <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Malta\" class=\"mw-redirect\" title=\"List of Governors of Malta\">Governor of Malta</a>, transforming the island from a <a href=\"https://wikipedia.org/wiki/Malta_Protectorate\" title=\"Malta Protectorate\">British protectorate</a> to a <i>de facto</i> <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Malta\" title=\"Crown Colony of Malta\">colony</a>.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a> is appointed as the first <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Malta\" class=\"mw-redirect\" title=\"List of Governors of Malta\">Governor of Malta</a>, transforming the island from a <a href=\"https://wikipedia.org/wiki/Malta_Protectorate\" title=\"Malta Protectorate\">British protectorate</a> to a <i>de facto</i> <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Malta\" title=\"Crown Colony of Malta\">colony</a>.", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)"}, {"title": "List of Governors of Malta", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Malta"}, {"title": "Malta Protectorate", "link": "https://wikipedia.org/wiki/Malta_Protectorate"}, {"title": "Crown Colony of Malta", "link": "https://wikipedia.org/wiki/Crown_Colony_of_Malta"}]}, {"year": "1821", "text": "While the Mora Rebellion continues, Greeks capture Monemvasia Castle. Turkish troops and citizens are transferred to Asia Minor's coasts.", "html": "1821 - While the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Mora Rebellion</a> continues, Greeks capture <a href=\"https://wikipedia.org/wiki/Monemvasia\" title=\"Monemvasia\">Monemvasia Castle</a>. Turkish troops and citizens are transferred to Asia Minor's coasts.", "no_year_html": "While the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Mora Rebellion</a> continues, Greeks capture <a href=\"https://wikipedia.org/wiki/Monemvasia\" title=\"Monemvasia\">Monemvasia Castle</a>. Turkish troops and citizens are transferred to Asia Minor's coasts.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "Monemvasia", "link": "https://wikipedia.org/wiki/Monemvasia"}]}, {"year": "1829", "text": "In the United States, <PERSON> patents the typographer, a precursor to the typewriter.", "html": "1829 - In the United States, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> patents the <a href=\"https://wikipedia.org/wiki/Typographer_(typewriter)\" title=\"Typographer (typewriter)\">typographer</a>, a precursor to the <a href=\"https://wikipedia.org/wiki/Typewriter\" title=\"Typewriter\">typewriter</a>.", "no_year_html": "In the United States, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> patents the <a href=\"https://wikipedia.org/wiki/Typographer_(typewriter)\" title=\"Typographer (typewriter)\">typographer</a>, a precursor to the <a href=\"https://wikipedia.org/wiki/Typewriter\" title=\"Typewriter\">typewriter</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>pographer (typewriter)", "link": "https://wikipedia.org/wiki/Typographer_(typewriter)"}, {"title": "Typewriter", "link": "https://wikipedia.org/wiki/Typewriter"}]}, {"year": "1840", "text": "The Province of Canada is created by the Act of Union.", "html": "1840 - The <a href=\"https://wikipedia.org/wiki/Province_of_Canada\" title=\"Province of Canada\">Province of Canada</a> is created by the <a href=\"https://wikipedia.org/wiki/Act_of_Union_1840\" title=\"Act of Union 1840\">Act of Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Province_of_Canada\" title=\"Province of Canada\">Province of Canada</a> is created by the <a href=\"https://wikipedia.org/wiki/Act_of_Union_1840\" title=\"Act of Union 1840\">Act of Union</a>.", "links": [{"title": "Province of Canada", "link": "https://wikipedia.org/wiki/Province_of_Canada"}, {"title": "Act of Union 1840", "link": "https://wikipedia.org/wiki/Act_of_Union_1840"}]}, {"year": "1862", "text": "American Civil War: <PERSON> becomes general-in-chief of the Union Army.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Commanding_General_of_the_United_States_Army\" title=\"Commanding General of the United States Army\">general-in-chief</a> of the <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Commanding_General_of_the_United_States_Army\" title=\"Commanding General of the United States Army\">general-in-chief</a> of the <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Commanding General of the United States Army", "link": "https://wikipedia.org/wiki/Commanding_General_of_the_United_States_Army"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}]}, {"year": "1874", "text": "<PERSON> de Ornelas e V<PERSON>los is appointed the Archbishop of the Portuguese colonial enclave of Goa, India.", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Aires_de_Ornelas_e_Vasconcelos\" title=\"Aires de Ornelas e Vasconcelos\">Aires de Ornelas e Vasconcelos</a> is appointed the <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">Archbishop</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Portuguese</a> colonial enclave of <a href=\"https://wikipedia.org/wiki/Portuguese_Goa\" class=\"mw-redirect\" title=\"Portuguese Goa\">Goa</a>, India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aires_de_Ornelas_e_Vasconcelos\" title=\"Aires de Ornelas e Vasconcelos\">Aires de Ornelas e Vasconcelos</a> is appointed the <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">Archbishop</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Portuguese</a> colonial enclave of <a href=\"https://wikipedia.org/wiki/Portuguese_Goa\" class=\"mw-redirect\" title=\"Portuguese Goa\">Goa</a>, India.", "links": [{"title": "Aires de Ornelas e Vasconcelos", "link": "https://wikipedia.org/wiki/Aires_de_Ornelas_e_Vasconcelos"}, {"title": "Archbishop", "link": "https://wikipedia.org/wiki/Archbishop"}, {"title": "Kingdom of Portugal", "link": "https://wikipedia.org/wiki/Kingdom_of_Portugal"}, {"title": "Portuguese Goa", "link": "https://wikipedia.org/wiki/Portuguese_Goa"}]}, {"year": "1881", "text": "The Boundary Treaty of 1881 between Chile and Argentina is signed in Buenos Aires.", "html": "1881 - The <a href=\"https://wikipedia.org/wiki/Boundary_Treaty_of_1881_between_Chile_and_Argentina\" title=\"Boundary Treaty of 1881 between Chile and Argentina\">Boundary Treaty of 1881 between Chile and Argentina</a> is signed in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Boundary_Treaty_of_1881_between_Chile_and_Argentina\" title=\"Boundary Treaty of 1881 between Chile and Argentina\">Boundary Treaty of 1881 between Chile and Argentina</a> is signed in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>.", "links": [{"title": "Boundary Treaty of 1881 between Chile and Argentina", "link": "https://wikipedia.org/wiki/Boundary_Treaty_of_1881_between_Chile_and_Argentina"}, {"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}]}, {"year": "1900", "text": "Pressed by expanding immigration, Canada closes its doors to paupers and criminals.", "html": "1900 - Pressed by expanding immigration, <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a> closes its doors to paupers and criminals.", "no_year_html": "Pressed by expanding immigration, <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a> closes its doors to paupers and criminals.", "links": [{"title": "Canada", "link": "https://wikipedia.org/wiki/Canada"}]}, {"year": "1903", "text": "The Ford Motor Company sells its first car.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> sells its first car.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> sells its first car.", "links": [{"title": "Ford Motor Company", "link": "https://wikipedia.org/wiki/Ford_Motor_Company"}]}, {"year": "1908", "text": "The Second Constitution accepted by the Ottomans.", "html": "1908 - The <a href=\"https://wikipedia.org/wiki/Second_Constitutional_Era\" title=\"Second Constitutional Era\">Second Constitution</a> accepted by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottomans</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Constitutional_Era\" title=\"Second Constitutional Era\">Second Constitution</a> accepted by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottomans</a>.", "links": [{"title": "Second Constitutional Era", "link": "https://wikipedia.org/wiki/Second_Constitutional_Era"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1914", "text": "Austria-Hungary issues a series of demands in an ultimatum to the Kingdom of Serbia demanding Serbia to allow the Austrians to determine who assassinated Archduke <PERSON>. Serbia accepts all but one of those demands and Austria declares war on July 28.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> issues a series of demands in an <a href=\"https://wikipedia.org/wiki/July_Crisis\" title=\"July Crisis\">ultimatum</a> to the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia\" title=\"Kingdom of Serbia\">Kingdom of Serbia</a> demanding Serbia to allow the Austrians to determine who <a href=\"https://wikipedia.org/wiki/Assassination_of_Archduke_<PERSON>_<PERSON>_of_Austria\" class=\"mw-redirect\" title=\"Assassination of Archduke <PERSON> of Austria\">assassinated</a> <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON></a>. Serbia accepts all but one of those demands and Austria declares war on <a href=\"https://wikipedia.org/wiki/July_28\" title=\"July 28\">July 28</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> issues a series of demands in an <a href=\"https://wikipedia.org/wiki/July_Crisis\" title=\"July Crisis\">ultimatum</a> to the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia\" title=\"Kingdom of Serbia\">Kingdom of Serbia</a> demanding Serbia to allow the Austrians to determine who <a href=\"https://wikipedia.org/wiki/Assassination_of_Archduke_<PERSON>_<PERSON>_of_Austria\" class=\"mw-redirect\" title=\"Assassination of Archduke <PERSON> of Austria\">assassinated</a> <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON></a>. Serbia accepts all but one of those demands and Austria declares war on <a href=\"https://wikipedia.org/wiki/July_28\" title=\"July 28\">July 28</a>.", "links": [{"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}, {"title": "July Crisis", "link": "https://wikipedia.org/wiki/July_Crisis"}, {"title": "Kingdom of Serbia", "link": "https://wikipedia.org/wiki/Kingdom_of_Serbia"}, {"title": "Assassination of Arch<PERSON>ke <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_of_Austria"}, {"title": "Archduke <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Archdu<PERSON>_<PERSON>_<PERSON>_of_Austria"}, {"title": "July 28", "link": "https://wikipedia.org/wiki/July_28"}]}, {"year": "1919", "text": "Prince Regent <PERSON><PERSON><PERSON><PERSON> signs the decree establishing the University of Ljubljana", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\">Prince Regent <PERSON><PERSON><PERSON><PERSON></a> signs the decree establishing the <a href=\"https://wikipedia.org/wiki/University_of_Ljubljana\" title=\"University of Ljubljana\">University of Ljubljana</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\">Prince Regent <PERSON><PERSON><PERSON><PERSON></a> signs the decree establishing the <a href=\"https://wikipedia.org/wiki/University_of_Ljubljana\" title=\"University of Ljubljana\">University of Ljubljana</a>", "links": [{"title": "<PERSON> of Yugoslavia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia"}, {"title": "University of Ljubljana", "link": "https://wikipedia.org/wiki/University_of_Ljubljana"}]}, {"year": "1921", "text": "The Chinese Communist Party (CCP) is established at the founding National Congress.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> (CCP) is established at the <a href=\"https://wikipedia.org/wiki/1st_National_Congress_of_the_Chinese_Communist_Party\" title=\"1st National Congress of the Chinese Communist Party\">founding National Congress</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> (CCP) is established at the <a href=\"https://wikipedia.org/wiki/1st_National_Congress_of_the_Chinese_Communist_Party\" title=\"1st National Congress of the Chinese Communist Party\">founding National Congress</a>.", "links": [{"title": "Chinese Communist Party", "link": "https://wikipedia.org/wiki/Chinese_Communist_Party"}, {"title": "1st National Congress of the Chinese Communist Party", "link": "https://wikipedia.org/wiki/1st_National_Congress_of_the_Chinese_Communist_Party"}]}, {"year": "1926", "text": "Fox Film buys the patents of the Movietone sound system for recording sound onto film.", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Fox_Film\" title=\"Fox Film\">Fox Film</a> buys the patents of the <a href=\"https://wikipedia.org/wiki/Movietone_sound_system\" title=\"Movietone sound system\">Movietone sound system</a> for recording sound onto film.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fox_Film\" title=\"Fox Film\">Fox Film</a> buys the patents of the <a href=\"https://wikipedia.org/wiki/Movietone_sound_system\" title=\"Movietone sound system\">Movietone sound system</a> for recording sound onto film.", "links": [{"title": "Fox Film", "link": "https://wikipedia.org/wiki/Fox_Film"}, {"title": "Movietone sound system", "link": "https://wikipedia.org/wiki/Movietone_sound_system"}]}, {"year": "1927", "text": "The first station of the Indian Broadcasting Company goes on the air in Bombay.", "html": "1927 - The first station of the <a href=\"https://wikipedia.org/wiki/Indian_Broadcasting_Company\" class=\"mw-redirect\" title=\"Indian Broadcasting Company\">Indian Broadcasting Company</a> goes on the air in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Bombay</a>.", "no_year_html": "The first station of the <a href=\"https://wikipedia.org/wiki/Indian_Broadcasting_Company\" class=\"mw-redirect\" title=\"Indian Broadcasting Company\">Indian Broadcasting Company</a> goes on the air in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Bombay</a>.", "links": [{"title": "Indian Broadcasting Company", "link": "https://wikipedia.org/wiki/Indian_Broadcasting_Company"}, {"title": "Mumbai", "link": "https://wikipedia.org/wiki/Mumbai"}]}, {"year": "1936", "text": "In Catalonia, Spain, the Unified Socialist Party of Catalonia is founded through the merger of Socialist and Communist parties.", "html": "1936 - In <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>, Spain, the <a href=\"https://wikipedia.org/wiki/Unified_Socialist_Party_of_Catalonia\" title=\"Unified Socialist Party of Catalonia\">Unified Socialist Party of Catalonia</a> is founded through the merger of <a href=\"https://wikipedia.org/wiki/Socialism\" title=\"Socialism\">Socialist</a> and <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> parties.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>, Spain, the <a href=\"https://wikipedia.org/wiki/Unified_Socialist_Party_of_Catalonia\" title=\"Unified Socialist Party of Catalonia\">Unified Socialist Party of Catalonia</a> is founded through the merger of <a href=\"https://wikipedia.org/wiki/Socialism\" title=\"Socialism\">Socialist</a> and <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> parties.", "links": [{"title": "Catalonia", "link": "https://wikipedia.org/wiki/Catalonia"}, {"title": "Unified Socialist Party of Catalonia", "link": "https://wikipedia.org/wiki/Unified_Socialist_Party_of_Catalonia"}, {"title": "Socialism", "link": "https://wikipedia.org/wiki/Socialism"}, {"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}]}, {"year": "1940", "text": "The United States' Under Secretary of State <PERSON> issues a declaration on the U.S. non-recognition policy of the Soviet annexation and incorporation of three Baltic states: Estonia, Latvia and Lithuania.", "html": "1940 - The United States' <a href=\"https://wikipedia.org/wiki/Under_Secretary_of_State\" class=\"mw-redirect\" title=\"Under Secretary of State\">Under Secretary of State</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues a <a href=\"https://wikipedia.org/wiki/Welles_Declaration\" title=\"Welles Declaration\">declaration</a> on the U.S. non-recognition policy of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> annexation and incorporation of three <a href=\"https://wikipedia.org/wiki/Baltic_states\" title=\"Baltic states\">Baltic states</a>: <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> and <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "no_year_html": "The United States' <a href=\"https://wikipedia.org/wiki/Under_Secretary_of_State\" class=\"mw-redirect\" title=\"Under Secretary of State\">Under Secretary of State</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues a <a href=\"https://wikipedia.org/wiki/Welles_Declaration\" title=\"Welles Declaration\">declaration</a> on the U.S. non-recognition policy of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> annexation and incorporation of three <a href=\"https://wikipedia.org/wiki/Baltic_states\" title=\"Baltic states\">Baltic states</a>: <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> and <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "links": [{"title": "Under Secretary of State", "link": "https://wikipedia.org/wiki/Under_Secretary_of_State"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Welles Declaration", "link": "https://wikipedia.org/wiki/Welles_Declaration"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Baltic states", "link": "https://wikipedia.org/wiki/Baltic_states"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}]}, {"year": "1942", "text": "World War II: The German offensives Operation Edelweiss and Operation Braunschweig begin.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German offensives <a href=\"https://wikipedia.org/wiki/Operation_Edelweiss\" class=\"mw-redirect\" title=\"Operation Edelweiss\">Operation Edelweiss</a> and <a href=\"https://wikipedia.org/wiki/Operation_Braunschweig\" title=\"Operation Braunschweig\">Operation Braunschweig</a> begin.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German offensives <a href=\"https://wikipedia.org/wiki/Operation_Edelweiss\" class=\"mw-redirect\" title=\"Operation Edelweiss\">Operation Edelweiss</a> and <a href=\"https://wikipedia.org/wiki/Operation_Braunschweig\" title=\"Operation Braunschweig\">Operation Braunschweig</a> begin.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Edelweiss", "link": "https://wikipedia.org/wiki/Operation_Edelweiss"}, {"title": "Operation Braunschweig", "link": "https://wikipedia.org/wiki/Operation_Braunschweig"}]}, {"year": "1942", "text": "Bulgarian poet and Communist leader <PERSON> is executed by firing squad.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgarian</a> poet and Communist leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is executed by firing squad.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgarian</a> poet and Communist leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is executed by firing squad.", "links": [{"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "The Rayleigh bath chair murder occurred in Rayleigh, Essex, England.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/Rayleigh_bath_chair_murder\" title=\"Rayleigh bath chair murder\">Rayleigh bath chair murder</a> occurred in <a href=\"https://wikipedia.org/wiki/Rayleigh,_Essex\" title=\"Rayleigh, Essex\">Rayleigh, Essex</a>, England.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rayleigh_bath_chair_murder\" title=\"Rayleigh bath chair murder\">Rayleigh bath chair murder</a> occurred in <a href=\"https://wikipedia.org/wiki/Rayleigh,_Essex\" title=\"Rayleigh, Essex\">Rayleigh, Essex</a>, England.", "links": [{"title": "<PERSON><PERSON> bath chair murder", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_bath_chair_murder"}, {"title": "Rayleigh, Essex", "link": "https://wikipedia.org/wiki/Rayleigh,_Essex"}]}, {"year": "1943", "text": "World War II: The British destroyers HMS Eclipse and HMS Laforey sink the Italian submarine <PERSON><PERSON><PERSON><PERSON> in the Mediterranean after she torpedoes the cruiser HMS Newfoundland.", "html": "1943 - World War II: The British destroyers <a href=\"https://wikipedia.org/wiki/HMS_Eclipse_(H08)\" title=\"HMS Eclipse (H08)\">HMS <i>Eclipse</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Laforey_(G99)\" title=\"HMS Laforey (G99)\">HMS <i>La<PERSON>rey</i></a> sink the <a href=\"https://wikipedia.org/wiki/Italian_submarine_Ascianghi\" title=\"Italian submarine Ascianghi\">Italian submarine <i>Ascianghi</i></a> in the Mediterranean after she torpedoes the cruiser <a href=\"https://wikipedia.org/wiki/HMS_Newfoundland_(59)\" title=\"HMS Newfoundland (59)\">HMS <i>Newfoundland</i></a>.", "no_year_html": "World War II: The British destroyers <a href=\"https://wikipedia.org/wiki/HMS_Eclipse_(H08)\" title=\"HMS Eclipse (H08)\">HMS <i>Eclipse</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Laforey_(G99)\" title=\"HMS Laforey (G99)\">HMS <i>La<PERSON>rey</i></a> sink the <a href=\"https://wikipedia.org/wiki/Italian_submarine_Ascianghi\" title=\"Italian submarine <PERSON>cianghi\">Italian submarine <i>Ascianghi</i></a> in the Mediterranean after she torpedoes the cruiser <a href=\"https://wikipedia.org/wiki/HMS_Newfoundland_(59)\" title=\"HMS Newfoundland (59)\">HMS <i>Newfoundland</i></a>.", "links": [{"title": "HMS Eclipse (H08)", "link": "https://wikipedia.org/wiki/HMS_Eclipse_(H08)"}, {"title": "HMS Laforey (G99)", "link": "https://wikipedia.org/wiki/HMS_Laforey_(G99)"}, {"title": "Italian submarine <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Italian_submarine_Ascian<PERSON>i"}, {"title": "HMS Newfoundland (59)", "link": "https://wikipedia.org/wiki/HMS_Newfoundland_(59)"}]}, {"year": "1945", "text": "The post-war legal processes against <PERSON> begin.", "html": "1945 - The post-war legal processes against <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tain\" title=\"<PERSON>\"><PERSON></a> begin.", "no_year_html": "The post-war legal processes against <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tain\" title=\"<PERSON>\"><PERSON></a> begin.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Philippe_P%C3%A9tain"}]}, {"year": "1952", "text": "General <PERSON> leads the Free Officers Movement (formed by <PERSON><PERSON><PERSON>, the real power behind the coup) in overthrowing King <PERSON><PERSON><PERSON> of Egypt.", "html": "1952 - General <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Free_Officers_Movement_(Egypt)\" class=\"mw-redirect\" title=\"Free Officers Movement (Egypt)\">Free Officers Movement</a> (formed by <a href=\"https://wikipedia.org/wiki/Gama<PERSON>_Abdel_Nasser\" title=\"Gamal Abd<PERSON>sser\"><PERSON><PERSON><PERSON></a>, the real power behind the coup) in <a href=\"https://wikipedia.org/wiki/Egyptian_revolution_of_1952\" class=\"mw-redirect\" title=\"Egyptian revolution of 1952\">overthrowing</a> King <a href=\"https://wikipedia.org/wiki/Farouk_of_Egypt\" title=\"Farouk of Egypt\">Farouk of Egypt</a>.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Free_Officers_Movement_(Egypt)\" class=\"mw-redirect\" title=\"Free Officers Movement (Egypt)\">Free Officers Movement</a> (formed by <a href=\"https://wikipedia.org/wiki/Gama<PERSON>_Abdel_Nasser\" title=\"Gama<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, the real power behind the coup) in <a href=\"https://wikipedia.org/wiki/Egyptian_revolution_of_1952\" class=\"mw-redirect\" title=\"Egyptian revolution of 1952\">overthrowing</a> King <a href=\"https://wikipedia.org/wiki/Farouk_of_Egypt\" title=\"Farouk of Egypt\">Farouk of Egypt</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Free Officers Movement (Egypt)", "link": "https://wikipedia.org/wiki/Free_Officers_Movement_(Egypt)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Egyptian revolution of 1952", "link": "https://wikipedia.org/wiki/Egyptian_revolution_of_1952"}, {"title": "Far<PERSON>k of Egypt", "link": "https://wikipedia.org/wiki/Farouk_of_Egypt"}]}, {"year": "1961", "text": "The Sandinista National Liberation Front is founded in Nicaragua.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/Sandinista_National_Liberation_Front\" title=\"Sandinista National Liberation Front\">Sandinista National Liberation Front</a> is founded in <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sandinista_National_Liberation_Front\" title=\"Sandinista National Liberation Front\">Sandinista National Liberation Front</a> is founded in <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "links": [{"title": "Sandinista National Liberation Front", "link": "https://wikipedia.org/wiki/Sandinista_National_Liberation_Front"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}]}, {"year": "1962", "text": "Telstar relays the first publicly transmitted, live trans-Atlantic television program, featuring <PERSON>.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Telstar\" title=\"Telstar\">Telstar</a> relays the first publicly transmitted, live trans-Atlantic television program, featuring <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Telstar\" title=\"Telstar\">Telstar</a> relays the first publicly transmitted, live trans-Atlantic television program, featuring <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Telstar", "link": "https://wikipedia.org/wiki/Telstar"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Walter_<PERSON>ronkite"}]}, {"year": "1962", "text": "The International Agreement on the Neutrality of Laos is signed.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/International_Agreement_on_the_Neutrality_of_Laos\" title=\"International Agreement on the Neutrality of Laos\">International Agreement on the Neutrality of Laos</a> is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Agreement_on_the_Neutrality_of_Laos\" title=\"International Agreement on the Neutrality of Laos\">International Agreement on the Neutrality of Laos</a> is signed.", "links": [{"title": "International Agreement on the Neutrality of Laos", "link": "https://wikipedia.org/wiki/International_Agreement_on_the_Neutrality_of_Laos"}]}, {"year": "1962", "text": "<PERSON> becomes the first African American to be inducted into the National Baseball Hall of Fame.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first African American to be inducted into the National Baseball Hall of Fame.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first African American to be inducted into the National Baseball Hall of Fame.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "Detroit Riots: In Detroit, one of the worst riots in United States history begins on 12th Street in the predominantly African American inner city. It ultimately kills 43 people, injures 342 and burns about 1,400 buildings.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/1967_Detroit_riot\" title=\"1967 Detroit riot\">Detroit Riots</a>: In <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>, one of the worst riots in United States history begins on 12th Street in the predominantly <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> inner city. It ultimately kills 43 people, injures 342 and burns about 1,400 buildings.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1967_Detroit_riot\" title=\"1967 Detroit riot\">Detroit Riots</a>: In <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>, one of the worst riots in United States history begins on 12th Street in the predominantly <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> inner city. It ultimately kills 43 people, injures 342 and burns about 1,400 buildings.", "links": [{"title": "1967 Detroit riot", "link": "https://wikipedia.org/wiki/1967_Detroit_riot"}, {"title": "Detroit", "link": "https://wikipedia.org/wiki/Detroit"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}]}, {"year": "1968", "text": "Glenville shootout: In Cleveland, Ohio, a violent shootout between a Black Militant organization and the Cleveland Police Department occurs. During the shootout, a riot begins and lasts for five days.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Glenville_shootout\" title=\"Glenville shootout\">Glenville shootout</a>: In <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>, a violent shootout between a Black Militant organization and the <a href=\"https://wikipedia.org/wiki/Cleveland_Division_of_Police\" title=\"Cleveland Division of Police\">Cleveland Police Department</a> occurs. During the shootout, a riot begins and lasts for five days.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glenville_shootout\" title=\"Glenville shootout\">Glenville shootout</a>: In <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>, a violent shootout between a Black Militant organization and the <a href=\"https://wikipedia.org/wiki/Cleveland_Division_of_Police\" title=\"Cleveland Division of Police\">Cleveland Police Department</a> occurs. During the shootout, a riot begins and lasts for five days.", "links": [{"title": "Glenville shootout", "link": "https://wikipedia.org/wiki/Glenville_shootout"}, {"title": "Cleveland, Ohio", "link": "https://wikipedia.org/wiki/Cleveland,_Ohio"}, {"title": "Cleveland Division of Police", "link": "https://wikipedia.org/wiki/Cleveland_Division_of_Police"}]}, {"year": "1968", "text": "The only successful hijacking of an El Al aircraft takes place when a Boeing 707 carrying ten crew and 38 passengers is taken over by three members of the Popular Front for the Liberation of Palestine. The aircraft was en route from Rome, to Lod, Israel.", "html": "1968 - The only successful hijacking of an <a href=\"https://wikipedia.org/wiki/El_Al\" title=\"El Al\">El Al</a> aircraft takes place when a <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a> carrying ten crew and 38 passengers is <a href=\"https://wikipedia.org/wiki/El_Al_Flight_426_hijacking\" title=\"El Al Flight 426 hijacking\">taken over</a> by three members of the <a href=\"https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine\" title=\"Popular Front for the Liberation of Palestine\">Popular Front for the Liberation of Palestine</a>. The aircraft was en route from Rome, to <a href=\"https://wikipedia.org/wiki/Lod\" title=\"Lod\">Lod</a>, Israel.", "no_year_html": "The only successful hijacking of an <a href=\"https://wikipedia.org/wiki/El_Al\" title=\"El Al\">El Al</a> aircraft takes place when a <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a> carrying ten crew and 38 passengers is <a href=\"https://wikipedia.org/wiki/El_Al_Flight_426_hijacking\" title=\"El Al Flight 426 hijacking\">taken over</a> by three members of the <a href=\"https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine\" title=\"Popular Front for the Liberation of Palestine\">Popular Front for the Liberation of Palestine</a>. The aircraft was en route from Rome, to <a href=\"https://wikipedia.org/wiki/Lod\" title=\"Lod\">Lod</a>, Israel.", "links": [{"title": "El Al", "link": "https://wikipedia.org/wiki/El_Al"}, {"title": "Boeing 707", "link": "https://wikipedia.org/wiki/Boeing_707"}, {"title": "El Al Flight 426 hijacking", "link": "https://wikipedia.org/wiki/El_Al_Flight_426_hijacking"}, {"title": "Popular Front for the Liberation of Palestine", "link": "https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine"}, {"title": "Lod", "link": "https://wikipedia.org/wiki/Lod"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON> bin <PERSON> becomes Sultan of Oman after overthrowing his father, <PERSON> bin <PERSON> initiating massive reforms, modernization programs and end to a decade long civil war.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> bin <PERSON> Said\"><PERSON><PERSON><PERSON> bin <PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Sultan_of_Oman\" title=\"Sultan of Oman\">Sultan of Oman</a> after overthrowing his father, <a href=\"https://wikipedia.org/wiki/<PERSON>_bin_Taimur\" title=\"<PERSON> bin Taimur\"><PERSON> bin Taimur</a> initiating massive reforms, modernization programs and end to a decade long civil war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Sultan_of_Oman\" title=\"Sultan of Oman\">Sultan of Oman</a> after overthrowing his father, <a href=\"https://wikipedia.org/wiki/<PERSON>_bin_Taimur\" title=\"<PERSON> bin Taimur\"><PERSON> bin Taimur</a> initiating massive reforms, modernization programs and end to a decade long civil war.", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of Oman", "link": "https://wikipedia.org/wiki/Sultan_of_Oman"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "The United States launches Landsat 1, the first Earth-resources satellite.", "html": "1972 - The United States launches <i><a href=\"https://wikipedia.org/wiki/Landsat_1\" title=\"Landsat 1\">Landsat 1</a></i>, the first Earth-resources <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellite</a>.", "no_year_html": "The United States launches <i><a href=\"https://wikipedia.org/wiki/Landsat_1\" title=\"Landsat 1\">Landsat 1</a></i>, the first Earth-resources <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellite</a>.", "links": [{"title": "Landsat 1", "link": "https://wikipedia.org/wiki/Landsat_1"}, {"title": "Satellite", "link": "https://wikipedia.org/wiki/Satellite"}]}, {"year": "1974", "text": "The Greek military junta collapses, and former Prime Minister <PERSON><PERSON> is invited to lead the new government, beginning Greece's metapolitefsi era.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">Greek military junta</a> collapses, and former Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is invited to lead the new government, beginning Greece's <a href=\"https://wikipedia.org/wiki/Metapolitefsi\" title=\"Metapolitefsi\">metapolitefsi</a> era.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">Greek military junta</a> collapses, and former Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is invited to lead the new government, beginning Greece's <a href=\"https://wikipedia.org/wiki/Metapolitefsi\" title=\"Metapolitefsi\">metapolitefsi</a> era.", "links": [{"title": "Greek military junta of 1967-74", "link": "https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Metapolitefsi", "link": "https://wikipedia.org/wiki/Metapolitefsi"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON> becomes the first Vietnamese citizen and the first Asian in space when he flies aboard the Soyuz 37 mission as an Intercosmos Research Cosmonaut.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Ph%E1%BA%A1m_Tu%C3%A2n\" title=\"Phạ<PERSON> Tuân\"><PERSON><PERSON><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> citizen and the first Asian in space when he flies aboard the Soyuz 37 mission as an <a href=\"https://wikipedia.org/wiki/Intercosmos\" class=\"mw-redirect\" title=\"Intercosmos\">Intercosmos</a> Research Cosmonaut.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ph%E1%BA%A1m_Tu%C3%A2n\" title=\"Phạm Tuân\"><PERSON><PERSON><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> citizen and the first Asian in space when he flies aboard the Soyuz 37 mission as an <a href=\"https://wikipedia.org/wiki/Intercosmos\" class=\"mw-redirect\" title=\"Intercosmos\">Intercosmos</a> Research Cosmonaut.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ph%E1%BA%A1m_Tu%C3%A2n"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "Intercosmos", "link": "https://wikipedia.org/wiki/Intercosmos"}]}, {"year": "1982", "text": "Outside Santa Clarita, California, actor <PERSON> and two children are killed when a helicopter crashes onto them while shooting a scene from Twilight Zone: The Movie.", "html": "1982 - Outside <a href=\"https://wikipedia.org/wiki/Santa_Clarita,_California\" title=\"Santa Clarita, California\">Santa Clarita, California</a>, actor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and two children are <a href=\"https://wikipedia.org/wiki/Twilight_Zone_accident\" title=\"Twilight Zone accident\">killed</a> when a helicopter crashes onto them while shooting a scene from <i><a href=\"https://wikipedia.org/wiki/Twilight_Zone:_The_Movie\" title=\"Twilight Zone: The Movie\">Twilight Zone: The Movie</a></i>.", "no_year_html": "Outside <a href=\"https://wikipedia.org/wiki/Santa_Clarita,_California\" title=\"Santa Clarita, California\">Santa Clarita, California</a>, actor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> and two children are <a href=\"https://wikipedia.org/wiki/Twilight_Zone_accident\" title=\"Twilight Zone accident\">killed</a> when a helicopter crashes onto them while shooting a scene from <i><a href=\"https://wikipedia.org/wiki/Twilight_Zone:_The_Movie\" title=\"Twilight Zone: The Movie\">Twilight Zone: The Movie</a></i>.", "links": [{"title": "Santa Clarita, California", "link": "https://wikipedia.org/wiki/Santa_Clarita,_California"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Twilight Zone accident", "link": "https://wikipedia.org/wiki/Twilight_Zone_accident"}, {"title": "Twilight Zone: The Movie", "link": "https://wikipedia.org/wiki/Twilight_Zone:_The_Movie"}]}, {"year": "1983", "text": "Thirteen Sri Lanka Army soldiers are killed after a deadly ambush by the militant Liberation Tigers of Tamil Eelam.", "html": "1983 - Thirteen <a href=\"https://wikipedia.org/wiki/Sri_Lanka_Army\" title=\"Sri Lanka Army\">Sri Lanka Army</a> soldiers are killed after a <a href=\"https://wikipedia.org/wiki/Four_Four_Bravo\" class=\"mw-redirect\" title=\"Four Four Bravo\">deadly ambush</a> by the militant <a href=\"https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam\" title=\"Liberation Tigers of Tamil Eelam\">Liberation Tigers of Tamil Eelam</a>.", "no_year_html": "Thirteen <a href=\"https://wikipedia.org/wiki/Sri_Lanka_Army\" title=\"Sri Lanka Army\">Sri Lanka Army</a> soldiers are killed after a <a href=\"https://wikipedia.org/wiki/Four_Four_Bravo\" class=\"mw-redirect\" title=\"Four Four Bravo\">deadly ambush</a> by the militant <a href=\"https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam\" title=\"Liberation Tigers of Tamil Eelam\">Liberation Tigers of Tamil Eelam</a>.", "links": [{"title": "Sri Lanka Army", "link": "https://wikipedia.org/wiki/Sri_Lanka_Army"}, {"title": "Four Four Bravo", "link": "https://wikipedia.org/wiki/Four_Four_Bravo"}, {"title": "Liberation Tigers of Tamil Eelam", "link": "https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam"}]}, {"year": "1983", "text": "Gimli Glider: Air Canada Flight 143 runs out of fuel and makes a deadstick landing at Gimli, Manitoba.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Gim<PERSON>_Glider\" title=\"Gimli Glider\">G<PERSON><PERSON> Glider</a>: <a href=\"https://wikipedia.org/wiki/Air_Canada\" title=\"Air Canada\">Air Canada</a> Flight 143 runs out of fuel and makes a <a href=\"https://wikipedia.org/wiki/Deadstick_landing\" title=\"Deadstick landing\">deadstick landing</a> at <a href=\"https://wikipedia.org/wiki/Rural_Municipality_of_Gimli\" title=\"Rural Municipality of Gimli\">Gimli, Manitoba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gim<PERSON>_Glider\" title=\"Gim<PERSON> Glider\">G<PERSON><PERSON> Glider</a>: <a href=\"https://wikipedia.org/wiki/Air_Canada\" title=\"Air Canada\">Air Canada</a> Flight 143 runs out of fuel and makes a <a href=\"https://wikipedia.org/wiki/Deadstick_landing\" title=\"Deadstick landing\">deadstick landing</a> at <a href=\"https://wikipedia.org/wiki/Rural_Municipality_of_Gimli\" title=\"Rural Municipality of Gimli\">Gimli, Manitoba</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lider"}, {"title": "Air Canada", "link": "https://wikipedia.org/wiki/Air_Canada"}, {"title": "Deadstick landing", "link": "https://wikipedia.org/wiki/Deadstick_landing"}, {"title": "Rural Municipality of Gimli", "link": "https://wikipedia.org/wiki/Rural_Municipality_of_Gimli"}]}, {"year": "1988", "text": "General <PERSON><PERSON>, effective ruler of Burma since 1962, resigns after pro-democracy protests.", "html": "1988 - General <a href=\"https://wikipedia.org/wiki/Ne_Win\" title=\"Ne Win\"><PERSON><PERSON> <PERSON></a>, effective ruler of <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a> since 1962, resigns after pro-democracy protests.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/Ne_<PERSON>\" title=\"Ne Win\"><PERSON><PERSON> <PERSON></a>, effective ruler of <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a> since 1962, resigns after pro-democracy protests.", "links": [{"title": "<PERSON>e Win", "link": "https://wikipedia.org/wiki/Ne_Win"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "1992", "text": "A Vatican commission, led by Cardinal <PERSON>, establishes that limiting certain rights of homosexual people and non-married couples is not equivalent to discrimination on grounds of race or gender.", "html": "1992 - A <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Vatican</a> commission, led by Cardinal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\"><PERSON></a>, establishes that limiting certain rights of <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexual</a> people and non-married couples is not equivalent to discrimination on grounds of race or gender.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Vatican</a> commission, led by Cardinal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\"><PERSON></a>, establishes that limiting certain rights of <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexual</a> people and non-married couples is not equivalent to discrimination on grounds of race or gender.", "links": [{"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Homosexuality", "link": "https://wikipedia.org/wiki/Homosexuality"}]}, {"year": "1992", "text": "Abkhazia declares independence from Georgia.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Abkhazia\" title=\"Abkhazia\">Abkhazia</a> declares independence from <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abkhazia\" title=\"Abkhazia\">Abkhazia</a> declares independence from <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a>.", "links": [{"title": "Abkhazia", "link": "https://wikipedia.org/wiki/Abkhazia"}, {"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}]}, {"year": "1993", "text": "China Northwest Airlines Flight 2119 crashes during takeoff from Yinchuan Xihuayuan Airport in Yinchuan, Ningxia, China, killing 55 people.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/China_Northwest_Airlines_Flight_2119\" title=\"China Northwest Airlines Flight 2119\">China Northwest Airlines Flight 2119</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Yinchuan_Xihuayuan_Airport\" title=\"Yinchuan Xihuayuan Airport\">Yinchuan Xihuayuan Airport</a> in <a href=\"https://wikipedia.org/wiki/Yinchuan\" title=\"Yinchuan\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ningxia\" title=\"Ningxia\">Ningxia</a>, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, killing 55 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Northwest_Airlines_Flight_2119\" title=\"China Northwest Airlines Flight 2119\">China Northwest Airlines Flight 2119</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Yinchuan_Xihuayuan_Airport\" title=\"Yinchuan Xihuayuan Airport\">Yinchuan Xihuayuan Airport</a> in <a href=\"https://wikipedia.org/wiki/Yinchuan\" title=\"Yinchuan\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ningxia\" title=\"Ningxia\">Ningxia</a>, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, killing 55 people.", "links": [{"title": "China Northwest Airlines Flight 2119", "link": "https://wikipedia.org/wiki/China_Northwest_Airlines_Flight_2119"}, {"title": "Yinchuan Xihuayuan Airport", "link": "https://wikipedia.org/wiki/Yinchuan_Xihuayuan_Airport"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yinchuan"}, {"title": "Ningxia", "link": "https://wikipedia.org/wiki/Ningxia"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}]}, {"year": "1995", "text": "Comet <PERSON> is discovered; it becomes visible to the naked eye on Earth nearly a year later.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp\" title=\"Comet Hale-Bopp\">Comet Hale-Bopp</a> is discovered; it becomes visible to the naked eye on Earth nearly a year later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp\" title=\"Comet Hale-Bopp\">Comet Hale-Bopp</a> is discovered; it becomes visible to the naked eye on Earth nearly a year later.", "links": [{"title": "Comet Hale-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp"}]}, {"year": "1997", "text": "Digital Equipment Corporation files antitrust charges against chipmaker Intel.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Digital_Equipment_Corporation\" title=\"Digital Equipment Corporation\">Digital Equipment Corporation</a> files <a href=\"https://wikipedia.org/wiki/Competition_law\" title=\"Competition law\">antitrust</a> charges against chipmaker <a href=\"https://wikipedia.org/wiki/Intel\" title=\"Intel\">Intel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Digital_Equipment_Corporation\" title=\"Digital Equipment Corporation\">Digital Equipment Corporation</a> files <a href=\"https://wikipedia.org/wiki/Competition_law\" title=\"Competition law\">antitrust</a> charges against chipmaker <a href=\"https://wikipedia.org/wiki/Intel\" title=\"Intel\">Intel</a>.", "links": [{"title": "Digital Equipment Corporation", "link": "https://wikipedia.org/wiki/Digital_Equipment_Corporation"}, {"title": "Competition law", "link": "https://wikipedia.org/wiki/Competition_law"}, {"title": "Intel", "link": "https://wikipedia.org/wiki/Intel"}]}, {"year": "1999", "text": "ANA Flight 61 is hijacked in Tokyo, Japan by <PERSON><PERSON>.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/All_Nippon_Airways_Flight_61\" title=\"All Nippon Airways Flight 61\">ANA Flight 61</a> is <a href=\"https://wikipedia.org/wiki/Aircraft_hijacking\" title=\"Aircraft hijacking\">hijacked</a> in <a href=\"https://wikipedia.org/wiki/Tokyo\" title=\"Tokyo\">Tokyo</a>, <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a> by <a href=\"https://wikipedia.org/wiki/All_Nippon_Airways_Flight_61\" title=\"All Nippon Airways Flight 61\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/All_Nippon_Airways_Flight_61\" title=\"All Nippon Airways Flight 61\">ANA Flight 61</a> is <a href=\"https://wikipedia.org/wiki/Aircraft_hijacking\" title=\"Aircraft hijacking\">hijacked</a> in <a href=\"https://wikipedia.org/wiki/Tokyo\" title=\"Tokyo\">Tokyo</a>, <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a> by <a href=\"https://wikipedia.org/wiki/All_Nippon_Airways_Flight_61\" title=\"All Nippon Airways Flight 61\"><PERSON><PERSON></a>.", "links": [{"title": "All Nippon Airways Flight 61", "link": "https://wikipedia.org/wiki/All_Nippon_Airways_Flight_61"}, {"title": "Aircraft hijacking", "link": "https://wikipedia.org/wiki/Aircraft_hijacking"}, {"title": "Tokyo", "link": "https://wikipedia.org/wiki/Tokyo"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}, {"title": "All Nippon Airways Flight 61", "link": "https://wikipedia.org/wiki/All_Nippon_Airways_Flight_61"}]}, {"year": "1999", "text": "Space Shuttle Columbia launches on STS-93, with <PERSON> becoming the first female space shuttle commander. The shuttle also carried and deployed the Chandra X-ray Observatory.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-93\" title=\"STS-93\">STS-93</a>, with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becoming the first female space shuttle commander. The shuttle also carried and deployed the <a href=\"https://wikipedia.org/wiki/Chandra_X-ray_Observatory\" title=\"Chandra X-ray Observatory\">Chandra X-ray Observatory</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-93\" title=\"STS-93\">STS-93</a>, with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becoming the first female space shuttle commander. The shuttle also carried and deployed the <a href=\"https://wikipedia.org/wiki/Chandra_X-ray_Observatory\" title=\"Chandra X-ray Observatory\">Chandra X-ray Observatory</a>.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-93", "link": "https://wikipedia.org/wiki/STS-93"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chandra X-ray Observatory", "link": "https://wikipedia.org/wiki/Chandra_X-ray_Observatory"}]}, {"year": "2005", "text": "Three bombs explode in the Naama Bay area of Sharm El Sheikh, Egypt, killing 88 people.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/2005_Shar<PERSON>_<PERSON>-<PERSON>_attacks\" class=\"mw-redirect\" title=\"2005 Sharm el-Sheikh attacks\">Three bombs explode</a> in the Naama Bay area of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egypt, killing 88 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2005_Shar<PERSON>_<PERSON>-<PERSON>_attacks\" class=\"mw-redirect\" title=\"2005 Sharm el-Sheikh attacks\">Three bombs explode</a> in the Naama Bay area of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egypt, killing 88 people.", "links": [{"title": "2005 <PERSON><PERSON><PERSON> el<PERSON>Sheikh attacks", "link": "https://wikipedia.org/wiki/2005_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_attacks"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "A high-speed train rear-ends another on a viaduct on the Yongtaiwen railway line in Wenzhou, Zhejiang province, China, resulting in 40 deaths.", "html": "2011 - A <a href=\"https://wikipedia.org/wiki/High-speed_rail\" title=\"High-speed rail\">high-speed train</a> <a href=\"https://wikipedia.org/wiki/Rear-end_collision\" title=\"Rear-end collision\">rear-ends</a> <a href=\"https://wikipedia.org/wiki/Wenzhou_train_collision\" title=\"Wenzhou train collision\">another on a viaduct</a> on the <a href=\"https://wikipedia.org/wiki/Ningbo%E2%80%93Taizhou%E2%80%93Wenzhou_Railway\" class=\"mw-redirect\" title=\"Ningbo-Taizhou-Wenzhou Railway\">Yongtaiwen railway line</a> in <a href=\"https://wikipedia.org/wiki/Wenzhou\" title=\"Wenzhou\">Wenzhou</a>, <a href=\"https://wikipedia.org/wiki/Zhejiang\" title=\"Zhejiang\">Zhejiang</a> province, China, resulting in 40 deaths.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/High-speed_rail\" title=\"High-speed rail\">high-speed train</a> <a href=\"https://wikipedia.org/wiki/Rear-end_collision\" title=\"Rear-end collision\">rear-ends</a> <a href=\"https://wikipedia.org/wiki/Wenzhou_train_collision\" title=\"Wenzhou train collision\">another on a viaduct</a> on the <a href=\"https://wikipedia.org/wiki/Ningbo%E2%80%93Taizhou%E2%80%93Wenzhou_Railway\" class=\"mw-redirect\" title=\"Ningbo-Taizhou-Wenzhou Railway\">Yongtaiwen railway line</a> in <a href=\"https://wikipedia.org/wiki/Wenzhou\" title=\"Wenzhou\">Wenzhou</a>, <a href=\"https://wikipedia.org/wiki/Zhejiang\" title=\"Zhejiang\">Zhejiang</a> province, China, resulting in 40 deaths.", "links": [{"title": "High-speed rail", "link": "https://wikipedia.org/wiki/High-speed_rail"}, {"title": "Rear-end collision", "link": "https://wikipedia.org/wiki/Rear-end_collision"}, {"title": "Wenzhou train collision", "link": "https://wikipedia.org/wiki/Wenzhou_train_collision"}, {"title": "Ningbo-Taizhou-Wenzhou Railway", "link": "https://wikipedia.org/wiki/Ningbo%E2%80%93Taizhou%E2%80%93Wenzhou_Railway"}, {"title": "Wenzhou", "link": "https://wikipedia.org/wiki/Wenzhou"}, {"title": "Zhejiang", "link": "https://wikipedia.org/wiki/Zhejiang"}]}, {"year": "2012", "text": "The Solar storm of 2012 was an unusually large coronal mass ejection that was emitted by the Sun which barely missed the Earth by nine days. If it hit, it would have caused up to US$2.6 trillion in damages to electrical equipment worldwide.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/Solar_storm_of_2012\" class=\"mw-redirect\" title=\"Solar storm of 2012\">Solar storm of 2012</a> was an unusually large <a href=\"https://wikipedia.org/wiki/Coronal_mass_ejection\" title=\"Coronal mass ejection\">coronal mass ejection</a> that was emitted by the <a href=\"https://wikipedia.org/wiki/Sun\" title=\"Sun\">Sun</a> which barely missed the <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> by nine days. If it hit, it would have caused up to US$2.6 trillion in damages to electrical equipment worldwide.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Solar_storm_of_2012\" class=\"mw-redirect\" title=\"Solar storm of 2012\">Solar storm of 2012</a> was an unusually large <a href=\"https://wikipedia.org/wiki/Coronal_mass_ejection\" title=\"Coronal mass ejection\">coronal mass ejection</a> that was emitted by the <a href=\"https://wikipedia.org/wiki/Sun\" title=\"Sun\">Sun</a> which barely missed the <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> by nine days. If it hit, it would have caused up to US$2.6 trillion in damages to electrical equipment worldwide.", "links": [{"title": "Solar storm of 2012", "link": "https://wikipedia.org/wiki/Solar_storm_of_2012"}, {"title": "Coronal mass ejection", "link": "https://wikipedia.org/wiki/Coronal_mass_ejection"}, {"title": "Sun", "link": "https://wikipedia.org/wiki/Sun"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}]}, {"year": "2014", "text": "TransAsia Airways Flight 222 crashes in Xixi village near Huxi, Penghu, during approach to Penghu Airport. Forty-eight of the 58 people on board are killed and five more people on the ground are injured.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/TransAsia_Airways_Flight_222\" title=\"TransAsia Airways Flight 222\">TransAsia Airways Flight 222</a> crashes in Xixi village near <a href=\"https://wikipedia.org/wiki/Huxi,_Penghu\" title=\"Huxi, Penghu\">Huxi, Penghu</a>, during approach to <a href=\"https://wikipedia.org/wiki/Penghu_Airport\" title=\"Penghu Airport\">Penghu Airport</a>. Forty-eight of the 58 people on board are killed and five more people on the ground are injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TransAsia_Airways_Flight_222\" title=\"TransAsia Airways Flight 222\">TransAsia Airways Flight 222</a> crashes in Xixi village near <a href=\"https://wikipedia.org/wiki/Huxi,_Penghu\" title=\"Huxi, Penghu\">Huxi, Penghu</a>, during approach to <a href=\"https://wikipedia.org/wiki/Penghu_Airport\" title=\"Penghu Airport\">Penghu Airport</a>. Forty-eight of the 58 people on board are killed and five more people on the ground are injured.", "links": [{"title": "TransAsia Airways Flight 222", "link": "https://wikipedia.org/wiki/TransAsia_Airways_Flight_222"}, {"title": "Huxi, Penghu", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>"}, {"title": "Penghu Airport", "link": "https://wikipedia.org/wiki/Penghu_Airport"}]}, {"year": "2015", "text": "NASA announces discovery of Kepler-452b by the Kepler space telescope.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> announces discovery of <a href=\"https://wikipedia.org/wiki/Kepler-452b\" title=\"Kepler-452b\">Kepler-452b</a> by the <a href=\"https://wikipedia.org/wiki/Kepler_space_telescope\" title=\"Kepler space telescope\">Kepler space telescope</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> announces discovery of <a href=\"https://wikipedia.org/wiki/Kepler-452b\" title=\"Kepler-452b\">Kepler-452b</a> by the <a href=\"https://wikipedia.org/wiki/Kepler_space_telescope\" title=\"Kepler space telescope\">Kepler space telescope</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Kepler-452b", "link": "https://wikipedia.org/wiki/Kepler-452b"}, {"title": "Kepler space telescope", "link": "https://wikipedia.org/wiki/Kepler_space_telescope"}]}, {"year": "2018", "text": "A wildfire in East Attica kills at least 102 people. It’s the deadliest wildfire in the history of Greece.", "html": "2018 - A <a href=\"https://wikipedia.org/wiki/2018_Attica_wildfires\" title=\"2018 Attica wildfires\">wildfire in East Attica</a> kills at least 102 people. It’s the deadliest wildfire in the history of Greece.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2018_Attica_wildfires\" title=\"2018 Attica wildfires\">wildfire in East Attica</a> kills at least 102 people. It’s the deadliest wildfire in the history of Greece.", "links": [{"title": "2018 Attica wildfires", "link": "https://wikipedia.org/wiki/2018_Attica_wildfires"}]}], "Births": [{"year": "1301", "text": "<PERSON>, Duke of Austria (d. 1339)", "html": "1301 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (d. 1339)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (d. 1339)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria"}]}, {"year": "1339", "text": "<PERSON>, Duke of Anjou (d. 1384)", "html": "1339 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Anjou\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Anjou\"><PERSON>, Duke of Anjou</a> (d. 1384)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Anjou\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Anjou\"><PERSON>, Duke of Anjou</a> (d. 1384)", "links": [{"title": "<PERSON>, Duke of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Anjou"}]}, {"year": "1370", "text": "<PERSON> the Elder, humanist (d. 1444 or 1445)", "html": "1370 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, humanist (d. 1444 or 1445)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the <PERSON>\"><PERSON> the Elder</a>, humanist (d. 1444 or 1445)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>er<PERSON>_the_Elder"}]}, {"year": "1401", "text": "<PERSON>, Italian husband of <PERSON> (d. 1466)", "html": "1401 - <a href=\"https://wikipedia.org/wiki/<PERSON>_I_<PERSON>\" title=\"<PERSON> I Sforz<PERSON>\"><PERSON></a>, Italian husband of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1466)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> I <PERSON>rz<PERSON>\"><PERSON></a>, Italian husband of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1466)", "links": [{"title": "Francesco I Sforza", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1441", "text": "<PERSON><PERSON> of Joseon, King of Joseon (d. 1457)", "html": "1441 - <a href=\"https://wikipedia.org/wiki/Danjong_of_Joseon\" title=\"Dan<PERSON> of Joseon\"><PERSON><PERSON> of Joseon</a>, King of <a href=\"https://wikipedia.org/wiki/Joseon\" title=\"Joseon\">Joseon</a> (d. 1457)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Danjong_of_Joseon\" title=\"Danjong of Joseon\"><PERSON><PERSON> of Joseon</a>, King of <a href=\"https://wikipedia.org/wiki/Joseon\" title=\"Joseon\">Joseon</a> (d. 1457)", "links": [{"title": "<PERSON><PERSON> of Joseon", "link": "https://wikipedia.org/wiki/Danjong_of_Joseon"}, {"title": "Joseon", "link": "https://wikipedia.org/wiki/Joseon"}]}, {"year": "1503", "text": "<PERSON> of Bohemia and Hungary (d. 1547)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bohemia_and_Hungary\" title=\"<PERSON> of Bohemia and Hungary\"><PERSON> of Bohemia and Hungary</a> (d. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bohemia_and_Hungary\" title=\"<PERSON> of Bohemia and Hungary\"><PERSON> of Bohemia and Hungary</a> (d. 1547)", "links": [{"title": "Anne of Bohemia and Hungary", "link": "https://wikipedia.org/wiki/Anne_of_Bohemia_and_Hungary"}]}, {"year": "1614", "text": "Bonaven<PERSON> P<PERSON> the Elder, Flemish painter (d. 1652)", "html": "1614 - <a href=\"https://wikipedia.org/wiki/Bonaventura_Peeters_the_Elder\" title=\"Bonaventura P<PERSON><PERSON> the Elder\">Bonaventura P<PERSON><PERSON> the Elder</a>, Flemish painter (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bonaventura_Peeters_the_Elder\" title=\"Bonaventura Peeters the Elder\">Bonaventura P<PERSON>ters the Elder</a>, Flemish painter (d. 1652)", "links": [{"title": "Bonaventura Peeters the Elder", "link": "https://wikipedia.org/wiki/Bonaventura_Peeters_the_Elder"}]}, {"year": "1635", "text": "<PERSON>, New France garrison commander (d. 1660)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Or<PERSON>aux\" title=\"Adam <PERSON>d des Ormeaux\"><PERSON>aux</a>, New France garrison commander (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>aux\" title=\"Adam Dollard des Ormeaux\"><PERSON> Or<PERSON>aux</a>, New France garrison commander (d. 1660)", "links": [{"title": "<PERSON> des Ormeaux", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON> (d. 1721)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Clement XI\"><PERSON></a> (d. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Clement XI\"><PERSON></a> (d. 1721)", "links": [{"title": "Pope <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1705", "text": "<PERSON>, English historian and author (d. 1752)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 1752)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON><PERSON>, Portuguese philosopher and pedagogue (d. 1792)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Ant%C3%B3nio_Verney\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese philosopher and pedagogue (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Ant%C3%B3nio_Verney\" title=\"<PERSON><PERSON>erne<PERSON>\"><PERSON><PERSON></a>, Portuguese philosopher and pedagogue (d. 1792)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lu%C3%ADs_Ant%C3%B3nio_Verney"}]}, {"year": "1773", "text": "<PERSON>, Scottish general and politician, 6th Governor of New South Wales (d. 1860)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas <PERSON>\"><PERSON></a>, Scottish general and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_New_South_Wales\" title=\"Governor of New South Wales\">Governor of New South Wales</a> (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas <PERSON>\"><PERSON></a>, Scottish general and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_New_South_Wales\" title=\"Governor of New South Wales\">Governor of New South Wales</a> (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New South Wales", "link": "https://wikipedia.org/wiki/Governor_of_New_South_Wales"}]}, {"year": "1773", "text": "<PERSON>, Irish anatomist (d. 1841) ", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish anatomist (d. 1841) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish anatomist (d. 1841) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON><PERSON><PERSON>, French physicist and mathematician (d. 1812)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and mathematician (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and mathematician (d. 1812)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, German painter and illustrator (d. 1810)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (d. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, Swedish surgeon and composer (d. 1868)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish surgeon and composer (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish surgeon and composer (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, Mexican general and president (d. 1853)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/Manuel_<PERSON>%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and president (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manuel_<PERSON>%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and president (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manuel_Mar%C3%ADa_Lombardini"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian archbishop and missionary (d. 1894)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian archbishop and missionary (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian archbishop and missionary (d. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>%C3%A9"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON>, French violinist and conductor (d. 1910)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/%C3%89douard_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and conductor (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89douard_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and conductor (d. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89do<PERSON>_<PERSON>ne"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON>, Norwegian-Danish painter (d. 1909)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON><PERSON>in_Kr%C3%B8yer\" title=\"<PERSON><PERSON><PERSON> Se<PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Norwegian-Danish painter (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Kr%C3%B8yer\" title=\"<PERSON><PERSON><PERSON> Se<PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Norwegian-Danish painter (d. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON>ver<PERSON>_Kr%C3%B8yer"}]}, {"year": "1854", "text": "<PERSON>, English barrister, journalist, philosopher, men's rights advocate, socialist and historian (d. 1926)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English barrister, journalist, philosopher, men's rights advocate, socialist and historian (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English barrister, journalist, philosopher, men's rights advocate, socialist and historian (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON>, Indian lawyer and journalist (d. 1920)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Bal_Gangadhar_Tilak\" title=\"Bal Gangadhar Tilak\"><PERSON><PERSON></a>, Indian lawyer and journalist (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bal_Gangadhar_Tilak\" title=\"Bal Gangadhar Tilak\"><PERSON><PERSON></a>, Indian lawyer and journalist (d. 1920)", "links": [{"title": "<PERSON><PERSON>adhar Tilak", "link": "https://wikipedia.org/wiki/Bal_Gangadhar_Tilak"}]}, {"year": "1864", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino lawyer and politician, 1st Prime Minister of the Philippines (d. 1903)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Apolinario_Ma<PERSON>i\" title=\"Apolinar<PERSON>\">Apolinar<PERSON></a>, Filipino lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Philippines\" title=\"Prime Minister of the Philippines\">Prime Minister of the Philippines</a> (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apolinario_<PERSON>\" title=\"Apolinar<PERSON>\">Apolinar<PERSON></a>, Filipino lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Philippines\" title=\"Prime Minister of the Philippines\">Prime Minister of the Philippines</a> (d. 1903)", "links": [{"title": "Apoli<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Apolinario_<PERSON><PERSON>i"}, {"title": "Prime Minister of the Philippines", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Philippines"}]}, {"year": "1865", "text": "<PERSON>, English businessman and politician (d. 1934)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman and politician (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman and politician (d. 1934)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_(businessman)"}]}, {"year": "1866", "text": "<PERSON>, Italian composer and academic (d. 1950)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and academic (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> C<PERSON>\"><PERSON></a>, Italian composer and academic (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_<PERSON>a"}]}, {"year": "1878", "text": "<PERSON>, Canadian lawyer and politician, 5th Premier of Saskatchewan (d. 1946)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Saskatchewan\" title=\"Premier of Saskatchewan\">Premier of Saskatchewan</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Saskatchewan\" title=\"Premier of Saskatchewan\">Premier of Saskatchewan</a> (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Saskatchewan", "link": "https://wikipedia.org/wiki/Premier_of_Saskatchewan"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish general and politician, 5th Speaker of the Grand National Assembly of Turkey (d. 1948)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/K%C3%A2z%C4%B1m_Karabekir\" title=\"<PERSON><PERSON><PERSON><PERSON>m Karabekir\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish general and politician, 5th Speaker of the Grand National Assembly of Turkey (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A2z%C4%B1m_Karabekir\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Karabekir\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish general and politician, 5th Speaker of the Grand National Assembly of Turkey (d. 1948)", "links": [{"title": "Kâz<PERSON>m Karabekir", "link": "https://wikipedia.org/wiki/K%C3%A2z%C4%B1m_Karabekir"}]}, {"year": "1883", "text": "<PERSON>, 1st Viscount <PERSON>, French-English field marshal and politician, Lord Lieutenant of the County of London (d. 1963)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, French-English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_the_County_of_London\" title=\"Lord Lieutenant of the County of London\">Lord Lieutenant of the County of London</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, French-English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_the_County_of_London\" title=\"Lord Lieutenant of the County of London\">Lord Lieutenant of the County of London</a> (d. 1963)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Lord Lieutenant of the County of London", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_the_County_of_London"}]}, {"year": "1884", "text": "<PERSON>, Swiss-German actor (d. 1950)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-German actor (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-German actor (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Canadian financier and philanthropist (d. 1955)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian financier and philanthropist (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian financier and philanthropist (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Georgian-American businessman, created Prince <PERSON> perfume (d. 1935)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-American businessman, created <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Prince <PERSON> perfume</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-American businessman, created <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Prince <PERSON> perfume</a> (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Spanish historian and diplomat (d. 1978)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Salvador_de_Madariaga\" title=\"Salvador de Madariaga\"><PERSON> Madariaga</a>, Spanish historian and diplomat (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_de_Madariaga\" title=\"Salvador de Madariaga\"><PERSON> Madaria<PERSON></a>, Spanish historian and diplomat (d. 1978)", "links": [{"title": "Salvador de Madariaga", "link": "https://wikipedia.org/wiki/Salvador_de_Madariaga"}]}, {"year": "1886", "text": "<PERSON>, Swiss-German physicist and engineer (d. 1976)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-German physicist and engineer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-German physicist and engineer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American crime novelist and screenwriter (d. 1959)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American crime novelist and screenwriter (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American crime novelist and screenwriter (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American surgeon and civil rights activist (d. 1952)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and civil rights activist (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and civil rights activist (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Ethiopian emperor (d. 1975)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Se<PERSON>ie\" title=\"<PERSON>le Selassie\"><PERSON><PERSON></a>, Ethiopian emperor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Se<PERSON>ie\" title=\"<PERSON>le Selassie\"><PERSON><PERSON></a>, Ethiopian emperor (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English-American actor and television personality (d. 1975)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and television personality (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and television personality (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, American actress (d. 1989)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Mexican historian, economist (d. 1976)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADo_Villegas\" title=\"<PERSON>\"><PERSON></a>, Mexican historian, economist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADo_<PERSON>gas\" title=\"<PERSON>\"><PERSON></a>, Mexican historian, economist (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Daniel_Cos%C3%ADo_Villegas"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Swedish actor and singer (d. 1941)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor and singer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor and singer (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ben<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Canadian ice hockey player and coach (d. 1987)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Estonian wrestler (d. 1970)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian wrestler (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian wrestler (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Ukrainian-American economist, journalist, and author (d. 1977)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American economist, journalist, and author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American economist, journalist, and author (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, German lawyer and politician, 3rd President of West Germany (d. 1976)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">President of West Germany</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">President of West Germany</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of German presidents", "link": "https://wikipedia.org/wiki/List_of_German_presidents"}]}, {"year": "1900", "text": "<PERSON>, American author and journalist (d. 1993)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Canadian-American sergeant (d. 2010)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American sergeant (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American sergeant (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Danish folklore researcher and writer (d. 1957)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Inger_<PERSON>g<PERSON><PERSON>_<PERSON>\" title=\"Inger Margrethe <PERSON>\">Inger <PERSON><PERSON><PERSON></a>, Danish folklore researcher and writer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inger_<PERSON>g<PERSON>he_<PERSON>\" title=\"Inger Margrethe <PERSON>\">Inger <PERSON><PERSON><PERSON></a>, Danish folklore researcher and writer (d. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Inger_<PERSON><PERSON><PERSON><PERSON>_<PERSON>g"}]}, {"year": "1901", "text": "<PERSON>, American actor and singer (d. 1992)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Puerto Rican brothel owner and madam in barrio Maragüez, Ponce, Puerto Rico (d. 1974)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican brothel owner and madam in barrio Maragüez, Ponce, Puerto Rico (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican brothel owner and madam in barrio Maragüez, Ponce, Puerto Rico (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Austrian author and educator (d. 2013)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and educator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ner"}]}, {"year": "1906", "text": "<PERSON>, Croatian-Swiss chemist and academic, Nobel Prize laureate (d. 1998)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Prelog\"><PERSON></a>, Croatian-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Vladimir Prelog\"><PERSON></a>, Croatian-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Prelog"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1906", "text": "<PERSON>, Indian activist (d. 1931)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian activist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian activist (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author, critic, and academic (d. 2015)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author, critic, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author, critic, and academic (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English actor (d. 1979)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 1979)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1913", "text": "<PERSON>, English journalist and politician, Secretary of State for Employment (d. 2010)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Employment\" title=\"Secretary of State for Employment\">Secretary of State for Employment</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Employment\" title=\"Secretary of State for Employment\">Secretary of State for Employment</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Employment", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Employment"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Greek-American painter (d. 2010)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-American painter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-American painter (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American illustrator (d. 1971)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, German-American actress (d. 2004)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, German-American actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, German-American actress (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Australian ballerina and choreographer (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian ballerina and choreographer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian ballerina and choreographer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Dutch comedian and actor (d. 2005)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>qui<PERSON>\" title=\"<PERSON>squita\"><PERSON></a>, Dutch comedian and actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch comedian and actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actress (d. 2014)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, American baseball player and sportscaster (d. 1999)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ee We<PERSON> Reese\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player and sportscaster (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ee We<PERSON> Reese\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player and sportscaster (d. 1999)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor (d. 2007)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Calvert_DeForest"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Italian director and screenwriter (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Canadian WWII servicewoman and photographer (d. 2004)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian WWII servicewoman and photographer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian WWII servicewoman and photographer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Cuban-American baseball player (d. 1997)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Luis <PERSON>\"><PERSON></a>, Cuban-American baseball player (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Luis <PERSON>\"><PERSON></a>, Cuban-American baseball player (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Latvian-American linguist and academic (d. 2018)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Morris_Halle\" title=\"Morris Halle\"><PERSON></a>, Latvian-American linguist and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morris_Halle\" title=\"Morris Halle\"><PERSON></a>, Latvian-American linguist and academic (d. 2018)", "links": [{"title": "Morris Halle", "link": "https://wikipedia.org/wiki/Morris_Halle"}]}, {"year": "1924", "text": "<PERSON>, English-American screenwriter and author (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American screenwriter and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American screenwriter and author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Turkish wrestler (d. 2008)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Gazanfer_Bilge\" title=\"Gazanfer Bilge\"><PERSON>n<PERSON> Bilge</a>, Turkish wrestler (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gazanfer_Bilge\" title=\"Gazanfer Bilge\"><PERSON><PERSON><PERSON> Bilge</a>, Turkish wrestler (d. 2008)", "links": [{"title": "Gazanfer Bilge", "link": "https://wikipedia.org/wiki/Gazanfer_Bilge"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Bangladeshi politician, 1st Prime Minister of Bangladesh (d. 1975)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister of Bangladesh</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister of Bangladesh</a> (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Bangladesh", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Botswana politician, the former Vice-President of Botswana (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Botswana politician, the former <a href=\"https://wikipedia.org/wiki/Vice-President_of_Botswana\" title=\"Vice-President of Botswana\">Vice-President of Botswana</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Botswana politician, the former <a href=\"https://wikipedia.org/wiki/Vice-President_of_Botswana\" title=\"Vice-President of Botswana\">Vice-President of Botswana</a> (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}, {"title": "Vice-President of Botswana", "link": "https://wikipedia.org/wiki/Vice-President_of_Botswana"}]}, {"year": "1925", "text": "<PERSON>, French historian and author (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actress and singer (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gloria_DeHaven"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech journalist and author (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Ludv%C3%ADk_Vacul%C3%ADk\" title=\"Ludvík Vaculík\">Ludvík Vaculík</a>, Czech journalist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludv%C3%ADk_Vacul%C3%ADk\" title=\"Ludvík Vaculík\"><PERSON>dv<PERSON> Vaculík</a>, Czech journalist and author (d. 2015)", "links": [{"title": "Ludvík Vaculík", "link": "https://wikipedia.org/wiki/Ludv%C3%ADk_Vacul%C3%ADk"}]}, {"year": "1927", "text": "<PERSON><PERSON>, French director and screenwriter (d. 2006)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French director and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French director and screenwriter (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>rach"}]}, {"year": "1928", "text": "<PERSON>, American pianist and conductor (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and conductor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and conductor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American astronomer and academic (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Jr., American author and screenwriter (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author and screenwriter (d. 2004)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1929", "text": "<PERSON>, American drummer (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Danny Barcelona\"><PERSON></a>, American drummer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Danny_<PERSON>\" title=\"Danny Barcelona\"><PERSON></a>, American drummer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Nigerian journalist and politician, 5th Governor of Lagos State (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/Governor_of_Lagos_State\" title=\"Governor of Lagos State\">Governor of Lagos State</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/Governor_of_Lagos_State\" title=\"Governor of Lagos State\">Governor of Lagos State</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ef_<PERSON>"}, {"title": "Governor of Lagos State", "link": "https://wikipedia.org/wiki/Governor_of_Lagos_State"}]}, {"year": "1931", "text": "<PERSON>, Māori queen (d. 2006)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Te_<PERSON>ang<PERSON>ahu\" title=\"<PERSON> Atairangikaahu\"><PERSON></a>, Māori queen (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ang<PERSON>ahu\" title=\"Te Atairangikaahu\"><PERSON></a>, Māori queen (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>airang<PERSON>ahu"}]}, {"year": "1931", "text": "<PERSON>, Canadian director, screenwriter, and cinematographer (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, Canadian director, screenwriter, and cinematographer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, Canadian director, screenwriter, and cinematographer (d. 2023)", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_(filmmaker)"}]}, {"year": "1931", "text": "<PERSON>, Canadian author and screenwriter", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Austrian architect, designed the Austrian Cultural Forum (d. 2010)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian architect, designed the <a href=\"https://wikipedia.org/wiki/Austrian_Cultural_Forum_New_York\" title=\"Austrian Cultural Forum New York\">Austrian Cultural Forum</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian architect, designed the <a href=\"https://wikipedia.org/wiki/Austrian_Cultural_Forum_New_York\" title=\"Austrian Cultural Forum New York\">Austrian Cultural Forum</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Austrian Cultural Forum New York", "link": "https://wikipedia.org/wiki/Austrian_Cultural_Forum_New_York"}]}, {"year": "1933", "text": "<PERSON>, American actor, singer, and game show host (d. 1991)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and game show host (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and game show host (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American priest, psychologist, and talk show host (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, psychologist, and talk show host (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, psychologist, and talk show host (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Italian-English architect, designed the Millennium Dome and Lloyd's building (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English architect, designed the <a href=\"https://wikipedia.org/wiki/Millennium_Dome\" title=\"Millennium Dome\">Millennium Dome</a> and <a href=\"https://wikipedia.org/wiki/Lloyd%27s_building\" title=\"Lloyd's building\"><PERSON>'s building</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English architect, designed the <a href=\"https://wikipedia.org/wiki/Millennium_Dome\" title=\"Millennium Dome\">Millennium Dome</a> and <a href=\"https://wikipedia.org/wiki/Lloyd%27s_building\" title=\"Lloyd's building\"><PERSON>'s building</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Millennium Dome", "link": "https://wikipedia.org/wiki/Millennium_Dome"}, {"title": "Lloyd's building", "link": "https://wikipedia.org/wiki/Lloyd%27s_building"}]}, {"year": "1935", "text": "<PERSON>, American race car driver", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1936", "text": "<PERSON>, American baseball player and sportscaster (d. 1993)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Don_<PERSON>sdale"}]}, {"year": "1936", "text": "<PERSON>, American lawyer and jurist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American football player and engineer (d. 2006)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and engineer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and engineer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American porn actress and producer (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American porn actress and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American porn actress and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and actor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American murderer (d. 2007)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Australian actor and television host (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and television host (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Newton\"><PERSON></a>, Australian actor and television host (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, French author, poet, and journalist (d. 1978)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and journalist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and journalist (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American radio host (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I<PERSON>\"><PERSON></a>, American radio host (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American novelist (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist (d. 2023)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Italian economist and politician, Italian Minister of Finance (d. 2010)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>hi<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Economy_and_Finances_(Italy)\" class=\"mw-redirect\" title=\"Ministry of Economy and Finances (Italy)\">Italian Minister of Finance</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Economy_and_Finances_(Italy)\" class=\"mw-redirect\" title=\"Ministry of Economy and Finances (Italy)\">Italian Minister of Finance</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Padoa-Sc<PERSON>"}, {"title": "Ministry of Economy and Finances (Italy)", "link": "https://wikipedia.org/wiki/Ministry_of_Economy_and_Finances_(Italy)"}]}, {"year": "1941", "text": "<PERSON>, English historian and academic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, English historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, English historian and academic", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(historian)"}]}, {"year": "1941", "text": "<PERSON>, American race car driver (d. 1985)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Italian lawyer, judge, and politician, 12th President of Italy", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer, judge, and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer, judge, and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Australian journalist and politician, Lord Mayor of Brisbane", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian journalist and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Brisbane\" title=\"Lord Mayor of Brisbane\">Lord Mayor of Brisbane</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian journalist and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Brisbane\" title=\"Lord Mayor of Brisbane\">Lord Mayor of Brisbane</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Lord Mayor of Brisbane", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_Brisbane"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American businessman, co-founded American Axle (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/American_Axle\" title=\"American Axle\">American Axle</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/American_Axle\" title=\"American Axle\">American Axle</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "American Axle", "link": "https://wikipedia.org/wiki/American_Axle"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Greek philosopher and author (d. 1998)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek philosopher and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek philosopher and author (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American scientist (d. 2007)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2018)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Tony <PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American drummer (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Portuguese pianist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Jo%C3%A3o_Pi<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Jo%C3%A3o_Pi<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Jo%C3%A3o_Pires"}]}, {"year": "1945", "text": "<PERSON>, English composer and educator", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English footballer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English oboe player and composer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Oboe\" title=\"Oboe\">oboe</a> player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Oboe\" title=\"Oboe\">oboe</a> player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Oboe", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American poet, painter, and critic (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>rd\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American poet, painter, and critic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>rd\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American poet, painter, and critic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Ricard"}]}, {"year": "1947", "text": "<PERSON>, American journalist and author (d. 2018)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gardner_Dozo<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter, and actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David <PERSON>\"><PERSON></a>, English singer-songwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David <PERSON>\"><PERSON></a>, English singer-songwriter, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Swedish race car driver", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Torsten Palm\"><PERSON><PERSON></a>, Swedish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Torsten Palm\"><PERSON><PERSON></a>, Swedish race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Palm"}]}, {"year": "1947", "text": "<PERSON>, English historian, critic, and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)\" title=\"<PERSON> (critic)\"><PERSON></a>, English historian, critic, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)\" title=\"<PERSON> (critic)\"><PERSON></a>, English historian, critic, and academic", "links": [{"title": "<PERSON> (critic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(critic)"}]}, {"year": "1948", "text": "<PERSON>, Australian-English lawyer, judge, and politician, Solicitor General for England and Wales", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales\" title=\"Solicitor General for England and Wales\">Solicitor General for England and Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales\" title=\"Solicitor General for England and Wales\">Solicitor General for England and Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ross_<PERSON>"}, {"title": "Solicitor General for England and Wales", "link": "https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales"}]}, {"year": "1948", "text": "<PERSON>, Northern Irish educator and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_York_politician)\" title=\"<PERSON> (New York politician)\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(New_York_politician)\" title=\"<PERSON> (New York politician)\"><PERSON></a>, American politician", "links": [{"title": "<PERSON> (New York politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(New_York_politician)"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Polish general (d. 2013)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Targosz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish general (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Targosz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish general (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_Targosz"}]}, {"year": "1949", "text": "<PERSON>, South African cricketer and coach (d. 2015)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Polish-born Ukrainian Greek Catholic hierarch (d. 2024)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wi<PERSON>\" title=\"<PERSON><PERSON> Me<PERSON>wit\"><PERSON><PERSON></a>, Polish-born Ukrainian Greek Catholic hierarch (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wi<PERSON>\" title=\"<PERSON><PERSON>wi<PERSON>\"><PERSON><PERSON></a>, Polish-born Ukrainian Greek Catholic hierarch (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1950", "text": "<PERSON>, Romanian-born American lawyer and judge", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-born American lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-born American lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_musician)\" title=\"<PERSON> (Canadian musician)\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_musician)\" title=\"<PERSON> (Canadian musician)\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON> (Canadian musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Canadian_musician)"}]}, {"year": "1950", "text": "<PERSON>, Canadian guitarist and songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian cricketer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1952", "text": "<PERSON>, Australian cricketer and coach (d. 2008)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American ice hockey player and coach (d. 1995)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian drummer (d. 2008)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American jazz singer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jazz singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jazz singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English cricketer and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Malaysian politician, 6th Prime Minister of Malaysia", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>b_<PERSON>"}, {"title": "Prime Minister of Malaysia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malaysia"}]}, {"year": "1957", "text": "<PERSON>, English comedian, actress, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Brand\" title=\"Jo Brand\"><PERSON></a>, English comedian, actress, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Brand\" title=\"Jo Brand\"><PERSON></a>, English comedian, actress, and screenwriter", "links": [{"title": "Jo <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American basketball player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Dutch actor, director, producer, and screenwriter (d. 2004)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, Dutch actor, director, producer, and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, Dutch actor, director, producer, and screenwriter (d. 2004)", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(film_director)"}]}, {"year": "1957", "text": "<PERSON>, English TV presenter, Top Gear", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English TV presenter, <a href=\"https://wikipedia.org/wiki/Top_Gear_(1977_TV_series)\" title=\"Top Gear (1977 TV series)\">Top Gear</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English TV presenter, <a href=\"https://wikipedia.org/wiki/Top_Gear_(1977_TV_series)\" title=\"Top Gear (1977 TV series)\">Top Gear</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Top Gear (1977 TV series)", "link": "https://wikipedia.org/wiki/Top_Gear_(1977_TV_series)"}]}, {"year": "1958", "text": "<PERSON>, American golfer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Indonesian businessman and philanthropist, founded the Artha Graha Peduli Foundation", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Artha_Graha_Peduli_Foundation\" title=\"Artha Graha Peduli Foundation\">Artha Graha Peduli Foundation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Artha_Graha_Peduli_Foundation\" title=\"Artha Graha Peduli Foundation\">Artha Graha Peduli Foundation</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Artha Graha Peduli Foundation", "link": "https://wikipedia.org/wiki/Artha_Graha_Peduli_Foundation"}]}, {"year": "1959", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian rugby player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American soprano and educator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American wrestler", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian comedian and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Ducharme\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Du<PERSON>rme\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Du<PERSON><PERSON>e"}]}, {"year": "1961", "text": "<PERSON>, American pilot and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor and activist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Indian actor, model, television show host, and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, model, television show host, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, model, television show host, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, American actor, director, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian rugby league player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1962", "text": "<PERSON>, Canadian pianist and composer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vre\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vre\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vre"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian tennis player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1964", "text": "<PERSON>, German drummer and songwriter (d. 2016)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German drummer and songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German drummer and songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, English-American guitarist, songwriter, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/S<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, English-American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, English-American guitarist, songwriter, and producer", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/Slash_(musician)"}]}, {"year": "1967", "text": "<PERSON>, American actor, director, and producer (d. 2014)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American basketball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player and actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American model and actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American politician and minister", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and minister", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and minister", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, German author and playwright", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, 13th Prime Minister of Lithuania", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lithuania\" title=\"Prime Minister of Lithuania\">Prime Minister of Lithuania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lithuania\" title=\"Prime Minister of Lithuania\">Prime Minister of Lithuania</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saulius_Skvernelis"}, {"title": "Prime Minister of Lithuania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lithuania"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American rapper and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rate\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rate\" title=\"<PERSON><PERSON>rate\"><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_DeGrate"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and fiddler", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and fiddler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and fiddler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American journalist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Turkish journalist, lawyer, and politician, former Turkish Minister of Youth and Sports", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Suat_K%C4%B1l%C4%B1%C3%A7\" title=\"Suat Kılıç\"><PERSON><PERSON>ı<PERSON></a>, Turkish journalist, lawyer, and politician, former <a href=\"https://wikipedia.org/wiki/Ministry_of_Youth_and_Sports_(Turkey)\" title=\"Ministry of Youth and Sports (Turkey)\">Turkish Minister of Youth and Sports</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suat_K%C4%B1l%C4%B1%C3%A7\" title=\"Suat Kılıç\"><PERSON><PERSON></a>, Turkish journalist, lawyer, and politician, former <a href=\"https://wikipedia.org/wiki/Ministry_of_Youth_and_Sports_(Turkey)\" title=\"Ministry of Youth and Sports (Turkey)\">Turkish Minister of Youth and Sports</a>", "links": [{"title": "Suat Kılıç", "link": "https://wikipedia.org/wiki/Suat_K%C4%B1l%C4%B1%C3%A7"}, {"title": "Ministry of Youth and Sports (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_Youth_and_Sports_(Turkey)"}]}, {"year": "1972", "text": "<PERSON>, Barbadian cricketer and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actor, director, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Marlon_Wayans\" title=\"Marlon Wayans\"><PERSON><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marlon_Wayans\" title=\"Marlon Wayans\"><PERSON><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>lon Wayans", "link": "https://wikipedia.org/wiki/Marlon_Wayans"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American baseball player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Scottish singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(musician)"}]}, {"year": "1973", "text": "<PERSON>, American activist and former White House intern", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and former White House intern", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and former White House intern", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Indian singer-songwriter, producer, actor, and director", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer-songwriter, producer, actor, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer-songwriter, producer, actor, and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Italian rugby player and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American football player and coach (d. 2017)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American sprinter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Belgian cyclist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/R<PERSON>_Verbrugghe\" title=\"<PERSON><PERSON> Verbrugghe\"><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON>_Verbrugghe\" title=\"<PERSON><PERSON> Verbrugghe\"><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "Rik Verbrugghe", "link": "https://wikipedia.org/wiki/Rik_V<PERSON>brugghe"}]}, {"year": "1975", "text": "<PERSON>, Cornish politician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cornish_people\" title=\"Cornish people\">Cornish</a> politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cornish_people\" title=\"Cornish people\">Cornish</a> politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cornish people", "link": "https://wikipedia.org/wiki/Cornish_people"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Hungarian chess player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Judit_<PERSON>g%C3%A1r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ju<PERSON>_<PERSON>g%C3%A1r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian chess player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Judit_Polg%C3%A1r"}]}, {"year": "1977", "text": "<PERSON>, American ice hockey player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English badminton player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English badminton player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Ecuadorian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/N%C3%A9icer_Reasco\" title=\"Néicer Reasco\"><PERSON><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%A9icer_Reasco\" title=\"Néicer Reasco\">N<PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%A9icer_Reasco"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Northern Irish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1978)\" title=\"<PERSON> (footballer, born 1978)\"><PERSON></a>, Northern Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1978)\" title=\"<PERSON> (footballer, born 1978)\"><PERSON></a>, Northern Irish footballer", "links": [{"title": "<PERSON> (footballer, born 1978)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1978)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Singaporean singer-songwriter and pianist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean singer-songwriter and pianist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American novelist and short story writer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Mexican wrestler and promoter (d. 2015)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Jr.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, Mexican wrestler and promoter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Jr.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, Mexican wrestler and promoter (d. 2015)", "links": [{"title": "<PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Jr."}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s_Kyrgiakos\" title=\"<PERSON><PERSON><PERSON>s Kyrgiakos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>yrgiakos\" title=\"<PERSON><PERSON><PERSON>s Kyrgiakos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Soti<PERSON>s_<PERSON>yrgiakos"}]}, {"year": "1979", "text": "<PERSON>, Zimbabwean cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Brazilian race car driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_Sperafico"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, German sprinter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tschirch\" title=\"<PERSON><PERSON><PERSON>irch\"><PERSON><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tschirch\" title=\"<PERSON><PERSON><PERSON> Tschirch\"><PERSON><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>schirch"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1980", "text": "<PERSON>, American biblical scholar and social media personality", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biblical_scholar)\" title=\"<PERSON> (biblical scholar)\"><PERSON></a>, American biblical scholar and social media personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biblical_scholar)\" title=\"<PERSON> (biblical scholar)\"><PERSON></a>, American biblical scholar and social media personality", "links": [{"title": "<PERSON> (biblical scholar)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biblical_scholar)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American actor, director, producer, and screenwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1981", "text": "<PERSON>, Canadian singer-songwriter, drummer, and director", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, drummer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, drummer, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Kazakhstani decathlete", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Kazakhstani decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Kazakhstani decathlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Estonian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Finnish tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/%C3%96mer_<PERSON><PERSON><PERSON>_<PERSON>%C4%B1%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96mer_<PERSON><PERSON><PERSON>_<PERSON>%C4%B1%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>%C4%B1%C5%9F"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/G%C3%B6khan_%C3%9Cnal\" title=\"Gökhan Ünal\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6khan_%C3%9Cnal\" title=\"Gökhan Ünal\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "G<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6khan_%C3%9Cnal"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor, director, and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Austrian politician", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Australian actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Uruguayan footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English singer and guitarist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress, producer, and screenwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Mexican footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Land%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Land%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_%C3%81ngel_Land%C3%ADn"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Japanese voice actress and singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, French race car driver", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Russian long jumper", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(long_jumper)\" title=\"<PERSON><PERSON> (long jumper)\"><PERSON><PERSON></a>, Russian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(long_jumper)\" title=\"<PERSON><PERSON> (long jumper)\"><PERSON><PERSON></a>, Russian long jumper", "links": [{"title": "<PERSON><PERSON> (long jumper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(long_jumper)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alessio_<PERSON>rci"}]}, {"year": "1987", "text": "<PERSON>, Brazilian singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Serdar_Kurtulu%C5%9F"}]}, {"year": "1987", "text": "<PERSON>, Belgian politician", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1990", "text": "<PERSON>, Canadian figure skater", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON> (figure skater)", "link": "https://wikipedia.org/wiki/<PERSON>_(figure_skater)"}]}, {"year": "1991", "text": "<PERSON>, Australian gymnast", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Australian rugby league footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gs"}]}, {"year": "1996", "text": "<PERSON>, Norwegian heiress and equestrian", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian heiress and equestrian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian heiress and equestrian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Slovak YouTube personality", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak YouTube personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak YouTube personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Bahamian basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, British pornographic actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British pornographic actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British pornographic actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON>, French tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/S%C3%A9l%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9l%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9l%C3%A9na_<PERSON><PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Model and Influencer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Model and Influencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Model and Influencer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "955", "text": "<PERSON>, Chinese chancellor (b. 898)", "html": "955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ng\"><PERSON></a>, Chinese chancellor (b. 898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ng\"><PERSON></a>, Chinese chancellor (b. 898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ng"}]}, {"year": "997", "text": "<PERSON><PERSON> <PERSON>, Samanid emir (b. 963)", "html": "997 - <a href=\"https://wikipedia.org/wiki/Nuh_II\" title=\"Nuh II\"><PERSON>uh II</a>, <PERSON><PERSON><PERSON> emir (b. 963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuh_II\" title=\"Nuh II\"><PERSON>uh II</a>, <PERSON><PERSON><PERSON> emir (b. 963)", "links": [{"title": "Nuh II", "link": "https://wikipedia.org/wiki/Nuh_II"}]}, {"year": "1065", "text": "<PERSON><PERSON> of Bamberg, bishop of Bamberg (c. 1025/1030)", "html": "1065 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bamberg\" title=\"<PERSON><PERSON> of Bamberg\"><PERSON><PERSON> of Bamberg</a>, bishop of Bamberg (c. 1025/1030)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bamberg\" title=\"<PERSON><PERSON> of Bamberg\"><PERSON><PERSON> of Bamberg</a>, bishop of Bamberg (c. 1025/1030)", "links": [{"title": "<PERSON><PERSON> of Bamberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bamberg"}]}, {"year": "1100", "text": "<PERSON> of Grez, French nobleman, relative of <PERSON> of Bouillon", "html": "1100 - <a href=\"https://wikipedia.org/wiki/Warner_of_Grez\" title=\"<PERSON> of Grez\"><PERSON> of Grez</a>, French nobleman, relative of <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bouillon\" title=\"<PERSON> of Bouillon\"><PERSON> of Bouillon</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Warner_of_Grez\" title=\"<PERSON> of Grez\"><PERSON> of Grez</a>, French nobleman, relative of <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bouillon\" title=\"<PERSON> of Bouillon\"><PERSON> of Bouillon</a>", "links": [{"title": "Warner of Grez", "link": "https://wikipedia.org/wiki/Warner_of_Grez"}, {"title": "<PERSON> of Bouillon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bouillon"}]}, {"year": "1227", "text": "<PERSON><PERSON>, Chinese religious leader, founded the Dragon Gate Taoism (b. 1148)", "html": "1227 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese religious leader, founded the <a href=\"https://wikipedia.org/wiki/Dragon_Gate_Taoism\" title=\"Dragon Gate Taoism\">Dragon Gate Taoism</a> (b. 1148)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese religious leader, founded the <a href=\"https://wikipedia.org/wiki/Dragon_Gate_Taoism\" title=\"Dragon Gate Taoism\">Dragon Gate Taoism</a> (b. 1148)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Dragon Gate Taoism", "link": "https://wikipedia.org/wiki/Dragon_Gate_Taoism"}]}, {"year": "1298", "text": "<PERSON><PERSON>, Armenian king (b. c. 1271)", "html": "1298 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III,_King_of_Armenia\" class=\"mw-redirect\" title=\"<PERSON><PERSON> III, King of Armenia\"><PERSON><PERSON> III</a>, Armenian king (b. c. 1271)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III,_King_of_Armenia\" class=\"mw-redirect\" title=\"<PERSON><PERSON> III, King of Armenia\"><PERSON><PERSON> III</a>, Armenian king (b. c. 1271)", "links": [{"title": "<PERSON><PERSON>, King of Armenia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_King_of_Armenia"}]}, {"year": "1373", "text": "<PERSON> of Sweden, Swedish mystic and saint, founded the Bridgettine Order (b. 1303)", "html": "1373 - <a href=\"https://wikipedia.org/wiki/Bridget_of_Sweden\" title=\"Bridget of Sweden\"><PERSON> of Sweden</a>, Swedish mystic and saint, founded the <a href=\"https://wikipedia.org/wiki/Bridgettines\" title=\"Bridgettines\">Bridgettine Order</a> (b. 1303)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bridget_of_Sweden\" title=\"Bridget of Sweden\"><PERSON> of Sweden</a>, Swedish mystic and saint, founded the <a href=\"https://wikipedia.org/wiki/Bridgettines\" title=\"Bridgettines\">Bridgettine Order</a> (b. 1303)", "links": [{"title": "Bridget of Sweden", "link": "https://wikipedia.org/wiki/Bridget_of_Sweden"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>s"}]}, {"year": "1403", "text": "<PERSON>, 1st Earl of Worcester, English rebel (b. 1343)", "html": "1403 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Worcester\" title=\"<PERSON>, 1st Earl of Worcester\"><PERSON>, 1st Earl of Worcester</a>, English rebel (b. 1343)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Worcester\" title=\"<PERSON>, 1st Earl of Worcester\"><PERSON>, 1st Earl of Worcester</a>, English rebel (b. 1343)", "links": [{"title": "<PERSON>, 1st Earl of Worcester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Worcester"}]}, {"year": "1531", "text": "<PERSON>, French husband of <PERSON>", "html": "1531 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9z%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French husband of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9z%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French husband of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_de_Br%C3%A9z%C3%A9"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1536", "text": "<PERSON>, 1st Duke of Richmond and Somerset, English politician, Lord Lieutenant of Ireland (b. 1519)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Richmond_and_Somerset\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Richmond and Somerset\"><PERSON>, 1st Duke of Richmond and Somerset</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Richmond_and_Somerset\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Richmond and Somerset\"><PERSON>, 1st Duke of Richmond and Somerset</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1519)", "links": [{"title": "<PERSON>, 1st Duke of Richmond and Somerset", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Richmond_and_Somerset"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1562", "text": "<PERSON><PERSON><PERSON>, German knight and poet (b. 1480)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>n\"><PERSON><PERSON><PERSON> <PERSON></a>, German knight and poet (b. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON> Be<PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German knight and poet (b. 1480)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1584", "text": "<PERSON>, English printer (b. 1522)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>, English printer (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>, English printer (b. 1522)", "links": [{"title": "<PERSON> (printer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(printer)"}]}, {"year": "1596", "text": "<PERSON>, 1st Baron <PERSON> (b. 1526)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>don\"><PERSON>, 1st Baron <PERSON></a> (b. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron Hu<PERSON>don\"><PERSON>, 1st Baron <PERSON></a> (b. 1526)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1645", "text": "<PERSON>, Russian tsar (b. 1596)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON></a>, Russian tsar (b. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON></a>, Russian tsar (b. 1596)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1692", "text": "<PERSON>, French lawyer, philologist, and scholar (b. 1613)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nage\" title=\"<PERSON>\"><PERSON></a>, French lawyer, philologist, and scholar (b. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nage\" title=\"<PERSON>\"><PERSON></a>, French lawyer, philologist, and scholar (b. 1613)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gilles_M%C3%A9nage"}]}, {"year": "1727", "text": "<PERSON>, 1st Viscount <PERSON>, English politician, Lord Chancellor of Great Britain (b. 1661)", "html": "1727 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of Great Britain</a> (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of Great Britain</a> (b. 1661)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1757", "text": "<PERSON>, Italian harpsichord player and composer (b. 1685)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and composer (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and composer (b. 1685)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1773", "text": "<PERSON>, English biologist and ornithologist (b. 1693)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(naturalist)\" title=\"<PERSON> (naturalist)\"><PERSON></a>, English biologist and ornithologist (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(naturalist)\" title=\"<PERSON> (naturalist)\"><PERSON></a>, English biologist and ornithologist (b. 1693)", "links": [{"title": "<PERSON> (naturalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(naturalist)"}]}, {"year": "1781", "text": "<PERSON>, Swiss-American pastor and politician (b. 1724)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American pastor and politician (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American pastor and politician (b. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, American lawyer and politician (b. 1721)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON><PERSON><PERSON>, Chilean politician, Chilean Minister of Finance (b. 1777)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_la_Cruz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Chile)\" title=\"Ministry of Finance (Chile)\">Chilean Minister of Finance</a> (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_la_Cruz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Cruz\"><PERSON><PERSON><PERSON></a>, Chilean politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Chile)\" title=\"Ministry of Finance (Chile)\">Chilean Minister of Finance</a> (b. 1777)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Ministry of Finance (Chile)", "link": "https://wikipedia.org/wiki/Ministry_of_Finance_(Chile)"}]}, {"year": "1853", "text": "<PERSON><PERSON>, South African general (b. 1798)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/And<PERSON>_Pretorius\" title=\"<PERSON><PERSON> Pretorius\"><PERSON><PERSON></a>, South African general (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pretorius\" title=\"<PERSON><PERSON> Pretorius\"><PERSON><PERSON></a>, South African general (b. 1798)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/And<PERSON>_Pretorius"}]}, {"year": "1875", "text": "<PERSON>, American businessman, founded the Singer Corporation (b. 1811)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Singer_Corporation\" title=\"Singer Corporation\">Singer Corporation</a> (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Singer\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Singer_Corporation\" title=\"Singer Corporation\">Singer Corporation</a> (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Singer Corporation", "link": "https://wikipedia.org/wiki/Singer_Corporation"}]}, {"year": "1878", "text": "<PERSON>, Bohemian physician, pathologist, and politician (b. 1804)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bohemian physician, pathologist, and politician (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bohemian physician, pathologist, and politician (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American general and politician, 18th President of the United States (b. 1822)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 18th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 18th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1904", "text": "<PERSON>, English-Australian politician, 7th Premier of Queensland (b. 1828)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Queensland_politician)\" title=\"<PERSON> (Queensland politician)\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Queensland_politician)\" title=\"<PERSON> (Queensland politician)\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1828)", "links": [{"title": "<PERSON> (Queensland politician)", "link": "https://wikipedia.org/wiki/<PERSON>(Queensland_politician)"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1909", "text": "<PERSON>, Australian politician, 19th Premier of South Australia (b. 1850)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1916", "text": "<PERSON>, Scottish chemist and academic, Nobel Prize laureate (b. 1852)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Greek historian and politician, 100th Prime Minister of Greece (b. 1851)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lamb<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek historian and politician, 100th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek historian and politician, 100th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1851)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Spyridon_Lambros"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1920", "text": "<PERSON>, German-American rancher and politician (b. 1835)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American rancher and politician (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American rancher and politician (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American author and scholar (b. 1850)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and scholar (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and scholar (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Russian painter (b. 1848)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, British brigadier general (b. 1864)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British brigadier general (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British brigadier general (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American pilot and engineer (b. 1878)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and engineer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and engineer (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Welsh runner (b. 1884)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh runner (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh runner (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Russian linguist (b. 1882)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian linguist (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian linguist (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American scholar and educator (b. 1860)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and educator (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and educator (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Peruvian soldier and pilot (b. 1914)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Qui%C3%B1ones_Gonzales\" title=\"<PERSON>\"><PERSON></a>, Peruvian soldier and pilot (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Qui%C3%B1<PERSON>_Gonzales\" title=\"<PERSON>\"><PERSON></a>, Peruvian soldier and pilot (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Qui%C3%B1<PERSON>_Gonzales"}]}, {"year": "1942", "text": "<PERSON>, Polish engineer and politician (b. 1880)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3w\" title=\"<PERSON>\"><PERSON></a>, Polish engineer and politician (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3w\" title=\"<PERSON>\"><PERSON></a>, Polish engineer and politician (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adam_Czerniak%C3%B3w"}]}, {"year": "1942", "text": "<PERSON>, English cricketer and footballer (b. 1886)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor, director, producer, and screenwriter (b. 1875)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor, director, producer, and screenwriter (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor, director, producer, and screenwriter (b. 1875)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Japanese politician and diplomat, Japanese Minister of Foreign Affairs (b. 1882)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Shigenori_T%C5%8Dg%C5%8D\" title=\"Shi<PERSON><PERSON> Tōgō\"><PERSON><PERSON><PERSON></a>, Japanese politician and diplomat, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Japan)\" title=\"Ministry of Foreign Affairs (Japan)\">Japanese Minister of Foreign Affairs</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>genori_T%C5%8Dg%C5%8D\" title=\"Shigen<PERSON> Tōgō\"><PERSON><PERSON><PERSON></a>, Japanese politician and diplomat, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Japan)\" title=\"Ministry of Foreign Affairs (Japan)\">Japanese Minister of Foreign Affairs</a> (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shigenori_T%C5%8Dg%C5%8D"}, {"title": "Ministry of Foreign Affairs (Japan)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Japan)"}]}, {"year": "1951", "text": "<PERSON>, American director and producer (b. 1884)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, French general and politician, 119th Prime Minister of France (b. 1856)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tain\" title=\"<PERSON>\"><PERSON></a>, French general and politician, 119th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tain\" title=\"<PERSON>\"><PERSON></a>, French general and politician, 119th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Philippe_P%C3%A9tain"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1954", "text": "<PERSON>, American runner (b. 1882)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, American captain, lawyer, and politician, 47th United States Secretary of State, Nobel Prize laureate (b. 1871)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ll <PERSON>\"><PERSON><PERSON><PERSON></a>, American captain, lawyer, and politician, 47th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American captain, lawyer, and politician, 47th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ll_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1957", "text": "<PERSON>, American football player and coach (b. 1870)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor (b. 1920)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lift\" title=\"Montgomery Clift\"><PERSON></a>, American actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lift\" title=\"Montgomery Clift\"><PERSON></a>, American actor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Montgomery_<PERSON>lift"}]}, {"year": "1968", "text": "<PERSON>, English pharmacologist and physiologist, Nobel Prize laureate (b. 1875)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pharmacologist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pharmacologist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Finnish politician (b. 1905)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor (b. 1910)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American geologist and paleontologist (b. 1895)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and paleontologist (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and paleontologist (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American pilot and race car driver, founded Rickenbacker Motors (b. 1890)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and race car driver, founded <a href=\"https://wikipedia.org/wiki/<PERSON>enbacker_(car)\" title=\"<PERSON><PERSON>backer (car)\"><PERSON><PERSON><PERSON>er Motors</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and race car driver, founded <a href=\"https://wikipedia.org/wiki/Rickenbacker_(car)\" title=\"<PERSON><PERSON>backer (car)\"><PERSON><PERSON><PERSON>er Motors</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (car)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(car)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Turkish industrialist (b. 1912)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON>l_Tolon\" title=\"Ka<PERSON><PERSON> Tolon\"><PERSON><PERSON><PERSON></a>, Turkish industrialist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kamil_Tolon\" title=\"Ka<PERSON><PERSON> Tolon\"><PERSON><PERSON><PERSON></a>, Turkish industrialist (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lon"}]}, {"year": "1979", "text": "<PERSON>, French journalist and author (b. 1898)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 38th Mayor of Montreal (b. 1908)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sart<PERSON>_<PERSON>"}, {"title": "Mayor of Montreal", "link": "https://wikipedia.org/wiki/Mayor_of_Montreal"}]}, {"year": "1980", "text": "<PERSON>, American keyboard player and songwriter (b. 1948)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Ukrainian activist (b. 1897)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian activist (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian activist (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor (b. 1929)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, French composer (b. 1899)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English cricketer and manager (b. 1923)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and manager (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and manager (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American short story writer and novelist (b. 1931)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and novelist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and novelist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Japanese engineer (b. 1899)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese engineer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese engineer (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actress (b. 1911)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1911)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese jumper and journalist (b. 1904)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Ch%C5%ABhe<PERSON>_Nambu\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese jumper and journalist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ch%C5%ABhei_Nambu\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese jumper and journalist (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ch%C5%ABhei_Nambu"}]}, {"year": "1999", "text": "<PERSON> of Morocco (b. 1929)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Hassan_II_of_Morocco\" title=\"<PERSON> II of Morocco\"><PERSON> of Morocco</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Morocco\" title=\"<PERSON> II of Morocco\"><PERSON> of Morocco</a> (b. 1929)", "links": [{"title": "<PERSON> II of Morocco", "link": "https://wikipedia.org/wiki/Hassan_II_of_Morocco"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, American novelist and short story writer (b. 1909)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lt<PERSON>\" title=\"<PERSON><PERSON><PERSON> Welty\"><PERSON><PERSON><PERSON></a>, American novelist and short story writer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>lty\"><PERSON><PERSON><PERSON></a>, American novelist and short story writer (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>y"}]}, {"year": "2002", "text": "<PERSON>, Australian-English actor (b. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English actor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American activist and author (b. 1933)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American novelist and rabbi (b. 1929)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist and rabbi (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist and rabbi (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American author and composer (b. 1938)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and composer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and composer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American police officer and politician (b. 1962)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(New_York_politician)\" title=\"<PERSON> (New York politician)\"><PERSON></a>, American police officer and politician (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(New_York_politician)\" title=\"<PERSON> (New York politician)\"><PERSON></a>, American police officer and politician (b. 1962)", "links": [{"title": "<PERSON> (New York politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(New_York_politician)"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Indian actor, director, and producer (b. 1932)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, director, and producer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, director, and producer (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Portuguese guitarist and composer (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese guitarist and composer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese guitarist and composer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Italian pianist, conductor, and composer (b. 1921)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist, conductor, and composer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist, conductor, and composer (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American guitarist and journalist (b. 1946)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and journalist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and journalist (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Canadian journalist and academic (b. 1927)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and academic (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American songwriter and producer (b. 1933)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American songwriter and producer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American songwriter and producer (b. 1933)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)"}]}, {"year": "2007", "text": "<PERSON>, Afghan king (b. 1914)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Afghan king (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Afghan king (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Swiss lawyer and politician, 70th President of the Swiss Confederation (b. 1924)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 70th <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 70th <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American author and screenwriter (b. 1955)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American author and screenwriter (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American author and screenwriter (b. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American journalist and author (b. 1916)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English singer-songwriter (b. 1983)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Amy Winehouse\"><PERSON></a>, English singer-songwriter (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Amy Winehouse\"><PERSON></a>, English singer-songwriter (b. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, New Zealand author (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American physicist and astronaut (b. 1951)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Sally_Ride\" title=\"Sally Ride\"><PERSON></a>, American physicist and astronaut (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sally_Ride\" title=\"Sally Ride\"><PERSON></a>, American physicist and astronaut (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Indian soldier and politician (b. 1914)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian soldier and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian soldier and politician (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Spanish publisher and author (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish publisher and author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish publisher and author (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Spanish television host and director (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish television host and director (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish television host and director (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Scottish actress (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish actress (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English author (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American diplomat (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian singer-songwriter and accordion player (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Dominguinhos\" title=\"Dominguinhos\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter and accordion player (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dominguinhos\" title=\"Dominguinhos\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter and accordion player (b. 1941)", "links": [{"title": "Dominguinhos", "link": "https://wikipedia.org/wiki/Dominguinhos"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American boxer and trainer (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer and trainer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer and trainer (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, South Korean director and producer (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean director and producer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean director and producer (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>k"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English actress and restaurateur (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and restaurateur (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and restaurateur (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American composer and conductor (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Brazilian author and playwright (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ariano_Suassuna\" title=\"Ariano Suassuna\"><PERSON><PERSON></a>, Brazilian author and playwright (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ariano_Suassuna\" title=\"Ariano Suassuna\"><PERSON><PERSON></a>, Brazilian author and playwright (b. 1927)", "links": [{"title": "Ariano <PERSON>", "link": "https://wikipedia.org/wiki/Ariano_Suassuna"}]}, {"year": "2014", "text": "<PERSON>, English footballer (b. 1990)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Jordan_Tabor\" title=\"Jordan Tabor\"><PERSON></a>, English footballer (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Tabor\" title=\"Jordan Tabor\"><PERSON></a>, English footballer (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Tabor"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Japanese-American sculptor and director (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American sculptor and director (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American sculptor and director (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American journalist, author, and academic (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and academic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American cardinal (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American basketball coach (b. 1916)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball coach (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball coach (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Burmese politician and rapper (b. 1981)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>haw\"><PERSON><PERSON><PERSON></a>, Burmese politician and rapper (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>haw\"><PERSON><PERSON><PERSON></a>, Burmese politician and rapper (b. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>haw"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Burmese political activist (b. 1969)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>ya<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>yaw <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Burmese political activist (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ya<PERSON>_<PERSON>_<PERSON>\" title=\"Kyaw <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Burmese political activist (b. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ya<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian pathologist and academic, Nobel Prize laureate (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pathologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pathologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}]}}