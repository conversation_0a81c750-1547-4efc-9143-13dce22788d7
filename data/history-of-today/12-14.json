{"date": "December 14", "url": "https://wikipedia.org/wiki/December_14", "data": {"Events": [{"year": "557", "text": "Constantinople is severely damaged by an earthquake, which cracks the dome of Hagia Sophia.", "html": "557 - <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> is severely damaged by <a href=\"https://wikipedia.org/wiki/557_Constantinople_earthquake\" title=\"557 Constantinople earthquake\">an earthquake</a>, which cracks the dome of <a href=\"https://wikipedia.org/wiki/Hagia_Sophia\" title=\"Hagia Sophia\">Hagia Sophia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> is severely damaged by <a href=\"https://wikipedia.org/wiki/557_Constantinople_earthquake\" title=\"557 Constantinople earthquake\">an earthquake</a>, which cracks the dome of <a href=\"https://wikipedia.org/wiki/Hagia_Sophia\" title=\"Hagia Sophia\">Hagia Sophia</a>.", "links": [{"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "557 Constantinople earthquake", "link": "https://wikipedia.org/wiki/557_Constantinople_earthquake"}, {"title": "Hagia Sophia", "link": "https://wikipedia.org/wiki/Hagia_Sophia"}]}, {"year": "835", "text": "Sweet Dew Incident: Emperor <PERSON><PERSON> of the Tang dynasty conspires to kill the powerful eunuchs of the Tang court, but the plot is foiled.", "html": "835 - <a href=\"https://wikipedia.org/wiki/Sweet_Dew_Incident\" class=\"mw-redirect\" title=\"Sweet Dew Incident\">Sweet Dew Incident</a>: <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON> of Tang\">Emperor <PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> conspires to kill the powerful <a href=\"https://wikipedia.org/wiki/Eunuch\" title=\"Eunuch\">eunuchs</a> of the Tang court, but the plot is foiled.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sweet_Dew_Incident\" class=\"mw-redirect\" title=\"Sweet Dew Incident\">Sweet Dew Incident</a>: <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON> of Tang\">Emperor <PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> conspires to kill the powerful <a href=\"https://wikipedia.org/wiki/Eunuch\" title=\"Eunuch\">eunuchs</a> of the Tang court, but the plot is foiled.", "links": [{"title": "Sweet Dew Incident", "link": "https://wikipedia.org/wiki/Sweet_Dew_Incident"}, {"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}, {"title": "Eunuch", "link": "https://wikipedia.org/wiki/Eunuch"}]}, {"year": "1287", "text": "St. Lucia's flood: The Zuiderzee sea wall in the Netherlands collapses, killing over 50,000 people.", "html": "1287 - <a href=\"https://wikipedia.org/wiki/St._Lucia%27s_flood\" title=\"St. Lucia's flood\">St. Lucia's flood</a>: The <a href=\"https://wikipedia.org/wiki/Zuiderzee\" title=\"Zuiderzee\">Zuiderzee</a> sea wall in the Netherlands collapses, killing over 50,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St._Lucia%27s_flood\" title=\"St. Lucia's flood\">St. Lucia's flood</a>: The <a href=\"https://wikipedia.org/wiki/Zuiderzee\" title=\"Zuiderzee\">Zuiderzee</a> sea wall in the Netherlands collapses, killing over 50,000 people.", "links": [{"title": "St. Lucia's flood", "link": "https://wikipedia.org/wiki/St._Lucia%27s_flood"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zuiderzee"}]}, {"year": "1542", "text": "Princess <PERSON> becomes Queen of Scots at the age of one week on the death of her father, <PERSON> of Scotland.", "html": "1542 - Princess <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Queen_of_Scots\" class=\"mw-redirect\" title=\"Queen of Scots\">Queen of Scots</a> at the age of one week on the death of her father, <a href=\"https://wikipedia.org/wiki/James_V_of_Scotland\" class=\"mw-redirect\" title=\"James V of Scotland\"><PERSON> of Scotland</a>.", "no_year_html": "Princess <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Queen_of_Scots\" class=\"mw-redirect\" title=\"Queen of Scots\">Queen of Scots</a> at the age of one week on the death of her father, <a href=\"https://wikipedia.org/wiki/James_V_of_Scotland\" class=\"mw-redirect\" title=\"James V of Scotland\"><PERSON> V of Scotland</a>.", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "Queen of Scots", "link": "https://wikipedia.org/wiki/Queen_of_Scots"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/James_V_of_Scotland"}]}, {"year": "1751", "text": "The Theresian Military Academy is founded in Wiener Neustadt, Austria.", "html": "1751 - The <a href=\"https://wikipedia.org/wiki/Theresian_Military_Academy\" title=\"Theresian Military Academy\">Theresian Military Academy</a> is founded in <a href=\"https://wikipedia.org/wiki/Wiener_Neustadt\" title=\"Wiener Neustadt\">Wiener Neustadt</a>, Austria.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Theresian_Military_Academy\" title=\"Theresian Military Academy\">Theresian Military Academy</a> is founded in <a href=\"https://wikipedia.org/wiki/Wiener_Neustadt\" title=\"Wiener Neustadt\">Wiener Neustadt</a>, Austria.", "links": [{"title": "Theresian Military Academy", "link": "https://wikipedia.org/wiki/Theresian_Military_Academy"}, {"title": "Wiener Neustadt", "link": "https://wikipedia.org/wiki/Wiener_Neustadt"}]}, {"year": "1780", "text": "Founding Father <PERSON> marries <PERSON> at the <PERSON><PERSON><PERSON> Mansion in Albany, New York.", "html": "1780 - <a href=\"https://wikipedia.org/wiki/Founding_Fathers_of_the_United_States\" title=\"Founding Fathers of the United States\">Founding Father</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Schuyler_Mansion\" title=\"Schuyler Mansion\">Schuyler Mansion</a> in <a href=\"https://wikipedia.org/wiki/Albany,_New_York\" title=\"Albany, New York\">Albany, New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Founding_Fathers_of_the_United_States\" title=\"Founding Fathers of the United States\">Founding Father</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Sc<PERSON>yler_Mansion\" title=\"Schuyler Mansion\">Schuyler Mansion</a> in <a href=\"https://wikipedia.org/wiki/Albany,_New_York\" title=\"Albany, New York\">Albany, New York</a>.", "links": [{"title": "Founding Fathers of the United States", "link": "https://wikipedia.org/wiki/Founding_Fathers_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sc<PERSON>yler Mansion", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Albany, New York", "link": "https://wikipedia.org/wiki/Albany,_New_York"}]}, {"year": "1782", "text": "The Montgolfier brothers first test fly an unmanned hot air balloon in France; it floats nearly 2.5 km (1.6 mi).", "html": "1782 - The <a href=\"https://wikipedia.org/wiki/Montgolfier_brothers\" title=\"Montgolfier brothers\">Montgolfier brothers</a> first test fly an unmanned <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> in France; it floats nearly 2.5 km (1.6 mi).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Montgolfier_brothers\" title=\"Montgolfier brothers\">Montgolfier brothers</a> first test fly an unmanned <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> in France; it floats nearly 2.5 km (1.6 mi).", "links": [{"title": "Montgolfier brothers", "link": "https://wikipedia.org/wiki/Montgol<PERSON>r_brothers"}, {"title": "Hot air balloon", "link": "https://wikipedia.org/wiki/Hot_air_balloon"}]}, {"year": "1812", "text": "The French invasion of Russia comes to an end as the remnants of the Grande Armée are expelled from Russia.", "html": "1812 - The <a href=\"https://wikipedia.org/wiki/French_invasion_of_Russia\" title=\"French invasion of Russia\">French invasion of Russia</a> comes to an end as the remnants of the <a href=\"https://wikipedia.org/wiki/Grande_Arm%C3%A9e\" title=\"Grande Armée\">Grande Armée</a> are expelled from Russia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_invasion_of_Russia\" title=\"French invasion of Russia\">French invasion of Russia</a> comes to an end as the remnants of the <a href=\"https://wikipedia.org/wiki/Grande_Arm%C3%A9e\" title=\"Grande Armée\">Grande Armée</a> are expelled from Russia.", "links": [{"title": "French invasion of Russia", "link": "https://wikipedia.org/wiki/French_invasion_of_Russia"}, {"title": "Grande Armée", "link": "https://wikipedia.org/wiki/Grande_Arm%C3%A9e"}]}, {"year": "1814", "text": "War of 1812: The Royal Navy seizes control of Lake Borgne, Louisiana.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Lake_Borgne\" title=\"Battle of Lake Borgne\">seizes control</a> of <a href=\"https://wikipedia.org/wiki/Lake_Borgne\" title=\"Lake Borgne\">Lake Borgne</a>, <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Lake_Borgne\" title=\"Battle of Lake Borgne\">seizes control</a> of <a href=\"https://wikipedia.org/wiki/Lake_Borgne\" title=\"Lake Borgne\">Lake Borgne</a>, <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Battle of Lake Borgne", "link": "https://wikipedia.org/wiki/Battle_of_Lake_Borgne"}, {"title": "Lake Borgne", "link": "https://wikipedia.org/wiki/Lake_Borgne"}, {"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}]}, {"year": "1819", "text": "Alabama becomes the 22nd U.S. state.", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> becomes the 22nd <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> becomes the 22nd <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1836", "text": "The Toledo War unofficially ends as the \"Frostbitten Convention\" votes to accept Congress' terms for admitting Michigan as a U.S. state.", "html": "1836 - The <a href=\"https://wikipedia.org/wiki/Toledo_War\" title=\"Toledo War\">Toledo War</a> unofficially ends as the \"Frostbitten Convention\" votes to accept Congress' terms for admitting <a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a> as a U.S. state.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Toledo_War\" title=\"Toledo War\">Toledo War</a> unofficially ends as the \"Frostbitten Convention\" votes to accept Congress' terms for admitting <a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a> as a U.S. state.", "links": [{"title": "Toledo War", "link": "https://wikipedia.org/wiki/Toledo_War"}, {"title": "Michigan", "link": "https://wikipedia.org/wiki/Michigan"}]}, {"year": "1863", "text": "American Civil War: The Confederate victory under General <PERSON> at the Battle of Bean's Station in East Tennessee ends the Knoxville Campaign, but achieves very little as <PERSON><PERSON><PERSON> returns to Virginia next spring.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> victory under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Bean%27s_Station\" title=\"Battle of Bean's Station\">Battle of Bean's Station</a> in <a href=\"https://wikipedia.org/wiki/East_Tennessee\" title=\"East Tennessee\">East Tennessee</a> ends the <a href=\"https://wikipedia.org/wiki/Knoxville_Campaign\" class=\"mw-redirect\" title=\"Knoxville Campaign\">Knoxville Campaign</a>, but achieves very little as <PERSON><PERSON><PERSON> returns to <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> next spring.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> victory under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Bean%27s_Station\" title=\"Battle of Bean's Station\">Battle of Bean's Station</a> in <a href=\"https://wikipedia.org/wiki/East_Tennessee\" title=\"East Tennessee\">East Tennessee</a> ends the <a href=\"https://wikipedia.org/wiki/Knoxville_Campaign\" class=\"mw-redirect\" title=\"Knoxville Campaign\">Knoxville Campaign</a>, but achieves very little as <PERSON><PERSON><PERSON> returns to <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> next spring.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Bean's Station", "link": "https://wikipedia.org/wiki/Battle_of_Bean%27s_Station"}, {"title": "East Tennessee", "link": "https://wikipedia.org/wiki/East_Tennessee"}, {"title": "Knoxville Campaign", "link": "https://wikipedia.org/wiki/Knoxville_Campaign"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}]}, {"year": "1896", "text": "The Glasgow Underground Railway is opened by the Glasgow District Subway Company.", "html": "1896 - The <a href=\"https://wikipedia.org/wiki/Glasgow_Subway\" title=\"Glasgow Subway\">Glasgow Underground Railway</a> is opened by the Glasgow District Subway Company.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Glasgow_Subway\" title=\"Glasgow Subway\">Glasgow Underground Railway</a> is opened by the Glasgow District Subway Company.", "links": [{"title": "Glasgow Subway", "link": "https://wikipedia.org/wiki/Glasgow_Subway"}]}, {"year": "1900", "text": "Quantum mechanics: <PERSON> presents a theoretical derivation of his black-body radiation law (quantum theory) at the Physic Society in Berlin.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Quantum_mechanics\" title=\"Quantum mechanics\">Quantum mechanics</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents a theoretical derivation of his <a href=\"https://wikipedia.org/wiki/Planck%27s_law\" title=\"<PERSON><PERSON>'s law\">black-body radiation law (quantum theory)</a> at the Physic Society in Berlin.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quantum_mechanics\" title=\"Quantum mechanics\">Quantum mechanics</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents a theoretical derivation of his <a href=\"https://wikipedia.org/wiki/Planck%27s_law\" title=\"<PERSON><PERSON>'s law\">black-body radiation law (quantum theory)</a> at the Physic Society in Berlin.", "links": [{"title": "Quantum mechanics", "link": "https://wikipedia.org/wiki/Quantum_mechanics"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>'s law", "link": "https://wikipedia.org/wiki/Planck%27s_law"}]}, {"year": "1902", "text": "The Commercial Pacific Cable Company lays the first Pacific telegraph cable, from San Francisco to Honolulu.", "html": "1902 - The <a href=\"https://wikipedia.org/wiki/Commercial_Pacific_Cable_Company\" title=\"Commercial Pacific Cable Company\">Commercial Pacific Cable Company</a> lays the first Pacific <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegraph</a> cable, from <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> to <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Commercial_Pacific_Cable_Company\" title=\"Commercial Pacific Cable Company\">Commercial Pacific Cable Company</a> lays the first Pacific <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegraph</a> cable, from <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> to <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu</a>.", "links": [{"title": "Commercial Pacific Cable Company", "link": "https://wikipedia.org/wiki/Commercial_Pacific_Cable_Company"}, {"title": "Telegraphy", "link": "https://wikipedia.org/wiki/Telegraphy"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}, {"title": "Honolulu", "link": "https://wikipedia.org/wiki/Honolulu"}]}, {"year": "1903", "text": "The <PERSON> brothers make their first attempt to fly with the Wright Flyer at Kitty Hawk, North Carolina.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\"><PERSON> brothers</a> make their first attempt to fly with the <i><a href=\"https://wikipedia.org/wiki/<PERSON>_Flyer\" title=\"Wright Flyer\"><PERSON> Flyer</a></i> at <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_North_Carolina\" title=\"Kitty Hawk, North Carolina\">Kitty Hawk, North Carolina</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\"><PERSON> brothers</a> make their first attempt to fly with the <i><a href=\"https://wikipedia.org/wiki/<PERSON>_Flyer\" title=\"Wright Flyer\"><PERSON> Flyer</a></i> at <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_North_Carolina\" title=\"Kitty Hawk, North Carolina\">Kitty Hawk, North Carolina</a>.", "links": [{"title": "<PERSON> brothers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Flyer", "link": "https://wikipedia.org/wiki/<PERSON>_Flyer"}, {"title": "Kitty Hawk, North Carolina", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_North_Carolina"}]}, {"year": "1907", "text": "The Thomas <PERSON>, the largest ever ship without a heat engine, runs aground and founders near the Hellweather's Reef within the Isles of Scilly in a gale. The pilot and 15 seamen die.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>(ship)\" title=\"<PERSON> (ship)\"><i><PERSON></i></a>, the <a href=\"https://wikipedia.org/wiki/List_of_large_sailing_vessels\" title=\"List of large sailing vessels\">largest ever ship without a heat engine</a>, runs aground and founders near the Hellweather's Reef within the <a href=\"https://wikipedia.org/wiki/Isles_of_Scilly\" title=\"Isles of Scilly\">Isles of Scilly</a> in a gale. The pilot and 15 seamen die.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(ship)\" title=\"<PERSON> (ship)\"><i><PERSON></i></a>, the <a href=\"https://wikipedia.org/wiki/List_of_large_sailing_vessels\" title=\"List of large sailing vessels\">largest ever ship without a heat engine</a>, runs aground and founders near the Hellweather's Reef within the <a href=\"https://wikipedia.org/wiki/Isles_of_Scilly\" title=\"Isles of Scilly\">Isles of Scilly</a> in a gale. The pilot and 15 seamen die.", "links": [{"title": "<PERSON> (ship)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(ship)"}, {"title": "List of large sailing vessels", "link": "https://wikipedia.org/wiki/List_of_large_sailing_vessels"}, {"title": "Isles of Scilly", "link": "https://wikipedia.org/wiki/Isles_of_Scilly"}]}, {"year": "1909", "text": "New South Wales Premier <PERSON> signs the Seat of Government Surrender Act 1909, formally completing the transfer of State land to the Commonwealth to create the Australian Capital Territory.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a> Premier <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Seat_of_Government_Surrender_Act_1909\" title=\"Seat of Government Surrender Act 1909\">Seat of Government Surrender Act 1909</a>, formally completing the transfer of State land to the Commonwealth to create the <a href=\"https://wikipedia.org/wiki/Australian_Capital_Territory\" title=\"Australian Capital Territory\">Australian Capital Territory</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a> Premier <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Seat_of_Government_Surrender_Act_1909\" title=\"Seat of Government Surrender Act 1909\">Seat of Government Surrender Act 1909</a>, formally completing the transfer of State land to the Commonwealth to create the <a href=\"https://wikipedia.org/wiki/Australian_Capital_Territory\" title=\"Australian Capital Territory\">Australian Capital Territory</a>.", "links": [{"title": "New South Wales", "link": "https://wikipedia.org/wiki/New_South_Wales"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Seat of Government Surrender Act 1909", "link": "https://wikipedia.org/wiki/Seat_of_Government_Surrender_Act_1909"}, {"title": "Australian Capital Territory", "link": "https://wikipedia.org/wiki/Australian_Capital_Territory"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>'s team, comprising himself, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, becomes the first to reach the South Pole.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Roald_Amundsen\" title=\"Roald Amundsen\"><PERSON><PERSON><PERSON></a>'s team, comprising himself, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sver<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/Oscar_Wisting\" title=\"<PERSON> Wisting\"><PERSON></a>, becomes the first to reach the <a href=\"https://wikipedia.org/wiki/South_Pole\" title=\"South Pole\">South Pole</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roa<PERSON>_Am<PERSON>en\" title=\"Roald Amundsen\"><PERSON><PERSON><PERSON></a>'s team, comprising himself, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sver<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/Oscar_Wisting\" title=\"<PERSON> Wisting\"><PERSON></a>, becomes the first to reach the <a href=\"https://wikipedia.org/wiki/South_Pole\" title=\"South Pole\">South Pole</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roald_Am<PERSON>en"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wisting"}, {"title": "South Pole", "link": "https://wikipedia.org/wiki/South_Pole"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, the fourth and last Kongō-class ship, launches, eventually becoming one of the Japanese workhorses during World War I and World War II.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Japanese_battleship_Haruna\" title=\"Japanese battleship <PERSON><PERSON><PERSON>\"><i><PERSON><PERSON><PERSON></i></a>, the fourth and last <a href=\"https://wikipedia.org/wiki/Kong%C5%8D-class_battlecruiser\" title=\"Kongō-class battlecruiser\"><i>Kongō</i>-class</a> ship, launches, eventually becoming one of the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> workhorses during <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a> and <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japanese_battleship_Haruna\" title=\"Japanese battleship <PERSON><PERSON><PERSON>\"><i><PERSON><PERSON><PERSON></i></a>, the fourth and last <a href=\"https://wikipedia.org/wiki/Kong%C5%8D-class_battlecruiser\" title=\"Kongō-class battlecruiser\"><i>Kongō</i>-class</a> ship, launches, eventually becoming one of the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> workhorses during <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a> and <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "links": [{"title": "Japanese battleship Haruna", "link": "https://wikipedia.org/wiki/Japanese_battleship_<PERSON><PERSON>a"}, {"title": "Kongō-class battlecruiser", "link": "https://wikipedia.org/wiki/Kong%C5%8D-class_battlecruiser"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON> and others found the Democratic Progressive Party (Partido Demócrata Progresista, PDP) at the Hotel Savoy, Buenos Aires, Argentina.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>Torre\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and others found the <a href=\"https://wikipedia.org/wiki/Democratic_Progressive_Party_(Argentina)\" title=\"Democratic Progressive Party (Argentina)\">Democratic Progressive Party</a> (<i>Partido Demócrata Progresista</i>, PDP) at the Hotel Savoy, <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and others found the <a href=\"https://wikipedia.org/wiki/Democratic_Progressive_Party_(Argentina)\" title=\"Democratic Progressive Party (Argentina)\">Democratic Progressive Party</a> (<i>Partido Demócrata Progresista</i>, PDP) at the Hotel Savoy, <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Democratic Progressive Party (Argentina)", "link": "https://wikipedia.org/wiki/Democratic_Progressive_Party_(Argentina)"}, {"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}]}, {"year": "1918", "text": "<PERSON>, a German prince elected by the Parliament of Finland to become King <PERSON><PERSON><PERSON><PERSON>, renounces the Finnish throne.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_Hesse\" title=\"Prince <PERSON> of Hesse\"><PERSON></a>, a German prince elected by the <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Parliament of Finland</a> to become King <PERSON><PERSON><PERSON><PERSON>, renounces the <a href=\"https://wikipedia.org/wiki/List_of_monarchs_of_Finland\" class=\"mw-redirect\" title=\"List of monarchs of Finland\">Finnish throne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_Frederick_<PERSON>_<PERSON>_Hesse\" title=\"Prince <PERSON> of Hesse\"><PERSON></a>, a German prince elected by the <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Parliament of Finland</a> to become King <PERSON><PERSON><PERSON><PERSON>, renounces the <a href=\"https://wikipedia.org/wiki/List_of_monarchs_of_Finland\" class=\"mw-redirect\" title=\"List of monarchs of Finland\">Finnish throne</a>.", "links": [{"title": "Prince <PERSON> of Hesse", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_Hesse"}, {"title": "Parliament of Finland", "link": "https://wikipedia.org/wiki/Parliament_of_Finland"}, {"title": "List of monarchs of Finland", "link": "https://wikipedia.org/wiki/List_of_monarchs_of_Finland"}]}, {"year": "1918", "text": "Portuguese President <PERSON><PERSON><PERSON> is assassinated.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">Portuguese President</a> <a href=\"https://wikipedia.org/wiki/Sid%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is assassinated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">Portuguese President</a> <a href=\"https://wikipedia.org/wiki/Sid%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is assassinated.", "links": [{"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sid%C3%B3nio_Pais"}]}, {"year": "1918", "text": "The 1918 United Kingdom general election occurs, the first where women were permitted to vote. In Ireland the Irish republican political party Sinn Féin wins a landslide victory with nearly 47% of the popular vote.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/1918_United_Kingdom_general_election\" title=\"1918 United Kingdom general election\">1918 United Kingdom general election</a> occurs, the first where women were permitted to vote. In Ireland the Irish republican political party <a href=\"https://wikipedia.org/wiki/Sinn_F%C3%A9in\" title=\"Sinn Féin\">Sinn Féin</a> wins a landslide victory with nearly 47% of the popular vote.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1918_United_Kingdom_general_election\" title=\"1918 United Kingdom general election\">1918 United Kingdom general election</a> occurs, the first where women were permitted to vote. In Ireland the Irish republican political party <a href=\"https://wikipedia.org/wiki/Sinn_F%C3%A9in\" title=\"Sinn Féin\">Sinn Féin</a> wins a landslide victory with nearly 47% of the popular vote.", "links": [{"title": "1918 United Kingdom general election", "link": "https://wikipedia.org/wiki/1918_United_Kingdom_general_election"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sinn_F%C3%A9in"}]}, {"year": "1918", "text": "<PERSON>'s comic opera <PERSON><PERSON><PERSON> premieres at the Metropolitan Opera in New York City.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s comic opera <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i> premieres at the <a href=\"https://wikipedia.org/wiki/Metropolitan_Opera\" title=\"Metropolitan Opera\">Metropolitan Opera</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s comic opera <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i> premieres at the <a href=\"https://wikipedia.org/wiki/Metropolitan_Opera\" title=\"Metropolitan Opera\">Metropolitan Opera</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Metropolitan Opera", "link": "https://wikipedia.org/wiki/Metropolitan_Opera"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1939", "text": "Winter War: The Soviet Union is expelled from the League of Nations for invading Finland.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> is expelled from the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> for invading Finland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> is expelled from the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> for invading Finland.", "links": [{"title": "Winter War", "link": "https://wikipedia.org/wiki/Winter_War"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1940", "text": "Plutonium (specifically Pu-238) is first isolated at Berkeley, California.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Plutonium\" title=\"Plutonium\">Plutonium</a> (specifically <a href=\"https://wikipedia.org/wiki/Pu-238\" class=\"mw-redirect\" title=\"Pu-238\">Pu-238</a>) is first isolated at Berkeley, California.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plutonium\" title=\"Plutonium\">Plutonium</a> (specifically <a href=\"https://wikipedia.org/wiki/Pu-238\" class=\"mw-redirect\" title=\"Pu-238\">Pu-238</a>) is first isolated at Berkeley, California.", "links": [{"title": "Plutonium", "link": "https://wikipedia.org/wiki/Plutonium"}, {"title": "Pu-238", "link": "https://wikipedia.org/wiki/Pu-238"}]}, {"year": "1942", "text": "An Aeroflot Tupolev ANT-20 crashes near Tashkent, killing all 36 people on board.", "html": "1942 - An <a href=\"https://wikipedia.org/wiki/Aeroflot\" title=\"Aeroflot\">Aeroflot</a> <a href=\"https://wikipedia.org/wiki/Tupolev_ANT-20\" title=\"Tupolev ANT-20\">Tupolev ANT-20</a> <a href=\"https://wikipedia.org/wiki/1942_Aeroflot_Tupolev_ANT-20bis_crash\" title=\"1942 Aeroflot Tupolev ANT-20bis crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Tashkent\" title=\"Tashkent\">Tashkent</a>, killing all 36 people on board.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Aeroflot\" title=\"Aeroflot\">Aeroflot</a> <a href=\"https://wikipedia.org/wiki/Tupolev_ANT-20\" title=\"Tupolev ANT-20\">Tupolev ANT-20</a> <a href=\"https://wikipedia.org/wiki/1942_Aeroflot_Tupolev_ANT-20bis_crash\" title=\"1942 Aeroflot Tupolev ANT-20bis crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Tashkent\" title=\"Tashkent\">Tashkent</a>, killing all 36 people on board.", "links": [{"title": "Aeroflot", "link": "https://wikipedia.org/wiki/Aeroflot"}, {"title": "Tupolev ANT-20", "link": "https://wikipedia.org/wiki/Tupolev_ANT-20"}, {"title": "1942 Aeroflot Tupolev ANT-20bis crash", "link": "https://wikipedia.org/wiki/1942_Aeroflot_Tupolev_ANT-20bis_crash"}, {"title": "Tashkent", "link": "https://wikipedia.org/wiki/Tashkent"}]}, {"year": "1948", "text": "<PERSON> Jr. and Estle Ray Mann are granted a patent for their cathode-ray tube amusement device, the earliest known interactive electronic game.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>smith Jr.\"><PERSON> Jr.</a> and Estle Ray Mann are granted a patent for their <a href=\"https://wikipedia.org/wiki/Cathode-ray_tube_amusement_device\" title=\"Cathode-ray tube amusement device\">cathode-ray tube amusement device</a>, the earliest known interactive <a href=\"https://wikipedia.org/wiki/Electronic_game\" title=\"Electronic game\">electronic game</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>smith Jr.\"><PERSON> Jr.</a> and Estle Ray Mann are granted a patent for their <a href=\"https://wikipedia.org/wiki/Cathode-ray_tube_amusement_device\" title=\"Cathode-ray tube amusement device\">cathode-ray tube amusement device</a>, the earliest known interactive <a href=\"https://wikipedia.org/wiki/Electronic_game\" title=\"Electronic game\">electronic game</a>.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Cathode-ray tube amusement device", "link": "https://wikipedia.org/wiki/Cathode-ray_tube_amusement_device"}, {"title": "Electronic game", "link": "https://wikipedia.org/wiki/Electronic_game"}]}, {"year": "1955", "text": "Albania, Austria, Bulgaria, Cambodia, Ceylon, Finland, Hungary, Ireland, Italy, Jordan, Laos, Libya, Nepal, Portugal, Romania and Spain join the United Nations through United Nations Security Council Resolution 109.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albania</a>, <a href=\"https://wikipedia.org/wiki/Austria\" title=\"Austria\">Austria</a>, <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Cambodia_(1953%E2%80%931970)\" title=\"Kingdom of Cambodia (1953-1970)\">Cambodia</a>, <a href=\"https://wikipedia.org/wiki/Dominion_of_Ceylon\" title=\"Dominion of Ceylon\">Ceylon</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a>, <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Ireland</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>, <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a>, <a href=\"https://wikipedia.org/wiki/Laos\" title=\"Laos\">Laos</a>, <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>, <a href=\"https://wikipedia.org/wiki/Nepal\" title=\"Nepal\">Nepal</a>, <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>, <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a> and <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a> join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> through <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_109\" title=\"United Nations Security Council Resolution 109\">United Nations Security Council Resolution 109</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albania</a>, <a href=\"https://wikipedia.org/wiki/Austria\" title=\"Austria\">Austria</a>, <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Cambodia_(1953%E2%80%931970)\" title=\"Kingdom of Cambodia (1953-1970)\">Cambodia</a>, <a href=\"https://wikipedia.org/wiki/Dominion_of_Ceylon\" title=\"Dominion of Ceylon\">Ceylon</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a>, <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Ireland</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>, <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a>, <a href=\"https://wikipedia.org/wiki/Laos\" title=\"Laos\">Laos</a>, <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>, <a href=\"https://wikipedia.org/wiki/Nepal\" title=\"Nepal\">Nepal</a>, <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>, <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a> and <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a> join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> through <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_109\" title=\"United Nations Security Council Resolution 109\">United Nations Security Council Resolution 109</a>.", "links": [{"title": "Albania", "link": "https://wikipedia.org/wiki/Albania"}, {"title": "Austria", "link": "https://wikipedia.org/wiki/Austria"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Kingdom of Cambodia (1953-1970)", "link": "https://wikipedia.org/wiki/Kingdom_of_Cambodia_(1953%E2%80%931970)"}, {"title": "Dominion of Ceylon", "link": "https://wikipedia.org/wiki/Dominion_of_Ceylon"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Hungary", "link": "https://wikipedia.org/wiki/Hungary"}, {"title": "Republic of Ireland", "link": "https://wikipedia.org/wiki/Republic_of_Ireland"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}, {"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}, {"title": "Laos", "link": "https://wikipedia.org/wiki/Laos"}, {"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "Nepal", "link": "https://wikipedia.org/wiki/Nepal"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}, {"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "United Nations Security Council Resolution 109", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_109"}]}, {"year": "1958", "text": "The 3rd Soviet Antarctic Expedition becomes the first to reach the southern pole of inaccessibility.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/3rd_Soviet_Antarctic_Expedition\" title=\"3rd Soviet Antarctic Expedition\">3rd Soviet Antarctic Expedition</a> becomes the first to reach the <a href=\"https://wikipedia.org/wiki/Pole_of_inaccessibility#Southern_pole_of_inaccessibility\" title=\"Pole of inaccessibility\">southern pole of inaccessibility</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/3rd_Soviet_Antarctic_Expedition\" title=\"3rd Soviet Antarctic Expedition\">3rd Soviet Antarctic Expedition</a> becomes the first to reach the <a href=\"https://wikipedia.org/wiki/Pole_of_inaccessibility#Southern_pole_of_inaccessibility\" title=\"Pole of inaccessibility\">southern pole of inaccessibility</a>.", "links": [{"title": "3rd Soviet Antarctic Expedition", "link": "https://wikipedia.org/wiki/3rd_Soviet_Antarctic_Expedition"}, {"title": "Pole of inaccessibility", "link": "https://wikipedia.org/wiki/Pole_of_inaccessibility#Southern_pole_of_inaccessibility"}]}, {"year": "1960", "text": "Convention against Discrimination in Education of UNESCO is adopted.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Convention_Against_Discrimination_in_Education\" title=\"Convention Against Discrimination in Education\">Convention against Discrimination in Education</a> of <a href=\"https://wikipedia.org/wiki/UNESCO\" title=\"UNESCO\">UNESCO</a> is adopted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Convention_Against_Discrimination_in_Education\" title=\"Convention Against Discrimination in Education\">Convention against Discrimination in Education</a> of <a href=\"https://wikipedia.org/wiki/UNESCO\" title=\"UNESCO\">UNESCO</a> is adopted.", "links": [{"title": "Convention Against Discrimination in Education", "link": "https://wikipedia.org/wiki/Convention_Against_Discrimination_in_Education"}, {"title": "UNESCO", "link": "https://wikipedia.org/wiki/UNESCO"}]}, {"year": "1962", "text": "NASA's Mariner 2 becomes the first spacecraft to fly by Venus.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <i><a href=\"https://wikipedia.org/wiki/Mariner_2\" title=\"Mariner 2\">Mariner 2</a></i> becomes the first <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> to fly by <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <i><a href=\"https://wikipedia.org/wiki/Mariner_2\" title=\"Mariner 2\">Mariner 2</a></i> becomes the first <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> to fly by <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mariner 2", "link": "https://wikipedia.org/wiki/Mariner_2"}, {"title": "Spacecraft", "link": "https://wikipedia.org/wiki/Spacecraft"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}]}, {"year": "1963", "text": "The dam containing the Baldwin Hills Reservoir bursts, killing five people and damaging hundreds of homes in Los Angeles, California.", "html": "1963 - The dam containing the <a href=\"https://wikipedia.org/wiki/Baldwin_Hills_Dam_disaster\" title=\"Baldwin Hills Dam disaster\">Baldwin Hills Reservoir</a> bursts, killing five people and damaging hundreds of homes in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles, California</a>.", "no_year_html": "The dam containing the <a href=\"https://wikipedia.org/wiki/Baldwin_Hills_Dam_disaster\" title=\"Baldwin Hills Dam disaster\">Baldwin Hills Reservoir</a> bursts, killing five people and damaging hundreds of homes in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles, California</a>.", "links": [{"title": "Baldwin Hills Dam disaster", "link": "https://wikipedia.org/wiki/Baldwin_Hills_Dam_disaster"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}]}, {"year": "1964", "text": "American Civil Rights Movement: Heart of Atlanta Motel v. United States: The Supreme Court of the United States rules that Congress can use the Constitution's Commerce Clause to fight discrimination.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">American Civil Rights Movement</a>: <i><a href=\"https://wikipedia.org/wiki/Heart_of_Atlanta_Motel,_Inc._v._United_States\" title=\"Heart of Atlanta Motel, Inc. v. United States\">Heart of Atlanta Motel v. United States</a></i>: The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> rules that <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> can use the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_United_States\" title=\"Constitution of the United States\">Constitution's</a> <a href=\"https://wikipedia.org/wiki/Commerce_Clause\" title=\"Commerce Clause\">Commerce Clause</a> to fight discrimination.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">American Civil Rights Movement</a>: <i><a href=\"https://wikipedia.org/wiki/Heart_of_Atlanta_Motel,_Inc._v._United_States\" title=\"Heart of Atlanta Motel, Inc. v. United States\">Heart of Atlanta Motel v. United States</a></i>: The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> rules that <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> can use the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_United_States\" title=\"Constitution of the United States\">Constitution's</a> <a href=\"https://wikipedia.org/wiki/Commerce_Clause\" title=\"Commerce Clause\">Commerce Clause</a> to fight discrimination.", "links": [{"title": "Civil rights movement", "link": "https://wikipedia.org/wiki/Civil_rights_movement"}, {"title": "Heart of Atlanta Motel, Inc. v. United States", "link": "https://wikipedia.org/wiki/Heart_of_Atlanta_Motel,_Inc._v._United_States"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Constitution of the United States", "link": "https://wikipedia.org/wiki/Constitution_of_the_United_States"}, {"title": "Commerce Clause", "link": "https://wikipedia.org/wiki/Commerce_Clause"}]}, {"year": "1971", "text": "Bangladesh Liberation War: Over 200 of East Pakistan's intellectuals are executed by the Pakistan Army and their local allies. (The date is commemorated in Bangladesh as Martyred Intellectuals Day.)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Bangladesh_Liberation_War\" title=\"Bangladesh Liberation War\">Bangladesh Liberation War</a>: Over 200 of <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a><span class=\"nowrap\" style=\"padding-left:0.1em;\">'</span>s intellectuals are <a href=\"https://wikipedia.org/wiki/1971_killing_of_Bengali_intellectuals#14_December_executions\" title=\"1971 killing of Bengali intellectuals\">executed</a> by the Pakistan Army and their local allies. (The date is commemorated in Bangladesh as <a href=\"https://wikipedia.org/wiki/Martyred_Intellectuals_Day\" title=\"Martyred Intellectuals Day\">Martyred Intellectuals Day</a>.)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bangladesh_Liberation_War\" title=\"Bangladesh Liberation War\">Bangladesh Liberation War</a>: Over 200 of <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a><span class=\"nowrap\" style=\"padding-left:0.1em;\">'</span>s intellectuals are <a href=\"https://wikipedia.org/wiki/1971_killing_of_Bengali_intellectuals#14_December_executions\" title=\"1971 killing of Bengali intellectuals\">executed</a> by the Pakistan Army and their local allies. (The date is commemorated in Bangladesh as <a href=\"https://wikipedia.org/wiki/Martyred_Intellectuals_Day\" title=\"Martyred Intellectuals Day\">Martyred Intellectuals Day</a>.)", "links": [{"title": "Bangladesh Liberation War", "link": "https://wikipedia.org/wiki/Bangladesh_Liberation_War"}, {"title": "East Pakistan", "link": "https://wikipedia.org/wiki/East_Pakistan"}, {"title": "1971 killing of Bengali intellectuals", "link": "https://wikipedia.org/wiki/1971_killing_of_Bengali_intellectuals#14_December_executions"}, {"title": "Martyred Intellectuals Day", "link": "https://wikipedia.org/wiki/Martyred_Intellectuals_Day"}]}, {"year": "1972", "text": "Apollo program: <PERSON> is the most recent person to walk on the Moon, after he and <PERSON> complete the third and final extravehicular activity (EVA) of the Apollo 17 mission.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the most recent person to walk on the Moon, after he and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> complete the third and final <a href=\"https://wikipedia.org/wiki/Extravehicular_activity\" title=\"Extravehicular activity\">extravehicular activity</a> (EVA) of the <a href=\"https://wikipedia.org/wiki/Apollo_17\" title=\"Apollo 17\">Apollo 17</a> mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the most recent person to walk on the Moon, after he and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> complete the third and final <a href=\"https://wikipedia.org/wiki/Extravehicular_activity\" title=\"Extravehicular activity\">extravehicular activity</a> (EVA) of the <a href=\"https://wikipedia.org/wiki/Apollo_17\" title=\"Apollo 17\">Apollo 17</a> mission.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Extravehicular activity", "link": "https://wikipedia.org/wiki/Extravehicular_activity"}, {"title": "Apollo 17", "link": "https://wikipedia.org/wiki/Apollo_17"}]}, {"year": "1981", "text": "Arab-Israeli conflict: Israel's <PERSON><PERSON><PERSON> ratifies the Golan Heights Law, extending Israeli law to the Golan Heights.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Arab%E2%80%93Israeli_conflict\" title=\"Arab-Israeli conflict\">Arab-Israeli conflict</a>: Israel's <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a> ratifies the <i><a href=\"https://wikipedia.org/wiki/Golan_Heights_Law\" title=\"Golan Heights Law\">Golan Heights Law</a></i>, extending Israeli law to the <a href=\"https://wikipedia.org/wiki/Golan_Heights\" title=\"Golan Heights\">Golan Heights</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arab%E2%80%93Israeli_conflict\" title=\"Arab-Israeli conflict\">Arab-Israeli conflict</a>: Israel's <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a> ratifies the <i><a href=\"https://wikipedia.org/wiki/Golan_Heights_Law\" title=\"Golan Heights Law\">Golan Heights Law</a></i>, extending Israeli law to the <a href=\"https://wikipedia.org/wiki/Golan_Heights\" title=\"Golan Heights\">Golan Heights</a>.", "links": [{"title": "Arab-Israeli conflict", "link": "https://wikipedia.org/wiki/Arab%E2%80%93Israeli_conflict"}, {"title": "Knesset", "link": "https://wikipedia.org/wiki/Knesset"}, {"title": "Golan Heights Law", "link": "https://wikipedia.org/wiki/Golan_Heights_Law"}, {"title": "Golan Heights", "link": "https://wikipedia.org/wiki/Golan_Heights"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON> takes office as the first woman elected to serve as Principal Chief of the Cherokee Nation.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_Man<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> takes office as the first woman elected to serve as Principal Chief of the <a href=\"https://wikipedia.org/wiki/Cherokee_Nation\" title=\"Cherokee Nation\">Cherokee Nation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">W<PERSON><PERSON></a> takes office as the first woman elected to serve as Principal Chief of the <a href=\"https://wikipedia.org/wiki/Cherokee_Nation\" title=\"Cherokee Nation\">Cherokee Nation</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>er"}, {"title": "Cherokee Nation", "link": "https://wikipedia.org/wiki/Cherokee_Nation"}]}, {"year": "1986", "text": "Qasba Aligarh massacre: Over 400 Muhajirs killed in revenge killings in Qasba colony after a raid on Pashtun heroin processing and distribution center in Sohrab Goth by the security forces.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Qasba_Aligarh_massacre\" title=\"Qasba Aligarh massacre\">Qasba Aligarh massacre</a>: Over 400 <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(Pakistan)\" title=\"<PERSON><PERSON><PERSON><PERSON> (Pakistan)\"><PERSON><PERSON><PERSON><PERSON></a> killed in revenge killings in Qasba colony after a raid on <a href=\"https://wikipedia.org/wiki/Pashtuns\" title=\"Pashtuns\">Pashtun</a> heroin processing and distribution center in <a href=\"https://wikipedia.org/wiki/Sohrab_Goth\" class=\"mw-redirect\" title=\"Sohrab Goth\">So<PERSON>b Goth</a> by the security forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qasba_Aligarh_massacre\" title=\"Qasba Aligarh massacre\">Qasba Aligarh massacre</a>: Over 400 <a href=\"https://wikipedia.org/wiki/Mu<PERSON><PERSON>r_(Pakistan)\" title=\"<PERSON><PERSON><PERSON><PERSON> (Pakistan)\">Mu<PERSON><PERSON><PERSON></a> killed in revenge killings in Qasba colony after a raid on <a href=\"https://wikipedia.org/wiki/Pashtuns\" title=\"Pashtuns\">Pashtun</a> heroin processing and distribution center in <a href=\"https://wikipedia.org/wiki/Sohrab_Goth\" class=\"mw-redirect\" title=\"Sohrab Goth\"><PERSON><PERSON><PERSON> Goth</a> by the security forces.", "links": [{"title": "Qasba Aligarh massacre", "link": "https://wikipedia.org/wiki/Qasba_Aligarh_massacre"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (Pakistan)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(Pakistan)"}, {"title": "Pashtuns", "link": "https://wikipedia.org/wiki/Pashtuns"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>h"}]}, {"year": "1992", "text": "War in Abkhazia: Siege of Tkvarcheli: A helicopter carrying evacuees from Tkvarcheli is shot down, resulting in at least 52 deaths, including 25 children. The incident catalyses more concerted Russian military intervention on behalf of Abkhazia.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/War_in_Abkhazia_(1992%E2%80%931993)\" title=\"War in Abkhazia (1992-1993)\">War in Abkhazia</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Tkvarcheli\" title=\"Siege of Tkvarcheli\">Siege of Tkvarcheli</a>: A helicopter carrying evacuees from <a href=\"https://wikipedia.org/wiki/Tkvarcheli\" title=\"Tkvarcheli\">Tkvarcheli</a> is <a href=\"https://wikipedia.org/wiki/1992_Russian_Air_Force_Mi-8_shootdown\" class=\"mw-redirect\" title=\"1992 Russian Air Force Mi-8 shootdown\">shot down</a>, resulting in at least 52 deaths, including 25 children. The incident catalyses more concerted Russian military intervention on behalf of Abkhazia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_in_Abkhazia_(1992%E2%80%931993)\" title=\"War in Abkhazia (1992-1993)\">War in Abkhazia</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Tkvarcheli\" title=\"Siege of Tkvarcheli\">Siege of Tkvarcheli</a>: A helicopter carrying evacuees from <a href=\"https://wikipedia.org/wiki/Tkvarcheli\" title=\"Tkvarcheli\">Tkvarcheli</a> is <a href=\"https://wikipedia.org/wiki/1992_Russian_Air_Force_Mi-8_shootdown\" class=\"mw-redirect\" title=\"1992 Russian Air Force Mi-8 shootdown\">shot down</a>, resulting in at least 52 deaths, including 25 children. The incident catalyses more concerted Russian military intervention on behalf of Abkhazia.", "links": [{"title": "War in Abkhazia (1992-1993)", "link": "https://wikipedia.org/wiki/War_in_Abkhazia_(1992%E2%80%931993)"}, {"title": "Siege of Tkvarcheli", "link": "https://wikipedia.org/wiki/Siege_of_Tkvar<PERSON>i"}, {"title": "T<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tkvarcheli"}, {"title": "1992 Russian Air Force Mi-8 shootdown", "link": "https://wikipedia.org/wiki/1992_Russian_Air_Force_Mi-8_shootdown"}]}, {"year": "1994", "text": "Construction begins on the Three Gorges Dam on the Yangtze river.", "html": "1994 - Construction begins on the <a href=\"https://wikipedia.org/wiki/Three_Gorges_Dam\" title=\"Three Gorges Dam\">Three Gorges Dam</a> on the <a href=\"https://wikipedia.org/wiki/Yangtze\" title=\"Yangtze\">Yangtze</a> river.", "no_year_html": "Construction begins on the <a href=\"https://wikipedia.org/wiki/Three_Gorges_Dam\" title=\"Three Gorges Dam\">Three Gorges Dam</a> on the <a href=\"https://wikipedia.org/wiki/Yangtze\" title=\"Yangtze\">Yangtze</a> river.", "links": [{"title": "Three Gorges Dam", "link": "https://wikipedia.org/wiki/Three_Gorges_Dam"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yang<PERSON>e"}]}, {"year": "1995", "text": "Yugoslav Wars: The Dayton Agreement is signed in Paris by the leaders of the Federal Republic of Yugoslavia, Croatia, and Bosnia and Herzegovina.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: The <a href=\"https://wikipedia.org/wiki/Dayton_Agreement\" title=\"Dayton Agreement\">Dayton Agreement</a> is signed in Paris by the leaders of the <a href=\"https://wikipedia.org/wiki/Serbia_and_Montenegro\" title=\"Serbia and Montenegro\">Federal Republic of Yugoslavia</a>, <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a>, and <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: The <a href=\"https://wikipedia.org/wiki/Dayton_Agreement\" title=\"Dayton Agreement\">Dayton Agreement</a> is signed in Paris by the leaders of the <a href=\"https://wikipedia.org/wiki/Serbia_and_Montenegro\" title=\"Serbia and Montenegro\">Federal Republic of Yugoslavia</a>, <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a>, and <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "links": [{"title": "Yugoslav Wars", "link": "https://wikipedia.org/wiki/Yugoslav_Wars"}, {"title": "Dayton Agreement", "link": "https://wikipedia.org/wiki/Dayton_Agreement"}, {"title": "Serbia and Montenegro", "link": "https://wikipedia.org/wiki/Serbia_and_Montenegro"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}]}, {"year": "1998", "text": "Yugoslav Wars: The Yugoslav Army ambushes a group of Kosovo Liberation Army fighters attempting to smuggle weapons from Albania into Kosovo, killing 36.", "html": "1998 - Yugoslav Wars: The <a href=\"https://wikipedia.org/wiki/Military_of_Serbia_and_Montenegro\" class=\"mw-redirect\" title=\"Military of Serbia and Montenegro\">Yugoslav Army</a> <a href=\"https://wikipedia.org/wiki/December_14,_1998,_Albanian%E2%80%93Yugoslav_border_ambush\" title=\"December 14, 1998, Albanian-Yugoslav border ambush\">ambushes</a> a group of <a href=\"https://wikipedia.org/wiki/Kosovo_Liberation_Army\" title=\"Kosovo Liberation Army\">Kosovo Liberation Army</a> fighters attempting to smuggle weapons from Albania into Kosovo, killing 36.", "no_year_html": "Yugoslav Wars: The <a href=\"https://wikipedia.org/wiki/Military_of_Serbia_and_Montenegro\" class=\"mw-redirect\" title=\"Military of Serbia and Montenegro\">Yugoslav Army</a> <a href=\"https://wikipedia.org/wiki/December_14,_1998,_Albanian%E2%80%93Yugoslav_border_ambush\" title=\"December 14, 1998, Albanian-Yugoslav border ambush\">ambushes</a> a group of <a href=\"https://wikipedia.org/wiki/Kosovo_Liberation_Army\" title=\"Kosovo Liberation Army\">Kosovo Liberation Army</a> fighters attempting to smuggle weapons from Albania into Kosovo, killing 36.", "links": [{"title": "Military of Serbia and Montenegro", "link": "https://wikipedia.org/wiki/Military_of_Serbia_and_Montenegro"}, {"title": "December 14, 1998, Albanian-Yugoslav border ambush", "link": "https://wikipedia.org/wiki/December_14,_1998,_Albanian%E2%80%93Yugoslav_border_ambush"}, {"title": "Kosovo Liberation Army", "link": "https://wikipedia.org/wiki/Kosovo_Liberation_Army"}]}, {"year": "1999", "text": "Torrential rains cause flash floods in Vargas, Venezuela, resulting in tens of thousands of deaths, the destruction of thousands of homes, and the complete collapse of the state's infrastructure.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_tragedy\" title=\"Vargas tragedy\">Torrential rains</a> cause <a href=\"https://wikipedia.org/wiki/Flash_flood\" title=\"Flash flood\">flash floods</a> in <a href=\"https://wikipedia.org/wiki/Vargas,_Venezuela\" class=\"mw-redirect\" title=\"Vargas, Venezuela\">Vargas, Venezuela</a>, resulting in tens of thousands of deaths, the destruction of thousands of homes, and the complete collapse of the state's infrastructure.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_tragedy\" title=\"Vargas tragedy\">Torrential rains</a> cause <a href=\"https://wikipedia.org/wiki/Flash_flood\" title=\"Flash flood\">flash floods</a> in <a href=\"https://wikipedia.org/wiki/Vargas,_Venezuela\" class=\"mw-redirect\" title=\"Vargas, Venezuela\">Vargas, Venezuela</a>, resulting in tens of thousands of deaths, the destruction of thousands of homes, and the complete collapse of the state's infrastructure.", "links": [{"title": "<PERSON> tragedy", "link": "https://wikipedia.org/wiki/<PERSON>_tragedy"}, {"title": "Flash flood", "link": "https://wikipedia.org/wiki/Flash_flood"}, {"title": "Vargas, Venezuela", "link": "https://wikipedia.org/wiki/Vargas,_Venezuela"}]}, {"year": "2003", "text": "Pakistani President <PERSON><PERSON> narrowly escapes an assassination attempt.", "html": "2003 - Pakistani President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> narrowly escapes an assassination attempt.", "no_year_html": "Pakistani President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> narrowly escapes an assassination attempt.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>f"}]}, {"year": "2004", "text": "The Millau Viaduct, the tallest bridge in the world, is formally inaugurated near Millau, France.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Millau_Viaduct\" title=\"Millau Viaduct\">Millau Viaduct</a>, the <a href=\"https://wikipedia.org/wiki/List_of_tallest_bridges\" title=\"List of tallest bridges\">tallest bridge in the world</a>, is formally inaugurated near <a href=\"https://wikipedia.org/wiki/Millau\" title=\"Millau\">Millau</a>, France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Millau_Viaduct\" title=\"Millau Viaduct\">Millau Viaduct</a>, the <a href=\"https://wikipedia.org/wiki/List_of_tallest_bridges\" title=\"List of tallest bridges\">tallest bridge in the world</a>, is formally inaugurated near <a href=\"https://wikipedia.org/wiki/Millau\" title=\"Millau\">Millau</a>, France.", "links": [{"title": "Millau Viaduct", "link": "https://wikipedia.org/wiki/Millau_Viaduct"}, {"title": "List of tallest bridges", "link": "https://wikipedia.org/wiki/List_of_tallest_bridges"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>au"}]}, {"year": "2012", "text": "Sandy Hook Elementary School shooting: Twenty-eight people, including the gunman, are killed in Sandy Hook, Connecticut.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Sandy_Hook_Elementary_School_shooting\" title=\"Sandy Hook Elementary School shooting\">Sandy Hook Elementary School shooting</a>: Twenty-eight people, including the gunman, are killed in <a href=\"https://wikipedia.org/wiki/Sandy_Hook,_Connecticut\" title=\"Sandy Hook, Connecticut\">Sandy Hook, Connecticut</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sandy_Hook_Elementary_School_shooting\" title=\"Sandy Hook Elementary School shooting\">Sandy Hook Elementary School shooting</a>: Twenty-eight people, including the gunman, are killed in <a href=\"https://wikipedia.org/wiki/Sandy_Hook,_Connecticut\" title=\"Sandy Hook, Connecticut\">Sandy Hook, Connecticut</a>.", "links": [{"title": "Sandy Hook Elementary School shooting", "link": "https://wikipedia.org/wiki/Sandy_Hook_Elementary_School_shooting"}, {"title": "Sandy Hook, Connecticut", "link": "https://wikipedia.org/wiki/Sandy_Hook,_Connecticut"}]}, {"year": "2013", "text": "A reported coup attempt in South Sudan leads to continued fighting and hundreds of casualties.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/South_Sudanese_Civil_War\" title=\"South Sudanese Civil War\">A reported coup attempt</a> in <a href=\"https://wikipedia.org/wiki/South_Sudan\" title=\"South Sudan\">South Sudan</a> leads to continued fighting and hundreds of casualties.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_Sudanese_Civil_War\" title=\"South Sudanese Civil War\">A reported coup attempt</a> in <a href=\"https://wikipedia.org/wiki/South_Sudan\" title=\"South Sudan\">South Sudan</a> leads to continued fighting and hundreds of casualties.", "links": [{"title": "South Sudanese Civil War", "link": "https://wikipedia.org/wiki/South_Sudanese_Civil_War"}, {"title": "South Sudan", "link": "https://wikipedia.org/wiki/South_Sudan"}]}, {"year": "2017", "text": "The Walt Disney Company announces that it would acquire 21st Century Fox, including the 20th Century Fox movie studio, for $52.4 billion.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> announces that it would acquire <a href=\"https://wikipedia.org/wiki/21st_Century_Fox\" title=\"21st Century Fox\">21st Century Fox</a>, including the <a href=\"https://wikipedia.org/wiki/20th_Century_Fox\" class=\"mw-redirect\" title=\"20th Century Fox\">20th Century Fox</a> movie studio, for $52.4 billion.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> announces that it would acquire <a href=\"https://wikipedia.org/wiki/21st_Century_Fox\" title=\"21st Century Fox\">21st Century Fox</a>, including the <a href=\"https://wikipedia.org/wiki/20th_Century_Fox\" class=\"mw-redirect\" title=\"20th Century Fox\">20th Century Fox</a> movie studio, for $52.4 billion.", "links": [{"title": "The Walt Disney Company", "link": "https://wikipedia.org/wiki/The_Walt_Disney_Company"}, {"title": "21st Century Fox", "link": "https://wikipedia.org/wiki/21st_Century_Fox"}, {"title": "20th Century Fox", "link": "https://wikipedia.org/wiki/20th_Century_Fox"}]}, {"year": "2020", "text": "A total solar eclipse is visible from parts of the South Pacific Ocean, southern South America, and the South Atlantic Ocean.", "html": "2020 - A <a href=\"https://wikipedia.org/wiki/Solar_eclipse_of_December_14,_2020\" title=\"Solar eclipse of December 14, 2020\">total solar eclipse</a> is visible from parts of the South Pacific Ocean, southern South America, and the South Atlantic Ocean.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Solar_eclipse_of_December_14,_2020\" title=\"Solar eclipse of December 14, 2020\">total solar eclipse</a> is visible from parts of the South Pacific Ocean, southern South America, and the South Atlantic Ocean.", "links": [{"title": "Solar eclipse of December 14, 2020", "link": "https://wikipedia.org/wiki/Solar_eclipse_of_December_14,_2020"}]}], "Births": [{"year": "1009", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, emperor of Japan (d. 1045)", "html": "1009 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Suzaku\" title=\"Emperor <PERSON>-Suzaku\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (d. 1045)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Suzaku\" title=\"Emperor <PERSON>-Suzaku\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (d. 1045)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>"}]}, {"year": "1332", "text": "<PERSON>, German nobleman (d. 1381)", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON></a>, German nobleman (d. 1381)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON> III</a>, German nobleman (d. 1381)", "links": [{"title": "<PERSON>, Landgrave of Thuringia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Thuringia"}]}, {"year": "1546", "text": "<PERSON><PERSON>, Danish astronomer and chemist (d. 1601)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/Ty<PERSON>_Brahe\" title=\"Tycho Brahe\"><PERSON><PERSON></a>, Danish astronomer and chemist (d. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tycho_Brahe\" title=\"Tycho Brahe\"><PERSON><PERSON></a>, Danish astronomer and chemist (d. 1601)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ty<PERSON>_<PERSON>e"}]}, {"year": "1599", "text": "<PERSON>, 2nd Viscount <PERSON>, English politician (d. 1668)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Viscount_<PERSON><PERSON>\" title=\"<PERSON>, 2nd Viscount <PERSON>\"><PERSON>, 2nd Viscount <PERSON></a>, English politician (d. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_<PERSON><PERSON>\" title=\"<PERSON>, 2nd Viscount <PERSON>\"><PERSON>, 2nd Viscount <PERSON></a>, English politician (d. 1668)", "links": [{"title": "<PERSON>, 2nd Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Viscount_<PERSON>"}]}, {"year": "1607", "text": "<PERSON><PERSON><PERSON>, Hungarian prince (d. 1662)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_(prince)\" title=\"<PERSON> (prince)\"><PERSON><PERSON><PERSON></a>, Hungarian prince (d. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_(prince)\" title=\"<PERSON> (prince)\"><PERSON><PERSON><PERSON></a>, Hungarian prince (d. 1662)", "links": [{"title": "<PERSON> (prince)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9ny_(prince)"}]}, {"year": "1625", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French orientalist and academic (d. 1695)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/Barth%C3%A9lemy_d%27Herbelot\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>Herb<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, French orientalist and academic (d. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barth%C3%A9lemy_d%27Herbelot\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>Herb<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, French orientalist and academic (d. 1695)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Barth%C3%A9lemy_d%27Herbelot"}]}, {"year": "1631", "text": "<PERSON>, English philosopher and author (d. 1679)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English philosopher and author (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English philosopher and author (d. 1679)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(philosopher)"}]}, {"year": "1640", "text": "<PERSON><PERSON><PERSON>, English playwright and author (d. 1689)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English playwright and author (d. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English playwright and author (d. 1689)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1678", "text": "<PERSON>, English historian and author (d. 1743)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON><PERSON>, German jurist and theorist (d. 1794)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/Justus_M%C3%B6ser\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German jurist and theorist (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Justus_M%C3%B6ser\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German jurist and theorist (d. 1794)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Justus_M%C3%B6ser"}]}, {"year": "1730", "text": "<PERSON><PERSON>, English organist and composer (d. 1790)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bond\"><PERSON><PERSON></a>, English organist and composer (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Cape<PERSON> Bond\"><PERSON><PERSON></a>, English organist and composer (d. 1790)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bond"}]}, {"year": "1738", "text": "<PERSON>, Czech composer and educator (d. 1814)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Ko%C5%BEeluh\" title=\"<PERSON>\"><PERSON></a>, Czech composer and educator (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Ko%C5%BEeluh\" title=\"<PERSON>\"><PERSON></a>, Czech composer and educator (d. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Ko%C5%BEeluh"}]}, {"year": "1775", "text": "<PERSON><PERSON>, American bishop and educator, founded Kenyon College (d. 1852)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chase\" title=\"<PERSON>ander Chase\"><PERSON><PERSON></a>, American bishop and educator, founded <a href=\"https://wikipedia.org/wiki/Kenyon_College\" title=\"Kenyon College\">Kenyon College</a> (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chase\" title=\"<PERSON>ander Chase\"><PERSON><PERSON></a>, American bishop and educator, founded <a href=\"https://wikipedia.org/wiki/Kenyon_College\" title=\"Kenyon College\">Kenyon College</a> (d. 1852)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Chase"}, {"title": "Kenyon College", "link": "https://wikipedia.org/wiki/Kenyon_College"}]}, {"year": "1775", "text": "<PERSON>, 10th Earl of Dundonald, Scottish admiral and politician (d. 1860)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Dundonald\" title=\"<PERSON>, 10th Earl of Dundonald\"><PERSON>, 10th Earl of Dundonald</a>, Scottish admiral and politician (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Dundonald\" title=\"<PERSON>, 10th Earl of Dundonald\"><PERSON>, 10th Earl of Dundonald</a>, Scottish admiral and politician (d. 1860)", "links": [{"title": "<PERSON>, 10th Earl of Dundonald", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Dundonald"}]}, {"year": "1777", "text": "<PERSON> <PERSON>, 2nd Earl of Caledon, Irish politician, Lord Lieutenant of Tyrone (d. 1839)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/Du_Pr%C3%A<PERSON>_<PERSON>,_2nd_Earl_of_Caledon\" title=\"<PERSON>, 2nd Earl of Caledon\"><PERSON>, 2nd Earl of Caledon</a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Tyrone\" title=\"Lord Lieutenant of Tyrone\">Lord Lieutenant of Tyrone</a> (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du_Pr%C3%A<PERSON>_<PERSON>,_2nd_Earl_of_Caledon\" title=\"<PERSON>, 2nd Earl of Caledon\"><PERSON>, 2nd Earl of Caledon</a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Tyrone\" title=\"Lord Lieutenant of Tyrone\">Lord Lieutenant of Tyrone</a> (d. 1839)", "links": [{"title": "<PERSON> <PERSON><PERSON>, 2nd Earl of Caledon", "link": "https://wikipedia.org/wiki/Du_Pr%C3%A<PERSON>_<PERSON>,_2nd_Earl_of_Caledon"}, {"title": "Lord Lieutenant of Tyrone", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Tyrone"}]}, {"year": "1784", "text": "Princess <PERSON> of Naples and Sicily (d. 1806)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Naples_and_Sicily\" title=\"Princess <PERSON> of Naples and Sicily\">Princess <PERSON> of Naples and Sicily</a> (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Naples_and_Sicily\" title=\"Princess <PERSON> of Naples and Sicily\">Princess <PERSON> of Naples and Sicily</a> (d. 1806)", "links": [{"title": "Princess <PERSON> of Naples and Sicily", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Naples_and_Sicily"}]}, {"year": "1789", "text": "<PERSON>, Polish composer and pianist (d. 1831)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish composer and pianist (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish composer and pianist (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_<PERSON>zy<PERSON>owska"}]}, {"year": "1791", "text": "<PERSON>, Irish priest and poet (d. 1823)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and poet (d. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and poet (d. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON><PERSON><PERSON>, American businessman and politician (d. 1872)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rn<PERSON>\" title=\"<PERSON><PERSON><PERSON> Corn<PERSON>\"><PERSON><PERSON><PERSON></a>, American businessman and politician (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Corn<PERSON>\"><PERSON><PERSON><PERSON></a>, American businessman and politician (d. 1872)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>st<PERSON>_Corning"}]}, {"year": "1816", "text": "<PERSON>, Hungarian rabbi and educator (d. 1889)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian rabbi and educator (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian rabbi and educator (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, French painter and illustrator (d. 1898)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, American general, lawyer, and politician (d. 1902)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, American novelist and short story writer (d. 1916)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Novelist\" title=\"Novelist\">novelist</a> and <a href=\"https://wikipedia.org/wiki/Short_story\" title=\"Short story\">short story</a> writer (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Novelist\" title=\"Novelist\">novelist</a> and <a href=\"https://wikipedia.org/wiki/Short_story\" title=\"Short story\">short story</a> writer (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Novelist", "link": "https://wikipedia.org/wiki/Novelist"}, {"title": "Short story", "link": "https://wikipedia.org/wiki/Short_story"}]}, {"year": "1852", "text": "<PERSON>, Curaçaoan-American journalist and politician (d. 1914)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Curaçaoan-American journalist and politician (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Curaçaoan-American journalist and politician (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, American lawyer and activist (d. 1929)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, English painter and critic (d. 1934)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and critic (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and critic (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Austrian lawyer and politician, 4th President of Austria (d. 1950)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Austria\" title=\"President of Austria\">President of Austria</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Austria\" title=\"President of Austria\">President of Austria</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Austria", "link": "https://wikipedia.org/wiki/President_of_Austria"}]}, {"year": "1881", "text": "<PERSON>, American actress and producer (d. 1956)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Greek pianist and composer (d. 1962)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Man<PERSON>_<PERSON>is\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek pianist and composer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>is\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek pianist and composer (d. 1962)", "links": [{"title": "Man<PERSON>", "link": "https://wikipedia.org/wiki/Man<PERSON>_Ka<PERSON>miris"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese martial artist, developed aikido (d. 1969)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>rihei_Ueshiba\" title=\"<PERSON><PERSON><PERSON><PERSON> Ueshiba\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese martial artist, developed <a href=\"https://wikipedia.org/wiki/Aikido\" title=\"Aikido\">aikido</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>i_Ueshiba\" title=\"<PERSON><PERSON><PERSON><PERSON> Ueshiba\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese martial artist, developed <a href=\"https://wikipedia.org/wiki/Aikido\" title=\"Aikido\">aikido</a> (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>i_Ueshiba"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>do"}]}, {"year": "1884", "text": "<PERSON>, American actress and playwright (d. 1950)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Argentinian painter and sculptor (d. 1963)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Xul_Solar\" title=\"Xul Solar\"><PERSON><PERSON></a>, Argentinian painter and sculptor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xul_Solar\" title=\"Xul Solar\"><PERSON><PERSON></a>, Argentinian painter and sculptor (d. 1963)", "links": [{"title": "Xul Solar", "link": "https://wikipedia.org/wiki/Xul_Solar"}]}, {"year": "1894", "text": "<PERSON>, Estonian-American painter and carpenter (d. 1974)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-American painter and carpenter (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-American painter and carpenter (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON> of the United Kingdom (d. 1952)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI\" title=\"George VI\"><PERSON></a> of the United Kingdom (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI\" title=\"George VI\"><PERSON></a> of the United Kingdom (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, French poet and author (d. 1952)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89luard\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89luard\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%89luard"}]}, {"year": "1896", "text": "<PERSON>, American general and pilot, Medal of Honor recipient (d. 1993)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1897", "text": "<PERSON>, Italian-Austrian lawyer and politician, 15th Federal Chancellor of Austria (d. 1977)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Austrian lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Federal_Chancellor_of_Austria\" class=\"mw-redirect\" title=\"Federal Chancellor of Austria\">Federal Chancellor of Austria</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Austrian lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Federal_Chancellor_of_Austria\" class=\"mw-redirect\" title=\"Federal Chancellor of Austria\">Federal Chancellor of Austria</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Chancellor of Austria", "link": "https://wikipedia.org/wiki/Federal_Chancellor_of_Austria"}]}, {"year": "1897", "text": "<PERSON>, American educator and politician (d. 1995)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, American Hall of Fame country and blues musician (d. 1982)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Country_Music_Hall_of_Fame\" class=\"mw-redirect\" title=\"Country Music Hall of Fame\">Hall of Fame</a> country and blues musician (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Country_Music_Hall_of_Fame\" class=\"mw-redirect\" title=\"Country Music Hall of Fame\">Hall of Fame</a> country and blues musician (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Country Music Hall of Fame", "link": "https://wikipedia.org/wiki/Country_Music_Hall_of_Fame"}]}, {"year": "1901", "text": "<PERSON>, French tennis player (d. 1987)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON> of Greece (d. 1964)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> (d. 1964)", "links": [{"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/<PERSON>_of_Greece"}]}, {"year": "1902", "text": "<PERSON>, American actress (d. 1989)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Austrian philosopher from the Vienna Circle (d. 1988)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher from the Vienna Circle (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher from the Vienna Circle (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English sprinter (d. 1982)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American civil rights activist (d. 2003)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Virginia_Coffey\" title=\"Virginia Coffey\"><PERSON></a>, American civil rights activist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Coffey\" title=\"Virginia Coffey\"><PERSON></a>, American civil rights activist (d. 2003)", "links": [{"title": "Virginia Coffey", "link": "https://wikipedia.org/wiki/Virginia_Coffey"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American actor, singer, and screenwriter (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Morey_Amsterdam\" title=\"Morey Amsterdam\"><PERSON><PERSON> Amsterdam</a>, American actor, singer, and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morey_Amsterdam\" title=\"Morey Amsterdam\"><PERSON><PERSON> Amsterdam</a>, American actor, singer, and screenwriter (d. 1996)", "links": [{"title": "Morey Amsterdam", "link": "https://wikipedia.org/wiki/Morey_Amsterdam"}]}, {"year": "1908", "text": "<PERSON>, Welsh rugby player (d. 2001)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Hungarian journalist, author, and screenwriter (d. 2007)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/M%C3%A1ria_Szepes\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian journalist, author, and screenwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1ria_Szepes\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian journalist, author, and screenwriter (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1ria_Szepes"}]}, {"year": "1909", "text": "<PERSON>, American geneticist and academic, Nobel Prize laureate (d. 1975)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Greek-Polish swimmer and water polo player (d. 1943)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>owicz\" title=\"<PERSON><PERSON><PERSON>jnowicz\"><PERSON><PERSON><PERSON></a>, Greek-Polish swimmer and water polo player (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Polish swimmer and water polo player (d. 1943)", "links": [{"title": "Jerzy Iwanow-Szajnowicz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Iwanow-<PERSON>owicz"}]}, {"year": "1911", "text": "<PERSON>, American singer and bandleader (d. 1965)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bandleader (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bandleader (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, German-American physicist and engineer (d. 1998)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and engineer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and engineer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, German lieutenant and politician, 5th President of the Federal Republic of Germany (d. 1992)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_Federal_Republic_of_Germany\" class=\"mw-redirect\" title=\"President of the Federal Republic of Germany\">President of the Federal Republic of Germany</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_Federal_Republic_of_Germany\" class=\"mw-redirect\" title=\"President of the Federal Republic of Germany\">President of the Federal Republic of Germany</a> (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the Federal Republic of Germany", "link": "https://wikipedia.org/wiki/President_of_the_Federal_Republic_of_Germany"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American pianist and harpsichord player (d. 2003)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1915", "text": "<PERSON>, American dancer and actor (d. 1978)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and actor (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American novelist and short story writer (d. 1965)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>.<PERSON><PERSON><PERSON>, Swedish author and politician (d. 2016)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish author and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish author and politician (d. 2016)", "links": [{"title": "C<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American actress and fashion designer (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and fashion designer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and fashion designer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American dancer and choreographer (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"June <PERSON>\"><PERSON></a>, American dancer and choreographer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American broadcaster (d. 1994)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Romanian actor and director (d. 2016)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian actor and director (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian actor and director (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Indian yoga instructor and author, founded Iyengar Yoga (d. 2014)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/B._K._S._Iyengar\" title=\"B. K. S. Iyengar\"><PERSON><PERSON> K<PERSON> <PERSON><PERSON></a>, Indian yoga instructor and author, founded <a href=\"https://wikipedia.org/wiki/Iyengar_Yoga\" title=\"Iyengar Yoga\">Iyengar Yoga</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B._K._S._Iyengar\" title=\"B. K. S. Iyengar\">B. K. S<PERSON> Iyenga<PERSON></a>, Indian yoga instructor and author, founded <a href=\"https://wikipedia.org/wiki/Iyengar_Yoga\" title=\"Iyengar Yoga\">Iyengar Yoga</a> (d. 2014)", "links": [{"title": "B. K. S. Iyengar", "link": "https://wikipedia.org/wiki/B._K._S._Iyengar"}, {"title": "Iyengar Yoga", "link": "https://wikipedia.org/wiki/Iyengar_Yoga"}]}, {"year": "1920", "text": "<PERSON>, American trumpet player, composer, and educator (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and educator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and educator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Russian physicist and academic, Nobel Prize laureate (d. 2001)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1922", "text": "<PERSON>, American journalist and producer, created 60 Minutes (d. 2009)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer, created <i><a href=\"https://wikipedia.org/wiki/60_Minutes\" title=\"60 Minutes\">60 Minutes</a></i> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer, created <i><a href=\"https://wikipedia.org/wiki/60_Minutes\" title=\"60 Minutes\">60 Minutes</a></i> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "60 Minutes", "link": "https://wikipedia.org/wiki/60_Minutes"}]}, {"year": "1922", "text": "<PERSON> <PERSON><PERSON>, American sergeant, Medal of Honor recipient (d. 1984)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Junior_<PERSON><PERSON>_<PERSON>\" title=\"Junior J<PERSON>rier\">Junior <PERSON><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Junior_<PERSON><PERSON>_<PERSON>\" title=\"Junior J. Spurrier\">Junior <PERSON><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1984)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1923", "text": "<PERSON>, Dutch-Belgian author and poet (d. 2006)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Belgian author and poet (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Belgian author and poet (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Indian actor, director, and producer (d. 1988)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor, director, and producer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor, director, and producer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American tenor and actor (d. 1998)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Dutch lawyer and politician, Dutch Minister of the Interior (d. 1986)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_and_Kingdom_Relations\" title=\"Ministry of the Interior and Kingdom Relations\">Dutch Minister of the Interior</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_and_Kingdom_Relations\" title=\"Ministry of the Interior and Kingdom Relations\">Dutch Minister of the Interior</a> (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of the Interior and Kingdom Relations", "link": "https://wikipedia.org/wiki/Ministry_of_the_Interior_and_Kingdom_Relations"}]}, {"year": "1929", "text": "<PERSON>, New Zealand rugby player (d. 1977)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, South African author (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English geographer, anthropologist, and archaeologist (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(geographer)\" title=\"<PERSON> (geographer)\"><PERSON></a>, English geographer, anthropologist, and archaeologist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(geographer)\" title=\"<PERSON> (geographer)\"><PERSON></a>, English geographer, anthropologist, and archaeologist (d. 2013)", "links": [{"title": "<PERSON> (geographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(geographer)"}]}, {"year": "1931", "text": "<PERSON>, Pakistani philosopher, poet, and scholar (d. 2002)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani philosopher, poet, and scholar (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani philosopher, poet, and scholar (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Estonian director and politician (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian director and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian director and politician (d. 2015)", "links": [{"title": "Vladimir<PERSON><PERSON>Orgus<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor and playwright (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American actress, singer, and dancer", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Abbe Lane\"><PERSON><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Abbe Lane\"><PERSON><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1995)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rich\"><PERSON></a>, American singer-songwriter and guitarist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Indian director and screenwriter (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>al\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and screenwriter (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hya<PERSON>_Benegal"}]}, {"year": "1934", "text": "<PERSON>, American guitarist and singer (d. 2006)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American guitarist and singer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American guitarist and singer (d. 2006)", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(guitarist)"}]}, {"year": "1935", "text": "<PERSON>, American actor, producer, and screenwriter (d. 2001)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actress (d. 1991)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Brazilian theologian and author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian theologian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian theologian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Barbadian cricketer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English academic and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American football player (d. 1963)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Scottish footballer and civil servant", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Lex_Gold\" title=\"Lex Gold\"><PERSON></a>, Scottish footballer and civil servant", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lex_Gold\" title=\"Lex Gold\"><PERSON></a>, Scottish footballer and civil servant", "links": [{"title": "Lex Gold", "link": "https://wikipedia.org/wiki/Lex_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American soprano and actress (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American journalist, critic, and academic (d. 2006)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, critic, and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, critic, and academic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English actor and director (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and director (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and director (d. 2014)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, English writer (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English writer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English writer (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>croft"}]}, {"year": "1943", "text": "<PERSON>, Scottish politician (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American journalist, author, and publisher, founded The American Spectator", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist, author, and publisher, founded <i><a href=\"https://wikipedia.org/wiki/The_American_Spectator\" title=\"The American Spectator\">The American Spectator</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist, author, and publisher, founded <i><a href=\"https://wikipedia.org/wiki/The_American_Spectator\" title=\"The American Spectator\">The American Spectator</a></i>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>"}, {"title": "The American Spectator", "link": "https://wikipedia.org/wiki/The_American_Spectator"}]}, {"year": "1944", "text": "<PERSON>, Baron <PERSON>, English businessman, founded DFS", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/DFS_(British_retailer)\" class=\"mw-redirect\" title=\"DFS (British retailer)\">DFS</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/DFS_(British_retailer)\" class=\"mw-redirect\" title=\"DFS (British retailer)\">DFS</a>", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "DFS (British retailer)", "link": "https://wikipedia.org/wiki/DFS_(British_retailer)"}]}, {"year": "1944", "text": "<PERSON>, English professional footballer murdered in the 2015 Sousse attacks (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English professional footballer murdered in the <a href=\"https://wikipedia.org/wiki/2015_Sousse_attacks\" title=\"2015 Sousse attacks\">2015 Sousse attacks</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English professional footballer murdered in the <a href=\"https://wikipedia.org/wiki/2015_Sousse_attacks\" title=\"2015 Sousse attacks\">2015 Sousse attacks</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2015 Sousse attacks", "link": "https://wikipedia.org/wiki/2015_Sousse_attacks"}]}, {"year": "1946", "text": "<PERSON>, English historian and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English-French actress and singer (d. 2023)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French actress and singer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French actress and singer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English conductor and composer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actress (d. 2016)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Duke\"><PERSON></a>, American actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Duke\"><PERSON></a>, American actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, German javelin thrower and politician (d. 2023)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German javelin thrower and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German javelin thrower and politician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Scottish footballer (d. 2021)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American talent agent, co-founded Creative Artists Agency", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talent agent, co-founded <a href=\"https://wikipedia.org/wiki/Creative_Artists_Agency\" title=\"Creative Artists Agency\">Creative Artists Agency</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talent agent, co-founded <a href=\"https://wikipedia.org/wiki/Creative_Artists_Agency\" title=\"Creative Artists Agency\">Creative Artists Agency</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Creative Artists Agency", "link": "https://wikipedia.org/wiki/Creative_Artists_Agency"}]}, {"year": "1946", "text": "<PERSON>, American tennis player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American actress (d. 2025)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2025)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American guitarist and educator", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Brazilian economist and politician, 36th President of Brazil", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian economist and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian economist and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1948", "text": "<PERSON>, American journalist and author (d. 1982)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian politician and diplomat, 9th Deputy Prime Minister of Australia", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician and diplomat, 9th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia\" title=\"Deputy Prime Minister of Australia\">Deputy Prime Minister of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician and diplomat, 9th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia\" title=\"Deputy Prime Minister of Australia\">Deputy Prime Minister of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch author, poet, and television host (d. 2002)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Boudewijn_B%C3%BCch\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch author, poet, and television host (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boudewijn_B%C3%BCch\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch author, poet, and television host (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Boudewijn_B%C3%BCch"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Estonian lawyer and politician (d. 2011)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and politician (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American baseball player and manager (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American artist and illustrator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Australian bass player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American basketball player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1951)\" title=\"<PERSON> (basketball, born 1951)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1951)\" title=\"<PERSON> (basketball, born 1951)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1951)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1951)"}]}, {"year": "1951", "text": "<PERSON>, Dutch chess player and author", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chess player and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chess player and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor, saxophonist, painter, director, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, saxophonist, painter, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, saxophonist, painter, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Indian tennis player and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian anthropologist, author, and photographer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anthropologist)\" title=\"<PERSON> (anthropologist)\"><PERSON></a>, Canadian anthropologist, author, and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(anthropologist)\" title=\"<PERSON> (anthropologist)\"><PERSON></a>, Canadian anthropologist, author, and photographer", "links": [{"title": "<PERSON> (anthropologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(anthropologist)"}]}, {"year": "1953", "text": "<PERSON>, Estonian composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_E<PERSON><PERSON>e"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Greek lawyer and politician, 4th Greek Minister for National Defence", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Minister_for_National_Defence_(Greece)\" title=\"Minister for National Defence (Greece)\">Greek Minister for National Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Minister_for_National_Defence_(Greece)\" title=\"Minister for National Defence (Greece)\">Greek Minister for National Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister for National Defence (Greece)", "link": "https://wikipedia.org/wiki/Minister_for_National_Defence_(Greece)"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Swedish soldier and politician, 29th Swedish Minister for Defence", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish soldier and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)\" title=\"Minister for Defence (Sweden)\">Swedish Minister for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish soldier and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)\" title=\"Minister for Defence (Sweden)\">Swedish Minister for Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister for Defence (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)"}]}, {"year": "1954", "text": "<PERSON>, American race car driver (d. 1993)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian physicist and astronaut", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, Canadian physicist and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, Canadian physicist and astronaut", "links": [{"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)"}]}, {"year": "1955", "text": "<PERSON>, Australian golfer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American mathematician and academic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Scottish politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, German skier", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_musician)\" title=\"<PERSON> (Scottish musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_musician)\" title=\"<PERSON> (Scottish musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON> (Scottish musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Scottish_musician)"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stacy\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stacy\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American-Canadian bodybuilder and actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian bodybuilder and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian bodybuilder and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bob_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Mexican boxer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American lawyer, 7th Director of the Federal Bureau of Investigation", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, 7th <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">Director of the Federal Bureau of Investigation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, 7th <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">Director of the Federal Bureau of Investigation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Director of the Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation"}]}, {"year": "1960", "text": "<PERSON>, American actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English footballer, manager, and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American sprinter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Pat<PERSON>_<PERSON>dstr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dstr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patrik_Sundstr%C3%B6m"}]}, {"year": "1963", "text": "<PERSON>, English footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1963", "text": "<PERSON>, American basketball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1963", "text": "<PERSON>, German discus thrower", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German discus thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress and model", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American baseball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Big<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American baseball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1965", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian race car driver", "html": "1966 - <a href=\"https://wikipedia.org/wiki/F<PERSON>riz<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F<PERSON>riz<PERSON>_<PERSON>rdi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabrizio_G<PERSON>rdi"}]}, {"year": "1966", "text": "<PERSON>, Trinidadian-Venezuelan basketball player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-Venezuelan basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-Venezuelan basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American basketball player (d. 2015)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2015)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Swedish bass player and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Tim_Sk%C3%B6ld\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swedish bass player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tim_Sk%C3%B6ld\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swedish bass player and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tim_Sk%C3%B6ld"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Danish academic and politician, 41st Prime Minister of Denmark", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish academic and politician, 41st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish academic and politician, 41st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "Prime Minister of Denmark", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Denmark"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Polish author", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Ewa_Bia%C5%82o%C5%82%C4%99cka\" title=\"Ewa Białołęcka\"><PERSON><PERSON> Białołęcka</a>, Polish author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ewa_Bia%C5%82o%C5%82%C4%99cka\" title=\"Ewa Białołęcka\"><PERSON><PERSON> Białołęcka</a>, Polish author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ewa_Bia%C5%82o%C5%82%C4%99cka"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Norwegian high jumper and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian high jumper and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian high jumper and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Egyptian actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Egyptian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Egyptian actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1969", "text": "<PERSON>, American baseball player and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, English-Irish actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Irish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Irish actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian baseball player and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Dutch footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Polish singer-songwriter, pianist, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish singer-songwriter, pianist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish singer-songwriter, pianist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ton\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actor and comedian", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, German hurdler", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>alk_<PERSON>\" title=\"Falk Balzer\"><PERSON><PERSON></a>, German hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>alk_<PERSON>\" title=\"Falk Balzer\"><PERSON><PERSON></a>, German hurdler", "links": [{"title": "Falk Balzer", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zer"}]}, {"year": "1973", "text": "<PERSON>, Irish basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Canadian soccer player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Lithuanian basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%A0tombergas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%A0tombergas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saulius_%C5%A0tombergas"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress and singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American bounty hunter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bounty hunter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bounty hunter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, French-German rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-German rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Portuguese race car driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Couto"}]}, {"year": "1976", "text": "<PERSON>, Spanish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Santiago_Ezquerro\" title=\"Santiago Ezquerro\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Ezquerro\" title=\"Santiago Ezquerro\"><PERSON></a>, Spanish footballer", "links": [{"title": "Santiago Ezquerro", "link": "https://wikipedia.org/wiki/Santiago_Ezquerro"}]}, {"year": "1977", "text": "<PERSON>, Australian-Jamaican cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Jamaican cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Jamaican cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English rugby player and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian footballer and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Kenyan runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/She<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"She<PERSON><PERSON>\">She<PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/She<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Shedra<PERSON>\">She<PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Zden%C4%9Bk_Posp%C4%9Bch\" title=\"Zdeněk Pospěch\">Zdeněk Pospěch</a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zden%C4%9Bk_Posp%C4%9Bch\" title=\"Zdeněk Pospěch\">Zdeněk Pospěch</a>, Czech footballer", "links": [{"title": "Zdeněk Pospěch", "link": "https://wikipedia.org/wiki/Zden%C4%9Bk_Posp%C4%9Bch"}]}, {"year": "1978", "text": "<PERSON>, Swiss tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kim_<PERSON>-Pierre\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kim_St-Pierre"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Estonian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English-Australian singer-songwriter and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer and sportscaster", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Slovenian opera stage director and designer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(opera_stage_director)\" title=\"<PERSON><PERSON><PERSON> (opera stage director)\"><PERSON><PERSON><PERSON></a>, Slovenian opera stage director and designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(opera_stage_director)\" title=\"<PERSON><PERSON><PERSON> (opera stage director)\"><PERSON><PERSON><PERSON></a>, Slovenian opera stage director and designer", "links": [{"title": "<PERSON><PERSON><PERSON> (opera stage director)", "link": "https://wikipedia.org/wiki/<PERSON>oc<PERSON>_(opera_stage_director)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Swedish race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Thed_Bj%C3%B6rk\" title=\"Thed Björk\"><PERSON><PERSON> Björk</a>, Swedish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thed_Bj%C3%B6rk\" title=\"Thed Björk\"><PERSON><PERSON>j<PERSON></a>, Swedish race car driver", "links": [{"title": "Thed Björk", "link": "https://wikipedia.org/wiki/Thed_Bj%C3%B6rk"}]}, {"year": "1980", "text": "<PERSON>, Scottish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Ivorian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Malaysian model", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}]}, {"year": "1981", "text": "<PERSON>, American wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Irish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (infielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English singer and actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anthony Way\"><PERSON></a>, English singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anthony Way\"><PERSON></a>, English singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anthony_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, English singer-songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Northern Irish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Indian actor and producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Zimbabwean cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor, singer, and musician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rat<PERSON>bone\"><PERSON></a>, American actor, singer, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>bone\"><PERSON></a>, American actor, singer, and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Jakub_B%C5%82as<PERSON><PERSON><PERSON>owski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jakub_B%C5%82as<PERSON><PERSON><PERSON>owski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jakub_B%C5%82<PERSON><PERSON><PERSON><PERSON>owski"}]}, {"year": "1985", "text": "<PERSON>, Welsh keyboard player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English-Welsh rugby player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union_player_born_1985)\" class=\"mw-redirect\" title=\"<PERSON> (rugby union player born 1985)\"><PERSON></a>, English-Welsh rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union_player_born_1985)\" class=\"mw-redirect\" title=\"<PERSON> (rugby union player born 1985)\"><PERSON></a>, English-Welsh rugby player", "links": [{"title": "<PERSON> (rugby union player born 1985)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union_player_born_1985)"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese actress and singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ami_Ta<PERSON>zawa"}]}, {"year": "1987", "text": "<PERSON>, Belizean-American hurdler", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belizean-American hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belizean-American hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, French basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Japanese baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Brazilian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1989)\" title=\"<PERSON> (footballer, born 1989)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1989)\" title=\"<PERSON> (footballer, born 1989)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1989)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1989)"}]}, {"year": "1989", "text": "<PERSON>, English rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, South Korean singer-songwriter and dancer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Onew\" title=\"Onew\">Onew</a>, South Korean singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Onew\" title=\"Onew\">Onew</a>, South Korean singer-songwriter and dancer", "links": [{"title": "Onew", "link": "https://wikipedia.org/wiki/Onew"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, New Zealand rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "Offset, American rapper", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Offset_(rapper)\" title=\"Offset (rapper)\">Offset</a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Offset_(rapper)\" title=\"Offset (rapper)\">Offset</a>, American rapper", "links": [{"title": "Offset (rapper)", "link": "https://wikipedia.org/wiki/Offset_(rapper)"}]}, {"year": "1992", "text": "<PERSON>, American singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Italian race car driver", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Russian ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, South African swimmer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Chinese figure skater", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Zijun\"><PERSON></a>, Chinese figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Zijun\"><PERSON></a>, Chinese figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_Z<PERSON>jun"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/DK_Metcalf\" title=\"DK Metcalf\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DK_Metcalf\" title=\"DK Metcalf\"><PERSON><PERSON></a>, American football player", "links": [{"title": "DK Metcalf", "link": "https://wikipedia.org/wiki/DK_Metcalf"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> IV\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> IV\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, South Korean singer and actor", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woon<PERSON>\" title=\"<PERSON>woon<PERSON>\"><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woong\" title=\"<PERSON>woon<PERSON>\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-woong"}]}, {"year": "2001", "text": "<PERSON>, American actor and activist", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rush\"><PERSON></a>, American actor and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joshua Rush\"><PERSON></a>, American actor and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Portuguese footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Francisco_Concei%C3%A7%C3%A3o\" title=\"Francisco Conceição\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Concei%C3%A7%C3%A3o\" title=\"Francisco Conceição\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Concei%C3%A7%C3%A3o"}]}], "Deaths": [{"year": "618", "text": "<PERSON><PERSON>, emperor of Qin", "html": "618 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Qin", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Qin", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "648", "text": "<PERSON> of the Sedre, Syriac Orthodox Patriarch of Antioch", "html": "648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Sedre\" title=\"<PERSON> III of the Sedre\"><PERSON> of the Sedre</a>, Syriac Orthodox Patriarch of Antioch", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Sedre\" title=\"<PERSON> III of the Sedre\"><PERSON> of the Sedre</a>, Syriac Orthodox Patriarch of Antioch", "links": [{"title": "<PERSON> of the Sedre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_<PERSON>_<PERSON>"}]}, {"year": "704", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Northumbria (or 705)", "html": "704 - <a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON><PERSON>_of_Northumbria\" title=\"<PERSON><PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Northumbria\" title=\"Northumbria\">Northumbria</a> (or <a href=\"https://wikipedia.org/wiki/705\" title=\"705\">705</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON><PERSON>_of_Northumbria\" title=\"<PERSON><PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Northumbria\" title=\"Northumbria\">Northumbria</a> (or <a href=\"https://wikipedia.org/wiki/705\" title=\"705\">705</a>)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/Aldfrith_of_Northumbria"}, {"title": "Northumbria", "link": "https://wikipedia.org/wiki/Northumbria"}, {"title": "705", "link": "https://wikipedia.org/wiki/705"}]}, {"year": "872", "text": "<PERSON> (b. 792)", "html": "872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Adrian <PERSON>\"><PERSON> <PERSON> II</a> (b. 792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Adrian <PERSON>\"><PERSON></a> (b. 792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1077", "text": "<PERSON> of Poitou, Holy Roman Empress  and regent (b. c. 1025)", "html": "1077 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Poitou\" title=\"<PERSON> of Poitou\"><PERSON> of Poitou</a>, Holy Roman Empress and regent (b. c. 1025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Poitou\" title=\"<PERSON> of Poitou\"><PERSON> of Poitou</a>, Holy Roman Empress and regent (b. c. 1025)", "links": [{"title": "Agnes of Poitou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Poitou"}]}, {"year": "1293", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Mamluk sultan of Egypt", "html": "1293 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mamluk sultan of Egypt", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mamluk sultan of Egypt", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1311", "text": "<PERSON>, German queen consort (b. 1276)", "html": "1311 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Brabant\"><PERSON> B<PERSON>ant</a>, German queen consort (b. 1276)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Brabant\"><PERSON> of B<PERSON>ant</a>, German queen consort (b. 1276)", "links": [{"title": "<PERSON> of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1332", "text": "<PERSON><PERSON><PERSON><PERSON>, Mongolian emperor (b. 1326)", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian emperor (b. 1326)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian emperor (b. 1326)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1359", "text": "<PERSON><PERSON><PERSON> <PERSON>, Lord of Verona (b. 1332)", "html": "1359 - <a href=\"https://wikipedia.org/wiki/Cangrande_II_della_Scala\" title=\"Cangrande II della Scala\">Cangra<PERSON> <PERSON> della Scala</a>, <PERSON> of Verona (b. 1332)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cangrande_II_della_Scala\" title=\"Cangrande II della Scala\">Cangra<PERSON> II della Scala</a>, Lord of Verona (b. 1332)", "links": [{"title": "Cangrande II della Scala", "link": "https://wikipedia.org/wiki/Cangrande_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1417", "text": "<PERSON>, English Lollard leader", "html": "1417 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Lollard leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Lollard leader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1460", "text": "<PERSON><PERSON><PERSON>, Italian scholar and translator (b. 1370)", "html": "1460 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_da_Verona\" title=\"<PERSON><PERSON><PERSON> da Verona\"><PERSON><PERSON><PERSON> Verona</a>, Italian scholar and translator (b. 1370)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_da_Verona\" title=\"<PERSON><PERSON><PERSON> da Verona\"><PERSON><PERSON><PERSON></a>, Italian scholar and translator (b. 1370)", "links": [{"title": "<PERSON><PERSON><PERSON> da Verona", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_da_Verona"}]}, {"year": "1480", "text": "<PERSON><PERSON><PERSON><PERSON>, humanist scholar (b. 1429)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, humanist scholar (b. 1429)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, humanist scholar (b. 1429)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>"}]}, {"year": "1503", "text": "<PERSON><PERSON> the Elder, regent of Sweden (b. 1440)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_the_Elder\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the Elder</a>, regent of Sweden (b. 1440)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_the_Elder\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the Elder</a>, regent of Sweden (b. 1440)", "links": [{"title": "<PERSON><PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_the_Elder"}]}, {"year": "1510", "text": "<PERSON> of Saxony (b. 1473)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxony_(Teutonic_Knight)\" title=\"<PERSON> of Saxony (Teutonic Knight)\"><PERSON> of Saxony</a> (b. 1473)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxony_(Teutonic_Knight)\" title=\"<PERSON> of Saxony (Teutonic Knight)\"><PERSON> of Saxony</a> (b. 1473)", "links": [{"title": "<PERSON> of Saxony (Teutonic Knight)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxony_(Teutonic_Knight)"}]}, {"year": "1542", "text": "<PERSON> of Scotland (b. 1512)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/James_V_of_Scotland\" class=\"mw-redirect\" title=\"James V of Scotland\"><PERSON> V of Scotland</a> (b. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/James_V_of_Scotland\" class=\"mw-redirect\" title=\"James V of Scotland\"><PERSON> V of Scotland</a> (b. 1512)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/James_V_of_Scotland"}]}, {"year": "1591", "text": "<PERSON> of the Cross, Spanish priest and saint (b. 1542)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Cross\" title=\"John of the Cross\"><PERSON> the Cross</a>, Spanish priest and saint (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Cross\" title=\"<PERSON> of the Cross\"><PERSON> the Cross</a>, Spanish priest and saint (b. 1542)", "links": [{"title": "John of the Cross", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1595", "text": "<PERSON>, 3rd Earl of Huntingdon (b. 1535)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Huntingdon\" title=\"<PERSON>, 3rd Earl of Huntingdon\"><PERSON>, 3rd Earl of Huntingdon</a> (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Huntingdon\" title=\"<PERSON>, 3rd Earl of Huntingdon\"><PERSON>, 3rd Earl of Huntingdon</a> (b. 1535)", "links": [{"title": "<PERSON>, 3rd Earl of Huntingdon", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Huntingdon"}]}, {"year": "1624", "text": "<PERSON>, 1st Earl of Nottingham, English politician, Lord High Admiral of England (b. 1536)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Nottingham\" title=\"<PERSON>, 1st Earl of Nottingham\"><PERSON>, 1st Earl of Nottingham</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Admiral_of_England\" class=\"mw-redirect\" title=\"Lord High Admiral of England\">Lord High Admiral of England</a> (b. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Nottingham\" title=\"<PERSON>, 1st Earl of Nottingham\"><PERSON>, 1st Earl of Nottingham</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Admiral_of_England\" class=\"mw-redirect\" title=\"Lord High Admiral of England\">Lord High Admiral of England</a> (b. 1536)", "links": [{"title": "<PERSON>, 1st Earl of Nottingham", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Nottingham"}, {"title": "Lord High Admiral of England", "link": "https://wikipedia.org/wiki/Lord_High_Admiral_of_England"}]}, {"year": "1651", "text": "<PERSON>, French historian and scholar (b. 1582)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scholar)\" title=\"<PERSON> (scholar)\"><PERSON></a>, French historian and scholar (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scholar)\" title=\"<PERSON> (scholar)\"><PERSON></a>, French historian and scholar (b. 1582)", "links": [{"title": "<PERSON> (scholar)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scholar)"}]}, {"year": "1715", "text": "<PERSON>, English archbishop (b. 1636)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1636)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1735", "text": "<PERSON>, English bishop and historian (b. 1674)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and historian (b. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and historian (b. 1674)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1741", "text": "<PERSON>, French historian and educator (b. 1661)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and educator (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and educator (b. 1661)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, Italian painter and engraver (b. 1727)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and engraver (b. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and engraver (b. 1727)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, German pianist and composer (b. 1714)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON> of Spain (b. 1716)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (b. 1716)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1799", "text": "<PERSON>,  American general and politician, 1st President of the United States (b. 1732)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George <PERSON>\"><PERSON></a>, American general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a>, American general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1732)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1831", "text": "<PERSON>, American businessman and politician, 5th Mayor of Cincinnati (b. 1765)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Cincinnati\" title=\"List of mayors of Cincinnati\">Mayor of Cincinnati</a> (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Cincinnati\" title=\"List of mayors of Cincinnati\">Mayor of Cincinnati</a> (b. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Cincinnati", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Cincinnati"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON>, Canadian physician (b. 1806)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ch%C3%A9nier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian physician (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9nier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian physician (b. 1806)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9nier"}]}, {"year": "1842", "text": "<PERSON>, king of several tribes around Cape Palmas", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, king of several tribes around <a href=\"https://wikipedia.org/wiki/Cape_Palmas\" title=\"Cape Palmas\">Cape Palmas</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, king of several tribes around <a href=\"https://wikipedia.org/wiki/Cape_Palmas\" title=\"Cape Palmas\">Cape Palmas</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Cape Palmas", "link": "https://wikipedia.org/wiki/Cape_Palmas"}]}, {"year": "1860", "text": "<PERSON>, 4th Earl of Aberdeen, Scottish-English politician, Prime Minister of the United Kingdom (b. 1784)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Aberdeen\" title=\"<PERSON>, 4th Earl of Aberdeen\"><PERSON>, 4th Earl of Aberdeen</a>, Scottish-English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Aberdeen\" title=\"<PERSON>, 4th Earl of Aberdeen\"><PERSON>, 4th Earl of Aberdeen</a>, Scottish-English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1784)", "links": [{"title": "<PERSON>, 4th Earl of Aberdeen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Aberdeen"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1861", "text": "<PERSON>, Prince Consort of the United Kingdom (b. 1819)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_Consort\" class=\"mw-redirect\" title=\"<PERSON>, Prince Consort\"><PERSON>, Prince Consort</a> of the United Kingdom (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_Consort\" class=\"mw-redirect\" title=\"<PERSON>, Prince <PERSON>\"><PERSON>, Prince Consort</a> of the United Kingdom (b. 1819)", "links": [{"title": "<PERSON>, Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_Consort"}]}, {"year": "1865", "text": "<PERSON>, Danish geologist and mineralogist (b. 1794)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish geologist and mineralogist (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish geologist and mineralogist (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Swiss-American zoologist and geologist (b. 1807)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American zoologist and geologist (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American zoologist and geologist (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "Princess <PERSON> of the United Kingdom (b. 1843)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Princess_Alice_of_the_United_Kingdom\" title=\"Princess Alice of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Alice_of_the_United_Kingdom\" title=\"Princess Alice of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (b. 1843)", "links": [{"title": "Princess <PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/Princess_Alice_of_the_United_Kingdom"}]}, {"year": "1912", "text": "Belgrave <PERSON>, English lieutenant and explorer (b. 1887)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Belgrave <PERSON>\">Belgrave <PERSON></a>, English lieutenant and explorer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Belgrave <PERSON>\">Bel<PERSON></a>, English lieutenant and explorer (b. 1887)", "links": [{"title": "Belgrave <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, French choreographer and girlfriend of <PERSON>", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French choreographer and girlfriend of <a href=\"https://wikipedia.org/wiki/<PERSON>_Picasso\" title=\"Pablo Picasso\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Go<PERSON>\"><PERSON></a>, French choreographer and girlfriend of <a href=\"https://wikipedia.org/wiki/<PERSON>_Picasso\" title=\"Pablo Picasso\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Welsh rugby player (b. 1889)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Welsh rugby player (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Welsh rugby player (b. 1889)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1920", "text": "<PERSON>, American football player (b. 1895)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Russian mathematician and academic (b. 1842)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, British admiral (b. 1855)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, British admiral (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, British admiral (b. 1855)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1935", "text": "<PERSON>, American author (b. 1902)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Filipino painter and educator (b. 1869)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_de_la_Rosa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino painter and educator (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_de_la_Rosa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino painter and educator (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabi%C3%A1<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Slovenian priest and politician, 10th Prime Minister of Yugoslavia (b. 1872)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1ec\" title=\"<PERSON>\"><PERSON></a>, Slovenian priest and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C5%A1ec\" title=\"<PERSON>\"><PERSON></a>, Slovenian priest and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ko<PERSON>%C5%A1ec"}, {"title": "Prime Minister of Yugoslavia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia"}]}, {"year": "1943", "text": "<PERSON>, American physician and businessman, co-invented corn flakes (b. 1852)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and businessman, co-invented <a href=\"https://wikipedia.org/wiki/Corn_flakes\" title=\"Corn flakes\">corn flakes</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and businessman, co-invented <a href=\"https://wikipedia.org/wiki/Corn_flakes\" title=\"Corn flakes\">corn flakes</a> (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Corn flakes", "link": "https://wikipedia.org/wiki/Corn_flakes"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Mexican actress (b. 1908)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Lu<PERSON>_V%C3%A9lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lupe_V%C3%A9lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lupe_V%C3%A9lez"}]}, {"year": "1947", "text": "<PERSON>, English lieutenant and politician, Prime Minister of the United Kingdom (b. 1867)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1947", "text": "<PERSON>, English-American 3rd General of The Salvation Army (b. 1864)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American 3rd <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American 3rd <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1953", "text": "<PERSON>, American author and academic (b. 1896)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Finnish lawyer and politician, 7th President of Finland (b. 1870)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (b. 1870)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American singer and pianist (b. 1924)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Dinah Washington\"><PERSON><PERSON></a>, American singer and pianist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dinah_<PERSON>\" title=\"Dinah Washington\"><PERSON><PERSON></a>, American singer and pianist (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dinah_Washington"}]}, {"year": "1964", "text": "<PERSON>, American actor (b. 1906)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, German judge and politician, German Reich Minister of Justice (b. 1876)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German judge and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Justice_(Germany)\" title=\"Federal Ministry of Justice (Germany)\">German Reich Minister of Justice</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German judge and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Justice_(Germany)\" title=\"Federal Ministry of Justice (Germany)\">German Reich Minister of Justice</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Ministry of Justice (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Justice_(Germany)"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi linguist and scholar (b. 1926)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Mufazzal_Haider_Chaudhury\" title=\"Mufazzal Haider Chaudhury\"><PERSON><PERSON><PERSON><PERSON> Chaudhury</a>, Bangladeshi linguist and scholar (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mu<PERSON><PERSON><PERSON>_Haider_Chaudhury\" title=\"Mufazzal Haider Chaudhury\"><PERSON><PERSON><PERSON><PERSON> Chaudhury</a>, Bangladeshi linguist and scholar (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Haider_Chaudhury"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Bangladeshi author, playwright, and critic (b. 1925)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi author, playwright, and critic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi author, playwright, and critic (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi journalist and author (b. 1927)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi journalist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi journalist and author (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American journalist and author (b. 1889)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English-American entertainer (b. 1894)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American entertainer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American entertainer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Spanish historian and diplomat, co-founded the College of Europe (b. 1886)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Salvador_de_Madariaga\" title=\"Salvador de Madariaga\"><PERSON> de Madariaga</a>, Spanish historian and diplomat, co-founded the <a href=\"https://wikipedia.org/wiki/College_of_Europe\" title=\"College of Europe\">College of Europe</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_de_Madariaga\" title=\"Salvador de Madariaga\"><PERSON> de Madariaga</a>, Spanish historian and diplomat, co-founded the <a href=\"https://wikipedia.org/wiki/College_of_Europe\" title=\"College of Europe\">College of Europe</a> (b. 1886)", "links": [{"title": "Salvador de Madariaga", "link": "https://wikipedia.org/wiki/Salvador_de_Madariaga"}, {"title": "College of Europe", "link": "https://wikipedia.org/wiki/College_of_Europe"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American baseball player and coach (b. 1929)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Elston Howard\"><PERSON><PERSON></a>, American baseball player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Howard\"><PERSON><PERSON></a>, American baseball player and coach (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Spanish poet and academic, Nobel Prize laureate (b. 1898)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ei<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1985", "text": "<PERSON>, Russian-Canadian activist, founded the Madonna House Apostolate (b. 1896)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Canadian activist, founded the <a href=\"https://wikipedia.org/wiki/Madonna_House_Apostolate\" title=\"Madonna House Apostolate\">Madonna House Apostolate</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Canadian activist, founded the <a href=\"https://wikipedia.org/wiki/Madonna_House_Apostolate\" title=\"Madonna House Apostolate\">Madonna House Apostolate</a> (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Madonna House Apostolate", "link": "https://wikipedia.org/wiki/Madonna_House_Apostolate"}]}, {"year": "1985", "text": "<PERSON>, American baseball player and coach (b. 1934)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American actor and stuntman (b. 1919)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and stuntman (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and stuntman (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Russian physicist and activist, Nobel Prize laureate (b. 1921)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1990", "text": "<PERSON>, Swiss author and playwright (b. 1921)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCrrenmatt\" title=\"<PERSON>\"><PERSON></a>, Swiss author and playwright (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCrrenmatt\" title=\"<PERSON>\"><PERSON></a>, Swiss author and playwright (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Friedrich_D%C3%BCrrenmatt"}]}, {"year": "1990", "text": "<PERSON>, French composer, pianist and lyricist (b. 1908)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer, pianist and lyricist (b. <a href=\"https://wikipedia.org/wiki/1908\" title=\"1908\">1908</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer, pianist and lyricist (b. <a href=\"https://wikipedia.org/wiki/1908\" title=\"1908\">1908</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1908", "link": "https://wikipedia.org/wiki/1908"}]}, {"year": "1991", "text": "<PERSON>, Japanese-English actor  (b. 1908)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-English actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-English actor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American football player (b. 1968)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American actress (b. 1905)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American soldier and politician, 36th Governor of Arkansas (b. 1910)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> F<PERSON>bus\"><PERSON><PERSON></a>, American soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> F<PERSON>bus\"><PERSON><PERSON></a>, American soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aubus"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "1995", "text": "<PERSON><PERSON> <PERSON><PERSON>, American soldier and author (b. 1922)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American soldier and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American soldier and author (b. 1922)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian poet and author (b. 1928)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, American actor and comedian (b. 1918)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and comedian (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and comedian (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American author (b. 1919)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian guitarist and songwriter (b. 1946)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and songwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor and comedian (b. 1924)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Fell\"><PERSON></a>, American actor and comedian (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norman_Fell\" title=\"Norman Fell\"><PERSON></a>, American actor and comedian (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON> <PERSON>, American lawyer, judge, and activist (b. 1928)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> <PERSON>.\"><PERSON><PERSON> <PERSON>.</a>, American lawyer, judge, and activist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> <PERSON>.\"><PERSON><PERSON> <PERSON>.</a>, American lawyer, judge, and activist (b. 1928)", "links": [{"title": "<PERSON><PERSON> <PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1998", "text": "<PERSON>, American philanthropist and politician, Mayor of Dallas (b. 1924)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Dallas\" title=\"Mayor of Dallas\">Mayor of Dallas</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Dallas\" title=\"Mayor of Dallas\">Mayor of Dallas</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Dallas", "link": "https://wikipedia.org/wiki/Mayor_of_Dallas"}]}, {"year": "2001", "text": "<PERSON><PERSON> <PERSON><PERSON>, German novelist, essayist, and poet  (b. 1944)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, German novelist, essayist, and poet (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, German novelist, essayist, and poet (b. 1944)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actress (b. 1925)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Filipino journalist and politician, 21st President of the Senate of the Philippines (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>le"}, {"title": "President of the Senate of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines"}]}, {"year": "2003", "text": "<PERSON>, American union leader and mobster (b. 1920)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American union leader and mobster (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American union leader and mobster (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American baseball player (b. 1934)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Filipino actor, director, producer, and politician (b. 1939)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Filipino actor, director, producer, and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Filipino actor, director, producer, and politician (b. 1939)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2006", "text": "<PERSON>, Sri Lankan-English strategist and negotiator (b. 1938)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan-English strategist and negotiator (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan-English strategist and negotiator (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Turkish-American composer and producer, co-founded Atlantic Records (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_E<PERSON>gun\" title=\"Ahmet Ertegun\"><PERSON><PERSON></a>, Turkish-American composer and producer, co-founded <a href=\"https://wikipedia.org/wiki/Atlantic_Records\" title=\"Atlantic Records\">Atlantic Records</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ah<PERSON>_E<PERSON>gun\" title=\"<PERSON><PERSON> Ertegun\"><PERSON><PERSON></a>, Turkish-American composer and producer, co-founded <a href=\"https://wikipedia.org/wiki/Atlantic_Records\" title=\"Atlantic Records\">Atlantic Records</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Atlantic Records", "link": "https://wikipedia.org/wiki/Atlantic_Records"}]}, {"year": "2006", "text": "<PERSON>, American actor and screenwriter (b. 1949)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (b. 1949)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2009", "text": "<PERSON>, English footballer and manager (b. 1934)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Alan_A%27Court\" title=\"Alan <PERSON>\"><PERSON></a>, English footballer and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alan_A%27Court\" title=\"Alan <PERSON>Court\"><PERSON></a>, English footballer and manager (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alan_A%27Court"}]}, {"year": "2010", "text": "<PERSON>, American politician, Mayor of Springfield (b. 1957)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Springfield,_Illinois\" title=\"List of mayors of Springfield, Illinois\">Mayor of Springfield</a> (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Springfield,_Illinois\" title=\"List of mayors of Springfield, Illinois\">Mayor of Springfield</a> (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Springfield, Illinois", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Springfield,_Illinois"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American actress (b. 1920)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actress (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actress (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English footballer (b. 1986)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)\" title=\"<PERSON> (footballer, born 1986)\"><PERSON></a>, English footballer (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)\" title=\"<PERSON> (footballer, born 1986)\"><PERSON></a>, English footballer (b. 1986)", "links": [{"title": "<PERSON> (footballer, born 1986)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)"}]}, {"year": "2011", "text": "<PERSON>, American author and illustrator (b. 1913)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Simon\"><PERSON></a>, American author and illustrator (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Simon\"><PERSON></a>, American author and illustrator (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American singer-songwriter (b. 1937)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Billie Jo Spears\"><PERSON></a>, American singer-songwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Billie Jo Spears\"><PERSON></a>, American singer-songwriter (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English general (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1923)\" title=\"<PERSON> (British Army officer, born 1923)\"><PERSON></a>, English general (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1923)\" title=\"<PERSON> (British Army officer, born 1923)\"><PERSON></a>, English general (b. 1923)", "links": [{"title": "<PERSON> (British Army officer, born 1923)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(British_Army_officer,_born_1923)"}]}, {"year": "2012", "text": "<PERSON>, American police officer and politician (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(North_Carolina_politician)\" title=\"<PERSON> (North Carolina politician)\"><PERSON></a>, American police officer and politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(North_Carolina_politician)\" title=\"<PERSON> (North Carolina politician)\"><PERSON></a>, American police officer and politician (b. 1950)", "links": [{"title": "<PERSON> (North Carolina politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(North_Carolina_politician)"}]}, {"year": "2012", "text": "<PERSON>, American educator (b. 1985)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (b. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American author (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian painter and illustrator (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian painter and illustrator (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian painter and illustrator (b. 1940)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English statistician and academic (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statistician and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statistician and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, British-Irish actor (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Toole\" title=\"<PERSON>\"><PERSON></a>, British-Irish actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Toole\" title=\"<PERSON>\"><PERSON></a>, British-Irish actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Toole"}]}, {"year": "2013", "text": "<PERSON>, American painter (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American zoologist and academic (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soprano and pianist (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and pianist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and pianist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Congolese politician, Prime Minister of the Democratic Republic of the Congo (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo\" title=\"Prime Minister of the Democratic Republic of the Congo\">Prime Minister of the Democratic Republic of the Congo</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo\" title=\"Prime Minister of the Democratic Republic of the Congo\">Prime Minister of the Democratic Republic of the Congo</a> (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American model, activist, game show panelist and television personality; Miss America 1945 (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, activist, game show panelist and television personality; <a href=\"https://wikipedia.org/wiki/Miss_America\" title=\"Miss America\">Miss America 1945</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, activist, game show panelist and television personality; <a href=\"https://wikipedia.org/wiki/Miss_America\" title=\"Miss America\">Miss America 1945</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Miss America", "link": "https://wikipedia.org/wiki/Miss_America"}]}, {"year": "2014", "text": "<PERSON>, American football player (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American soldier and politician (b. 1954)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>mor\" title=\"<PERSON> Sonmor\"><PERSON></a>, Canadian ice hockey player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sonmor\" title=\"<PERSON> Sonmor\"><PERSON></a>, Canadian ice hockey player and coach (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>mor"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and manager (b. 1963)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tyshchenko\" title=\"<PERSON><PERSON><PERSON> Tyshchenko\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tyshchenko\" title=\"<PERSON><PERSON><PERSON> Tyshchenko\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (b. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German-American businesswoman and philanthropist, founded the Lillian Vernon Company (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businesswoman)\" title=\"<PERSON> (businesswoman)\"><PERSON></a>, German-American businesswoman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(company)\" title=\"<PERSON> (company)\">Lillian Vernon Company</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businesswoman)\" title=\"<PERSON> (businesswoman)\"><PERSON></a>, German-American businesswoman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(company)\" title=\"<PERSON> (company)\">Lillian Vernon Company</a> (b. 1927)", "links": [{"title": "<PERSON> (businesswoman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(businesswoman)"}, {"title": "<PERSON> (company)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(company)"}]}, {"year": "2016", "text": "<PERSON>, Brazilian cardinal (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian cardinal (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian cardinal (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_Evaristo_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Welsh actor (b. 1927)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Welsh actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Welsh actor (b. 1927)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2017", "text": "<PERSON>, Chinese writer (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-chung\" title=\"<PERSON>-chung\"><PERSON></a>, Chinese writer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-chung\" title=\"<PERSON>-chung\"><PERSON>ung</a>, Chinese writer (b. 1928)", "links": [{"title": "<PERSON>ung", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-chung"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Mexican-American comedian and actor (b. 1956)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American comedian and actor (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American comedian and actor (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, French Football manager (b. 1947)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French Football manager (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French Football manager (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>,  American academic and literary critic (b. 1924)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and literary critic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and literary critic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Slovak writer (b. 1937)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak writer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak writer (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Turkish-Spanish billionaire businessman (b. 1953)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-Spanish billionaire businessman (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-Spanish billionaire businessman (b. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}]}}