{"date": "August 9", "url": "https://wikipedia.org/wiki/August_9", "data": {"Events": [{"year": "48 BC", "text": "<PERSON>'s Civil War: Battle of Pharsalus: <PERSON> decisively defeats <PERSON><PERSON><PERSON> at Pharsalus and <PERSON><PERSON><PERSON> flees to Egypt.", "html": "48 BC - 48 BC - <a href=\"https://wikipedia.org/wiki/Caesar%27s_Civil_War\" class=\"mw-redirect\" title=\"Caesar's Civil War\">Caesar's Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Pharsalus\" title=\"Battle of Pharsalus\">Battle of Pharsalus</a>: <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\"><PERSON> Caesar</a> decisively defeats <a href=\"https://wikipedia.org/wiki/Pompey\" title=\"Pompey\">Pompey</a> at <a href=\"https://wikipedia.org/wiki/Farsala\" title=\"Farsala\">Pharsalus</a> and <PERSON><PERSON><PERSON> flees to <a href=\"https://wikipedia.org/wiki/Ancient_Egypt\" title=\"Ancient Egypt\">Egypt</a>.", "no_year_html": "48 BC - <a href=\"https://wikipedia.org/wiki/Caesar%27s_Civil_War\" class=\"mw-redirect\" title=\"Caesar's Civil War\">Caesar's Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Pharsalus\" title=\"Battle of Pharsalus\">Battle of Pharsalus</a>: <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\"><PERSON> Caesar</a> decisively defeats <a href=\"https://wikipedia.org/wiki/Pompey\" title=\"Pompey\">Pompey</a> at <a href=\"https://wikipedia.org/wiki/Farsala\" title=\"Farsala\">Pharsalus</a> and <PERSON><PERSON><PERSON> flees to <a href=\"https://wikipedia.org/wiki/Ancient_Egypt\" title=\"Ancient Egypt\">Egypt</a>.", "links": [{"title": "<PERSON>'s Civil War", "link": "https://wikipedia.org/wiki/Caesar%27s_Civil_War"}, {"title": "Battle of Pharsalus", "link": "https://wikipedia.org/wiki/Battle_of_Pharsalus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pompey", "link": "https://wikipedia.org/wiki/Pompey"}, {"title": "Farsala", "link": "https://wikipedia.org/wiki/Farsala"}, {"title": "Ancient Egypt", "link": "https://wikipedia.org/wiki/Ancient_Egypt"}]}, {"year": "378", "text": "Gothic War: Battle of Adrianople: A large Roman army led by Emperor <PERSON><PERSON> is defeated by the Visigoths. <PERSON><PERSON> is killed along with over half of his army.", "html": "378 - <a href=\"https://wikipedia.org/wiki/Gothic_War_(376%E2%80%93382)\" title=\"Gothic War (376-382)\">Gothic War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Adrianople\" title=\"Battle of Adrianople\">Battle of Adrianople</a>: A large <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman</a> army led by Emperor <a href=\"https://wikipedia.org/wiki/Valens\" title=\"Valens\"><PERSON><PERSON></a> is defeated by the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a>. <PERSON><PERSON> is killed along with over half of his army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gothic_War_(376%E2%80%93382)\" title=\"Gothic War (376-382)\">Gothic War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Adrianople\" title=\"Battle of Adrianople\">Battle of Adrianople</a>: A large <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman</a> army led by Emperor <a href=\"https://wikipedia.org/wiki/Valens\" title=\"Valens\"><PERSON><PERSON></a> is defeated by the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a>. <PERSON><PERSON> is killed along with over half of his army.", "links": [{"title": "Gothic War (376-382)", "link": "https://wikipedia.org/wiki/Gothic_War_(376%E2%80%93382)"}, {"title": "Battle of Adrianople", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>le"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}, {"title": "Valens", "link": "https://wikipedia.org/wiki/Valens"}, {"title": "Visigoths", "link": "https://wikipedia.org/wiki/Visigoths"}]}, {"year": "1173", "text": "Construction of the campanile of the Cathedral of Pisa (now known as the Leaning Tower of Pisa) begins; it will take two centuries to complete.", "html": "1173 - Construction of the <a href=\"https://wikipedia.org/wiki/Campanile\" class=\"mw-redirect\" title=\"Campanile\">campanile</a> of the <a href=\"https://wikipedia.org/wiki/Cathedral_of_Pisa\" class=\"mw-redirect\" title=\"Cathedral of Pisa\">Cathedral of Pisa</a> (now known as the <a href=\"https://wikipedia.org/wiki/Leaning_Tower_of_Pisa\" title=\"Leaning Tower of Pisa\">Leaning Tower of Pisa</a>) begins; it will take two centuries to complete.", "no_year_html": "Construction of the <a href=\"https://wikipedia.org/wiki/Campanile\" class=\"mw-redirect\" title=\"Campanile\">campanile</a> of the <a href=\"https://wikipedia.org/wiki/Cathedral_of_Pisa\" class=\"mw-redirect\" title=\"Cathedral of Pisa\">Cathedral of Pisa</a> (now known as the <a href=\"https://wikipedia.org/wiki/Leaning_Tower_of_Pisa\" title=\"Leaning Tower of Pisa\">Leaning Tower of Pisa</a>) begins; it will take two centuries to complete.", "links": [{"title": "Camp<PERSON>le", "link": "https://wikipedia.org/wiki/<PERSON>anile"}, {"title": "Cathedral of Pisa", "link": "https://wikipedia.org/wiki/Cathedral_of_Pisa"}, {"title": "Leaning Tower of Pisa", "link": "https://wikipedia.org/wiki/Leaning_Tower_of_Pisa"}]}, {"year": "1329", "text": "Quilon, the first Indian Christian Diocese, is erected by Pope <PERSON>; the French-born <PERSON><PERSON> is appointed the first Bishop.", "html": "1329 - <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Quilon\" title=\"Roman Catholic Diocese of Quilon\">Quilon</a>, the first <a href=\"https://wikipedia.org/wiki/Christianity_in_India\" title=\"Christianity in India\">Indian Christian Diocese</a>, is erected by <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope John <PERSON>\">Pope <PERSON></a>; the French-born <PERSON><PERSON> is appointed the first Bishop.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Quilon\" title=\"Roman Catholic Diocese of Quilon\">Quilon</a>, the first <a href=\"https://wikipedia.org/wiki/Christianity_in_India\" title=\"Christianity in India\">Indian Christian Diocese</a>, is erected by <a href=\"https://wikipedia.org/wiki/<PERSON>_John_<PERSON>\" title=\"Pope John <PERSON>\">Pope <PERSON></a>; the French-born <PERSON><PERSON> is appointed the first Bishop.", "links": [{"title": "Roman Catholic Diocese of Quilon", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Quilon"}, {"title": "Christianity in India", "link": "https://wikipedia.org/wiki/Christianity_in_India"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1428", "text": "Sources cite biggest caravan trade between Podvisoki and Republic of Ragusa. Vlachs committed to Ragusan lord <PERSON><PERSON>, that they will with 600 horses deliver 1,500 modius of salt. Delivery was meant for <PERSON><PERSON><PERSON><PERSON>, and Vlachs price was half of delivered salt.", "html": "1428 - Sources cite biggest <a href=\"https://wikipedia.org/wiki/Caravan_(travellers)\" title=\"Caravan (travellers)\">caravan trade</a> between <a href=\"https://wikipedia.org/wiki/Podvisoki\" title=\"Podvisoki\">Podvisoki</a> and <a href=\"https://wikipedia.org/wiki/Republic_of_Ragusa\" title=\"Republic of Ragusa\">Republic of Ragusa</a>. <a href=\"https://wikipedia.org/wiki/Vlachs\" title=\"Vlachs\">Vlachs</a> committed to <a href=\"https://wikipedia.org/wiki/Ragusan_nobility\" title=\"Ragusan nobility\">Ragusan</a> lord <PERSON><PERSON>, that they will with 600 <a href=\"https://wikipedia.org/wiki/Horse\" title=\"Horse\">horses</a> deliver 1,500 <a href=\"https://wikipedia.org/wiki/Ancient_Roman_units_of_measurement#Dry_measure\" title=\"Ancient Roman units of measurement\">modius</a> of <a href=\"https://wikipedia.org/wiki/Salt\" title=\"Salt\">salt</a>. Delivery was meant for <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> price was half of delivered salt.", "no_year_html": "Sources cite biggest <a href=\"https://wikipedia.org/wiki/Caravan_(travellers)\" title=\"Caravan (travellers)\">caravan trade</a> between <a href=\"https://wikipedia.org/wiki/Podvisoki\" title=\"Podvisoki\">Podvisoki</a> and <a href=\"https://wikipedia.org/wiki/Republic_of_Ragusa\" title=\"Republic of Ragusa\">Republic of Ragusa</a>. <a href=\"https://wikipedia.org/wiki/Vlachs\" title=\"Vlachs\">Vlachs</a> committed to <a href=\"https://wikipedia.org/wiki/Ragusan_nobility\" title=\"Ragusan nobility\">Ragusan</a> lord <PERSON><PERSON>, that they will with 600 <a href=\"https://wikipedia.org/wiki/Horse\" title=\"Horse\">horses</a> deliver 1,500 <a href=\"https://wikipedia.org/wiki/Ancient_Roman_units_of_measurement#Dry_measure\" title=\"Ancient Roman units of measurement\">modius</a> of <a href=\"https://wikipedia.org/wiki/Salt\" title=\"Salt\">salt</a>. Delivery was meant for <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> price was half of delivered salt.", "links": [{"title": "Caravan (travellers)", "link": "https://wikipedia.org/wiki/Caravan_(travellers)"}, {"title": "Podvisoki", "link": "https://wikipedia.org/wiki/Podvisoki"}, {"title": "Republic of Ragusa", "link": "https://wikipedia.org/wiki/Republic_of_Ragusa"}, {"title": "Vlachs", "link": "https://wikipedia.org/wiki/Vlachs"}, {"title": "Ragusan nobility", "link": "https://wikipedia.org/wiki/<PERSON>gusan_nobility"}, {"title": "Horse", "link": "https://wikipedia.org/wiki/Horse"}, {"title": "Ancient Roman units of measurement", "link": "https://wikipedia.org/wiki/Ancient_Roman_units_of_measurement#Dry_measure"}, {"title": "Salt", "link": "https://wikipedia.org/wiki/Salt"}]}, {"year": "1500", "text": "Ottoman-Venetian War (1499-1503): The Ottomans capture Methoni, Messenia.", "html": "1500 - <a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Venetian_War_(1499%E2%80%931503)\" title=\"Ottoman-Venetian War (1499-1503)\">Ottoman-Venetian War (1499-1503)</a>: The Ottomans capture <a href=\"https://wikipedia.org/wiki/Methoni,_Messenia\" title=\"Methoni, Messenia\">Methoni, Messenia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Venetian_War_(1499%E2%80%931503)\" title=\"Ottoman-Venetian War (1499-1503)\">Ottoman-Venetian War (1499-1503)</a>: The Ottomans capture <a href=\"https://wikipedia.org/wiki/Methoni,_Messenia\" title=\"Methoni, Messenia\">Methoni, Messenia</a>.", "links": [{"title": "Ottoman-Venetian War (1499-1503)", "link": "https://wikipedia.org/wiki/Ottoman%E2%80%93Venetian_War_(1499%E2%80%931503)"}, {"title": "Methoni, Messenia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Messen<PERSON>"}]}, {"year": "1610", "text": "The First Anglo-Powhatan War begins in colonial Virginia.", "html": "1610 - The <a href=\"https://wikipedia.org/wiki/Anglo-Powhatan_Wars\" title=\"Anglo-Powhatan Wars\">First Anglo-Powhatan War</a> begins in <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">colonial Virginia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anglo-Powhatan_Wars\" title=\"Anglo-Powhatan Wars\">First Anglo-Powhatan War</a> begins in <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">colonial Virginia</a>.", "links": [{"title": "Anglo-Powhatan Wars", "link": "https://wikipedia.org/wiki/Anglo-Powhatan_Wars"}, {"title": "Colony of Virginia", "link": "https://wikipedia.org/wiki/Colony_of_Virginia"}]}, {"year": "1810", "text": "Napoleon annexes Westphalia as part of the First French Empire.", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\">Napoleon</a> annexes <a href=\"https://wikipedia.org/wiki/Westphalia\" title=\"Westphalia\">Westphalia</a> as part of the <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">First French Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\">Napoleon</a> annexes <a href=\"https://wikipedia.org/wiki/Westphalia\" title=\"Westphalia\">Westphalia</a> as part of the <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">First French Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Westphalia", "link": "https://wikipedia.org/wiki/Westphalia"}, {"title": "First French Empire", "link": "https://wikipedia.org/wiki/First_French_Empire"}]}, {"year": "1814", "text": "American Indian Wars: The Creek sign the Treaty of Fort Jackson, giving up huge parts of Alabama and Georgia.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: The <a href=\"https://wikipedia.org/wiki/Creek_people\" class=\"mw-redirect\" title=\"Creek people\">Creek</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Fort_Jackson\" title=\"Treaty of Fort Jackson\">Treaty of Fort Jackson</a>, giving up huge parts of <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> and <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: The <a href=\"https://wikipedia.org/wiki/Creek_people\" class=\"mw-redirect\" title=\"Creek people\">Creek</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Fort_Jackson\" title=\"Treaty of Fort Jackson\">Treaty of Fort Jackson</a>, giving up huge parts of <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> and <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a>.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Creek people", "link": "https://wikipedia.org/wiki/Creek_people"}, {"title": "Treaty of Fort Jackson", "link": "https://wikipedia.org/wiki/Treaty_of_Fort_Jackson"}, {"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S._state)"}]}, {"year": "1830", "text": "<PERSON> becomes the king of the French following abdication of <PERSON>.", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the king of the French following abdication of <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the king of the French following abdication of <PERSON>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "The Webster-Ashburton Treaty is signed, establishing the United States-Canada border east of the Rocky Mountains.", "html": "1842 - The <a href=\"https://wikipedia.org/wiki/Webster%E2%80%93Ashburton_Treaty\" title=\"Webster-Ashburton Treaty\">Webster-Ashburton Treaty</a> is signed, establishing the United States-Canada border east of the <a href=\"https://wikipedia.org/wiki/Rocky_Mountains\" title=\"Rocky Mountains\">Rocky Mountains</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Webster%E2%80%93Ashburton_Treaty\" title=\"Webster-Ashburton Treaty\">Webster-Ashburton Treaty</a> is signed, establishing the United States-Canada border east of the <a href=\"https://wikipedia.org/wiki/Rocky_Mountains\" title=\"Rocky Mountains\">Rocky Mountains</a>.", "links": [{"title": "Webster-Ashburton Treaty", "link": "https://wikipedia.org/wiki/Webster%E2%80%93Ashburton_Treaty"}, {"title": "Rocky Mountains", "link": "https://wikipedia.org/wiki/Rocky_Mountains"}]}, {"year": "1854", "text": "American Transcendentalist philosopher <PERSON> publishes his memoir <PERSON>.", "html": "1854 - American <a href=\"https://wikipedia.org/wiki/Transcendentalism\" title=\"Transcendentalism\">Transcendentalist</a> philosopher <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes his memoir <i><a href=\"https://wikipedia.org/wiki/Walden\" title=\"Walden\">Walden</a></i>.", "no_year_html": "American <a href=\"https://wikipedia.org/wiki/Transcendentalism\" title=\"Transcendentalism\">Transcendentalist</a> philosopher <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes his memoir <i><a href=\"https://wikipedia.org/wiki/Walden\" title=\"<PERSON>\">Walden</a></i>.", "links": [{"title": "Transcendentalism", "link": "https://wikipedia.org/wiki/Transcendentalism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Walden", "link": "https://wikipedia.org/wiki/Walden"}]}, {"year": "1855", "text": "Åland War: The Battle of Suomenlinna begins.", "html": "1855 - <a href=\"https://wikipedia.org/wiki/%C3%85land_War\" title=\"Åland War\">Åland War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Suomenlinna\" title=\"Battle of Suomenlinna\">Battle of Suomenlinna</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%85land_War\" title=\"Åland War\">Åland War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Suomenlinna\" title=\"Battle of Suomenlinna\">Battle of Suomenlinna</a> begins.", "links": [{"title": "Åland War", "link": "https://wikipedia.org/wiki/%C3%85land_War"}, {"title": "Battle of Suomenlinna", "link": "https://wikipedia.org/wiki/Battle_of_Suomenlinna"}]}, {"year": "1862", "text": "American Civil War: Battle of Cedar Mountain: At Cedar Mountain, Virginia, Confederate General <PERSON><PERSON> narrowly defeats Union forces under General <PERSON>.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cedar_Mountain\" title=\"Battle of Cedar Mountain\">Battle of Cedar Mountain</a>: At <a href=\"https://wikipedia.org/wiki/Culpeper_County,_Virginia\" title=\"Culpeper County, Virginia\">Cedar Mountain, Virginia</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jackson\" title=\"Stone<PERSON> Jackson\"><PERSON><PERSON></a> narrowly defeats <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union</a> forces under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(military_officer)\" class=\"mw-redirect\" title=\"<PERSON> (military officer)\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cedar_Mountain\" title=\"Battle of Cedar Mountain\">Battle of Cedar Mountain</a>: At <a href=\"https://wikipedia.org/wiki/Culpeper_County,_Virginia\" title=\"Culpeper County, Virginia\">Cedar Mountain, Virginia</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/Stone<PERSON>_Jackson\" title=\"Stonewall Jackson\"><PERSON><PERSON> Jackson</a> narrowly defeats <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union</a> forces under General <a href=\"https://wikipedia.org/wiki/<PERSON>(military_officer)\" class=\"mw-redirect\" title=\"<PERSON> (military officer)\"><PERSON></a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Cedar Mountain", "link": "https://wikipedia.org/wiki/Battle_of_Cedar_Mountain"}, {"title": "Culpeper County, Virginia", "link": "https://wikipedia.org/wiki/Culpeper_County,_Virginia"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON><PERSON> Jackson", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Jackson"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "<PERSON> (military officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(military_officer)"}]}, {"year": "1877", "text": "American Indian Wars: Battle of the Big Hole: A small band of Nez Percé Indians clash with the United States Army.", "html": "1877 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Big_Hole\" title=\"Battle of the Big Hole\">Battle of the Big Hole</a>: A small band of <a href=\"https://wikipedia.org/wiki/Nez_Perce_people\" class=\"mw-redirect\" title=\"Nez Perce people\">Nez Percé Indians</a> clash with the <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Big_Hole\" title=\"Battle of the Big Hole\">Battle of the Big Hole</a>: A small band of <a href=\"https://wikipedia.org/wiki/Nez_Perce_people\" class=\"mw-redirect\" title=\"Nez Perce people\">Nez Percé Indians</a> clash with the <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a>.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Battle of the Big Hole", "link": "https://wikipedia.org/wiki/Battle_of_the_Big_Hole"}, {"title": "Nez Perce people", "link": "https://wikipedia.org/wiki/Nez_Perce_people"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}]}, {"year": "1892", "text": "Thomas Edison receives a patent for a two-way telegraph.", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> receives a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for a two-way <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegraph</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_Edison\" title=\"Thomas Edison\"><PERSON></a> receives a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for a two-way <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegraph</a>.", "links": [{"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Telegraphy", "link": "https://wikipedia.org/wiki/Telegraphy"}]}, {"year": "1897", "text": "The first International Congress of Mathematicians is held in Zürich, Switzerland.", "html": "1897 - The first <a href=\"https://wikipedia.org/wiki/International_Congress_of_Mathematicians\" title=\"International Congress of Mathematicians\">International Congress of Mathematicians</a> is held in <a href=\"https://wikipedia.org/wiki/Z%C3%BCrich\" class=\"mw-redirect\" title=\"Zürich\">Zürich</a>, <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Switzerland</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/International_Congress_of_Mathematicians\" title=\"International Congress of Mathematicians\">International Congress of Mathematicians</a> is held in <a href=\"https://wikipedia.org/wiki/Z%C3%BCrich\" class=\"mw-redirect\" title=\"Zürich\">Zürich</a>, <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Switzerland</a>.", "links": [{"title": "International Congress of Mathematicians", "link": "https://wikipedia.org/wiki/International_Congress_of_Mathematicians"}, {"title": "Zürich", "link": "https://wikipedia.org/wiki/Z%C3%BCrich"}, {"title": "Switzerland", "link": "https://wikipedia.org/wiki/Switzerland"}]}, {"year": "1902", "text": "<PERSON> and <PERSON> of Denmark are crowned King and Queen of the United Kingdom of Great Britain and Ireland.", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Edward VII\"><PERSON> VII</a> and <a href=\"https://wikipedia.org/wiki/Alexandra_of_Denmark\" title=\"Alexandra of Denmark\"><PERSON> of Denmark</a> are <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_VII_and_<PERSON>\" title=\"Coronation of <PERSON> VII and <PERSON>\">crowned King and Queen</a> of the United Kingdom of Great Britain and Ireland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VII\" title=\"<PERSON> VII\"><PERSON> VII</a> and <a href=\"https://wikipedia.org/wiki/Alexandra_of_Denmark\" title=\"Alexandra of Denmark\"><PERSON> of Denmark</a> are <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_VII_and_<PERSON>\" title=\"Coronation of <PERSON> VII and <PERSON>\">crowned King and Queen</a> of the United Kingdom of Great Britain and Ireland.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Alexandra_of_Denmark"}, {"title": "Coronation of <PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>_and_<PERSON>"}]}, {"year": "1907", "text": "The first Boy Scout encampment concludes at Brownsea Island in southern England.", "html": "1907 - The first <a href=\"https://wikipedia.org/wiki/Scouting\" title=\"Scouting\">Boy Scout</a> <a href=\"https://wikipedia.org/wiki/Brownsea_Island_Scout_camp\" title=\"Brownsea Island Scout camp\">encampment</a> concludes at <a href=\"https://wikipedia.org/wiki/Brownsea_Island\" title=\"Brownsea Island\">Brownsea Island</a> in southern England.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Scouting\" title=\"Scouting\">Boy Scout</a> <a href=\"https://wikipedia.org/wiki/Brownsea_Island_Scout_camp\" title=\"Brownsea Island Scout camp\">encampment</a> concludes at <a href=\"https://wikipedia.org/wiki/Brownsea_Island\" title=\"Brownsea Island\">Brownsea Island</a> in southern England.", "links": [{"title": "Scouting", "link": "https://wikipedia.org/wiki/Scouting"}, {"title": "Brownsea Island Scout camp", "link": "https://wikipedia.org/wiki/Brownsea_Island_Scout_camp"}, {"title": "Brownsea Island", "link": "https://wikipedia.org/wiki/Brownsea_Island"}]}, {"year": "1925", "text": "A train robbery takes place in Kakori, near Lucknow, India, by the Indian independence revolutionaries, against the British government.", "html": "1925 - A <a href=\"https://wikipedia.org/wiki/Kakori_train_robbery\" class=\"mw-redirect\" title=\"Kakori train robbery\">train robbery</a> takes place in <a href=\"https://wikipedia.org/wiki/Kakori\" title=\"Kakori\">Kakori</a>, near <a href=\"https://wikipedia.org/wiki/Lucknow\" title=\"Lucknow\">Lucknow</a>, India, by the Indian independence revolutionaries, against the British government.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Kakori_train_robbery\" class=\"mw-redirect\" title=\"Kakori train robbery\">train robbery</a> takes place in <a href=\"https://wikipedia.org/wiki/Kakori\" title=\"Kakori\">Kakori</a>, near <a href=\"https://wikipedia.org/wiki/Lucknow\" title=\"Lucknow\">Lucknow</a>, India, by the Indian independence revolutionaries, against the British government.", "links": [{"title": "Kakori train robbery", "link": "https://wikipedia.org/wiki/Kakori_train_robbery"}, {"title": "Kakori", "link": "https://wikipedia.org/wiki/Kakori"}, {"title": "Lucknow", "link": "https://wikipedia.org/wiki/Lucknow"}]}, {"year": "1936", "text": "Summer Olympics: <PERSON> wins his fourth gold medal at the games.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/1936_Summer_Olympics\" title=\"1936 Summer Olympics\">Summer Olympics</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins his fourth gold medal at the games.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1936_Summer_Olympics\" title=\"1936 Summer Olympics\">Summer Olympics</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins his fourth gold medal at the games.", "links": [{"title": "1936 Summer Olympics", "link": "https://wikipedia.org/wiki/1936_Summer_Olympics"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "World War II: Battle of Savo Island: Allied naval forces protecting their amphibious forces during the initial stages of the Battle of Guadalcanal are surprised and defeated by an Imperial Japanese Navy cruiser force.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Savo_Island\" title=\"Battle of Savo Island\">Battle of Savo Island</a>: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> naval forces protecting their amphibious forces during the initial stages of the <a href=\"https://wikipedia.org/wiki/Battle_of_Guadalcanal\" class=\"mw-redirect\" title=\"Battle of Guadalcanal\">Battle of Guadalcanal</a> are surprised and defeated by an <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> cruiser force.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Savo_Island\" title=\"Battle of Savo Island\">Battle of Savo Island</a>: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> naval forces protecting their amphibious forces during the initial stages of the <a href=\"https://wikipedia.org/wiki/Battle_of_Guadalcanal\" class=\"mw-redirect\" title=\"Battle of Guadalcanal\">Battle of Guadalcanal</a> are surprised and defeated by an <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> cruiser force.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Savo Island", "link": "https://wikipedia.org/wiki/Battle_of_Savo_Island"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Battle of Guadalcanal", "link": "https://wikipedia.org/wiki/Battle_of_Guadalcanal"}, {"title": "Imperial Japanese Navy", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Navy"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>'s 7th symphony premiers in a besieged Leningrad.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s 7th symphony <a href=\"https://wikipedia.org/wiki/Leningrad_premi%C3%A8re_of_S<PERSON><PERSON>kovich%27s_Symphony_No._7\" title=\"Leningrad première of <PERSON><PERSON><PERSON><PERSON><PERSON>'s Symphony No. 7\">premiers</a> in a <a href=\"https://wikipedia.org/wiki/Siege_of_Leningrad\" title=\"Siege of Leningrad\">besieged Leningrad</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s 7th symphony <a href=\"https://wikipedia.org/wiki/Leningrad_premi%C3%A8re_of_<PERSON><PERSON><PERSON><PERSON>h%27s_Symphony_No._7\" title=\"Leningrad première of <PERSON><PERSON><PERSON><PERSON><PERSON>'s Symphony No. 7\">premiers</a> in a <a href=\"https://wikipedia.org/wiki/Siege_of_Leningrad\" title=\"Siege of Leningrad\">besieged Leningrad</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Leningrad première of <PERSON><PERSON><PERSON><PERSON><PERSON>'s Symphony No. 7", "link": "https://wikipedia.org/wiki/Leningrad_premi%C3%A8re_of_Shostakovich%27s_Symphony_No._7"}, {"title": "Siege of Leningrad", "link": "https://wikipedia.org/wiki/Siege_of_Leningrad"}]}, {"year": "1944", "text": "The United States Forest Service and the Wartime Advertising Council release posters featuring <PERSON><PERSON> for the first time.", "html": "1944 - The <a href=\"https://wikipedia.org/wiki/United_States_Forest_Service\" title=\"United States Forest Service\">United States Forest Service</a> and the Wartime Advertising Council release posters featuring <a href=\"https://wikipedia.org/wiki/Smokey_Bear\" title=\"Smokey Bear\">Smokey Bear</a> for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Forest_Service\" title=\"United States Forest Service\">United States Forest Service</a> and the Wartime Advertising Council release posters featuring <a href=\"https://wikipedia.org/wiki/Smokey_Bear\" title=\"Smokey Bear\"><PERSON>y Bear</a> for the first time.", "links": [{"title": "United States Forest Service", "link": "https://wikipedia.org/wiki/United_States_Forest_Service"}, {"title": "<PERSON><PERSON> Bear", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "World War II: Continuation War: The Vyborg-Petrozavodsk Offensive, the largest offensive launched by Soviet Union against Finland during the Second World War, ends to a strategic stalemate. Both Finnish and Soviet troops at the Finnish front dug to defensive positions, and the front remains stable until the end of the war.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Continuation_War\" title=\"Continuation War\">Continuation War</a>: The <a href=\"https://wikipedia.org/wiki/Vyborg%E2%80%93Petrozavodsk_Offensive\" class=\"mw-redirect\" title=\"Vyborg-Petrozavodsk Offensive\">Vyborg-Petrozavodsk Offensive</a>, the largest offensive launched by Soviet Union against Finland during the Second World War, ends to a strategic stalemate. Both Finnish and Soviet troops at the Finnish front dug to defensive positions, and the front remains stable until the end of the war.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Continuation_War\" title=\"Continuation War\">Continuation War</a>: The <a href=\"https://wikipedia.org/wiki/Vyborg%E2%80%93Petrozavodsk_Offensive\" class=\"mw-redirect\" title=\"Vyborg-Petrozavodsk Offensive\">Vyborg-Petrozavodsk Offensive</a>, the largest offensive launched by Soviet Union against Finland during the Second World War, ends to a strategic stalemate. Both Finnish and Soviet troops at the Finnish front dug to defensive positions, and the front remains stable until the end of the war.", "links": [{"title": "Continuation War", "link": "https://wikipedia.org/wiki/Continuation_War"}, {"title": "Vyborg-Petrozavodsk Offensive", "link": "https://wikipedia.org/wiki/Vyborg%E2%80%93Petrozavodsk_Offensive"}]}, {"year": "1945", "text": "World War II: Nagasaki is devastated when an atomic bomb, Fat <PERSON>, is dropped by the United States B-29 Bockscar. Thirty-five thousand people are killed outright, including 23,200-28,200 Japanese war workers, 2,000 Korean forced workers, and 150 Japanese soldiers.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Nagasaki\" title=\"Nagasaki\">Nagasaki</a> is <a href=\"https://wikipedia.org/wiki/Atomic_bombings_of_Hiroshima_and_Nagasaki\" title=\"Atomic bombings of Hiroshima and Nagasaki\">devastated</a> when an <a href=\"https://wikipedia.org/wiki/Nuclear_weapon\" title=\"Nuclear weapon\">atomic bomb</a>, <i><a href=\"https://wikipedia.org/wiki/Fat_Man\" title=\"Fat Man\">Fat Man</a></i>, is dropped by the United States <a href=\"https://wikipedia.org/wiki/B-29_Superfortress\" class=\"mw-redirect\" title=\"B-29 Superfortress\">B-29</a> <i><a href=\"https://wikipedia.org/wiki/Bockscar\" title=\"Bockscar\">Bockscar</a></i>. Thirty-five thousand people are killed outright, including 23,200-28,200 Japanese war workers, 2,000 Korean forced workers, and 150 Japanese soldiers.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Nagasaki\" title=\"Nagasaki\">Nagasaki</a> is <a href=\"https://wikipedia.org/wiki/Atomic_bombings_of_Hiroshima_and_Nagasaki\" title=\"Atomic bombings of Hiroshima and Nagasaki\">devastated</a> when an <a href=\"https://wikipedia.org/wiki/Nuclear_weapon\" title=\"Nuclear weapon\">atomic bomb</a>, <i><a href=\"https://wikipedia.org/wiki/Fat_Man\" title=\"Fat Man\">Fat Man</a></i>, is dropped by the United States <a href=\"https://wikipedia.org/wiki/B-29_Superfortress\" class=\"mw-redirect\" title=\"B-29 Superfortress\">B-29</a> <i><a href=\"https://wikipedia.org/wiki/Bockscar\" title=\"Bockscar\">Bockscar</a></i>. Thirty-five thousand people are killed outright, including 23,200-28,200 Japanese war workers, 2,000 Korean forced workers, and 150 Japanese soldiers.", "links": [{"title": "Nagasaki", "link": "https://wikipedia.org/wiki/Nagasaki"}, {"title": "Atomic bombings of Hiroshima and Nagasaki", "link": "https://wikipedia.org/wiki/Atomic_bombings_of_Hiroshima_and_Nagasaki"}, {"title": "Nuclear weapon", "link": "https://wikipedia.org/wiki/Nuclear_weapon"}, {"title": "Fat Man", "link": "https://wikipedia.org/wiki/<PERSON>_Man"}, {"title": "B-29 Superfortress", "link": "https://wikipedia.org/wiki/B-29_Superfortress"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>car"}]}, {"year": "1945", "text": "The Red Army invades Japanese-occupied Manchuria.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> <a href=\"https://wikipedia.org/wiki/Soviet_invasion_of_Manchuria\" title=\"Soviet invasion of Manchuria\">invades</a> Japanese-occupied Manchuria.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> <a href=\"https://wikipedia.org/wiki/Soviet_invasion_of_Manchuria\" title=\"Soviet invasion of Manchuria\">invades</a> Japanese-occupied Manchuria.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Soviet invasion of Manchuria", "link": "https://wikipedia.org/wiki/Soviet_invasion_of_Manchuria"}]}, {"year": "1960", "text": "South Kasai secedes from the Congo.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/South_Kasai\" title=\"South Kasai\">South Kasai</a> secedes from the <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo_(L%C3%A9opoldville)\" title=\"Republic of the Congo (Léopoldville)\">Congo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_Kasai\" title=\"South Kasai\">South Kasai</a> secedes from the <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo_(L%C3%A9opoldville)\" title=\"Republic of the Congo (Léopoldville)\">Congo</a>.", "links": [{"title": "South Kasai", "link": "https://wikipedia.org/wiki/South_Kasai"}, {"title": "Republic of the Congo (Léopoldville)", "link": "https://wikipedia.org/wiki/Republic_of_the_Congo_(L%C3%A9opoldville)"}]}, {"year": "1965", "text": "Singapore is expelled from Malaysia and becomes the only country to date to gain independence unwillingly.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> is <a href=\"https://wikipedia.org/wiki/History_of_the_Republic_of_Singapore#Independence_from_Malaysia\" title=\"History of the Republic of Singapore\">expelled</a> from <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> and becomes the only country to date to gain independence unwillingly.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> is <a href=\"https://wikipedia.org/wiki/History_of_the_Republic_of_Singapore#Independence_from_Malaysia\" title=\"History of the Republic of Singapore\">expelled</a> from <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> and becomes the only country to date to gain independence unwillingly.", "links": [{"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}, {"title": "History of the Republic of Singapore", "link": "https://wikipedia.org/wiki/History_of_the_Republic_of_Singapore#Independence_from_Malaysia"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}]}, {"year": "1969", "text": "Tate-LaBianca murders: Followers of <PERSON> murder pregnant actress <PERSON> (wife of <PERSON>), coffee heiress <PERSON>, Polish actor <PERSON><PERSON><PERSON><PERSON><PERSON>, men's hairstylist <PERSON> and recent high-school graduate <PERSON>.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Tate%E2%80%93LaBianca_murders\" title=\"Tate-LaBianca murders\">Tate-LaBianca murders</a>: Followers of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> murder pregnant actress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (wife of <a href=\"https://wikipedia.org/wiki/Roman_Polanski\" title=\"Roman Polanski\">Roman Pol<PERSON></a>), <a href=\"https://wikipedia.org/wiki/Coffee\" title=\"Coffee\">coffee</a> heiress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>olger\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Polish actor <a href=\"https://wikipedia.org/wiki/Wojcie<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Woj<PERSON><PERSON>\">Wo<PERSON><PERSON><PERSON></a>, men's hairstylist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>bring\"><PERSON> Sebring</a> and recent high-school graduate <a href=\"https://wikipedia.org/wiki/Steven_Parent\" class=\"mw-redirect\" title=\"Steven Parent\">Steven Parent</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tate%E2%80%93LaBianca_murders\" title=\"Tate-LaBianca murders\">Tate-LaBianca murders</a>: Followers of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> murder pregnant actress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (wife of <a href=\"https://wikipedia.org/wiki/Roman_Polanski\" title=\"Roman Polanski\"><PERSON></a>), <a href=\"https://wikipedia.org/wiki/Coffee\" title=\"Coffee\">coffee</a> heiress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>olger\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Polish actor <a href=\"https://wikipedia.org/wiki/Wojcie<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Woj<PERSON><PERSON>\">Wo<PERSON><PERSON><PERSON></a>, men's hairstylist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>bring\"><PERSON> Sebring</a> and recent high-school graduate <a href=\"https://wikipedia.org/wiki/Steven_Parent\" class=\"mw-redirect\" title=\"Steven Parent\">Steven Parent</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> murders", "link": "https://wikipedia.org/wiki/Tate%E2%80%93LaBianca_murders"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Polanski"}, {"title": "Coffee", "link": "https://wikipedia.org/wiki/Coffee"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "LANSA Flight 502 crashes after takeoff from Alejandro <PERSON>elasco Astete International Airport in Cusco, Peru, killing 99 of the 100 people on board, as well as two people on the ground.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/LANSA_Flight_502\" title=\"LANSA Flight 502\">LANSA Flight 502</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Astete_International_Airport\" title=\"Alejandro Velasco Astete International Airport\">Alejandro <PERSON>elasco Astete International Airport</a> in <a href=\"https://wikipedia.org/wiki/Cusco\" title=\"Cusco\">Cusco</a>, <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>, killing 99 of the 100 people on board, as well as two people on the ground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/LANSA_Flight_502\" title=\"LANSA Flight 502\">LANSA Flight 502</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>sco_Astete_International_Airport\" title=\"Alejandro Velasco Astete International Airport\">Alejandro <PERSON> Astete International Airport</a> in <a href=\"https://wikipedia.org/wiki/Cusco\" title=\"Cusco\">Cusco</a>, <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>, killing 99 of the 100 people on board, as well as two people on the ground.", "links": [{"title": "LANSA Flight 502", "link": "https://wikipedia.org/wiki/LANSA_Flight_502"}, {"title": "Alejandro <PERSON> Astete International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Astete_International_Airport"}, {"title": "Cusco", "link": "https://wikipedia.org/wiki/Cusco"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}]}, {"year": "1971", "text": "The Troubles: In Northern Ireland, the British authorities launch Operation Demetrius. The operation involves the mass arrest and internment without trial of individuals suspected of being affiliated with the Irish Republican Army (PIRA). Mass riots follow, and thousands of people flee or are forced out of their homes.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: In <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, the British authorities launch <a href=\"https://wikipedia.org/wiki/Operation_Demetrius\" title=\"Operation Demetrius\">Operation Demetrius</a>. The operation involves the mass arrest and internment without trial of individuals suspected of being affiliated with the <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Irish Republican Army</a> (PIRA). Mass riots follow, and thousands of people flee or are forced out of their homes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: In <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, the British authorities launch <a href=\"https://wikipedia.org/wiki/Operation_Demetrius\" title=\"Operation Demetrius\">Operation Demetrius</a>. The operation involves the mass arrest and internment without trial of individuals suspected of being affiliated with the <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Irish Republican Army</a> (PIRA). Mass riots follow, and thousands of people flee or are forced out of their homes.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "Operation Demetrius", "link": "https://wikipedia.org/wiki/Operation_Demetrius"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}]}, {"year": "1973", "text": "Mars 7 is launched from the USSR.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Mars_7\" title=\"Mars 7\">Mars 7</a> is launched from the USSR.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mars_7\" title=\"Mars 7\">Mars 7</a> is launched from the USSR.", "links": [{"title": "Mars 7", "link": "https://wikipedia.org/wiki/Mars_7"}]}, {"year": "1974", "text": "As a direct result of the <PERSON>gate scandal, <PERSON> becomes the first President of the United States to resign from office. Vice President <PERSON> becomes president.", "html": "1974 - As a direct result of the <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first President of the United States to resign from office. <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States_of_America\" class=\"mw-redirect\" title=\"Vice President of the United States of America\">Vice President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes president.", "no_year_html": "As a direct result of the <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first President of the United States to resign from office. <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States_of_America\" class=\"mw-redirect\" title=\"Vice President of the United States of America\">Vice President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes president.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States of America", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "The Italian prosecuting magistrate <PERSON><PERSON> is murdered by the <PERSON><PERSON>dra<PERSON><PERSON><PERSON> on behalf of the Sicilian Mafia while preparing the government's case in the final appeal of the Maxi Trial.", "html": "1991 - The Italian prosecuting magistrate <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is murdered by the <a href=\"https://wikipedia.org/wiki/%27Ndrangheta\" title=\"'Ndranghe<PERSON>\">'<PERSON><PERSON><PERSON><PERSON><PERSON></a> on behalf of the <a href=\"https://wikipedia.org/wiki/Sicilian_Mafia\" title=\"Sicilian Mafia\">Sicilian Mafia</a> while preparing the government's case in the final appeal of the <a href=\"https://wikipedia.org/wiki/Maxi_Trial\" title=\"Maxi Trial\">Maxi Trial</a>.", "no_year_html": "The Italian prosecuting magistrate <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is murdered by the <a href=\"https://wikipedia.org/wiki/%27Ndrangheta\" title=\"'Ndrang<PERSON><PERSON>\">'<PERSON><PERSON><PERSON><PERSON><PERSON></a> on behalf of the <a href=\"https://wikipedia.org/wiki/Sicilian_Mafia\" title=\"Sicilian Mafia\">Sicilian Mafia</a> while preparing the government's case in the final appeal of the <a href=\"https://wikipedia.org/wiki/Maxi_Trial\" title=\"Maxi Trial\">Maxi Trial</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%27Ndrangheta"}, {"title": "Sicilian Mafia", "link": "https://wikipedia.org/wiki/Sicilian_Mafia"}, {"title": "Maxi Trial", "link": "https://wikipedia.org/wiki/Maxi_Trial"}]}, {"year": "1993", "text": "The Liberal Democratic Party of Japan loses a 38-year hold on national leadership.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Liberal_Democratic_Party_(Japan)\" title=\"Liberal Democratic Party (Japan)\">Liberal Democratic Party of Japan</a> loses a 38-year hold on national leadership.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Liberal_Democratic_Party_(Japan)\" title=\"Liberal Democratic Party (Japan)\">Liberal Democratic Party of Japan</a> loses a 38-year hold on national leadership.", "links": [{"title": "Liberal Democratic Party (Japan)", "link": "https://wikipedia.org/wiki/Liberal_Democratic_Party_(Japan)"}]}, {"year": "1995", "text": "Aviateca Flight 901 crashes into the San Vicente volcano in El Salvador, killing all 65 people on board.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Aviateca_Flight_901\" title=\"Aviateca Flight 901\">Aviateca Flight 901</a> crashes into the <a href=\"https://wikipedia.org/wiki/San_Vicente_(volcano)\" title=\"San Vicente (volcano)\">San Vicente volcano</a> in <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a>, killing all 65 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aviateca_Flight_901\" title=\"Aviateca Flight 901\">Aviateca Flight 901</a> crashes into the <a href=\"https://wikipedia.org/wiki/San_Vicente_(volcano)\" title=\"San Vicente (volcano)\">San Vicente volcano</a> in <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a>, killing all 65 people on board.", "links": [{"title": "Aviateca Flight 901", "link": "https://wikipedia.org/wiki/Aviateca_Flight_901"}, {"title": "San Vicente (volcano)", "link": "https://wikipedia.org/wiki/San_Vicente_(volcano)"}, {"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}]}, {"year": "1999", "text": "Russian President <PERSON> fires his Prime Minister, <PERSON>, and for the fourth time fires his entire cabinet.", "html": "1999 - Russian President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> fires his Prime Minister, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and for the fourth time fires his entire cabinet.", "no_year_html": "Russian President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> fires his Prime Minister, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and for the fourth time fires his entire cabinet.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "At least 21 suspected terrorists are arrested in the 2006 transatlantic aircraft plot that happened in the United Kingdom. The arrests are made in London, Birmingham, and High Wycombe in an overnight operation.", "html": "2006 - At least 21 suspected terrorists are arrested in the <a href=\"https://wikipedia.org/wiki/2006_transatlantic_aircraft_plot\" title=\"2006 transatlantic aircraft plot\">2006 transatlantic aircraft plot</a> that happened in the United Kingdom. The arrests are made in London, <a href=\"https://wikipedia.org/wiki/Birmingham\" title=\"Birmingham\">Birmingham</a>, and <a href=\"https://wikipedia.org/wiki/High_Wycombe\" title=\"High Wycombe\">High Wycombe</a> in an overnight operation.", "no_year_html": "At least 21 suspected terrorists are arrested in the <a href=\"https://wikipedia.org/wiki/2006_transatlantic_aircraft_plot\" title=\"2006 transatlantic aircraft plot\">2006 transatlantic aircraft plot</a> that happened in the United Kingdom. The arrests are made in London, <a href=\"https://wikipedia.org/wiki/Birmingham\" title=\"Birmingham\">Birmingham</a>, and <a href=\"https://wikipedia.org/wiki/High_Wycombe\" title=\"High Wycombe\">High Wycombe</a> in an overnight operation.", "links": [{"title": "2006 transatlantic aircraft plot", "link": "https://wikipedia.org/wiki/2006_transatlantic_aircraft_plot"}, {"title": "Birmingham", "link": "https://wikipedia.org/wiki/Birmingham"}, {"title": "High Wycombe", "link": "https://wikipedia.org/wiki/High_Wycombe"}]}, {"year": "2007", "text": "Air Moorea Flight 1121 crashes after takeoff from Moorea Airport in French Polynesia, killing all 20 people on board.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Air_Moorea_Flight_1121\" title=\"Air Moorea Flight 1121\">Air Moorea Flight 1121</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Moorea_Airport\" title=\"Moorea Airport\">Moorea Airport</a> in <a href=\"https://wikipedia.org/wiki/French_Polynesia\" title=\"French Polynesia\">French Polynesia</a>, killing all 20 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Moorea_Flight_1121\" title=\"Air Moorea Flight 1121\">Air Moorea Flight 1121</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Moorea_Airport\" title=\"Moorea Airport\">Moorea Airport</a> in <a href=\"https://wikipedia.org/wiki/French_Polynesia\" title=\"French Polynesia\">French Polynesia</a>, killing all 20 people on board.", "links": [{"title": "Air Moorea Flight 1121", "link": "https://wikipedia.org/wiki/Air_Moorea_Flight_1121"}, {"title": "Moorea Airport", "link": "https://wikipedia.org/wiki/Moorea_Airport"}, {"title": "French Polynesia", "link": "https://wikipedia.org/wiki/French_Polynesia"}]}, {"year": "2012", "text": "<PERSON> becomes the first woman to officiate an NFL game.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Shannon_Eastin\" title=\"Shannon Eastin\"><PERSON></a> becomes the first woman to officiate an <a href=\"https://wikipedia.org/wiki/NFL\" class=\"mw-redirect\" title=\"NFL\">NFL</a> game.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shannon_Eastin\" title=\"Shannon Eastin\"><PERSON></a> becomes the first woman to officiate an <a href=\"https://wikipedia.org/wiki/NFL\" class=\"mw-redirect\" title=\"NFL\">NFL</a> game.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Shannon_<PERSON>in"}, {"title": "NFL", "link": "https://wikipedia.org/wiki/NFL"}]}, {"year": "2013", "text": "Gunmen open fire at a Sunni mosque in the city of Quetta killing at least ten people and injuring 30.", "html": "2013 - Gunmen <a href=\"https://wikipedia.org/wiki/9_August_2013_Quetta_shooting\" class=\"mw-redirect\" title=\"9 August 2013 Quetta shooting\">open fire</a> at a <a href=\"https://wikipedia.org/wiki/Sunni\" class=\"mw-redirect\" title=\"Sunni\">Sunni</a> mosque in the city of <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta</a> killing at least ten people and injuring 30.", "no_year_html": "Gunmen <a href=\"https://wikipedia.org/wiki/9_August_2013_Quetta_shooting\" class=\"mw-redirect\" title=\"9 August 2013 Quetta shooting\">open fire</a> at a <a href=\"https://wikipedia.org/wiki/Sunni\" class=\"mw-redirect\" title=\"Sunni\">Sunni</a> mosque in the city of <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta</a> killing at least ten people and injuring 30.", "links": [{"title": "9 August 2013 Quetta shooting", "link": "https://wikipedia.org/wiki/9_August_2013_Quetta_shooting"}, {"title": "Sunni", "link": "https://wikipedia.org/wiki/Sunni"}, {"title": "Quetta", "link": "https://wikipedia.org/wiki/Quetta"}]}, {"year": "2014", "text": "<PERSON>, an 18-year-old African American male in Ferguson, Missouri, is shot and killed by a Ferguson police officer after reportedly assaulting the officer and attempting to steal his weapon, sparking protests and unrest in the city.", "html": "2014 - <PERSON>, an 18-year-old African American male in <a href=\"https://wikipedia.org/wiki/Ferguson,_Missouri\" title=\"Ferguson, Missouri\">Ferguson, Missouri</a>, is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON>_<PERSON>\" title=\"Killing of <PERSON>\">shot and killed</a> by a <a href=\"https://wikipedia.org/wiki/Ferguson_Police_Department_(Missouri)\" class=\"mw-redirect\" title=\"Ferguson Police Department (Missouri)\"><PERSON></a> police officer after reportedly assaulting the officer and attempting to steal his weapon, sparking <a href=\"https://wikipedia.org/wiki/Ferguson_unrest\" title=\"Ferguson unrest\">protests and unrest</a> in the city.", "no_year_html": "<PERSON>, an 18-year-old African American male in <a href=\"https://wikipedia.org/wiki/Ferguson,_Missouri\" title=\"Ferguson, Missouri\">Ferguson, Missouri</a>, is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON>\" title=\"Killing of <PERSON>\">shot and killed</a> by a <a href=\"https://wikipedia.org/wiki/Ferguson_Police_Department_(Missouri)\" class=\"mw-redirect\" title=\"Ferguson Police Department (Missouri)\"><PERSON></a> police officer after reportedly assaulting the officer and attempting to steal his weapon, sparking <a href=\"https://wikipedia.org/wiki/Ferguson_unrest\" title=\"Ferguson unrest\">protests and unrest</a> in the city.", "links": [{"title": "Ferguson, Missouri", "link": "https://wikipedia.org/wiki/Ferguson,_Missouri"}, {"title": "Killing of <PERSON>", "link": "https://wikipedia.org/wiki/Killing_of_<PERSON>_<PERSON>"}, {"title": "Ferguson Police Department (Missouri)", "link": "https://wikipedia.org/wiki/Ferguson_Police_Department_(Missouri)"}, {"title": "Ferguson unrest", "link": "https://wikipedia.org/wiki/Ferguson_unrest"}]}, {"year": "2021", "text": "The Tampere light rail officially starts operating.", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/Tampere_light_rail\" title=\"Tampere light rail\">Tampere light rail</a> officially starts operating.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tampere_light_rail\" title=\"Tampere light rail\">Tampere light rail</a> officially starts operating.", "links": [{"title": "Tampere light rail", "link": "https://wikipedia.org/wiki/Tampere_light_rail"}]}, {"year": "2024", "text": "Voepass Linhas Aéreas Flight 2283 crashes near Vinhedo, São Paulo, killing all 62 people on board.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Voepass_Flight_2283\" title=\"Voepass Flight 2283\">Voepass Linhas Aéreas Flight 2283</a> crashes near <a href=\"https://wikipedia.org/wiki/Vinhedo\" title=\"Vinhedo\">Vinhedo</a>, <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo_(state)\" title=\"São Paulo (state)\">São Paulo</a>, killing all 62 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Voepass_Flight_2283\" title=\"Voepass Flight 2283\">Voepass Linhas Aéreas Flight 2283</a> crashes near <a href=\"https://wikipedia.org/wiki/Vinhedo\" title=\"Vinhedo\">Vinhedo</a>, <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo_(state)\" title=\"São Paulo (state)\">São Paulo</a>, killing all 62 people on board.", "links": [{"title": "Voepass Flight 2283", "link": "https://wikipedia.org/wiki/Voepass_Flight_2283"}, {"title": "Vinhedo", "link": "https://wikipedia.org/wiki/Vinhedo"}, {"title": "São Paulo (state)", "link": "https://wikipedia.org/wiki/S%C3%A3o_Paulo_(state)"}]}], "Births": [{"year": "1201", "text": "<PERSON>, English historian and merchant (d. 1274)", "html": "1201 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Thedmar\" title=\"<PERSON> Thedmar\"><PERSON></a>, English historian and merchant (d. 1274)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Thedmar\" title=\"<PERSON> Thedmar\"><PERSON></a>, English historian and merchant (d. 1274)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Thedmar"}]}, {"year": "1537", "text": "<PERSON>, Italian mathematician and astronomer (d. 1604)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Mathematician\" title=\"Mathematician\">mathematician</a> and <a href=\"https://wikipedia.org/wiki/Astronomer\" title=\"Astronomer\">astronomer</a> (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Mathematician\" title=\"Mathematician\">mathematician</a> and <a href=\"https://wikipedia.org/wiki/Astronomer\" title=\"Astronomer\">astronomer</a> (d. 1604)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mathematician", "link": "https://wikipedia.org/wiki/Mathematician"}, {"title": "Astronomer", "link": "https://wikipedia.org/wiki/Astronomer"}]}, {"year": "1544", "text": "<PERSON><PERSON><PERSON>, Duke of Pomerania (d. 1606)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Pomerania\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Duke of Pomerania\"><PERSON><PERSON><PERSON>, Duke of Pomerania</a> (d. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Pomerania\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Duke of Pomerania\"><PERSON><PERSON><PERSON>, Duke of Pomerania</a> (d. 1606)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_XIII,_Duke_of_Pomerania"}]}, {"year": "1590", "text": "<PERSON>, colonial settler and governor of Connecticut (d. 1661)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, colonial settler and governor of Connecticut (d. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, colonial settler and governor of Connecticut (d. 1661)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>(governor)"}]}, {"year": "1603", "text": "<PERSON>, German-Dutch theologian and academic (d. 1669)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch theologian and academic (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch theologian and academic (d. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1611", "text": "<PERSON> of Nassau-Siegen, German count, officer in the Dutch Army, diplomat for the Dutch Republic (b. 1611)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Nassau-Siegen_(1611%E2%80%931652)\" title=\"<PERSON> of Nassau-Siegen (1611-1652)\"><PERSON> of Nassau-Siegen</a>, German count, officer in the Dutch Army, diplomat for the Dutch Republic (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Nassau-Siegen_(1611%E2%80%931652)\" title=\"<PERSON> of Nassau-Siegen (1611-1652)\"><PERSON> of Nassau-Siegen</a>, German count, officer in the Dutch Army, diplomat for the Dutch Republic (b. 1611)", "links": [{"title": "<PERSON> of Nassau-Siegen (1611-1652)", "link": "https://wikipedia.org/wiki/Henry_of_Nassau-Siegen_(1611%E2%80%931652)"}]}, {"year": "1648", "text": "<PERSON>, German composer (d. 1694)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1694)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1653", "text": "<PERSON>, English poet and translator (d. 1683)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and translator (d. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and translator (d. 1683)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1696", "text": "<PERSON>, Prince of Liechtenstein (d. 1772)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Prince_of_Liechtenstein\" title=\"<PERSON>, Prince of Liechtenstein\"><PERSON>, Prince of Liechtenstein</a> (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Liechtenstein\" title=\"<PERSON>, Prince of Liechtenstein\"><PERSON>, Prince of Liechtenstein</a> (d. 1772)", "links": [{"title": "<PERSON>, Prince of Liechtenstein", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Prince_of_Liechtenstein"}]}, {"year": "1722", "text": "<PERSON> <PERSON> of Prussia (d. 1758)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Prussia\" title=\"Prince <PERSON> of Prussia\">Prince <PERSON> of Prussia</a> (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Prussia\" title=\"Prince <PERSON> of Prussia\">Prince <PERSON> of Prussia</a> (d. 1758)", "links": [{"title": "Prince <PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Prince_Augustus_<PERSON>_of_Prussia"}]}, {"year": "1726", "text": "<PERSON>, Italian priest, zoologist, and mathematician (d. 1778)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, <a href=\"https://wikipedia.org/wiki/Zoologist\" class=\"mw-redirect\" title=\"Zoologist\">zoologist</a>, and <a href=\"https://wikipedia.org/wiki/Mathematician\" title=\"Mathematician\">mathematician</a> (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, <a href=\"https://wikipedia.org/wiki/Zoologist\" class=\"mw-redirect\" title=\"Zoologist\">zoologist</a>, and <a href=\"https://wikipedia.org/wiki/Mathematician\" title=\"Mathematician\">mathematician</a> (d. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Zoologist", "link": "https://wikipedia.org/wiki/Zoologist"}, {"title": "Mathematician", "link": "https://wikipedia.org/wiki/Mathematician"}]}, {"year": "1748", "text": "<PERSON>, German music publisher (d. 1809)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German music publisher (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German music publisher (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, American humanitarian; wife of <PERSON> (d. 1854)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American humanitarian; wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American humanitarian; wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, Scottish architect and engineer, designed the Menai Suspension Bridge (d. 1834)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Menai_Suspension_Bridge\" title=\"Menai Suspension Bridge\">Menai Suspension Bridge</a> (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Menai_Suspension_Bridge\" title=\"Menai Suspension Bridge\">Menai Suspension Bridge</a> (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Menai Suspension Bridge", "link": "https://wikipedia.org/wiki/Menai_Suspension_Bridge"}]}, {"year": "1776", "text": "<PERSON><PERSON><PERSON>, Italian physicist and chemist (d. 1856)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physicist and chemist (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physicist and chemist (d. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1783", "text": "Grand Duchess <PERSON> of Russia (d. 1801)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (d. 1801)", "links": [{"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1788", "text": "<PERSON><PERSON><PERSON>, American missionary and lexicographer (d. 1850)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American missionary and <a href=\"https://wikipedia.org/wiki/Lexicographer\" class=\"mw-redirect\" title=\"Lexicographer\">lexicographer</a> (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American missionary and <a href=\"https://wikipedia.org/wiki/Lexicographer\" class=\"mw-redirect\" title=\"Lexicographer\">lexicographer</a> (d. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Lexicographer", "link": "https://wikipedia.org/wiki/Lexicographer"}]}, {"year": "1797", "text": "<PERSON>, English lieutenant and surveyor (d. 1855)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and surveyor (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and surveyor (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, English engineer and politician (d. 1860)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, Canadian saint (d. 1937)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian saint (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian saint (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1847", "text": "<PERSON>, French-Italian wife of <PERSON><PERSON><PERSON> of Spain (d. 1876)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Vitt<PERSON>\"><PERSON></a>, French-Italian wife of <a href=\"https://wikipedia.org/wiki/Amadeo_I_of_Spain\" title=\"Amadeo I of Spain\">Amadeo I of Spain</a> (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Vittoria da<PERSON>\"><PERSON></a>, French-Italian wife of <a href=\"https://wikipedia.org/wiki/Amadeo_I_of_Spain\" title=\"Amadeo I of Spain\">Amadeo I of Spain</a> (d. 1876)", "links": [{"title": "Maria Vittoria da<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>itt<PERSON>_da<PERSON>_<PERSON>"}, {"title": "Amadeo I of Spain", "link": "https://wikipedia.org/wiki/Amadeo_I_of_Spain"}]}, {"year": "1848", "text": "<PERSON>, Australian-born businessman and philanthropist. (d. 1900)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-born businessman and philanthropist. (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-born businessman and philanthropist. (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American astronomer and academic (d. 1942)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON>, Scottish nurse and activist (d. 1920)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish nurse and activist (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish nurse and activist (d. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "Archdu<PERSON> of Austria (d. 1962)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_August_of_Austria\" title=\"Archduke <PERSON> August of Austria\">Archduke <PERSON> August of Austria</a> (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arch<PERSON><PERSON>_<PERSON>_August_of_Austria\" title=\"Archduke <PERSON> August of Austria\">Archduke <PERSON> August of Austria</a> (d. 1962)", "links": [{"title": "Archdu<PERSON> <PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_August_of_Austria"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON>, Venezuelan composer and conductor (d. 1947)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan composer and conductor (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan composer and conductor (d. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, English pianist, composer, and conductor (d. 1959)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8lbey\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8lbey\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8lbey"}]}, {"year": "1878", "text": "<PERSON>, Irish architect and furniture designer (d. 1976)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish architect and furniture designer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish architect and furniture designer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Australian politician, 15th Premier of Western Australia, (d. 1956)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>, (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>, (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1881", "text": "Prince <PERSON><PERSON><PERSON><PERSON> of Orléans-Braganza, Brazilian prince (d. 1918)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>t%C3%B4nio_Gast%C3%A3o_of_Orl%C3%A9ans-Braganza\" title=\"Prince <PERSON><PERSON><PERSON><PERSON> of Orléans-Braganza\">Prince <PERSON><PERSON><PERSON><PERSON> of Orléans-Braganza</a>, Brazilian prince (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>t%C3%B4nio_Gast%C3%A3o_of_Orl%C3%A9ans-Braganza\" title=\"Prince <PERSON><PERSON><PERSON><PERSON> of Orléans-Braganza\">Prince <PERSON><PERSON><PERSON><PERSON> of Orléans-Braganza</a>, Brazilian prince (d. 1918)", "links": [{"title": "Prince <PERSON><PERSON><PERSON><PERSON> of Orléans-Braganza", "link": "https://wikipedia.org/wiki/Prince_<PERSON>t%C3%B4nio_Gast%C3%A3o_of_Orl%C3%A9ans-Braganza"}]}, {"year": "1890", "text": "<PERSON><PERSON>, Finnish philosopher and psychologist, attendant of the Vienna circle (d. 1958)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish philosopher and psychologist, attendant of the Vienna circle (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish philosopher and psychologist, attendant of the Vienna circle (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, German physicist and chemist (d. 1980)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and chemist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and chemist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_H%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Swiss psychologist and philosopher (d. 1980)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychologist and philosopher (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychologist and philosopher (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON> <PERSON><PERSON>, Australian-English author and actress (d. 1996)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/P._L._Travers\" title=\"P. L. Travers\"><PERSON><PERSON> <PERSON><PERSON> Trave<PERSON></a>, Australian-English author and actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P._L._Travers\" title=\"P. L. Travers\"><PERSON><PERSON> <PERSON><PERSON> Trave<PERSON></a>, Australian-English author and actress (d. 1996)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_Travers"}]}, {"year": "1900", "text": "<PERSON>, American actor and singer (d. 1990)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, French violinist (d. 1991)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French violinist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French violinist (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian general and politician (d. 1984)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Panteleim<PERSON>_<PERSON>\" title=\"Panteleim<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian general and politician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Panteleim<PERSON>_<PERSON>\" title=\"Panteleim<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian general and politician (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panteleim<PERSON>_<PERSON><PERSON>"}]}, {"year": "1905", "text": "<PERSON>, British actor and barrister (d. 1978)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor and barrister (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor and barrister (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Genn"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Indian scholar, author, and academic (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>kak\" title=\"<PERSON><PERSON><PERSON> Gokak\"><PERSON><PERSON><PERSON></a>, Indian scholar, author, and academic (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Gokak\" title=\"<PERSON><PERSON><PERSON> Gokak\"><PERSON><PERSON><PERSON></a>, Indian scholar, author, and academic (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Krishna_Gokak"}]}, {"year": "1909", "text": "<PERSON><PERSON>, American educator, first Black woman college president (d. 2003)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Player\"><PERSON><PERSON></a>, American educator, first Black woman college president (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Player\"><PERSON><PERSON></a>, American educator, first Black woman college president (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, German lawyer and diplomat (d. 1944)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_zu_<PERSON>z\" title=\"<PERSON> zu <PERSON>z\"><PERSON> zu <PERSON></a>, German lawyer and diplomat (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_zu_<PERSON>z\" title=\"<PERSON> zu <PERSON>z\"><PERSON> zu <PERSON></a>, German lawyer and diplomat (d. 1944)", "links": [{"title": "<PERSON> zu <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American astronomer and astrophysicist, Nobel Laureate (d. 1996)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and astrophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Laureate</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and astrophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Laureate</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1911", "text": "<PERSON>, American boxer and trainer (d. 2001)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Northern Irish soldier, boxer, and politician (d. 1984)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish soldier, boxer, and politician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish soldier, boxer, and politician (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Australian astronomer and engineer (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>_<PERSON>\" title=\"Wil<PERSON>\">Wil<PERSON></a>, Australian astronomer and engineer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>_<PERSON>\" title=\"Wil<PERSON>\">Wil<PERSON></a>, Australian astronomer and engineer (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Hungarian-Austrian conductor and director (d. 1963)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian conductor and director (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian conductor and director (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ren<PERSON>_<PERSON>ay"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Finnish author and illustrator (d. 2001)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author and illustrator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author and illustrator (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English footballer and manager (d. 1990)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, American astronomer and geologist (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Mareta_West\" title=\"Mareta West\"><PERSON><PERSON></a>, American astronomer and geologist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mareta_West\" title=\"Mareta West\"><PERSON><PERSON></a>, American astronomer and geologist (d. 1998)", "links": [{"title": "Mareta West", "link": "https://wikipedia.org/wiki/Mareta_West"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, American colonel (d. 1989)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rmit_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Irish soldier and playwright (d. 1966)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, Irish soldier and playwright (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, Irish soldier and playwright (d. 1966)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)"}]}, {"year": "1918", "text": "<PERSON>, American police officer (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Dutch journalist, economist, and politician, Prime Minister of the Netherlands (d. 1987)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist, economist, and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist, economist, and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1919", "text": "<PERSON>, American baseball player and manager (d. 2010)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Italian journalist and author (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and author (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enzo_Biagi"}]}, {"year": "1921", "text": "<PERSON>, American evangelist and author (d. 2021)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and author (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American soldier and politician, 33rd Governor of Nebraska (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and politician, 33rd <a href=\"https://wikipedia.org/wiki/Governor_of_Nebraska\" class=\"mw-redirect\" title=\"Governor of Nebraska\">Governor of Nebraska</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and politician, 33rd <a href=\"https://wikipedia.org/wiki/Governor_of_Nebraska\" class=\"mw-redirect\" title=\"Governor of Nebraska\">Governor of Nebraska</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Nebraska", "link": "https://wikipedia.org/wiki/Governor_of_Nebraska"}]}, {"year": "1922", "text": "<PERSON>, English poet and novelist (d. 1985)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and novelist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and novelist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Indian metropolitan (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Barna<PERSON>\" class=\"mw-redirect\" title=\"Mathews Mar Barnabas\"><PERSON></a>, Indian metropolitan (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Mathews Mar Barnabas\"><PERSON></a>, Indian metropolitan (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mathews_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American soldier and painter (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Frank_<PERSON>%C3%AD<PERSON><PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American soldier and painter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frank_Mart%C3%AD<PERSON><PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American soldier and painter (d. 2013)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/Frank_Mart%C3%<PERSON><PERSON><PERSON>_(artist)"}]}, {"year": "1925", "text": "<PERSON>, American computer scientist, developed <PERSON><PERSON><PERSON> coding (d. 1999)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, developed <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_coding\" title=\"<PERSON><PERSON><PERSON> coding\"><PERSON><PERSON><PERSON> coding</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, developed <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_coding\" title=\"<PERSON><PERSON><PERSON> coding\"><PERSON><PERSON><PERSON> coding</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> coding", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_coding"}]}, {"year": "1926", "text": "<PERSON>, Barbadian cricketer (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American short story writer and novelist (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and novelist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and novelist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actor and screenwriter (d. 1978)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and screenwriter (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and screenwriter (d. 1978)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1928", "text": "<PERSON>, American basketball player and coach", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American violinist and educator (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Cam<PERSON>_Wicks\" title=\"Cam<PERSON> Wicks\"><PERSON><PERSON></a>, American violinist and educator (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cam<PERSON>_Wicks\" title=\"Cam<PERSON> Wicks\"><PERSON><PERSON></a>, American violinist and educator (d. 2020)", "links": [{"title": "Camilla Wicks", "link": "https://wikipedia.org/wiki/Camilla_Wicks"}]}, {"year": "1928", "text": "<PERSON>, American soprano and actress (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Turkish journalist and activist (d. 1979)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Abdi_%C4%B0pek%C3%A7i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish journalist and activist (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abdi_%C4%B0pek%C3%A7i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish journalist and activist (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abdi_%C4%B0pek%C3%A7i"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American baseball player and scout (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>lt_<PERSON>\" title=\"Milt Bolling\"><PERSON><PERSON></a>, American baseball player and scout (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Milt <PERSON>lling\"><PERSON><PERSON></a>, American baseball player and scout (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milt_<PERSON>lling"}]}, {"year": "1930", "text": "<PERSON>, Canadian economist and politician, 26th Premier of Quebec (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1931", "text": "<PERSON>, American baseball player and lawyer", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chuck_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American geophysicist and academic (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geophysicist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geophysicist and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American businesswoman, co-founded <PERSON><PERSON> (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Red<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ken"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and coach (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and coach (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Scottish academic and politician (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish academic and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish academic and politician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian lawyer and jurist (d. 2021)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Japanese actress, talk show host, and author", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress, talk show host, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress, talk show host, and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, American actress (d. 2008)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Dominican-American baseball player", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Juli%C3%A1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juli%C3%A1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juli%C3%A1n_Javier"}]}, {"year": "1936", "text": "<PERSON>, Chinese-Hong Kong actor, director, producer, and screenwriter", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Hong Kong actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Hong Kong actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>se"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Ukrainian engineer and politician, 2nd President of Ukraine", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian engineer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President of Ukraine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian engineer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President of Ukraine</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Ukraine", "link": "https://wikipedia.org/wiki/President_of_Ukraine"}]}, {"year": "1938", "text": "<PERSON>, Australian tennis player and coach", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, German footballer, coach, and manager", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/H%C3%A9rcules_Brito_Ruas\" class=\"mw-redirect\" title=\"Hércules Brito Ruas\"><PERSON><PERSON><PERSON><PERSON> Brito Ruas</a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9rcules_Brito_Ruas\" class=\"mw-redirect\" title=\"Hércules Brito Ruas\"><PERSON><PERSON><PERSON><PERSON> B<PERSON>ua<PERSON></a>, Brazilian footballer", "links": [{"title": "Hércules B<PERSON>ua<PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9rcules_Brito_Ruas"}]}, {"year": "1939", "text": "<PERSON>, Northern Irish journalist (d. 1997)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish journalist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish journalist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON> Mighty Hannibal, American singer-songwriter and producer (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/The_Mighty_Hannibal\" title=\"The Mighty Hannibal\">The Mighty Hannibal</a>, American singer-songwriter and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Mighty_Hannibal\" title=\"The Mighty Hannibal\">The Mighty Hannibal</a>, American singer-songwriter and producer (d. 2014)", "links": [{"title": "The Mighty Hannibal", "link": "https://wikipedia.org/wiki/The_Mighty_Hannibal"}]}, {"year": "1939", "text": "<PERSON>, American singer (d. 2007)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American singer (d. 2007)", "links": [{"title": "<PERSON> (American singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)"}]}, {"year": "1939", "text": "<PERSON><PERSON>, French actress and screenwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Bulle_<PERSON>\" title=\"<PERSON><PERSON> Ogier\"><PERSON><PERSON></a>, French actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulle_<PERSON>\" title=\"<PERSON>e Ogier\"><PERSON><PERSON></a>, French actress and screenwriter", "links": [{"title": "Bulle Ogier", "link": "https://wikipedia.org/wiki/Bulle_Ogier"}]}, {"year": "1939", "text": "<PERSON>, Italian academic and politician, 52nd Prime Minister of Italy", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian academic and politician, 52nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian academic and politician, 52nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>di"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1939", "text": "<PERSON>, American bassist (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American mathematician and academic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian actor, director, producer, and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American boxer and actor (d. 2013)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English footballer (d. 2000)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 2000)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1944", "text": "<PERSON>, French racing driver (d. 1980)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American soldier, engineer, and author (d. 2017)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, engineer, and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, engineer, and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Russian figure skater and sportscaster (d. 2012)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater and sportscaster (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater and sportscaster (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, English author and illustrator", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Simmonds\" title=\"<PERSON><PERSON> Simmonds\"><PERSON><PERSON></a>, English author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Simmond<PERSON>\" title=\"<PERSON><PERSON> Simmonds\"><PERSON><PERSON></a>, English author and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Simmonds"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Dutch rock bass player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>rits<PERSON>\"><PERSON><PERSON></a>, Dutch rock bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> G<PERSON>rits<PERSON>\"><PERSON><PERSON></a>, Dutch rock bass player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English footballer and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American R&B/soul singer-songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/soul singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/soul singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1948", "text": "<PERSON>, American baseball player and coach (d. 2023)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (d. 2023)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1949", "text": "<PERSON>, American psychologist and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American baseball player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish journalist and radio host", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American baseball player and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Thai activist and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Prateep_<PERSON>_<PERSON>\" title=\"Prateep <PERSON>\"><PERSON><PERSON><PERSON></a>, Thai activist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prateep_<PERSON>_<PERSON>\" title=\"Prateep <PERSON>\">Prate<PERSON></a>, Thai activist and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prateep_Ungs<PERSON>_Hata"}]}, {"year": "1953", "text": "<PERSON>, Norwegian speed skater", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "1953", "text": "<PERSON>, French economist and academic, Nobel Prize laureate", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1954", "text": "<PERSON>, South African cricketer and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English drummer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian Olympic cyclist (d. 2024)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian Olympic cyclist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian Olympic cyclist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actress and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress, comedian and director", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, South African engineer and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rius\" title=\"<PERSON><PERSON>sto<PERSON>\"><PERSON><PERSON></a>, South African engineer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rius\" title=\"<PERSON><PERSON> Pistorius\"><PERSON><PERSON></a>, South African engineer and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ie_Pi<PERSON>rius"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American rapper, producer, and actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>low\"><PERSON><PERSON></a>, American rapper, producer, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American fashion designer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American tennis player and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, New Zealand businessman and politician, 38th Prime Minister of New Zealand", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman and politician, 38th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman and politician, 38th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1962", "text": "<PERSON>, American football player and radio host", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American football player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON> \"<PERSON> Rod\" <PERSON>, American basketball player (d. 2015)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Hot_Rod%22_<PERSON>\" title='<PERSON> \"Hot Rod\" Williams'><PERSON> \"Hot Rod\" <PERSON></a>, American basketball player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Hot_Rod%22_<PERSON>\" title='<PERSON> \"Hot Rod\" Williams'><PERSON> \"Hot Rod\" <PERSON></a>, American basketball player (d. 2015)", "links": [{"title": "<PERSON> \"Hot Rod\" Williams", "link": "https://wikipedia.org/wiki/<PERSON>_%22Hot_Rod%22_Williams"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter, producer, and actress (d. 2012)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Whitney_<PERSON>\" title=\"Whitney Houston\"><PERSON></a>, American singer-songwriter, producer, and actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Whitney_Houston\" title=\"Whitney Houston\"><PERSON></a>, American singer-songwriter, producer, and actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Whitney_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 2013)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian surfer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Barton Lynch\"><PERSON></a>, Australian surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Barton Lynch\"><PERSON></a>, Australian surfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian-American ice hockey player and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American journalist and television personality", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Hoda_Kotb\" title=\"Hoda Kotb\"><PERSON><PERSON></a>, American journalist and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hoda_Kotb\" title=\"Hoda Kotb\"><PERSON><PERSON></a>, American journalist and television personality", "links": [{"title": "Hoda Kotb", "link": "https://wikipedia.org/wiki/Hoda_Kotb"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Indian art director, production designer, and film and television producer (d. 2023)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian art director, production designer, and film and television producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian art director, production designer, and film and television producer (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Vinny_<PERSON>_<PERSON>\" title=\"Vinny <PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ny_<PERSON>_<PERSON>\" title=\"Vinny <PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vinny_Del_Negro"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Norwegian journalist and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American football and baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football and baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football and baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American-British actress, activist and writer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British actress, activist and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British actress, activist and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian actor, comedian, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, comedian, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, comedian, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American drummer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American director and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/McG\" title=\"McG\"><PERSON><PERSON><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/McG\" title=\"McG\"><PERSON><PERSON><PERSON></a>, American director and producer", "links": [{"title": "McG", "link": "https://wikipedia.org/wiki/McG"}]}, {"year": "1969", "text": "<PERSON>, American baseball player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>val"}]}, {"year": "1970", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Brind%27Amour\" title=\"<PERSON> Brind'Amour\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>rind%27Amour\" title=\"<PERSON>rind'Amour\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rod_Brind%27Amour"}]}, {"year": "1970", "text": "<PERSON>, American lawyer and journalist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor and comedian", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Colombian singer and songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Italian footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1973", "text": "<PERSON>, Scottish actor and director", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American author and illustrator", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Hong Kong actor, singer, director, and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actor, singer, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actor, singer, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Scottish snowboarder", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish snowboarder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American lawyer and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Reznik\"><PERSON><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Reznik\"><PERSON><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Reznik"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, French biathlete", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Poir%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Poir%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French biathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rapha%C3%ABl_Poir%C3%A9e"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Indian actor and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Uzbek football referee", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uzbek football referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uzbek football referee", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian soccer player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, English actress and singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, French model and actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON>_Holdsclaw\" title=\"Cha<PERSON><PERSON> Holdsclaw\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON>_Holdsclaw\" title=\"Chami<PERSON> Holdsclaw\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Holdsclaw"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Uzbek football referee", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uzbek football referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uzbek football referee", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Adewale_Ogunleye\" title=\"Adewale Ogunleye\">Adewale Ogunleye</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adewale_Ogunleye\" title=\"Adewale Ogunleye\">Adewale Ogunleye</a>, American football player", "links": [{"title": "Adewale Ogunleye", "link": "https://wikipedia.org/wiki/Adewale_Ogunleye"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/I<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Udoka\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Udo<PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ime_Udoka"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Mika%C3%ABl_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mika%C3%ABl_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mika%C3%AB<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Moldavian lawyer and politician, Mayor of Chișinău", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chirtoac%C4%83\" title=\"<PERSON><PERSON> Chirtoacă\"><PERSON><PERSON></a>, Moldavian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Chi%C8%99in%C4%83u\" title=\"Mayor of Chișinău\">Mayor of Chișinău</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chirtoac%C4%83\" title=\"<PERSON><PERSON> Chi<PERSON>oa<PERSON>ă\"><PERSON><PERSON></a>, Moldavian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Chi%C8%99in%C4%83u\" title=\"Mayor of Chișinău\">Mayor of Chișinău</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dorin_Chirtoac%C4%83"}, {"title": "Mayor of Chișinău", "link": "https://wikipedia.org/wiki/Mayor_of_Chi%C8%99in%C4%83u"}]}, {"year": "1978", "text": "<PERSON>, Mexican actress and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Belgian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American football coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Kliff Kingsbury\"><PERSON><PERSON></a>, American football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>bury\"><PERSON><PERSON></a>, American football coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bury"}]}, {"year": "1979", "text": "<PERSON>, British politician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Singaporean table tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jiawei\"><PERSON></a>, Singaporean table tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jiawei\"><PERSON></a>, Singaporean table tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>awei"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American sprinter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gay\" title=\"Tyson Gay\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gay\" title=\"Tyson Gay\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Russian singer and activist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian singer and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian singer and activist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Kanst<PERSON><PERSON>_<PERSON>\" title=\"Kanst<PERSON><PERSON>ov\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Kanst<PERSON><PERSON> Si<PERSON>ov\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian actor and comedian", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_actor)\" title=\"<PERSON> (Canadian actor)\"><PERSON></a>, Canadian actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_actor)\" title=\"<PERSON> (Canadian actor)\"><PERSON></a>, Canadian actor and comedian", "links": [{"title": "<PERSON> (Canadian actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_actor)"}]}, {"year": "1983", "text": "<PERSON>, Zimbabwean cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Masakadza\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Masakadza\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hamilton_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(ice_hockey)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Polish-English violinist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-English violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-English violinist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Scottish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1985", "text": "<PERSON>, Italian racing driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Filipe_Lu%C3%ADs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Filipe_Lu%C3%ADs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Filipe_Lu%C3%ADs"}]}, {"year": "1985", "text": "<PERSON>, American actress and singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Peirsol\"><PERSON><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Peirsol\"><PERSON><PERSON></a>, American swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American entrepreneur", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American entrepreneur", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>a<PERSON><PERSON><PERSON>_Russell"}]}, {"year": "1985", "text": "<PERSON>, American football player (d. 2013)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, German footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, German rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Estonian sprinter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>it"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1988)\" title=\"<PERSON><PERSON> (footballer, born 1988)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1988)\" title=\"<PERSON><PERSON> (footballer, born 1988)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1988)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1988)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Italian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Japanese actor and model", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/%C4%B0shak_Do%C4%9Fan\" title=\"İshak <PERSON>ğan\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0shak_Do%C4%9Fan\" title=\"İshak <PERSON>ğan\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "İshak Doğan", "link": "https://wikipedia.org/wiki/%C4%B0shak_Do%C4%9Fan"}]}, {"year": "1990", "text": "<PERSON>, American LGBT activist", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American LGBT activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American LGBT activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Scottish rugby player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, French skier", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Brice Roger\"><PERSON><PERSON></a>, French skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Roger\"><PERSON><PERSON></a>, French skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Australian cricketer ", "html": "1990 - <a href=\"https://wikipedia.org/wiki/D%27A<PERSON><PERSON>_<PERSON>\" title=\"D'<PERSON><PERSON> Short\"><PERSON><PERSON><PERSON><PERSON></a>, Australian cricketer ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%27A<PERSON><PERSON>_<PERSON>\" title=\"D'<PERSON><PERSON> Short\"><PERSON><PERSON><PERSON><PERSON></a>, Australian cricketer ", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%27A<PERSON>y_Short"}]}, {"year": "1990", "text": "<PERSON>, Swedish actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A5rd\" title=\"<PERSON>\"><PERSON></a>, Swedish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A5rd\" title=\"<PERSON>\"><PERSON></a>, Swedish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bill_Skarsg%C3%A5rd"}]}, {"year": "1991", "text": "<PERSON>, English actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American bodybuilder and wrestler", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bodybuilder and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bodybuilder and wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bliss"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Indian actress ", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Afghan journalist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Farahnaz_Forotan\" title=\"Farahnaz Forotan\">Farahnaz Forotan</a>, Afghan journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Farahnaz_Forotan\" title=\"Farahnaz Forotan\">Farahnaz Forotan</a>, Afghan journalist", "links": [{"title": "Farahnaz Forotan", "link": "https://wikipedia.org/wiki/Farahnaz_Forotan"}]}, {"year": "1993", "text": "<PERSON><PERSON>, South Korean singer and actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Jun.Q\" title=\"Jun.Q\">Jun.Q</a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jun.Q\" title=\"Jun.Q\">Jun.Q</a>, South Korean singer and actor", "links": [{"title": "Jun.Q", "link": "https://wikipedia.org/wiki/Jun.Q"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Indian gymnast", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American soccer player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American rapper (d. 2020)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"King Von\"><PERSON></a>, American rapper (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"King Von\"><PERSON></a>, American rapper (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Eli_Apple\" title=\"Eli Apple\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eli_Apple\" title=\"Eli Apple\"><PERSON></a>, American football player", "links": [{"title": "Eli Apple", "link": "https://wikipedia.org/wiki/Eli_Apple"}]}, {"year": "1995", "text": "<PERSON>, American actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "Justice <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, South Korean singer and actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hyun\" title=\"<PERSON><PERSON>-hyun\"><PERSON><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hyun\" title=\"<PERSON><PERSON>-hyun\"><PERSON><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON><PERSON>yun", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hyun"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Filipino actress and model", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Latvian figure skater", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>asi%C4%BCjevs\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>asi%C4%BCjevs\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Deniss_Vasi%C4%BCjevs"}]}, {"year": "2000", "text": "<PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, British singer-songwriter", "html": "2000 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"A<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"A<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arlo_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Andorran tennis player", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Victoria_Jim%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Andorran tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Jim%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Andorran tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victoria_Jim%C3%A9<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}], "Deaths": [{"year": "378", "text": "<PERSON><PERSON><PERSON>, Roman general", "html": "378 - <a href=\"https://wikipedia.org/wiki/Traianus_(magister_peditum)\" title=\"Trai<PERSON> (magister peditum)\"><PERSON><PERSON><PERSON></a>, Roman general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Traianus_(magister_peditum)\" title=\"Traianus (magister peditum)\"><PERSON><PERSON><PERSON></a>, Roman general", "links": [{"title": "<PERSON><PERSON><PERSON> (magister peditum)", "link": "https://wikipedia.org/wiki/Traianus_(magister_peditum)"}]}, {"year": "378", "text": "<PERSON><PERSON>, Roman emperor (b. 328)", "html": "378 - <a href=\"https://wikipedia.org/wiki/Valens\" title=\"Valens\"><PERSON><PERSON></a>, Roman emperor (b. 328)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valens\" title=\"Valens\"><PERSON><PERSON></a>, Roman emperor (b. 328)", "links": [{"title": "Valens", "link": "https://wikipedia.org/wiki/Valens"}]}, {"year": "803", "text": "<PERSON> of Athens, Byzantine ruler (b. 752)", "html": "803 - <a href=\"https://wikipedia.org/wiki/Irene_of_Athens\" title=\"<PERSON> of Athens\"><PERSON> of Athens</a>, Byzantine ruler (b. 752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Athens\" title=\"<PERSON> of Athens\"><PERSON> of Athens</a>, Byzantine ruler (b. 752)", "links": [{"title": "Irene of Athens", "link": "https://wikipedia.org/wiki/<PERSON>_of_Athens"}]}, {"year": "833", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Iraqi caliph (b. 786)", "html": "833 - <a href=\"https://wikipedia.org/wiki/Al-Ma%27mun\" title=\"Al-Ma'mun\"><PERSON><PERSON><PERSON><PERSON>m<PERSON></a>, Iraqi <a href=\"https://wikipedia.org/wiki/Caliph\" class=\"mw-redirect\" title=\"Caliph\">caliph</a> (b. 786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Ma%27mun\" title=\"Al-Ma'mun\"><PERSON><PERSON><PERSON><PERSON>m<PERSON></a>, Iraqi <a href=\"https://wikipedia.org/wiki/Caliph\" class=\"mw-redirect\" title=\"Caliph\">caliph</a> (b. 786)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Ma%27mun"}, {"title": "Caliph", "link": "https://wikipedia.org/wiki/Caliph"}]}, {"year": "1048", "text": "Pope Damasus II", "html": "1048 - <a href=\"https://wikipedia.org/wiki/Pope_Damasus_II\" title=\"Pope Damasus II\">Pope Damasus II</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Damasus_II\" title=\"Pope Damasus II\">Pope Damasus II</a>", "links": [{"title": "Pope Damasus II", "link": "https://wikipedia.org/wiki/<PERSON>_Damasus_II"}]}, {"year": "1107", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1079)", "html": "1107 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1079)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1079)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1173", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, Kurdish soldier and politician", "html": "1173 - <a href=\"https://wikipedia.org/wiki/Najm_ad-<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Najm ad<PERSON>\">Najm ad<PERSON><PERSON></a>, Kurdish soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Najm_ad-<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Najm ad-<PERSON>\">Najm ad<PERSON><PERSON></a>, Kurdish soldier and politician", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naj<PERSON>_<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1211", "text": "<PERSON>, 4th Lord of Bramber, exiled Anglo-Norman baron (b. 1144/53)", "html": "1211 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Lord_of_Bramber\" title=\"<PERSON>, 4th Lord of Bramber\"><PERSON>, 4th Lord of Bramber</a>, exiled Anglo-Norman baron (b. 1144/53)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Lord_of_Bramber\" title=\"<PERSON>, 4th Lord of Bramber\"><PERSON>, 4th Lord of Bramber</a>, exiled Anglo-Norman baron (b. 1144/53)", "links": [{"title": "<PERSON>, 4th Lord of Bramber", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Lord_of_Bramber"}]}, {"year": "1260", "text": "<PERSON> of Kirkham, Bishop of Durham", "html": "1260 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Kirk<PERSON>\" title=\"<PERSON> of Kirkham\"><PERSON> of Kirkham</a>, Bishop of Durham", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Kirk<PERSON>\" title=\"<PERSON> of Kirkham\"><PERSON> of Kirkham</a>, Bishop of Durham", "links": [{"title": "<PERSON> of Kirkham", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1296", "text": "<PERSON>, Count of Brienne, French crusader", "html": "1296 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Brienne\" title=\"<PERSON>, Count of Brienne\"><PERSON>, Count of Brienne</a>, French crusader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Brienne\" title=\"<PERSON>, Count of Brienne\"><PERSON>, Count of Brienne</a>, French crusader", "links": [{"title": "<PERSON>, Count of Brienne", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1341", "text": "<PERSON> of Anjou, queen consort of Sicily (b. 1289)", "html": "1341 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a>, queen consort of Sicily (b. 1289)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a>, queen consort of Sicily (b. 1289)", "links": [{"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1354", "text": "<PERSON>, Duke of Slavonia, Hungarian prince (b. 1332)", "html": "1354 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Slavonia\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Slavonia\"><PERSON>, Duke of Slavonia</a>, Hungarian prince (b. 1332)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Slavonia\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Slavonia\"><PERSON>, Duke of Slavonia</a>, Hungarian prince (b. 1332)", "links": [{"title": "<PERSON>, Duke of Slavonia", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Slavonia"}]}, {"year": "1420", "text": "<PERSON>, French theologian and cardinal (b. 1351)", "html": "1420 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ailly\" title=\"<PERSON>\"><PERSON></a>, French theologian and cardinal (b. 1351)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ailly\" title=\"<PERSON>\"><PERSON></a>, French theologian and cardinal (b. 1351)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ailly"}]}, {"year": "1516", "text": "<PERSON><PERSON><PERSON><PERSON>, Early Netherlandish painter (b. circa 1450)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Early Netherlandish painter (b. circa 1450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Early Netherlandish painter (b. circa 1450)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1534", "text": "<PERSON>, Italian cardinal and philosopher (b. 1470)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and philosopher (b. 1470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and philosopher (b. 1470)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1580", "text": "<PERSON><PERSON><PERSON> of Constantinople (b. 1520)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/Metrophanes_III_of_Constantinople\" title=\"Metrophanes III of Constantinople\">Metrophanes III of Constantinople</a> (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Metrophanes_III_of_Constantinople\" title=\"Metrophanes III of Constantinople\"><PERSON>pha<PERSON> III of Constantinople</a> (b. 1520)", "links": [{"title": "<PERSON><PERSON><PERSON> III of Constantinople", "link": "https://wikipedia.org/wiki/Metrophanes_III_of_Constantinople"}]}, {"year": "1601", "text": "<PERSON> the <PERSON>, Romanian prince (b. 1558)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brave\" title=\"<PERSON> the Brave\"><PERSON> the <PERSON></a>, Romanian prince (b. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brave\" title=\"<PERSON> the Brave\"><PERSON> the Brave</a>, Romanian prince (b. 1558)", "links": [{"title": "<PERSON> the Brave", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1634", "text": "<PERSON>, English lawyer and judge (b. 1577)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (b. 1577)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON>, English orientalist and academic (b. 1678)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English orientalist and academic (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English orientalist and academic (b. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON>, 1st Duke of Chandos, English academic and politician, Lord Lieutenant of Radnorshire (b. 1673)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Chandos\" title=\"<PERSON>, 1st Duke of Chandos\"><PERSON>, 1st Duke of Chandos</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Radnorshire\" title=\"Lord Lieutenant of Radnorshire\">Lord Lieutenant of Radnorshire</a> (b. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Chandos\" title=\"<PERSON>, 1st Duke of Chandos\"><PERSON>, 1st Duke of Chandos</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Radnorshire\" title=\"Lord Lieutenant of Radnorshire\">Lord Lieutenant of Radnorshire</a> (b. 1673)", "links": [{"title": "<PERSON>, 1st Duke of Chandos", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Chandos"}, {"title": "Lord Lieutenant of Radnorshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Radnorshire"}]}, {"year": "1816", "text": "<PERSON>, German jurist and author (b. 1771)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and author (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and author (b. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_A<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, English composer and publisher (b. 1781)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and publisher (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and publisher (b. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Irish lawyer and poet (b. 1810)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and poet (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and poet (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Chinese martial artist, co-founded the Chin Woo Athletic Association (b. 1868)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese martial artist, co-founded the <a href=\"https://wikipedia.org/wiki/Chin_Woo_Athletic_Association\" title=\"Chin Woo Athletic Association\">Chin Woo Athletic Association</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese martial artist, co-founded the <a href=\"https://wikipedia.org/wiki/Chin_Woo_Athletic_Association\" title=\"Chin Woo Athletic Association\">Chin Woo Athletic Association</a> (b. 1868)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}, {"title": "Chin Woo Athletic Association", "link": "https://wikipedia.org/wiki/Chin_Woo_Athletic_Association"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer and educator (b. 1857)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and educator (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and educator (b. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rug<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Welsh-Australian politician, 9th Premier of Queensland (b. 1845)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1932", "text": "<PERSON>, Canadian mathematician, founder of the Fields Medal (b. 1863)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian mathematician, founder of the <a href=\"https://wikipedia.org/wiki/Fields_Medal\" title=\"Fields Medal\">Fields Medal</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian mathematician, founder of the <a href=\"https://wikipedia.org/wiki/Fields_Medal\" title=\"Fields Medal\">Fields Medal</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fields Medal", "link": "https://wikipedia.org/wiki/Fields_Medal"}]}, {"year": "1941", "text": "<PERSON>, Executed Irish Republican (b. 1915)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_Republican)\" class=\"mw-redirect\" title=\"<PERSON> (Irish Republican)\"><PERSON></a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_Republicanism\" class=\"mw-redirect\" title=\"Irish Republicanism\">Irish Republican</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_Republican)\" class=\"mw-redirect\" title=\"<PERSON> (Irish Republican)\"><PERSON></a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_Republicanism\" class=\"mw-redirect\" title=\"Irish Republicanism\">Irish Republican</a> (b. 1915)", "links": [{"title": "<PERSON> (Irish Republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_Republican)"}, {"title": "Irish Republicanism", "link": "https://wikipedia.org/wiki/Irish_Republicanism"}]}, {"year": "1942", "text": "<PERSON>, German nun and saint (b. 1891)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German nun and saint (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German nun and saint (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Belarusian-French painter and educator (b. 1893)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Cha%C3%AFm_Soutine\" title=\"Cha<PERSON><PERSON> Souti<PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-French painter and educator (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cha%C3%AFm_Soutine\" title=\"<PERSON><PERSON><PERSON> Soutine\"><PERSON><PERSON><PERSON></a>, Belarusian-French painter and educator (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cha%C3%AFm_Soutine"}]}, {"year": "1945", "text": "<PERSON>, Canadian lieutenant and pilot, Victoria Cross recipient (b. 1917)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lieutenant and pilot, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lieutenant and pilot, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1945", "text": "<PERSON>, American runner and coach (b. 1881)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, South African cricketer (b. 1876)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, German fashion designer, founded <PERSON> (b. 1885)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Hugo_Boss_(fashion_designer)\" class=\"mw-redirect\" title=\"<PERSON> Boss (fashion designer)\"><PERSON></a>, German fashion designer, founded <a href=\"https://wikipedia.org/wiki/Hugo_Boss\" title=\"Hugo Boss\">Hugo Boss</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hugo_Boss_(fashion_designer)\" class=\"mw-redirect\" title=\"<PERSON> Boss (fashion designer)\"><PERSON></a>, German fashion designer, founded <a href=\"https://wikipedia.org/wiki/Hugo_Boss\" title=\"Hugo Boss\">Hugo Boss</a> (b. 1885)", "links": [{"title": "<PERSON> (fashion designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fashion_designer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American psychologist and academic (b. 1874)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German Nazi physician (b. 1898)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Nazi\" class=\"mw-redirect\" title=\"Nazi\">Nazi</a> physician (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Nazi\" class=\"mw-redirect\" title=\"Nazi\">Nazi</a> physician (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nazi", "link": "https://wikipedia.org/wiki/Nazi"}]}, {"year": "1962", "text": "<PERSON>, German-born Swiss poet, novelist, and painter, Nobel Prize laureate (b. 1877)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born Swiss poet, novelist, and painter, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born Swiss poet, novelist, and painter, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1963", "text": "<PERSON>, American son of <PERSON> (b. 1963)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English author and playwright (b. 1933)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish-American actor and author (b. 1936)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Wo<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish-American actor and author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish-American actor and author (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American model and actress (b. 1943)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON> <PERSON><PERSON>, English physicist and academic, Nobel Prize laureate (b. 1903)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1903)", "links": [{"title": "C<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1970", "text": "<PERSON> (Irish republican), lifelong militant and editor (b. 1907)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a>, lifelong militant and editor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a>, lifelong militant and editor (b. 1907)", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish lawyer and academic (b. 1897)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/S%C4%B1dd%C4%B1k_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish lawyer and academic (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C4%B1dd%C4%B1k_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish lawyer and academic (b. 1897)", "links": [{"title": "Sıdd<PERSON>k <PERSON>", "link": "https://wikipedia.org/wiki/S%C4%B1dd%C4%B1k_<PERSON>_<PERSON>ar"}]}, {"year": "1974", "text": "<PERSON>, American trumpet player and bandleader (b. 1934)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Russian pianist and composer (b. 1906)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist and composer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist and composer (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American novelist and short story writer (b. 1903)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ens\"><PERSON></a>, American novelist and short story writer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>zzens\"><PERSON></a>, American novelist and short story writer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American businessman (b. 1903)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Walter_O%27Malley"}]}, {"year": "1979", "text": "<PERSON>, American gang leader, founded the Crips (b. 1953)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gang leader, founded the <a href=\"https://wikipedia.org/wiki/Crips\" title=\"Crips\">Crips</a> (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gang leader, founded the <a href=\"https://wikipedia.org/wiki/Crips\" title=\"Crips\">Crips</a> (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Raymond_<PERSON>"}, {"title": "Crips", "link": "https://wikipedia.org/wiki/Crips"}]}, {"year": "1980", "text": "<PERSON>, American pilot (b. 1906)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American civil rights activist (b. 1909)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Austrian-born car importer and businessman (b. 1904)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born car importer and businessman (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born car importer and businessman (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player and coach (b. 1927)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON> (Irish republican), Chief of Staff of the Irish Republican Army (b. 1914)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON><PERSON><PERSON> (Irish republican)\"><PERSON><PERSON><PERSON> (Irish republican)</a>, <a href=\"https://wikipedia.org/wiki/Chief_of_Staff_of_the_Irish_Republican_Army\" title=\"Chief of Staff of the Irish Republican Army\">Chief of Staff of the Irish Republican Army</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON><PERSON><PERSON> (Irish republican)\"><PERSON><PERSON><PERSON> (Irish republican)</a>, <a href=\"https://wikipedia.org/wiki/Chief_of_Staff_of_the_Irish_Republican_Army\" title=\"Chief of Staff of the Irish Republican Army\">Chief of Staff of the Irish Republican Army</a> (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_(Irish_republican)"}, {"title": "Chief of Staff of the Irish Republican Army", "link": "https://wikipedia.org/wiki/Chief_of_Staff_of_the_Irish_Republican_Army"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American author, educator, poet, and playwright (b. 1919)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, educator, poet, and playwright (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American author, educator, poet, and playwright (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer (b. 1905)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Scelsi"}]}, {"year": "1990", "text": "<PERSON>, English footballer and manager (b. 1914)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian singer and actor (b. 1938)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian singer and actor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian singer and actor (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1942)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English soldier and engineer, invented the jet engine (b. 1907)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and engineer, invented the <a href=\"https://wikipedia.org/wiki/Jet_engine\" title=\"Jet engine\">jet engine</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and engineer, invented the <a href=\"https://wikipedia.org/wiki/Jet_engine\" title=\"Jet engine\">jet engine</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jet engine", "link": "https://wikipedia.org/wiki/Jet_engine"}]}, {"year": "1999", "text": "<PERSON>, English sports journalist and sportscaster (b. 1956)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sports journalist and sportscaster (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sports journalist and sportscaster (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Egyptian journalist and politician (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian journalist and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian journalist and politician (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Hungarian-American economist and academic, Nobel Prize laureate (b. 1920)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2000", "text": "<PERSON>, American murder victim (b. 1984)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\"><PERSON></a>, American murder victim (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\"><PERSON></a>, American murder victim (b. 1984)", "links": [{"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English guitarist (b. 1953)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, French director and screenwriter (b. 1929)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English footballer and manager (b. 1945)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor, dancer, and choreographer (b. 1946)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and choreographer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and choreographer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Sri Lankan lawyer, journalist, and academic (b. 1931)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer, journalist, and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer, journalist, and academic (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2004", "text": "<PERSON>, French lawyer and politician, Lord Chancellor of France (b. 1908)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_France\" class=\"mw-redirect\" title=\"Lord Chancellor of France\">Lord Chancellor of France</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_France\" class=\"mw-redirect\" title=\"Lord Chancellor of France\">Lord Chancellor of France</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Chancellor of France", "link": "https://wikipedia.org/wiki/Lord_Chancellor_of_France"}]}, {"year": "2004", "text": "<PERSON>, American guitarist and composer (b. 1918)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American composer and educator (b. 1912)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American author (b. 1935)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English author (b. 1914)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American physicist and academic (b. 1914)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American photographer and journalist (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell_(photojournalist)\" title=\"<PERSON> (photojournalist)\"><PERSON></a>, American photographer and journalist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell_(photojournalist)\" title=\"<PERSON> (photojournalist)\"><PERSON></a>, American photographer and journalist (b. 1922)", "links": [{"title": "<PERSON> (photojournalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell_(photojournalist)"}]}, {"year": "2008", "text": "<PERSON>, American comedian, actor, screenwriter, and producer (b. 1957)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mac\" title=\"<PERSON> Mac\"><PERSON></a>, American comedian, actor, screenwriter, and producer (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mac\" title=\"<PERSON> Mac\"><PERSON></a>, American comedian, actor, screenwriter, and producer (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Palestinian author and poet (b. 1941)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian author and poet (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian author and poet (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American singer and bass player (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Fuzz%22_<PERSON>\" title='<PERSON> \"Fuzz\" <PERSON>'><PERSON> \"Fuzz\" <PERSON></a>, American singer and bass player (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Fuzz%22_<PERSON>\" title='<PERSON> \"Fuzz\" <PERSON>'><PERSON> \"Fuzz\" <PERSON></a>, American singer and bass player (b. 1926)", "links": [{"title": "<PERSON> \"Fuzz\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Fuzz%22_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American soldier, lawyer, and politician (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American record producer (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(record_producer)\" title=\"<PERSON> (record producer)\"><PERSON></a>, American record producer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(record_producer)\" title=\"<PERSON> (record producer)\"><PERSON></a>, American record producer (b. 1934)", "links": [{"title": "<PERSON> (record producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(record_producer)"}]}, {"year": "2012", "text": "<PERSON>, American engineer, theorist, and academic (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, theorist, and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, theorist, and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Jr., American actor, director, and educator (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor, director, and educator (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor, director, and educator (b. 1934)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2012", "text": "<PERSON>, Canadian-American actor and journalist (b. 1964)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and journalist (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and journalist (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Puerto Rican-American actress (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actress (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Richardson"}]}, {"year": "2012", "text": "<PERSON>, American director and producer (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and coach (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (b. 1923)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2013", "text": "<PERSON>, Argentinian guitarist and composer (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA\" title=\"<PERSON>\"><PERSON></a>, Argentinian guitarist and composer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA\" title=\"<PERSON>\"><PERSON></a>, Argentinian guitarist and composer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eduardo_Fal%C3%BA"}]}, {"year": "2013", "text": "<PERSON>, Jr., American lawyer and politician (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and politician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and politician (b. 1947)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, Nigerian historian and academic (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Nigerian historian and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Nigerian historian and academic (b. 1929)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and coach (b. 1958)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and coach (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and coach (b. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "2014", "text": "<PERSON>, American businessman and philanthropist, co-founded Arlen Realty and Development Corporation (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Arlen_Realty_and_Development_Corporation\" title=\"Arlen Realty and Development Corporation\">Arlen Realty and Development Corporation</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Arlen_Realty_and_Development_Corporation\" title=\"Arlen Realty and Development Corporation\">Arlen Realty and Development Corporation</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Arlen Realty and Development Corporation", "link": "https://wikipedia.org/wiki/Arlen_Realty_and_Development_Corporation"}]}, {"year": "2014", "text": "<PERSON>, American actor (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American football player, sportscaster, and actor (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American computer scientist and academic (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Honduran footballer (b. 1977)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAn_L%C3%B3pez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Honduran footballer (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAn_L%C3%B3pez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Honduran footballer (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Walter_Nah%C3%BAn_L%C3%B3pez"}]}, {"year": "2015", "text": "<PERSON>, English author and screenwriter (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian journalist, author, and poet (b. 1915)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ki<PERSON>\"><PERSON><PERSON></a>, Indian journalist, author, and poet (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist, author, and poet (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Turkish painter and journalist (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish painter and journalist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish painter and journalist (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>am"}]}, {"year": "2016", "text": "<PERSON>, 6th Duke of Westminster, third-richest British citizen (b. 1951)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Duke_of_Westminster\" title=\"<PERSON>, 6th Duke of Westminster\"><PERSON>, 6th Duke <PERSON> Westminster</a>, third-richest British citizen (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Duke_of_Westminster\" title=\"<PERSON>, 6th Duke of Westminster\"><PERSON>, 6th Duke <PERSON> Westminster</a>, third-richest British citizen (b. 1951)", "links": [{"title": "<PERSON>, 6th Duke of Westminster", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Duke_of_Westminster"}]}, {"year": "2021", "text": "<PERSON>, English actress and producer (b. 1928)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, South African rapper, dancer and record producer (b. 1998)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Killer_Kau\" title=\"Killer Kau\"><PERSON></a>, South African rapper, dancer and record producer (b. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Killer_Kau\" title=\"Killer Kau\"><PERSON></a>, South African rapper, dancer and record producer (b. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>u"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON><PERSON>, Malaysian voice actress (b. 1972)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Malaysian voice actress (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Malaysian voice actress (b. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Canadian singer-songwriter, guitarist, producer, and actor (b. 1943)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, producer, and actor (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, producer, and actor (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Polish-American technology executive (b. 1968)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American technology executive (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American technology executive (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}