{"date": "November 27", "url": "https://wikipedia.org/wiki/November_27", "data": {"Events": [{"year": "25", "text": "Luoyang is declared capital of the Eastern Han dynasty by Emperor <PERSON><PERSON><PERSON> of Han.", "html": "25 - AD 25 - <a href=\"https://wikipedia.org/wiki/Luoyang\" title=\"Luoyang\">Luoyang</a> is <a href=\"https://wikipedia.org/wiki/History_of_the_Han_dynasty#Reconsolidation_under_<PERSON><PERSON><PERSON>\" title=\"History of the Han dynasty\">declared</a> capital of the <a href=\"https://wikipedia.org/wiki/Eastern_Han_dynasty\" class=\"mw-redirect\" title=\"Eastern Han dynasty\">Eastern Han dynasty</a> by <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON><PERSON> of Han\">Emperor <PERSON><PERSON><PERSON> of Han</a>.", "no_year_html": "AD 25 - <a href=\"https://wikipedia.org/wiki/Luoyang\" title=\"Luoyang\">Luoyang</a> is <a href=\"https://wikipedia.org/wiki/History_of_the_Han_dynasty#Reconsolidation_under_<PERSON><PERSON><PERSON>\" title=\"History of the Han dynasty\">declared</a> capital of the <a href=\"https://wikipedia.org/wiki/Eastern_Han_dynasty\" class=\"mw-redirect\" title=\"Eastern Han dynasty\">Eastern Han dynasty</a> by <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON><PERSON> of Han\">Emperor <PERSON><PERSON><PERSON> of Han</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luoyang"}, {"title": "History of the Han dynasty", "link": "https://wikipedia.org/wiki/History_of_the_Han_dynasty#Reconsolidation_under_<PERSON><PERSON><PERSON>"}, {"title": "Eastern Han dynasty", "link": "https://wikipedia.org/wiki/Eastern_Han_dynasty"}, {"title": "Emperor <PERSON><PERSON><PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Han"}]}, {"year": "176", "text": "Emperor <PERSON> grants his son <PERSON><PERSON><PERSON> the rank of \"Imperator\" and makes him Supreme Commander of the Roman legions.", "html": "176 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> grants his son <a href=\"https://wikipedia.org/wiki/Commodus\" title=\"Commodus\">Commodus</a> the rank of \"<a href=\"https://wikipedia.org/wiki/Imperator\" title=\"Imperator\">Imperator</a>\" and makes him Supreme Commander of the <a href=\"https://wikipedia.org/wiki/Roman_legion\" title=\"Roman legion\">Roman legions</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> grants his son <a href=\"https://wikipedia.org/wiki/Commodus\" title=\"Commodus\">Commodus</a> the rank of \"<a href=\"https://wikipedia.org/wiki/Imperator\" title=\"Imperator\">Imperator</a>\" and makes him Supreme Commander of the <a href=\"https://wikipedia.org/wiki/Roman_legion\" title=\"Roman legion\">Roman legions</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Commodus", "link": "https://wikipedia.org/wiki/Commodus"}, {"title": "Imperator", "link": "https://wikipedia.org/wiki/Imperator"}, {"title": "Roman legion", "link": "https://wikipedia.org/wiki/Roman_legion"}]}, {"year": "395", "text": "<PERSON><PERSON><PERSON>, praetorian prefect of the East, is murdered by Gothic mercenaries under <PERSON><PERSON><PERSON>.", "html": "395 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(consul)\" title=\"<PERSON><PERSON><PERSON> (consul)\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Praetorian_prefecture_of_the_East\" title=\"Praetorian prefecture of the East\">praetorian prefect of the East</a>, is murdered by <a href=\"https://wikipedia.org/wiki/Goths\" title=\"Goths\">Gothic</a> mercenaries under <a href=\"https://wikipedia.org/wiki/Gainas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(consul)\" title=\"<PERSON><PERSON><PERSON> (consul)\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Praetorian_prefecture_of_the_East\" title=\"Praetorian prefecture of the East\">praetorian prefect of the East</a>, is murdered by <a href=\"https://wikipedia.org/wiki/Goths\" title=\"Goths\">Gothic</a> mercenaries under <a href=\"https://wikipedia.org/wiki/Gainas\" title=\"<PERSON>ain<PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (consul)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(consul)"}, {"title": "Praetorian prefecture of the East", "link": "https://wikipedia.org/wiki/Praetorian_prefecture_of_the_East"}, {"title": "Goths", "link": "https://wikipedia.org/wiki/Goths"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ainas"}]}, {"year": "511", "text": "King <PERSON><PERSON><PERSON> dies at Lutetia and is buried in the Abbey of St Genevieve.", "html": "511 - King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lovis I\"><PERSON><PERSON><PERSON> <PERSON></a> dies at <a href=\"https://wikipedia.org/wiki/Lutetia\" title=\"Lu<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and is buried in the <a href=\"https://wikipedia.org/wiki/Abbey_of_St_Genevieve\" class=\"mw-redirect\" title=\"Abbey of St Genevieve\">Abbey of St Genevieve</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> I</a> dies at <a href=\"https://wikipedia.org/wiki/Lutetia\" title=\"Lu<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and is buried in the <a href=\"https://wikipedia.org/wiki/Abbey_of_St_Genevieve\" class=\"mw-redirect\" title=\"Abbey of St Genevieve\">Abbey of St Genevieve</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Lutetia", "link": "https://wikipedia.org/wiki/Lu<PERSON>tia"}, {"title": "Abbey of St Genevieve", "link": "https://wikipedia.org/wiki/Abbey_of_St_Genevieve"}]}, {"year": "602", "text": "Byzantine Emperor <PERSON> is forced to watch as the usurper <PERSON><PERSON><PERSON> executes his five sons before <PERSON> is beheaded himself.", "html": "602 - <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> is forced to watch as the usurper <a href=\"https://wikipedia.org/wiki/Phocas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> executes his five sons before <PERSON> is <a href=\"https://wikipedia.org/wiki/Decapitation\" title=\"Decapitation\">beheaded</a> himself.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> is forced to watch as the usurper <a href=\"https://wikipedia.org/wiki/Phocas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> executes his five sons before <PERSON> is <a href=\"https://wikipedia.org/wiki/Decapitation\" title=\"Decapitation\">beheaded</a> himself.", "links": [{"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(emperor)"}, {"title": "Phocas", "link": "https://wikipedia.org/wiki/Phocas"}, {"title": "Decapitation", "link": "https://wikipedia.org/wiki/Decapitation"}]}, {"year": "1095", "text": "<PERSON> <PERSON> declares the First Crusade at the Council of Clermont.", "html": "1095 - <a href=\"https://wikipedia.org/wiki/Pope_Urban_II\" title=\"Pope Urban II\"><PERSON> Urban II</a> declares the <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a> at the <a href=\"https://wikipedia.org/wiki/Council_of_Clermont\" title=\"Council of Clermont\">Council of Clermont</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Urban_II\" title=\"Pope Urban II\"><PERSON> Urban II</a> declares the <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a> at the <a href=\"https://wikipedia.org/wiki/Council_of_Clermont\" title=\"Council of Clermont\">Council of Clermont</a>.", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/Pope_Urban_II"}, {"title": "First Crusade", "link": "https://wikipedia.org/wiki/First_Crusade"}, {"title": "Council of Clermont", "link": "https://wikipedia.org/wiki/Council_of_Clermont"}]}, {"year": "1382", "text": "<PERSON><PERSON><PERSON><PERSON>, the last Qalawunid sultan, was deposed by <PERSON><PERSON><PERSON><PERSON> in 1382, ending the long period of the Turkic Bahri Mamluk period in general and particularly the Qalawunid dynasty and starting the reign of the Circassian Burji Mamluk.", "html": "1382 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>ih_Ha<PERSON>ji\" title=\"Al-<PERSON><PERSON> Hajji\"><PERSON><PERSON><PERSON><PERSON></a>, the last Qalawunid sultan, was deposed by <a href=\"https://wikipedia.org/wiki/Barquq\" title=\"Barquq\">Barquq</a> in 1382, ending the long period of the <a href=\"https://wikipedia.org/wiki/Turkic_peoples\" title=\"Turkic peoples\">Turkic</a> <a href=\"https://wikipedia.org/wiki/Bahri_Mamluks\" title=\"Bahri Mamluks\"><PERSON><PERSON><PERSON></a> period in general and particularly the <a href=\"https://wikipedia.org/wiki/Qalawunid_dynasty\" class=\"mw-redirect\" title=\"Qalawunid dynasty\">Qalawunid dynasty</a> and starting the reign of the <a href=\"https://wikipedia.org/wiki/Circassians\" title=\"Circassians\">Circassian</a> <a href=\"https://wikipedia.org/wiki/B<PERSON>ji_Ma<PERSON>luks\" title=\"Burji Ma<PERSON>luks\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Salih_Hajji\" title=\"Al-Salih Hajji\"><PERSON><PERSON><PERSON><PERSON></a>, the last Qalawunid sultan, was deposed by <a href=\"https://wikipedia.org/wiki/Barquq\" title=\"Barquq\">Barquq</a> in 1382, ending the long period of the <a href=\"https://wikipedia.org/wiki/Turkic_peoples\" title=\"Turkic peoples\">Turkic</a> <a href=\"https://wikipedia.org/wiki/Bahri_Mamluks\" title=\"Bahri Mamluks\"><PERSON><PERSON><PERSON></a> period in general and particularly the <a href=\"https://wikipedia.org/wiki/Qalawunid_dynasty\" class=\"mw-redirect\" title=\"Qalawunid dynasty\">Qalawunid dynasty</a> and starting the reign of the <a href=\"https://wikipedia.org/wiki/Circassians\" title=\"Circassians\">Circassian</a> <a href=\"https://wikipedia.org/wiki/B<PERSON>ji_Ma<PERSON>luks\" title=\"Burji Ma<PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>ji"}, {"title": "Barquq", "link": "https://wikipedia.org/wiki/Barquq"}, {"title": "Turkic peoples", "link": "https://wikipedia.org/wiki/Turkic_peoples"}, {"title": "Bahri Mamluks", "link": "https://wikipedia.org/wiki/Bahri_Mamluks"}, {"title": "Qalawunid dynasty", "link": "https://wikipedia.org/wiki/Qalawunid_dynasty"}, {"title": "Circassians", "link": "https://wikipedia.org/wiki/Circassians"}, {"title": "Burji Mamluks", "link": "https://wikipedia.org/wiki/Burji_Mamluks"}]}, {"year": "1542", "text": "Palace plot of Renyin year: A group of Ming dynasty palace women fail to murder the Jiajing Emperor, and are executed by slow-slicing.", "html": "1542 - <a href=\"https://wikipedia.org/wiki/Palace_plot_of_Renyin_year\" title=\"Palace plot of Renyin year\">Palace plot of Renyin year</a>: A group of <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> <a href=\"https://wikipedia.org/wiki/Lady-in-waiting\" title=\"Lady-in-waiting\">palace women</a> fail to murder the <a href=\"https://wikipedia.org/wiki/Jiajing_Emperor\" title=\"Jiajing Emperor\">Jiajing Emperor</a>, and are executed by <a href=\"https://wikipedia.org/wiki/Lingchi\" title=\"Lingchi\">slow-slicing</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Palace_plot_of_Renyin_year\" title=\"Palace plot of Renyin year\">Palace plot of Renyin year</a>: A group of <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> <a href=\"https://wikipedia.org/wiki/Lady-in-waiting\" title=\"Lady-in-waiting\">palace women</a> fail to murder the <a href=\"https://wikipedia.org/wiki/Jiajing_Emperor\" title=\"Jiajing Emperor\">Jiajing Emperor</a>, and are executed by <a href=\"https://wikipedia.org/wiki/Lingchi\" title=\"Lingchi\">slow-slicing</a>.", "links": [{"title": "Palace plot of Renyin year", "link": "https://wikipedia.org/wiki/Palace_plot_of_<PERSON><PERSON>n_year"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "Lady-in-waiting", "link": "https://wikipedia.org/wiki/Lady-in-waiting"}, {"title": "<PERSON><PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lingchi"}]}, {"year": "1727", "text": "The foundation stone to the Jerusalem Church in Berlin is laid.", "html": "1727 - The foundation stone to the <a href=\"https://wikipedia.org/wiki/Jerusalem_Church_(Berlin)\" title=\"Jerusalem Church (Berlin)\">Jerusalem Church</a> in <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a> is laid.", "no_year_html": "The foundation stone to the <a href=\"https://wikipedia.org/wiki/Jerusalem_Church_(Berlin)\" title=\"Jerusalem Church (Berlin)\">Jerusalem Church</a> in <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a> is laid.", "links": [{"title": "Jerusalem Church (Berlin)", "link": "https://wikipedia.org/wiki/Jerusalem_Church_(Berlin)"}, {"title": "Berlin", "link": "https://wikipedia.org/wiki/Berlin"}]}, {"year": "1755", "text": "An earthquake in northern Morocco devastates the cities of Fes and Meknes.", "html": "1755 - An <a href=\"https://wikipedia.org/wiki/1755_Meknes_earthquake\" title=\"1755 Meknes earthquake\">earthquake in northern Morocco</a> devastates the cities of <a href=\"https://wikipedia.org/wiki/Fes\" class=\"mw-redirect\" title=\"Fes\">Fes</a> and <a href=\"https://wikipedia.org/wiki/Meknes\" title=\"Meknes\">Meknes</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1755_Meknes_earthquake\" title=\"1755 Meknes earthquake\">earthquake in northern Morocco</a> devastates the cities of <a href=\"https://wikipedia.org/wiki/Fes\" class=\"mw-redirect\" title=\"Fes\">Fes</a> and <a href=\"https://wikipedia.org/wiki/Meknes\" title=\"Meknes\">Meknes</a>.", "links": [{"title": "1755 Meknes earthquake", "link": "https://wikipedia.org/wiki/1755_Meknes_earthquake"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fes"}, {"title": "Meknes", "link": "https://wikipedia.org/wiki/Meknes"}]}, {"year": "1809", "text": "The Berners Street hoax is perpetrated by <PERSON> in the City of Westminster, London.", "html": "1809 - The <a href=\"https://wikipedia.org/wiki/Berners_Street_hoax\" title=\"Berners Street hoax\">Berners Street hoax</a> is perpetrated by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/City_of_Westminster\" title=\"City of Westminster\">City of Westminster</a>, London.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Berners_Street_hoax\" title=\"Berners Street hoax\">Berners Street hoax</a> is perpetrated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/City_of_Westminster\" title=\"City of Westminster\">City of Westminster</a>, London.", "links": [{"title": "Berners Street hoax", "link": "https://wikipedia.org/wiki/Berners_Street_hoax"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "City of Westminster", "link": "https://wikipedia.org/wiki/City_of_Westminster"}]}, {"year": "1815", "text": "Adoption of the Constitution of the Kingdom of Poland.", "html": "1815 - Adoption of the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_Kingdom_of_Poland\" title=\"Constitution of the Kingdom of Poland\">Constitution of the Kingdom of Poland</a>.", "no_year_html": "Adoption of the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_Kingdom_of_Poland\" title=\"Constitution of the Kingdom of Poland\">Constitution of the Kingdom of Poland</a>.", "links": [{"title": "Constitution of the Kingdom of Poland", "link": "https://wikipedia.org/wiki/Constitution_of_the_Kingdom_of_Poland"}]}, {"year": "1830", "text": "<PERSON> experiences a Marian apparition.", "html": "1830 - Saint <a href=\"https://wikipedia.org/wiki/Catherine_Labour%C3%A9\" title=\"<PERSON>\"><PERSON></a> experiences a <a href=\"https://wikipedia.org/wiki/Marian_apparition\" title=\"Marian apparition\">Marian apparition</a>.", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a> experiences a <a href=\"https://wikipedia.org/wiki/Marian_apparition\" title=\"Marian apparition\">Marian apparition</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Catherine_Labour%C3%A9"}, {"title": "Marian apparition", "link": "https://wikipedia.org/wiki/Marian_apparition"}]}, {"year": "1835", "text": "<PERSON> and <PERSON> are hanged in London; they are the last two to be executed for sodomy in England.", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> are hanged in London; they are the last two to be executed for sodomy in England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> are hanged in London; they are the last two to be executed for sodomy in England.", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "In Boston, Massachusetts, the American Statistical Association is founded.", "html": "1839 - In <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a>, the <a href=\"https://wikipedia.org/wiki/American_Statistical_Association\" title=\"American Statistical Association\">American Statistical Association</a> is founded.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a>, the <a href=\"https://wikipedia.org/wiki/American_Statistical_Association\" title=\"American Statistical Association\">American Statistical Association</a> is founded.", "links": [{"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}, {"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}, {"title": "American Statistical Association", "link": "https://wikipedia.org/wiki/American_Statistical_Association"}]}, {"year": "1856", "text": "The Coup of 1856 leads to Luxembourg's unilateral adoption of a new, reactionary constitution.", "html": "1856 - The <a href=\"https://wikipedia.org/wiki/Luxembourg_Coup_of_1856\" class=\"mw-redirect\" title=\"Luxembourg Coup of 1856\">Coup of 1856</a> leads to <a href=\"https://wikipedia.org/wiki/Luxembourg\" title=\"Luxembourg\">Luxembourg</a>'s unilateral adoption of a new, reactionary <a href=\"https://wikipedia.org/wiki/Constitution_of_Luxembourg\" title=\"Constitution of Luxembourg\">constitution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Luxembourg_Coup_of_1856\" class=\"mw-redirect\" title=\"Luxembourg Coup of 1856\">Coup of 1856</a> leads to <a href=\"https://wikipedia.org/wiki/Luxembourg\" title=\"Luxembourg\">Luxembourg</a>'s unilateral adoption of a new, reactionary <a href=\"https://wikipedia.org/wiki/Constitution_of_Luxembourg\" title=\"Constitution of Luxembourg\">constitution</a>.", "links": [{"title": "Luxembourg Coup of 1856", "link": "https://wikipedia.org/wiki/Luxembourg_Coup_of_1856"}, {"title": "Luxembourg", "link": "https://wikipedia.org/wiki/Luxembourg"}, {"title": "Constitution of Luxembourg", "link": "https://wikipedia.org/wiki/Constitution_of_Luxembourg"}]}, {"year": "1863", "text": "American Civil War: Confederate cavalry leader <PERSON> and several of his men escape the Ohio Penitentiary and return safely to the South.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> cavalry leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and several of his men escape the <a href=\"https://wikipedia.org/wiki/Ohio_Penitentiary\" title=\"Ohio Penitentiary\">Ohio Penitentiary</a> and return safely to the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">South</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> cavalry leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and several of his men escape the <a href=\"https://wikipedia.org/wiki/Ohio_Penitentiary\" title=\"Ohio Penitentiary\">Ohio Penitentiary</a> and return safely to the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">South</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Ohio Penitentiary", "link": "https://wikipedia.org/wiki/Ohio_Penitentiary"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1863", "text": "American Civil War: Battle of Mine Run: Union forces under General <PERSON> take up positions against troops led by Confederate General <PERSON>.", "html": "1863 - American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Mine_Run\" title=\"Battle of Mine Run\">Battle of Mine Run</a>: <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Union</a> forces under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> take up positions against troops led by <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Mine_Run\" title=\"Battle of Mine Run\">Battle of Mine Run</a>: <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Union</a> forces under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> take up positions against troops led by <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Battle of Mine Run", "link": "https://wikipedia.org/wiki/Battle_of_Mine_Run"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1868", "text": "American Indian Wars: Battle of Washita River: United States Army Lieutenant Colonel <PERSON> leads an attack on Cheyenne living on reservation land.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Washita_River\" class=\"mw-redirect\" title=\"Battle of Washita River\">Battle of Washita River</a>: <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> <a href=\"https://wikipedia.org/wiki/Lieutenant_Colonel\" class=\"mw-redirect\" title=\"Lieutenant Colonel\">Lieutenant Colonel</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads an attack on <a href=\"https://wikipedia.org/wiki/Cheyenne\" title=\"Cheyenne\">Cheyenne</a> living on reservation land.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Washita_River\" class=\"mw-redirect\" title=\"Battle of Washita River\">Battle of Washita River</a>: <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> <a href=\"https://wikipedia.org/wiki/Lieutenant_Colonel\" class=\"mw-redirect\" title=\"Lieutenant Colonel\">Lieutenant Colonel</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads an attack on <a href=\"https://wikipedia.org/wiki/Cheyenne\" title=\"Cheyenne\">Cheyenne</a> living on reservation land.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Battle of Washita River", "link": "https://wikipedia.org/wiki/Battle_of_Washita_River"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Lieutenant Colonel", "link": "https://wikipedia.org/wiki/Lieutenant_Colonel"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cheyenne", "link": "https://wikipedia.org/wiki/Cheyenne"}]}, {"year": "1879", "text": "War of the Pacific: Battle of Tarapacá: The confrontation between the Chilean Army and the Peruvian Army takes place in Tarapacá, the Peruvian victory is consummated with the death of the 2 generals and the capture the Chilean general in said place of battle, headed by the Peruvian victory of General <PERSON>.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Tarapac%C3%A1\" title=\"Battle of Tarapacá\">Battle of Tarapacá</a>: The confrontation between the <a href=\"https://wikipedia.org/wiki/Chilean_Army\" title=\"Chilean Army\">Chilean Army</a> and the <a href=\"https://wikipedia.org/wiki/Peruvian_Army\" title=\"Peruvian Army\">Peruvian Army</a> takes place in <a href=\"https://wikipedia.org/wiki/Tarapac%C3%A1\" title=\"Tarapacá\">Tarapacá</a>, the Peruvian victory is consummated with the death of the 2 <a href=\"https://wikipedia.org/wiki/General\" class=\"mw-redirect\" title=\"General\">generals</a> and the capture the <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chilean</a> <a href=\"https://wikipedia.org/wiki/General\" class=\"mw-redirect\" title=\"General\">general</a> in said place of battle, headed by the Peruvian victory of General <a href=\"https://wikipedia.org/wiki/Juan_Buend%C3%ADa\" title=\"Juan Buendía\">Juan Buendía y Noregia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Tarapac%C3%A1\" title=\"Battle of Tarapacá\">Battle of Tarapacá</a>: The confrontation between the <a href=\"https://wikipedia.org/wiki/Chilean_Army\" title=\"Chilean Army\">Chilean Army</a> and the <a href=\"https://wikipedia.org/wiki/Peruvian_Army\" title=\"Peruvian Army\">Peruvian Army</a> takes place in <a href=\"https://wikipedia.org/wiki/Tarapac%C3%A1\" title=\"Tarapacá\">Tarapacá</a>, the Peruvian victory is consummated with the death of the 2 <a href=\"https://wikipedia.org/wiki/General\" class=\"mw-redirect\" title=\"General\">generals</a> and the capture the <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chilean</a> <a href=\"https://wikipedia.org/wiki/General\" class=\"mw-redirect\" title=\"General\">general</a> in said place of battle, headed by the Peruvian victory of General <a href=\"https://wikipedia.org/wiki/Juan_Buend%C3%ADa\" title=\"Juan Buendía\">Juan Buendía y Noregia</a>.", "links": [{"title": "War of the Pacific", "link": "https://wikipedia.org/wiki/War_of_the_Pacific"}, {"title": "Battle of Tarapacá", "link": "https://wikipedia.org/wiki/Battle_of_Tarapac%C3%A1"}, {"title": "Chilean Army", "link": "https://wikipedia.org/wiki/Chilean_Army"}, {"title": "Peruvian Army", "link": "https://wikipedia.org/wiki/Peruvian_Army"}, {"title": "Tarapacá", "link": "https://wikipedia.org/wiki/Tarapac%C3%A1"}, {"title": "General", "link": "https://wikipedia.org/wiki/General"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "General", "link": "https://wikipedia.org/wiki/General"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Buend%C3%ADa"}]}, {"year": "1895", "text": "At the Swedish-Norwegian Club in Paris, <PERSON> signs his last will and testament, setting aside his estate to establish the Nobel Prize after he dies.", "html": "1895 - At the Swedish-Norwegian Club in Paris, <a href=\"https://wikipedia.org/wiki/Alfred_Nobel\" title=\"Alfred Nobel\"><PERSON> Nobel</a> signs his <a href=\"https://wikipedia.org/wiki/Last_will_and_testament\" class=\"mw-redirect\" title=\"Last will and testament\">last will and testament</a>, setting aside his estate to establish the <a href=\"https://wikipedia.org/wiki/Nobel_Prize\" title=\"Nobel Prize\">Nobel Prize</a> after he dies.", "no_year_html": "At the Swedish-Norwegian Club in Paris, <a href=\"https://wikipedia.org/wiki/Alfred_Nobel\" title=\"Alfred Nobel\">Alfred Nobel</a> signs his <a href=\"https://wikipedia.org/wiki/Last_will_and_testament\" class=\"mw-redirect\" title=\"Last will and testament\">last will and testament</a>, setting aside his estate to establish the <a href=\"https://wikipedia.org/wiki/Nobel_Prize\" title=\"Nobel Prize\">Nobel Prize</a> after he dies.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Last will and testament", "link": "https://wikipedia.org/wiki/Last_will_and_testament"}, {"title": "Nobel Prize", "link": "https://wikipedia.org/wiki/Nobel_Prize"}]}, {"year": "1896", "text": "Also sprach <PERSON><PERSON><PERSON> by <PERSON> is first performed.", "html": "1896 - <i><a href=\"https://wikipedia.org/wiki/Also_sprach_Zarathustra\" title=\"Also sprach Zarathustra\">Also sprach Zarathustra</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is first performed.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Also_sprach_Zarathustra\" title=\"Also sprach Zarathustra\">Also sprach Zarathustra</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is first performed.", "links": [{"title": "Also sprach Zarathustra", "link": "https://wikipedia.org/wiki/Also_sprach_Zarathustra"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "The U.S. Army War College is established.", "html": "1901 - The <a href=\"https://wikipedia.org/wiki/U.S._Army_War_College\" class=\"mw-redirect\" title=\"U.S. Army War College\">U.S. Army War College</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/U.S._Army_War_College\" class=\"mw-redirect\" title=\"U.S. Army War College\">U.S. Army War College</a> is established.", "links": [{"title": "U.S. Army War College", "link": "https://wikipedia.org/wiki/U.S._Army_War_College"}]}, {"year": "1912", "text": "Spain declares a protectorate over the north shore of Morocco.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a> declares a <a href=\"https://wikipedia.org/wiki/Spanish_protectorate_in_Morocco\" title=\"Spanish protectorate in Morocco\">protectorate</a> over the north shore of <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a> declares a <a href=\"https://wikipedia.org/wiki/Spanish_protectorate_in_Morocco\" title=\"Spanish protectorate in Morocco\">protectorate</a> over the north shore of <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>.", "links": [{"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}, {"title": "Spanish protectorate in Morocco", "link": "https://wikipedia.org/wiki/Spanish_protectorate_in_Morocco"}, {"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}]}, {"year": "1917", "text": "<PERSON><PERSON> <PERSON><PERSON> becomes the chairman of his first senate, technically the first Prime Minister of Finland.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>hufvud\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> becomes the chairman of <a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>hufvud%27s_first_senate\" title=\"<PERSON><PERSON><PERSON>'s first senate\">his first senate</a>, technically the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>hufvud\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> becomes the chairman of <a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Svinhufvud%27s_first_senate\" title=\"<PERSON><PERSON><PERSON>'s first senate\">his first senate</a>, technically the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>d"}, {"title": "<PERSON><PERSON><PERSON>'s first senate", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>vinhufvud%27s_first_senate"}, {"title": "Prime Minister of Finland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Finland"}]}, {"year": "1918", "text": "The Makhnovshchina is established.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a> is established.", "links": [{"title": "Makhnovshchina", "link": "https://wikipedia.org/wiki/Makhnovshchina"}]}, {"year": "1924", "text": "In New York City, the first Macy's Thanksgiving Day Parade is held.", "html": "1924 - In New York City, the first <a href=\"https://wikipedia.org/wiki/Macy%27s_Thanksgiving_Day_Parade\" title=\"Macy's Thanksgiving Day Parade\">Macy's Thanksgiving Day Parade</a> is held.", "no_year_html": "In New York City, the first <a href=\"https://wikipedia.org/wiki/Macy%27s_Thanksgiving_Day_Parade\" title=\"Macy's Thanksgiving Day Parade\">Macy's Thanksgiving Day Parade</a> is held.", "links": [{"title": "Macy's Thanksgiving Day Parade", "link": "https://wikipedia.org/wiki/Macy%27s_Thanksgiving_Day_Parade"}]}, {"year": "1940", "text": "In Romania, the ruling Iron Guard fascist party assassinates over 60 of arrested King <PERSON> of Romania's aides and other political dissidents.", "html": "1940 - In <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>, the ruling <a href=\"https://wikipedia.org/wiki/Iron_Guard\" title=\"Iron Guard\">Iron Guard</a> fascist party <a href=\"https://wikipedia.org/wiki/Jilava_massacre\" title=\"Jilava massacre\">assassinates</a> over 60 of arrested King <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Romania\" title=\"<PERSON> II of Romania\"><PERSON> of Romania</a>'s aides and other political dissidents.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>, the ruling <a href=\"https://wikipedia.org/wiki/Iron_Guard\" title=\"Iron Guard\">Iron Guard</a> fascist party <a href=\"https://wikipedia.org/wiki/Jilava_massacre\" title=\"Jilava massacre\">assassinates</a> over 60 of arrested King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"Carol II of Romania\"><PERSON> of Romania</a>'s aides and other political dissidents.", "links": [{"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}, {"title": "Iron Guard", "link": "https://wikipedia.org/wiki/Iron_Guard"}, {"title": "Jilava massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_massacre"}, {"title": "<PERSON> of Romania", "link": "https://wikipedia.org/wiki/Carol_II_of_Romania"}]}, {"year": "1940", "text": "World War II: At the Battle of Cape Spartivento, the Royal Navy engages the Regia Marina in the Mediterranean Sea.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Cape_Spartivento\" title=\"Battle of Cape Spartivento\">Battle of Cape Spartivento</a>, the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> engages the <a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Regia Marina</a> in the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean Sea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Cape_Spartivento\" title=\"Battle of Cape Spartivento\">Battle of Cape Spartivento</a>, the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> engages the <a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Regia Marina</a> in the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean Sea</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Cape Spartivento", "link": "https://wikipedia.org/wiki/Battle_of_Cape_Spartivento"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Regia Marina", "link": "https://wikipedia.org/wiki/Regia_Marina"}, {"title": "Mediterranean Sea", "link": "https://wikipedia.org/wiki/Mediterranean_Sea"}]}, {"year": "1942", "text": "World War II: At Toulon, the French navy scuttles its ships and submarines to keep them out of Nazi hands.", "html": "1942 - World War II: At <a href=\"https://wikipedia.org/wiki/Toulon\" title=\"Toulon\">Toulon</a>, the French navy <a href=\"https://wikipedia.org/wiki/Scuttling_of_the_French_fleet_in_Toulon\" class=\"mw-redirect\" title=\"Scuttling of the French fleet in Toulon\">scuttles its ships and submarines</a> to keep them out of <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> hands.", "no_year_html": "World War II: At <a href=\"https://wikipedia.org/wiki/Toulon\" title=\"Toulon\">Toulon</a>, the French navy <a href=\"https://wikipedia.org/wiki/Scuttling_of_the_French_fleet_in_Toulon\" class=\"mw-redirect\" title=\"Scuttling of the French fleet in Toulon\">scuttles its ships and submarines</a> to keep them out of <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> hands.", "links": [{"title": "Toulon", "link": "https://wikipedia.org/wiki/Toulon"}, {"title": "Scuttling of the French fleet in Toulon", "link": "https://wikipedia.org/wiki/Scuttling_of_the_French_fleet_in_Toulon"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}]}, {"year": "1944", "text": "World War II: RAF Fauld explosion: An explosion at a Royal Air Force ammunition dump in Staffordshire kills seventy people.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/RAF_Fauld_explosion\" title=\"RAF Fauld explosion\">RAF Fauld explosion</a>: An explosion at a <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> ammunition dump in <a href=\"https://wikipedia.org/wiki/Staffordshire\" title=\"Staffordshire\">Staffordshire</a> kills seventy people.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/RAF_Fauld_explosion\" title=\"RAF Fauld explosion\">RAF Fauld explosion</a>: An explosion at a <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> ammunition dump in <a href=\"https://wikipedia.org/wiki/Staffordshire\" title=\"Staffordshire\">Staffordshire</a> kills seventy people.", "links": [{"title": "RAF Fauld explosion", "link": "https://wikipedia.org/wiki/RAF_Fauld_explosion"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}, {"title": "Staffordshire", "link": "https://wikipedia.org/wiki/Staffordshire"}]}, {"year": "1945", "text": "CARE (then the Cooperative for American Remittances to Europe) is founded to send CARE Packages of food relief to Europe after World War II.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/CARE_(relief_agency)\" class=\"mw-redirect\" title=\"CARE (relief agency)\">CARE</a> (then the Cooperative for American Remittances to Europe) is founded to send <a href=\"https://wikipedia.org/wiki/CARE_Package\" title=\"CARE Package\">CARE Packages</a> of food relief to Europe after World War II.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/CARE_(relief_agency)\" class=\"mw-redirect\" title=\"CARE (relief agency)\">CARE</a> (then the Cooperative for American Remittances to Europe) is founded to send <a href=\"https://wikipedia.org/wiki/CARE_Package\" title=\"CARE Package\">CARE Packages</a> of food relief to Europe after World War II.", "links": [{"title": "CARE (relief agency)", "link": "https://wikipedia.org/wiki/CARE_(relief_agency)"}, {"title": "CARE Package", "link": "https://wikipedia.org/wiki/CARE_Package"}]}, {"year": "1954", "text": "<PERSON><PERSON> is released from prison after serving 44 months for perjury.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hiss\"><PERSON><PERSON></a> is released from prison after serving 44 months for <a href=\"https://wikipedia.org/wiki/Perjury\" title=\"Perjury\">perjury</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hiss\"><PERSON><PERSON></a> is released from prison after serving 44 months for <a href=\"https://wikipedia.org/wiki/Perjury\" title=\"Perjury\">perjury</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hiss"}, {"title": "Perjury", "link": "https://wikipedia.org/wiki/Perjury"}]}, {"year": "1965", "text": "Vietnam War: The Pentagon tells U.S. President <PERSON> that if planned operations are to succeed, the number of American troops in Vietnam has to be increased from 120,000 to 400,000.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/The_Pentagon\" title=\"The Pentagon\">The Pentagon</a> tells U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> that if planned operations are to succeed, the number of American troops in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> has to be increased from 120,000 to 400,000.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/The_Pentagon\" title=\"The Pentagon\">The Pentagon</a> tells U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> that if planned operations are to succeed, the number of American troops in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> has to be increased from 120,000 to 400,000.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "The Pentagon", "link": "https://wikipedia.org/wiki/The_Pentagon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1968", "text": "<PERSON> becomes the first woman to play in a major professional men's basketball league, for the Kentucky Colonels in an ABA game against the Los Angeles Stars.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to play in a major professional men's basketball league, for the <a href=\"https://wikipedia.org/wiki/Kentucky_Colonels\" title=\"Kentucky Colonels\">Kentucky Colonels</a> in an <a href=\"https://wikipedia.org/wiki/American_Basketball_Association\" title=\"American Basketball Association\">ABA</a> game against the <a href=\"https://wikipedia.org/wiki/Los_Angeles_Stars\" class=\"mw-redirect\" title=\"Los Angeles Stars\">Los Angeles Stars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to play in a major professional men's basketball league, for the <a href=\"https://wikipedia.org/wiki/Kentucky_Colonels\" title=\"Kentucky Colonels\">Kentucky Colonels</a> in an <a href=\"https://wikipedia.org/wiki/American_Basketball_Association\" title=\"American Basketball Association\">ABA</a> game against the <a href=\"https://wikipedia.org/wiki/Los_Angeles_Stars\" class=\"mw-redirect\" title=\"Los Angeles Stars\">Los Angeles Stars</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Kentucky Colonels", "link": "https://wikipedia.org/wiki/Kentucky_Colonels"}, {"title": "American Basketball Association", "link": "https://wikipedia.org/wiki/American_Basketball_Association"}, {"title": "Los Angeles Stars", "link": "https://wikipedia.org/wiki/Los_Angeles_Stars"}]}, {"year": "1971", "text": "The Soviet space program's Mars 2 orbiter releases a descent module. It malfunctions and crashes, but it is the first man-made object to reach the surface of Mars.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Soviet_space_program\" title=\"Soviet space program\">Soviet space program</a>'s <a href=\"https://wikipedia.org/wiki/Mars_probe_program\" class=\"mw-redirect\" title=\"Mars probe program\"><i>Mars 2</i></a> orbiter releases a descent module. It malfunctions and crashes, but it is the first man-made object to reach the surface of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_space_program\" title=\"Soviet space program\">Soviet space program</a>'s <a href=\"https://wikipedia.org/wiki/Mars_probe_program\" class=\"mw-redirect\" title=\"Mars probe program\"><i>Mars 2</i></a> orbiter releases a descent module. It malfunctions and crashes, but it is the first man-made object to reach the surface of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "Soviet space program", "link": "https://wikipedia.org/wiki/Soviet_space_program"}, {"title": "Mars probe program", "link": "https://wikipedia.org/wiki/Mars_probe_program"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "1973", "text": "Twenty-fifth Amendment: The United States Senate votes 92-3 to confirm <PERSON> as Vice President of the United States. (On December 6, the House will confirm him 387-35).", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution\" title=\"Twenty-fifth Amendment to the United States Constitution\">Twenty-fifth Amendment</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> votes 92-3 to confirm <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>. (On <a href=\"https://wikipedia.org/wiki/December_6\" title=\"December 6\">December 6</a>, the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House</a> will confirm him 387-35).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution\" title=\"Twenty-fifth Amendment to the United States Constitution\">Twenty-fifth Amendment</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> votes 92-3 to confirm <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>. (On <a href=\"https://wikipedia.org/wiki/December_6\" title=\"December 6\">December 6</a>, the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House</a> will confirm him 387-35).", "links": [{"title": "Twenty-fifth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "December 6", "link": "https://wikipedia.org/wiki/December_6"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}]}, {"year": "1975", "text": "The Provisional IRA assassinates <PERSON>, after a press conference in which <PERSON><PERSON><PERSON><PERSON><PERSON> had announced a reward for the capture of those responsible for multiple bombings and shootings across England.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Provisional_IRA\" class=\"mw-redirect\" title=\"Provisional IRA\">Provisional IRA</a> assassinates <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, after a <a href=\"https://wikipedia.org/wiki/News_conference\" class=\"mw-redirect\" title=\"News conference\">press conference</a> in which <PERSON><PERSON><PERSON><PERSON><PERSON> had announced a reward for the capture of those responsible for multiple bombings and shootings across <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Provisional_IRA\" class=\"mw-redirect\" title=\"Provisional IRA\">Provisional IRA</a> assassinates <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, after a <a href=\"https://wikipedia.org/wiki/News_conference\" class=\"mw-redirect\" title=\"News conference\">press conference</a> in which <PERSON><PERSON><PERSON><PERSON><PERSON> had announced a reward for the capture of those responsible for multiple bombings and shootings across <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>.", "links": [{"title": "Provisional IRA", "link": "https://wikipedia.org/wiki/Provisional_IRA"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "News conference", "link": "https://wikipedia.org/wiki/News_conference"}, {"title": "England", "link": "https://wikipedia.org/wiki/England"}]}, {"year": "1978", "text": "In San Francisco, city mayor <PERSON> and openly gay city supervisor <PERSON> are assassinated by former supervisor <PERSON>.", "html": "1978 - In <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>, city mayor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and openly <a href=\"https://wikipedia.org/wiki/Gay\" title=\"Gay\">gay</a> <a href=\"https://wikipedia.org/wiki/City_supervisor\" class=\"mw-redirect\" title=\"City supervisor\">city supervisor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> are <a href=\"https://wikipedia.org/wiki/Moscone%E2%80%93Milk_assassinations\" title=\"Mo<PERSON><PERSON>-<PERSON> assassinations\">assassinated</a> by former supervisor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>, city mayor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and openly <a href=\"https://wikipedia.org/wiki/Gay\" title=\"Gay\">gay</a> <a href=\"https://wikipedia.org/wiki/City_supervisor\" class=\"mw-redirect\" title=\"City supervisor\">city supervisor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Milk\"><PERSON></a> are <a href=\"https://wikipedia.org/wiki/Moscone%E2%80%93Milk_assassinations\" title=\"<PERSON><PERSON><PERSON>-<PERSON> assassinations\">assassinated</a> by former supervisor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gay"}, {"title": "City supervisor", "link": "https://wikipedia.org/wiki/City_supervisor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Moscone-Milk assassinations", "link": "https://wikipedia.org/wiki/Moscone%E2%80%93Milk_assassinations"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "The Kurdistan Workers' Party (PKK) is founded in the Turkish village of Fis.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Kurdistan_Workers%27_Party\" title=\"Kurdistan Workers' Party\">Kurdistan Workers' Party</a> (PKK) is founded in the Turkish village of Fis.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kurdistan_Workers%27_Party\" title=\"Kurdistan Workers' Party\">Kurdistan Workers' Party</a> (PKK) is founded in the Turkish village of Fis.", "links": [{"title": "Kurdistan Workers' Party", "link": "https://wikipedia.org/wiki/Kurdistan_Workers%27_Party"}]}, {"year": "1983", "text": "Avianca Flight 011: A Boeing 747 crashes near Madrid's Barajas Airport, killing 181.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Avianca_Flight_011\" title=\"Avianca Flight 011\">Avianca Flight 011</a>: A <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a> crashes near <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>'s <a href=\"https://wikipedia.org/wiki/Barajas_Airport\" class=\"mw-redirect\" title=\"Barajas Airport\">Barajas Airport</a>, killing 181.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avianca_Flight_011\" title=\"Avianca Flight 011\">Avianca Flight 011</a>: A <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a> crashes near <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>'s <a href=\"https://wikipedia.org/wiki/Barajas_Airport\" class=\"mw-redirect\" title=\"Barajas Airport\">Barajas Airport</a>, killing 181.", "links": [{"title": "Avianca Flight 011", "link": "https://wikipedia.org/wiki/Avianca_Flight_011"}, {"title": "Boeing 747", "link": "https://wikipedia.org/wiki/Boeing_747"}, {"title": "Madrid", "link": "https://wikipedia.org/wiki/Madrid"}, {"title": "Barajas Airport", "link": "https://wikipedia.org/wiki/Barajas_Airport"}]}, {"year": "1984", "text": "Under the Brussels Agreement signed between the governments of the United Kingdom and Spain, the former agrees to enter into discussions with Spain over Gibraltar, including sovereignty.", "html": "1984 - Under the <a href=\"https://wikipedia.org/wiki/Brussels_Agreement,_1984\" class=\"mw-redirect\" title=\"Brussels Agreement, 1984\">Brussels Agreement</a> signed between the governments of the United Kingdom and Spain, the former agrees to enter into discussions with Spain over <a href=\"https://wikipedia.org/wiki/Gibraltar\" title=\"Gibraltar\">Gibraltar</a>, including sovereignty.", "no_year_html": "Under the <a href=\"https://wikipedia.org/wiki/Brussels_Agreement,_1984\" class=\"mw-redirect\" title=\"Brussels Agreement, 1984\">Brussels Agreement</a> signed between the governments of the United Kingdom and Spain, the former agrees to enter into discussions with Spain over <a href=\"https://wikipedia.org/wiki/Gibraltar\" title=\"Gibraltar\">Gibraltar</a>, including sovereignty.", "links": [{"title": "Brussels Agreement, 1984", "link": "https://wikipedia.org/wiki/Brussels_Agreement,_1984"}, {"title": "Gibraltar", "link": "https://wikipedia.org/wiki/Gibraltar"}]}, {"year": "1989", "text": "Avianca Flight 203: A Boeing 727 explodes in mid-air over Colombia, killing all 107 people on board and three people on the ground. The Medellín Cartel claimed responsibility for the attack.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Avianca_Flight_203\" title=\"Avianca Flight 203\">Avianca Flight 203</a>: A <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a> explodes in mid-air over <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, killing all 107 people on board and three people on the ground. The <a href=\"https://wikipedia.org/wiki/Medell%C3%ADn_Cartel\" title=\"Medellín Cartel\">Medellín Cartel</a> claimed responsibility for the attack.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avianca_Flight_203\" title=\"Avianca Flight 203\">Avianca Flight 203</a>: A <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a> explodes in mid-air over <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, killing all 107 people on board and three people on the ground. The <a href=\"https://wikipedia.org/wiki/Medell%C3%ADn_Cartel\" title=\"Medellín Cartel\">Medellín Car<PERSON></a> claimed responsibility for the attack.", "links": [{"title": "Avianca Flight 203", "link": "https://wikipedia.org/wiki/Avianca_Flight_203"}, {"title": "Boeing 727", "link": "https://wikipedia.org/wiki/Boeing_727"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}, {"title": "Medellín <PERSON>", "link": "https://wikipedia.org/wiki/Medell%C3%ADn_Cartel"}]}, {"year": "1992", "text": "For the second time in a year, military forces try to overthrow president <PERSON> in Venezuela.", "html": "1992 - For the second time in a year, <a href=\"https://wikipedia.org/wiki/Armed_forces\" class=\"mw-redirect\" title=\"Armed forces\">military forces</a> try to overthrow president <a href=\"https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9s_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>.", "no_year_html": "For the second time in a year, <a href=\"https://wikipedia.org/wiki/Armed_forces\" class=\"mw-redirect\" title=\"Armed forces\">military forces</a> try to overthrow president <a href=\"https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9s_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>.", "links": [{"title": "Armed forces", "link": "https://wikipedia.org/wiki/Armed_forces"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Andr%C3%A9s_P%C3%A9rez"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}]}, {"year": "1997", "text": "Twenty-five people are killed in the second Souhane massacre in Algeria.", "html": "1997 - Twenty-five people are killed in the second <a href=\"https://wikipedia.org/wiki/Souhane_massacre\" title=\"Souhane massacre\">Souhane massacre</a> in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "no_year_html": "Twenty-five people are killed in the second <a href=\"https://wikipedia.org/wiki/Souhane_massacre\" title=\"Souhane massacre\">Souhane massacre</a> in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "links": [{"title": "Souhane massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "1999", "text": "The centre-left Labour Party takes control of the New Zealand government with leader <PERSON> becoming the first elected female prime minister in New Zealand's history.", "html": "1999 - The centre-left <a href=\"https://wikipedia.org/wiki/New_Zealand_Labour_Party\" title=\"New Zealand Labour Party\">Labour Party</a> takes control of the <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a> government with leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becoming the first elected female prime minister in New Zealand's history.", "no_year_html": "The centre-left <a href=\"https://wikipedia.org/wiki/New_Zealand_Labour_Party\" title=\"New Zealand Labour Party\">Labour Party</a> takes control of the <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a> government with leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becoming the first elected female prime minister in New Zealand's history.", "links": [{"title": "New Zealand Labour Party", "link": "https://wikipedia.org/wiki/New_Zealand_Labour_Party"}, {"title": "New Zealand", "link": "https://wikipedia.org/wiki/New_Zealand"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "A hydrogen atmosphere is discovered on the extrasolar planet Osiris by the Hubble Space Telescope, the first atmosphere detected on an extrasolar planet.", "html": "2001 - A <a href=\"https://wikipedia.org/wiki/Hydrogen\" title=\"Hydrogen\">hydrogen</a> <a href=\"https://wikipedia.org/wiki/Celestial_body_atmosphere\" class=\"mw-redirect\" title=\"Celestial body atmosphere\">atmosphere</a> is discovered on the <a href=\"https://wikipedia.org/wiki/Extrasolar_planet\" class=\"mw-redirect\" title=\"Extrasolar planet\">extrasolar planet</a> <a href=\"https://wikipedia.org/wiki/Osiris_(planet)\" class=\"mw-redirect\" title=\"Osiris (planet)\">Osiris</a> by the <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a>, the first atmosphere detected on an extrasolar planet.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Hydrogen\" title=\"Hydrogen\">hydrogen</a> <a href=\"https://wikipedia.org/wiki/Celestial_body_atmosphere\" class=\"mw-redirect\" title=\"Celestial body atmosphere\">atmosphere</a> is discovered on the <a href=\"https://wikipedia.org/wiki/Extrasolar_planet\" class=\"mw-redirect\" title=\"Extrasolar planet\">extrasolar planet</a> <a href=\"https://wikipedia.org/wiki/Osiris_(planet)\" class=\"mw-redirect\" title=\"Osiris (planet)\">Osiris</a> by the <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a>, the first atmosphere detected on an extrasolar planet.", "links": [{"title": "Hydrogen", "link": "https://wikipedia.org/wiki/Hydrogen"}, {"title": "Celestial body atmosphere", "link": "https://wikipedia.org/wiki/Celestial_body_atmosphere"}, {"title": "Extrasolar planet", "link": "https://wikipedia.org/wiki/Extrasolar_planet"}, {"title": "<PERSON><PERSON><PERSON> (planet)", "link": "https://wikipedia.org/wiki/Osiris_(planet)"}, {"title": "<PERSON>bble Space Telescope", "link": "https://wikipedia.org/wiki/Hubble_Space_Telescope"}]}, {"year": "2004", "text": "Pope <PERSON> returns the relics of Saint <PERSON> to the Eastern Orthodox Church.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> returns the relics of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to the <a href=\"https://wikipedia.org/wiki/Eastern_Orthodox_Church\" title=\"Eastern Orthodox Church\">Eastern Orthodox Church</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> returns the relics of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to the <a href=\"https://wikipedia.org/wiki/Eastern_Orthodox_Church\" title=\"Eastern Orthodox Church\">Eastern Orthodox Church</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Eastern Orthodox Church", "link": "https://wikipedia.org/wiki/Eastern_Orthodox_Church"}]}, {"year": "2004", "text": "Blackwater 61 crash: A CASA C-212 Aviocar crashes into the Koh-i-Baba mountain range in Afghanistan, killing six.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Blackwater_61_crash\" title=\"Blackwater 61 crash\">Blackwater 61 crash</a>: A <a href=\"https://wikipedia.org/wiki/CASA_C-212_Aviocar\" title=\"CASA C-212 Aviocar\">CASA C-212 Aviocar</a> crashes into the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON><PERSON><PERSON></a> mountain range in Afghanistan, killing six.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Blackwater_61_crash\" title=\"Blackwater 61 crash\">Blackwater 61 crash</a>: A <a href=\"https://wikipedia.org/wiki/CASA_C-212_Aviocar\" title=\"CASA C-212 Aviocar\">CASA C-212 Aviocar</a> crashes into the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON><PERSON><PERSON></a> mountain range in Afghanistan, killing six.", "links": [{"title": "Blackwater 61 crash", "link": "https://wikipedia.org/wiki/Blackwater_61_crash"}, {"title": "CASA C-212 Aviocar", "link": "https://wikipedia.org/wiki/CASA_C-212_Aviocar"}, {"title": "Koh-i-Baba", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON>"}]}, {"year": "2006", "text": "The House of Commons of Canada approves a motion introduced by Prime Minister <PERSON> recognizing the Québécois as a nation within Canada.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_Canada\" title=\"House of Commons of Canada\">House of Commons of Canada</a> approves a motion introduced by <a href=\"https://wikipedia.org/wiki/Canadian_Prime_Minister\" class=\"mw-redirect\" title=\"Canadian Prime Minister\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> recognizing <a href=\"https://wikipedia.org/wiki/Qu%C3%A9b%C3%A9cois_nation_motion\" title=\"Québécois nation motion\">the Québécois as a nation</a> within Canada.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_Canada\" title=\"House of Commons of Canada\">House of Commons of Canada</a> approves a motion introduced by <a href=\"https://wikipedia.org/wiki/Canadian_Prime_Minister\" class=\"mw-redirect\" title=\"Canadian Prime Minister\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> recognizing <a href=\"https://wikipedia.org/wiki/Qu%C3%A9b%C3%A9cois_nation_motion\" title=\"Québécois nation motion\">the Québécois as a nation</a> within Canada.", "links": [{"title": "House of Commons of Canada", "link": "https://wikipedia.org/wiki/House_of_Commons_of_Canada"}, {"title": "Canadian Prime Minister", "link": "https://wikipedia.org/wiki/Canadian_Prime_Minister"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Québécois nation motion", "link": "https://wikipedia.org/wiki/Qu%C3%A9b%C3%A9cois_nation_motion"}]}, {"year": "2008", "text": "XL Airways Germany Flight 888T: An Airbus A320 performing a flight test crashes near the French commune of Canet-en-Roussillon, killing all seven people on board.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/XL_Airways_Germany_Flight_888T\" title=\"XL Airways Germany Flight 888T\">XL Airways Germany Flight 888T</a>: An <a href=\"https://wikipedia.org/wiki/Airbus_A320_family\" title=\"Airbus A320 family\">Airbus A320</a> performing a <a href=\"https://wikipedia.org/wiki/Flight_test\" title=\"Flight test\">flight test</a> crashes near the <a href=\"https://wikipedia.org/wiki/Communes_of_France\" title=\"Communes of France\">French commune</a> of <a href=\"https://wikipedia.org/wiki/Canet-en-Roussillon\" title=\"Canet-en-Roussillon\">Canet-en-Roussillon</a>, killing all seven people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/XL_Airways_Germany_Flight_888T\" title=\"XL Airways Germany Flight 888T\">XL Airways Germany Flight 888T</a>: An <a href=\"https://wikipedia.org/wiki/Airbus_A320_family\" title=\"Airbus A320 family\">Airbus A320</a> performing a <a href=\"https://wikipedia.org/wiki/Flight_test\" title=\"Flight test\">flight test</a> crashes near the <a href=\"https://wikipedia.org/wiki/Communes_of_France\" title=\"Communes of France\">French commune</a> of <a href=\"https://wikipedia.org/wiki/Canet-en-Roussillon\" title=\"Canet-en-Roussillon\">Canet-en-Roussillon</a>, killing all seven people on board.", "links": [{"title": "XL Airways Germany Flight 888T", "link": "https://wikipedia.org/wiki/XL_Airways_Germany_Flight_888T"}, {"title": "Airbus A320 family", "link": "https://wikipedia.org/wiki/Airbus_A320_family"}, {"title": "Flight test", "link": "https://wikipedia.org/wiki/Flight_test"}, {"title": "Communes of France", "link": "https://wikipedia.org/wiki/Communes_of_France"}, {"title": "Canet-en-Roussillon", "link": "https://wikipedia.org/wiki/Canet-en-Roussillon"}]}, {"year": "2009", "text": "Nevsky Express bombing: A bomb explodes on the Nevsky Express train between Moscow and Saint Petersburg, derailing it and causing 28 deaths and 96 injuries.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/2009_Nevsky_Express_bombing\" title=\"2009 Nevsky Express bombing\">Nevsky Express bombing</a>: A bomb explodes on the <a href=\"https://wikipedia.org/wiki/Nevsky_Express\" title=\"Nevsky Express\">Nevsky Express</a> train between <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a> and <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, derailing it and causing 28 deaths and 96 injuries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2009_Nevsky_Express_bombing\" title=\"2009 Nevsky Express bombing\">Nevsky Express bombing</a>: A bomb explodes on the <a href=\"https://wikipedia.org/wiki/Nevsky_Express\" title=\"Nevsky Express\">Nevsky Express</a> train between <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a> and <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, derailing it and causing 28 deaths and 96 injuries.", "links": [{"title": "2009 Nevsky Express bombing", "link": "https://wikipedia.org/wiki/2009_<PERSON><PERSON>sky_Express_bombing"}, {"title": "Nevsky Express", "link": "https://wikipedia.org/wiki/Nevsky_Express"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}]}, {"year": "2015", "text": "An active shooter inside a Planned Parenthood facility in Colorado Springs, Colorado, shoots at least four police officers. One officer later dies. Two civilians are also killed, and six injured. The shooter later surrendered.", "html": "2015 - An active shooter inside a <a href=\"https://wikipedia.org/wiki/Planned_Parenthood\" title=\"Planned Parenthood\">Planned Parenthood</a> facility in <a href=\"https://wikipedia.org/wiki/Colorado_Springs,_Colorado\" title=\"Colorado Springs, Colorado\">Colorado Springs, Colorado</a>, <a href=\"https://wikipedia.org/wiki/Colorado_Springs_Planned_Parenthood_shooting\" title=\"Colorado Springs Planned Parenthood shooting\">shoots</a> at least four <a href=\"https://wikipedia.org/wiki/Colorado_Springs_Police_Department\" title=\"Colorado Springs Police Department\">police officers</a>. One officer later dies. Two civilians are also killed, and six injured. The shooter later surrendered.", "no_year_html": "An active shooter inside a <a href=\"https://wikipedia.org/wiki/Planned_Parenthood\" title=\"Planned Parenthood\">Planned Parenthood</a> facility in <a href=\"https://wikipedia.org/wiki/Colorado_Springs,_Colorado\" title=\"Colorado Springs, Colorado\">Colorado Springs, Colorado</a>, <a href=\"https://wikipedia.org/wiki/Colorado_Springs_Planned_Parenthood_shooting\" title=\"Colorado Springs Planned Parenthood shooting\">shoots</a> at least four <a href=\"https://wikipedia.org/wiki/Colorado_Springs_Police_Department\" title=\"Colorado Springs Police Department\">police officers</a>. One officer later dies. Two civilians are also killed, and six injured. The shooter later surrendered.", "links": [{"title": "Planned Parenthood", "link": "https://wikipedia.org/wiki/Planned_Parenthood"}, {"title": "Colorado Springs, Colorado", "link": "https://wikipedia.org/wiki/Colorado_Springs,_Colorado"}, {"title": "Colorado Springs Planned Parenthood shooting", "link": "https://wikipedia.org/wiki/Colorado_Springs_Planned_Parenthood_shooting"}, {"title": "Colorado Springs Police Department", "link": "https://wikipedia.org/wiki/Colorado_Springs_Police_Department"}]}, {"year": "2020", "text": "Iran's top nuclear scientist, <PERSON><PERSON><PERSON>, is assassinated near Tehran.", "html": "2020 - Iran's top nuclear scientist, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>en_Fakhrizadeh\" title=\"<PERSON><PERSON><PERSON> Fakhrizadeh\"><PERSON><PERSON><PERSON></a>, is assassinated near <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>.", "no_year_html": "Iran's top nuclear scientist, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>en_Fakhrizadeh\" title=\"<PERSON><PERSON><PERSON> Fakhrizadeh\"><PERSON><PERSON><PERSON></a>, is assassinated near <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Fakhrizadeh"}, {"title": "Tehran", "link": "https://wikipedia.org/wiki/Tehran"}]}, {"year": "2020", "text": "Days after the announcement of its discovery, the Utah monolith is removed by recreationists.", "html": "2020 - Days after the announcement of its discovery, the <a href=\"https://wikipedia.org/wiki/Utah_monolith\" title=\"Utah monolith\">Utah monolith</a> is removed by recreationists.", "no_year_html": "Days after the announcement of its discovery, the <a href=\"https://wikipedia.org/wiki/Utah_monolith\" title=\"Utah monolith\">Utah monolith</a> is removed by recreationists.", "links": [{"title": "Utah monolith", "link": "https://wikipedia.org/wiki/Utah_monolith"}]}, {"year": "2024", "text": "Syrian rebel groups led by <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> launch a ground offensive into Syria.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Military_Operations_Command_(Syria)\" class=\"mw-redirect\" title=\"Military Operations Command (Syria)\">Syrian rebel groups</a> led by <a href=\"https://wikipedia.org/wiki/Hay%27at_Tahrir_al-Sham\" title=\"Hay'at Tahrir al-Sham\">Hay'at Tahrir al-Sham</a> launch a <a href=\"https://wikipedia.org/wiki/Northwestern_Syria_offensive_(2024)\" class=\"mw-redirect\" title=\"Northwestern Syria offensive (2024)\">ground offensive</a> into <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Military_Operations_Command_(Syria)\" class=\"mw-redirect\" title=\"Military Operations Command (Syria)\">Syrian rebel groups</a> led by <a href=\"https://wikipedia.org/wiki/Hay%27at_Tahrir_al-Sham\" title=\"Hay'at Tahrir al-Sham\">Hay'at Tahrir al-Sham</a> launch a <a href=\"https://wikipedia.org/wiki/Northwestern_Syria_offensive_(2024)\" class=\"mw-redirect\" title=\"Northwestern Syria offensive (2024)\">ground offensive</a> into <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>.", "links": [{"title": "Military Operations Command (Syria)", "link": "https://wikipedia.org/wiki/Military_Operations_Command_(Syria)"}, {"title": "Hay'at Tahrir al-Sham", "link": "https://wikipedia.org/wiki/Hay%27at_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "Northwestern Syria offensive (2024)", "link": "https://wikipedia.org/wiki/Northwestern_Syria_offensive_(2024)"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}], "Births": [{"year": "111", "text": "<PERSON><PERSON>, Greek favourite of <PERSON><PERSON> (d. 130)", "html": "111 - <a href=\"https://wikipedia.org/wiki/Antinous\" title=\"Antinous\"><PERSON><PERSON></a>, Greek favourite of <a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"Hadrian\"><PERSON><PERSON></a> (d. 130)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antinous\" title=\"Antinous\"><PERSON><PERSON></a>, Greek favourite of <a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"Hadrian\"><PERSON><PERSON></a> (d. 130)", "links": [{"title": "Antinous", "link": "https://wikipedia.org/wiki/Antinous"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1127", "text": "Emperor <PERSON><PERSON> of Song (d. 1194)", "html": "1127 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (d. 1194)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (d. 1194)", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1380", "text": "King <PERSON> of Aragon (d. 1416)", "html": "1380 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (d. 1416)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (d. 1416)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}]}, {"year": "1422", "text": "<PERSON>, Count of Foix, French nobleman (d. 1472)", "html": "1422 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Foix\" title=\"<PERSON>, Count of Foix\"><PERSON>, Count of Foix</a>, French nobleman (d. 1472)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Foix\" title=\"<PERSON>, Count of Foix\"><PERSON>, Count of Foix</a>, French nobleman (d. 1472)", "links": [{"title": "<PERSON>, Count of Foix", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1548", "text": "<PERSON><PERSON><PERSON>, Italian philosopher (d. 1598)", "html": "1548 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian philosopher (d. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian philosopher (d. 1598)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1558", "text": "<PERSON><PERSON>, Crown Prince of Burma (d. 1593)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Swa\" title=\"Mingyi Swa\"><PERSON><PERSON></a>, Crown Prince of Burma (d. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Swa\" title=\"Mingyi Swa\"><PERSON><PERSON></a>, Crown Prince of Burma (d. 1593)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ming<PERSON>_<PERSON>wa"}]}, {"year": "1576", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (d. 1638)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ats<PERSON>\" title=\"<PERSON><PERSON><PERSON> Tadatsune\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Tadatsune\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1638)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>une"}]}, {"year": "1582", "text": "<PERSON>, French historian and scholar (d. 1651)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scholar)\" title=\"<PERSON> (scholar)\"><PERSON></a>, French historian and scholar (d. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scholar)\" title=\"<PERSON> (scholar)\"><PERSON></a>, French historian and scholar (d. 1651)", "links": [{"title": "<PERSON> (scholar)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scholar)"}]}, {"year": "1586", "text": "Sir <PERSON>, 2nd Baronet, English politicians and Roundheads supporter (d. 1655) ", "html": "1586 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English politicians and Roundheads supporter (d. 1655) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English politicians and Roundheads supporter (d. 1655) ", "links": [{"title": "Sir <PERSON>, 2nd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet"}]}, {"year": "1630", "text": "<PERSON><PERSON><PERSON>, Archduke of Austria (d. 1665)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON><PERSON><PERSON>, Archduke of Austria\"><PERSON><PERSON><PERSON>, Archduke of Austria</a> (d. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON><PERSON><PERSON>, Archduke of Austria\"><PERSON><PERSON><PERSON>, Archduke of Austria</a> (d. 1665)", "links": [{"title": "<PERSON><PERSON><PERSON>, Archduke of Austria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>ke_of_Austria"}]}, {"year": "1635", "text": "<PERSON><PERSON>, Marquis<PERSON>, second wife of <PERSON> of France (d. 1719)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_d%27Aubign%C3%A9,_Marquis<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>, Marquis<PERSON>\"><PERSON><PERSON>, Marquis<PERSON></a>, second wife of <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_d%27Aubign%C3%A9,_Marquis<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>, Marquis<PERSON>\"><PERSON><PERSON>, Marquis<PERSON></a>, second wife of <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1719)", "links": [{"title": "<PERSON><PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7oise_d%27Aubign%C3%A9,_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XIV_of_France"}]}, {"year": "1640", "text": "<PERSON>, 1st Duchess of Cleveland (d. 1709)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duchess_of_Cleveland\" title=\"<PERSON>, 1st Duchess of Cleveland\"><PERSON>, 1st Duchess of Cleveland</a> (d. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duchess_of_Cleveland\" title=\"<PERSON>, 1st Duchess of Cleveland\"><PERSON>, 1st Duchess of Cleveland</a> (d. 1709)", "links": [{"title": "<PERSON>, 1st Duchess of Cleveland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duchess_of_Cleveland"}]}, {"year": "1701", "text": "<PERSON>, Swedish astronomer, physicist, and mathematician (d. 1744)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish astronomer, physicist, and mathematician (d. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish astronomer, physicist, and mathematician (d. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, English bishop and academic (d. 1787)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and academic (d. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and academic (d. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1746", "text": "<PERSON>, American lawyer and politician, 1st United States Secretary for Foreign Affairs (d. 1813)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(chancellor)\" class=\"mw-redirect\" title=\"<PERSON> (chancellor)\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_for_Foreign_Affairs\" class=\"mw-redirect\" title=\"United States Secretary for Foreign Affairs\">United States Secretary for Foreign Affairs</a> (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(chancellor)\" class=\"mw-redirect\" title=\"<PERSON> (chancellor)\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_for_Foreign_Affairs\" class=\"mw-redirect\" title=\"United States Secretary for Foreign Affairs\">United States Secretary for Foreign Affairs</a> (d. 1813)", "links": [{"title": "<PERSON> (chancellor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(chancellor)"}, {"title": "United States Secretary for Foreign Affairs", "link": "https://wikipedia.org/wiki/United_States_Secretary_for_Foreign_Affairs"}]}, {"year": "1746", "text": "Inc<PERSON>, American lawyer, jurist, and politician, 5th Governor of Massachusetts (d. 1799)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/Increase_<PERSON>\" title=\"Increase Sumner\"><PERSON><PERSON></a>, American lawyer, jurist, and politician, 5th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Increase_Sumner\" title=\"Increase Sumner\"><PERSON><PERSON></a>, American lawyer, jurist, and politician, 5th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1799)", "links": [{"title": "Increase Sumner", "link": "https://wikipedia.org/wiki/Increase_Sumner"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1754", "text": "<PERSON>, German-Polish ethnologist and journalist (d. 1794)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Polish ethnologist and journalist (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Polish ethnologist and journalist (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1759", "text": "<PERSON>, Czech violinist and composer (d. 1831)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and composer (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and composer (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON><PERSON>, du<PERSON> de Clermont<PERSON>, French general and politician, French Minister of Defence (d. 1865)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/Aim%C3%A9,_duc_<PERSON>_Clermont-Tonnerre\" title=\"<PERSON><PERSON>, duc de Clermont-Tonnerre\"><PERSON><PERSON>, duc de Clermont-Tonnerre</a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of Defence</a> (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aim%C3%A9,_duc_<PERSON>_Clermont-Tonnerre\" title=\"<PERSON><PERSON>, duc de Clermont-Tonnerre\"><PERSON><PERSON>, duc de Clermont-Tonnerre</a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of Defence</a> (d. 1865)", "links": [{"title": "Aimé, duc de Clermont-Tonnerre", "link": "https://wikipedia.org/wiki/Aim%C3%A9,_duc_<PERSON>_Clermont-Tonnerre"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1798", "text": "<PERSON><PERSON>, Boer leader after whom Pretoria was named, Prime Minister of the Natalia Republic (d. 1853)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/And<PERSON>_Pretorius\" title=\"<PERSON><PERSON> Pretorius\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Boers\" title=\"Boers\">Boer</a> leader after whom <a href=\"https://wikipedia.org/wiki/Pretoria\" title=\"Pretoria\">Pretoria</a> was named, Prime Minister of the <a href=\"https://wikipedia.org/wiki/Natalia_Republic\" title=\"Natalia Republic\">Natalia Republic</a> (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/And<PERSON>_Pretorius\" title=\"<PERSON><PERSON> Pretorius\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Boers\" title=\"Boers\">Boer</a> leader after whom <a href=\"https://wikipedia.org/wiki/Pretoria\" title=\"Pretoria\">Pretoria</a> was named, Prime Minister of the <a href=\"https://wikipedia.org/wiki/Natalia_Republic\" title=\"Natalia Republic\">Natalia Republic</a> (d. 1853)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/And<PERSON>_Pretorius"}, {"title": "Boers", "link": "https://wikipedia.org/wiki/Boers"}, {"title": "Pretoria", "link": "https://wikipedia.org/wiki/Pretoria"}, {"title": "Natalia Republic", "link": "https://wikipedia.org/wiki/Natalia_Republic"}]}, {"year": "1804", "text": "<PERSON>, German-English conductor and composer (d. 1885)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English conductor and composer (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English conductor and composer (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, English actress, playwright, and poet (d. 1893)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, playwright, and poet (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, playwright, and poet (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, marquis de <PERSON>Sémonville, French politician and diplomat, French ambassador to the United States (d. 1886)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A7ois-Fr%C3%A9d%C3%A9<PERSON>,_marquis_de_Montholon-S%C3%A9monville\" title=\"<PERSON><PERSON><PERSON>, marquis de Montholon-Sémonville\"><PERSON><PERSON><PERSON>, marquis de Montholon-Sémonville</a>, French politician and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_French_ambassadors_to_the_United_States\" class=\"mw-redirect\" title=\"List of French ambassadors to the United States\">French ambassador to the United States</a> (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A7ois-Fr%C3%A9d%C3%A<PERSON><PERSON>,_marquis_de_Montholon-S%C3%A9monville\" title=\"<PERSON><PERSON><PERSON>, marquis de Montholon-Sémonville\"><PERSON><PERSON><PERSON>, marquis de Montholon-Sé<PERSON></a>, French politician and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_French_ambassadors_to_the_United_States\" class=\"mw-redirect\" title=\"List of French ambassadors to the United States\">French ambassador to the United States</a> (d. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, marquis de <PERSON>olon-Sémonville", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A7ois-Fr%C3%A9d%C3%A9ric,_marquis_de_Montholon-S%C3%A9monville"}, {"title": "List of French ambassadors to the United States", "link": "https://wikipedia.org/wiki/List_of_French_ambassadors_to_the_United_States"}]}, {"year": "1820", "text": "<PERSON>, fourth woman to earn a medical degree in the United States (d. 1905)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fourth woman to earn a medical degree in the United States (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fourth woman to earn a medical degree in the United States (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, Scottish-Australian politician, 12th Premier of Victoria (d. 1899)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/James_Service\" title=\"James Service\">James Service</a>, Scottish-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/James_Service\" title=\"James Service\"><PERSON></a>, Scottish-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1899)", "links": [{"title": "James <PERSON>", "link": "https://wikipedia.org/wiki/James_Service"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1833", "text": "Princess <PERSON> of Cambridge (d. 1897)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Cambridge\" title=\"Princess <PERSON> of Cambridge\">Princess <PERSON> of Cambridge</a> (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Cambridge\" title=\"Princess <PERSON> of Cambridge\">Princess <PERSON> of Cambridge</a> (d. 1897)", "links": [{"title": "Princess <PERSON> of Cambridge", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_Adelaide_of_Cambridge"}]}, {"year": "1841", "text": "<PERSON><PERSON><PERSON>, Norwegian author (d. 1898)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian author (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian author (d. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American businessman (d. 1899)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON></a>, American businessman (d. 1899)", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, American artist and author (d. 1918)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and author (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and author (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>inshield"}]}, {"year": "1853", "text": "<PERSON>, English painter and illustrator (d. 1928)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, English physiologist, bacteriologist, and pathologist, Nobel Prize laureate (d. 1952)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist, bacteriologist, and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist, bacteriologist, and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1859", "text": "<PERSON>, American painter (d. 1886)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American environmental activist (d. 1949)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmental activist (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmental activist (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON>, Slovene priest, journalist, and politician (d. 1917)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Evangelist_Krek\" title=\"<PERSON>z Evangelist Krek\"><PERSON><PERSON> Evan<PERSON></a>, Slovene priest, journalist, and politician (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Evangelist_Krek\" title=\"<PERSON>z Evangelist Krek\"><PERSON><PERSON></a>, Slovene priest, journalist, and politician (d. 1917)", "links": [{"title": "<PERSON><PERSON> Evan<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Evangelist_Krek"}]}, {"year": "1867", "text": "<PERSON>, French composer and educator (d. 1950)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON>, Finnish academic and politician, 7th President of Finland (d. 1956)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish academic and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish academic and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1871", "text": "<PERSON>, Italian physicist and engineer (d. 1950)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and engineer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and engineer (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American historian, author, and educator, co-founded The New School (d. 1948)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and educator, co-founded <a href=\"https://wikipedia.org/wiki/The_New_School\" title=\"The New School\">The New School</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and educator, co-founded <a href=\"https://wikipedia.org/wiki/The_New_School\" title=\"The New School\">The New School</a> (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "The New School", "link": "https://wikipedia.org/wiki/The_New_School"}]}, {"year": "1874", "text": "<PERSON><PERSON>, Belarusian-Israeli chemist and politician, 1st President of Israel (d. 1952)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-Israeli chemist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-Israeli chemist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (d. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "1875", "text": "<PERSON>, Austrian gymnast (d. 1962)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian gymnast (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian gymnast (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON>, American biographer (d. 1965)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biographer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biographer (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian poet and critic (d. 1948)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and critic (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and critic (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American pole vaulter and coach (d. 1969)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter and coach (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter and coach (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, French actor (d. 1963)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Romanian author and playwright (d. 1944)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian author and playwright (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian author and playwright (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>anu"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  Japanese-French painter and printmaker  (d. 1968)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Tsuguharu_F<PERSON>jita\" title=\"Tsuguharu Foujita\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese-French painter and printmaker (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsuguharu_<PERSON>jita\" title=\"Tsuguharu Foujita\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese-French painter and printmaker (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsu<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese general (d. 1946)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general (d. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Indian activist and politician, 1st Speaker of the Lok Sabha (d. 1956)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist and politician, 1st <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist and politician, 1st <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a> (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the Lok Sabha", "link": "https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Japanese businessman, founded Panasonic (d. 1989)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/Panasonic\" title=\"Panasonic\">Panasonic</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/Panasonic\" title=\"Panasonic\">Panasonic</a> (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Panasonic", "link": "https://wikipedia.org/wiki/Panasonic"}]}, {"year": "1894", "text": "<PERSON>, American author and illustrator (d. 1977)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv, Ukrainian monk and saint (d. 1971)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Amphi<PERSON><PERSON><PERSON>_of_Pochayiv\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv</a>, Ukrainian monk and saint (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_of_Pochayiv\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv</a>, Ukrainian monk and saint (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv", "link": "https://wikipedia.org/wiki/<PERSON>phi<PERSON><PERSON><PERSON>_of_Pochayiv"}]}, {"year": "1898", "text": "<PERSON><PERSON>, English author and publisher (d. 1981)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and publisher (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and publisher (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ric_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Canadian journalist, author, and radio show host (d. 1981)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist, author, and radio show host (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist, author, and radio show host (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American sportscaster (d. 1962)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Norwegian-American chemist and physicist, Nobel Prize laureate (d. 1976)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, American actress (d. 1978)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>wyn"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Indian poet and author (d. 2003)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and author (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American historian and author (d. 2000)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pra<PERSON>_de_Camp\" title=\"L. Sprague de Camp\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, American historian and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>prague_de_Camp\" title=\"L. Sprague de Camp\"><PERSON><PERSON> <PERSON></a>, American historian and author (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_de_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American novelist, screenwriter, and critic (d. 1955)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter, and critic (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter, and critic (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and theorist (d. 1967)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and theorist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and theorist (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Filipino pediatrician and educator (d. 2011)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Fe_del_Mundo\" title=\"Fe del Mundo\"><PERSON> del Mundo</a>, Filipino pediatrician and educator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fe_del_Mundo\" title=\"Fe del Mundo\"><PERSON> del Mundo</a>, Filipino pediatrician and educator (d. 2011)", "links": [{"title": "Fe del Mundo", "link": "https://wikipedia.org/wiki/Fe_del_Mundo"}]}, {"year": "1911", "text": "<PERSON>, American director and producer (d. 2000)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American actress (d. 2018)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American sportscaster and actor (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Chick_Hearn\" title=\"Chick Hearn\"><PERSON><PERSON> Hearn</a>, American sportscaster and actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chick_Hearn\" title=\"Chick Hearn\"><PERSON><PERSON> Hearn</a>, American sportscaster and actor (d. 2002)", "links": [{"title": "<PERSON><PERSON>n", "link": "https://wikipedia.org/wiki/Chi<PERSON>_Hearn"}]}, {"year": "1917", "text": "<PERSON>, American actor and television host (d. 1998)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Buffalo <PERSON>\"><PERSON></a>, American actor and television host (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Buffalo <PERSON>\"><PERSON></a>, American actor and television host (d. 1998)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actor (d. 2005)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2005)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1920", "text": "<PERSON>, Dutch footballer (d. 1985)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English actor (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Automobile dealer and television personality (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Cal_Worthington\" title=\"Cal Worthington\"><PERSON></a>, Automobile dealer and television personality (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cal_Worthington\" title=\"Cal Worthington\"><PERSON></a>, Automobile dealer and television personality (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cal_Worthington"}]}, {"year": "1921", "text": "<PERSON>, American pilot and academic (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Slovak soldier and politician (d. 1992)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Slovak soldier and politician (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Slovak soldier and politician (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexander_Dub%C4%8Dek"}]}, {"year": "1922", "text": "<PERSON>, American director, producer, and screenwriter (d. 1993)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bartlett\"><PERSON></a>, American director, producer, and screenwriter (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bartlett"}]}, {"year": "1922", "text": "<PERSON>, American principal dancer and charter member of the New York City Ballet (d. 1977)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American principal dancer and charter member of the <a href=\"https://wikipedia.org/wiki/New_York_City_Ballet\" title=\"New York City Ballet\">New York City Ballet</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American principal dancer and charter member of the <a href=\"https://wikipedia.org/wiki/New_York_City_Ballet\" title=\"New York City Ballet\">New York City Ballet</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New York City Ballet", "link": "https://wikipedia.org/wiki/New_York_City_Ballet"}]}, {"year": "1923", "text": "<PERSON><PERSON> <PERSON>, American nuclear scientist, mechanical engineer and mathematician (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON>.</a>, American nuclear scientist, mechanical engineer and mathematician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, American nuclear scientist, mechanical engineer and mathematician (d. 2011)", "links": [{"title": "<PERSON><PERSON> <PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1925", "text": "<PERSON><PERSON>, American folk singer-songwriter and musician (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American folk singer-songwriter and musician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American folk singer-songwriter and musician (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Welsh chemist, physicist, and journalist (d. 2009)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh chemist, physicist, and journalist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh chemist, physicist, and journalist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor, director, and screenwriter (d. 1992)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English actor, comedian, singer, and screenwriter (d. 1999)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wise\"><PERSON></a>, English actor, comedian, singer, and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wise\"><PERSON></a>, English actor, comedian, singer, and screenwriter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, South Korean general (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-shin\" title=\"<PERSON><PERSON>-shin\"><PERSON><PERSON>-<PERSON>hin</a>, South Korean general (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-shin\" title=\"<PERSON><PERSON>-shin\"><PERSON><PERSON>-<PERSON>hin</a>, South Korean general (d. 2013)", "links": [{"title": "<PERSON><PERSON>hin", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-shin"}]}, {"year": "1927", "text": "<PERSON>, Brazilian footballer and manager (d. 1987)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American soldier and politician, 63rd United States Secretary of the Treasury (d. 2000)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 63rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 63rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Greek actor and director (d. 2005)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor and director (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor and director (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English painter and illustrator (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English screenwriter and producer (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scriptwriter)\" title=\"<PERSON> (scriptwriter)\"><PERSON></a>, English screenwriter and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(scriptwriter)\" title=\"<PERSON> (scriptwriter)\"><PERSON></a>, English screenwriter and producer (d. 2017)", "links": [{"title": "<PERSON> (scriptwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_(scriptwriter)"}]}, {"year": "1930", "text": "<PERSON>, American meteorologist (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American meteorologist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American meteorologist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Australian rugby league player and coach", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1930", "text": "<PERSON>, Singaporean engineer and author (d. 2009)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean engineer and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean engineer and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Filipino journalist and politician (d. 1983)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>.</a>, Filipino journalist and politician (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>.</a>, Filipino journalist and politician (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1933", "text": "<PERSON>, Canadian journalist, author, director, and screenwriter", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, author, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, author, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American historian and academic", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Iraqi footballer and manager (d. 2009)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ammo Baba\"><PERSON><PERSON></a>, Iraqi footballer and manager (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ammo Baba\"><PERSON><PERSON></a>, Iraqi footballer and manager (d. 2009)", "links": [{"title": "Ammo Baba", "link": "https://wikipedia.org/wiki/Ammo_Baba"}]}, {"year": "1934", "text": "<PERSON>, Jr., American drummer, songwriter, and producer (d. 1975)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American drummer, songwriter, and producer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American drummer, songwriter, and producer (d. 1975)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1934", "text": "<PERSON>, American mathematician and academic", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American director and producer (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, French musicologist and philosopher (d. 2008)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French musicologist and philosopher (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French musicologist and philosopher (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American boxer (d. 1997)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American journalist and author (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English biologist and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biologist)\" title=\"<PERSON> (biologist)\"><PERSON></a>, English biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(biologist)\" title=\"<PERSON> (biologist)\"><PERSON></a>, English biologist and academic", "links": [{"title": "<PERSON> (biologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(biologist)"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Ugandan academic and politician, Prime Minister of Uganda (d. 2019)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Apolo_Nsibambi\" title=\"Apolo Nsibambi\"><PERSON><PERSON><PERSON></a>, Ugandan academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Uganda\" title=\"Prime Minister of Uganda\">Prime Minister of Uganda</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apolo_Nsibambi\" title=\"Apolo Nsibambi\"><PERSON><PERSON><PERSON></a>, Ugandan academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Uganda\" title=\"Prime Minister of Uganda\">Prime Minister of Uganda</a> (d. 2019)", "links": [{"title": "Apolo Nsibambi", "link": "https://wikipedia.org/wiki/Apolo_Nsibambi"}, {"title": "Prime Minister of Uganda", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Uganda"}]}, {"year": "1939", "text": "<PERSON>, American baseball player and manager", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Congolese politician, President of the Democratic Republic of the Congo (d. 2001)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>-D%C3%A9sir%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Congolese politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Democratic_Republic_of_the_Congo\" title=\"List of heads of state of the Democratic Republic of the Congo\">President of the Democratic Republic of the Congo</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9sir%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Congolese politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Democratic_Republic_of_the_Congo\" title=\"List of heads of state of the Democratic Republic of the Congo\">President of the Democratic Republic of the Congo</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laurent-D%C3%A9sir%C3%A9_<PERSON><PERSON>a"}, {"title": "List of heads of state of the Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Democratic_Republic_of_the_Congo"}]}, {"year": "1939", "text": "<PERSON>, American singer and songwriter (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> C<PERSON>\"><PERSON></a>, American singer and songwriter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American-Chinese actor, martial artist, and screenwriter (d. 1973)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Chinese actor, martial artist, and screenwriter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Chinese actor, martial artist, and screenwriter (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American football player and sprinter (d. 2015)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sprinter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sprinter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, French footballer, coach, and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Aim%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aim%C3%A9_J<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aim%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1998)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Dutch pianist (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pianist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pianist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American poet and critic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ha<PERSON>\"><PERSON></a>, American poet and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and producer (d. 1970)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian author and poet", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, German fashion designer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American activist and politician (d. 1989)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor (d. 2013)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Dutch model and actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American trumpeter and flugelhornist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpeter and flugelhornist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpeter and flugelhornist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English race car driver (d. 2022)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Mariana Islander businessman and politician, 7th Governor of the Northern Mariana Islands", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mariana Islander businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_the_Northern_Mariana_Islands\" class=\"mw-redirect\" title=\"List of Governors of the Northern Mariana Islands\">Governor of the Northern Mariana Islands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mariana Islander businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_the_Northern_Mariana_Islands\" class=\"mw-redirect\" title=\"List of Governors of the Northern Mariana Islands\">Governor of the Northern Mariana Islands</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>igno_<PERSON>tial"}, {"title": "List of Governors of the Northern Mariana Islands", "link": "https://wikipedia.org/wiki/List_of_Governors_of_the_Northern_Mariana_Islands"}]}, {"year": "1945", "text": "<PERSON>, Australian journalist and television host (d. 2025)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and television host (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and television host (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American politician, 53rd Governor of New Jersey", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 53rd <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 53rd <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Ethiopian-Djiboutian lawyer and politician, President of Djibouti", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Isma%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian-Djiboutian lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Djibouti\" class=\"mw-redirect\" title=\"List of heads of state of Djibouti\">President of Djibouti</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isma%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian-Djiboutian lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Djibouti\" class=\"mw-redirect\" title=\"List of heads of state of Djibouti\">President of Djibouti</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isma%C3%AF<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of heads of state of Djibouti", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Djibouti"}]}, {"year": "1947", "text": "<PERSON>, American tenor and actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American basketball player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, English journalist and businessman", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and businessman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Brazilian actress, Miss Brasil 1969", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Brazilian actress, <a href=\"https://wikipedia.org/wiki/Miss_Brasil\" class=\"mw-redirect\" title=\"Miss Brasil\">Miss Brasil</a> 1969", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Brazilian actress, <a href=\"https://wikipedia.org/wiki/Miss_Brasil\" class=\"mw-redirect\" title=\"Miss Brasil\">Miss Brasil</a> 1969", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}, {"title": "Miss Brasil", "link": "https://wikipedia.org/wiki/Miss_Brasil"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Estonian singer and guitarist (d. 2004)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Graps\" title=\"<PERSON><PERSON> Graps\"><PERSON><PERSON></a>, Estonian singer and guitarist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Graps\" title=\"<PERSON><PERSON> Graps\"><PERSON><PERSON></a>, Estonian singer and guitarist (d. 2004)", "links": [{"title": "<PERSON><PERSON> G<PERSON>s", "link": "https://wikipedia.org/wiki/Gunn<PERSON>_Graps"}]}, {"year": "1952", "text": "<PERSON>, Canadian journalist and politician, 6th Deputy Prime Minister of Canada", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 6th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 6th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Indian singer-songwriter and producer (d. 2022)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter and producer (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1952", "text": "<PERSON>, American captain, engineer, and astronaut", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, engineer, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, engineer, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor, singer, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American media executive and political figure", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American media executive and political figure", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American media executive and political figure", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Russian singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Estonian admiral and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Tarmo_K%C3%B5uts\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian admiral and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tarmo_K%C3%B5uts\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian admiral and politician", "links": [{"title": "Tarmo K<PERSON>", "link": "https://wikipedia.org/wiki/Tarmo_K%C3%B5uts"}]}, {"year": "1953", "text": "<PERSON>, American keyboardist and composer (d. 2020)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboardist and composer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboardist and composer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American composer (d. 2001)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer (d. 2001)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1954", "text": "<PERSON>, English comedian, actor, and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, English comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, English comedian, actor, and screenwriter", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1955", "text": "<PERSON>, Canadian ice hockey player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American engineer, educator, and television host", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, educator, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, educator, and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English journalist and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON> of Perak, Sultan of Perak", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Perak\" title=\"<PERSON><PERSON><PERSON> of Perak\"><PERSON><PERSON><PERSON> of Perak</a>, Sultan of Perak", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Perak\" title=\"<PERSON><PERSON><PERSON> of Perak\"><PERSON><PERSON><PERSON> of Perak</a>, Sultan of Perak", "links": [{"title": "<PERSON><PERSON><PERSON> of Perak", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Perak"}]}, {"year": "1957", "text": "<PERSON>, Northern Irish race car driver", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Icelandic actress, singer, director and artist (d. 2016)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Edda_Hei%C3%B0r%C3%BAn_Backman\" title=\"<PERSON>da <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic actress, singer, director and artist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edda_Hei%C3%B0r%C3%BAn_Backman\" title=\"<PERSON>da <PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic actress, singer, director and artist (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edda_Hei%C3%B0r%C3%BAn_Backman"}]}, {"year": "1957", "text": "<PERSON>, Dutch singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American lawyer and diplomat, 27th United States Ambassador to Australia, daughter of President <PERSON>", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat, 27th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Australia\" class=\"mw-redirect\" title=\"United States Ambassador to Australia\">United States Ambassador to Australia</a>, daughter of President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat, 27th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Australia\" class=\"mw-redirect\" title=\"United States Ambassador to Australia\">United States Ambassador to Australia</a>, daughter of President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Australia", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Australia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American game designer and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American baseball player and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Scottish guitarist and songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Russian violinist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian violinist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viktor<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American author and illustrator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American football player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ken_O%27Brien"}]}, {"year": "1960", "text": "<PERSON>, American lawyer and politician, 39th Governor of Minnesota", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Minnesota", "link": "https://wikipedia.org/wiki/Governor_of_Minnesota"}]}, {"year": "1960", "text": "<PERSON>, American actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American composer and musician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American composer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American composer and musician", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Ukrainian economist and politician, 10th Prime Minister of Ukraine", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian economist and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Ukraine\" title=\"Prime Minister of Ukraine\">Prime Minister of Ukraine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian economist and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Ukraine\" title=\"Prime Minister of Ukraine\">Prime Minister of Ukraine</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Ukraine", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Ukraine"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Italian lawyer and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American drummer and songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American drummer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English-Canadian wrestler (d. 2002)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian wrestler (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian wrestler (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor, director, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Italian footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Japanese golfer (d. 2013)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese golfer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese golfer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American golfer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American television writer, producer and voice actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television writer, producer and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television writer, producer and voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English bass guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Barrow\" title=\"<PERSON>\"><PERSON></a>, English bass guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Barrow"}]}, {"year": "1968", "text": "<PERSON>, French-American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Panamanian singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/El_Chombo\" title=\"El Chombo\"><PERSON></a>, Panamanian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El_Chombo\" title=\"El Chombo\"><PERSON></a>, Panamanian singer-songwriter", "links": [{"title": "El Chombo", "link": "https://wikipedia.org/wiki/El_Chombo"}]}, {"year": "1969", "text": "<PERSON>, English politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Elizabeth_Marvel\" title=\"Elizabeth Marvel\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_Marvel\" title=\"Elizabeth Marvel\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American politician and businesswoman", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, South Korean writer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Austrian politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American football player (d. 2024)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Puerto Rican-American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Rodr%C3%ADguez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Rodr%C3%ADguez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_Rodr%C3%ADguez"}]}, {"year": "1971", "text": "<PERSON>, American basketball player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American screenwriter and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, South African actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Shar<PERSON><PERSON>_<PERSON>pley\" title=\"Shar<PERSON><PERSON> Copley\"><PERSON><PERSON><PERSON><PERSON></a>, South African actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shar<PERSON><PERSON>_<PERSON>y\" title=\"Shar<PERSON><PERSON>pley\"><PERSON><PERSON><PERSON><PERSON></a>, South African actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sharlto_Copley"}]}, {"year": "1973", "text": "<PERSON>, American model and television host", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American wrestler and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Japanese comedian, actor, sculptor, and potter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese comedian, actor, sculptor, and potter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese comedian, actor, sculptor, and potter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American football player and politician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, American rapper and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Twista\" title=\"Twista\">T<PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Twista\" title=\"Twista\"><PERSON><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Twista"}]}, {"year": "1974", "text": "<PERSON>, Northern Irish racing cyclist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish racing cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish racing cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Scottish actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American rapper (d. 2019)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_A<PERSON>_(rapper)\" title=\"<PERSON> Azz (rapper)\"><PERSON> Azz</a>, American rapper (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_A<PERSON>_(rapper)\" title=\"<PERSON> Azz (rapper)\"><PERSON> Azz</a>, American rapper (d. 2019)", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(rapper)"}]}, {"year": "1975", "text": "<PERSON>, Argentine-American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Gram%C3%A1tica\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentine-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Gram%C3%A1tica\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentine-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Gram%C3%A1tica"}]}, {"year": "1975", "text": "<PERSON>, Estonian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>enberg"}]}, {"year": "1976", "text": "<PERSON>, South African-American rapper and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American rapper and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player and firefighter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Chad_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and firefighter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chad_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and firefighter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American actor and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, British art historian", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Bendor_Grosvenor\" title=\"<PERSON><PERSON> Grosvenor\"><PERSON><PERSON></a>, British art historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bendor_Grosvenor\" title=\"<PERSON>or Grosvenor\"><PERSON><PERSON></a>, British art historian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bendor_Grosvenor"}]}, {"year": "1977", "text": "<PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Hungarian tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Eszter_Moln%C3%A1r\" title=\"<PERSON>sz<PERSON> Molnár\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eszter_Moln%C3%A1r\" title=\"<PERSON>sz<PERSON> Molnár\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "links": [{"title": "Eszter Molnár", "link": "https://wikipedia.org/wiki/Eszter_Moln%C3%A1r"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English rapper and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English rapper and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Czech tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%A0t%C4%9Bp%C3%A1nek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%A0t%C4%9Bp%C3%A1nek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radek_%C5%A0t%C4%9Bp%C3%A1nek"}]}, {"year": "1979", "text": "<PERSON>, American motocross racer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Motocross\" title=\"Motocross\">motocross</a> racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Motocross\" title=\"Motocross\">motocross</a> racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Motocross", "link": "https://wikipedia.org/wiki/Motocross"}]}, {"year": "1979", "text": "<PERSON>, American violinist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Finnish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Estonian singer and conductor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian singer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian singer and conductor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>smuth"}]}, {"year": "1980", "text": "<PERSON>, English cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Portuguese footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian mixed martial artist (d. 2016)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian mixed martial artist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian mixed martial artist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1982", "text": "<PERSON>, French footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Russian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English activist, co-founded the English Defence League", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist, co-founded the <a href=\"https://wikipedia.org/wiki/English_Defence_League\" title=\"English Defence League\">English Defence League</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Robinson\"><PERSON></a>, English activist, co-founded the <a href=\"https://wikipedia.org/wiki/English_Defence_League\" title=\"English Defence League\">English Defence League</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "English Defence League", "link": "https://wikipedia.org/wiki/English_Defence_League"}]}, {"year": "1983", "text": "<PERSON>, English rapper", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Professor_<PERSON>\" title=\"Professor <PERSON>\">Professor <PERSON></a>, English rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Professor <PERSON>\"><PERSON></a>, English rapper", "links": [{"title": "Professor <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Swedish-Kazakhstani ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Kazakhstani ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Kazakhstani ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American-Venezuelan basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Venezuelan basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Venezuelan basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American entrepreneur", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Peko\" title=\"Domata Peko\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Peko\" title=\"Domata Peko\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> Pek<PERSON>", "link": "https://wikipedia.org/wiki/Domata_Peko"}]}, {"year": "1985", "text": "<PERSON>, South Korean singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin"}]}, {"year": "1985", "text": "<PERSON>, Canadian actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Thilo_Versick\" title=\"Thilo Versick\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thilo_Versick\" title=\"Thilo Versick\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thilo_Versick"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American-Filipino footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Filipino footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Filipino footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter, producer, and dancer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Orits%C3%A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter, producer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orits%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter, producer, and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Orits%C3%A9_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Italian basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, English actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lynch\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Polish singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>yn"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, South Korean rapper, singer, songwriter, actor and model", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>ye<PERSON>\" title=\"<PERSON>ye<PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean rapper, singer, songwriter, actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean rapper, singer, songwriter, actor and model", "links": [{"title": "Chanyeol", "link": "https://wikipedia.org/wiki/<PERSON>yeol"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American actress and singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Fijian rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Suliasi_Vunivalu\" title=\"Suliasi Vunivalu\"><PERSON><PERSON><PERSON></a>, Fijian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suliasi_Vunivalu\" title=\"Suliasi Vunivalu\"><PERSON><PERSON><PERSON></a>, Fijian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suliasi_Vunivalu"}]}, {"year": "1996", "text": "<PERSON>, Dutch DJ and record producer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)\" title=\"<PERSON> (DJ)\"><PERSON></a>, Dutch DJ and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)\" title=\"<PERSON> (DJ)\"><PERSON></a>, Dutch DJ and record producer", "links": [{"title": "<PERSON> (DJ)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)"}]}, {"year": "2001", "text": "<PERSON>, American actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "8 BC", "text": "<PERSON>, Roman soldier and poet (b. 65 BC)", "html": "8 BC - 8 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman soldier and poet (b. 65 BC)", "no_year_html": "8 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman soldier and poet (b. 65 BC)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "395", "text": "<PERSON><PERSON><PERSON>, Roman politician (b. 335)", "html": "395 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(consul)\" title=\"<PERSON><PERSON><PERSON> (consul)\"><PERSON><PERSON><PERSON></a>, Roman politician (b. 335)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(consul)\" title=\"<PERSON><PERSON><PERSON> (consul)\"><PERSON><PERSON><PERSON></a>, Roman politician (b. 335)", "links": [{"title": "<PERSON><PERSON><PERSON> (consul)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(consul)"}]}, {"year": "450", "text": "<PERSON><PERSON><PERSON>, Roman Empress (b. 392)", "html": "450 - <a href=\"https://wikipedia.org/wiki/Galla_Placidia\" title=\"Galla Placidia\">Galla Placidia</a>, <a href=\"https://wikipedia.org/wiki/List_of_Roman_and_Byzantine_Empresses\" class=\"mw-redirect\" title=\"List of Roman and Byzantine Empresses\">Roman Empress</a> (b. 392)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Galla_Placidia\" title=\"Galla Placidia\">Galla Placidia</a>, <a href=\"https://wikipedia.org/wiki/List_of_Roman_and_Byzantine_Empresses\" class=\"mw-redirect\" title=\"List of Roman and Byzantine Empresses\">Roman Empress</a> (b. 392)", "links": [{"title": "Galla Placid<PERSON>", "link": "https://wikipedia.org/wiki/Galla_Placidia"}, {"title": "List of Roman and Byzantine Empresses", "link": "https://wikipedia.org/wiki/List_of_Roman_and_Byzantine_Empresses"}]}, {"year": "511", "text": "<PERSON><PERSON><PERSON>, king of the Franks", "html": "511 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lov<PERSON> I\"><PERSON><PERSON><PERSON></a>, king of the <a href=\"https://wikipedia.org/wiki/Franks\" title=\"<PERSON>\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, king of the <a href=\"https://wikipedia.org/wiki/Franks\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franks"}]}, {"year": "602", "text": "<PERSON>, Byzantine emperor (b. 539)", "html": "602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a>, Byzantine emperor (b. 539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a>, Byzantine emperor (b. 539)", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(emperor)"}]}, {"year": "639", "text": "<PERSON><PERSON><PERSON>, bishop of Doornik and Noyon", "html": "639 - <a href=\"https://wikipedia.org/wiki/<PERSON>car<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Tournai\" title=\"Roman Catholic Diocese of Tournai\">bishop of Doornik and Noyon</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>car<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Tournai\" title=\"Roman Catholic Diocese of Tournai\">bishop of Doornik and Noyon</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Acarius"}, {"title": "Roman Catholic Diocese of Tournai", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Tournai"}]}, {"year": "1198", "text": "<PERSON>, Queen of Sicily (b. 1154)", "html": "1198 - <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON>, Queen of Sicily</a> (b. 1154)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON>, Queen of Sicily</a> (b. 1154)", "links": [{"title": "Constance I of Sicily", "link": "https://wikipedia.org/wiki/Constance_I_of_Sicily"}]}, {"year": "1252", "text": "<PERSON> of Castile (b. 1188)", "html": "1252 - <a href=\"https://wikipedia.org/wiki/Blanche_of_Castile\" title=\"Blanche of Castile\"><PERSON> of Castile</a> (b. 1188)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Blanche_of_Castile\" title=\"Blanche of Castile\"><PERSON> of Castile</a> (b. 1188)", "links": [{"title": "Blanche of Castile", "link": "https://wikipedia.org/wiki/Blanche_of_Castile"}]}, {"year": "1346", "text": "<PERSON> of Sinai (b. c. 1260)", "html": "1346 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Sinai\" title=\"<PERSON> of Sinai\"><PERSON> of Sinai</a> (b. c. 1260)", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Sinai\" title=\"<PERSON> of Sinai\"><PERSON> of Sinai</a> (b. c. 1260)", "links": [{"title": "<PERSON> of Sinai", "link": "https://wikipedia.org/wiki/Gregory_of_Sinai"}]}, {"year": "1382", "text": "<PERSON>, Flemish patriot (b. 1340)", "html": "1382 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish patriot (b. 1340)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish patriot (b. 1340)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1474", "text": "<PERSON>, French composer and music theorist (b. 1397)", "html": "1474 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and music theorist (b. 1397)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and music theorist (b. 1397)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1570", "text": "<PERSON><PERSON><PERSON>, Italian sculptor and architect (b. 1486)", "html": "1570 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sculptor and architect (b. 1486)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sculptor and architect (b. 1486)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1592", "text": "<PERSON><PERSON><PERSON>, Japanese commander (b. 1568)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/Naka<PERSON>_Hidemasa\" title=\"Naka<PERSON> Hidemasa\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hidemasa\" title=\"Naka<PERSON> Hidemasa\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1568)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nakagawa_Hidemasa"}]}, {"year": "1620", "text": "<PERSON>, Duke of Pomerania-Stettin, Bishop of Cammin (b. 1577)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a>-<PERSON><PERSON><PERSON>, Bishop of Cammin (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a>-<PERSON><PERSON><PERSON>, Bishop of Cammin (b. 1577)", "links": [{"title": "<PERSON>, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Pomerania"}]}, {"year": "1632", "text": "<PERSON>, English politician (b. 1592)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(statesman)\" title=\"<PERSON> (statesman)\"><PERSON></a>, English politician (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(statesman)\" title=\"<PERSON> (statesman)\"><PERSON></a>, English politician (b. 1592)", "links": [{"title": "<PERSON> (statesman)", "link": "https://wikipedia.org/wiki/<PERSON>(statesman)"}]}, {"year": "1703", "text": "<PERSON>, English painter and engineer (b. 1644)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and engineer (b. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and engineer (b. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, French-English mathematician and theorist (b. 1667)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English mathematician and theorist (b. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English mathematician and theorist (b. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, Scottish engineer, designed the threshing machine (b. 1719)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer, designed the <a href=\"https://wikipedia.org/wiki/Threshing_machine\" title=\"Threshing machine\">threshing machine</a> (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer, designed the <a href=\"https://wikipedia.org/wiki/Threshing_machine\" title=\"Threshing machine\">threshing machine</a> (b. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Threshing machine", "link": "https://wikipedia.org/wiki/Threshing_machine"}]}, {"year": "1819", "text": "<PERSON><PERSON>, Irish-born American merchant sea captain, an officer in the Continental Navy and a privateer.", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-born American merchant sea captain, an officer in the <a href=\"https://wikipedia.org/wiki/Continental_Navy\" title=\"Continental Navy\">Continental Navy</a> and a <a href=\"https://wikipedia.org/wiki/Privateer\" title=\"Privateer\">privateer</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-born American merchant sea captain, an officer in the <a href=\"https://wikipedia.org/wiki/Continental_Navy\" title=\"Continental Navy\">Continental Navy</a> and a <a href=\"https://wikipedia.org/wiki/Privateer\" title=\"Privateer\">privateer</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Continental Navy", "link": "https://wikipedia.org/wiki/Continental_Navy"}, {"title": "Privateer", "link": "https://wikipedia.org/wiki/Privateer"}]}, {"year": "1822", "text": "<PERSON> <PERSON>, English barge horse, oldest recorded horse (b. 1760)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Old_Billy\" title=\"Old Billy\">Old Billy</a>, English <a href=\"https://wikipedia.org/wiki/Barge_horse\" class=\"mw-redirect\" title=\"Barge horse\">barge horse</a>, oldest recorded horse (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Old_Billy\" title=\"Old Billy\">Old Billy</a>, English <a href=\"https://wikipedia.org/wiki/Barge_horse\" class=\"mw-redirect\" title=\"Barge horse\">barge horse</a>, oldest recorded horse (b. 1760)", "links": [{"title": "Old Billy", "link": "https://wikipedia.org/wiki/Old_Billy"}, {"title": "Barge horse", "link": "https://wikipedia.org/wiki/Barge_horse"}]}, {"year": "1830", "text": "<PERSON>, Belgian-American architect (b. 1780)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON><PERSON>_(landscape_architect)\" title=\"<PERSON> (landscape architect)\"><PERSON></a>, Belgian-American architect (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>_(landscape_architect)\" title=\"<PERSON> (landscape architect)\"><PERSON></a>, Belgian-American architect (b. 1780)", "links": [{"title": "<PERSON> (landscape architect)", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON><PERSON>_(landscape_architect)"}]}, {"year": "1852", "text": "<PERSON>, English mathematician and computer scientist (b. 1815)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lace\" title=\"<PERSON> Lovelace\"><PERSON></a>, English mathematician and computer scientist (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lace\" title=\"<PERSON> Lovelace\"><PERSON></a>, English mathematician and computer scientist (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lace"}]}, {"year": "1875", "text": "<PERSON>, English astronomer and educator (b. 1826)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and educator (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and educator (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, German flute player and composer (b. 1794)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German flute player and composer (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German flute player and composer (b. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Austrian ballerina (b. 1810)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ballerina (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ballerina (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Indian Activist (b. 1827)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Phule\" class=\"mw-redirect\" title=\"Ma<PERSON><PERSON> Phule\"><PERSON><PERSON><PERSON></a>, Indian Activist (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Phule\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Phule\"><PERSON><PERSON><PERSON></a>, Indian Activist (b. 1827)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ule"}]}, {"year": "1895", "text": "<PERSON>, fils, French novelist and playwright (b. 1824)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_fils\" class=\"mw-redirect\" title=\"<PERSON>, fils\"><PERSON>, fils</a>, French novelist and playwright (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_fils\" class=\"mw-redirect\" title=\"<PERSON>, fils\"><PERSON>, fils</a>, French novelist and playwright (b. 1824)", "links": [{"title": "<PERSON>, fils", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_fils"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Swiss academic and politician, 10th President of the Swiss Council of States (b. 1819)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Constant_Fornerod\" title=\"Constant Fornerod\"><PERSON><PERSON></a>, Swiss academic and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Council_of_States\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Council of States\">President of the Swiss Council of States</a> (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constant_Fornerod\" title=\"Constant Fornerod\"><PERSON><PERSON></a>, Swiss academic and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Council_of_States\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Council of States\">President of the Swiss Council of States</a> (b. 1819)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constant_Fornerod"}, {"title": "List of Presidents of the Swiss Council of States", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Council_of_States"}]}, {"year": "1901", "text": "<PERSON>, American businessman, co-founded St<PERSON><PERSON><PERSON> (b. 1831)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Studebaker\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Studebaker</a> (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Stude<PERSON>ker\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON>baker</a> (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>ba<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1908", "text": "<PERSON>, French geologist and palaeontologist (b. 1827)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French geologist and palaeontologist (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French geologist and palaeontologist (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Belgian poet and playwright (b. 1855)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian poet and playwright (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian poet and playwright (b. 1855)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Panamanian pharmacist and politician (b. 1857)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ista\" title=\"<PERSON>\"><PERSON></a>, Panamanian pharmacist and politician (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian pharmacist and politician (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_Batista"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Ukrainian-Austrian philosopher and author (b. 1853)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Austrian philosopher and author (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Austrian philosopher and author (b. 1853)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>g"}]}, {"year": "1921", "text": "<PERSON>, Canadian contractor and politician, 8th Lieutenant Governor of Manitoba (b. 1854)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian contractor and politician, 8th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Manitoba\" title=\"Lieutenant Governor of Manitoba\">Lieutenant Governor of Manitoba</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian contractor and politician, 8th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Manitoba\" title=\"Lieutenant Governor of Manitoba\">Lieutenant Governor of Manitoba</a> (b. 1854)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Lieutenant Governor of Manitoba", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Manitoba"}]}, {"year": "1921", "text": "<PERSON>, Australian zoo owner (b. 1841)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian zoo owner (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian zoo owner (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Potawatomi political activist (b. 1851)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Potawatomi political activist (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Potawatomi political activist (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Slovak-American actress (b. 1899)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak-American actress (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak-American actress (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American criminal (b. 1908)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Baby_Face_Nelson\" title=\"Baby Face <PERSON>\"><PERSON> Face <PERSON></a>, American criminal (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Face_Nelson\" title=\"Baby Face Nelson\"><PERSON> Face <PERSON></a>, American criminal (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Greek-French businessman and philanthropist (b. 1849)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-French businessman and philanthropist (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-French businessman and philanthropist (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Romanian historian and politician, 34th Prime Minister of Romania (b. 1871)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian historian and politician, 34th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Romania\" class=\"mw-redirect\" title=\"List of Prime Ministers of Romania\">Prime Minister of Romania</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian historian and politician, 34th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Romania\" class=\"mw-redirect\" title=\"List of Prime Ministers of Romania\">Prime Minister of Romania</a> (b. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Romania", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Romania"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Croatian soldier and politician (b. 1916)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian soldier and politician (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian soldier and politician (b. 1916)", "links": [{"title": "<PERSON>vo <PERSON>", "link": "https://wikipedia.org/wiki/Ivo_<PERSON>_Riba<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Russian physicist and academic (b. 1879)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and academic (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and academic (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American playwright, Nobel Prize laureate (b. 1888)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, American playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, American playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_O%27Neill"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1955", "text": "<PERSON>, French-Swiss composer and academic (b. 1892)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss composer and academic (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss composer and academic (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Bulgarian politician, Head of State of Bulgaria (b. 1892)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Bulgaria\" title=\"List of heads of state of Bulgaria\">Head of State of Bulgaria</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Bulgaria\" title=\"List of heads of state of Bulgaria\">Head of State of Bulgaria</a> (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of Bulgaria", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Bulgaria"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Polish-American conductor (b. 1892)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Artur_<PERSON>zi%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American conductor (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artur_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American conductor (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artur_Rodzi%C5%84ski"}]}, {"year": "1960", "text": "<PERSON>, Irish-English cricketer (b. 1875)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English cricketer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English cricketer (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Dutch lawyer and politician, Prime Minister of the Netherlands (b. 1870)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1962", "text": "<PERSON>, Estonian footballer (b. 1903)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/August_Lass\" title=\"August Lass\">August <PERSON></a>, Estonian footballer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Lass\" title=\"August Lass\">August <PERSON></a>, Estonian footballer (b. 1903)", "links": [{"title": "August Lass", "link": "https://wikipedia.org/wiki/August_Lass"}]}, {"year": "1967", "text": "<PERSON>, Gabonese politician, 1st President of Gabon (b. 1902)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/L%C3%A9on_M%27ba\" title=\"<PERSON>a\"><PERSON></a>, Gabonese politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Gabon\" title=\"President of Gabon\">President of Gabon</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9on_M%27ba\" title=\"<PERSON>\"><PERSON></a>, Gabonese politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Gabon\" title=\"President of Gabon\">President of Gabon</a> (b. 1902)", "links": [{"title": "Léon M<PERSON>ba", "link": "https://wikipedia.org/wiki/L%C3%A9on_M%27ba"}, {"title": "President of Gabon", "link": "https://wikipedia.org/wiki/President_of_Gabon"}]}, {"year": "1969", "text": "<PERSON>, English Australian children's author, illustrator, and cartoonist, (b. 1877)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Australian children's author, illustrator, and cartoonist, (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Australian children's author, illustrator, and cartoonist, (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/May_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American swimmer and nurse (b. 1913)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American swimmer and nurse (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American swimmer and nurse (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American trumpet player (b. 1887)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)\" title=\"<PERSON> (trumpeter)\"><PERSON></a>, American trumpet player (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)\" title=\"<PERSON> (trumpeter)\"><PERSON></a>, American trumpet player (b. 1887)", "links": [{"title": "<PERSON> (trumpeter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)"}]}, {"year": "1975", "text": "<PERSON>, Italian automotive engineer (b. 1895)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian automotive engineer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian automotive engineer (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English author and activist, co-founded the Guinness Book of Records (b. 1925)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist, co-founded the <i><a href=\"https://wikipedia.org/wiki/Guinness_Book_of_Records\" class=\"mw-redirect\" title=\"Guinness Book of Records\">Guinness Book of Records</a></i> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist, co-founded the <i><a href=\"https://wikipedia.org/wiki/Guinness_Book_of_Records\" class=\"mw-redirect\" title=\"Guinness Book of Records\">Guinness Book of Records</a></i> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Guinness Book of Records", "link": "https://wikipedia.org/wiki/Guinness_Book_of_Records"}]}, {"year": "1977", "text": "<PERSON>, Estonian basketball player (b. 1936)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Mart_Laga\" title=\"Mart Laga\"><PERSON></a>, Estonian basketball player (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart_Laga\" title=\"Mart Laga\"><PERSON></a>, Estonian basketball player (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart_Laga"}]}, {"year": "1978", "text": "<PERSON>, American lieutenant and politician (b. 1930)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harvey Milk\"><PERSON></a>, American lieutenant and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harvey Milk\"><PERSON></a>, American lieutenant and politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American lawyer and politician, 37th Mayor of San Francisco (b. 1929)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of San Francisco", "link": "https://wikipedia.org/wiki/Mayor_of_San_Francisco"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American architect, designed the Villa Vizcaya (b. 1882)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Villa_Vizcaya\" class=\"mw-redirect\" title=\"Villa Vizcaya\">Villa Vizcaya</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Villa_Vizcaya\" class=\"mw-redirect\" title=\"Villa Vizcaya\">Villa Vizcaya</a> (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Villa Vizcaya", "link": "https://wikipedia.org/wiki/Villa_Vizcaya"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Austrian singer and actress (b. 1898)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian singer and actress (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian singer and actress (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lenya"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Indonesian actor (b. 1920)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actor (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor (b, 1952)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b, 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b, 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actor (b. 1906)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Dutch chess player and author (b. 1927)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chess player and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chess player and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Spanish politician, Prime Minister of Spain (b. 1908)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Spain\" class=\"mw-redirect\" title=\"List of Prime Ministers of Spain\">Prime Minister of Spain</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Spain\" class=\"mw-redirect\" title=\"List of Prime Ministers of Spain\">Prime Minister of Spain</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Spain", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Spain"}]}, {"year": "1990", "text": "<PERSON>, American actor (b. 1916)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1916)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Greek physicist and academic (b. 1951)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Basilis_C._X<PERSON>los\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek physicist and academic (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Basilis_C._X<PERSON>los\" title=\"<PERSON><PERSON> C<PERSON>\"><PERSON><PERSON></a>, Greek physicist and academic (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Basilis_C._<PERSON><PERSON><PERSON>los"}]}, {"year": "1992", "text": "<PERSON>, Croatian painter (b. 1914)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian painter (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian painter (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ivan_Generali%C4%87"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Portuguese composer and conductor (b. 1906)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ra%C3%A7a\" title=\"<PERSON>\"><PERSON></a>, Portuguese composer and conductor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A7a\" title=\"<PERSON>\"><PERSON></a>, Portuguese composer and conductor (b. 1906)", "links": [{"title": "<PERSON>Graça", "link": "https://wikipedia.org/wiki/Fernando_Lo<PERSON>-Gra%C3%A7a"}]}, {"year": "1997", "text": "<PERSON>, American baseball player and educator (b. 1907)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and educator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and educator (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter (b. 1943)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Spanish poet and author of children's literature (b. 1917)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and author of children's literature (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and author of children's literature (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Fuertes"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Japanese-American wrestler and trainer (b. 1937)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American wrestler and trainer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American wrestler and trainer (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, French scholar and politician, French Minister of Justice (b. 1925)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(France)\" title=\"Ministry of Justice (France)\">French Minister of Justice</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(France)\" title=\"Ministry of Justice (France)\">French Minister of Justice</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Justice (France)", "link": "https://wikipedia.org/wiki/Ministry_of_Justice_(France)"}]}, {"year": "1999", "text": "<PERSON>, American author and librarian (b. 1902)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and librarian (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and librarian (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English author and academic (b. 1932)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Estonian-Canadian architect (b. 1924)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Uno_Prii\" title=\"Uno Prii\"><PERSON><PERSON></a>, Estonian-Canadian architect (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uno_Prii\" title=\"Uno Prii\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian-Canadian architect (b. 1924)", "links": [{"title": "Uno Prii", "link": "https://wikipedia.org/wiki/Uno_Prii"}]}, {"year": "2000", "text": "<PERSON>, English footballer and journalist (b. 1922)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and journalist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and journalist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actress (b. 1908)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and academic (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and academic (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American actress (b. 1919)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American singer-songwriter (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1926)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2006", "text": "<PERSON>, American tuba player (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tuba player (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tuba player (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American author and educator (b. 1950)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and educator (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and educator (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American sportscaster (b. 1951)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Australian activist (b. 1946)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian activist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian activist (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American physician and academic, co-invented Gatorade (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic, co-invented <a href=\"https://wikipedia.org/wiki/Gatorade\" title=\"Gatorade\">Gatorade</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic, co-invented <a href=\"https://wikipedia.org/wiki/Gatorade\" title=\"Gatorade\">Gatorade</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gatorade", "link": "https://wikipedia.org/wiki/Gatorade"}]}, {"year": "2007", "text": "<PERSON>, American football player (b. 1983)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American football player and coach (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, 7th Prime Minister of India (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "2009", "text": "<PERSON>, American singer-songwriter (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American actor, director, and producer (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and producer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and producer (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English photographer and director (b. 1928)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and director (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and director (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English actor, director, producer, and screenwriter (b. 1927)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and screenwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and screenwriter (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Welsh footballer and manager (b. 1969)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American guitarist (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Dutch footballer and manager (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Ab_Fafi%C3%A9\" title=\"Ab Fafié\">A<PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Dutch footballer and manager (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ab_Fafi%C3%A9\" title=\"Ab Fafié\"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Dutch footballer and manager (b. 1941)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ab_Fafi%C3%A9"}]}, {"year": "2012", "text": "<PERSON><PERSON>, French journalist and author (b. 1954)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French journalist and author (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French journalist and author (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American businessman and union leader (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and union leader (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and union leader (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American photographer and businessman, co-founded Rockcityclub (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Rockcityclub\" class=\"mw-redirect\" title=\"Rockcityclub\">Rockcityclub</a> (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Rockcityclub\" class=\"mw-redirect\" title=\"Rockcityclub\">Rockcityclub</a> (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Rockcityclub", "link": "https://wikipedia.org/wiki/Rockcityclub"}]}, {"year": "2013", "text": "<PERSON>, English-American actor (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician, Attorney General of Rhode Island (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Rhode_Island\" class=\"mw-redirect\" title=\"Attorney General of Rhode Island\">Attorney General of Rhode Island</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Rhode_Island\" class=\"mw-redirect\" title=\"Attorney General of Rhode Island\">Attorney General of Rhode Island</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_DeSimone"}, {"title": "Attorney General of Rhode Island", "link": "https://wikipedia.org/wiki/Attorney_General_of_Rhode_Island"}]}, {"year": "2013", "text": "<PERSON><PERSON>, German physiologist and biologist (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Roe<PERSON>\" title=\"Volker Roemheld\"><PERSON><PERSON></a>, German physiologist and biologist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Volker Roemheld\"><PERSON><PERSON></a>, German physiologist and biologist (b. 1941)", "links": [{"title": "Volker Roemheld", "link": "https://wikipedia.org/wiki/<PERSON>ker_<PERSON>held"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/N%C3%<PERSON><PERSON>_Santos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%<PERSON>lton_Santos\" title=\"Nílton Santos\"><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Filipino colonel (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino colonel (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino colonel (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Polish physician and missionary (b. 1911)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B%C5%82e%C5%84ska\" title=\"<PERSON> Bł<PERSON>ń<PERSON>\"><PERSON></a>, Polish physician and missionary (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C5%82e%C5%84ska\" title=\"<PERSON>\"><PERSON></a>, Polish physician and missionary (b. 1911)", "links": [{"title": "Wanda Błeńska", "link": "https://wikipedia.org/wiki/Wanda_B%C5%82e%C5%84ska"}]}, {"year": "2014", "text": "<PERSON>, Australian cricketer (b. 1988)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, English author (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author (b. 1920)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Irish rugby player and humanitarian (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player and humanitarian (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player and humanitarian (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, Portuguese-American businessman and philanthropist (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> B<PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Portuguese-American businessman and philanthropist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Portuguese-American businessman and philanthropist (b. 1922)", "links": [{"title": "Fe<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Tanzanian-South African author and academic (b. 1963)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian-South African author and academic (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian-South African author and academic (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian businessman and diplomat (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and diplomat (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and diplomat (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American figure skater and coach (b. 1971)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and coach (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and coach (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Belgian tennis player and golfer (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player and golfer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player and golfer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Greek statesman (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek statesman (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek statesman (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, Iranian nuclear scientist (b. 1958) ", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>akhrizadeh\" title=\"<PERSON><PERSON><PERSON> Fakhrizadeh\"><PERSON><PERSON><PERSON></a>, Iranian nuclear scientist (b. 1958) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hrizadeh\" title=\"<PERSON><PERSON><PERSON> Fakhrizadeh\"><PERSON><PERSON><PERSON></a>, Iranian nuclear scientist (b. 1958) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Fakhrizadeh"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Norwegian YouTuber (b. 1964)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Apetor\" title=\"Apetor\"><PERSON><PERSON><PERSON></a>, Norwegian YouTuber (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apetor\" title=\"Apetor\"><PERSON><PERSON><PERSON></a>, Norwegian YouTuber (b. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Apetor"}]}, {"year": "2024", "text": "<PERSON>, American motorcycle racer (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}