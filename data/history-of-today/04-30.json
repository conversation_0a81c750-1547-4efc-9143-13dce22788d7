{"date": "April 30", "url": "https://wikipedia.org/wiki/April_30", "data": {"Events": [{"year": "311", "text": "The Diocletianic Persecution of Christians in the Roman Empire ends.", "html": "311 - The <a href=\"https://wikipedia.org/wiki/Diocletianic_Persecution\" title=\"Diocletianic Persecution\">Diocletianic Persecution</a> of Christians in the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a> ends.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Diocletianic_Persecution\" title=\"Diocletianic Persecution\">Diocletianic Persecution</a> of Christians in the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a> ends.", "links": [{"title": "Diocletianic Persecution", "link": "https://wikipedia.org/wiki/Di<PERSON>letian<PERSON>_Persecution"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}]}, {"year": "1315", "text": "<PERSON><PERSON><PERSON><PERSON> is hanged at the instigation of <PERSON>, Count of Valois.", "html": "1315 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> is hanged at the instigation of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Valois\" title=\"<PERSON>, Count of Valois\"><PERSON>, Count of Valois</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> is hanged at the instigation of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Valois\" title=\"<PERSON>, Count of Valois\"><PERSON>, Count of Valois</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/En<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>, Count of Valois", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON>_Valois"}]}, {"year": "1492", "text": "Spain gives <PERSON> his commission of exploration. He is named admiral of the ocean sea, viceroy and governor of any territory he discovers.", "html": "1492 - Spain gives <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> his commission of exploration. He is named admiral of the ocean sea, viceroy and governor of any territory he discovers.", "no_year_html": "Spain gives <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> his commission of exploration. He is named admiral of the ocean sea, viceroy and governor of any territory he discovers.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1513", "text": "<PERSON>, Yorkist pretender to the English throne, is executed on the orders of <PERSON>.", "html": "1513 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Suffolk\" title=\"<PERSON>, 3rd Duke of Suffolk\"><PERSON></a>, Yorkist pretender to the English throne, is executed on the orders of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Suffolk\" title=\"<PERSON>, 3rd Duke of Suffolk\"><PERSON></a>, Yorkist pretender to the English throne, is executed on the orders of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON></a>.", "links": [{"title": "<PERSON>, 3rd Duke of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Suffolk"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1598", "text": "<PERSON> begins the conquest of Santa Fe de Nuevo México.", "html": "1598 - <a href=\"https://wikipedia.org/wiki/Juan_de_O%C3%B1ate\" title=\"<PERSON> Oñate\"><PERSON></a> begins the conquest of <a href=\"https://wikipedia.org/wiki/Santa_Fe_de_Nuevo_M%C3%A9xico\" title=\"Santa Fe de Nuevo México\">Santa Fe de Nuevo México</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_de_O%C3%B1ate\" title=\"<PERSON> O<PERSON>te\"><PERSON></a> begins the conquest of <a href=\"https://wikipedia.org/wiki/Santa_Fe_de_Nuevo_M%C3%A9xico\" title=\"Santa Fe de Nuevo México\">Santa Fe de Nuevo México</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B1ate"}, {"title": "Santa Fe de Nuevo México", "link": "https://wikipedia.org/wiki/Santa_Fe_de_Nuevo_M%C3%A9xico"}]}, {"year": "1598", "text": "<PERSON> of France issues the Edict of Nantes, allowing freedom of religion to the Huguenots.", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_France\" title=\"Henry IV of France\"><PERSON> of France</a> issues the <a href=\"https://wikipedia.org/wiki/Edict_of_Nantes\" title=\"Edict of Nantes\">Edict of Nantes</a>, allowing <a href=\"https://wikipedia.org/wiki/Freedom_of_religion\" title=\"Freedom of religion\">freedom of religion</a> to the <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> issues the <a href=\"https://wikipedia.org/wiki/Edict_of_Nantes\" title=\"Edict of Nantes\">Edict of Nantes</a>, allowing <a href=\"https://wikipedia.org/wiki/Freedom_of_religion\" title=\"Freedom of religion\">freedom of religion</a> to the <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}, {"title": "Edict of Nantes", "link": "https://wikipedia.org/wiki/Edict_of_Nantes"}, {"title": "Freedom of religion", "link": "https://wikipedia.org/wiki/Freedom_of_religion"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>not"}]}, {"year": "1636", "text": "Eighty Years' War: Dutch Republic forces recapture a strategically important fort from Spain after a nine-month siege.", "html": "1636 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a> forces recapture a strategically important fort from Spain after <a href=\"https://wikipedia.org/wiki/Siege_of_Schenkenschans\" title=\"Siege of Schenkenschans\">a nine-month siege</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a> forces recapture a strategically important fort from Spain after <a href=\"https://wikipedia.org/wiki/Siege_of_Schenkenschans\" title=\"Siege of Schenkenschans\">a nine-month siege</a>.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Dutch Republic", "link": "https://wikipedia.org/wiki/Dutch_Republic"}, {"title": "Siege of Schenkenschans", "link": "https://wikipedia.org/wiki/Siege_of_Schenkenschans"}]}, {"year": "1789", "text": "On the balcony of Federal Hall on Wall Street in New York City, <PERSON> takes the oath of office to become the first President of the United States.", "html": "1789 - On the balcony of <a href=\"https://wikipedia.org/wiki/Federal_Hall\" title=\"Federal Hall\">Federal Hall</a> on <a href=\"https://wikipedia.org/wiki/Wall_Street\" title=\"Wall Street\">Wall Street</a> in New York City, <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> takes the <a href=\"https://wikipedia.org/wiki/First_inauguration_of_George_Washington\" title=\"First inauguration of <PERSON>\">oath of office</a> to become the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "no_year_html": "On the balcony of <a href=\"https://wikipedia.org/wiki/Federal_Hall\" title=\"Federal Hall\">Federal Hall</a> on <a href=\"https://wikipedia.org/wiki/Wall_Street\" title=\"Wall Street\">Wall Street</a> in New York City, <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> takes the <a href=\"https://wikipedia.org/wiki/First_inauguration_of_<PERSON>_<PERSON>\" title=\"First inauguration of <PERSON>\">oath of office</a> to become the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "links": [{"title": "Federal Hall", "link": "https://wikipedia.org/wiki/Federal_Hall"}, {"title": "Wall Street", "link": "https://wikipedia.org/wiki/Wall_Street"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "First inauguration of <PERSON>", "link": "https://wikipedia.org/wiki/First_inauguration_of_<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1803", "text": "Louisiana Purchase: The United States purchases the Louisiana Territory from France for $15 million, more than doubling the size of the young nation.", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">Louisiana Purchase</a>: The United States purchases the <a href=\"https://wikipedia.org/wiki/Louisiana_Territory\" title=\"Louisiana Territory\">Louisiana Territory</a> from France for $15 million, more than doubling the size of the young nation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">Louisiana Purchase</a>: The United States purchases the <a href=\"https://wikipedia.org/wiki/Louisiana_Territory\" title=\"Louisiana Territory\">Louisiana Territory</a> from France for $15 million, more than doubling the size of the young nation.", "links": [{"title": "Louisiana Purchase", "link": "https://wikipedia.org/wiki/Louisiana_Purchase"}, {"title": "Louisiana Territory", "link": "https://wikipedia.org/wiki/Louisiana_Territory"}]}, {"year": "1812", "text": "The Territory of Orleans becomes the 18th U.S. state under the name Louisiana.", "html": "1812 - The <a href=\"https://wikipedia.org/wiki/Territory_of_Orleans\" title=\"Territory of Orleans\">Territory of Orleans</a> becomes the 18th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> under the name <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Territory_of_Orleans\" title=\"Territory of Orleans\">Territory of Orleans</a> becomes the 18th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> under the name <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>.", "links": [{"title": "Territory of Orleans", "link": "https://wikipedia.org/wiki/Territory_of_Orleans"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}, {"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}]}, {"year": "1838", "text": "Nicaragua declares independence from the Central American Federation.", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a> declares independence from the <a href=\"https://wikipedia.org/wiki/Central_American_Federation\" class=\"mw-redirect\" title=\"Central American Federation\">Central American Federation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a> declares independence from the <a href=\"https://wikipedia.org/wiki/Central_American_Federation\" class=\"mw-redirect\" title=\"Central American Federation\">Central American Federation</a>.", "links": [{"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}, {"title": "Central American Federation", "link": "https://wikipedia.org/wiki/Central_American_Federation"}]}, {"year": "1863", "text": "A 65-man French Foreign Legion infantry patrol fights a force of nearly 2,000 Mexican soldiers to nearly the last man in Hacienda Camarón, Mexico.", "html": "1863 - A 65-man <a href=\"https://wikipedia.org/wiki/French_Foreign_Legion\" title=\"French Foreign Legion\">French Foreign Legion</a> infantry patrol fights a force of nearly 2,000 Mexican soldiers to nearly the last man in <a href=\"https://wikipedia.org/wiki/Battle_of_Camar%C3%B3n\" title=\"Battle of Camarón\">Hacienda Camarón</a>, Mexico.", "no_year_html": "A 65-man <a href=\"https://wikipedia.org/wiki/French_Foreign_Legion\" title=\"French Foreign Legion\">French Foreign Legion</a> infantry patrol fights a force of nearly 2,000 Mexican soldiers to nearly the last man in <a href=\"https://wikipedia.org/wiki/Battle_of_Camar%C3%B3n\" title=\"Battle of Camarón\">Hacienda Camarón</a>, Mexico.", "links": [{"title": "French Foreign Legion", "link": "https://wikipedia.org/wiki/French_Foreign_Legion"}, {"title": "Battle of Camarón", "link": "https://wikipedia.org/wiki/Battle_of_Camar%C3%B3n"}]}, {"year": "1871", "text": "The Camp Grant massacre takes place in Arizona Territory.", "html": "1871 - The <a href=\"https://wikipedia.org/wiki/Camp_Grant_massacre\" title=\"Camp Grant massacre\">Camp Grant massacre</a> takes place in <a href=\"https://wikipedia.org/wiki/Arizona_Territory\" title=\"Arizona Territory\">Arizona Territory</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Camp_Grant_massacre\" title=\"Camp Grant massacre\">Camp Grant massacre</a> takes place in <a href=\"https://wikipedia.org/wiki/Arizona_Territory\" title=\"Arizona Territory\">Arizona Territory</a>.", "links": [{"title": "Camp Grant massacre", "link": "https://wikipedia.org/wiki/Camp_<PERSON>_massacre"}, {"title": "Arizona Territory", "link": "https://wikipedia.org/wiki/Arizona_Territory"}]}, {"year": "1885", "text": "Governor of New York <PERSON> signs legislation creating the Niagara Reservation, New York's first state park, ensuring that Niagara Falls will not be devoted solely to industrial and commercial use.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs legislation creating the <a href=\"https://wikipedia.org/wiki/Niagara_Reservation_State_Park\" class=\"mw-redirect\" title=\"Niagara Reservation State Park\">Niagara Reservation</a>, New York's first state park, ensuring that <a href=\"https://wikipedia.org/wiki/Niagara_Falls\" title=\"Niagara Falls\">Niagara Falls</a> will not be devoted solely to industrial and commercial use.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs legislation creating the <a href=\"https://wikipedia.org/wiki/Niagara_Reservation_State_Park\" class=\"mw-redirect\" title=\"Niagara Reservation State Park\">Niagara Reservation</a>, New York's first state park, ensuring that <a href=\"https://wikipedia.org/wiki/Niagara_Falls\" title=\"Niagara Falls\">Niagara Falls</a> will not be devoted solely to industrial and commercial use.", "links": [{"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Niagara Reservation State Park", "link": "https://wikipedia.org/wiki/Niagara_Reservation_State_Park"}, {"title": "Niagara Falls", "link": "https://wikipedia.org/wiki/Niagara_Falls"}]}, {"year": "1897", "text": "<PERSON><PERSON> <PERSON><PERSON> of the Cavendish Laboratory announces his discovery of the electron as a subatomic particle, over 1,800 times smaller than a proton (in the atomic nucleus), at a lecture at the Royal Institution in London.", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Cavendish_Laboratory\" title=\"Cavendish Laboratory\">Cavendish Laboratory</a> announces his discovery of the <a href=\"https://wikipedia.org/wiki/Electron\" title=\"Electron\">electron</a> as a <a href=\"https://wikipedia.org/wiki/Subatomic_particle\" title=\"Subatomic particle\">subatomic particle</a>, over 1,800 times smaller than a <a href=\"https://wikipedia.org/wiki/Proton\" title=\"Proton\">proton</a> (in the atomic nucleus), at a lecture at the <a href=\"https://wikipedia.org/wiki/Royal_Institution\" title=\"Royal Institution\">Royal Institution</a> in London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Cavendish_Laboratory\" title=\"Cavendish Laboratory\">Cavendish Laboratory</a> announces his discovery of the <a href=\"https://wikipedia.org/wiki/Electron\" title=\"Electron\">electron</a> as a <a href=\"https://wikipedia.org/wiki/Subatomic_particle\" title=\"Subatomic particle\">subatomic particle</a>, over 1,800 times smaller than a <a href=\"https://wikipedia.org/wiki/Proton\" title=\"Proton\">proton</a> (in the atomic nucleus), at a lecture at the <a href=\"https://wikipedia.org/wiki/Royal_Institution\" title=\"Royal Institution\">Royal Institution</a> in London.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Cavendish Laboratory", "link": "https://wikipedia.org/wiki/Cavendish_Laboratory"}, {"title": "Electron", "link": "https://wikipedia.org/wiki/Electron"}, {"title": "Subatomic particle", "link": "https://wikipedia.org/wiki/Subatomic_particle"}, {"title": "Proton", "link": "https://wikipedia.org/wiki/Proton"}, {"title": "Royal Institution", "link": "https://wikipedia.org/wiki/Royal_Institution"}]}, {"year": "1900", "text": "Hawaii becomes a territory of the United States, with <PERSON> as governor.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Hawaii\" title=\"Hawaii\">Hawaii</a> becomes a <a href=\"https://wikipedia.org/wiki/Territory_of_Hawaii\" title=\"Territory of Hawaii\">territory</a> of the United States, with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Sanford <PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Governor_of_Hawaii\" title=\"Governor of Hawaii\">governor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hawaii\" title=\"Hawaii\">Hawaii</a> becomes a <a href=\"https://wikipedia.org/wiki/Territory_of_Hawaii\" title=\"Territory of Hawaii\">territory</a> of the United States, with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Governor_of_Hawaii\" title=\"Governor of Hawaii\">governor</a>.", "links": [{"title": "Hawaii", "link": "https://wikipedia.org/wiki/Hawaii"}, {"title": "Territory of Hawaii", "link": "https://wikipedia.org/wiki/Territory_of_Hawaii"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Hawaii", "link": "https://wikipedia.org/wiki/Governor_of_Hawaii"}]}, {"year": "1905", "text": "<PERSON> completes his doctoral thesis at the University of Zurich.", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> completes his doctoral thesis at the <a href=\"https://wikipedia.org/wiki/University_of_Zurich\" title=\"University of Zurich\">University of Zurich</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Einstein\"><PERSON></a> completes his doctoral thesis at the <a href=\"https://wikipedia.org/wiki/University_of_Zurich\" title=\"University of Zurich\">University of Zurich</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "University of Zurich", "link": "https://wikipedia.org/wiki/University_of_Zurich"}]}, {"year": "1925", "text": "Automaker Dodge Brothers, Inc is sold to Dillon, Read & Co. for US$146 million plus $50 million for charity.", "html": "1925 - Automaker <a href=\"https://wikipedia.org/wiki/Dodge_Brothers,_Inc\" class=\"mw-redirect\" title=\"Dodge Brothers, Inc\">Dodge Brothers, Inc</a> is sold to <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_%26_Co.\" title=\"Dillon, Read &amp; Co.\">Dillon, Read &amp; Co.</a> for US$146 million plus $50 million for charity.", "no_year_html": "Automaker <a href=\"https://wikipedia.org/wiki/Dodge_Brothers,_Inc\" class=\"mw-redirect\" title=\"Dodge Brothers, Inc\">Dodge Brothers, Inc</a> is sold to <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_%26_Co.\" title=\"Dillon, Read &amp; Co.\">Dillon, Read &amp; Co.</a> for US$146 million plus $50 million for charity.", "links": [{"title": "Dodge Brothers, Inc", "link": "https://wikipedia.org/wiki/Dodge_Brothers,_Inc"}, {"title": "Dillon, Read & Co.", "link": "https://wikipedia.org/wiki/<PERSON>,_Read_%26_Co."}]}, {"year": "1927", "text": "The Federal Industrial Institute for Women opens in Alderson, West Virginia, as the first women's federal prison in the United States.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Federal_Industrial_Institute_for_Women\" class=\"mw-redirect\" title=\"Federal Industrial Institute for Women\">Federal Industrial Institute for Women</a> opens in <a href=\"https://wikipedia.org/wiki/Alderson,_West_Virginia\" title=\"Alderson, West Virginia\">Alderson, West Virginia</a>, as the first women's federal prison in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_Industrial_Institute_for_Women\" class=\"mw-redirect\" title=\"Federal Industrial Institute for Women\">Federal Industrial Institute for Women</a> opens in <a href=\"https://wikipedia.org/wiki/Alderson,_West_Virginia\" title=\"Alderson, West Virginia\">Alderson, West Virginia</a>, as the first women's federal prison in the United States.", "links": [{"title": "Federal Industrial Institute for Women", "link": "https://wikipedia.org/wiki/Federal_Industrial_Institute_for_Women"}, {"title": "Alderson, West Virginia", "link": "https://wikipedia.org/wiki/Alderson,_West_Virginia"}]}, {"year": "1937", "text": "The Commonwealth of the Philippines holds a plebiscite for Filipino women on whether they should be extended the right to suffrage; over 90% would vote in the affirmative.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">Commonwealth of the Philippines</a> holds a <a href=\"https://wikipedia.org/wiki/Philippine_women%27s_suffrage_plebiscite,_1937\" class=\"mw-redirect\" title=\"Philippine women's suffrage plebiscite, 1937\">plebiscite for Filipino women</a> on whether they should be extended the right to suffrage; over 90% would vote in the affirmative.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">Commonwealth of the Philippines</a> holds a <a href=\"https://wikipedia.org/wiki/Philippine_women%27s_suffrage_plebiscite,_1937\" class=\"mw-redirect\" title=\"Philippine women's suffrage plebiscite, 1937\">plebiscite for Filipino women</a> on whether they should be extended the right to suffrage; over 90% would vote in the affirmative.", "links": [{"title": "Commonwealth of the Philippines", "link": "https://wikipedia.org/wiki/Commonwealth_of_the_Philippines"}, {"title": "Philippine women's suffrage plebiscite, 1937", "link": "https://wikipedia.org/wiki/Philippine_women%27s_suffrage_plebiscite,_1937"}]}, {"year": "1939", "text": "The 1939-40 New York World's Fair opens.", "html": "1939 - The <a href=\"https://wikipedia.org/wiki/1939_New_York_World%27s_Fair\" title=\"1939 New York World's Fair\">1939-40 New York World's Fair</a> opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1939_New_York_World%27s_Fair\" title=\"1939 New York World's Fair\">1939-40 New York World's Fair</a> opens.", "links": [{"title": "1939 New York World's Fair", "link": "https://wikipedia.org/wiki/1939_New_York_World%27s_Fair"}]}, {"year": "1939", "text": "NBC inaugurates its regularly scheduled television service in New York City, broadcasting President <PERSON>'s N.Y. World's Fair opening day ceremonial address.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/NBC\" title=\"NBC\">NBC</a> inaugurates its regularly scheduled <a href=\"https://wikipedia.org/wiki/History_of_television#Mechanical_television\" title=\"History of television\">television</a> service in New York City, broadcasting President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/1939_New_York_World%27s_Fair\" title=\"1939 New York World's Fair\">N.Y. World's Fair</a> opening day ceremonial address.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NBC\" title=\"NBC\">NBC</a> inaugurates its regularly scheduled <a href=\"https://wikipedia.org/wiki/History_of_television#Mechanical_television\" title=\"History of television\">television</a> service in New York City, broadcasting President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/1939_New_York_World%27s_Fair\" title=\"1939 New York World's Fair\">N.Y. World's Fair</a> opening day ceremonial address.", "links": [{"title": "NBC", "link": "https://wikipedia.org/wiki/NBC"}, {"title": "History of television", "link": "https://wikipedia.org/wiki/History_of_television#Mechanical_television"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1939 New York World's Fair", "link": "https://wikipedia.org/wiki/1939_New_York_World%27s_Fair"}]}, {"year": "1943", "text": "World War II: The British submarine HMS Seraph surfaces near Huelva to cast adrift a dead man dressed as a courier and carrying false invasion plans.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The British submarine <a href=\"https://wikipedia.org/wiki/HMS_Seraph_(P219)\" title=\"HMS Seraph (P219)\">HMS <i>Seraph</i></a> surfaces near <a href=\"https://wikipedia.org/wiki/Huelva\" title=\"Huelva\">Huelva</a> to cast adrift a dead man dressed as a courier and carrying <a href=\"https://wikipedia.org/wiki/Operation_Mincemeat\" title=\"Operation Mincemeat\">false invasion plans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The British submarine <a href=\"https://wikipedia.org/wiki/HMS_Seraph_(P219)\" title=\"HMS Seraph (P219)\">HMS <i>Seraph</i></a> surfaces near <a href=\"https://wikipedia.org/wiki/Huelva\" title=\"Huelva\">Huelva</a> to cast adrift a dead man dressed as a courier and carrying <a href=\"https://wikipedia.org/wiki/Operation_Mincemeat\" title=\"Operation Mincemeat\">false invasion plans</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "HMS Seraph (P219)", "link": "https://wikipedia.org/wiki/HMS_Seraph_(P219)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>va"}, {"title": "Operation Mincemeat", "link": "https://wikipedia.org/wiki/Operation_Mincemeat"}]}, {"year": "1945", "text": "World War II: Führerbunker: <PERSON> and <PERSON> commit suicide after being married for less than 40 hours. Soviet soldiers raise the Victory Banner over the Reichstag building.", "html": "1945 - World War II: <i><a href=\"https://wikipedia.org/wiki/F%C3%BChrerbunker\" title=\"Führerbunker\">Führerbunker</a></i>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Eva_Braun\" title=\"Eva Braun\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Death_of_Adolf_Hitler\" title=\"Death of Adolf Hitler\">commit suicide</a> after being married for less than 40 hours. <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> soldiers raise the <a href=\"https://wikipedia.org/wiki/Victory_Banner\" title=\"Victory Banner\">Victory Banner</a> over the <a href=\"https://wikipedia.org/wiki/Reichstag_building\" title=\"Reichstag building\">Reichstag building</a>.", "no_year_html": "World War II: <i><a href=\"https://wikipedia.org/wiki/F%C3%BChrerbunker\" title=\"Führerbunker\">Führerbunker</a></i>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Eva_Braun\" title=\"Eva Braun\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Death_of_Adolf_Hitler\" title=\"Death of <PERSON> Hitler\">commit suicide</a> after being married for less than 40 hours. <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> soldiers raise the <a href=\"https://wikipedia.org/wiki/Victory_Banner\" title=\"Victory Banner\">Victory Banner</a> over the <a href=\"https://wikipedia.org/wiki/Reichstag_building\" title=\"Reichstag building\">Reichstag building</a>.", "links": [{"title": "Führerbunker", "link": "https://wikipedia.org/wiki/F%C3%BChrerbunker"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Death of <PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Victory Banner", "link": "https://wikipedia.org/wiki/Victory_Banner"}, {"title": "Reichstag building", "link": "https://wikipedia.org/wiki/Reichstag_building"}]}, {"year": "1945", "text": "World War II: Stalag Luft I prisoner-of-war camp near Barth, Germany is liberated by Soviet soldiers, freeing nearly 9,000 American and British airmen.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Stalag_Luft_I\" title=\"Stalag Luft I\">Stalag Luft I</a> prisoner-of-war camp near Barth, Germany is liberated by Soviet soldiers, freeing nearly 9,000 American and British airmen.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Stalag_Luft_I\" title=\"Stalag Luft I\">Stalag Luft I</a> prisoner-of-war camp near Barth, Germany is liberated by Soviet soldiers, freeing nearly 9,000 American and British airmen.", "links": [{"title": "Stalag Luft I", "link": "https://wikipedia.org/wiki/Stalag_Luft_I"}]}, {"year": "1947", "text": "In Nevada, Boulder Dam is renamed Hoover Dam.", "html": "1947 - In <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a>, Boulder Dam is renamed <a href=\"https://wikipedia.org/wiki/Hoover_Dam\" title=\"Hoover Dam\">Hoover Dam</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a>, Boulder Dam is renamed <a href=\"https://wikipedia.org/wiki/Hoover_Dam\" title=\"Hoover Dam\">Hoover Dam</a>.", "links": [{"title": "Nevada", "link": "https://wikipedia.org/wiki/Nevada"}, {"title": "Hoover Dam", "link": "https://wikipedia.org/wiki/Hoover_Dam"}]}, {"year": "1948", "text": "In Bogotá, Colombia, the Organization of American States is established.", "html": "1948 - In <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a>, Colombia, the <a href=\"https://wikipedia.org/wiki/Organization_of_American_States\" title=\"Organization of American States\">Organization of American States</a> is established.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a>, Colombia, the <a href=\"https://wikipedia.org/wiki/Organization_of_American_States\" title=\"Organization of American States\">Organization of American States</a> is established.", "links": [{"title": "Bogotá", "link": "https://wikipedia.org/wiki/Bogot%C3%A1"}, {"title": "Organization of American States", "link": "https://wikipedia.org/wiki/Organization_of_American_States"}]}, {"year": "1956", "text": "Former Vice President and Democratic Senator <PERSON><PERSON> dies during a speech in Virginia.", "html": "1956 - Former Vice President and Democratic Senator <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> dies during a speech in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "no_year_html": "Former Vice President and Democratic Senator <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> dies during a speech in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alben_Barkley"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}]}, {"year": "1957", "text": "Supplementary Convention on the Abolition of Slavery entered into force.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Supplementary_Convention_on_the_Abolition_of_Slavery\" title=\"Supplementary Convention on the Abolition of Slavery\">Supplementary Convention on the Abolition of Slavery</a> entered into force.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Supplementary_Convention_on_the_Abolition_of_Slavery\" title=\"Supplementary Convention on the Abolition of Slavery\">Supplementary Convention on the Abolition of Slavery</a> entered into force.", "links": [{"title": "Supplementary Convention on the Abolition of Slavery", "link": "https://wikipedia.org/wiki/Supplementary_Convention_on_the_Abolition_of_Slavery"}]}, {"year": "1961", "text": "K-19, the first Soviet nuclear submarine equipped with nuclear missiles, is commissioned.", "html": "1961 - <i><a href=\"https://wikipedia.org/wiki/Soviet_submarine_K-19\" title=\"Soviet submarine K-19\">K-19</a></i>, the first <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Nuclear_submarine\" title=\"Nuclear submarine\">nuclear submarine</a> equipped with <a href=\"https://wikipedia.org/wiki/Submarine-launched_ballistic_missile\" title=\"Submarine-launched ballistic missile\">nuclear missiles</a>, is <a href=\"https://wikipedia.org/wiki/Ship_commissioning\" title=\"Ship commissioning\">commissioned</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Soviet_submarine_K-19\" title=\"Soviet submarine K-19\">K-19</a></i>, the first <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Nuclear_submarine\" title=\"Nuclear submarine\">nuclear submarine</a> equipped with <a href=\"https://wikipedia.org/wiki/Submarine-launched_ballistic_missile\" title=\"Submarine-launched ballistic missile\">nuclear missiles</a>, is <a href=\"https://wikipedia.org/wiki/Ship_commissioning\" title=\"Ship commissioning\">commissioned</a>.", "links": [{"title": "Soviet submarine K-19", "link": "https://wikipedia.org/wiki/Soviet_submarine_K-19"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Nuclear submarine", "link": "https://wikipedia.org/wiki/Nuclear_submarine"}, {"title": "Submarine-launched ballistic missile", "link": "https://wikipedia.org/wiki/Submarine-launched_ballistic_missile"}, {"title": "Ship commissioning", "link": "https://wikipedia.org/wiki/Ship_commissioning"}]}, {"year": "1963", "text": "The Bristol Bus Boycott is held in Bristol to protest the Bristol Omnibus Company's refusal to employ Black or Asian bus crews, drawing national attention to racial discrimination in the United Kingdom.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Bristol_Bus_Boycott\" title=\"Bristol Bus Boycott\">Bristol Bus Boycott</a> is held in <a href=\"https://wikipedia.org/wiki/Bristol\" title=\"Bristol\">Bristol</a> to protest the <a href=\"https://wikipedia.org/wiki/Bristol_Omnibus_Company\" title=\"Bristol Omnibus Company\">Bristol Omnibus Company</a>'s refusal to employ Black or Asian bus crews, drawing national attention to racial discrimination in the United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bristol_Bus_Boycott\" title=\"Bristol Bus Boycott\">Bristol Bus Boycott</a> is held in <a href=\"https://wikipedia.org/wiki/Bristol\" title=\"Bristol\">Bristol</a> to protest the <a href=\"https://wikipedia.org/wiki/Bristol_Omnibus_Company\" title=\"Bristol Omnibus Company\">Bristol Omnibus Company</a>'s refusal to employ Black or Asian bus crews, drawing national attention to racial discrimination in the United Kingdom.", "links": [{"title": "Bristol Bus Boycott", "link": "https://wikipedia.org/wiki/Bristol_Bus_Boycott"}, {"title": "Bristol", "link": "https://wikipedia.org/wiki/Bristol"}, {"title": "Bristol Omnibus Company", "link": "https://wikipedia.org/wiki/Bristol_Omnibus_Company"}]}, {"year": "1973", "text": "Watergate scandal: U.S. President <PERSON> fires White House Counsel <PERSON>; other top aides, most notably <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, resign.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> fires <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> Counsel <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>; other top aides, most notably <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, resign.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> fires <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> Counsel <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>; other top aides, most notably <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, resign.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "Fall of Saigon: Communist forces gain control of Saigon. The Vietnam War formally ends with the unconditional surrender of South Vietnamese president <PERSON><PERSON><PERSON><PERSON>.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Fall_of_Saigon\" title=\"Fall of Saigon\">Fall of Saigon</a>: <a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">Communist</a> forces gain control of <a href=\"https://wikipedia.org/wiki/Saigon\" class=\"mw-redirect\" title=\"Saigon\">Saigon</a>. The <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a> formally ends with the unconditional surrender of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> president <a href=\"https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh\" title=\"Dương Vă<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fall_of_Saigon\" title=\"Fall of Saigon\">Fall of Saigon</a>: <a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">Communist</a> forces gain control of <a href=\"https://wikipedia.org/wiki/Saigon\" class=\"mw-redirect\" title=\"Saigon\">Saigon</a>. The <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a> formally ends with the unconditional surrender of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> president <a href=\"https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh\" title=\"Dương Văn Minh\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Fall of Saigon", "link": "https://wikipedia.org/wiki/Fall_of_Saigon"}, {"title": "Communist", "link": "https://wikipedia.org/wiki/Communist"}, {"title": "Saigon", "link": "https://wikipedia.org/wiki/Saigon"}, {"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh"}]}, {"year": "1979", "text": "Eruption of Mount Marapi: Mount Marapi, a complex volcano on the Indonesian island of Sumatra, erupted. 80 up to 100 people were killed.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/1979_eruption_of_Mount_Marapi\" title=\"1979 eruption of Mount Marapi\">Eruption of Mount Marapi</a>: <a href=\"https://wikipedia.org/wiki/Mount_Marapi\" title=\"Mount Marapi\">Mount Marapi</a>, a <a href=\"https://wikipedia.org/wiki/Complex_volcano\" title=\"Complex volcano\">complex volcano</a> on the <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> island of <a href=\"https://wikipedia.org/wiki/Sumatra\" title=\"Sumatra\">Sumatra</a>, erupted. 80 up to 100 people were killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1979_eruption_of_Mount_Marapi\" title=\"1979 eruption of Mount Marapi\">Eruption of Mount Marapi</a>: <a href=\"https://wikipedia.org/wiki/Mount_Marapi\" title=\"Mount Marapi\">Mount Marapi</a>, a <a href=\"https://wikipedia.org/wiki/Complex_volcano\" title=\"Complex volcano\">complex volcano</a> on the <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> island of <a href=\"https://wikipedia.org/wiki/Sumatra\" title=\"Sumatra\">Sumatra</a>, erupted. 80 up to 100 people were killed.", "links": [{"title": "1979 eruption of Mount Marapi", "link": "https://wikipedia.org/wiki/1979_eruption_of_Mount_Marapi"}, {"title": "Mount Marapi", "link": "https://wikipedia.org/wiki/Mount_Marapi"}, {"title": "Complex volcano", "link": "https://wikipedia.org/wiki/Complex_volcano"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Sumatra", "link": "https://wikipedia.org/wiki/Sumatra"}]}, {"year": "1980", "text": "<PERSON><PERSON> is inaugurated as Queen of the Netherlands following the abdication of <PERSON>.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Beat<PERSON>_of_the_Netherlands\" title=\"Beatrix of the Netherlands\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Inauguration_of_the_Dutch_monarch\" title=\"Inauguration of the Dutch monarch\">inaugurated</a> as <a href=\"https://wikipedia.org/wiki/Queen_of_the_Netherlands\" class=\"mw-redirect\" title=\"Queen of the Netherlands\">Queen of the Netherlands</a> following the abdication of <a href=\"https://wikipedia.org/wiki/Juliana_of_the_Netherlands\" title=\"Juliana of the Netherlands\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beat<PERSON>_of_the_Netherlands\" title=\"Beatrix of the Netherlands\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Inauguration_of_the_Dutch_monarch\" title=\"Inauguration of the Dutch monarch\">inaugurated</a> as <a href=\"https://wikipedia.org/wiki/Queen_of_the_Netherlands\" class=\"mw-redirect\" title=\"Queen of the Netherlands\">Queen of the Netherlands</a> following the abdication of <a href=\"https://wikipedia.org/wiki/Juliana_of_the_Netherlands\" title=\"Juliana of the Netherlands\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/Beat<PERSON>_of_the_Netherlands"}, {"title": "Inauguration of the Dutch monarch", "link": "https://wikipedia.org/wiki/Inauguration_of_the_Dutch_monarch"}, {"title": "Queen of the Netherlands", "link": "https://wikipedia.org/wiki/Queen_of_the_Netherlands"}, {"title": "Juliana of the Netherlands", "link": "https://wikipedia.org/wiki/Juliana_of_the_Netherlands"}]}, {"year": "1980", "text": "The Iranian Embassy siege begins in London.", "html": "1980 - The <a href=\"https://wikipedia.org/wiki/Iranian_Embassy_siege\" title=\"Iranian Embassy siege\">Iranian Embassy siege</a> begins in London.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Iranian_Embassy_siege\" title=\"Iranian Embassy siege\">Iranian Embassy siege</a> begins in London.", "links": [{"title": "Iranian Embassy siege", "link": "https://wikipedia.org/wiki/Iranian_Embassy_siege"}]}, {"year": "1982", "text": "The Bijon Setu massacre occurs in Calcutta, India.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/Bijon_Setu_massacre\" title=\"Bijon Setu massacre\">Bijon Setu massacre</a> occurs in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>, India.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bijon_Setu_massacre\" title=\"Bijon Setu massacre\">Bijon Setu massacre</a> occurs in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>, India.", "links": [{"title": "<PERSON><PERSON><PERSON>u massacre", "link": "https://wikipedia.org/wiki/Bijon_Setu_massacre"}, {"title": "Kolkata", "link": "https://wikipedia.org/wiki/Kolkata"}]}, {"year": "1989", "text": "The Monkseaton shootings occur in Tyne and Wear, England. One killed, 16 injured.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/Monkseaton_shootings\" title=\"Monkseaton shootings\">Monkseaton shootings</a> occur in <a href=\"https://wikipedia.org/wiki/Tyne_and_Wear\" title=\"Tyne and Wear\">Tyne and Wear</a>, England. One killed, 16 injured.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Monkseaton_shootings\" title=\"Monkseaton shootings\">Monkseaton shootings</a> occur in <a href=\"https://wikipedia.org/wiki/Tyne_and_Wear\" title=\"Tyne and Wear\">Tyne and Wear</a>, England. One killed, 16 injured.", "links": [{"title": "Monkseaton shootings", "link": "https://wikipedia.org/wiki/Monkseaton_shootings"}, {"title": "Tyne and Wear", "link": "https://wikipedia.org/wiki/Tyne_and_Wear"}]}, {"year": "1993", "text": "CERN announces World Wide Web protocols will be free.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/CERN\" title=\"CERN\">CERN</a> announces <a href=\"https://wikipedia.org/wiki/World_Wide_Web\" title=\"World Wide Web\">World Wide Web</a> protocols will be free.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/CERN\" title=\"CERN\">CERN</a> announces <a href=\"https://wikipedia.org/wiki/World_Wide_Web\" title=\"World Wide Web\">World Wide Web</a> protocols will be free.", "links": [{"title": "CERN", "link": "https://wikipedia.org/wiki/CERN"}, {"title": "World Wide Web", "link": "https://wikipedia.org/wiki/World_Wide_Web"}]}, {"year": "1994", "text": "Formula One racing driver <PERSON> is killed in a crash during the qualifying session of the San Marino Grand Prix run at Autodromo Enzo e Dino Ferrari outside Imola, Italy.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> racing driver <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed in a crash during the qualifying session of the <a href=\"https://wikipedia.org/wiki/San_Marino_Grand_Prix\" title=\"San Marino Grand Prix\">San Marino Grand Prix</a> run at <a href=\"https://wikipedia.org/wiki/Autodromo_Enzo_e_Dino_Ferrari\" class=\"mw-redirect\" title=\"Autodromo Enzo e Dino Ferrari\">Autodromo Enzo e Dino Ferrari</a> outside <a href=\"https://wikipedia.org/wiki/Imola\" title=\"Imola\">Imola</a>, Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> racing driver <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed in a crash during the qualifying session of the <a href=\"https://wikipedia.org/wiki/San_Marino_Grand_Prix\" title=\"San Marino Grand Prix\">San Marino Grand Prix</a> run at <a href=\"https://wikipedia.org/wiki/Autodromo_Enzo_e_Dino_Ferrari\" class=\"mw-redirect\" title=\"Autodromo Enzo e Dino Ferrari\">Autodromo Enzo e Dino Ferrari</a> outside <a href=\"https://wikipedia.org/wiki/Imola\" title=\"Imola\">Imola</a>, Italy.", "links": [{"title": "Formula One", "link": "https://wikipedia.org/wiki/Formula_One"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "San Marino Grand Prix", "link": "https://wikipedia.org/wiki/San_Marino_Grand_Prix"}, {"title": "Autodromo Enzo e Dino Ferrari", "link": "https://wikipedia.org/wiki/Autodromo_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Imola"}]}, {"year": "1999", "text": "Neo-Nazi <PERSON> carries out the last of his three nail bombings in London at the Admiral Duncan gay pub, killing three people and injuring 79 others.", "html": "1999 - Neo-Nazi <PERSON> carries out the last of his three <a href=\"https://wikipedia.org/wiki/1999_London_nail_bombings\" title=\"1999 London nail bombings\">nail bombings in London</a> at the <a href=\"https://wikipedia.org/wiki/Admiral_<PERSON>_(pub)\" title=\"<PERSON> (pub)\">Admiral <PERSON></a> <a href=\"https://wikipedia.org/wiki/Gay_bar\" title=\"Gay bar\">gay pub</a>, killing three people and injuring 79 others.", "no_year_html": "Neo-Nazi <PERSON> carries out the last of his three <a href=\"https://wikipedia.org/wiki/1999_London_nail_bombings\" title=\"1999 London nail bombings\">nail bombings in London</a> at the <a href=\"https://wikipedia.org/wiki/Admiral_<PERSON>(pub)\" title=\"<PERSON> (pub)\">Admiral <PERSON></a> <a href=\"https://wikipedia.org/wiki/Gay_bar\" title=\"Gay bar\">gay pub</a>, killing three people and injuring 79 others.", "links": [{"title": "1999 London nail bombings", "link": "https://wikipedia.org/wiki/1999_London_nail_bombings"}, {"title": "<PERSON> (pub)", "link": "https://wikipedia.org/wiki/Admiral_<PERSON>_(pub)"}, {"title": "Gay bar", "link": "https://wikipedia.org/wiki/Gay_bar"}]}, {"year": "2000", "text": "Canonization of <PERSON><PERSON> in the presence of 200,000 people and the first Divine Mercy Sunday celebrated worldwide.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">Canonization</a> of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in the presence of 200,000 people and the first <a href=\"https://wikipedia.org/wiki/Divine_Mercy_Sunday\" title=\"Divine Mercy Sunday\">Divine Mercy Sunday</a> celebrated worldwide.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">Canonization</a> of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in the presence of 200,000 people and the first <a href=\"https://wikipedia.org/wiki/Divine_Mercy_Sunday\" title=\"Divine Mercy Sunday\">Divine Mercy Sunday</a> celebrated worldwide.", "links": [{"title": "Canonization", "link": "https://wikipedia.org/wiki/Canonization"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Divine Mercy Sunday", "link": "https://wikipedia.org/wiki/Divine_Mercy_Sunday"}]}, {"year": "2004", "text": "U.S. media release graphic photos of American soldiers committing war crimes against Iraqi prisoners at Abu Ghraib prison.", "html": "2004 - U.S. media release graphic photos of <a href=\"https://wikipedia.org/wiki/Abu_Ghraib_torture_and_prisoner_abuse\" title=\"Abu Ghraib torture and prisoner abuse\">American soldiers committing war crimes</a> against <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraqi</a> prisoners at <a href=\"https://wikipedia.org/wiki/Abu_Ghraib_prison\" title=\"Abu Ghraib prison\">Abu Ghraib prison</a>.", "no_year_html": "U.S. media release graphic photos of <a href=\"https://wikipedia.org/wiki/Abu_Ghraib_torture_and_prisoner_abuse\" title=\"Abu Ghraib torture and prisoner abuse\">American soldiers committing war crimes</a> against <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraqi</a> prisoners at <a href=\"https://wikipedia.org/wiki/Abu_Ghraib_prison\" title=\"Abu Ghraib prison\">Abu Ghraib prison</a>.", "links": [{"title": "Abu Ghraib torture and prisoner abuse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_torture_and_prisoner_abuse"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Abu Ghraib prison", "link": "https://wikipedia.org/wiki/Abu_Ghraib_prison"}]}, {"year": "2008", "text": "Two skeletal remains found near Yekaterinburg are confirmed by Russian scientists to be the remains of <PERSON> and <PERSON>, two of the children of the last Tsar of Russia, whose entire family was executed at Yekaterinburg by the Bolsheviks.", "html": "2008 - Two skeletal remains found near <a href=\"https://wikipedia.org/wiki/Yekaterinburg\" title=\"Yekaterinburg\">Yekaterinburg</a> are confirmed by Russian scientists to be the remains of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Tsarevich_of_Russia\" title=\"<PERSON>, Tsarevich of Russia\">Alexei</a> and <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\"><PERSON></a>, two of the children of the last Tsar of Russia, whose entire family was executed at Yekaterinburg by the Bolsheviks.", "no_year_html": "Two skeletal remains found near <a href=\"https://wikipedia.org/wiki/Yekaterinburg\" title=\"Yekaterinburg\">Yekaterinburg</a> are confirmed by Russian scientists to be the remains of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Tsarevich_of_Russia\" title=\"<PERSON>, Tsarevich of Russia\">Alexei</a> and <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\"><PERSON></a>, two of the children of the last Tsar of Russia, whose entire family was executed at Yekaterinburg by the Bolsheviks.", "links": [{"title": "Yekaterinburg", "link": "https://wikipedia.org/wiki/Yekaterinburg"}, {"title": "<PERSON>, <PERSON><PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>evich_of_Russia"}, {"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "2009", "text": "Chrysler files for Chapter 11 bankruptcy.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> files for <a href=\"https://wikipedia.org/wiki/Chapter_11,_Title_11,_United_States_Code\" title=\"Chapter 11, Title 11, United States Code\">Chapter 11</a> bankruptcy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> files for <a href=\"https://wikipedia.org/wiki/Chapter_11,_Title_11,_United_States_Code\" title=\"Chapter 11, Title 11, United States Code\">Chapter 11</a> bankruptcy.", "links": [{"title": "Chrysler", "link": "https://wikipedia.org/wiki/Chrysler"}, {"title": "Chapter 11, Title 11, United States Code", "link": "https://wikipedia.org/wiki/Chapter_11,_Title_11,_United_States_Code"}]}, {"year": "2009", "text": "Seven civilians and the perpetrator are killed and another ten injured at a Queen's Day parade in Apeldoorn, Netherlands in an attempted assassination on Queen <PERSON><PERSON>.", "html": "2009 - Seven civilians and the perpetrator are <a href=\"https://wikipedia.org/wiki/2009_attack_on_the_Dutch_Royal_Family\" class=\"mw-redirect\" title=\"2009 attack on the Dutch Royal Family\">killed</a> and another ten injured at a <a href=\"https://wikipedia.org/wiki/Koningsdag\" title=\"Koningsdag\">Queen's Day</a> parade in <a href=\"https://wikipedia.org/wiki/Apeldoorn\" title=\"Apeldoorn\">Apeldoorn</a>, Netherlands in an attempted assassination on Queen <a href=\"https://wikipedia.org/wiki/Beatrix_of_the_Netherlands\" title=\"Beatrix of the Netherlands\">Beatrix</a>.", "no_year_html": "Seven civilians and the perpetrator are <a href=\"https://wikipedia.org/wiki/2009_attack_on_the_Dutch_Royal_Family\" class=\"mw-redirect\" title=\"2009 attack on the Dutch Royal Family\">killed</a> and another ten injured at a <a href=\"https://wikipedia.org/wiki/Koningsdag\" title=\"Koningsdag\">Queen's Day</a> parade in <a href=\"https://wikipedia.org/wiki/Apeldoorn\" title=\"Apeldoorn\">Apeldoorn</a>, Netherlands in an attempted assassination on Queen <a href=\"https://wikipedia.org/wiki/Beatrix_of_the_Netherlands\" title=\"Beatrix of the Netherlands\">Beatrix</a>.", "links": [{"title": "2009 attack on the Dutch Royal Family", "link": "https://wikipedia.org/wiki/2009_attack_on_the_Dutch_Royal_Family"}, {"title": "Koningsdag", "link": "https://wikipedia.org/wiki/Koningsdag"}, {"title": "Apeldoorn", "link": "https://wikipedia.org/wiki/Apeldoorn"}, {"title": "<PERSON><PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/Beat<PERSON>_of_the_Netherlands"}]}, {"year": "2012", "text": "An overloaded ferry capsizes on the Brahmaputra River in India killing at least 108 people.  At least 150 more are missing and presumed dead.", "html": "2012 - An overloaded <a href=\"https://wikipedia.org/wiki/Assam_ferry_sinking\" title=\"Assam ferry sinking\">ferry capsizes</a> on the <a href=\"https://wikipedia.org/wiki/Brahmaputra_River\" title=\"Brahmaputra River\">Brahmaputra River</a> in India killing at least 108 people. At least 150 more are missing and presumed dead.", "no_year_html": "An overloaded <a href=\"https://wikipedia.org/wiki/Assam_ferry_sinking\" title=\"Assam ferry sinking\">ferry capsizes</a> on the <a href=\"https://wikipedia.org/wiki/Brahmaputra_River\" title=\"Brahmaputra River\">Brahmaputra River</a> in India killing at least 108 people. At least 150 more are missing and presumed dead.", "links": [{"title": "Assam ferry sinking", "link": "https://wikipedia.org/wiki/Assam_ferry_sinking"}, {"title": "Brahmaputra River", "link": "https://wikipedia.org/wiki/Brahmaputra_River"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON> is inaugurated as King of the Netherlands following the abdication of <PERSON><PERSON>.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_of_the_Netherlands\" title=\"<PERSON><PERSON>Alexander of the Netherlands\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Inauguration_of_the_Dutch_monarch\" title=\"Inauguration of the Dutch monarch\">inaugurated</a> as <a href=\"https://wikipedia.org/wiki/King_of_the_Netherlands\" class=\"mw-redirect\" title=\"King of the Netherlands\">King of the Netherlands</a> following the abdication of <a href=\"https://wikipedia.org/wiki/Beatrix_of_the_Netherlands\" title=\"Beatrix of the Netherlands\">Beatrix</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Netherlands\" title=\"<PERSON><PERSON> of the Netherlands\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Inauguration_of_the_Dutch_monarch\" title=\"Inauguration of the Dutch monarch\">inaugurated</a> as <a href=\"https://wikipedia.org/wiki/King_of_the_Netherlands\" class=\"mw-redirect\" title=\"King of the Netherlands\">King of the Netherlands</a> following the abdication of <a href=\"https://wikipedia.org/wiki/Beatrix_of_the_Netherlands\" title=\"Beatrix of the Netherlands\">Beatrix</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_of_the_Netherlands"}, {"title": "Inauguration of the Dutch monarch", "link": "https://wikipedia.org/wiki/Inauguration_of_the_Dutch_monarch"}, {"title": "King of the Netherlands", "link": "https://wikipedia.org/wiki/King_of_the_Netherlands"}, {"title": "<PERSON><PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/Beat<PERSON>_of_the_Netherlands"}]}, {"year": "2014", "text": "A bomb blast in Ürümqi, China kills three people and injures 79 others.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/April_2014_%C3%9Cr%C3%BCmqi_attack\" title=\"April 2014 Ürümqi attack\">bomb blast</a> in <a href=\"https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi\" title=\"Ürümqi\">Ürümqi</a>, China kills three people and injures 79 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/April_2014_%C3%9Cr%C3%BCmqi_attack\" title=\"April 2014 Ürümqi attack\">bomb blast</a> in <a href=\"https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi\" title=\"Ürümqi\"><PERSON><PERSON><PERSON><PERSON>qi</a>, China kills three people and injures 79 others.", "links": [{"title": "April 2014 Ürümqi attack", "link": "https://wikipedia.org/wiki/April_2014_%C3%9Cr%C3%BCmqi_attack"}, {"title": "Ürümqi", "link": "https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi"}]}, {"year": "2021", "text": "Forty-five men and boys are killed in the Meron stampede in Israel.", "html": "2021 - Forty-five men and boys are killed in the <a href=\"https://wikipedia.org/wiki/2021_Meron_stampede\" class=\"mw-redirect\" title=\"2021 Meron stampede\">Meron stampede</a> in Israel.", "no_year_html": "Forty-five men and boys are killed in the <a href=\"https://wikipedia.org/wiki/2021_Meron_stampede\" class=\"mw-redirect\" title=\"2021 Meron stampede\">Meron stampede</a> in Israel.", "links": [{"title": "2021 Meron stampede", "link": "https://wikipedia.org/wiki/2021_Me<PERSON>_stampede"}]}], "Births": [{"year": "1310", "text": "King <PERSON> III of Poland (d. 1368)", "html": "1310 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Poland\" class=\"mw-redirect\" title=\"<PERSON> III of Poland\"><PERSON> of Poland</a> (d. 1368)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Poland\" class=\"mw-redirect\" title=\"<PERSON> III of Poland\"><PERSON> of Poland</a> (d. 1368)", "links": [{"title": "<PERSON> of Poland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Poland"}]}, {"year": "1331", "text": "<PERSON>, Count of Foix (d. 1391)", "html": "1331 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Foix\" title=\"<PERSON>, Count of Foix\"><PERSON>, Count of Foix</a> (d. 1391)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Foix\" title=\"<PERSON>, Count of Foix\"><PERSON>, Count of Foix</a> (d. 1391)", "links": [{"title": "<PERSON>, Count of Foix", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1383", "text": "<PERSON> of Gloucester, English countess, granddaughter of King <PERSON> of England (d. 1438)", "html": "1383 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Gloucester\" title=\"<PERSON> of Gloucester\"><PERSON> of Gloucester</a>, English countess, granddaughter of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1438)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Gloucester\" title=\"<PERSON> of Gloucester\"><PERSON> of Gloucester</a>, English countess, granddaughter of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1438)", "links": [{"title": "<PERSON> of Gloucester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Gloucester"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1425", "text": "<PERSON>, Landgrave of Thuringia (d. 1482)", "html": "1425 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON>, Landgrave of Thuringia</a> (d. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON>, Landgrave of Thuringia</a> (d. 1482)", "links": [{"title": "<PERSON>, Landgrave of Thuringia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Thuringia"}]}, {"year": "1504", "text": "<PERSON>, Italian painter (d. 1570)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Primaticcio\"><PERSON></a>, Italian painter (d. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Primaticcio\"><PERSON></a>, Italian painter (d. 1570)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>cio"}]}, {"year": "1553", "text": "<PERSON> Lorraine (d. 1601)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lorraine\"><PERSON> of Lorraine</a> (d. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lorraine\"><PERSON> of Lorraine</a> (d. 1601)", "links": [{"title": "<PERSON> of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1623", "text": "<PERSON>, French-Canadian bishop and saint (d. 1708)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian bishop and saint (d. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian bishop and saint (d. 1708)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1651", "text": "<PERSON><PERSON><PERSON>, French priest and saint (d. 1719)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and saint (d. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and saint (d. 1719)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1662", "text": "<PERSON> of England (d. 1694)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" class=\"mw-redirect\" title=\"Mary II of England\"><PERSON> of England</a> (d. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"Mary II of England\"><PERSON> of England</a> (d. 1694)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Mary_<PERSON>_of_England"}]}, {"year": "1664", "text": "<PERSON>, Prince of Conti (d. 1709)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>,_Prince_of_Conti\" title=\"<PERSON>, Prince of Conti\"><PERSON>, Prince of Conti</a> (d. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>,_Prince_of_Conti\" title=\"<PERSON>, Prince of Conti\"><PERSON>, Prince of Conti</a> (d. 1709)", "links": [{"title": "<PERSON>, Prince of Conti", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>,_Prince_<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, Bavarian general (d. 1795)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_La_Ros%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, Bavarian general (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_Ros%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, Bavarian general (d. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_Ros%C3%A9e"}]}, {"year": "1723", "text": "<PERSON><PERSON><PERSON>, French zoologist and philosopher (d. 1806)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French zoologist and philosopher (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French zoologist and philosopher (d. 1806)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, Maltese commander and politician (d. 1802)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Maltese commander and politician (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Maltese commander and politician (d. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, English-Canadian cartographer and explorer (d. 1857)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English-Canadian cartographer and explorer (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English-Canadian cartographer and explorer (d. 1857)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_(explorer)"}]}, {"year": "1777", "text": "<PERSON>, German mathematician and physicist (d. 1855)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, American businessman and entrepreneur (d. 1879)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and entrepreneur (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and entrepreneur (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON><PERSON>, Prussian soldier and politician, 10th Minister President of Prussia (d. 1879)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Prussian soldier and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_President_of_Prussia\" title=\"Minister President of Prussia\">Minister President of Prussia</a> (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Prussian soldier and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_President_of_Prussia\" title=\"Minister President of Prussia\">Minister President of Prussia</a> (d. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister President of Prussia", "link": "https://wikipedia.org/wiki/Minister_President_of_Prussia"}]}, {"year": "1829", "text": "<PERSON>, Austrian geologist and academic (d. 1884)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist and academic (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist and academic (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, French naturalist (d. 1924)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French naturalist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French naturalist (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON>, Swiss psychiatrist and eugenicist (d. 1940)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss psychiatrist and eugenicist (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss psychiatrist and eugenicist (d. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, German banker and philanthropist (d. 1920)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, German banker and philanthropist (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, German banker and philanthropist (d. 1920)", "links": [{"title": "<PERSON> (philanthropist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(philanthropist)"}]}, {"year": "1865", "text": "<PERSON>, German historian and academic (d. 1944)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Net<PERSON>\"><PERSON></a>, German historian and academic (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nettlau\"><PERSON></a>, German historian and academic (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>tlau"}]}, {"year": "1866", "text": "<PERSON>, American pioneer dentist (d. 1936)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pioneer dentist (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pioneer dentist (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1869", "text": "<PERSON>, German architect, designed the IG Farben Building and Großes Schauspielhaus (d. 1936)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/IG_Farben_Building\" title=\"IG Farben Building\">IG Farben Building</a> and <a href=\"https://wikipedia.org/wiki/Gro%C3%9Fes_Schauspielhaus\" title=\"Großes Schauspielhaus\">G<PERSON><PERSON><PERSON> Schauspielhaus</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/IG_Farben_Building\" title=\"IG Farben Building\">IG Farben Building</a> and <a href=\"https://wikipedia.org/wiki/Gro%C3%9Fes_Schauspielhaus\" title=\"Großes Schauspielhaus\">Großes Schauspielhaus</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "IG Farben Building", "link": "https://wikipedia.org/wiki/IG_Farben_Building"}, {"title": "Großes Schauspielhaus", "link": "https://wikipedia.org/wiki/Gro%C3%9Fes_<PERSON><PERSON>lhaus"}]}, {"year": "1870", "text": "<PERSON>, Hungarian composer (d. 1948)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1r\" title=\"<PERSON>\"><PERSON></a>, Hungarian composer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1r\" title=\"<PERSON>\"><PERSON></a>, Hungarian composer (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_Leh%C3%A1r"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian director, producer, and screenwriter (d. 1944)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON>, Flemish priest and author (d. 1949)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>eve\" title=\"<PERSON><PERSON>eve\"><PERSON><PERSON></a>, Flemish priest and author (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>eve\"><PERSON><PERSON></a>, Flemish priest and author (d. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>eve"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Italian physicist and politician (d. 1937)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian physicist and politician (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian physicist and politician (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, French cyclist (d. 1917)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/L%C3%A9on_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Flameng"}]}, {"year": "1877", "text": "<PERSON>, American memoirist (d. 1967)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American memoirist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American memoirist (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish psychologist, philosopher, translator, historian (of philosophy and art) and artist (d. 1948)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Witwicki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish psychologist, philosopher, translator, historian (of philosophy and art) and artist (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Witwicki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish psychologist, philosopher, translator, historian (of philosophy and art) and artist (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Witwicki"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, Hungarian Olympic champion wrestler (d. 1945)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Rich%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian Olympic champion wrestler (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rich%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian Olympic champion wrestler (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rich%C3%A1rd_<PERSON>sz"}]}, {"year": "1880", "text": "<PERSON>, Scottish cartoonist (d. 1967)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Charles_Exeter_Devereux_Crombie\" title=\"Charles <PERSON> Devereux Crombie\"><PERSON></a>, Scottish cartoonist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_Exeter_Devereux_Crombie\" title=\"Charles Exeter Devereux Crombie\"><PERSON>rom<PERSON></a>, Scottish cartoonist (d. 1967)", "links": [{"title": "<PERSON>ereux Crombie", "link": "https://wikipedia.org/wiki/Charles_Exeter_Devereux_Crombie"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, Czech soldier and author (d. 1923)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%A1ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech soldier and author (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%A1ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech soldier and author (d. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aroslav_Ha%C5%A1ek"}]}, {"year": "1883", "text": "<PERSON>, Italian painter and composer (d. 1947)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and composer (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and composer (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Swedish actor (d. 1965)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actor (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actor (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American poet, critic, and academic (d. 1974)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Ra<PERSON>m\"><PERSON></a>, American poet, critic, and academic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Ransom\"><PERSON></a>, American poet, critic, and academic (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Australian public servant (d. 1966)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, German soldier and politician, 14th German Reich Minister for Foreign Affairs (d. 1946)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, 14th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)\" title=\"Minister for Foreign Affairs (Germany)\">German Reich Minister for Foreign Affairs</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, 14th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)\" title=\"Minister for Foreign Affairs (Germany)\">German Reich Minister for Foreign Affairs</a> (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Germany)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)"}]}, {"year": "1895", "text": "<PERSON>, Canadian physician, academic, and diplomat (d. 1960)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician, academic, and diplomat (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician, academic, and diplomat (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "Reverend <PERSON>, American singer and guitarist (d. 1972)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Reverend_<PERSON>_<PERSON>\" title=\"Reverend <PERSON>\">Reverend <PERSON></a>, American singer and guitarist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reverend_<PERSON>_<PERSON>\" title=\"Reverend <PERSON>\">Reverend <PERSON></a>, American singer and guitarist (d. 1972)", "links": [{"title": "Reverend <PERSON>", "link": "https://wikipedia.org/wiki/Reverend_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Austrian scientist and businessman, founded the AVL Engineering Company (d. 1996)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_List\" title=\"Hans List\"><PERSON></a>, Austrian scientist and businessman, founded the AVL Engineering Company (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hans_List\" title=\"Hans List\"><PERSON></a>, Austrian scientist and businessman, founded the AVL Engineering Company (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hans_List"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Brazilian director and screenwriter (d. 1983)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian director and screenwriter (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian director and screenwriter (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Estonian author and poet (d. 1984)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian author and poet (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian author and poet (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Belarusian-American economist, statistician, and academic, Nobel Prize laureate (d. 1985)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-American economist, statistician, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-American economist, statistician, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1902", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (d. 1998)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1905", "text": "<PERSON>, Russian mathematician and academic (d. 2012)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American actress (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Icelandic professor of law and politician, 13th Prime Minister of Iceland (d. 1970)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(born_1908)\" title=\"<PERSON><PERSON><PERSON> (born 1908)\"><PERSON><PERSON><PERSON></a>, Icelandic professor of law and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(born_1908)\" title=\"<PERSON><PERSON><PERSON> (born 1908)\"><PERSON><PERSON><PERSON></a>, Icelandic professor of law and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON> (born 1908)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_(born_1908)"}, {"title": "Prime Minister of Iceland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iceland"}]}, {"year": "1908", "text": "<PERSON>, Canadian air marshal and politician (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian air marshal and politician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian air marshal and politician (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON> <PERSON><PERSON>, Irish sculptor and educator (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish sculptor and educator (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ill<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> McWill<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish sculptor and educator (d. 1992)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1909", "text": "Juliana of the Netherlands (d. 2004)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Netherlands\" title=\"Juliana of the Netherlands\"><PERSON> of the Netherlands</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juliana_of_the_Netherlands\" title=\"<PERSON> of the Netherlands\"><PERSON> of the Netherlands</a> (d. 2004)", "links": [{"title": "Juliana of the Netherlands", "link": "https://wikipedia.org/wiki/Juliana_of_the_Netherlands"}]}, {"year": "1910", "text": "<PERSON>, Filipino pianist, violinist, and composer (d. 2002)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino pianist, violinist, and composer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino pianist, violinist, and composer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>io"}]}, {"year": "1914", "text": "<PERSON>, American middle-distance runner (d. 1997)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American middle-distance runner (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American middle-distance runner (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Brazilian singer-songwriter, actor, and painter (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter, actor, and painter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter, actor, and painter (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1916", "text": "<PERSON>, Estonian journalist and author (d. 2003)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and author (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American mathematician and engineer (d. 2001)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and engineer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and engineer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American conductor (d. 1999)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, American conductor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, American conductor (d. 1999)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>(conductor)"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American singer (d. 2017)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>ain\"><PERSON><PERSON></a>, American singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>ain\"><PERSON><PERSON></a>, American singer (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ain"}]}, {"year": "1920", "text": "<PERSON>, Irish-English race car driver and pilot (d. 1994)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Irish-English race car driver and pilot (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Irish-English race car driver and pilot (d. 1994)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Austrian-American historian and woman's history author (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-American historian and woman's history author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-American historian and woman's history author (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, British army officer and fundraiser (d. 2021)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fundraiser)\" class=\"mw-redirect\" title=\"<PERSON> (fundraiser)\"><PERSON></a>, British army officer and fundraiser (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fundraiser)\" class=\"mw-redirect\" title=\"<PERSON> (fundraiser)\"><PERSON></a>, British army officer and fundraiser (d. 2021)", "links": [{"title": "<PERSON> (fundraiser)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fundraiser)"}]}, {"year": "1921", "text": "<PERSON>, American scientist, co-invented the GPS (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist, co-invented the <a href=\"https://wikipedia.org/wiki/Global_Positioning_System\" title=\"Global Positioning System\">GPS</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist, co-invented the <a href=\"https://wikipedia.org/wiki/Global_Positioning_System\" title=\"Global Positioning System\">GPS</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Global Positioning System", "link": "https://wikipedia.org/wiki/Global_Positioning_System"}]}, {"year": "1922", "text": "<PERSON>, South African cricketer (d. 1995)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American bassist (d. 2005)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 42nd <PERSON><PERSON><PERSON><PERSON> (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Kagamisat<PERSON>_Kiyoji\" title=\"Kagamisat<PERSON> Kiyoji\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 42nd <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kagami<PERSON><PERSON>_Kiyoji\" title=\"Kagamisat<PERSON> Kiyoji\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 42nd <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kagamisato_Ki<PERSON>ji"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1924", "text": "<PERSON>, American lyricist (d. 2023)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lyricist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lyricist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Estonian KGB officer and author (d. 2008)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Uno_<PERSON>\" title=\"Uno La<PERSON>\"><PERSON><PERSON></a>, Estonian <a href=\"https://wikipedia.org/wiki/KGB\" title=\"KGB\">KGB</a> officer and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uno_<PERSON>\" title=\"Uno Laht\"><PERSON><PERSON></a>, Estonian <a href=\"https://wikipedia.org/wiki/KGB\" title=\"KGB\">KGB</a> officer and author (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uno_<PERSON>ht"}, {"title": "KGB", "link": "https://wikipedia.org/wiki/KGB"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, French actress (d. 2001)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1960)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Indian composer (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian composer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian composer (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hale"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, American actress and comedian (d. 2021)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and comedian (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and comedian (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian author and academic (d. 2000)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Italian tennis player (d. 1995)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Sirola\" title=\"Orlando <PERSON>\"><PERSON></a>, Italian tennis player (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Sirola"}]}, {"year": "1930", "text": "<PERSON>, French psychotherapist and philosopher (d. 1992)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_G<PERSON>ttari\" title=\"<PERSON>\"><PERSON></a>, French psychotherapist and philosopher (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_G<PERSON>ttari\" title=\"<PERSON>\"><PERSON></a>, French psychotherapist and philosopher (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Guattari"}]}, {"year": "1933", "text": "<PERSON>, <PERSON> of Bowden, English politician", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Bowden\" title=\"<PERSON>, Baron <PERSON> of Bowden\"><PERSON>, Baron <PERSON> of Bowden</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Bowden\" title=\"<PERSON>, Baron <PERSON> of Bowden\"><PERSON>, Baron <PERSON> of Bowden</a>, English politician", "links": [{"title": "<PERSON>, <PERSON> of Bowden", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Bowden"}]}, {"year": "1934", "text": "<PERSON>, English singer-songwriter (d. 1995)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English poet and playwright", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actor and talk show host (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and talk show host (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and talk show host (d. 2012)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Slovak director and screenwriter (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak director and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak director and screenwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American author and screenwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Dutch journalist and writer (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch journalist and writer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch journalist and writer (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1940", "text": "<PERSON>, Australian rugby player and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, Australian rugby player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, Australian rugby player and politician", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Estonian sculptor (d. 1988)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/%C3%9Clo_%C3%95un\" title=\"<PERSON>lo Õun\"><PERSON><PERSON> Õun</a>, Estonian sculptor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9Clo_%C3%95un\" title=\"<PERSON>lo Õun\"><PERSON><PERSON> Õun</a>, Estonian sculptor (d. 1988)", "links": [{"title": "Ülo Õun", "link": "https://wikipedia.org/wiki/%C3%9Clo_%C3%95un"}]}, {"year": "1940", "text": "<PERSON>, American actor and painter (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and painter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and painter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Greek lawyer and politician, Greek Minister of Foreign Affairs", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)\" class=\"mw-redirect\" title=\"Minister for Foreign Affairs (Greece)\">Greek Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)\" class=\"mw-redirect\" title=\"Minister for Foreign Affairs (Greece)\">Greek Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Greece)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)"}]}, {"year": "1941", "text": "<PERSON>, New Zealand-Australian singer-songwriter (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer-songwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer-songwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON> of Kedah, Sultan of Kedah", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Kedah\" title=\"<PERSON><PERSON><PERSON> of Kedah\"><PERSON><PERSON><PERSON> of Kedah</a>, Sultan of Kedah", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Kedah\" title=\"<PERSON><PERSON><PERSON> of Kedah\"><PERSON><PERSON><PERSON> of Kedah</a>, Sultan of Kedah", "links": [{"title": "<PERSON><PERSON><PERSON> of Kedah", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Kedah"}]}, {"year": "1943", "text": "<PERSON>, Zambian politician, 2nd President of Zambia (d. 2011)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frederick_<PERSON>"}, {"title": "President of Zambia", "link": "https://wikipedia.org/wiki/President_of_Zambia"}]}, {"year": "1943", "text": "<PERSON>, American pop singer-songwriter (d. 2016)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer-songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer-songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Norwegian author, scholar, and academic (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author, scholar, and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author, scholar, and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actress (d. 2010)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON> <PERSON>, British radiologist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British radiologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British radiologist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American novelist, essayist, and poet", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter, guitarist, and activist (d. 2001)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and activist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and activist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mimi_Fari%C3%B1a"}]}, {"year": "1945", "text": "<PERSON>, American pilot, and astronaut (d. 1986)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(astronaut)\" class=\"mw-redirect\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American pilot, and astronaut (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(astronaut)\" class=\"mw-redirect\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American pilot, and astronaut (d. 1986)", "links": [{"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(astronaut)"}]}, {"year": "1946", "text": "King <PERSON> of Sweden", "html": "1946 - King <a href=\"https://wikipedia.org/wiki/Carl_<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a>", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Carl_<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a>", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1946", "text": "<PERSON>, American animator, producer, and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American swimmer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English theologian and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Norwegian singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Finn_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Danish footballer and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_K%C3%B8hlert\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_K%C3%B8hlert\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tom_K%C3%B8hlert"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Swedish economist and politician, Swedish Minister for Financial Markets", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Sweden)\" title=\"Ministry of Finance (Sweden)\">Swedish Minister for Financial Markets</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Sweden)\" title=\"Ministry of Finance (Sweden)\">Swedish Minister for Financial Markets</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Finance (Sweden)", "link": "https://wikipedia.org/wiki/Ministry_of_Finance_(Sweden)"}]}, {"year": "1948", "text": "<PERSON>, American guitarist and singer-songwriter (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and singer-songwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and singer-songwriter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_<PERSON>g%C3%A9"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Hungarian athlete", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Margit_Papp\" title=\"Margit Papp\"><PERSON><PERSON></a>, Hungarian athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Margit_Papp\" title=\"Margit Papp\"><PERSON><PERSON></a>, Hungarian athlete", "links": [{"title": "Margit Papp", "link": "https://wikipedia.org/wiki/Margit_Papp"}]}, {"year": "1949", "text": "<PERSON>, American baseball player and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese academic and politician, 114th Prime Minister of Portugal and 9th Secretary-General of the United Nations", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Guterres\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese academic and politician, 114th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> and 9th <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_Guterres\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese academic and politician, 114th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> and 9th <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_G<PERSON>res"}, {"title": "Prime Minister of Portugal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Portugal"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}]}, {"year": "1949", "text": "<PERSON>, German tennis player (d. 2014)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, French director and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Dutch motorcycle racer (d. 1984)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch motorcycle racer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch motorcycle racer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jack_<PERSON>del<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer and bass player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, New Zealand director, producer, and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jane_<PERSON>ion"}]}, {"year": "1954", "text": "<PERSON>, English diplomat, UK Permanent Representative to the European Union", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Permanent_Representatives_of_the_United_Kingdom_to_the_European_Union\" class=\"mw-redirect\" title=\"List of Permanent Representatives of the United Kingdom to the European Union\">UK Permanent Representative to the European Union</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Permanent_Representatives_of_the_United_Kingdom_to_the_European_Union\" class=\"mw-redirect\" title=\"List of Permanent Representatives of the United Kingdom to the European Union\">UK Permanent Representative to the European Union</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Permanent Representatives of the United Kingdom to the European Union", "link": "https://wikipedia.org/wiki/List_of_Permanent_Representatives_of_the_United_Kingdom_to_the_European_Union"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, French journalist and environmentalist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and environmentalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and environmentalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English lawyer and judge", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Indian director and screenwriter (d. 2023)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and screenwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Bosnian writer and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Zlatko_Top%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian writer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zlatko_Top%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian writer and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zlatko_Top%C4%8Di%C4%87"}]}, {"year": "1956", "text": "<PERSON>, Danish director and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American rapper and songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mike\" title=\"Wonder Mike\"><PERSON></a>, American rapper and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wonder_Mike\" title=\"Wonder Mike\"><PERSON></a>, American rapper and songwriter", "links": [{"title": "<PERSON> Mike", "link": "https://wikipedia.org/wiki/<PERSON>_Mike"}]}, {"year": "1958", "text": "<PERSON>, French actor, director, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian economist and politician, 22nd Prime Minister of Canada", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1960", "text": "<PERSON>, English lawyer and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)"}]}, {"year": "1960", "text": "<PERSON>, American academic and politician, 70th Lieutenant Governor of Massachusetts", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 70th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts\" title=\"Lieutenant Governor of Massachusetts\">Lieutenant Governor of Massachusetts</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 70th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts\" title=\"Lieutenant Governor of Massachusetts\">Lieutenant Governor of Massachusetts</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic footballer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Arn%C3%B3r_Gu%C3%B0<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arn%C3%B3r_Gu%C3%B0<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arn%C3%B3r_Gu%C3%B0<PERSON><PERSON>sen"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American basketball player, coach, and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English tenor and conductor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tenor and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tenor and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American race car driver and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Malaysian-Indian businessman, co-founded Tune Group", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian-Indian businessman, co-founded <a href=\"https://wikipedia.org/wiki/Tune_Group\" title=\"Tune Group\">Tune Group</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian-Indian businessman, co-founded <a href=\"https://wikipedia.org/wiki/Tune_Group\" title=\"Tune Group\">Tune Group</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Tune Group", "link": "https://wikipedia.org/wiki/Tune_Group"}]}, {"year": "1964", "text": "<PERSON>, Australian cricketer, coach, and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Belgian footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Lorenzo_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lorenzo_<PERSON>\" title=\"Lorenzo <PERSON>\"><PERSON></a>, Belgian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lorenzo_Staelens"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian actor (d. 2022)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian actor (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Romanian-Australian discus thrower", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-Australian discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-Australian discus thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ian"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey_b._1966)\" class=\"mw-redirect\" title=\"<PERSON> (ice hockey b. 1966)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey_b._1966)\" class=\"mw-redirect\" title=\"<PERSON> (ice hockey b. 1966)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey b. 1966)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey_b._1966)"}]}, {"year": "1966", "text": "<PERSON>, American football player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Taiwanese singer-songwriter and actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Bulgarian-born Russian singer, composer and actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-born Russian singer, composer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-born Russian singer, composer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American rapper", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Turbo_B\" title=\"Turbo B\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turbo_B\" title=\"Turbo B\"><PERSON></a>, American rapper", "links": [{"title": "Turbo B", "link": "https://wikipedia.org/wiki/Turbo_B"}]}, {"year": "1969", "text": "<PERSON>, American bass player and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Warren_De<PERSON>ver"}]}, {"year": "1969", "text": "<PERSON><PERSON>, English accountant and politician, Secretary of State for International Development", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_International_Development\" class=\"mw-redirect\" title=\"Secretary of State for International Development\">Secretary of State for International Development</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_International_Development\" class=\"mw-redirect\" title=\"Secretary of State for International Development\">Secretary of State for International Development</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Secretary of State for International Development", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_International_Development"}]}, {"year": "1969", "text": "<PERSON>, Brazilian bass player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, Brazilian bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, Brazilian bass player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Japanese actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English comedian and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Dutch athlete", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Jamaican sprinter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian rugby league player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American astronaut", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American politician", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American technologist, journalist, and author", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American technologist, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American technologist, journalist, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Taiwanese singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Mexican footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Argentinian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Irish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shea\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shea\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>%27Shea"}]}, {"year": "1981", "text": "<PERSON><PERSON>, British-Indian actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nayyar\" title=\"<PERSON><PERSON> Nayyar\"><PERSON><PERSON></a>, British-Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nayyar\" title=\"<PERSON><PERSON> Nayyar\"><PERSON><PERSON></a>, British-Indian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter, multi-instrumentalist, and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, multi-instrumentalist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, multi-instrumentalist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian-American singer-songwriter, dancer, and actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter, dancer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_(American_football)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, German luger", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Tat<PERSON>_H%C3%BCfner\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German luger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tat<PERSON>_H%C3%BCfner\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German luger", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tatjana_H%C3%BCfner"}]}, {"year": "1983", "text": "<PERSON>, Slovenian hurdler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Marina_Tomi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Slovenian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marina_Tomi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Slovenian hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Tomi%C4%87"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Williamson"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ne_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American wrestler and manager", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Estonian javelin thrower", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Risto_M%C3%A4tas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Risto_M%C3%A4tas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian javelin thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Risto_M%C3%A4tas"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Israeli actress and model", "html": "1985 - <a href=\"https://wikipedia.org/wiki/G<PERSON>_Gado<PERSON>\" title=\"G<PERSON> Gadot\"><PERSON><PERSON></a>, Israeli actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON> Gadot\"><PERSON><PERSON></a>, Israeli actress and model", "links": [{"title": "Gal Gadot", "link": "https://wikipedia.org/wiki/G<PERSON>_<PERSON>t"}]}, {"year": "1985", "text": "<PERSON>, American journalist, singer, and prostitute", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American journalist, singer, and prostitute", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American journalist, singer, and prostitute", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actress and singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Estonian biathlete", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ldvee\" title=\"<PERSON><PERSON> Kaldvee\"><PERSON><PERSON></a>, Estonian biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ldvee\" title=\"<PERSON><PERSON> Kaldvee\"><PERSON><PERSON></a>, Estonian biathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Kaldvee"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Australian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Alipate <PERSON>\"><PERSON><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Alipate <PERSON>\"><PERSON><PERSON></a>, Australian footballer", "links": [{"title": "Alipate <PERSON>", "link": "https://wikipedia.org/wiki/Alipate_Carlile"}]}, {"year": "1987", "text": "<PERSON>, South African cricketer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian chef", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chef)\" title=\"<PERSON> (chef)\"><PERSON></a>, Australian chef", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(chef)\" title=\"<PERSON> (chef)\"><PERSON></a>, Australian chef", "links": [{"title": "<PERSON> (chef)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(chef)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Dutch field hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch field hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Cuban actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Chinese singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>jun\"><PERSON></a>, Chinese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Xijun\"><PERSON></a>, Chinese singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, South Korean taekwondo athlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Oh_Hye-ri\" title=\"Oh Hye-ri\"><PERSON> <PERSON><PERSON>-ri</a>, South Korean taekwondo athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oh_<PERSON>ye-ri\" title=\"Oh Hye-ri\"><PERSON> <PERSON>-ri</a>, South Korean taekwondo athlete", "links": [{"title": "Oh <PERSON><PERSON><PERSON>ri", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-ri"}]}, {"year": "1989", "text": "<PERSON>, South Korean singer and actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>yo<PERSON>\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, English triathlete", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English triathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian singer-songwriter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mac_DeMarco"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Spanish singer-songwriter and actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ib%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ib%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Rib%C3%B3"}]}, {"year": "1991", "text": "<PERSON>, American ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American rapper and producer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1992", "text": "<PERSON>, German politician", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A9_ter_Stegen\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_ter_Stegen\" title=\"<PERSON><PERSON><PERSON> te<PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-Andr%C3%A9_ter_Stegen"}]}, {"year": "1993", "text": "<PERSON>, Dutch swimmer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Czech canoeist", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech canoeist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, South Korean actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-jin\" title=\"<PERSON><PERSON> <PERSON><PERSON>-jin\"><PERSON><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-jin\" title=\"<PERSON><PERSON><PERSON>-jin\"><PERSON><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON><PERSON>n", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-jin"}]}, {"year": "1994", "text": "<PERSON>, Chinese tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Friend\" title=\"<PERSON> Friend\"><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Friend\" title=\"<PERSON> Friend\"><PERSON></a>, English singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Friend"}]}, {"year": "1997", "text": "<PERSON>, Polish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Spanish actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3s\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3s\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgina_Amor%C3%B3s"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Dutch chess grandmaster", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> For<PERSON>\"><PERSON><PERSON></a>, Dutch chess grandmaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> For<PERSON>\"><PERSON><PERSON></a>, Dutch chess grandmaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Thai actor and singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON>_<PERSON>n"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Japanese singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Spanish-Swedish chess player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-Swedish chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-Swedish chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, English footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gi"}]}, {"year": "2003", "text": "<PERSON>, British actress", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, South Korean actor", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}], "Deaths": [{"year": "65", "text": "<PERSON>, Roman poet (b. 39)", "html": "65 - AD 65 - <a href=\"https://wikipedia.org/wiki/Lucan\" title=\"Lucan\"><PERSON></a>, Roman poet (b. 39)", "no_year_html": "AD 65 - <a href=\"https://wikipedia.org/wiki/Lucan\" title=\"Lucan\"><PERSON></a>, Roman poet (b. 39)", "links": [{"title": "Lucan", "link": "https://wikipedia.org/wiki/Lucan"}]}, {"year": "125", "text": "<PERSON>, Chinese emperor (b. 94)", "html": "125 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Han\" title=\"Emperor An of Han\"><PERSON></a>, Chinese emperor (b. 94)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Han\" title=\"Emperor An of Han\"><PERSON></a>, Chinese emperor (b. 94)", "links": [{"title": "Emperor <PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "535", "text": "<PERSON><PERSON><PERSON><PERSON>, Ostrogothic queen and regent", "html": "535 - <a href=\"https://wikipedia.org/wiki/Amalasuntha\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ostrogothic queen and regent", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amalasuntha\" class=\"mw-redirect\" title=\"<PERSON><PERSON>sun<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ostrogothic queen and regent", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>tha"}]}, {"year": "783", "text": "<PERSON><PERSON><PERSON> of the Vinzgau, Frankish queen", "html": "783 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_the_Vinzgau\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of the Vinzgau\"><PERSON><PERSON><PERSON> of the Vinzgau</a>, Frankish queen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_the_Vinzgau\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of the Vinzgau\"><PERSON><PERSON><PERSON> of the Vinzgau</a>, Frankish queen", "links": [{"title": "Hildegard of the Vinzgau", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_the_Vinzgau"}]}, {"year": "1002", "text": "<PERSON><PERSON><PERSON>, German nobleman", "html": "1002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Mar<PERSON> of Meissen\"><PERSON><PERSON><PERSON></a>, German nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON> of Meissen\"><PERSON><PERSON><PERSON></a>, German nobleman", "links": [{"title": "<PERSON><PERSON><PERSON>, Mar<PERSON> of Meissen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_Mei<PERSON>"}]}, {"year": "1030", "text": "<PERSON><PERSON><PERSON> of Ghazni, Ghaznavid emir (b. 971)", "html": "1030 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Ghazni\" title=\"<PERSON><PERSON><PERSON> of Ghazni\"><PERSON><PERSON><PERSON> of Ghazni</a>, <PERSON><PERSON><PERSON><PERSON><PERSON> emir (b. 971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Ghazni\" title=\"<PERSON><PERSON><PERSON> of Ghazni\"><PERSON><PERSON><PERSON> of Ghazni</a>, <PERSON><PERSON><PERSON><PERSON><PERSON> emir (b. 971)", "links": [{"title": "<PERSON><PERSON><PERSON> of Ghazni", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_G<PERSON>z<PERSON>"}]}, {"year": "1063", "text": "<PERSON>, Chinese emperor (b. 1010)", "html": "1063 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\"><PERSON></a>, Chinese emperor (b. 1010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\"><PERSON></a>, Chinese emperor (b. 1010)", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1131", "text": "Ad<PERSON><PERSON>, French knight and saint", "html": "1131 - <a href=\"https://wikipedia.org/wiki/Adjutor\" title=\"Adjutor\">Adjutor</a>, French knight and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adjutor\" title=\"Adjutor\">Adjutor</a>, French knight and saint", "links": [{"title": "Adjutor", "link": "https://wikipedia.org/wiki/Adjutor"}]}, {"year": "1305", "text": "<PERSON>, Italian military adventurer (b. 1267)", "html": "1305 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian military adventurer (b. 1267)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian military adventurer (b. 1267)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1341", "text": "<PERSON>, duke of Brittany (b. 1286)", "html": "1341 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON></a>, duke of Brittany (b. 1286)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON></a>, duke of Brittany (b. 1286)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1439", "text": "<PERSON>, 13th Earl of Warwick, English commander (b. 1382)", "html": "1439 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_13th_Earl_of_Warwick\" class=\"mw-redirect\" title=\"<PERSON>, 13th Earl of Warwick\"><PERSON>, 13th Earl of Warwick</a>, English commander (b. 1382)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_13th_Earl_of_Warwick\" class=\"mw-redirect\" title=\"<PERSON>, 13th Earl of Warwick\"><PERSON>, 13th Earl of Warwick</a>, English commander (b. 1382)", "links": [{"title": "<PERSON>, 13th Earl of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_13th_Earl_of_Warwick"}]}, {"year": "1524", "text": "<PERSON>, seigneur <PERSON>, French soldier (b. 1473)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_seigneur_<PERSON>_<PERSON>\" title=\"<PERSON>, seigneur de <PERSON>\"><PERSON>, seigneur de <PERSON></a>, French soldier (b. 1473)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_seigneur_de_<PERSON>\" title=\"<PERSON>, seigneur de <PERSON>\"><PERSON>, seigneur de <PERSON></a>, French soldier (b. 1473)", "links": [{"title": "<PERSON>, seigneur de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_seigneur_<PERSON>_<PERSON>"}]}, {"year": "1544", "text": "<PERSON>, 1st Baron <PERSON> of Walden, English lawyer and judge, Lord Chancellor of England (b. 1488)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Walden\" title=\"<PERSON>, 1st Baron <PERSON> of Walden\"><PERSON>, 1st Baron <PERSON> of Walden</a>, English lawyer and judge, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of England</a> (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Walden\" title=\"<PERSON>, 1st Baron <PERSON> of Walden\"><PERSON>, 1st Baron <PERSON> of Walden</a>, English lawyer and judge, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of England</a> (b. 1488)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Walden", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Walden"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1550", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Burmese king (b. 1516)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/Tabin<PERSON><PERSON>hti\" title=\"Tabin<PERSON><PERSON>ht<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (b. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tabin<PERSON><PERSON>hti\" title=\"Tabin<PERSON><PERSON>ht<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (b. 1516)", "links": [{"title": "<PERSON>binshweht<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>binshwehti"}]}, {"year": "1632", "text": "<PERSON>, Count of Tilly, Bavarian general (b. 1559)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Tilly\" title=\"<PERSON>, Count of Tilly\"><PERSON>, Count of Tilly</a>, Bavarian general (b. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Tilly\" title=\"<PERSON>, Count of Tilly\"><PERSON>, Count of Tilly</a>, Bavarian general (b. 1559)", "links": [{"title": "<PERSON>, Count of Tilly", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1632", "text": "<PERSON><PERSON><PERSON>, Swedish-Polish son of <PERSON> of Sweden (b. 1566)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_Vasa\" title=\"<PERSON><PERSON><PERSON> III Vasa\"><PERSON><PERSON><PERSON></a>, Swedish-Polish son of <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Sweden\" title=\"<PERSON> III of Sweden\"><PERSON> of Sweden</a> (b. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_Vasa\" title=\"<PERSON><PERSON><PERSON> III Vasa\"><PERSON><PERSON><PERSON></a>, Swedish-Polish son of <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Sweden\" title=\"<PERSON> III of Sweden\"><PERSON> of Sweden</a> (b. 1566)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1637", "text": "<PERSON><PERSON>, Japanese daimyō (b. 1571)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gashige\" title=\"<PERSON><PERSON> Nagashige\"><PERSON><PERSON></a>, Japanese daimyō (b. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hige\" title=\"<PERSON><PERSON> Nagashige\"><PERSON><PERSON></a>, Japanese daimyō (b. 1571)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gashige"}]}, {"year": "1655", "text": "<PERSON><PERSON><PERSON>, French painter (b. 1617)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1617)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1660", "text": "<PERSON><PERSON>, Dutch historian and scholar (b. 1576)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch historian and scholar (b. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch historian and scholar (b. 1576)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Scriverius"}]}, {"year": "1672", "text": "<PERSON> of the Incarnation, French-Canadian nun and saint, founded the Ursulines of Quebec (b. 1599)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Incarnation_(Ursuline)\" title=\"Marie of the Incarnation (Ursuline)\"><PERSON> of the Incarnation</a>, French-Canadian nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Ursulines_of_Quebec\" title=\"Ursulines of Quebec\">Ursulines of Quebec</a> (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Incarnation_(Ursuline)\" title=\"Marie of the Incarnation (Ursuline)\"><PERSON> of the Incarnation</a>, French-Canadian nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Ursulines_of_Quebec\" title=\"Ursulines of Quebec\">Ursulines of Quebec</a> (b. 1599)", "links": [{"title": "Marie of the Incarnation (Ursuline)", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Incarnation_(<PERSON><PERSON><PERSON><PERSON>)"}, {"title": "Ursulines of Quebec", "link": "https://wikipedia.org/wiki/Ursulines_of_Quebec"}]}, {"year": "1696", "text": "<PERSON>, English chemist and academic (b. 1640)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (b. 1640)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1712", "text": "<PERSON>, Dutch theologian and author (b. 1633)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theologian and author (b. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theologian and author (b. 1633)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1733", "text": "<PERSON>, 1st Marquis of Abrantes, Portuguese diplomat (b. 1676)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A1_Almeida_e_<PERSON>,_1st_Marquis_of_Abrantes\" title=\"<PERSON> Almeida <PERSON>, 1st Marquis of Abrantes\"><PERSON>, 1st Marquis of Abrantes</a>, Portuguese diplomat (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A1_Almeida_e_<PERSON>,_1st_Marquis_of_Abrantes\" title=\"<PERSON>á Almeida e <PERSON>, 1st Marquis of Abrantes\"><PERSON> Almei<PERSON>, 1st Marquis of Abrantes</a>, Portuguese diplomat (b. 1676)", "links": [{"title": "<PERSON> Almei<PERSON>, 1st Marquis of Abrantes", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_de_S%C3%A1_Almeida_e_<PERSON>,_1st_Marquis_of_Abrantes"}]}, {"year": "1736", "text": "<PERSON>, German scholar and author (b. 1668)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and author (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and author (b. 1668)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, French organist and composer (b. 1684)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_d%27Agincourt\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_d%27Agincourt\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_d%27Agincourt"}]}, {"year": "1792", "text": "<PERSON>, 4th Earl of Sandwich, English politician, Secretary of State for the Northern Department (b. 1718)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_Sandwich\" title=\"<PERSON>, 4th Earl of Sandwich\"><PERSON>, 4th Earl of Sandwich</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_Sandwich\" title=\"<PERSON>, 4th Earl of Sandwich\"><PERSON>, 4th Earl of Sandwich</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (b. 1718)", "links": [{"title": "<PERSON>, 4th Earl of Sandwich", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Sandwich"}, {"title": "Secretary of State for the Northern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department"}]}, {"year": "1795", "text": "<PERSON><PERSON><PERSON>, French archaeologist and author (b. 1716)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9lemy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French archaeologist and author (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9lemy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French archaeologist and author (b. 1716)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9lemy"}]}, {"year": "1806", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 5th <PERSON><PERSON><PERSON><PERSON> (b. 1758)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/Onogawa_Kisabur%C5%8D\" title=\"Onogawa Kisaburō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 5th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Onogawa_Kisabur%C5%8D\" title=\"Onoga<PERSON> Kisaburō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 5th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1758)", "links": [{"title": "Onogawa Kisaburō", "link": "https://wikipedia.org/wiki/Onogawa_Kisabur%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1841", "text": "<PERSON>, Danish philologist and author (b. 1758)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish philologist and author (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish philologist and author (b. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, Austrian commander and duke of Teschen (b. 1771)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Teschen\" title=\"<PERSON><PERSON><PERSON>, Duke of Teschen\"><PERSON></a>, Austrian commander and duke of Teschen (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Teschen\" title=\"<PERSON><PERSON><PERSON>, Duke of Teschen\"><PERSON></a>, Austrian commander and duke of Teschen (b. 1771)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Teschen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Teschen"}]}, {"year": "1863", "text": "<PERSON>, French captain (b. 1828)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French captain (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French captain (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, English admiral, meteorologist, and politician, 2nd Governor of New Zealand (b. 1805)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral, meteorologist, and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_New_Zealand\" class=\"mw-redirect\" title=\"Governor of New Zealand\">Governor of New Zealand</a> (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral, meteorologist, and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_New_Zealand\" class=\"mw-redirect\" title=\"Governor of New Zealand\">Governor of New Zealand</a> (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Zealand", "link": "https://wikipedia.org/wiki/Governor_of_New_Zealand"}]}, {"year": "1870", "text": "<PERSON>, Canadian bishop and missionary (b. 1792)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Canadian bishop and missionary (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Canadian bishop and missionary (b. 1792)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French explorer, lithographer, and cartographer (b. 1766)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French explorer, lithographer, and cartographer (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French explorer, lithographer, and cartographer (b. 1766)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A9ric_W<PERSON>eck"}]}, {"year": "1879", "text": "<PERSON>, American religious leader (b. 1804)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, French painter (b. 1832)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1832)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American paleontologist and author (b. 1823)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist and author (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist and author (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American railroad engineer (b. 1864)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American railroad engineer (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American railroad engineer (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Canadian physician and activist (b. 1831)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and activist (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and activist (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Greek poet and critic (b. 1856)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9as\" title=\"<PERSON>\"><PERSON></a>, Greek poet and critic (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9as\" title=\"<PERSON>\"><PERSON></a>, Greek poet and critic (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9as"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American pilot (b. 1892)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pilot (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pilot (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON> <PERSON><PERSON>, English poet and scholar (b. 1859)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet and scholar (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet and scholar (b. 1859)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American boxer (b. 1883)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American footballer (b. 1902)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Danish linguist and academic (b. 1860)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish linguist and academic (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish linguist and academic (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English sociologist and economist (b. 1858)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist and economist (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist and economist (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German photographer and office and lab assistant, wife of <PERSON> (b. 1912)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer and office and lab assistant, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Adolf <PERSON>\"><PERSON></a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer and office and lab assistant, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Austrian-German politician and author, dictator of Nazi Germany (b. 1889)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a>, Austrian-German politician and author, dictator of <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a>, Austrian-German politician and author, dictator of <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1953", "text": "<PERSON>, Estonian linguist and author (b. 1874)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Estonian linguist and author (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Estonian linguist and author (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American lawyer and politician, 35th Vice President of the United States (b. 1877)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1877)", "links": [{"title": "<PERSON><PERSON> W<PERSON>", "link": "https://wikipedia.org/wiki/Alben_W<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1970", "text": "<PERSON>, Dutch historian, writer and poet (b. 1899)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian, writer and poet (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian, writer and poet (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Swedish-American actress (b. 1934)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American actress (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, English-American model and actress (b. 1934)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American model and actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American model and actress (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gia_Scala"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech poet and playwright (b. 1911)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_Ren%C4%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech poet and playwright (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_Ren%C4%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech poet and playwright (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A1clav_Ren%C4%8D"}]}, {"year": "1974", "text": "<PERSON>, American actress (b. 1900)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Puerto Rican journalist and politician, 1st Governor of Puerto Rico (b. 1898)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz_Mar%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Puerto_Rico\" title=\"Governor of Puerto Rico\">Governor of Puerto Rico</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz_Mar%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Puerto_Rico\" title=\"Governor of Puerto Rico\">Governor of Puerto Rico</a> (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Mu%C3%B1oz_Mar%C3%ADn"}, {"title": "Governor of Puerto Rico", "link": "https://wikipedia.org/wiki/Governor_of_Puerto_Rico"}]}, {"year": "1982", "text": "<PERSON>, American journalist and author (b. 1949)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Russian dancer and choreographer (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian dancer and choreographer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian dancer and choreographer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and bandleader (b. 1913)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mu<PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and bandleader (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and bandleader (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mu<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss physician and mountaineer (b. 1897)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Edouard_<PERSON><PERSON><PERSON>-Dunant\" title=\"Edouard W<PERSON>-Dunant\">Edo<PERSON></a>, Swiss physician and mountaineer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edouard_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"Edo<PERSON> W<PERSON>-Dunant\">Edo<PERSON></a>, Swiss physician and mountaineer (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edouard_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English director, producer, and screenwriter (b. 1905)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, English director, producer, and screenwriter (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, English director, producer, and screenwriter (b. 1905)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_(director)"}]}, {"year": "1989", "text": "<PERSON>, Italian director, producer, and screenwriter (b. 1929)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Sergio_Leone\" title=\"Sergio Leone\"><PERSON></a>, Italian director, producer, and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sergio_Leone\" title=\"Sergio Leone\"><PERSON></a>, Italian director, producer, and screenwriter (b. 1929)", "links": [{"title": "Sergio <PERSON>", "link": "https://wikipedia.org/wiki/Sergio_Leone"}]}, {"year": "1993", "text": "<PERSON>, English footballer (b. 1962)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Austrian race car driver (b. 1960)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American author and illustrator (b. 1919)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Burmese colonel and politician, 8th Prime Minister of Burma (b. 1920)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ma<PERSON>\"><PERSON><PERSON></a>, Burmese colonel and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Burma\" class=\"mw-redirect\" title=\"Prime Minister of Burma\">Prime Minister of Burma</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burmese colonel and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Burma\" class=\"mw-redirect\" title=\"Prime Minister of Burma\">Prime Minister of Burma</a> (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Burma", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Burma"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Syrian-English poet, publisher, and diplomat (b. 1926)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian-English poet, publisher, and diplomat (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian-English poet, publisher, and diplomat (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Danish politician, 36th Prime Minister of Denmark (b. 1914)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish politician, 36th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish politician, 36th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a> (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Denmark", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Denmark"}]}, {"year": "2002", "text": "<PERSON>, German philanthropist, founded the Gründerzeit Museum (b. 1928)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Gr%C3%BCnderzeit\" title=\"Gründerzeit\">Gründerzeit Museum</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Gr%C3%BCnderzeit\" title=\"Gründerzeit\">Gründerzeit Museum</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Gründerzeit", "link": "https://wikipedia.org/wiki/Gr%C3%BCnderzeit"}]}, {"year": "2003", "text": "<PERSON>, American economist and academic (b. 1955)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, American economist and academic (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, American economist and academic (b. 1955)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, New Zealand race car driver (b. 1956)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Poss<PERSON> Bourne\"><PERSON><PERSON><PERSON></a>, New Zealand race car driver (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Poss<PERSON> Bourne\"><PERSON><PERSON><PERSON></a>, New Zealand race car driver (b. 1956)", "links": [{"title": "Possum Bourne", "link": "https://wikipedia.org/wiki/Possum_Bourne"}]}, {"year": "2005", "text": "<PERSON>, American lieutenant and pilot (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, French philosopher (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A7ois_Revel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A7ois_Revel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_Revel"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indonesian author and academic (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Pramoe<PERSON>a_<PERSON>nta_<PERSON>\" title=\"Pramoedya Ananta <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indonesian author and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pramoe<PERSON>a_<PERSON>_<PERSON>\" title=\"Pramoedya Ananta <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indonesian author and academic (b. 1925)", "links": [{"title": "Pramoe<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pramoedya_<PERSON><PERSON>_<PERSON>er"}]}, {"year": "2007", "text": "<PERSON>, American football player (b. 1971)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linebacker)\" title=\"<PERSON> (linebacker)\"><PERSON></a>, American football player (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(linebacker)\" title=\"<PERSON> (linebacker)\"><PERSON></a>, American football player (b. 1971)", "links": [{"title": "<PERSON> (linebacker)", "link": "https://wikipedia.org/wiki/<PERSON>(linebacker)"}]}, {"year": "2007", "text": "<PERSON>, American actor, comedian, and game show panelist (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and game show panelist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and game show panelist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American film and television actor (b. 1926)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film and television actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film and television actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Dutch Antillean politician (b. 1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch Antillean politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch Antillean politician (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Dutch cyclist (b. 1935)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/He<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Indian politician, 6th Chief Minister of Arunachal Pradesh (b. 1955)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 6th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Arunachal_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Arunachal Pradesh\">Chief Minister of Arunachal Pradesh</a> (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 6th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Arunachal_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Arunachal Pradesh\">Chief Minister of Arunachal Pradesh</a> (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of Arunachal Pradesh", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Arunachal_Pradesh"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Estonian painter (b. 1915)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian painter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian painter (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>as"}]}, {"year": "2011", "text": "<PERSON>, Argentinian physicist, author, and painter (b. 1911)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian physicist, author, and painter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian physicist, author, and painter (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernesto_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Nicaraguan poet and politician, co-founded the Sandinista National Liberation Front (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Borge\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nicaraguan poet and politician, co-founded the <a href=\"https://wikipedia.org/wiki/Sandinista_National_Liberation_Front\" title=\"Sandinista National Liberation Front\">Sandinista National Liberation Front</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Borge\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nicaraguan poet and politician, co-founded the <a href=\"https://wikipedia.org/wiki/Sandinista_National_Liberation_Front\" title=\"Sandinista National Liberation Front\">Sandinista National Liberation Front</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Borge"}, {"title": "Sandinista National Liberation Front", "link": "https://wikipedia.org/wiki/Sandinista_National_Liberation_Front"}]}, {"year": "2012", "text": "<PERSON>, Norwegian swimmer (b. 1985)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian swimmer (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian swimmer (b. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Greek footballer (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (b. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Russian-Israeli historian and academic (b. 1910)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Netanyahu\"><PERSON><PERSON></a>, Russian-Israeli historian and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Netanyahu\"><PERSON><PERSON></a>, Russian-Israeli historian and academic (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Filipino painter and sculptor (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino painter and sculptor (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino painter and sculptor (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian skier (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, French author and critic (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and critic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and critic (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Indian painter and set designer (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian painter and set designer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian painter and set designer (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English biologist and academic (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biologist)\" title=\"<PERSON> (biologist)\"><PERSON></a>, English biologist and academic (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biologist)\" title=\"<PERSON> (biologist)\"><PERSON></a>, English biologist and academic (b. 1946)", "links": [{"title": "<PERSON> (biologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biologist)"}]}, {"year": "2014", "text": "<PERSON>, American businessman and politician (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Australian journalist (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newsreader)\" title=\"<PERSON> (newsreader)\"><PERSON></a>, Australian journalist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newsreader)\" title=\"<PERSON> (newsreader)\"><PERSON></a>, Australian journalist (b. 1940)", "links": [{"title": "<PERSON> (newsreader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newsreader)"}]}, {"year": "2015", "text": "<PERSON>, American singer-songwriter and producer (b. 1938)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American priest and activist (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and activist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and activist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (b. 1939)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Brazilian singer and composer (b. 1946)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Brazilian singer and composer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Brazilian singer and composer (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "2019", "text": "<PERSON>, English-American actor (b. 1944)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Nigerian drummer and composer (b. 1940)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Nigerian drummer and composer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Nigerian drummer and composer (b. 1940)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Indian actor, film director and producer (b. 1952)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, film director and producer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, film director and producer (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, English composer (b. 1936)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American singer-songwriter and actress (b. 1946)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Italian football agent (b. 1967)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian football agent (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian football agent (b. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ola"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Scottish television presenter and chef (b. 1976)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish television presenter and chef (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish television presenter and chef (b. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American writer and film director (b. 1947)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and film director (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and film director (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}