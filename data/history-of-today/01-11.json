{"date": "January 11", "url": "https://wikipedia.org/wiki/January_11", "data": {"Events": [{"year": "532", "text": "Nika riots in Constantinople: A quarrel between supporters of different chariot teams—the Blues and the Greens—in the Hippodrome escalates into violence.", "html": "532 - <a href=\"https://wikipedia.org/wiki/Nika_riots\" title=\"Nika riots\">Nika riots</a> in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>: A quarrel between supporters of different <a href=\"https://wikipedia.org/wiki/Chariot_racing\" title=\"Chariot racing\">chariot</a> teams—the Blues and the Greens—in the <a href=\"https://wikipedia.org/wiki/Hippodrome_of_Constantinople\" title=\"Hippodrome of Constantinople\">Hippodrome</a> escalates into violence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nika_riots\" title=\"Nika riots\">Nika riots</a> in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>: A quarrel between supporters of different <a href=\"https://wikipedia.org/wiki/Chariot_racing\" title=\"Chariot racing\">chariot</a> teams—the Blues and the Greens—in the <a href=\"https://wikipedia.org/wiki/Hippodrome_of_Constantinople\" title=\"Hippodrome of Constantinople\">Hippodrome</a> escalates into violence.", "links": [{"title": "Nika riots", "link": "https://wikipedia.org/wiki/Nika_riots"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "Chariot racing", "link": "https://wikipedia.org/wiki/Chariot_racing"}, {"title": "Hippodrome of Constantinople", "link": "https://wikipedia.org/wiki/Hippodrome_of_Constantinople"}]}, {"year": "630", "text": "Conquest of Mecca: <PERSON> and his followers conquer the city, and the Quraysh association of clans surrenders.", "html": "630 - <a href=\"https://wikipedia.org/wiki/Conquest_of_Mecca\" title=\"Conquest of Mecca\">Conquest of Mecca</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his followers conquer the city, and the <a href=\"https://wikipedia.org/wiki/Quraysh\" title=\"Quraysh\">Quraysh</a> association of clans surrenders.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Conquest_of_Mecca\" title=\"Conquest of Mecca\">Conquest of Mecca</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his followers conquer the city, and the <a href=\"https://wikipedia.org/wiki/Quraysh\" title=\"Quraysh\">Quraysh</a> association of clans surrenders.", "links": [{"title": "Conquest of Mecca", "link": "https://wikipedia.org/wiki/Conquest_of_Mecca"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Qur<PERSON>h"}]}, {"year": "930", "text": "Sack of Mecca by the Qarmatians.", "html": "930 - <a href=\"https://wikipedia.org/wiki/Sack_of_Mecca\" title=\"Sack of Mecca\">Sack of Mecca</a> by the <a href=\"https://wikipedia.org/wiki/Qarmatians\" title=\"Qarmatians\">Qarmatians</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sack_of_Mecca\" title=\"Sack of Mecca\">Sack of Mecca</a> by the <a href=\"https://wikipedia.org/wiki/Qarmatians\" title=\"Qarmatians\">Qarmatians</a>.", "links": [{"title": "Sack of Mecca", "link": "https://wikipedia.org/wiki/Sack_of_Mecca"}, {"title": "Qarmatians", "link": "https://wikipedia.org/wiki/Qarmatians"}]}, {"year": "1055", "text": "<PERSON><PERSON> is crowned empress of the Byzantine Empire.", "html": "1055 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_Porphyrogenita\" class=\"mw-redirect\" title=\"<PERSON><PERSON> III Porphyrogenita\"><PERSON><PERSON></a> is crowned empress of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_Porphyrogenita\" class=\"mw-redirect\" title=\"<PERSON>a III Porphyrogenita\"><PERSON><PERSON></a> is crowned empress of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "links": [{"title": "Theodora III Porphyrogenita", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_III_Porphyrogenita"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "1158", "text": "<PERSON><PERSON><PERSON>, Duke of Bohemia becomes King of Bohemia.", "html": "1158 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON>, Duke of Bohemia</a> becomes King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bohemia\" title=\"Kingdom of Bohemia\">Bohemia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON>, Duke of Bohemia</a> becomes King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bohemia\" title=\"Kingdom of Bohemia\">Bohemia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia"}, {"title": "Kingdom of Bohemia", "link": "https://wikipedia.org/wiki/Kingdom_of_Bohemia"}]}, {"year": "1569", "text": "First recorded lottery in England.", "html": "1569 - First recorded <a href=\"https://wikipedia.org/wiki/Lottery\" title=\"Lottery\">lottery</a> in <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "no_year_html": "First recorded <a href=\"https://wikipedia.org/wiki/Lottery\" title=\"Lottery\">lottery</a> in <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "links": [{"title": "Lottery", "link": "https://wikipedia.org/wiki/Lottery"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}]}, {"year": "1654", "text": "Arauco War: A Spanish army is defeated by local Mapuche-Huilliches as it tries to cross Bueno River in Southern Chile.", "html": "1654 - <a href=\"https://wikipedia.org/wiki/Arauco_War\" title=\"Arauco War\">Arauco War</a>: A Spanish army <a href=\"https://wikipedia.org/wiki/Battle_of_R%C3%ADo_Bueno\" class=\"mw-redirect\" title=\"Battle of Río Bueno\">is defeated</a> by local <a href=\"https://wikipedia.org/wiki/Huilliche_people\" title=\"Huilliche people\">Mapuche-Huilliches</a> as it tries to cross <a href=\"https://wikipedia.org/wiki/Bueno_River\" title=\"Bueno River\">Bueno River</a> in <a href=\"https://wikipedia.org/wiki/Zona_Sur\" title=\"Zona Sur\">Southern Chile</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arauco_War\" title=\"Arauco War\">Arauco War</a>: A Spanish army <a href=\"https://wikipedia.org/wiki/Battle_of_R%C3%ADo_Bueno\" class=\"mw-redirect\" title=\"Battle of Río Bueno\">is defeated</a> by local <a href=\"https://wikipedia.org/wiki/Huilliche_people\" title=\"Huilliche people\">Mapuche-Huilliches</a> as it tries to cross <a href=\"https://wikipedia.org/wiki/Bueno_River\" title=\"Bueno River\">Bueno River</a> in <a href=\"https://wikipedia.org/wiki/Zona_Sur\" title=\"Zona Sur\">Southern Chile</a>.", "links": [{"title": "Arauco War", "link": "https://wikipedia.org/wiki/Arauco_War"}, {"title": "Battle of Río Bueno", "link": "https://wikipedia.org/wiki/Battle_of_R%C3%ADo_Bueno"}, {"title": "Huilliche people", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_people"}, {"title": "Bueno River", "link": "https://wikipedia.org/wiki/Bueno_River"}, {"title": "Zona Sur", "link": "https://wikipedia.org/wiki/Zona_Sur"}]}, {"year": "1759", "text": "The first American life insurance company, the Corporation for Relief of Poor and Distressed Presbyterian Ministers and of the Poor and Distressed Widows and Children of the Presbyterian Ministers (now part of Unum Group), is incorporated in Philadelphia, Pennsylvania.", "html": "1759 - The first American <a href=\"https://wikipedia.org/wiki/Life_insurance\" title=\"Life insurance\">life insurance</a> company, the Corporation for Relief of Poor and Distressed Presbyterian Ministers and of the Poor and Distressed Widows and Children of the Presbyterian Ministers (now part of <a href=\"https://wikipedia.org/wiki/Unum\" title=\"Unum\">Unum Group</a>), is incorporated in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia, Pennsylvania</a>.", "no_year_html": "The first American <a href=\"https://wikipedia.org/wiki/Life_insurance\" title=\"Life insurance\">life insurance</a> company, the Corporation for Relief of Poor and Distressed Presbyterian Ministers and of the Poor and Distressed Widows and Children of the Presbyterian Ministers (now part of <a href=\"https://wikipedia.org/wiki/Unum\" title=\"Unum\">Unum Group</a>), is incorporated in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia, Pennsylvania</a>.", "links": [{"title": "Life insurance", "link": "https://wikipedia.org/wiki/Life_insurance"}, {"title": "Unum", "link": "https://wikipedia.org/wiki/Unum"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1779", "text": "<PERSON><PERSON><PERSON><PERSON> is crowned King of Manipur.", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ching<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_Meitei_kings\" class=\"mw-redirect\" title=\"List of Meitei kings\">King</a> of <a href=\"https://wikipedia.org/wiki/Manipur\" title=\"Manipur\">Manipur</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_Meitei_kings\" class=\"mw-redirect\" title=\"List of Meitei kings\">King</a> of <a href=\"https://wikipedia.org/wiki/Manipur\" title=\"Manipur\">Manipur</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "List of Meitei kings", "link": "https://wikipedia.org/wiki/List_of_Meitei_kings"}, {"title": "Manipur", "link": "https://wikipedia.org/wiki/Manipur"}]}, {"year": "1787", "text": "<PERSON> discovers Titania and O<PERSON>on, two moons of Uranus.", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Timeline_of_discovery_of_Solar_System_planets_and_their_moons\" title=\"Timeline of discovery of Solar System planets and their moons\">discovers</a> <a href=\"https://wikipedia.org/wiki/Titania_(moon)\" title=\"Titania (moon)\">Titania</a> and <a href=\"https://wikipedia.org/wiki/Oberon_(moon)\" title=\"Oberon (moon)\">Oberon</a>, two <a href=\"https://wikipedia.org/wiki/Moons_of_Uranus\" title=\"Moons of Uranus\">moons</a> of <a href=\"https://wikipedia.org/wiki/Uranus\" title=\"Uranus\">Uranus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Timeline_of_discovery_of_Solar_System_planets_and_their_moons\" title=\"Timeline of discovery of Solar System planets and their moons\">discovers</a> <a href=\"https://wikipedia.org/wiki/Titania_(moon)\" title=\"Titania (moon)\">Titania</a> and <a href=\"https://wikipedia.org/wiki/Oberon_(moon)\" title=\"Oberon (moon)\">Oberon</a>, two <a href=\"https://wikipedia.org/wiki/Moons_of_Uranus\" title=\"Moons of Uranus\">moons</a> of <a href=\"https://wikipedia.org/wiki/Uranus\" title=\"Uranus\">Uranus</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Timeline of discovery of Solar System planets and their moons", "link": "https://wikipedia.org/wiki/Timeline_of_discovery_of_Solar_System_planets_and_their_moons"}, {"title": "Titania (moon)", "link": "https://wikipedia.org/wiki/Titania_(moon)"}, {"title": "Oberon (moon)", "link": "https://wikipedia.org/wiki/Oberon_(moon)"}, {"title": "Moons of Uranus", "link": "https://wikipedia.org/wiki/Moons_of_Uranus"}, {"title": "Uranus", "link": "https://wikipedia.org/wiki/Uranus"}]}, {"year": "1805", "text": "The Michigan Territory is created.", "html": "1805 - The <a href=\"https://wikipedia.org/wiki/Michigan_Territory\" title=\"Michigan Territory\">Michigan Territory</a> is created.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Michigan_Territory\" title=\"Michigan Territory\">Michigan Territory</a> is created.", "links": [{"title": "Michigan Territory", "link": "https://wikipedia.org/wiki/Michigan_Territory"}]}, {"year": "1820", "text": "The Great Savannah Fire of 1820 destroys over 400 buildings in Savannah, Georgia.", "html": "1820 - The <a href=\"https://wikipedia.org/wiki/Great_Savannah_Fire_of_1820\" title=\"Great Savannah Fire of 1820\">Great Savannah Fire of 1820</a> destroys over 400 buildings in <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Savannah_Fire_of_1820\" title=\"Great Savannah Fire of 1820\">Great Savannah Fire of 1820</a> destroys over 400 buildings in <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>.", "links": [{"title": "Great Savannah Fire of 1820", "link": "https://wikipedia.org/wiki/Great_Savannah_Fire_of_1820"}, {"title": "Savannah, Georgia", "link": "https://wikipedia.org/wiki/Savannah,_Georgia"}]}, {"year": "1851", "text": "Taiping Rebellion: <PERSON> proclaims the Taiping Heavenly Kingdom, starting the Jintian Uprising.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Taiping_Rebellion\" title=\"Taiping Rebellion\">Taiping Rebellion</a>: <a href=\"https://wikipedia.org/wiki/Hong_Xiuquan\" title=\"Hong Xiu<PERSON>n\"><PERSON></a> proclaims the <a href=\"https://wikipedia.org/wiki/Taiping_Heavenly_Kingdom\" title=\"Taiping Heavenly Kingdom\">Taiping Heavenly Kingdom</a>, starting the <a href=\"https://wikipedia.org/wiki/Jintian_Uprising\" title=\"Jintian Uprising\">Jintian Uprising</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taiping_Rebellion\" title=\"Taiping Rebellion\">Taiping Rebellion</a>: <a href=\"https://wikipedia.org/wiki/Hong_Xiuquan\" title=\"Hong Xi<PERSON>n\"><PERSON></a> proclaims the <a href=\"https://wikipedia.org/wiki/Taiping_Heavenly_Kingdom\" title=\"Taiping Heavenly Kingdom\">Taiping Heavenly Kingdom</a>, starting the <a href=\"https://wikipedia.org/wiki/Jintian_Uprising\" title=\"Jintian Uprising\">Jintian Uprising</a>.", "links": [{"title": "Taiping Rebellion", "link": "https://wikipedia.org/wiki/Taiping_Rebellion"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hong_<PERSON>"}, {"title": "Taiping Heavenly Kingdom", "link": "https://wikipedia.org/wiki/Taiping_Heavenly_Kingdom"}, {"title": "Jintian Uprising", "link": "https://wikipedia.org/wiki/Jintian_Uprising"}]}, {"year": "1861", "text": "American Civil War: Alabama secedes from the United States.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> secedes from the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> secedes from the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}, {"year": "1863", "text": "American Civil War: The three-day Battle of Arkansas Post concludes as General <PERSON> and Admiral <PERSON> capture Fort Hindman and secure control over the Arkansas River for the Union.", "html": "1863 - American Civil War: The three-day <a href=\"https://wikipedia.org/wiki/Battle_of_Arkansas_Post_(1863)\" title=\"Battle of Arkansas Post (1863)\">Battle of Arkansas Post</a> concludes as General <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> capture Fort Hindman and secure control over the <a href=\"https://wikipedia.org/wiki/Arkansas_River\" title=\"Arkansas River\">Arkansas River</a> for the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a>.", "no_year_html": "American Civil War: The three-day <a href=\"https://wikipedia.org/wiki/Battle_of_Arkansas_Post_(1863)\" title=\"Battle of Arkansas Post (1863)\">Battle of Arkansas Post</a> concludes as General <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> capture Fort Hindman and secure control over the <a href=\"https://wikipedia.org/wiki/Arkansas_River\" title=\"Arkansas River\">Arkansas River</a> for the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a>.", "links": [{"title": "Battle of Arkansas Post (1863)", "link": "https://wikipedia.org/wiki/Battle_of_Arkansas_Post_(1863)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Arkansas River", "link": "https://wikipedia.org/wiki/Arkansas_River"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}]}, {"year": "1863", "text": "American Civil War: CSS Alabama encounters and sinks the USS Hatteras off Galveston Lighthouse in Texas.", "html": "1863 - American Civil War: <a href=\"https://wikipedia.org/wiki/CSS_Alabama\" title=\"CSS Alabama\">CSS <i>Alabama</i></a> <a href=\"https://wikipedia.org/wiki/Action_off_Galveston_Light\" title=\"Action off Galveston Light\">encounters and sinks</a> the <a href=\"https://wikipedia.org/wiki/USS_Hatteras_(1861)\" title=\"USS Hatteras (1861)\">USS <i>Hatteras</i></a> off Galveston Lighthouse in <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/CSS_Alabama\" title=\"CSS Alabama\">CSS <i>Alabama</i></a> <a href=\"https://wikipedia.org/wiki/Action_off_Galveston_Light\" title=\"Action off Galveston Light\">encounters and sinks</a> the <a href=\"https://wikipedia.org/wiki/USS_Hatteras_(1861)\" title=\"USS Hatteras (1861)\">USS <i>Hatteras</i></a> off Galveston Lighthouse in <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a>.", "links": [{"title": "CSS Alabama", "link": "https://wikipedia.org/wiki/CSS_Alabama"}, {"title": "Action off Galveston Light", "link": "https://wikipedia.org/wiki/Action_off_Galveston_Light"}, {"title": "USS Hatteras (1861)", "link": "https://wikipedia.org/wiki/USS_Hatteras_(1861)"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}]}, {"year": "1879", "text": "The Anglo-Zulu War begins.", "html": "1879 - The <a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a> begins.", "links": [{"title": "Anglo-Zulu War", "link": "https://wikipedia.org/wiki/Anglo-Zulu_War"}]}, {"year": "1908", "text": "Grand Canyon National Monument is created.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Grand_Canyon_National_Park\" title=\"Grand Canyon National Park\">Grand Canyon National Monument</a> is created.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Canyon_National_Park\" title=\"Grand Canyon National Park\">Grand Canyon National Monument</a> is created.", "links": [{"title": "Grand Canyon National Park", "link": "https://wikipedia.org/wiki/Grand_Canyon_National_Park"}]}, {"year": "1912", "text": "Immigrant textile workers in Lawrence, Massachusetts, go on strike when wages are reduced in response to a mandated shortening of the work week.", "html": "1912 - Immigrant textile workers in <a href=\"https://wikipedia.org/wiki/Lawrence,_Massachusetts\" title=\"Lawrence, Massachusetts\">Lawrence, Massachusetts</a>, <a href=\"https://wikipedia.org/wiki/1912_Lawrence_Textile_Strike\" class=\"mw-redirect\" title=\"1912 Lawrence Textile Strike\">go on strike</a> when wages are reduced in response to a mandated shortening of the work week.", "no_year_html": "Immigrant textile workers in <a href=\"https://wikipedia.org/wiki/Lawrence,_Massachusetts\" title=\"Lawrence, Massachusetts\">Lawrence, Massachusetts</a>, <a href=\"https://wikipedia.org/wiki/1912_Lawrence_Textile_Strike\" class=\"mw-redirect\" title=\"1912 Lawrence Textile Strike\">go on strike</a> when wages are reduced in response to a mandated shortening of the work week.", "links": [{"title": "Lawrence, Massachusetts", "link": "https://wikipedia.org/wiki/Lawrence,_Massachusetts"}, {"title": "1912 Lawrence Textile Strike", "link": "https://wikipedia.org/wiki/1912_<PERSON>_Textile_Strike"}]}, {"year": "1914", "text": "The Karluk, flagship of the Canadian Arctic Expedition, sinks after being crushed by ice.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/HMCS_Karluk\" class=\"mw-redirect\" title=\"HMCS Karluk\"><i><PERSON><PERSON></i></a>, flagship of the <a href=\"https://wikipedia.org/wiki/Canadian_Arctic_Expedition,_1913%E2%80%931916\" title=\"Canadian Arctic Expedition, 1913-1916\">Canadian Arctic Expedition</a>, <a href=\"https://wikipedia.org/wiki/Last_voyage_of_the_Karluk\" title=\"Last voyage of the Karluk\">sinks after being crushed by ice</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/HMCS_Karluk\" class=\"mw-redirect\" title=\"HMCS Karluk\"><i><PERSON><PERSON></i></a>, flagship of the <a href=\"https://wikipedia.org/wiki/Canadian_Arctic_Expedition,_1913%E2%80%931916\" title=\"Canadian Arctic Expedition, 1913-1916\">Canadian Arctic Expedition</a>, <a href=\"https://wikipedia.org/wiki/Last_voyage_of_the_Karluk\" title=\"Last voyage of the Karluk\">sinks after being crushed by ice</a>.", "links": [{"title": "HMCS Karluk", "link": "https://wikipedia.org/wiki/HMCS_Karluk"}, {"title": "Canadian Arctic Expedition, 1913-1916", "link": "https://wikipedia.org/wiki/Canadian_Arctic_Expedition,_1913%E2%80%931916"}, {"title": "Last voyage of the Karluk", "link": "https://wikipedia.org/wiki/Last_voyage_of_the_Karluk"}]}, {"year": "1917", "text": "The Kingsland munitions factory explosion occurs as a result of sabotage.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/Kingsland_Explosion\" class=\"mw-redirect\" title=\"Kingsland Explosion\">Kingsland munitions factory explosion</a> occurs as a result of <a href=\"https://wikipedia.org/wiki/Sabotage\" title=\"Sabotage\">sabotage</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kingsland_Explosion\" class=\"mw-redirect\" title=\"Kingsland Explosion\">Kingsland munitions factory explosion</a> occurs as a result of <a href=\"https://wikipedia.org/wiki/Sabotage\" title=\"Sabotage\">sabotage</a>.", "links": [{"title": "Kingsland Explosion", "link": "https://wikipedia.org/wiki/Kingsland_Explosion"}, {"title": "Sabotage", "link": "https://wikipedia.org/wiki/Sabotage"}]}, {"year": "1922", "text": "<PERSON> becomes the first person to be injected with insulin.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diabetic)\" title=\"<PERSON> (diabetic)\"><PERSON></a> becomes the first person to be injected with insulin.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diabetic)\" title=\"<PERSON> (diabetic)\"><PERSON></a> becomes the first person to be injected with insulin.", "links": [{"title": "<PERSON> (diabetic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diabetic)"}]}, {"year": "1923", "text": "Occupation of the Ruhr: Troops from France and Belgium occupy the Ruhr area to force Germany to make its World War I reparation payments.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Occupation_of_the_Ruhr\" title=\"Occupation of the Ruhr\">Occupation of the Ruhr</a>: Troops from <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> and <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a> occupy the <a href=\"https://wikipedia.org/wiki/Ruhr\" title=\"Ruhr\">Ruhr</a> area to force <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Germany</a> to make its <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a> reparation payments.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Occupation_of_the_Ruhr\" title=\"Occupation of the Ruhr\">Occupation of the Ruhr</a>: Troops from <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> and <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a> occupy the <a href=\"https://wikipedia.org/wiki/Ruhr\" title=\"Ruhr\">Ruhr</a> area to force <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Germany</a> to make its <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a> reparation payments.", "links": [{"title": "Occupation of the Ruhr", "link": "https://wikipedia.org/wiki/Occupation_of_the_Ruhr"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "Belgium", "link": "https://wikipedia.org/wiki/Belgium"}, {"title": "Ruhr", "link": "https://wikipedia.org/wiki/Ruhr"}, {"title": "Germany", "link": "https://wikipedia.org/wiki/Germany"}, {"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}]}, {"year": "1927", "text": "<PERSON>, head of film studio Metro-Goldwyn-Mayer (MGM), announces the creation of the Academy of Motion Picture Arts and Sciences, at a banquet in Los Angeles, California.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, head of film studio <a href=\"https://wikipedia.org/wiki/Metro-Goldwyn-Mayer\" title=\"Metro-Goldwyn-Mayer\">Metro-Goldwyn-Mayer</a> (MGM), announces the creation of the <a href=\"https://wikipedia.org/wiki/Academy_of_Motion_Picture_Arts_and_Sciences\" title=\"Academy of Motion Picture Arts and Sciences\">Academy of Motion Picture Arts and Sciences</a>, at a banquet in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, head of film studio <a href=\"https://wikipedia.org/wiki/Metro-Goldwyn-Mayer\" title=\"Metro-Goldwyn-Mayer\">Metro-Goldwyn-Mayer</a> (MGM), announces the creation of the <a href=\"https://wikipedia.org/wiki/Academy_of_Motion_Picture_Arts_and_Sciences\" title=\"Academy of Motion Picture Arts and Sciences\">Academy of Motion Picture Arts and Sciences</a>, at a banquet in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles, California</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Metro-Goldwyn-Mayer", "link": "https://wikipedia.org/wiki/Metro-Goldwyn-Mayer"}, {"title": "Academy of Motion Picture Arts and Sciences", "link": "https://wikipedia.org/wiki/Academy_of_Motion_Picture_Arts_and_Sciences"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}]}, {"year": "1935", "text": "<PERSON> becomes the first person to fly solo from Hawaii to California.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to fly solo from <a href=\"https://wikipedia.org/wiki/Hawaii\" title=\"Hawaii\">Hawaii</a> to <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to fly solo from <a href=\"https://wikipedia.org/wiki/Hawaii\" title=\"Hawaii\">Hawaii</a> to <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hawaii", "link": "https://wikipedia.org/wiki/Hawaii"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "1942", "text": "World War II: Japanese forces capture Kuala Lumpur, the capital of the Federated Malay States.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Kuala_Lumpur\" title=\"Battle of Kuala Lumpur\">capture Kuala Lumpur</a>, the capital of the Federated Malay States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Kuala_Lumpur\" title=\"Battle of Kuala Lumpur\">capture Kuala Lumpur</a>, the capital of the Federated Malay States.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Battle of Kuala Lumpur", "link": "https://wikipedia.org/wiki/Battle_of_Kuala_Lumpur"}]}, {"year": "1942", "text": "World War II: Japanese forces attack Tarakan in Borneo, Netherlands Indies (Battle of Tarakan)", "html": "1942 - World War II: Japanese forces attack <a href=\"https://wikipedia.org/wiki/Tarakan_Island\" class=\"mw-redirect\" title=\"Tarakan Island\">Tarakan</a> in <a href=\"https://wikipedia.org/wiki/Dutch_Borneo\" class=\"mw-redirect\" title=\"Dutch Borneo\">Borneo</a>, <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Netherlands Indies</a> (<a href=\"https://wikipedia.org/wiki/Battle_of_Tarakan_(1942)\" title=\"Battle of Tarakan (1942)\">Battle of Tarakan</a>)", "no_year_html": "World War II: Japanese forces attack <a href=\"https://wikipedia.org/wiki/Tarakan_Island\" class=\"mw-redirect\" title=\"Tarakan Island\">Tarakan</a> in <a href=\"https://wikipedia.org/wiki/Dutch_Borneo\" class=\"mw-redirect\" title=\"Dutch Borneo\">Borneo</a>, <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Netherlands Indies</a> (<a href=\"https://wikipedia.org/wiki/Battle_of_Tarakan_(1942)\" title=\"Battle of Tarakan (1942)\">Battle of Tarakan</a>)", "links": [{"title": "Tarakan Island", "link": "https://wikipedia.org/wiki/Tarakan_Island"}, {"title": "Dutch Borneo", "link": "https://wikipedia.org/wiki/Dutch_Borneo"}, {"title": "Dutch East Indies", "link": "https://wikipedia.org/wiki/Dutch_East_Indies"}, {"title": "Battle of Tarakan (1942)", "link": "https://wikipedia.org/wiki/Battle_of_Tarakan_(1942)"}]}, {"year": "1943", "text": "The Republic of China agrees to the Sino-British New Equal Treaty and the Sino-American New Equal Treaty.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic of China</a> agrees to the <a href=\"https://wikipedia.org/wiki/Sino-British_Treaty_for_the_Relinquishment_of_Extra-Territorial_Rights_in_China\" title=\"Sino-British Treaty for the Relinquishment of Extra-Territorial Rights in China\">Sino-British New Equal Treaty</a> and the <a href=\"https://wikipedia.org/wiki/Sino-American_Treaty_for_the_Relinquishment_of_Extraterritorial_Rights_in_China\" class=\"mw-redirect\" title=\"Sino-American Treaty for the Relinquishment of Extraterritorial Rights in China\">Sino-American New Equal Treaty</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic of China</a> agrees to the <a href=\"https://wikipedia.org/wiki/Sino-British_Treaty_for_the_Relinquishment_of_Extra-Territorial_Rights_in_China\" title=\"Sino-British Treaty for the Relinquishment of Extra-Territorial Rights in China\">Sino-British New Equal Treaty</a> and the <a href=\"https://wikipedia.org/wiki/Sino-American_Treaty_for_the_Relinquishment_of_Extraterritorial_Rights_in_China\" class=\"mw-redirect\" title=\"Sino-American Treaty for the Relinquishment of Extraterritorial Rights in China\">Sino-American New Equal Treaty</a>.", "links": [{"title": "Republic of China (1912-1949)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)"}, {"title": "Sino-British Treaty for the Relinquishment of Extra-Territorial Rights in China", "link": "https://wikipedia.org/wiki/Sino-British_Treaty_for_the_Relinquishment_of_Extra-Territorial_Rights_in_China"}, {"title": "Sino-American Treaty for the Relinquishment of Extraterritorial Rights in China", "link": "https://wikipedia.org/wiki/Sino-American_Treaty_for_the_Relinquishment_of_Extraterritorial_Rights_in_China"}]}, {"year": "1943", "text": "Italian-American anarchist <PERSON> is assassinated in New York City.", "html": "1943 - Italian-American anarchist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "no_year_html": "Italian-American anarchist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "links": [{"title": "Carlo <PERSON>", "link": "https://wikipedia.org/wiki/Carlo_<PERSON>"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Secretary General of the Communist Party of Albania, declares the People's Republic of Albania with himself as head of state.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Enver_Ho<PERSON>\" title=\"Enver Ho<PERSON>\"><PERSON><PERSON></a>, Secretary General of the <a href=\"https://wikipedia.org/wiki/Party_of_Labour_of_Albania\" title=\"Party of Labour of Albania\">Communist Party of Albania</a>, declares the <a href=\"https://wikipedia.org/wiki/People%27s_Socialist_Republic_of_Albania\" title=\"People's Socialist Republic of Albania\">People's Republic of Albania</a> with himself as head of state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Enver_Ho<PERSON>\" title=\"Enver Ho<PERSON>\"><PERSON><PERSON></a>, Secretary General of the <a href=\"https://wikipedia.org/wiki/Party_of_Labour_of_Albania\" title=\"Party of Labour of Albania\">Communist Party of Albania</a>, declares the <a href=\"https://wikipedia.org/wiki/People%27s_Socialist_Republic_of_Albania\" title=\"People's Socialist Republic of Albania\">People's Republic of Albania</a> with himself as head of state.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enver_<PERSON>xha"}, {"title": "Party of Labour of Albania", "link": "https://wikipedia.org/wiki/Party_of_Labour_of_Albania"}, {"title": "People's Socialist Republic of Albania", "link": "https://wikipedia.org/wiki/People%27s_Socialist_Republic_of_Albania"}]}, {"year": "1949", "text": "The first \"networked\" television broadcasts took place as KDKA-TV in Pittsburgh, Pennsylvania goes on the air connecting the east coast and mid-west programming.", "html": "1949 - The first \"networked\" television broadcasts took place as <a href=\"https://wikipedia.org/wiki/KDKA-TV\" title=\"KDKA-TV\">KDKA-TV</a> in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh, Pennsylvania</a> goes on the air connecting the east coast and mid-west programming.", "no_year_html": "The first \"networked\" television broadcasts took place as <a href=\"https://wikipedia.org/wiki/KDKA-TV\" title=\"KDKA-TV\">KDKA-TV</a> in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh, Pennsylvania</a> goes on the air connecting the east coast and mid-west programming.", "links": [{"title": "KDKA-TV", "link": "https://wikipedia.org/wiki/KDKA-TV"}, {"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}]}, {"year": "1957", "text": "The African Convention is founded in Dakar, Senegal.", "html": "1957 - The <a href=\"https://wikipedia.org/wiki/African_Convention\" title=\"African Convention\">African Convention</a> is founded in <a href=\"https://wikipedia.org/wiki/Dakar\" title=\"Dakar\">Dakar, Senegal</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/African_Convention\" title=\"African Convention\">African Convention</a> is founded in <a href=\"https://wikipedia.org/wiki/Dakar\" title=\"Dakar\">Dakar, Senegal</a>.", "links": [{"title": "African Convention", "link": "https://wikipedia.org/wiki/African_Convention"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>kar"}]}, {"year": "1959", "text": "36 people are killed when Lufthansa Flight 502 crashes on approach to Rio de Janeiro/Galeão International Airport in Brazil.", "html": "1959 - 36 people are killed when <a href=\"https://wikipedia.org/wiki/Lufthansa_Flight_502\" title=\"Lufthansa Flight 502\">Lufthansa Flight 502</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Rio_de_Janeiro/Gale%C3%A3o_International_Airport\" title=\"Rio de Janeiro/Galeão International Airport\">Rio de Janeiro/Galeão International Airport</a> in Brazil.", "no_year_html": "36 people are killed when <a href=\"https://wikipedia.org/wiki/Lufthansa_Flight_502\" title=\"Lufthansa Flight 502\">Lufthansa Flight 502</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Rio_de_Janeiro/Gale%C3%A3o_International_Airport\" title=\"Rio de Janeiro/Galeão International Airport\">Rio de Janeiro/Galeão International Airport</a> in Brazil.", "links": [{"title": "Lufthansa Flight 502", "link": "https://wikipedia.org/wiki/Lufthansa_Flight_502"}, {"title": "Rio de Janeiro/Galeão International Airport", "link": "https://wikipedia.org/wiki/Rio_de_Janeiro/Gale%C3%A3o_International_Airport"}]}, {"year": "1961", "text": "Throgs Neck Bridge over the East River, linking New York City's boroughs of The Bronx and Queens, opens to road traffic.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Throgs_Neck_Bridge\" title=\"Throgs Neck Bridge\">Throgs Neck Bridge</a> over the <a href=\"https://wikipedia.org/wiki/East_River\" title=\"East River\">East River</a>, linking <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>'s boroughs of <a href=\"https://wikipedia.org/wiki/The_Bronx\" title=\"The Bronx\">The Bronx</a> and <a href=\"https://wikipedia.org/wiki/Queens\" title=\"Queens\">Queens</a>, opens to road traffic.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Throgs_Neck_Bridge\" title=\"Throgs Neck Bridge\">Throgs Neck Bridge</a> over the <a href=\"https://wikipedia.org/wiki/East_River\" title=\"East River\">East River</a>, linking <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>'s boroughs of <a href=\"https://wikipedia.org/wiki/The_Bronx\" title=\"The Bronx\">The Bronx</a> and <a href=\"https://wikipedia.org/wiki/Queens\" title=\"Queens\">Queens</a>, opens to road traffic.", "links": [{"title": "Throgs Neck Bridge", "link": "https://wikipedia.org/wiki/Throgs_Neck_Bridge"}, {"title": "East River", "link": "https://wikipedia.org/wiki/East_River"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}, {"title": "The Bronx", "link": "https://wikipedia.org/wiki/The_Bronx"}, {"title": "Queens", "link": "https://wikipedia.org/wiki/Queens"}]}, {"year": "1962", "text": "Cold War: While tied to its pier in Polyarny, the Soviet submarine B-37 is destroyed when fire breaks out in its torpedo compartment.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: While tied to its pier in <a href=\"https://wikipedia.org/wiki/Russian_Shipyard_Number_10\" title=\"Russian Shipyard Number 10\">Polyarny</a>, the <a href=\"https://wikipedia.org/wiki/Soviet_submarine_B-37\" title=\"Soviet submarine B-37\">Soviet submarine B-37</a> is destroyed when fire breaks out in its torpedo compartment.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: While tied to its pier in <a href=\"https://wikipedia.org/wiki/Russian_Shipyard_Number_10\" title=\"Russian Shipyard Number 10\">Polyarny</a>, the <a href=\"https://wikipedia.org/wiki/Soviet_submarine_B-37\" title=\"Soviet submarine B-37\">Soviet submarine B-37</a> is destroyed when fire breaks out in its torpedo compartment.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Russian Shipyard Number 10", "link": "https://wikipedia.org/wiki/Russian_Shipyard_Number_10"}, {"title": "Soviet submarine B-37", "link": "https://wikipedia.org/wiki/Soviet_submarine_B-37"}]}, {"year": "1962", "text": "An avalanche on Huascarán in Peru causes around 4,000 deaths.", "html": "1962 - An avalanche on <a href=\"https://wikipedia.org/wiki/Huascar%C3%A1n\" title=\"Huascarán\">Huascarán</a> in <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> causes around 4,000 deaths.", "no_year_html": "An avalanche on <a href=\"https://wikipedia.org/wiki/Huascar%C3%A1n\" title=\"Huascarán\">Huascarán</a> in <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> causes around 4,000 deaths.", "links": [{"title": "Huascarán", "link": "https://wikipedia.org/wiki/Huascar%C3%A1n"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}]}, {"year": "1964", "text": "Surgeon General of the United States Dr. <PERSON>, M.D., publishes the landmark report Smoking and Health: Report of the Advisory Committee to the Surgeon General of the Public Health Service saying that smoking may be hazardous to health, sparking national and worldwide anti-smoking efforts.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a> Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, M.D., publishes the landmark report <i><a href=\"https://wikipedia.org/wiki/Smoking_and_Health:_Report_of_the_Advisory_Committee_to_the_Surgeon_General_of_the_Public_Health_Service\" class=\"mw-redirect\" title=\"Smoking and Health: Report of the Advisory Committee to the Surgeon General of the Public Health Service\">Smoking and Health: Report of the Advisory Committee to the Surgeon General of the Public Health Service</a></i> saying that smoking may be hazardous to health, sparking national and worldwide anti-smoking efforts.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a> Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, M.D., publishes the landmark report <i><a href=\"https://wikipedia.org/wiki/Smoking_and_Health:_Report_of_the_Advisory_Committee_to_the_Surgeon_General_of_the_Public_Health_Service\" class=\"mw-redirect\" title=\"Smoking and Health: Report of the Advisory Committee to the Surgeon General of the Public Health Service\">Smoking and Health: Report of the Advisory Committee to the Surgeon General of the Public Health Service</a></i> saying that smoking may be hazardous to health, sparking national and worldwide anti-smoking efforts.", "links": [{"title": "Surgeon General of the United States", "link": "https://wikipedia.org/wiki/Surgeon_General_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Smoking and Health: Report of the Advisory Committee to the Surgeon General of the Public Health Service", "link": "https://wikipedia.org/wiki/Smoking_and_Health:_Report_of_the_Advisory_Committee_to_the_Surgeon_General_of_the_Public_Health_Service"}]}, {"year": "1966", "text": "The Tbilisi Metro is opened.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Tbilisi_Metro\" title=\"Tbilisi Metro\">Tbilisi Metro</a> is opened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tbilisi_Metro\" title=\"Tbilisi Metro\">Tbilisi Metro</a> is opened.", "links": [{"title": "Tbilisi Metro", "link": "https://wikipedia.org/wiki/Tbilisi_Metro"}]}, {"year": "1972", "text": "East Pakistan renames itself Bangladesh.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a> renames itself <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a> renames itself <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "links": [{"title": "East Pakistan", "link": "https://wikipedia.org/wiki/East_Pakistan"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "1973", "text": "Major League Baseball owners vote in approval of the American League adopting the designated hitter position.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> owners vote in approval of the <a href=\"https://wikipedia.org/wiki/American_League\" title=\"American League\">American League</a> adopting the <a href=\"https://wikipedia.org/wiki/Designated_hitter\" title=\"Designated hitter\">designated hitter</a> position.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> owners vote in approval of the <a href=\"https://wikipedia.org/wiki/American_League\" title=\"American League\">American League</a> adopting the <a href=\"https://wikipedia.org/wiki/Designated_hitter\" title=\"Designated hitter\">designated hitter</a> position.", "links": [{"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}, {"title": "American League", "link": "https://wikipedia.org/wiki/American_League"}, {"title": "Designated hitter", "link": "https://wikipedia.org/wiki/Designated_hitter"}]}, {"year": "1983", "text": "United Airlines Flight 2885 crashes after takeoff from Detroit Metropolitan Airport, killing three.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_2885\" title=\"United Airlines Flight 2885\">United Airlines Flight 2885</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Detroit_Metropolitan_Airport\" title=\"Detroit Metropolitan Airport\">Detroit Metropolitan Airport</a>, killing three.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_2885\" title=\"United Airlines Flight 2885\">United Airlines Flight 2885</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Detroit_Metropolitan_Airport\" title=\"Detroit Metropolitan Airport\">Detroit Metropolitan Airport</a>, killing three.", "links": [{"title": "United Airlines Flight 2885", "link": "https://wikipedia.org/wiki/United_Airlines_Flight_2885"}, {"title": "Detroit Metropolitan Airport", "link": "https://wikipedia.org/wiki/Detroit_Metropolitan_Airport"}]}, {"year": "1986", "text": "The Gateway Bridge, Brisbane, Queensland, Australia is officially opened.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>_<PERSON>\" title=\"Sir <PERSON>\">Gateway Bridge, Brisbane, Queensland, Australia</a> is officially opened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>_<PERSON>\" title=\"Sir <PERSON>\">Gateway Bridge, Brisbane, Queensland, Australia</a> is officially opened.", "links": [{"title": "Sir <PERSON>", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "The Irish Government announces the end of a 15-year broadcasting ban on the IRA and its political arm Sinn Féin.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Irish</a> Government announces the end of a 15-year broadcasting ban on the <a href=\"https://wikipedia.org/wiki/Official_Irish_Republican_Army\" title=\"Official Irish Republican Army\">IRA</a> and its political arm <a href=\"https://wikipedia.org/wiki/Sinn_F%C3%A9in\" title=\"Sinn Féin\">Sinn Féin</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Irish</a> Government announces the end of a 15-year broadcasting ban on the <a href=\"https://wikipedia.org/wiki/Official_Irish_Republican_Army\" title=\"Official Irish Republican Army\">IRA</a> and its political arm <a href=\"https://wikipedia.org/wiki/Sinn_F%C3%A9in\" title=\"Sinn Féin\">Sinn Féin</a>.", "links": [{"title": "Republic of Ireland", "link": "https://wikipedia.org/wiki/Republic_of_Ireland"}, {"title": "Official Irish Republican Army", "link": "https://wikipedia.org/wiki/Official_Irish_Republican_Army"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sinn_F%C3%A9in"}]}, {"year": "1995", "text": "51 people are killed in a plane crash in María La Baja, Colombia.", "html": "1995 - 51 people are killed in a <a href=\"https://wikipedia.org/wiki/Intercontinental_de_Aviaci%C3%B3n_Flight_256\" title=\"Intercontinental de Aviación Flight 256\">plane crash</a> in <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_La_Baja\" title=\"María La Baja\">María La Baja</a>, Colombia.", "no_year_html": "51 people are killed in a <a href=\"https://wikipedia.org/wiki/Intercontinental_de_Aviaci%C3%B3n_Flight_256\" title=\"Intercontinental de Aviación Flight 256\">plane crash</a> in <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_La_Baja\" title=\"María La Baja\">María La Baja</a>, Colombia.", "links": [{"title": "Intercontinental de Aviación Flight 256", "link": "https://wikipedia.org/wiki/Intercontinental_de_Aviaci%C3%B3n_Flight_256"}, {"title": "María La Baja", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_La_Baja"}]}, {"year": "1996", "text": "Space Shuttle program: STS-72 launches from the Kennedy Space Center marking the start of the 74th Space Shuttle mission and the 10th flight of Endeavour.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-72\" title=\"STS-72\">STS-72</a> launches from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> marking the start of the 74th <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> mission and the 10th flight of <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Endeavour</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-72\" title=\"STS-72\">STS-72</a> launches from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> marking the start of the 74th <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> mission and the 10th flight of <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Endeavour</a></i>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-72", "link": "https://wikipedia.org/wiki/STS-72"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}, {"title": "Space Shuttle", "link": "https://wikipedia.org/wiki/Space_Shuttle"}, {"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}]}, {"year": "1998", "text": "Over 100 people are killed in the Sidi-Hamed massacre in Algeria.", "html": "1998 - Over 100 people are killed in the <a href=\"https://wikipedia.org/wiki/Sid<PERSON>-<PERSON><PERSON>_massacre\" title=\"Sidi-Hamed massacre\">Sidi-Hamed massacre</a> in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "no_year_html": "Over 100 people are killed in the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_massacre\" title=\"Sidi-Hamed massacre\">Sidi-Hamed massacre</a> in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "links": [{"title": "Sidi-<PERSON>ed massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_massacre"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "2003", "text": "Illinois Governor <PERSON> commutes the death sentences of 167 prisoners on Illinois's death row based on the <PERSON> scandal.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Illinois Governor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> commutes the death sentences of 167 prisoners on <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>'s death row based on the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> scandal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Illinois Governor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> commutes the death sentences of 167 prisoners on <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>'s death row based on the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> scandal.", "links": [{"title": "Governor of Illinois", "link": "https://wikipedia.org/wiki/Governor_of_Illinois"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "One French soldier and 17 militants are killed in a failed attempt to free a French hostage in Bulo Marer, Somalia.", "html": "2013 - One French soldier and 17 militants are killed in a <a href=\"https://wikipedia.org/wiki/Bulo_Marer_hostage_rescue_attempt\" title=\"Bulo Marer hostage rescue attempt\">failed attempt</a> to free a French hostage in <a href=\"https://wikipedia.org/wiki/Bulo_Marer\" class=\"mw-redirect\" title=\"Bulo Marer\">Bulo Marer</a>, <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>.", "no_year_html": "One French soldier and 17 militants are killed in a <a href=\"https://wikipedia.org/wiki/Bulo_Marer_hostage_rescue_attempt\" title=\"Bulo Marer hostage rescue attempt\">failed attempt</a> to free a French hostage in <a href=\"https://wikipedia.org/wiki/Bulo_Marer\" class=\"mw-redirect\" title=\"Bulo Marer\">Bulo Marer</a>, <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>.", "links": [{"title": "<PERSON><PERSON>r hostage rescue attempt", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_hostage_rescue_attempt"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r"}, {"title": "Somalia", "link": "https://wikipedia.org/wiki/Somalia"}]}, {"year": "2020", "text": "COVID-19 pandemic in Hubei: Municipal health officials in Wuhan announce the first recorded death from COVID-19.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_Hubei\" title=\"COVID-19 pandemic in Hubei\">COVID-19 pandemic in Hubei</a>: Municipal health officials in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> announce the first recorded death from <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_Hubei\" title=\"COVID-19 pandemic in Hubei\">COVID-19 pandemic in Hubei</a>: Municipal health officials in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> announce the first recorded death from <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a>.", "links": [{"title": "COVID-19 pandemic in Hubei", "link": "https://wikipedia.org/wiki/COVID-19_pandemic_in_Hubei"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>han"}, {"title": "COVID-19", "link": "https://wikipedia.org/wiki/COVID-19"}]}], "Births": [{"year": "347", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 395)", "html": "347 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON> I</a>, Roman emperor (d. 395)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON>ius I\"><PERSON><PERSON><PERSON> I</a>, Roman emperor (d. 395)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}]}, {"year": "889", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, first <PERSON><PERSON><PERSON> of Córdoba (d. 961)", "html": "889 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_III\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III</a>, first <a href=\"https://wikipedia.org/wiki/Caliph_of_C%C3%B3rdoba\" class=\"mw-redirect\" title=\"Caliph of Córdoba\">Caliph of Córdoba</a> (d. 961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_III\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III</a>, first <a href=\"https://wikipedia.org/wiki/Caliph_of_C%C3%B3rdoba\" class=\"mw-redirect\" title=\"Caliph of Córdoba\">Caliph of Córdoba</a> (d. 961)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Caliph of Córdoba", "link": "https://wikipedia.org/wiki/Caliph_of_C%C3%B3rdoba"}]}, {"year": "1113", "text": "<PERSON>, Chinese religious leader and poet (d. 1170)", "html": "1113 - <a href=\"https://wikipedia.org/wiki/Wang_<PERSON>\" title=\"Wang Chongyang\"><PERSON></a>, Chinese religious leader and poet (d. 1170)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wang_<PERSON>\" title=\"Wang Cho<PERSON>yang\"><PERSON></a>, Chinese religious leader and poet (d. 1170)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wang_Cho<PERSON>yang"}]}, {"year": "1209", "text": "<PERSON><PERSON><PERSON><PERSON>, Mongolian emperor (d. 1259)", "html": "1209 - <a href=\"https://wikipedia.org/wiki/M%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian emperor (d. 1259)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian emperor (d. 1259)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%B6ng<PERSON>_Khan"}]}, {"year": "1322", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (d. 1380)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dmy%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1380)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dmy%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1380)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_K%C5%8Dmy%C5%8D"}]}, {"year": "1359", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (d. 1393)", "html": "1359 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-En%27y%C5%AB\" title=\"Emperor <PERSON>-En'yū\">Emperor <PERSON><PERSON><PERSON>'yū</a> of Japan (d. 1393)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-En%27y%C5%AB\" title=\"Emperor <PERSON>-En'yū\">Emperor <PERSON><PERSON>En'yū</a> of Japan (d. 1393)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-En%27y%C5%AB"}]}, {"year": "1395", "text": "<PERSON> of Valois, daughter of <PERSON> of France (d. 1422)", "html": "1395 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Valois\" class=\"mw-redirect\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, daughter of <PERSON> of France (d. 1422)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Valois\" class=\"mw-redirect\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, daughter of <PERSON> of France (d. 1422)", "links": [{"title": "<PERSON> of Valois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1503", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian artist (d. 1540)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/Parmigianino\" title=\"Parm<PERSON>ani<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian artist (d. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Parm<PERSON>anino\" title=\"Parm<PERSON>ani<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian artist (d. 1540)", "links": [{"title": "Parmigianino", "link": "https://wikipedia.org/wiki/Parmigianino"}]}, {"year": "1589", "text": "<PERSON>, English politician (d. 1666)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(of_Barrington)\" title=\"<PERSON> (of Barrington)\"><PERSON></a>, English politician (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(of_Barrington)\" title=\"<PERSON> (of Barrington)\"><PERSON></a>, English politician (d. 1666)", "links": [{"title": "<PERSON> (of Barrington)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(of_Barrington)"}]}, {"year": "1591", "text": "<PERSON>, 3rd Earl of Essex, English general and politician, Lord Lieutenant of Staffordshire (d. 1646)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Essex\" title=\"<PERSON>, 3rd Earl of Essex\"><PERSON>, 3rd Earl of Essex</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Staffordshire\" title=\"Lord Lieutenant of Staffordshire\">Lord Lieutenant of Staffordshire</a> (d. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Essex\" title=\"<PERSON>, 3rd Earl of Essex\"><PERSON>, 3rd Earl of Essex</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Staffordshire\" title=\"Lord Lieutenant of Staffordshire\">Lord Lieutenant of Staffordshire</a> (d. 1646)", "links": [{"title": "<PERSON>, 3rd Earl of Essex", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Essex"}, {"title": "Lord Lieutenant of Staffordshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Staffordshire"}]}, {"year": "1624", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch painter (d. 1680)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/Bast<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Bast<PERSON><PERSON> <PERSON><PERSON>\"><PERSON>st<PERSON><PERSON> <PERSON><PERSON></a>, Dutch painter (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bast<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Bastia<PERSON> <PERSON><PERSON>\"><PERSON>st<PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Dutch painter (d. 1680)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1630", "text": "<PERSON>, English-American minister, physician, and academic (d. 1684)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Harvard)\" title=\"<PERSON> (Harvard)\"><PERSON></a>, English-American minister, physician, and academic (d. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Harvard)\" title=\"<PERSON> (Harvard)\"><PERSON></a>, English-American minister, physician, and academic (d. 1684)", "links": [{"title": "<PERSON> (Harvard)", "link": "https://wikipedia.org/wiki/<PERSON>_(Harvard)"}]}, {"year": "1638", "text": "<PERSON>, Danish bishop and anatomist (d. 1686)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish bishop and anatomist (d. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish bishop and anatomist (d. 1686)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1642", "text": "<PERSON>, German organist and composer (d. 1710)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1650", "text": "<PERSON>, Dutch-German painter (d. 1721)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-German painter (d. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-German painter (d. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1671", "text": "<PERSON><PERSON><PERSON>, 1st duc <PERSON>, French general and diplomat (d. 1745)", "html": "1671 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_1st_duc_de_Broglie\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, 1st duc de Broglie\"><PERSON><PERSON><PERSON>, 1st duc de Broglie</a>, French general and diplomat (d. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_1st_duc_de_Brog<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, 1st duc de Broglie\"><PERSON><PERSON><PERSON>, 1st duc de Broglie</a>, French general and diplomat (d. 1745)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st duc de B<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>-<PERSON>,_1st_duc_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1755", "text": "<PERSON>, Nevisian-American general, economist and politician, 1st United States Secretary of the Treasury (d. 1804)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nevisian-American general, economist and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nevisian-American general, economist and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1757", "text": "<PERSON>, English engineer and architect (d. 1831)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and architect (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and architect (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1760", "text": "<PERSON>, American lawyer and politician, 2nd United States Secretary of the Treasury, 24th Governor of Connecticut (d. 1833)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (d. 1833)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}, {"title": "Governor of Connecticut", "link": "https://wikipedia.org/wiki/Governor_of_Connecticut"}]}, {"year": "1777", "text": "<PERSON>, Maltese merchant and rebel leader (d. 1837)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese merchant and rebel leader (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese merchant and rebel leader (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "<PERSON>, English physicist (d. 1869)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>er"}]}, {"year": "1788", "text": "<PERSON>, English chemist and academic (d. 1866)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON><PERSON><PERSON>, Hungarian physicist and engineer (d. 1895)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/%C3%81ny<PERSON>_Jedlik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian physicist and engineer (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81nyos_Jedlik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian physicist and engineer (d. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81nyos_Jedlik"}]}, {"year": "1807", "text": "<PERSON>, American businessman and philanthropist, founded Western Union and Cornell University (d. 1874)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Western_Union\" title=\"Western Union\">Western Union</a> and <a href=\"https://wikipedia.org/wiki/Cornell_University\" title=\"Cornell University\">Cornell University</a> (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Western_Union\" title=\"Western Union\">Western Union</a> and <a href=\"https://wikipedia.org/wiki/Cornell_University\" title=\"Cornell University\">Cornell University</a> (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Western Union", "link": "https://wikipedia.org/wiki/Western_Union"}, {"title": "Cornell University", "link": "https://wikipedia.org/wiki/Cornell_University"}]}, {"year": "1814", "text": "<PERSON>, English surgeon and pathologist (d. 1899)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surgeon and pathologist (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surgeon and pathologist (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON>, American businessman and politician (d. 1867)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Socrates_<PERSON>\" title=\"Socrates <PERSON>\"><PERSON><PERSON></a>, American businessman and politician (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Socrates_<PERSON>\" title=\"Socrates <PERSON>\"><PERSON><PERSON></a>, American businessman and politician (d. 1867)", "links": [{"title": "So<PERSON>", "link": "https://wikipedia.org/wiki/So<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Scottish-Canadian lawyer and politician, 1st Prime Minister of Canada (d. 1891)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1825", "text": "<PERSON><PERSON>, American poet, author, and critic (d. 1878)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet, author, and critic (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet, author, and critic (d. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON>, Puerto Rican lawyer, philosopher, and sociologist (d. 1903)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>%C3%AD<PERSON>_de_<PERSON>os\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican lawyer, philosopher, and sociologist (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AD<PERSON>_de_<PERSON>os\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican lawyer, philosopher, and sociologist (d. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eugenio_Mar%C3%<PERSON><PERSON>_de_Hostos"}]}, {"year": "1842", "text": "<PERSON>, American psychologist and philosopher (d. 1910)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and philosopher (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and philosopher (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, German painter (d. 1914)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, Swedish mathematician and physicist (d. 1912)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish mathematician and physicist (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish mathematician and physicist (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>"}]}, {"year": "1850", "text": "<PERSON>, American pathologist and mycologist (d. 1942)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pathologist and mycologist (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pathologist and mycologist (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "Constantin <PERSON>, German lawyer and politician, 4th Chancellor of Weimar Germany (d. 1926)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/Constant<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Constant<PERSON></a>, German lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany#Weimar_Republic_(Reichskanzler)_(1919-1933)\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Weimar Germany</a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_<PERSON>\" title=\"Constantin <PERSON>\">Constant<PERSON></a>, German lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany#Weimar_Republic_(Reichskanzler)_(1919-1933)\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Weimar Germany</a> (d. 1926)", "links": [{"title": "Constantin <PERSON>", "link": "https://wikipedia.org/wiki/Constantin_<PERSON>"}, {"title": "List of Chancellors of Germany", "link": "https://wikipedia.org/wiki/List_of_Chancellors_of_Germany#Weimar_Republic_(Reichskanzler)_(1919-1933)"}]}, {"year": "1853", "text": "<PERSON><PERSON>, Greek painter and sculptor (d. 1932)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and sculptor (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and sculptor (d. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Jakobides"}]}, {"year": "1856", "text": "<PERSON>, Norwegian pianist and composer (d. 1941)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"Christian Sinding\"><PERSON></a>, Norwegian pianist and composer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_Sinding\" title=\"Christian Sinding\"><PERSON></a>, Norwegian pianist and composer (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Sinding"}]}, {"year": "1857", "text": "<PERSON>, English jockey (d. 1886)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, English jockey (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, English jockey (d. 1886)", "links": [{"title": "<PERSON> (jockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)"}]}, {"year": "1858", "text": "<PERSON>, American-English businessman, founded Selfridges (d. 1947)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English businessman, founded <a href=\"https://wikipedia.org/wiki/Selfridges\" title=\"Selfridges\">Selfridges</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English businessman, founded <a href=\"https://wikipedia.org/wiki/Selfridges\" title=\"Selfridges\">Selfridges</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Selfridges", "link": "https://wikipedia.org/wiki/Selfridges"}]}, {"year": "1859", "text": "<PERSON>, 1st Marquess <PERSON> of Kedleston, English politician, 35th Governor-General of India (d. 1925)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_<PERSON><PERSON>_of_Kedleston\" title=\"<PERSON>, 1st Marquess <PERSON> of Kedleston\"><PERSON>, 1st Marquess <PERSON> of Kedleston</a>, English politician, 35th <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_<PERSON><PERSON>_of_Kedleston\" title=\"<PERSON>, 1st Marquess <PERSON> of Kedleston\"><PERSON>, 1st Marquess <PERSON> of Kedleston</a>, English politician, 35th <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (d. 1925)", "links": [{"title": "<PERSON>, 1st Marquess <PERSON> of Kedleston", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_<PERSON>_of_Kedleston"}, {"title": "Governor-General of India", "link": "https://wikipedia.org/wiki/Governor-General_of_India"}]}, {"year": "1864", "text": "<PERSON>, Jr., American minister, lawyer, and politician (d. 1946)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American minister, lawyer, and politician (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American minister, lawyer, and politician (d. 1946)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1867", "text": "<PERSON>, English educationalist and Director of Education of the Colony of Transvaal (d.1950)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educationalist and Director of Education of the <a href=\"https://wikipedia.org/wiki/Colony_of_Transvaal\" class=\"mw-redirect\" title=\"Colony of Transvaal\">Colony of Transvaal</a> (d.1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educationalist and Director of Education of the <a href=\"https://wikipedia.org/wiki/Colony_of_Transvaal\" class=\"mw-redirect\" title=\"Colony of Transvaal\">Colony of Transvaal</a> (d.1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Colony of Transvaal", "link": "https://wikipedia.org/wiki/Colony_of_Transvaal"}]}, {"year": "1867", "text": "<PERSON>, English psychologist and academic (d. 1927)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and academic (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and academic (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON>, Chinese philosopher, academic, and politician (d. 1940)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Cai_<PERSON>i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese philosopher, academic, and politician (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cai_Yuan<PERSON>i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese philosopher, academic, and politician (d. 1940)", "links": [{"title": "Cai <PERSON>", "link": "https://wikipedia.org/wiki/Cai_Yuanpei"}]}, {"year": "1870", "text": "<PERSON>, American sculptor and educator (d. 1945)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON> <PERSON><PERSON>, American physicist and academic (d. 1956)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American physicist and academic (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American physicist and academic (d. 1956)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, American soldier and journalist (d. 1949)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Laughlin\" title=\"<PERSON>\"><PERSON></a>, American soldier and journalist (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27L<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and journalist (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27L<PERSON><PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, Russian composer and academic (d. 1956)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Reinhold_Gli%C3%A8re\" title=\"<PERSON><PERSON><PERSON> G<PERSON>ère\"><PERSON><PERSON><PERSON></a>, Russian composer and academic (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rein<PERSON>_Gli%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian composer and academic (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Reinhold_Gli%C3%A8re"}]}, {"year": "1876", "text": "<PERSON>, American baseball player (d. 1971)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>lick\"><PERSON></a>, American baseball player (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Flick\"><PERSON></a>, American baseball player (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lick"}]}, {"year": "1876", "text": "<PERSON>, American runner (d. 1952)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner (d. 1952)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Greek general and politician, President of Greece (d. 1952)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(general)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (general)\"><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece\" title=\"List of heads of state of Greece\">President of Greece</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(general)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (general)\"><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece\" title=\"List of heads of state of Greece\">President of Greece</a> (d. 1952)", "links": [{"title": "<PERSON><PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(general)"}, {"title": "List of heads of state of Greece", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece"}]}, {"year": "1885", "text": "<PERSON>, American activist and suffragist (d. 1977)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and suffragist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and suffragist (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, British actor (d. 1960)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, American ecologist and author (d. 1948)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ecologist and author (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ecologist and author (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aldo_Leopold"}]}, {"year": "1888", "text": "<PERSON>, American jurist and politician (d. 1954)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American geneticist and academic (d. 1938)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American baseball player and manager (d. 1976)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Brazilian poet and critic (d. 1954)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet and critic (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet and critic (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American runner (d. 1919)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Estonian painter (d. 1969)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian painter (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian painter (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>r_Aiki"}]}, {"year": "1893", "text": "<PERSON>, Australian rugby league player and coach (d. 1981)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach (d. 1981)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1893", "text": "<PERSON>, American journalist and author (d. 1942)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, American engineer and businessman, founded the Hammond Clock Company (d. 1973)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Hammond_Clock_Company\" title=\"Hammond Clock Company\">Hammond Clock Company</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Hammond_Clock_Company\" title=\"Hammond Clock Company\">Hammond Clock Company</a> (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Hammond Clock Company", "link": "https://wikipedia.org/wiki/Hammond_Clock_Company"}]}, {"year": "1897", "text": "<PERSON>, American historian and author (d. 1955)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German SS officer (d. 1979)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1899", "text": "<PERSON>, English-American actress, director, and producer (d. 1991)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress, director, and producer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress, director, and producer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Korean pilot (d. 1988)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"Kwon Ki-ok\"><PERSON><PERSON>-<PERSON></a>, Korean pilot (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"Kwon Ki-ok\"><PERSON><PERSON>-<PERSON></a>, Korean pilot (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ok"}]}, {"year": "1902", "text": "<PERSON>, French organist and composer (d. 1986)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1903", "text": "<PERSON>, South African author and activist (d. 1988)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author and activist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author and activist (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American anthropologist and theorist (d. 1960)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and theorist (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and theorist (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Swiss chemist and academic, discoverer of LSD (d. 2008)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic, discoverer of <a href=\"https://wikipedia.org/wiki/LSD\" title=\"LSD\">LSD</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic, discoverer of <a href=\"https://wikipedia.org/wiki/LSD\" title=\"LSD\">LSD</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "LSD", "link": "https://wikipedia.org/wiki/LSD"}]}, {"year": "1907", "text": "<PERSON>, French lawyer and politician, 142nd Prime Minister of France (d. 1982)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s_France\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 142nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Prime Minister of France</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s_France\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 142nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Prime Minister of France</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Mend%C3%A8s_France"}, {"title": "List of Prime Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France"}]}, {"year": "1907", "text": "<PERSON>, Polish-American rabbi, theologian, and philosopher (d. 1972)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American rabbi, theologian, and philosopher (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American rabbi, theologian, and philosopher (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American actor and activist (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and activist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and activist (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, New Zealand rugby player (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Australian soldier and politician (d. 1966)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and politician (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and politician (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American singer-songwriter (d. 1967)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Australian painter (d. 2003)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Japanese politician, 70th Prime Minister of Japan (d. 2004)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Zenk%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 70th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zenk%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 70th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zenk%C5%8D_Suzuki"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1912", "text": "<PERSON> \"<PERSON>\" <PERSON>, American actor, producer, and screenwriter (d. 1980)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Don_%22Red%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American actor, producer, and screenwriter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Don_%22Red%22_<PERSON>\" title='<PERSON> \"<PERSON>\" Barry'><PERSON> \"<PERSON>\" <PERSON></a>, American actor, producer, and screenwriter (d. 1980)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/Don_%22Red%22_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Danish actor (d. 1980)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actor (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actor (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, German javelin thrower (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kr%C3%BCger\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German javelin thrower (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kr%C3%BCger\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German javelin thrower (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luise_Kr%C3%BCger"}]}, {"year": "1915", "text": "<PERSON>, British colonel and lawyer (d. 1955)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British colonel and lawyer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British colonel and lawyer (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Argentinian-French actor (d. 1989)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-French actor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-French actor (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Canadian lawyer and politician, 17th Premier of Ontario (d. 1982)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Ontario", "link": "https://wikipedia.org/wiki/Premier_of_Ontario"}]}, {"year": "1918", "text": "<PERSON>, American author and journalist (d. 1973)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27B<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and journalist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27B<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and journalist (d. 1973)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._O%27B<PERSON>_(author)"}]}, {"year": "1918", "text": "<PERSON>, Australian rugby league player and soldier (d. 1945)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and soldier (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and soldier (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English wrestler (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, English wrestler (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, English wrestler (d. 2013)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American wrestler and trainer (d. 1990)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and trainer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Guerrero\"><PERSON><PERSON></a>, American wrestler and trainer (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ry_Guerrero"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American economist and politician, 24th United States Secretary of Commerce (d. 2010)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and politician, 24th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and politician, 24th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}]}, {"year": "1923", "text": "<PERSON>, American author and screenwriter (d. 1998)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, German historian and philosopher (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and philosopher (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and philosopher (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American race car driver, engineer, and businessman, founded Carroll Shelby International (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver, engineer, and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_International\" class=\"mw-redirect\" title=\"Carroll Shelby International\">Carroll Shelby International</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver, engineer, and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Carroll Shelby International\">Carroll Shelby International</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, French-American physician and endocrinologist, Nobel Prize laureate (d. 2024)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American physician and endocrinologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American physician and endocrinologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1924", "text": "<PERSON>, Jr., American lawyer, judge, and politician (d. 1994)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer, judge, and politician (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer, judge, and politician (d. 1994)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1924", "text": "<PERSON>, American blues singer-songwriter and musician (d. 1970)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Slim_Ha<PERSON>o\" title=\"Slim Harpo\"><PERSON></a>, American blues singer-songwriter and musician (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ha<PERSON>o\" title=\"Slim Harpo\"><PERSON></a>, American blues singer-songwriter and musician (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ha<PERSON>o"}]}, {"year": "1925", "text": "<PERSON>, American television producer, co-founded MTM Enterprises (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television producer, co-founded <a href=\"https://wikipedia.org/wiki/MTM_Enterprises\" title=\"MTM Enterprises\">MTM Enterprises</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television producer, co-founded <a href=\"https://wikipedia.org/wiki/MTM_Enterprises\" title=\"MTM Enterprises\">MTM Enterprises</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "MTM Enterprises", "link": "https://wikipedia.org/wiki/MTM_Enterprises"}]}, {"year": "1926", "text": "<PERSON>, Russian colonel, pilot, and astronaut (d. 1998)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, pilot, and astronaut (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, pilot, and astronaut (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American director and producer (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Estonian architect and theorist (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian architect and theorist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian architect and theorist (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Australian lawyer and politician, 10th Deputy Premier of New South Wales (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_New_South_Wales\" title=\"Deputy Premier of New South Wales\">Deputy Premier of New South Wales</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_New_South_Wales\" title=\"Deputy Premier of New South Wales\">Deputy Premier of New South Wales</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Premier of New South Wales", "link": "https://wikipedia.org/wiki/Deputy_Premier_of_New_South_Wales"}]}, {"year": "1930", "text": "<PERSON>, Australian-American actor and screenwriter (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American actor and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American actor and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Australian painter, historian, and curator (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter, historian, and curator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter, historian, and curator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American composer and author (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Mexican actor and director", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American country singer-songwriter and guitarist (d. 2005)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country singer-songwriter and guitarist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country singer-songwriter and guitarist (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian lawyer and politician, 20th Prime Minister of Canada", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9tien\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9tien"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1934", "text": "<PERSON>, American actor (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, German-American sculptor and educator (d. 1970)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sculptor and educator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sculptor and educator (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Italian character actor, circus performer, voice artist, and stuntman (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian character actor, circus performer, voice artist, and stuntman (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian character actor, circus performer, voice artist, and stuntman (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English miner, activist, and politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English miner, activist, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English miner, activist, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian alpine skier", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>veit\" title=\"<PERSON>veit\"><PERSON></a>, Canadian alpine skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ve<PERSON>\" title=\"<PERSON>veit\"><PERSON></a>, Canadian alpine skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>veit"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Estonian geographer and politician, 10th Prime Minister of Estonia", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian geographer and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian geographer and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rson"}]}, {"year": "1942", "text": "<PERSON>, American basketball player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American saxophonist and actor (d. 2011)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Sudanese poet and academic (d. 1989)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese poet and academic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese poet and academic (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Indian politician, 3rd Chief Minister of Jharkhand", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Jharkhand\" class=\"mw-redirect\" title=\"Chief Minister of Jharkhand\">Chief Minister of Jharkhand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Jharkhand\" class=\"mw-redirect\" title=\"Chief Minister of Jharkhand\">Chief Minister of Jharkhand</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ren"}, {"title": "Chief Minister of Jharkhand", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Jharkhand"}]}, {"year": "1945", "text": "<PERSON>, German actress, author, and businesswoman (d. 2017)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress, author, and businesswoman (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress, author, and businesswoman (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter and actress (d. 2022)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English progressive rock keyboard player and songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English progressive rock keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English progressive rock keyboard player and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1946", "text": "<PERSON>, American theologian and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, American theologian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, American theologian and author", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(theologian)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, New Zealand rugby player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rugby_player)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rugby player)\"><PERSON><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rugby_player)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rugby player)\"><PERSON><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON><PERSON> (rugby player)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rugby_player)"}]}, {"year": "1948", "text": "<PERSON>, German footballer and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Scottish footballer and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 54th <PERSON><PERSON><PERSON><PERSON> (d. 2018)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hiroshi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 54th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hiroshi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 54th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waj<PERSON>_<PERSON><PERSON>hi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1948", "text": "<PERSON>, American runner and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Welsh drummer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, Welsh drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, Welsh drummer", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "1949", "text": "<PERSON>, Australian singer-songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American basketball player and coach (d. 2023)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Iranian lawyer and politician, 2nd Vice President of Iran", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Iran\" class=\"mw-redirect\" title=\"Vice President of Iran\">Vice President of Iran</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Iran\" class=\"mw-redirect\" title=\"Vice President of Iran\">Vice President of Iran</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of Iran", "link": "https://wikipedia.org/wiki/Vice_President_of_Iran"}]}, {"year": "1951", "text": "<PERSON>, American rock singer and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English footballer and manager (d. 2000)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish archbishop (d. 2021)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish archbishop (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish archbishop (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Australian actor and playwright (d. 2013)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actor and playwright (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actor and playwright (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American golfer and architect", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and architect", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Australian lawyer and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American guitarist, composer, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English politician, Vice-Chamberlain of the Household", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Vice-Chamberlain_of_the_Household\" title=\"Vice-Chamberlain of the Household\">Vice-Chamberlain of the Household</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Vice-Chamberlain_of_the_Household\" title=\"Vice-Chamberlain of the Household\">Vice-Chamberlain of the Household</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}, {"title": "Vice-Chamberlain of the Household", "link": "https://wikipedia.org/wiki/Vice-Chamberlain_of_the_Household"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Greek engineer and politician, Greek Minister of Agricultural Development and Food", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek engineer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Agricultural_Development_and_Food_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Agricultural Development and Food (Greece)\">Greek Minister of Agricultural Development and Food</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek engineer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Agricultural_Development_and_Food_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Agricultural Development and Food (Greece)\">Greek Minister of Agricultural Development and Food</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>dalidis"}, {"title": "Ministry of Agricultural Development and Food (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Agricultural_Development_and_Food_(Greece)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Estonian physicist and politician, 26th Estonian Minister of Defence", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian physicist and politician, 26th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian physicist and politician, 26th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aa<PERSON>_<PERSON><PERSON>oo"}, {"title": "Minister of Defence (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Indian engineer, academic, and activist, Nobel Prize laureate", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Kailash_Satyarthi\" title=\"Kailash Satyarthi\"><PERSON><PERSON></a>, Indian engineer, academic, and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kailash_Satyarthi\" title=\"Kailash Satyarthi\"><PERSON><PERSON></a>, Indian engineer, academic, and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kailash_Sa<PERSON><PERSON>hi"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1956", "text": "<PERSON>, Australian rugby league player (d. 1994)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 1994)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1956", "text": "<PERSON> <PERSON>, American rapper (d. 2014)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Big_Bank_Hank\" title=\"Big Bank Hank\">Big Bank Hank</a>, American rapper (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Bank_Hank\" title=\"Big Bank Hank\">Big Bank Hank</a>, American rapper (d. 2014)", "links": [{"title": "Big Bank Hank", "link": "https://wikipedia.org/wiki/Big_Bank_Hank"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Scottish actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American basketball player and coach (d. 2015)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian rules footballer and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rules_footballer)\" title=\"<PERSON> (Australian rules footballer)\"><PERSON></a>, Australian rules footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_rules_footballer)\" title=\"<PERSON> (Australian rules footballer)\"><PERSON></a>, Australian rules footballer and coach", "links": [{"title": "<PERSON> (Australian rules footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_rules_footballer)"}]}, {"year": "1957", "text": "<PERSON>, English footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American NASCAR driver", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American NASCAR driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American NASCAR driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ramage\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ramage\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ge"}]}, {"year": "1961", "text": "<PERSON>, English author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Swedish racing driver (d. 1989)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish racing driver (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish racing driver (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Austrian politician, Head of the House of Habsburg-Lorraine", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, Head of the <a href=\"https://wikipedia.org/wiki/House_of_Habsburg-Lorraine\" title=\"House of Habsburg-Lorraine\">House of Habsburg-Lorraine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, Head of the <a href=\"https://wikipedia.org/wiki/House_of_Habsburg-Lorraine\" title=\"House of Habsburg-Lorraine\">House of Habsburg-Lorraine</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "House of Habsburg-Lorraine", "link": "https://wikipedia.org/wiki/House_of_Habsburg-Lorraine"}]}, {"year": "1962", "text": "<PERSON>, Welsh politician, Minister of State for Europe", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of State for Europe", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Europe"}]}, {"year": "1962", "text": "<PERSON>, American actress and comedian", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American journalist and activist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English rugby player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_(rugby_union)"}]}, {"year": "1963", "text": "<PERSON>, American-Australian swimmer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Italian-born British actor and director", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-born British actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-born British actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, German swimmer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Schneider"}]}, {"year": "1964", "text": "<PERSON>, French actor and director", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Filipino lawyer and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Mexican wrestler", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Mascarita_Sagrada\" title=\"Mascarita Sagrada\"><PERSON><PERSON><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mascarita_Sagrada\" title=\"Mascarita Sagrada\"><PERSON><PERSON><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mascarita_Sagrada"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Russian footballer and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American author and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>-<PERSON>, Irish politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Swedish economist and politician, Swedish Minister for Finance", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish economist and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Finance_(Sweden)\" title=\"Minister for Finance (Sweden)\">Swedish Minister for Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish economist and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Finance_(Sweden)\" title=\"Minister for Finance (Sweden)\">Swedish Minister for Finance</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Finance (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_Finance_(Sweden)"}]}, {"year": "1968", "text": "<PERSON>, American guitarist and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian rugby league player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Dominican-American baseball player, coach, manager, and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player, coach, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player, coach, manager, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Italian painter and sculptor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian painter and sculptor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manfredi_Beninati"}]}, {"year": "1970", "text": "<PERSON>, American basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American director, producer, screenwriter, and actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American composer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter, producer, and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Blige\" title=\"<PERSON> Blige\"><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Blige\" title=\"<PERSON> Blige\"><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ge"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter, producer, and actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Danish composer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress and playwright", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dunbar\" title=\"Rock<PERSON> Dunbar\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rockmond_Dunbar\" title=\"Rock<PERSON> Dunbar\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON> Dunbar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Dunbar"}]}, {"year": "1974", "text": "<PERSON>, German footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Roman_G%C3%B6rtz\" title=\"Roman Görtz\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_G%C3%B6rtz\" title=\"Roman Görtz\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_G%C3%B6rtz"}]}, {"year": "1974", "text": "<PERSON>, Canadian baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, German footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English rugby player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Italian politician, 56th Prime Minister of Italy", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, 56th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, 56th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Efthim<PERSON>_Rentzias\" title=\"Efthimios Rentzias\">E<PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Efthim<PERSON>_Rentzias\" title=\"Efthimios Rentzias\"><PERSON><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Efthimios_Rentzias"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>-<PERSON><PERSON>, German speed skater", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, German speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, German speed skater", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian rugby league player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Ukrainian long jumper", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ch\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian long jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Estonian basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>lo_Allingu\" title=\"Vallo Allingu\"><PERSON><PERSON></a>, Estonian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vallo_Allingu\" title=\"Vallo Allingu\"><PERSON><PERSON></a>, Estonian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vallo_Allingu"}]}, {"year": "1978", "text": "<PERSON>, Australian actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Irish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, English footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American director and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Malaysian singer-songwriter and businesswoman", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Sit<PERSON>_<PERSON>za\" title=\"Sit<PERSON> Nurhaliza\"><PERSON><PERSON></a>, Malaysian singer-songwriter and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sit<PERSON>_<PERSON>za\" title=\"Sit<PERSON> Nurhaliza\"><PERSON><PERSON></a>, Malaysian singer-songwriter and businesswoman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Siti_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Irish hurler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player and coach", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1982", "text": "<PERSON>, Australian-French rugby league player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-French rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-French rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1982", "text": "<PERSON>, American actor (d. 2017)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1982", "text": "<PERSON>, South Korean actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin\" title=\"<PERSON> Ye-jin\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin\" title=\"Son Ye-jin\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>n", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin"}]}, {"year": "1983", "text": "<PERSON>, Swedish skier", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>er"}]}, {"year": "1983", "text": "<PERSON>, Australian rules footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German racing driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Croatian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Dar<PERSON>_Kre%C5%A1i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dar<PERSON>_Kre%C5%A1i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dario_Kre%C5%A1i%C4%87"}]}, {"year": "1984", "text": "<PERSON>, American web developer and businessman, co-created WordPress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American web developer and businessman, co-created <a href=\"https://wikipedia.org/wiki/WordPress\" title=\"WordPress\">WordPress</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American web developer and businessman, co-created <a href=\"https://wikipedia.org/wiki/WordPress\" title=\"WordPress\">WordPress</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "WordPress", "link": "https://wikipedia.org/wiki/WordPress"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ja Naomi <PERSON>\"><PERSON><PERSON> <PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ja Naomi <PERSON>\"><PERSON><PERSON> <PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American BMX rider", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American BMX rider", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American BMX rider", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Hungarian sprint canoer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian sprint canoer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian sprint canoer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Danuta_Koz%C3%A1k"}]}, {"year": "1987", "text": "<PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, South Korean actor and model", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, South Korean actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, South Korean actor and model", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(actor)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, American-Russian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Epiphanny_Prince\" title=\"Epiphanny Prince\">Epiphanny <PERSON></a>, American-Russian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Epiphanny_Prince\" title=\"Epiphanny Prince\">Epiphanny <PERSON></a>, American-Russian basketball player", "links": [{"title": "Epiphanny <PERSON>", "link": "https://wikipedia.org/wiki/Epiphanny_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_lineman)\" title=\"<PERSON> (defensive lineman)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_lineman)\" title=\"<PERSON> (defensive lineman)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (defensive lineman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_lineman)"}]}, {"year": "1991", "text": "<PERSON>, Italian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Spanish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, South Korean rapper and dancer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, South Korean rapper and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, South Korean rapper and dancer", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hoon_(rapper)"}]}, {"year": "1993", "text": "<PERSON>, Saint Lucian-Canadian basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Saint Lucian-Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Saint Lucian-Canadian basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1993", "text": "<PERSON>, South Korean Go player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean Go player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean Go player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1993)\" title=\"<PERSON> (footballer, born 1993)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1993)\" title=\"<PERSON> (footballer, born 1993)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1993)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1993)"}]}, {"year": "1993", "text": "<PERSON>, Irish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American baseball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, German footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leroy_San%C3%A9"}]}, {"year": "1997", "text": "<PERSON>, Australian singer-songwriter, guitarist, and actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, New Zealand rugby league player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Norwegian sport shooter", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian sport shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian sport shooter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, South Korean volleyball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-jin\"><PERSON></a>, South Korean volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-ji<PERSON>\"><PERSON></a>, South Korean volleyball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>n"}]}, {"year": "1999", "text": "<PERSON>, Australian-Fijian rugby league player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Fijian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Fijian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, South Korean singer-songwriter", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(singer,_born_2000)\" title=\"<PERSON> (singer, born 2000)\"><PERSON><PERSON><PERSON></a>, South Korean singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer,_born_2000)\" title=\"<PERSON> (singer, born 2000)\"><PERSON><PERSON><PERSON></a>, South Korean singer-songwriter", "links": [{"title": "<PERSON> (singer, born 2000)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-yeon_(singer,_born_2000)"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "140", "text": "<PERSON>, Bishop of Rome (b. 74)", "html": "140 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Hyginus\" title=\"Pope Hyginus\"><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Bishop of Rome (b. 74)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Hyginus\" title=\"Pope Hyginus\"><PERSON> <PERSON><PERSON><PERSON></a>, Bishop of Rome (b. 74)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Hyginus"}]}, {"year": "705", "text": "<PERSON> (b. 655)", "html": "705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> John VI\"><PERSON> <PERSON> VI</a> (b. 655)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John <PERSON>\"><PERSON> VI</a> (b. 655)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "782", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 709)", "html": "782 - <a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dnin\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dnin\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 709)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_K%C5%8Dnin"}]}, {"year": "812", "text": "<PERSON><PERSON><PERSON><PERSON>, Byzantine emperor", "html": "812 - <a href=\"https://wikipedia.org/wiki/Staurakios\" title=\"St<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Staurakios\" title=\"St<PERSON>rak<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine emperor", "links": [{"title": "Staurak<PERSON>", "link": "https://wikipedia.org/wiki/Staurakios"}]}, {"year": "844", "text": "<PERSON>, Byzantine emperor (b. 770)", "html": "844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "887", "text": "<PERSON><PERSON> of Provence, Frankish nobleman", "html": "887 - <a href=\"https://wikipedia.org/wiki/Boso_of_Provence\" title=\"<PERSON><PERSON> of Provence\"><PERSON><PERSON> of Provence</a>, Frankish <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boso_of_Provence\" title=\"<PERSON><PERSON> of Provence\"><PERSON><PERSON> of Provence</a>, Frankish <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "links": [{"title": "<PERSON><PERSON> of Provence", "link": "https://wikipedia.org/wiki/Boso_of_Provence"}, {"title": "Nobility", "link": "https://wikipedia.org/wiki/Nobility"}]}, {"year": "937", "text": "<PERSON>, empress of Later Tang", "html": "937 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(<PERSON>_<PERSON>%27s_wife)\" title=\"Empress <PERSON> (<PERSON>'s wife)\"><PERSON></a>, empress of <a href=\"https://wikipedia.org/wiki/Later_Tang\" title=\"Later Tang\">Later Tang</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(<PERSON>_<PERSON>%27s_wife)\" title=\"Empress <PERSON> (<PERSON>'s wife)\"><PERSON></a>, empress of <a href=\"https://wikipedia.org/wiki/Later_Tang\" title=\"Later Tang\">Later Tang</a>", "links": [{"title": "<PERSON> <PERSON> (<PERSON>'s wife)", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_(<PERSON>_<PERSON>%27s_wife)"}, {"title": "Later Tang", "link": "https://wikipedia.org/wiki/Later_Tang"}]}, {"year": "937", "text": "<PERSON>, prince of Later Tang", "html": "937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>mei\" title=\"<PERSON> Chongmei\"><PERSON></a>, prince of Later Tang", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>mei\" title=\"Li Chongmei\"><PERSON></a>, prince of Later Tang", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "937", "text": "<PERSON>, emperor of Later Tang (b. 885)", "html": "937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>gke\"><PERSON></a>, emperor of Later Tang (b. 885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ke\"><PERSON></a>, emperor of Later Tang (b. 885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "937", "text": "<PERSON>, empress of Later Tang", "html": "937 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(<PERSON>_<PERSON>%27s_wife)\" title=\"Empress <PERSON> (<PERSON>'s wife)\"><PERSON></a>, empress of Later Tang", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(<PERSON>_<PERSON>%27s_wife)\" title=\"Empress <PERSON> (<PERSON>'s wife)\"><PERSON></a>, empress of Later Tang", "links": [{"title": "Empress <PERSON> (<PERSON>'s wife)", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_(<PERSON>_<PERSON><PERSON>%27s_wife)"}]}, {"year": "1055", "text": "<PERSON>, Byzantine emperor (b. 1000)", "html": "1055 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Monomachos\" title=\"<PERSON> Monomachos\"><PERSON>oma<PERSON></a>, Byzantine emperor (b. 1000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IX_Monomachos\" title=\"<PERSON> Monomachos\"><PERSON> Monomacho<PERSON></a>, Byzantine emperor (b. 1000)", "links": [{"title": "<PERSON> Monomachos", "link": "https://wikipedia.org/wiki/Constantine_IX_Monomachos"}]}, {"year": "1068", "text": "<PERSON><PERSON><PERSON>, Margrave of Meissen", "html": "1068 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON><PERSON><PERSON>, Mar<PERSON> of Meissen\"><PERSON><PERSON><PERSON>, Mar<PERSON> of Meissen</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON> of Meissen\"><PERSON><PERSON><PERSON>, <PERSON><PERSON> of Meissen</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, Margrave of Meissen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1083", "text": "<PERSON> of Nordheim (b. 1020)", "html": "1083 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nordheim\" title=\"<PERSON> of Nordheim\"><PERSON> of Nordheim</a> (b. 1020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nordheim\" title=\"<PERSON> of Nordheim\"><PERSON> of Nordheim</a> (b. 1020)", "links": [{"title": "<PERSON> of Nordheim", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nordheim"}]}, {"year": "1266", "text": "Swietopel<PERSON> II, Duke of Pomerania", "html": "1266 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_II,_Duke_of_Pomerania\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> II, Duke of Pomerania\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Pomerania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_II,_Duke_of_Pomerania\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Pomerania\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Pomerania</a>", "links": [{"title": "Swietopel<PERSON> II, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_II,_<PERSON>_of_Pomerania"}]}, {"year": "1344", "text": "<PERSON>, Bishop of Hereford and Lord Chancellor of Ireland", "html": "1344 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Bishop of Hereford and Lord Chancellor of Ireland", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Bishop of Hereford and Lord Chancellor of Ireland", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1372", "text": "<PERSON> of Lancaster, English noblewoman (b. 1318)", "html": "1372 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lancaster\"><PERSON> Lancaster</a>, English noblewoman (b. 1318)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Lancaster\"><PERSON> Lancaster</a>, English noblewoman (b. 1318)", "links": [{"title": "<PERSON> of Lancaster", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1396", "text": "<PERSON><PERSON><PERSON>, Metropolitan bishop of Thessalonica (b.c. 1341)", "html": "1396 - <a href=\"https://wikipedia.org/wiki/Is<PERSON>re_Glabas\" title=\"<PERSON><PERSON><PERSON> Glabas\"><PERSON><PERSON><PERSON></a>, Metropolitan bishop of <a href=\"https://wikipedia.org/wiki/Thessalonica\" class=\"mw-redirect\" title=\"Thessalonica\">Thessalonica</a> (b.c. 1341)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gla<PERSON>\"><PERSON><PERSON><PERSON></a>, Metropolitan bishop of <a href=\"https://wikipedia.org/wiki/Thessalonica\" class=\"mw-redirect\" title=\"Thessalonica\">Thessalonica</a> (b.c. 1341)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Is<PERSON>re_<PERSON>bas"}, {"title": "Thessalonica", "link": "https://wikipedia.org/wiki/Thessalonica"}]}, {"year": "1397", "text": "<PERSON><PERSON><PERSON>, Grand Duke of Lithuania", "html": "1397 - <a href=\"https://wikipedia.org/wiki/Skirgaila\" title=\"Skirga<PERSON>\"><PERSON><PERSON><PERSON></a>, Grand Duke of Lithuania", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Skirga<PERSON>\" title=\"Skirga<PERSON>\"><PERSON><PERSON><PERSON></a>, Grand Duke of Lithuania", "links": [{"title": "Skirgaila", "link": "https://wikipedia.org/wiki/Skirgaila"}]}, {"year": "1494", "text": "<PERSON>, Italian painter (b. 1449)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1449)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1449)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1495", "text": "<PERSON>, Spanish cardinal (b. 1428)", "html": "1495 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_de_Mendoza\" title=\"<PERSON>\"><PERSON></a>, Spanish cardinal (b. 1428)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_de_Mendoza\" title=\"<PERSON>\"><PERSON></a>, Spanish cardinal (b. 1428)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gonz%C3%<PERSON><PERSON><PERSON>_de_Mendoza"}]}, {"year": "1546", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian painter and sculptor (b. c. 1471)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/Gauden<PERSON>_Ferrari\" title=\"Gaudenzio Ferrari\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter and sculptor (b. c. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gauden<PERSON>_Ferrari\" title=\"Gaudenzio Ferrari\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter and sculptor (b. c. 1471)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aud<PERSON><PERSON>_Ferrari"}]}, {"year": "1554", "text": "<PERSON>, king of Arakan (b. 1493)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bin\"><PERSON></a>, king of Arakan (b. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bin\"><PERSON></a>, king of Arakan (b. 1493)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1641", "text": "<PERSON>, Spanish poet and painter (b. 1583)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Juan_Mart%C3%ADnez_de_J%C3%A1uregui_y_Aguilar\" class=\"mw-redirect\" title=\"<PERSON>gui y Aguilar\"><PERSON></a>, Spanish poet and painter (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_<PERSON>%C3%ADnez_de_J%C3%A1uregui_y_Aguilar\" class=\"mw-redirect\" title=\"<PERSON>gui y Aguilar\"><PERSON> y <PERSON></a>, Spanish poet and painter (b. 1583)", "links": [{"title": "<PERSON>gui y Aguilar", "link": "https://wikipedia.org/wiki/Juan_Mart%C3%ADnez_de_J%C3%A1uregui_y_Aguilar"}]}, {"year": "1696", "text": "<PERSON>, French priest, missionary, and explorer (b. 1616)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, missionary, and explorer (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, missionary, and explorer (b. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1703", "text": "<PERSON>, German scholar and critic (b. 1632)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and critic (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and critic (b. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, French priest and theologian (b. 1637)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and theologian (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and theologian (b. 1637)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1735", "text": "<PERSON><PERSON>, Metropolitan of Cetinje (b. 1670)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Metropolitan_of_Cetinje\" title=\"<PERSON><PERSON>, Metropolitan of Cetinje\"><PERSON><PERSON>, Metropolitan of Cetinje</a> (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Metropolitan_of_Cetinje\" title=\"<PERSON><PERSON>, Metropolitan of Cetinje\"><PERSON><PERSON>, Metropolitan of Cetinje</a> (b. 1670)", "links": [{"title": "<PERSON><PERSON>, Metropolitan of Cetinje", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Metropolitan_of_Cetinje"}]}, {"year": "1753", "text": "<PERSON>, Irish-English physician and academic (b. 1660)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English physician and academic (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English physician and academic (b. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, French mathematician and philosopher (b. 1688)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (b. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (b. 1688)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON><PERSON><PERSON>, French-English sculptor (b. 1695)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A7ois_Roubiliac\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-English sculptor (b. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A7ois_Roubiliac\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-English sculptor (b. 1695)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A7ois_Roubiliac"}]}, {"year": "1763", "text": "<PERSON><PERSON><PERSON>, German poet, historian, and theologian (b. 1676)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German poet, historian, and theologian (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German poet, historian, and theologian (b. 1676)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON><PERSON><PERSON>, <PERSON>, French philosopher and author (b. 1704)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>%27Argens\" title=\"<PERSON><PERSON><PERSON>, Marquis <PERSON>\"><PERSON><PERSON><PERSON>, <PERSON></a>, French philosopher and author (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>%27Argens\" title=\"<PERSON><PERSON><PERSON>, Marquis <PERSON>\"><PERSON><PERSON><PERSON>, <PERSON></a>, French philosopher and author (b. 1704)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_<PERSON>_d%27A<PERSON>s"}]}, {"year": "1788", "text": "<PERSON>, French admiral (b. 1722)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral (b. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, Welsh composer and poet (b. 1717)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh composer and poet (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>celyn\"><PERSON></a>, Welsh composer and poet (b. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON><PERSON><PERSON> of Georgia (b. 1720)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Georgia\" title=\"<PERSON><PERSON><PERSON> II of Georgia\"><PERSON><PERSON><PERSON> II of Georgia</a> (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Georgia\" title=\"<PERSON><PERSON><PERSON> II of Georgia\"><PERSON><PERSON><PERSON> II of Georgia</a> (b. 1720)", "links": [{"title": "<PERSON><PERSON><PERSON> of Georgia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Georgia"}]}, {"year": "1801", "text": "<PERSON>, Italian composer and educator (b. 1749)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, 1st Baron <PERSON>, Anglo-Irish politician and peer (b. 1736)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Anglo-Irish politician and peer (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Anglo-Irish politician and peer (b. 1736)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, Canadian businessman, founded the Molson Brewing Company (b. 1763)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\">Molson Brewing Company</a> (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\">Molson Brewing Company</a> (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American lawyer, author, and songwriter (b. 1779)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and songwriter (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and songwriter (b. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON>, Irish actor (b. 1818)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish actor (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish actor (b. 1818)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, English minister and academic (b. 1816)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(educator)\" title=\"<PERSON> (educator)\"><PERSON></a>, English minister and academic (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(educator)\" title=\"<PERSON> (educator)\"><PERSON></a>, English minister and academic (b. 1816)", "links": [{"title": "<PERSON> (educator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(educator)"}]}, {"year": "1867", "text": "<PERSON>, English-Australian businessman and politician, 1st Premier of New South Wales (b. 1812)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1882", "text": "<PERSON>, German physiologist and biologist (b. 1810)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and biologist (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and biologist (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, French urban planner (b. 1809)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French urban planner (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French urban planner (b. 1809)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A8<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English cricketer and rugby player (b. 1862)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and rugby player (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and rugby player (b. 1862)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1904", "text": "<PERSON>, Canadian merchant and politician (b. 1815)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian merchant and politician (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian merchant and politician (b. 1815)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1914", "text": "<PERSON>, Danish brewer and philanthropist (b. 1842)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish brewer and philanthropist (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish brewer and philanthropist (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Norwegian philologist and lexicographer (b. 1844)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hj%C3%B8tt\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian philologist and lexicographer (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hj%C3%B8tt\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian philologist and lexicographer (b. 1844)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Steinar_Schj%C3%B8tt"}]}, {"year": "1923", "text": "<PERSON> of Greece (b. 1868)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Constantine_I_of_Greece\" title=\"Constantine I of Greece\"><PERSON> of Greece</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_I_of_Greece\" title=\"Constantine I of Greece\"><PERSON> of Greece</a> (b. 1868)", "links": [{"title": "Constantine I of Greece", "link": "https://wikipedia.org/wiki/Constantine_I_of_Greece"}]}, {"year": "1928", "text": "<PERSON>, English novelist and poet (b. 1840)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Swedish organist, composer, and conductor (b. 1841)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Andr%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish organist, composer, and conductor (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Andr%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish organist, composer, and conductor (b. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elfrida_Andr%C3%A9e"}]}, {"year": "1931", "text": "<PERSON>, American pastor, historian, and author (b. 1852)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor, historian, and author (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor, historian, and author (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Turkish colonel and politician (b. 1882)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish colonel and politician (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish colonel and politician (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, German mathematician, philosopher, and chess player (b. 1868)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, philosopher, and chess player (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, philosopher, and chess player (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Italian politician, Italian Minister of Foreign Affairs (b. 1903)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)\" title=\"Minister of Foreign Affairs (Italy)\">Italian Minister of Foreign Affairs</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)\" title=\"Minister of Foreign Affairs (Italy)\">Italian Minister of Foreign Affairs</a> (b. 1903)", "links": [{"title": "Galeazzo <PERSON>iano", "link": "https://wikipedia.org/wiki/Galeazzo_Ciano"}, {"title": "Minister of Foreign Affairs (Italy)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)"}]}, {"year": "1947", "text": "<PERSON>, Canadian singer (b. 1879)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Eva <PERSON>\"><PERSON></a>, Canadian singer (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_<PERSON>uay"}]}, {"year": "1952", "text": "<PERSON>, French general (b. 1889)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Italian tenor and educator (b. 1885)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tenor and educator (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tenor and educator (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Georgian journalist and politician, Prime Minister of Georgia (b. 1868)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Noe_Zhordania\" title=\"Noe Zhordania\"><PERSON><PERSON></a>, Georgian journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Georgia\" title=\"Prime Minister of Georgia\">Prime Minister of Georgia</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Noe_Zhordan<PERSON>\" title=\"Noe Zhordania\"><PERSON><PERSON></a>, Georgian journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Georgia\" title=\"Prime Minister of Georgia\">Prime Minister of Georgia</a> (b. 1868)", "links": [{"title": "Noe Zhordania", "link": "https://wikipedia.org/wiki/Noe_Zhordania"}, {"title": "Prime Minister of Georgia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Georgia"}]}, {"year": "1953", "text": "<PERSON>, American businesswoman (b.1874)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman (b.1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman (b.1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Austrian composer (b. 1870)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Austrian composer (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Austrian composer (b. 1870)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1957", "text": "<PERSON>, Australian lawyer and politician, Solicitor-General of Australia (b. 1867)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor-General_of_Australia\" title=\"Solicitor-General of Australia\">Solicitor-General of Australia</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor-General_of_Australia\" title=\"Solicitor-General of Australia\">Solicitor-General of Australia</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Solicitor-General of Australia", "link": "https://wikipedia.org/wiki/Solicitor-General_of_Australia"}]}, {"year": "1961", "text": "<PERSON>, German soprano and actress (b. 1883)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soprano and actress (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soprano and actress (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English-American scholar, theologian, and academic (b. 1902)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American scholar, theologian, and academic (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American scholar, theologian, and academic (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American baseball player (b. 1893)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Swiss sculptor and painter (b. 1901)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss sculptor and painter (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss sculptor and painter (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Indian academic and politician, 2nd Prime Minister of India (b. 1904)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian academic and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian academic and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Israeli linguist and scholar (b. 1876)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli linguist and scholar (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli linguist and scholar (b. 1876)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1969", "text": "<PERSON><PERSON>, English author and educator (b. 1890)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> C<PERSON>pton\"><PERSON><PERSON></a>, English author and educator (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> C<PERSON>pton\"><PERSON><PERSON></a>, English author and educator (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Irish poet and playwright (b. 1881)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Padraic_Colum\" title=\"Padraic Colum\"><PERSON><PERSON><PERSON></a>, Irish poet and playwright (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Padra<PERSON>_Colum\" title=\"Padraic Colum\"><PERSON><PERSON><PERSON></a>, Irish poet and playwright (b. 1881)", "links": [{"title": "Padraic Colum", "link": "https://wikipedia.org/wiki/Padraic_Colum"}]}, {"year": "1975", "text": "<PERSON>, German tenor and actor (b. 1901)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, German tenor and actor (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, German tenor and actor (b. 1901)", "links": [{"title": "<PERSON> (tenor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)"}]}, {"year": "1980", "text": "<PERSON>, English author (b. 1913)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ym"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1889)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1985", "text": "<PERSON>, American actor, director, and screenwriter (b. 1895)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian lawyer and politician, 12th Governor-General of Australia (b. 1891)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}]}, {"year": "1986", "text": "<PERSON>, English author and screenwriter (b. 1916)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Polish mountaineer (b. 1948)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Swiss-English pianist, composer, and conductor (b. 1911)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-English pianist, composer, and conductor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-English pianist, composer, and conductor (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American colonel and pilot, Medal of Honor recipient (b. 1912)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pa<PERSON>\"><PERSON><PERSON></a>, American colonel and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pa<PERSON>\"><PERSON><PERSON></a>, American colonel and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Polish-American physicist and academic, Nobel Prize laureate (b. 1898)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1989", "text": "<PERSON>, English radio host (b. 1942)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio host (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio host (b. 1942)", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)"}]}, {"year": "1990", "text": "<PERSON>, American author and illustrator (b. 1898)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1905)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1994", "text": "<PERSON>, German physician (b. 1902)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Belarusian-American violinist and educator (b. 1909)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-American violinist and educator (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-American violinist and educator (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Turkish author and poet (b. 1936)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Onat_<PERSON>ar\" title=\"Onat Kutlar\"><PERSON><PERSON></a>, Turkish author and poet (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Onat_<PERSON>ar\" title=\"Onat Kutlar\"><PERSON><PERSON></a>, Turkish author and poet (b. 1936)", "links": [{"title": "Onat Kutlar", "link": "https://wikipedia.org/wiki/Onat_<PERSON>ar"}]}, {"year": "1995", "text": "<PERSON>, U.S. Army captain (b. 1918)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, U.S. Army captain (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, U.S. Army captain (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, German general (b. 1907)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian-American ice hockey player (b. 1942)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian singer-songwriter and guitarist (b. 1940)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_De_Andr%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Andr%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabrizio_De_Andr%C3%A9"}]}, {"year": "1999", "text": "<PERSON>, Scottish author and poet (b. 1897)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and poet (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and poet (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Irish-Canadian author and screenwriter (b. 1921)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Irish-Canadian author and screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Irish-Canadian author and screenwriter (b. 1921)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>(novelist)"}]}, {"year": "2000", "text": "<PERSON>, American businessman, invented <PERSON><PERSON><PERSON> (b. 1911)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, invented <a href=\"https://wikipedia.org/wiki/Clearasil\" title=\"Clearasil\">Clearasil</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, invented <a href=\"https://wikipedia.org/wiki/Clearasil\" title=\"Clearasil\">Clearasil</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Clearasil", "link": "https://wikipedia.org/wiki/Clearasil"}]}, {"year": "2000", "text": "<PERSON>, American baseball player and manager (b. 1920)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English-Australian cricketer and educator (b. 1907)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer and educator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer and educator (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, English architect, co-designed the Royal National Theatre (b. 1914)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English architect, co-designed the <a href=\"https://wikipedia.org/wiki/Royal_National_Theatre\" title=\"Royal National Theatre\">Royal National Theatre</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English architect, co-designed the <a href=\"https://wikipedia.org/wiki/Royal_National_Theatre\" title=\"Royal National Theatre\">Royal National Theatre</a> (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Den<PERSON>_Lasdun"}, {"title": "Royal National Theatre", "link": "https://wikipedia.org/wiki/Royal_National_Theatre"}]}, {"year": "2002", "text": "<PERSON>, French-Armenian director and playwright (b. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Armenian director and playwright (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Armenian director and playwright (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Slovenian sociologist and politician (b. 1932)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Jo%C5%BEe_Pu%C4%8Dnik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian sociologist and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C5%BEe_Pu%C4%8Dnik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian sociologist and politician (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C5%BEe_Pu%C4%8Dnik"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American actor, writer, and performance artist (b. 1941)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Spalding_Gray\" title=\"Spalding Gray\"><PERSON><PERSON></a>, American actor, writer, and performance artist (b. <a href=\"https://wikipedia.org/wiki/1941\" title=\"1941\">1941</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spalding_Gray\" title=\"Spalding Gray\"><PERSON><PERSON></a>, American actor, writer, and performance artist (b. <a href=\"https://wikipedia.org/wiki/1941\" title=\"1941\">1941</a>)", "links": [{"title": "Spalding Gray", "link": "https://wikipedia.org/wiki/Spalding_Gray"}, {"title": "1941", "link": "https://wikipedia.org/wiki/1941"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, French-German actress (b. 1961)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-German actress (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-German actress (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Solveig_Dommartin"}]}, {"year": "2007", "text": "<PERSON>, American psychologist, author, poet, and playwright (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist, author, poet, and playwright (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist, author, poet, and playwright (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, New Zealand mountaineer and explorer (b. 1919)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand mountaineer and explorer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand mountaineer and explorer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American businessman, co-founded <PERSON>'s Jr. (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Jr.\" title=\"<PERSON>'s Jr.\"><PERSON>'s Jr.</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Jr.\" title=\"<PERSON>'s Jr.\"><PERSON>'s Jr.</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>%27s_Jr."}]}, {"year": "2010", "text": "<PERSON><PERSON>, Austrian-Dutch humanitarian (b. 1909)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Dutch humanitarian (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Dutch humanitarian (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miep_Gies"}]}, {"year": "2010", "text": "<PERSON><PERSON>, French director, screenwriter, and critic (b. 1920)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French director, screenwriter, and critic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French director, screenwriter, and critic (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American actor, director, and producer (b. 1936)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and producer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and producer (b. 1936)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Iranian physicist and academic (b. 1980)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian physicist and academic (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian physicist and academic (b. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-<PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French journalist and photographer (b. 1968)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and photographer (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and photographer (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Jr, American-Canadian businessman and philanthropist (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Jr</a>, American-Canadian businessman and philanthropist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Jr</a>, American-Canadian businessman and philanthropist (b. 1942)", "links": [{"title": "<PERSON>, Jr", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr"}]}, {"year": "2012", "text": "<PERSON>, American basketball player (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English astrophysicist, astronomer, and academic (b. 1961)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrophysicist, astronomer, and academic (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrophysicist, astronomer, and academic (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English composer and conductor (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and conductor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and conductor (b. 1931)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "2013", "text": "<PERSON>, Italian businessman, founded the Forti Racing Team (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian businessman, founded the <a href=\"https://wikipedia.org/wiki/Forti\" title=\"Forti\">Forti Racing Team</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian businessman, founded the <a href=\"https://wikipedia.org/wiki/Forti\" title=\"Forti\">Forti Racing Team</a> (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guido_<PERSON>i"}, {"title": "Forti", "link": "https://wikipedia.org/wiki/Forti"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese general and politician, 3rd President of South Vietnam (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Leaders_of_South_Vietnam\" class=\"mw-redirect\" title=\"Leaders of South Vietnam\">President of South Vietnam</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Leaders_of_South_Vietnam\" class=\"mw-redirect\" title=\"Leaders of South Vietnam\">President of South Vietnam</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh"}, {"title": "Leaders of South Vietnam", "link": "https://wikipedia.org/wiki/Leaders_of_South_Vietnam"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Italian actress (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Welsh chemist, invented the breathalyzer (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh chemist, invented the <a href=\"https://wikipedia.org/wiki/Breathalyzer\" title=\"Breathalyzer\">breathalyzer</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh chemist, invented the <a href=\"https://wikipedia.org/wiki/Breathalyzer\" title=\"Breathalyzer\">breathalyzer</a> (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Breathalyzer", "link": "https://wikipedia.org/wiki/Breathalyzer"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Ethiopian runner (b. 1988)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON><PERSON><PERSON> Shu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian runner (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Shumy<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian runner (b. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "2013", "text": "<PERSON>, American programmer and activist (b. 1986)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American programmer and activist (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American programmer and activist (b. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Japanese actress (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ji"}]}, {"year": "2014", "text": "<PERSON>, Indian-Bangladeshi jurist and politician, Prime Minister of Bangladesh (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Bangladeshi jurist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bangladesh\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bangladesh\">Prime Minister of Bangladesh</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Bangladeshi jurist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bangladesh\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bangladesh\">Prime Minister of Bangladesh</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Bangladesh", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bangladesh"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Taiwanese educator and politician (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rong-rong\" title=\"<PERSON><PERSON> Trong-rong\"><PERSON><PERSON>ong</a>, Taiwanese educator and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rong-rong\" title=\"<PERSON><PERSON> Trong-rong\"><PERSON><PERSON>-<PERSON>ong</a>, Taiwanese educator and politician (b. 1935)", "links": [{"title": "<PERSON><PERSON>ong", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Trong-rong"}]}, {"year": "2014", "text": "<PERSON>, Israeli general and politician, 11th Prime Minister of Israel (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli general and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli general and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Hungarian footballer and coach (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Jen%C5%91_Buz%C3%A1nszky\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian footballer and coach (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jen%C5%91_Buz%C3%A1nszky\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian footballer and coach (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jen%C5%91_Buz%C3%A1nszky"}]}, {"year": "2015", "text": "<PERSON>, Swedish-Italian model and actress (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Italian model and actress (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Italian model and actress (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Bangladeshi director and producer (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zrul_<PERSON>\" title=\"Chashi Nazrul Islam\"><PERSON><PERSON></a>, Bangladeshi director and producer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rul_<PERSON>\" title=\"<PERSON>shi Nazrul Islam\"><PERSON><PERSON></a>, Bangladeshi director and producer (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American neuroscientist and academic (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroscientist and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroscientist and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American baseball player (b. 1919)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1937)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Malaysian politician and Chief Minister of Sarawak, Malaysia (b. 1944)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian politician and <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Sarawak\" class=\"mw-redirect\" title=\"Chief Minister of Sarawak\">Chief Minister of Sarawak</a>, <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian politician and <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Sarawak\" class=\"mw-redirect\" title=\"Chief Minister of Sarawak\">Chief Minister of Sarawak</a>, <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of Sarawak", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Sarawak"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}]}, {"year": "2018", "text": "<PERSON>, American murderer (b. 1925)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, British-Lebanese mathematician (b. 1929)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Lebanese mathematician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Lebanese mathematician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American actress and singer (b. 1924)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}