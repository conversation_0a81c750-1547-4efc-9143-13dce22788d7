{"date": "July 11", "url": "https://wikipedia.org/wiki/July_11", "data": {"Events": [{"year": "472", "text": "After being besieged in Rome by his own generals, Western Roman Emperor <PERSON><PERSON><PERSON> is captured in St. Peter's Basilica and put to death.", "html": "472 - After being besieged in Rome by his own generals, Western Roman Emperor <a href=\"https://wikipedia.org/wiki/Anthem<PERSON>\" title=\"<PERSON>them<PERSON>\"><PERSON><PERSON><PERSON></a> is captured in <a href=\"https://wikipedia.org/wiki/Old_St._Peter%27s_Basilica\" title=\"Old St. Peter's Basilica\">St. Peter's Basilica</a> and put to death.", "no_year_html": "After being besieged in Rome by his own generals, Western Roman Emperor <a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>\" title=\"<PERSON>them<PERSON>\"><PERSON><PERSON><PERSON></a> is captured in <a href=\"https://wikipedia.org/wiki/Old_St._Peter%27s_Basilica\" title=\"Old St. Peter's Basilica\">St. Peter's Basilica</a> and put to death.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anthemius"}, {"title": "Old St. Peter's Basilica", "link": "https://wikipedia.org/wiki/Old_St._Peter%27s_Basilica"}]}, {"year": "813", "text": "Byzantine emperor <PERSON>, under threat by conspiracies, abdicates in favor of his general <PERSON> the Armenian, and becomes a monk (under the name <PERSON><PERSON><PERSON>).", "html": "813 - Byzantine emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, under threat by conspiracies, abdicates in favor of his general <a href=\"https://wikipedia.org/wiki/<PERSON>_V_the_Armenian\" title=\"<PERSON> the Armenian\"><PERSON> the Armenian</a>, and becomes a <a href=\"https://wikipedia.org/wiki/Monk\" title=\"Monk\">monk</a> (under the name <PERSON><PERSON><PERSON>).", "no_year_html": "Byzantine emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, under threat by conspiracies, abdicates in favor of his general <a href=\"https://wikipedia.org/wiki/<PERSON>_V_the_Armenian\" title=\"<PERSON> the Armenian\"><PERSON> the Armenian</a>, and becomes a <a href=\"https://wikipedia.org/wiki/Monk\" title=\"Monk\">monk</a> (under the name <PERSON><PERSON><PERSON>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> the Armenian", "link": "https://wikipedia.org/wiki/Leo_V_the_Armenian"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Monk"}]}, {"year": "911", "text": "Signing of the Treaty of Saint-Clair-sur-Epte between Charles the Simple and Rollo of Normandy.", "html": "911 - Signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Saint-Clair-sur-Epte\" title=\"Treaty of Saint-Clair-sur-Epte\">Treaty of Saint-Clair-sur-Epte</a> between <a href=\"https://wikipedia.org/wiki/Charles_the_Simple\" title=\"Charles the Simple\"><PERSON> the Simple</a> and <a href=\"https://wikipedia.org/wiki/Rollo\" title=\"Rollo\">Rollo</a> of Normandy.", "no_year_html": "Signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Saint-Clair-sur-Epte\" title=\"Treaty of Saint-Clair-sur-Epte\">Treaty of Saint-Clair-sur-Epte</a> between <a href=\"https://wikipedia.org/wiki/Charles_the_Simple\" title=\"Charles the Simple\"><PERSON> the Simple</a> and <a href=\"https://wikipedia.org/wiki/Rollo\" title=\"Rollo\"><PERSON><PERSON></a> of Normandy.", "links": [{"title": "Treaty of Saint-Clair-sur-Epte", "link": "https://wikipedia.org/wiki/Treaty_of_Saint-Clair-sur-Epte"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Simple"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rollo"}]}, {"year": "1174", "text": "<PERSON> <PERSON>, 13, becomes King of Jerusalem, with <PERSON>, Count of Tripoli as regent and <PERSON> of Tyre as chancellor.", "html": "1174 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" class=\"mw-redirect\" title=\"<PERSON> IV\"><PERSON> IV</a>, 13, becomes <a href=\"https://wikipedia.org/wiki/King_of_Jerusalem\" title=\"King of Jerusalem\">King of Jerusalem</a>, with <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Tripoli\" title=\"<PERSON>, Count of Tripoli\"><PERSON>, Count of Tripoli</a> as regent and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Tyre\"><PERSON> of Tyre</a> as chancellor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" class=\"mw-redirect\" title=\"<PERSON> IV\"><PERSON> IV</a>, 13, becomes <a href=\"https://wikipedia.org/wiki/King_of_Jerusalem\" title=\"King of Jerusalem\">King of Jerusalem</a>, with <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Tripoli\" title=\"<PERSON>, Count of Tripoli\"><PERSON>, Count of Tripoli</a> as regent and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tyre\" title=\"<PERSON> of Tyre\"><PERSON> of Tyre</a> as chancellor.", "links": [{"title": "Baldwin IV", "link": "https://wikipedia.org/wiki/<PERSON>_IV"}, {"title": "King of Jerusalem", "link": "https://wikipedia.org/wiki/King_of_Jerusalem"}, {"title": "<PERSON>, Count of Tripoli", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Tripoli"}, {"title": "<PERSON> of Tyre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1302", "text": "Battle of the Golden Spurs (Guldensporenslag in Dutch): A coalition around the Flemish cities defeats the king of France's royal army.", "html": "1302 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Golden_Spurs\" title=\"Battle of the Golden Spurs\">Battle of the Golden Spurs</a> (<i>Guldensporenslag</i> in Dutch): A coalition around the <a href=\"https://wikipedia.org/wiki/Flanders\" title=\"Flanders\">Flemish</a> cities defeats the king of France's royal army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Golden_Spurs\" title=\"Battle of the Golden Spurs\">Battle of the Golden Spurs</a> (<i>Guldensporenslag</i> in Dutch): A coalition around the <a href=\"https://wikipedia.org/wiki/Flanders\" title=\"Flanders\">Flemish</a> cities defeats the king of France's royal army.", "links": [{"title": "Battle of the Golden Spurs", "link": "https://wikipedia.org/wiki/Battle_of_the_Golden_Spurs"}, {"title": "Flanders", "link": "https://wikipedia.org/wiki/Flanders"}]}, {"year": "1346", "text": "<PERSON>, Count of Luxembourg and King of Bohemia, is elected King of the Romans.", "html": "1346 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV</a>, <a href=\"https://wikipedia.org/wiki/Count_of_Luxembourg\" class=\"mw-redirect\" title=\"Count of Luxembourg\">Count of Luxembourg</a> and <a href=\"https://wikipedia.org/wiki/King_of_Bohemia\" class=\"mw-redirect\" title=\"King of Bohemia\">King of Bohemia</a>, is elected <a href=\"https://wikipedia.org/wiki/King_of_the_Romans\" title=\"King of the Romans\">King of the Romans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV</a>, <a href=\"https://wikipedia.org/wiki/Count_of_Luxembourg\" class=\"mw-redirect\" title=\"Count of Luxembourg\">Count of Luxembourg</a> and <a href=\"https://wikipedia.org/wiki/King_of_Bohemia\" class=\"mw-redirect\" title=\"King of Bohemia\">King of Bohemia</a>, is elected <a href=\"https://wikipedia.org/wiki/King_of_the_Romans\" title=\"King of the Romans\">King of the Romans</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Count of Luxembourg", "link": "https://wikipedia.org/wiki/Count_of_Luxembourg"}, {"title": "King of Bohemia", "link": "https://wikipedia.org/wiki/King_of_Bohemia"}, {"title": "King of the Romans", "link": "https://wikipedia.org/wiki/King_of_the_Romans"}]}, {"year": "1405", "text": "Ming admiral <PERSON> sets sail to explore the world for the first time.", "html": "1405 - <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming</a> admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> He</a> sets sail <a href=\"https://wikipedia.org/wiki/Ming_treasure_voyages\" title=\"Ming treasure voyages\">to explore the world</a> for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming</a> admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> He</a> sets sail <a href=\"https://wikipedia.org/wiki/Ming_treasure_voyages\" title=\"Ming treasure voyages\">to explore the world</a> for the first time.", "links": [{"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ming treasure voyages", "link": "https://wikipedia.org/wiki/Ming_treasure_voyages"}]}, {"year": "1410", "text": "Ottoman Interregnum: <PERSON><PERSON><PERSON><PERSON> defeats his brother <PERSON> outside the Ottoman capital, Edirne.", "html": "1410 - <a href=\"https://wikipedia.org/wiki/Ottoman_Interregnum\" title=\"Ottoman Interregnum\">Ottoman Interregnum</a>: <a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_%C3%87elebi\" title=\"Süleyman Çelebi\"><PERSON>üley<PERSON> Çelebi</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Edirne_(1410)\" title=\"Battle of Edirne (1410)\">defeats</a> his brother <a href=\"https://wikipedia.org/wiki/Musa_%C3%87elebi\" title=\"<PERSON> Çelebi\"><PERSON> Çelebi</a> outside the Ottoman capital, <a href=\"https://wikipedia.org/wiki/Edirne\" title=\"Edirne\">Edirne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman_Interregnum\" title=\"Ottoman Interregnum\">Ottoman Interregnum</a>: <a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_%C3%87elebi\" title=\"Süleyman Çelebi\"><PERSON><PERSON>leyman Çelebi</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Edirne_(1410)\" title=\"Battle of Edirne (1410)\">defeats</a> his brother <a href=\"https://wikipedia.org/wiki/Musa_%C3%87elebi\" title=\"Musa Çelebi\"><PERSON> Çelebi</a> outside the Ottoman capital, <a href=\"https://wikipedia.org/wiki/Edirne\" title=\"Edirne\">Edirne</a>.", "links": [{"title": "Ottoman Interregnum", "link": "https://wikipedia.org/wiki/Ottoman_Interregnum"}, {"title": "Süleyman <PERSON>elebi", "link": "https://wikipedia.org/wiki/S%C3%BCleyman_%C3%87elebi"}, {"title": "Battle of Edirne (1410)", "link": "https://wikipedia.org/wiki/Battle_of_Edirne_(1410)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Musa_%C3%87elebi"}, {"title": "Ed<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edirne"}]}, {"year": "1476", "text": "<PERSON><PERSON><PERSON><PERSON> is appointed bishop of Coutances.", "html": "1476 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Julius_II\" title=\"Pope Julius II\"><PERSON><PERSON><PERSON><PERSON></a> is appointed bishop of <a href=\"https://wikipedia.org/wiki/Coutances\" title=\"Coutances\">Coutances</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Julius_<PERSON>\" title=\"Pope Julius II\"><PERSON><PERSON><PERSON><PERSON></a> is appointed bishop of <a href=\"https://wikipedia.org/wiki/Coutances\" title=\"Coutances\">Coutances</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Coutances", "link": "https://wikipedia.org/wiki/Coutances"}]}, {"year": "1576", "text": "While exploring the North Atlantic Ocean in an attempt to find the Northwest Passage, <PERSON> sights Greenland, mistaking it for the hypothesized (but non-existent) island of \"Frisland\".", "html": "1576 - While exploring the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean#Northern_Atlantic\" title=\"Atlantic Ocean\">North Atlantic Ocean</a> in an attempt to find the <a href=\"https://wikipedia.org/wiki/Northwest_Passage\" title=\"Northwest Passage\">Northwest Passage</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sights <a href=\"https://wikipedia.org/wiki/Greenland\" title=\"Greenland\">Greenland</a>, mistaking it for the hypothesized (but non-existent) island of \"Frisland\".", "no_year_html": "While exploring the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean#Northern_Atlantic\" title=\"Atlantic Ocean\">North Atlantic Ocean</a> in an attempt to find the <a href=\"https://wikipedia.org/wiki/Northwest_Passage\" title=\"Northwest Passage\">Northwest Passage</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sights <a href=\"https://wikipedia.org/wiki/Greenland\" title=\"Greenland\">Greenland</a>, mistaking it for the hypothesized (but non-existent) island of \"Frisland\".", "links": [{"title": "Atlantic Ocean", "link": "https://wikipedia.org/wiki/Atlantic_Ocean#Northern_Atlantic"}, {"title": "Northwest Passage", "link": "https://wikipedia.org/wiki/Northwest_Passage"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Greenland", "link": "https://wikipedia.org/wiki/Greenland"}]}, {"year": "1616", "text": "<PERSON> returns to Quebec.", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}]}, {"year": "1735", "text": "Mathematical calculations suggest that it is on this day that dwarf planet <PERSON><PERSON><PERSON> moved inside the orbit of Neptune for the last time before 1979.", "html": "1735 - Mathematical calculations suggest that it is on this day that <a href=\"https://wikipedia.org/wiki/Dwarf_planet\" title=\"Dwarf planet\">dwarf planet</a> <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> moved inside the orbit of <a href=\"https://wikipedia.org/wiki/Neptune\" title=\"Neptune\">Neptune</a> for the last time before 1979.", "no_year_html": "Mathematical calculations suggest that it is on this day that <a href=\"https://wikipedia.org/wiki/Dwarf_planet\" title=\"Dwarf planet\">dwarf planet</a> <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> moved inside the orbit of <a href=\"https://wikipedia.org/wiki/Neptune\" title=\"Neptune\">Neptune</a> for the last time before 1979.", "links": [{"title": "Dwarf planet", "link": "https://wikipedia.org/wiki/Dwarf_planet"}, {"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}, {"title": "Neptune", "link": "https://wikipedia.org/wiki/Neptune"}]}, {"year": "1789", "text": "<PERSON> is dismissed as France's Finance Minister sparking the Storming of the Bastille.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is dismissed as France's Finance Minister sparking the <a href=\"https://wikipedia.org/wiki/Storming_of_the_Bastille\" title=\"Storming of the Bastille\">Storming of the Bastille</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is dismissed as France's Finance Minister sparking the <a href=\"https://wikipedia.org/wiki/Storming_of_the_Bastille\" title=\"Storming of the Bastille\">Storming of the Bastille</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Storming of the Bastille", "link": "https://wikipedia.org/wiki/Storming_of_the_Bastille"}]}, {"year": "1796", "text": "The United States takes possession of Detroit from Great Britain under terms of the Jay Treaty.", "html": "1796 - The United States takes possession of <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a> from <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a> under terms of the <a href=\"https://wikipedia.org/wiki/Jay_Treaty\" title=\"Jay Treaty\">Jay Treaty</a>.", "no_year_html": "The United States takes possession of <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a> from <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a> under terms of the <a href=\"https://wikipedia.org/wiki/Jay_Treaty\" title=\"Jay Treaty\">Jay Treaty</a>.", "links": [{"title": "Detroit", "link": "https://wikipedia.org/wiki/Detroit"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Jay <PERSON>", "link": "https://wikipedia.org/wiki/Jay_Treaty"}]}, {"year": "1798", "text": "The United States Marine Corps is re-established; they had been disbanded after the American Revolutionary War.", "html": "1798 - The <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marine Corps</a> is re-established; they had been disbanded after the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marine Corps</a> is re-established; they had been disbanded after the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>.", "links": [{"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1801", "text": "French astronomer <PERSON><PERSON><PERSON> makes his first comet discovery. In the next 27 years he discovers another 36 comets, more than any other person in history.", "html": "1801 - French astronomer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> makes his first <a href=\"https://wikipedia.org/wiki/Comet\" title=\"Comet\">comet</a> discovery. In the next 27 years he discovers another 36 comets, more than any other person in history.", "no_year_html": "French astronomer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> makes his first <a href=\"https://wikipedia.org/wiki/Comet\" title=\"Comet\">comet</a> discovery. In the next 27 years he discovers another 36 comets, more than any other person in history.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Comet", "link": "https://wikipedia.org/wiki/Comet"}]}, {"year": "1804", "text": "A duel occurs in which the Vice President of the United States <PERSON> mortally wounds former Secretary of the Treasury <PERSON>.", "html": "1804 - <a href=\"https://wikipedia.org/wiki/Burr%E2%80%93Hamilton_duel\" title=\"<PERSON><PERSON>Hamilton duel\">A duel occurs</a> in which the <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> mortally wounds former <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">Secretary of the Treasury</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Burr%E2%80%93Hamilton_duel\" title=\"<PERSON><PERSON>Hamilton duel\">A duel occurs</a> in which the <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> mortally wounds former <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">Secretary of the Treasury</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Burr-<PERSON> duel", "link": "https://wikipedia.org/wiki/Burr%E2%80%93Hamilton_duel"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "Noongar Australian aboriginal warrior <PERSON><PERSON>, wanted for the murder of white colonists in Western Australia, is killed.", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>ongar\" title=\"<PERSON>ong<PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Indigenous_Australians\" title=\"Indigenous Australians\">Australian aboriginal</a> warrior <a href=\"https://wikipedia.org/wiki/Yagan\" title=\"Yagan\"><PERSON><PERSON></a>, wanted for the murder of white colonists in Western Australia, is killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ong<PERSON>\" title=\"<PERSON>ong<PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Indigenous_Australians\" title=\"Indigenous Australians\">Australian aboriginal</a> warrior <a href=\"https://wikipedia.org/wiki/Yagan\" title=\"Yagan\"><PERSON><PERSON></a>, wanted for the murder of white colonists in Western Australia, is killed.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Noongar"}, {"title": "Indigenous Australians", "link": "https://wikipedia.org/wiki/Indigenous_Australians"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yagan"}]}, {"year": "1836", "text": "The Fly-fisher's Entomology is published by <PERSON>. The book transformed the sport and went to many editions.", "html": "1836 - <i><a href=\"https://wikipedia.org/wiki/The_Fly-fisher%27s_Entomology\" title=\"The Fly-fisher's Entomology\">The Fly-fisher's Entomology</a></i> is published by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. The book transformed the sport and went to many editions.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Fly-fisher%27s_Entomology\" title=\"The Fly-fisher's Entomology\">The Fly-fisher's Entomology</a></i> is published by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. The book transformed the sport and went to many editions.", "links": [{"title": "The Fly-fisher's Entomology", "link": "https://wikipedia.org/wiki/The_Fly-fisher%27s_Entomology"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "Waterloo railway station in London opens.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/London_Waterloo_station\" title=\"London Waterloo station\">Waterloo</a> railway station in London opens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/London_Waterloo_station\" title=\"London Waterloo station\">Waterloo</a> railway station in London opens.", "links": [{"title": "London Waterloo station", "link": "https://wikipedia.org/wiki/London_Waterloo_station"}]}, {"year": "1864", "text": "American Civil War: Battle of Fort Stevens; Confederate forces attempt to invade Washington, D.C.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Stevens\" title=\"Battle of Fort Stevens\">Battle of Fort Stevens</a>; <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces attempt to invade Washington, D.C.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Stevens\" title=\"Battle of Fort Stevens\">Battle of Fort Stevens</a>; <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces attempt to invade Washington, D.C.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Fort Stevens", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Stevens"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1882", "text": "The British Mediterranean Fleet begins the Bombardment of Alexandria in Egypt as part of the Anglo-Egyptian War.", "html": "1882 - The British <a href=\"https://wikipedia.org/wiki/Mediterranean_Fleet\" title=\"Mediterranean Fleet\">Mediterranean Fleet</a> begins the <a href=\"https://wikipedia.org/wiki/Bombardment_of_Alexandria\" title=\"Bombardment of Alexandria\">Bombardment of Alexandria</a> in Egypt as part of the <a href=\"https://wikipedia.org/wiki/Anglo-Egyptian_War\" title=\"Anglo-Egyptian War\">Anglo-Egyptian War</a>.", "no_year_html": "The British <a href=\"https://wikipedia.org/wiki/Mediterranean_Fleet\" title=\"Mediterranean Fleet\">Mediterranean Fleet</a> begins the <a href=\"https://wikipedia.org/wiki/Bombardment_of_Alexandria\" title=\"Bombardment of Alexandria\">Bombardment of Alexandria</a> in Egypt as part of the <a href=\"https://wikipedia.org/wiki/Anglo-Egyptian_War\" title=\"Anglo-Egyptian War\">Anglo-Egyptian War</a>.", "links": [{"title": "Mediterranean Fleet", "link": "https://wikipedia.org/wiki/Mediterranean_Fleet"}, {"title": "Bombardment of Alexandria", "link": "https://wikipedia.org/wiki/Bombardment_of_Alexandria"}, {"title": "Anglo-Egyptian War", "link": "https://wikipedia.org/wiki/Anglo-Egyptian_War"}]}, {"year": "1889", "text": "Tijuana, Mexico, is founded.", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Tijuana\" title=\"Tijuana\">Tijuana</a>, Mexico, is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tijuana\" title=\"Tijuana\">Tijuana</a>, Mexico, is founded.", "links": [{"title": "Tijuana", "link": "https://wikipedia.org/wiki/Tijuana"}]}, {"year": "1893", "text": "The first cultured pearl is obtained by <PERSON><PERSON><PERSON><PERSON>.", "html": "1893 - The first cultured <a href=\"https://wikipedia.org/wiki/Pearl\" title=\"Pearl\">pearl</a> is obtained by <a href=\"https://wikipedia.org/wiki/Mikimoto_K%C5%8Dkichi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "The first cultured <a href=\"https://wikipedia.org/wiki/Pearl\" title=\"<PERSON>\">pearl</a> is obtained by <a href=\"https://wikipedia.org/wiki/Mikimoto_K%C5%8Dkichi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>ōki<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Pearl", "link": "https://wikipedia.org/wiki/Pearl"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikimoto_K%C5%8Dkichi"}]}, {"year": "1893", "text": "A revolution led by the liberal general and politician <PERSON> takes over state power in Nicaragua.", "html": "1893 - A <a href=\"https://wikipedia.org/wiki/Revolution\" title=\"Revolution\">revolution</a> led by the liberal general and politician <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes over state power in <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Revolution\" title=\"Revolution\">revolution</a> led by the liberal general and politician <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes over state power in <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "links": [{"title": "Revolution", "link": "https://wikipedia.org/wiki/Revolution"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_Zelaya"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}]}, {"year": "1897", "text": "Salomon August <PERSON><PERSON> leaves Spitsbergen to attempt to reach the North Pole by balloon.", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Salomon_August_Andr%C3%A9e\" title=\"Salomon August <PERSON>\">Salomon August <PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Spitsbergen\" title=\"Spitsbergen\">Spitsbergen</a> to attempt to reach the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a> by <a href=\"https://wikipedia.org/wiki/Balloon\" title=\"Balloon\">balloon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salomon_August_Andr%C3%A9e\" title=\"Salomon August <PERSON>\">Salomon August <PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Spitsbergen\" title=\"Spitsbergen\">Spitsbergen</a> to attempt to reach the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a> by <a href=\"https://wikipedia.org/wiki/Balloon\" title=\"Balloon\">balloon</a>.", "links": [{"title": "Salomon <PERSON>", "link": "https://wikipedia.org/wiki/Salomon_August_Andr%C3%A9e"}, {"title": "Spitsbergen", "link": "https://wikipedia.org/wiki/Spitsbergen"}, {"title": "North Pole", "link": "https://wikipedia.org/wiki/North_Pole"}, {"title": "Balloon", "link": "https://wikipedia.org/wiki/<PERSON>oon"}]}, {"year": "1899", "text": "Fiat founded by <PERSON> in Turin, Italy.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Fiat_Automobiles\" class=\"mw-redirect\" title=\"Fiat Automobiles\">Fiat</a> founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Turin,_Italy\" class=\"mw-redirect\" title=\"Turin, Italy\">Turin, Italy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fiat_Automobiles\" class=\"mw-redirect\" title=\"Fiat Automobiles\">Fiat</a> founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Turin,_Italy\" class=\"mw-redirect\" title=\"Turin, Italy\">Turin, Italy</a>.", "links": [{"title": "Fiat Automobiles", "link": "https://wikipedia.org/wiki/Fiat_Automobiles"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Turin, Italy", "link": "https://wikipedia.org/wiki/Turin,_Italy"}]}, {"year": "1906", "text": "Murder of <PERSON> by <PERSON> in the United States, inspiration for <PERSON>'s An American Tragedy.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">Murder of <PERSON></a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the United States, inspiration for <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/An_American_Tragedy\" title=\"An American Tragedy\">An American Tragedy</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">Murder of <PERSON></a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the United States, inspiration for <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/An_American_Tragedy\" title=\"An American Tragedy\">An American Tragedy</a></i>.", "links": [{"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chester_Gillette"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "An American Tragedy", "link": "https://wikipedia.org/wiki/An_American_Tragedy"}]}, {"year": "1914", "text": "<PERSON> makes his debut in Major League Baseball.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Babe_Ruth\" title=\"Babe Ruth\"><PERSON></a> makes his debut in <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Babe_Ruth\" title=\"Babe Ruth\"><PERSON></a> makes his debut in <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}]}, {"year": "1914", "text": "The US Navy launches the USS Nevada (BB-36) as its first standard-type battleship.", "html": "1914 - The US Navy launches the <a href=\"https://wikipedia.org/wiki/USS_Nevada_(BB-36)\" title=\"USS Nevada (BB-36)\">USS <i>Nevada</i> (BB-36)</a> as its first <a href=\"https://wikipedia.org/wiki/Standard-type_battleship\" title=\"Standard-type battleship\">standard-type battleship</a>.", "no_year_html": "The US Navy launches the <a href=\"https://wikipedia.org/wiki/USS_Nevada_(BB-36)\" title=\"USS Nevada (BB-36)\">USS <i>Nevada</i> (BB-36)</a> as its first <a href=\"https://wikipedia.org/wiki/Standard-type_battleship\" title=\"Standard-type battleship\">standard-type battleship</a>.", "links": [{"title": "USS Nevada (BB-36)", "link": "https://wikipedia.org/wiki/USS_Nevada_(BB-36)"}, {"title": "Standard-type battleship", "link": "https://wikipedia.org/wiki/Standard-type_battleship"}]}, {"year": "1919", "text": "The eight-hour day and free Sunday become law for workers in the Netherlands.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Eight-hour_day\" class=\"mw-redirect\" title=\"Eight-hour day\">eight-hour day</a> and free Sunday become law for workers in the Netherlands.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Eight-hour_day\" class=\"mw-redirect\" title=\"Eight-hour day\">eight-hour day</a> and free Sunday become law for workers in the Netherlands.", "links": [{"title": "Eight-hour day", "link": "https://wikipedia.org/wiki/Eight-hour_day"}]}, {"year": "1920", "text": "In the East Prussian plebiscite the local populace decides to remain with Weimar Germany.", "html": "1920 - In the <a href=\"https://wikipedia.org/wiki/East_Prussian_plebiscite\" class=\"mw-redirect\" title=\"East Prussian plebiscite\">East Prussian plebiscite</a> the local populace decides to remain with <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Weimar Germany</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/East_Prussian_plebiscite\" class=\"mw-redirect\" title=\"East Prussian plebiscite\">East Prussian plebiscite</a> the local populace decides to remain with <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Weimar Germany</a>.", "links": [{"title": "East Prussian plebiscite", "link": "https://wikipedia.org/wiki/East_Prussian_plebiscite"}, {"title": "Weimar Republic", "link": "https://wikipedia.org/wiki/Weimar_Republic"}]}, {"year": "1921", "text": "A truce in the Irish War of Independence comes into effect.", "html": "1921 - A truce in the <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a> comes into effect.", "no_year_html": "A truce in the <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a> comes into effect.", "links": [{"title": "Irish War of Independence", "link": "https://wikipedia.org/wiki/Irish_War_of_Independence"}]}, {"year": "1921", "text": "The Red Army captures Mongolia from the White Army and establishes the Mongolian People's Republic.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> captures <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a> from the <a href=\"https://wikipedia.org/wiki/White_movement#Structure\" title=\"White movement\">White Army</a> and establishes the <a href=\"https://wikipedia.org/wiki/Mongolian_People%27s_Republic\" title=\"Mongolian People's Republic\">Mongolian People's Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> captures <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a> from the <a href=\"https://wikipedia.org/wiki/White_movement#Structure\" title=\"White movement\">White Army</a> and establishes the <a href=\"https://wikipedia.org/wiki/Mongolian_People%27s_Republic\" title=\"Mongolian People's Republic\">Mongolian People's Republic</a>.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Mongolia", "link": "https://wikipedia.org/wiki/Mongolia"}, {"title": "White movement", "link": "https://wikipedia.org/wiki/White_movement#Structure"}, {"title": "Mongolian People's Republic", "link": "https://wikipedia.org/wiki/Mongolian_People%27s_Republic"}]}, {"year": "1921", "text": "Former president of the United States <PERSON> is sworn in as 10th chief justice of the U.S. Supreme Court, becoming the only person ever to hold both offices.", "html": "1921 - Former <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">president of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as 10th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">chief justice</a> of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a>, becoming the only person ever to hold both offices.", "no_year_html": "Former <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">president of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as 10th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">chief justice</a> of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a>, becoming the only person ever to hold both offices.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}]}, {"year": "1922", "text": "The Hollywood Bowl opens.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Hollywood_Bowl\" title=\"Hollywood Bowl\">Hollywood Bowl</a> opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hollywood_Bowl\" title=\"Hollywood Bowl\">Hollywood Bowl</a> opens.", "links": [{"title": "Hollywood Bowl", "link": "https://wikipedia.org/wiki/Hollywood_Bowl"}]}, {"year": "1924", "text": "<PERSON> won the gold medal in 400m at the 1924 Paris Olympics, after refusing to run in the heats for 100m, his favoured distance, on a Sunday.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> won the gold medal in 400m at the 1924 Paris Olympics, after refusing to run in the heats for 100m, his favoured distance, on a Sunday.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> won the gold medal in 400m at the 1924 Paris Olympics, after refusing to run in the heats for 100m, his favoured distance, on a Sunday.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON> of Germany flies his large human-powered aircraft, the Zaschka Human-Power Aircraft, about 20 meters at Berlin Tempelhof Airport without assisted take-off.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of Germany flies his large <a href=\"https://wikipedia.org/wiki/Human-powered_aircraft\" title=\"Human-powered aircraft\">human-powered aircraft</a>, the <i>Zaschka Human-Power Aircraft</i>, about 20 meters at <a href=\"https://wikipedia.org/wiki/Berlin_Tempelhof_Airport\" title=\"Berlin Tempelhof Airport\">Berlin Tempelhof Airport</a> without <a href=\"https://wikipedia.org/wiki/Assisted_take-off\" title=\"Assisted take-off\">assisted take-off</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of Germany flies his large <a href=\"https://wikipedia.org/wiki/Human-powered_aircraft\" title=\"Human-powered aircraft\">human-powered aircraft</a>, the <i>Zaschka Human-Power Aircraft</i>, about 20 meters at <a href=\"https://wikipedia.org/wiki/Berlin_Tempelhof_Airport\" title=\"Berlin Tempelhof Airport\">Berlin Tempelhof Airport</a> without <a href=\"https://wikipedia.org/wiki/Assisted_take-off\" title=\"Assisted take-off\">assisted take-off</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Human-powered aircraft", "link": "https://wikipedia.org/wiki/Human-powered_aircraft"}, {"title": "Berlin Tempelhof Airport", "link": "https://wikipedia.org/wiki/Berlin_Tempelhof_Airport"}, {"title": "Assisted take-off", "link": "https://wikipedia.org/wiki/Assisted_take-off"}]}, {"year": "1936", "text": "The Triborough Bridge in New York City is opened to traffic.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/Triborough_Bridge\" class=\"mw-redirect\" title=\"Triborough Bridge\">Triborough Bridge</a> in New York City is opened to traffic.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Triborough_Bridge\" class=\"mw-redirect\" title=\"Triborough Bridge\">Triborough Bridge</a> in New York City is opened to traffic.", "links": [{"title": "Triborough Bridge", "link": "https://wikipedia.org/wiki/Triborough_Bridge"}]}, {"year": "1940", "text": "World War II: <PERSON>hy <PERSON> regime is formally established. <PERSON> becomes Chief of the French State.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy France</a> regime is formally established. <a href=\"https://wikipedia.org/wiki/Philippe_P%C3%A9tain\" title=\"<PERSON>\"><PERSON></a> becomes Chief of the French State.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy France</a> regime is formally established. <a href=\"https://wikipedia.org/wiki/Philippe_P%C3%A9tain\" title=\"<PERSON>\"><PERSON></a> becomes Chief of the French State.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vichy_France"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Philippe_P%C3%A9tain"}]}, {"year": "1941", "text": "The Northern Rhodesian Labour Party holds its first congress in Nkana.", "html": "1941 - The <a href=\"https://wikipedia.org/wiki/Northern_Rhodesian_Labour_Party\" title=\"Northern Rhodesian Labour Party\">Northern Rhodesian Labour Party</a> holds its first congress in <a href=\"https://wikipedia.org/wiki/Nkana\" title=\"Nkana\">Nkana</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Northern_Rhodesian_Labour_Party\" title=\"Northern Rhodesian Labour Party\">Northern Rhodesian Labour Party</a> holds its first congress in <a href=\"https://wikipedia.org/wiki/Nkana\" title=\"Nkan<PERSON>\">Nkana</a>.", "links": [{"title": "Northern Rhodesian Labour Party", "link": "https://wikipedia.org/wiki/Northern_Rhodesian_Labour_Party"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>kana"}]}, {"year": "1943", "text": "Massacres of Poles in Volhynia and Eastern Galicia by the Ukrainian Insurgent Army within the Reichskommissariat Ukraine (Volhynia) peak.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Massacres_of_Poles_in_Volhynia_and_Eastern_Galicia\" title=\"Massacres of Poles in Volhynia and Eastern Galicia\">Massacres of Poles in Volhynia and Eastern Galicia</a> by the <a href=\"https://wikipedia.org/wiki/Ukrainian_Insurgent_Army\" title=\"Ukrainian Insurgent Army\">Ukrainian Insurgent Army</a> within the <a href=\"https://wikipedia.org/wiki/Reichskommissariat_Ukraine\" title=\"Reichskommissariat Ukraine\">Reichskommissariat Ukraine</a> (<a href=\"https://wikipedia.org/wiki/Volhynia\" title=\"Volhynia\">Volhynia</a>) peak.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Massacres_of_Poles_in_Volhynia_and_Eastern_Galicia\" title=\"Massacres of Poles in Volhynia and Eastern Galicia\">Massacres of Poles in Volhynia and Eastern Galicia</a> by the <a href=\"https://wikipedia.org/wiki/Ukrainian_Insurgent_Army\" title=\"Ukrainian Insurgent Army\">Ukrainian Insurgent Army</a> within the <a href=\"https://wikipedia.org/wiki/Reichskommissariat_Ukraine\" title=\"Reichskommissariat Ukraine\">Reichskommissariat Ukraine</a> (<a href=\"https://wikipedia.org/wiki/Volhynia\" title=\"Volhynia\">Volhynia</a>) peak.", "links": [{"title": "Massacres of Poles in Volhynia and Eastern Galicia", "link": "https://wikipedia.org/wiki/Massacres_of_Poles_in_Volhynia_and_Eastern_Galicia"}, {"title": "Ukrainian Insurgent Army", "link": "https://wikipedia.org/wiki/Ukrainian_Insurgent_Army"}, {"title": "Reichskommissariat Ukraine", "link": "https://wikipedia.org/wiki/Reichskommissariat_Ukraine"}, {"title": "Volhynia", "link": "https://wikipedia.org/wiki/Volhynia"}]}, {"year": "1943", "text": "World War II: Allied invasion of Sicily: German and Italian troops launch a counter-attack on Allied forces in Sicily.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Allied_invasion_of_Sicily\" title=\"Allied invasion of Sicily\">Allied invasion of Sicily</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> and Italian troops launch a <a href=\"https://wikipedia.org/wiki/Battle_of_Gela_(1943)\" title=\"Battle of Gela (1943)\">counter-attack</a> on <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied forces</a> in <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Allied_invasion_of_Sicily\" title=\"Allied invasion of Sicily\">Allied invasion of Sicily</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> and Italian troops launch a <a href=\"https://wikipedia.org/wiki/Battle_of_Gela_(1943)\" title=\"Battle of Gela (1943)\">counter-attack</a> on <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied forces</a> in <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a>.", "links": [{"title": "Allied invasion of Sicily", "link": "https://wikipedia.org/wiki/Allied_invasion_of_Sicily"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Battle of Gela (1943)", "link": "https://wikipedia.org/wiki/Battle_of_Gela_(1943)"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Sicily", "link": "https://wikipedia.org/wiki/Sicily"}]}, {"year": "1947", "text": "The Exodus 1947 heads to Palestine from France.", "html": "1947 - The <i><a href=\"https://wikipedia.org/wiki/SS_Exodus\" title=\"SS Exodus\">Exodus 1947</a></i> heads to Palestine from France.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/SS_Exodus\" title=\"SS Exodus\">Exodus 1947</a></i> heads to Palestine from France.", "links": [{"title": "SS Exodus", "link": "https://wikipedia.org/wiki/SS_Exodus"}]}, {"year": "1950", "text": "Pakistan joins the International Monetary Fund and the International Bank.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> joins the <a href=\"https://wikipedia.org/wiki/International_Monetary_Fund\" title=\"International Monetary Fund\">International Monetary Fund</a> and the International Bank.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> joins the <a href=\"https://wikipedia.org/wiki/International_Monetary_Fund\" title=\"International Monetary Fund\">International Monetary Fund</a> and the International Bank.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "International Monetary Fund", "link": "https://wikipedia.org/wiki/International_Monetary_Fund"}]}, {"year": "1957", "text": "Prince <PERSON><PERSON> IV inherits the office of <PERSON><PERSON> as the 49th Imam of Shia Imami <PERSON>i worldwide, after the death of Sir <PERSON> <PERSON><PERSON><PERSON>.", "html": "1957 - Prince <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>ga_Khan_IV\" title=\"<PERSON>ga Khan IV\"><PERSON><PERSON> Khan IV</a> inherits the office of <PERSON><PERSON> as the 49th Imam of Shia Imami <a href=\"https://wikipedia.org/wiki/Isma%27ilism\" title=\"<PERSON><PERSON><PERSON>ilism\"><PERSON><PERSON><PERSON><PERSON><PERSON>i</a> worldwide, after the death of Sir <PERSON> <a href=\"https://wikipedia.org/wiki/Aga_Khan_III\" title=\"Aga Khan III\"><PERSON>ga Khan III</a>.", "no_year_html": "Prince <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Khan_IV\" title=\"<PERSON>ga Khan IV\"><PERSON><PERSON> Khan IV</a> inherits the office of <PERSON><PERSON> as the 49th Imam of Shia Imami <a href=\"https://wikipedia.org/wiki/Isma%27ilism\" title=\"Is<PERSON>'ilism\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> worldwide, after the death of Sir <PERSON> <a href=\"https://wikipedia.org/wiki/Aga_Khan_III\" title=\"Aga Khan III\"><PERSON>ga Khan III</a>.", "links": [{"title": "<PERSON><PERSON> Khan IV", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_IV"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isma%27ilism"}, {"title": "<PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/A<PERSON>_Khan_<PERSON>"}]}, {"year": "1960", "text": "France legislates for the independence of Dahomey (later Benin), Upper Volta (later Burkina Faso) and Niger.", "html": "1960 - France legislates for the independence of <a href=\"https://wikipedia.org/wiki/Republic_of_Dahomey\" title=\"Republic of Dahomey\">Dahomey</a> (later <a href=\"https://wikipedia.org/wiki/Benin\" title=\"Benin\">Benin</a>), <a href=\"https://wikipedia.org/wiki/Republic_of_Upper_Volta\" title=\"Republic of Upper Volta\">Upper Volta</a> (later <a href=\"https://wikipedia.org/wiki/Burkina_Faso\" title=\"Burkina Faso\">Burkina Faso</a>) and <a href=\"https://wikipedia.org/wiki/Niger\" title=\"Niger\">Niger</a>.", "no_year_html": "France legislates for the independence of <a href=\"https://wikipedia.org/wiki/Republic_of_Dahomey\" title=\"Republic of Dahomey\">Dahomey</a> (later <a href=\"https://wikipedia.org/wiki/Benin\" title=\"Benin\">Benin</a>), <a href=\"https://wikipedia.org/wiki/Republic_of_Upper_Volta\" title=\"Republic of Upper Volta\">Upper Volta</a> (later <a href=\"https://wikipedia.org/wiki/Burkina_Faso\" title=\"Burkina Faso\">Burkina Faso</a>) and <a href=\"https://wikipedia.org/wiki/Niger\" title=\"Niger\">Niger</a>.", "links": [{"title": "Republic of Dahomey", "link": "https://wikipedia.org/wiki/Republic_of_Dahomey"}, {"title": "Benin", "link": "https://wikipedia.org/wiki/Benin"}, {"title": "Republic of Upper Volta", "link": "https://wikipedia.org/wiki/Republic_of_Upper_Volta"}, {"title": "Burkina Faso", "link": "https://wikipedia.org/wiki/Burkina_Faso"}, {"title": "Niger", "link": "https://wikipedia.org/wiki/Niger"}]}, {"year": "1960", "text": "Congo Crisis: The State of Katanga breaks away from the Democratic Republic of the Congo.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Congo_Crisis\" title=\"Congo Crisis\">Congo Crisis</a>: The <a href=\"https://wikipedia.org/wiki/State_of_Katanga\" title=\"State of Katanga\">State of Katanga</a> breaks away from the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Congo_Crisis\" title=\"Congo Crisis\">Congo Crisis</a>: The <a href=\"https://wikipedia.org/wiki/State_of_Katanga\" title=\"State of Katanga\">State of Katanga</a> breaks away from the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>.", "links": [{"title": "Congo Crisis", "link": "https://wikipedia.org/wiki/Congo_Crisis"}, {"title": "State of Katanga", "link": "https://wikipedia.org/wiki/State_of_Katanga"}, {"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}]}, {"year": "1960", "text": "To Kill a Mockingbird by <PERSON> is first published, in the United States.", "html": "1960 - <i><a href=\"https://wikipedia.org/wiki/To_Kill_a_Mockingbird\" title=\"To Kill a Mockingbird\">To Kill a Mockingbird</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lee\"><PERSON></a> is first published, in the United States.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/To_Kill_a_Mockingbird\" title=\"To Kill a Mockingbird\">To Kill a Mockingbird</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lee\"><PERSON></a> is first published, in the United States.", "links": [{"title": "To Kill a Mockingbird", "link": "https://wikipedia.org/wiki/To_Kill_a_Mockingbird"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "First transatlantic satellite television transmission.", "html": "1962 - First transatlantic <a href=\"https://wikipedia.org/wiki/Satellite_television\" title=\"Satellite television\">satellite television</a> transmission.", "no_year_html": "First transatlantic <a href=\"https://wikipedia.org/wiki/Satellite_television\" title=\"Satellite television\">satellite television</a> transmission.", "links": [{"title": "Satellite television", "link": "https://wikipedia.org/wiki/Satellite_television"}]}, {"year": "1962", "text": "Project Apollo: At a press conference, NASA announces lunar orbit rendezvous as the means to land astronauts on the Moon, and return them to Earth.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Project_Apollo\" class=\"mw-redirect\" title=\"Project Apollo\">Project Apollo</a>: At a press conference, <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> announces <a href=\"https://wikipedia.org/wiki/Lunar_orbit_rendezvous\" title=\"Lunar orbit rendezvous\">lunar orbit rendezvous</a> as the means to land <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronauts</a> on the Moon, and return them to Earth.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Apollo\" class=\"mw-redirect\" title=\"Project Apollo\">Project Apollo</a>: At a press conference, <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> announces <a href=\"https://wikipedia.org/wiki/Lunar_orbit_rendezvous\" title=\"Lunar orbit rendezvous\">lunar orbit rendezvous</a> as the means to land <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronauts</a> on the Moon, and return them to Earth.", "links": [{"title": "Project Apollo", "link": "https://wikipedia.org/wiki/Project_Apollo"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Lunar orbit rendezvous", "link": "https://wikipedia.org/wiki/Lunar_orbit_rendezvous"}, {"title": "Astronaut", "link": "https://wikipedia.org/wiki/Astronaut"}]}, {"year": "1971", "text": "Copper mines in Chile are nationalized.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Copper\" title=\"Copper\">Copper</a> mines in <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a> are nationalized.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Copper\" title=\"Copper\">Copper</a> mines in <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a> are nationalized.", "links": [{"title": "Copper", "link": "https://wikipedia.org/wiki/Copper"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}]}, {"year": "1972", "text": "The first game of the World Chess Championship 1972 between challenger <PERSON> and defending champion <PERSON> starts.", "html": "1972 - The first game of the <a href=\"https://wikipedia.org/wiki/World_Chess_Championship_1972\" title=\"World Chess Championship 1972\">World Chess Championship 1972</a> between challenger <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> and defending champion <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> starts.", "no_year_html": "The first game of the <a href=\"https://wikipedia.org/wiki/World_Chess_Championship_1972\" title=\"World Chess Championship 1972\">World Chess Championship 1972</a> between challenger <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and defending champion <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> starts.", "links": [{"title": "World Chess Championship 1972", "link": "https://wikipedia.org/wiki/World_Chess_Championship_1972"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "Varig Flight 820 crashes near Paris on approach to Orly Airport, killing 123 of the 134 on board. In response, the FAA bans smoking in airplane lavatories.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Varig_Flight_820\" title=\"Varig Flight 820\">Varig Flight 820</a> crashes near <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> on approach to <a href=\"https://wikipedia.org/wiki/Orly_Airport\" title=\"Orly Airport\">Orly Airport</a>, killing 123 of the 134 on board. In response, the <a href=\"https://wikipedia.org/wiki/Federal_Aviation_Administration\" title=\"Federal Aviation Administration\">FAA</a> bans smoking in airplane lavatories.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Varig_Flight_820\" title=\"Varig Flight 820\">Varig Flight 820</a> crashes near <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> on approach to <a href=\"https://wikipedia.org/wiki/Orly_Airport\" title=\"Orly Airport\">Orly Airport</a>, killing 123 of the 134 on board. In response, the <a href=\"https://wikipedia.org/wiki/Federal_Aviation_Administration\" title=\"Federal Aviation Administration\">FAA</a> bans smoking in airplane lavatories.", "links": [{"title": "Varig Flight 820", "link": "https://wikipedia.org/wiki/Varig_Flight_820"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}, {"title": "Orly Airport", "link": "https://wikipedia.org/wiki/Orly_Airport"}, {"title": "Federal Aviation Administration", "link": "https://wikipedia.org/wiki/Federal_Aviation_Administration"}]}, {"year": "1977", "text": "<PERSON>, assassinated in 1968, is awarded the Presidential Medal of Freedom.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>_Jr.\" title=\"Assassination of <PERSON> Jr.\">assassinated in 1968</a>, is awarded the <a href=\"https://wikipedia.org/wiki/Presidential_Medal_of_Freedom\" title=\"Presidential Medal of Freedom\">Presidential Medal of Freedom</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>_Jr.\" title=\"Assassination of <PERSON> Jr.\">assassinated in 1968</a>, is awarded the <a href=\"https://wikipedia.org/wiki/Presidential_Medal_of_Freedom\" title=\"Presidential Medal of Freedom\">Presidential Medal of Freedom</a>.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Assassination of <PERSON>.", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Presidential Medal of Freedom", "link": "https://wikipedia.org/wiki/Presidential_Medal_of_Freedom"}]}, {"year": "1978", "text": "Los Alfaques disaster: A truck carrying liquid gas crashes and explodes at a coastal campsite in Tarragona, Spain killing 216 tourists.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Los_Alfaques_disaster\" title=\"Los Alfaques disaster\">Los Alfaques disaster</a>: A truck carrying liquid gas crashes and explodes at a coastal campsite in <a href=\"https://wikipedia.org/wiki/Tarragona\" title=\"Tarragona\">Tarragona</a>, Spain killing 216 tourists.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Los_Alfaques_disaster\" title=\"Los Alfaques disaster\">Los Alfaques disaster</a>: A truck carrying liquid gas crashes and explodes at a coastal campsite in <a href=\"https://wikipedia.org/wiki/Tarragona\" title=\"Tarragona\">Tarragona</a>, Spain killing 216 tourists.", "links": [{"title": "Los Alfaques disaster", "link": "https://wikipedia.org/wiki/Los_Alfaques_disaster"}, {"title": "Tarragona", "link": "https://wikipedia.org/wiki/Tarragona"}]}, {"year": "1979", "text": "America's first space station, <PERSON><PERSON>, is destroyed as it re-enters the Earth's atmosphere over the Indian Ocean.", "html": "1979 - America's first space station, <i><a href=\"https://wikipedia.org/wiki/Skylab\" title=\"Skylab\"><PERSON>b</a></i>, is destroyed as it re-enters the Earth's atmosphere over the Indian Ocean.", "no_year_html": "America's first space station, <i><a href=\"https://wikipedia.org/wiki/Skylab\" title=\"Skylab\"><PERSON><PERSON></a></i>, is destroyed as it re-enters the Earth's atmosphere over the Indian Ocean.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Skylab"}]}, {"year": "1982", "text": "The Italy National Football Team defeats West Germany at Santiago Bernabéu Stadium to capture the 1982 FIFA World Cup.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/Italy_National_Football_Team\" class=\"mw-redirect\" title=\"Italy National Football Team\">Italy National Football Team</a> defeats <a href=\"https://wikipedia.org/wiki/West_Germany_national_football_team_results\" title=\"West Germany national football team results\">West Germany</a> at <a href=\"https://wikipedia.org/wiki/Santiago_Bernab%C3%A9u_Stadium\" title=\"Santiago Bernabéu Stadium\">Santiago Bernabéu Stadium</a> to capture the <a href=\"https://wikipedia.org/wiki/1982_FIFA_World_Cup_Final\" class=\"mw-redirect\" title=\"1982 FIFA World Cup Final\">1982 FIFA World Cup</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Italy_National_Football_Team\" class=\"mw-redirect\" title=\"Italy National Football Team\">Italy National Football Team</a> defeats <a href=\"https://wikipedia.org/wiki/West_Germany_national_football_team_results\" title=\"West Germany national football team results\">West Germany</a> at <a href=\"https://wikipedia.org/wiki/Santiago_Bernab%C3%A9u_Stadium\" title=\"Santiago Bernabéu Stadium\">Santiago Bernabéu Stadium</a> to capture the <a href=\"https://wikipedia.org/wiki/1982_FIFA_World_Cup_Final\" class=\"mw-redirect\" title=\"1982 FIFA World Cup Final\">1982 FIFA World Cup</a>.", "links": [{"title": "Italy National Football Team", "link": "https://wikipedia.org/wiki/Italy_National_Football_Team"}, {"title": "West Germany national football team results", "link": "https://wikipedia.org/wiki/West_Germany_national_football_team_results"}, {"title": "Santiago Bernabéu Stadium", "link": "https://wikipedia.org/wiki/Santiago_Bernab%C3%A9u_Stadium"}, {"title": "1982 FIFA World Cup Final", "link": "https://wikipedia.org/wiki/1982_FIFA_World_Cup_Final"}]}, {"year": "1983", "text": "A TAME airline Boeing 737-200 crashes near Cuenca, Ecuador, killing all 119 passengers and crew on board.", "html": "1983 - A <a href=\"https://wikipedia.org/wiki/TAME\" title=\"TAME\">TAME</a> airline <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737-200</a> <a href=\"https://wikipedia.org/wiki/1983_TAME_Boeing_737_crash\" class=\"mw-redirect\" title=\"1983 TAME Boeing 737 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Cuenca,_Ecuador\" title=\"Cuenca, Ecuador\">Cuenca, Ecuador</a>, killing all 119 passengers and crew on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/TAME\" title=\"TAME\">TAME</a> airline <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737-200</a> <a href=\"https://wikipedia.org/wiki/1983_TAME_Boeing_737_crash\" class=\"mw-redirect\" title=\"1983 TAME Boeing 737 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Cuenca,_Ecuador\" title=\"Cuenca, Ecuador\">Cuenca, Ecuador</a>, killing all 119 passengers and crew on board.", "links": [{"title": "TAME", "link": "https://wikipedia.org/wiki/TAME"}, {"title": "Boeing 737", "link": "https://wikipedia.org/wiki/Boeing_737"}, {"title": "1983 TAME Boeing 737 crash", "link": "https://wikipedia.org/wiki/1983_TAME_Boeing_737_crash"}, {"title": "Cuenca, Ecuador", "link": "https://wikipedia.org/wiki/Cuenca,_Ecuador"}]}, {"year": "1990", "text": "Oka Crisis: First Nations land dispute in Quebec begins.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Oka_Crisis\" title=\"Oka Crisis\">Oka Crisis</a>: <a href=\"https://wikipedia.org/wiki/First_Nations_in_Canada\" title=\"First Nations in Canada\">First Nations</a> land dispute in <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oka_Crisis\" title=\"Oka Crisis\">Oka Crisis</a>: <a href=\"https://wikipedia.org/wiki/First_Nations_in_Canada\" title=\"First Nations in Canada\">First Nations</a> land dispute in <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> begins.", "links": [{"title": "Oka Crisis", "link": "https://wikipedia.org/wiki/Oka_Crisis"}, {"title": "First Nations in Canada", "link": "https://wikipedia.org/wiki/First_Nations_in_Canada"}, {"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}]}, {"year": "1991", "text": "Nigeria Airways Flight 2120 crashes in Jeddah, Saudi Arabia, killing all 261 passengers and crew on board.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Nigeria_Airways_Flight_2120\" title=\"Nigeria Airways Flight 2120\">Nigeria Airways Flight 2120</a> crashes in <a href=\"https://wikipedia.org/wiki/Jeddah\" title=\"Jeddah\">Jeddah</a>, Saudi Arabia, killing all 261 passengers and crew on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nigeria_Airways_Flight_2120\" title=\"Nigeria Airways Flight 2120\">Nigeria Airways Flight 2120</a> crashes in <a href=\"https://wikipedia.org/wiki/Jeddah\" title=\"Jeddah\">Jeddah</a>, Saudi Arabia, killing all 261 passengers and crew on board.", "links": [{"title": "Nigeria Airways Flight 2120", "link": "https://wikipedia.org/wiki/Nigeria_Airways_Flight_2120"}, {"title": "Jeddah", "link": "https://wikipedia.org/wiki/Jeddah"}]}, {"year": "1995", "text": "Yugoslav Wars: Srebrenica massacre begins; lasts until 22 July.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: <a href=\"https://wikipedia.org/wiki/Srebrenica_massacre\" title=\"Srebrenica massacre\">Srebrenica massacre</a> begins; lasts until 22 July.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: <a href=\"https://wikipedia.org/wiki/Srebrenica_massacre\" title=\"Srebrenica massacre\">Srebrenica massacre</a> begins; lasts until 22 July.", "links": [{"title": "Yugoslav Wars", "link": "https://wikipedia.org/wiki/Yugoslav_Wars"}, {"title": "Srebrenica massacre", "link": "https://wikipedia.org/wiki/Srebrenica_massacre"}]}, {"year": "2006", "text": "Mumbai train bombings: 209 people  are killed in a series of bomb attacks in Mumbai, India.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/2006_Mumbai_train_bombings\" title=\"2006 Mumbai train bombings\">Mumbai train bombings</a>: 209 people are killed in a series of bomb attacks in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Mumbai</a>, India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2006_Mumbai_train_bombings\" title=\"2006 Mumbai train bombings\">Mumbai train bombings</a>: 209 people are killed in a series of bomb attacks in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Mumbai</a>, India.", "links": [{"title": "2006 Mumbai train bombings", "link": "https://wikipedia.org/wiki/2006_Mumbai_train_bombings"}, {"title": "Mumbai", "link": "https://wikipedia.org/wiki/Mumbai"}]}, {"year": "2010", "text": "The Islamist militia group Al-Shabaab carries out multiple suicide bombings in Kampala, Uganda, killing 74 people and injuring 85 others.", "html": "2010 - The <a href=\"https://wikipedia.org/wiki/Islamism\" title=\"Islamism\">Islamist</a> militia group <a href=\"https://wikipedia.org/wiki/Al-Shabaab_(militant_group)\" title=\"Al<PERSON><PERSON><PERSON><PERSON><PERSON> (militant group)\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> carries out <a href=\"https://wikipedia.org/wiki/2010_Kampala_attacks\" class=\"mw-redirect\" title=\"2010 Kampala attacks\">multiple suicide bombings</a> in <a href=\"https://wikipedia.org/wiki/Kampala\" title=\"Kampala\">Kampala</a>, Uganda, killing 74 people and injuring 85 others.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Islamism\" title=\"Islamism\">Islamist</a> militia group <a href=\"https://wikipedia.org/wiki/Al-Shabaab_(militant_group)\" title=\"Al<PERSON><PERSON><PERSON><PERSON><PERSON> (militant group)\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> carries out <a href=\"https://wikipedia.org/wiki/2010_Kampala_attacks\" class=\"mw-redirect\" title=\"2010 Kampala attacks\">multiple suicide bombings</a> in <a href=\"https://wikipedia.org/wiki/Kampala\" title=\"Kampala\">Kampala</a>, Uganda, killing 74 people and injuring 85 others.", "links": [{"title": "Islamism", "link": "https://wikipedia.org/wiki/Islamism"}, {"title": "Al-Shabaab (militant group)", "link": "https://wikipedia.org/wiki/Al-Shabaab_(militant_group)"}, {"title": "2010 Kampala attacks", "link": "https://wikipedia.org/wiki/2010_Kampala_attacks"}, {"title": "Kampala", "link": "https://wikipedia.org/wiki/Kampala"}]}, {"year": "2010", "text": "Spain defeats the Netherlands to win the 2010 FIFA World Cup in Johannesburg.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Spanish_National_Football_Team\" class=\"mw-redirect\" title=\"Spanish National Football Team\">Spain</a> defeats the <a href=\"https://wikipedia.org/wiki/Netherlands_National_Football_Team\" class=\"mw-redirect\" title=\"Netherlands National Football Team\">Netherlands</a> to win the <a href=\"https://wikipedia.org/wiki/2010_FIFA_World_Cup\" title=\"2010 FIFA World Cup\">2010 FIFA World Cup</a> in Johannesburg.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_National_Football_Team\" class=\"mw-redirect\" title=\"Spanish National Football Team\">Spain</a> defeats the <a href=\"https://wikipedia.org/wiki/Netherlands_National_Football_Team\" class=\"mw-redirect\" title=\"Netherlands National Football Team\">Netherlands</a> to win the <a href=\"https://wikipedia.org/wiki/2010_FIFA_World_Cup\" title=\"2010 FIFA World Cup\">2010 FIFA World Cup</a> in Johannesburg.", "links": [{"title": "Spanish National Football Team", "link": "https://wikipedia.org/wiki/Spanish_National_Football_Team"}, {"title": "Netherlands National Football Team", "link": "https://wikipedia.org/wiki/Netherlands_National_Football_Team"}, {"title": "2010 FIFA World Cup", "link": "https://wikipedia.org/wiki/2010_FIFA_World_Cup"}]}, {"year": "2011", "text": "Ninety-eight containers of explosives self-detonate killing 13 people in Zygi, Cyprus.", "html": "2011 - Ninety-eight containers of explosives <a href=\"https://wikipedia.org/wiki/Evangelos_Florakis_Naval_Base_explosion\" title=\"Evangelos <PERSON> Naval Base explosion\">self-detonate killing 13 people</a> in <a href=\"https://wikipedia.org/wiki/Zygi\" title=\"Zygi\">Zygi</a>, Cyprus.", "no_year_html": "Ninety-eight containers of explosives <a href=\"https://wikipedia.org/wiki/Evangelos_Florakis_Naval_Base_explosion\" title=\"Evangelos Florakis Naval Base explosion\">self-detonate killing 13 people</a> in <a href=\"https://wikipedia.org/wiki/Zygi\" title=\"Zygi\">Zygi</a>, Cyprus.", "links": [{"title": "<PERSON><PERSON><PERSON> Naval Base explosion", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Naval_Base_explosion"}, {"title": "Zygi", "link": "https://wikipedia.org/wiki/<PERSON>ygi"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON> Chapo\" <PERSON> escapes from the maximum security Altiplano prison in Mexico, his second escape.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_%22El_Chapo%22_Guzm%C3%A1n\" title='<PERSON><PERSON><PERSON><PERSON> \"El Chapo\" Guzmán'><PERSON><PERSON><PERSON><PERSON> \"El Chapo\" <PERSON><PERSON></a> escapes from the maximum security <a href=\"https://wikipedia.org/wiki/Federal_Social_Readaptation_Center_No._1\" title=\"Federal Social Readaptation Center No. 1\">Altiplano prison</a> in Mexico, his second escape.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_%22El_Chapo%22_Guzm%C3%A1n\" title='<PERSON><PERSON><PERSON><PERSON> \"El Chapo\" Guzmán'><PERSON><PERSON><PERSON><PERSON> \"El Chapo\" Guzmán</a> escapes from the maximum security <a href=\"https://wikipedia.org/wiki/Federal_Social_Readaptation_Center_No._1\" title=\"Federal Social Readaptation Center No. 1\">Altiplano prison</a> in Mexico, his second escape.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> \"El Chapo\" Guzmán", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_%22El_Chapo%22_Guzm%C3%A1n"}, {"title": "Federal Social Readaptation Center No. 1", "link": "https://wikipedia.org/wiki/Federal_Social_Readaptation_Center_No._1"}]}, {"year": "2021", "text": "<PERSON> becomes the first civilian to be launched into space via his Virgin Galactic spacecraft.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first civilian to be launched into space via his <a href=\"https://wikipedia.org/wiki/Virgin_Galactic\" title=\"Virgin Galactic\">Virgin Galactic</a> spacecraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first civilian to be launched into space via his <a href=\"https://wikipedia.org/wiki/Virgin_Galactic\" title=\"Virgin Galactic\">Virgin Galactic</a> spacecraft.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Virgin Galactic", "link": "https://wikipedia.org/wiki/Virgin_Galactic"}]}], "Births": [{"year": "154", "text": "<PERSON><PERSON><PERSON>, Syrian astrologer, scholar, and philosopher (d. 222)", "html": "154 - <a href=\"https://wikipedia.org/wiki/Bardaisan\" title=\"Bar<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian astrologer, scholar, and philosopher (d. 222)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bardaisan\" title=\"Bardai<PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian astrologer, scholar, and philosopher (d. 222)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>san"}]}, {"year": "1274", "text": "<PERSON> the <PERSON>, Scottish king (d. 1329)", "html": "1274 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>, Scottish king (d. 1329)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>, Scottish king (d. 1329)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1406", "text": "<PERSON>, Margrave of Hachberg-Sausenberg (d. 1482)", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Mar<PERSON>_of_Hachberg-Sausenberg\" title=\"<PERSON>, Margrave of Hachberg-Sausenberg\"><PERSON>, Margrave of Hachberg-Sausenberg</a> (d. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Hachberg-Sausenberg\" title=\"<PERSON>, Margrave of Hachberg-Sausenberg\"><PERSON>, Margrave of Hachberg-Sausenberg</a> (d. 1482)", "links": [{"title": "<PERSON>, Margrave of Hachberg-Sausenberg", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Hachberg-Sausenberg"}]}, {"year": "1459", "text": "<PERSON><PERSON><PERSON>, Count <PERSON><PERSON> of Zweibrücken, German nobleman (d. 1527)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON>n\" title=\"<PERSON><PERSON><PERSON>, Count <PERSON><PERSON> of Zweibrücken\"><PERSON><PERSON><PERSON>, Count <PERSON><PERSON> of Zweibrücken</a>, German nobleman (d. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>, Count <PERSON><PERSON> of Zweibrücken\"><PERSON><PERSON><PERSON>, Count <PERSON><PERSON> of Zweibrücken</a>, German nobleman (d. 1527)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count <PERSON><PERSON> of Zweibrücken", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%BC<PERSON>n"}]}, {"year": "1558", "text": "<PERSON>, English author and playwright (d. 1592)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)\" title=\"<PERSON> (dramatist)\"><PERSON></a>, English author and playwright (d. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)\" title=\"<PERSON> (dramatist)\"><PERSON></a>, English author and playwright (d. 1592)", "links": [{"title": "<PERSON> (dramatist)", "link": "https://wikipedia.org/wiki/<PERSON>_(dramatist)"}]}, {"year": "1561", "text": "<PERSON>, Spanish cleric and poet (d. 1627)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3ngora\" title=\"<PERSON>ón<PERSON>\"><PERSON></a>, Spanish cleric and poet (d. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3ngora\" title=\"<PERSON>\"><PERSON></a>, Spanish cleric and poet (d. 1627)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_de_G%C3%B3ngora"}]}, {"year": "1603", "text": "<PERSON><PERSON><PERSON>, English astrologer, courtier, and diplomat (d. 1665)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Di<PERSON>\" title=\"<PERSON><PERSON><PERSON> Digby\"><PERSON><PERSON><PERSON></a>, English astrologer, courtier, and diplomat (d. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dig<PERSON>\" title=\"Ken<PERSON><PERSON> Digby\"><PERSON><PERSON><PERSON></a>, English astrologer, courtier, and diplomat (d. 1665)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gby"}]}, {"year": "1628", "text": "<PERSON>, Japanese daimyō (d. 1701)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese daimyō (d. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese daimyō (d. 1701)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1653", "text": "<PERSON>, American woman accused of witchcraft (d. 1692)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Salem_witch_trials)\" class=\"mw-redirect\" title=\"<PERSON> (Salem witch trials)\"><PERSON></a>, American woman accused of <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a> (d. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Salem_witch_trials)\" class=\"mw-redirect\" title=\"<PERSON> (Salem witch trials)\"><PERSON></a>, American woman accused of <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a> (d. 1692)", "links": [{"title": "<PERSON> (Salem witch trials)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Salem_witch_trials)"}, {"title": "Witchcraft", "link": "https://wikipedia.org/wiki/Witchcraft"}]}, {"year": "1657", "text": "<PERSON> of Prussia (d. 1713)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (d. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (d. 1713)", "links": [{"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Frederick_<PERSON>_of_Prussia"}]}, {"year": "1662", "text": "<PERSON>, Elector of Bavaria (d. 1726)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON>, Elector of Bavaria\"><PERSON>, Elector of Bavaria</a> (d. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON>, Elector of Bavaria\"><PERSON>, Elector of Bavaria</a> (d. 1726)", "links": [{"title": "<PERSON>, Elector of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Bavaria"}]}, {"year": "1709", "text": "<PERSON>, Swedish chemist and mineralogist (d. 1785)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish chemist and mineralogist (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish chemist and mineralogist (d. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1723", "text": "<PERSON><PERSON><PERSON>, French historian and author (d. 1799)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A7ois_Marmontel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and author (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7ois_Marmontel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and author (d. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A7ois_Marmontel"}]}, {"year": "1754", "text": "<PERSON>, English physician and philanthropist (d. 1825)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and philanthropist (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and philanthropist (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1760", "text": "<PERSON>, American wife of <PERSON> and American Revolutionary War spy (d. 1804)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a> <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">spy</a> (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a> <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">spy</a> (d. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}]}, {"year": "1767", "text": "<PERSON>, American lawyer and politician, 6th President of the United States (d. 1848)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1826", "text": "<PERSON>, Russian ethnographer and author (d. 1871)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ethnographer and author (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ethnographer and author (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1832", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek lawyer and politician, 55th Prime Minister of Greece (d. 1896)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s_Tri<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1834", "text": "<PERSON>, American-English painter and illustrator (d. 1903)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Whistler\" class=\"mw-redirect\" title=\"<PERSON> Whistler\"><PERSON></a>, American-English painter and illustrator (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Whistler\" class=\"mw-redirect\" title=\"<PERSON>l Whistler\"><PERSON></a>, American-English painter and illustrator (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian composer (d. 1896)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian composer (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian composer (d. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, French author and poet (d. 1917)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Bloy"}]}, {"year": "1849", "text": "<PERSON><PERSON> <PERSON><PERSON>, English plant taxonomist and authority on succulents (d. 1934)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English plant taxonomist and authority on succulents (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English plant taxonomist and authority on succulents (d. 1934)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, American missionary (d. 1938)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "Princess <PERSON> of Hesse and by Rhine (d. 1953)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine\" title=\"Princess <PERSON> of Hesse and by Rhine\">Princess <PERSON> of Hesse and by Rhine</a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine\" title=\"Princess <PERSON> of Hesse and by Rhine\">Princess <PERSON> of Hesse and by Rhine</a> (d. 1953)", "links": [{"title": "Princess <PERSON> of Hesse and by Rhine", "link": "https://wikipedia.org/wiki/Princess_Irene_of_Hesse_and_by_Rhine"}]}, {"year": "1875", "text": "<PERSON><PERSON> <PERSON><PERSON>, British painter and illustrator (d. 1960)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British painter and illustrator (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British painter and illustrator (d. 1960)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, German architect and academic (d. 1964)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect and academic (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect and academic (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American astronomer and author (d. 1966)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and author (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and author (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American miner, explorer, and park ranger (d. 1946)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American miner, explorer, and park ranger (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American miner, explorer, and park ranger (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Russian painter and illustrator (d. 1939)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and illustrator (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and illustrator (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, German philosopher and jurist (d. 1985)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and jurist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and jurist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American actor, singer, and screenwriter (d. 1962)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and screenwriter (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and screenwriter (d. 1962)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1894", "text": "<PERSON><PERSON>, German zoologist (d. 1968)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German zoologist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German zoologist (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, English author and poet (d. 1941)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American police officer (d. 1973)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, German businessman and philanthropist (d. 1943)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Wilfrid_Israel\" title=\"Wilfrid Israel\">Wil<PERSON><PERSON> Israel</a>, German businessman and philanthropist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilfrid_Israel\" title=\"Wilfrid Israel\">Wil<PERSON><PERSON> Israel</a>, German businessman and philanthropist (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilfrid_Israel"}]}, {"year": "1899", "text": "<PERSON><PERSON> <PERSON><PERSON>, American essayist and journalist (d. 1985)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"E. B. White\"><PERSON><PERSON> <PERSON><PERSON></a>, American essayist and journalist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"E. B. White\"><PERSON><PERSON> <PERSON><PERSON></a>, American essayist and journalist (d. 1985)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Belizean businesswoman, activist, and politician (d. 1975)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Gwen<PERSON>lyn_Lizarraga\" title=\"<PERSON><PERSON><PERSON> Lizarra<PERSON>\"><PERSON><PERSON><PERSON></a>, Belizean businesswoman, activist, and politician (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gwen<PERSON><PERSON>_Lizarraga\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belizean businesswoman, activist, and politician (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gwendolyn_Liza<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English-Russian colonel (d. 1971)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Russian colonel (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Russian colonel (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American bullfighter (d. 1976)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bullfighter)\" title=\"<PERSON> (bullfighter)\"><PERSON></a>, American bullfighter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bullfighter)\" title=\"<PERSON> (bullfighter)\"><PERSON></a>, American bullfighter (d. 1976)", "links": [{"title": "<PERSON> (bullfighter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bullfighter)"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Spanish guitarist and composer (d. 1972)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Ni%C3%B1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish guitarist and composer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ni%C3%B1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish guitarist and composer (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ni%C3%B1o_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Australian statistician and biometrician (d. 1952)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian statistician and <a href=\"https://wikipedia.org/wiki/Biometrician\" class=\"mw-redirect\" title=\"Biometrician\">biometrician</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian statistician and <a href=\"https://wikipedia.org/wiki/Biometrician\" class=\"mw-redirect\" title=\"Biometrician\">biometrician</a> (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Biometrician", "link": "https://wikipedia.org/wiki/Biometrician"}]}, {"year": "1906", "text": "<PERSON>, American actor and announcer (d. 1981)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and announcer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and announcer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German politician, Minister of Intra-German Relations (d. 1990)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Intra-German_Relations\" title=\"Minister of Intra-German Relations\">Minister of Intra-German Relations</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Intra-German_Relations\" title=\"Minister of Intra-German Relations\">Minister of Intra-German Relations</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Intra-German Relations", "link": "https://wikipedia.org/wiki/Minister_of_Intra-German_Relations"}]}, {"year": "1909", "text": "<PERSON>, American actress (d. 1998)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Dutch catholic priest (d. 2018)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch catholic priest (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch catholic priest (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actress (d. 1997)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, German nurse who was still present in the Führerbunker when it was captured by Soviet troops (d. 2006)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>legel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German nurse who was still present in the <i><a href=\"https://wikipedia.org/wiki/F%C3%BChrerbunker\" title=\"Führerbunker\">Führerbunker</a></i> when it was captured by Soviet troops (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German nurse who was still present in the <i><a href=\"https://wikipedia.org/wiki/F%C3%BChrerbunker\" title=\"Führerbunker\">Führerbunker</a></i> when it was captured by Soviet troops (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>_Flegel"}, {"title": "Führerbunker", "link": "https://wikipedia.org/wiki/F%C3%BChrerbunker"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Romanian conductor and composer (d. 1996)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian conductor and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian conductor and composer (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ache"}]}, {"year": "1912", "text": "<PERSON><PERSON>, English cricketer (d. 1989)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American captain and politician, 48th Mayor of Syracuse (d. 2011)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 48th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Syracuse,_New_York\" title=\"List of mayors of Syracuse, New York\">Mayor of Syracuse</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 48th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Syracuse,_New_York\" title=\"List of mayors of Syracuse, New York\">Mayor of Syracuse</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "List of mayors of Syracuse, New York", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Syracuse,_New_York"}]}, {"year": "1913", "text": "<PERSON>, English cricketer (d. 1977)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON><PERSON>, American sinologist, author, and academic (d. 1966)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rd<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American sinologist, author, and academic (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rdwain<PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American sinologist, author, and academic (d. 1966)", "links": [{"title": "<PERSON>rdwain<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, British protozoologist (d. 2008)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British protozoologist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British protozoologist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American tax attorney, educator, and IRS Commissioner (d. 2019)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tax attorney, educator, and IRS Commissioner (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tax attorney, educator, and IRS Commissioner (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Dutch water polo player (d. 2018)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch water polo player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch water polo player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Australian-Russian physicist and academic, Nobel Prize laureate (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1916", "text": "<PERSON>, English actor and screenwriter (d. 2008)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2008)", "links": [{"title": "Reg <PERSON>", "link": "https://wikipedia.org/wiki/Reg_Varney"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Australian lieutenant, lawyer, and politician, 21st Prime Minister of Australia (d. 2014)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Gough_<PERSON>hit<PERSON>\" title=\"Gough Whitlam\"><PERSON><PERSON></a>, Australian lieutenant, lawyer, and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gough_<PERSON>hit<PERSON>\" title=\"Gough Whitlam\"><PERSON><PERSON></a>, Australian lieutenant, lawyer, and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON>_Whitlam"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, English educator, who named <PERSON><PERSON><PERSON> (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English educator, who named <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English educator, who named <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Venetia_Burney"}, {"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}]}, {"year": "1918", "text": "<PERSON>, American illustrator (d. 1983)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Russian actor and dancer (d. 1985)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian actor and dancer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian actor and dancer (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Russian-American author (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Zech<PERSON>_<PERSON>\" title=\"<PERSON>ech<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zech<PERSON>_<PERSON>\" title=\"<PERSON>ech<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American author (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ech<PERSON>_<PERSON>chin"}]}, {"year": "1922", "text": "<PERSON>, American actor (d. 1998)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, German-Swiss racing driver (d. 1991)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss racing driver (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss racing driver (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Polish-American historian and academic (d. 2018)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American historian and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American historian and academic (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Indian actress and comedian (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Tun_<PERSON>n\" title=\"Tun <PERSON>n\"><PERSON><PERSON></a>, Indian actress and comedian (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON>n <PERSON>n\"><PERSON><PERSON></a>, Indian actress and comedian (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>n_Tun"}]}, {"year": "1924", "text": "<PERSON>, Brazilian physicist and academic (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian physicist and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian physicist and academic (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Lattes"}]}, {"year": "1924", "text": "<PERSON>, Canadian-American actress and singer (d. 2007)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Northern Irish footballer and manager (d. 1971)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American businessman", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French composer (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Swedish operatic tenor (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish operatic tenor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish operatic tenor (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American lawyer and politician (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2004)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2004)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1926", "text": "<PERSON>, American minister, theologian, and author (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, theologian, and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, theologian, and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American-Canadian physicist and engineer (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian physicist and engineer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian physicist and engineer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English footballer (d. 1987)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Swedish conductor", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Baron <PERSON> of Braunstone, Welsh-English lawyer and politician (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_Braunstone\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON> of Braunstone\"><PERSON><PERSON><PERSON>, Baron <PERSON> of Braunstone</a>, Welsh-English lawyer and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_Braunstone\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON> of Braunstone\"><PERSON><PERSON><PERSON>, Baron <PERSON> of Braunstone</a>, Welsh-English lawyer and politician (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>, Baron <PERSON> of Braunstone", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_Braunstone"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American boxer (d. 2002)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Filipina choirmaster (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipina choirmaster (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipina choirmaster (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Veneracion"}]}, {"year": "1929", "text": "<PERSON>, American singer-songwriter and saxophonist (d. 2006)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and saxophonist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and saxophonist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Irish actor (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Irish actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Irish actor (d. 2012)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1930", "text": "<PERSON>, New Zealand cricketer (d. 2024)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American literary critic (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary critic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bloom\"><PERSON></a>, American literary critic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American politician, 53rd Governor of Louisiana (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American politician, 53rd <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American politician, 53rd <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (d. 2020)", "links": [{"title": "<PERSON> (American politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)"}, {"title": "Governor of Louisiana", "link": "https://wikipedia.org/wiki/Governor_of_Louisiana"}]}, {"year": "1930", "text": "<PERSON>, English businessman, founded Pukka Pies (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Pukka_Pies\" title=\"Pukka Pies\">Pukka Pies</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Pukka_Pies\" title=\"Pukka Pies\">Pukka Pies</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pukka Pies", "link": "https://wikipedia.org/wiki/Pukka_Pies"}]}, {"year": "1930", "text": "<PERSON>, American sociologist (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sociologist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sociologist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American baseball player (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, American doo-wop singer (d. 1990)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American doo-wop singer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American doo-wop singer (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American actor and singer (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>b_<PERSON>\" title=\"Tab <PERSON>\"><PERSON><PERSON></a>, American actor and singer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Tab Hunter\"><PERSON><PERSON></a>, American actor and singer (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tab_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Italian physicist and academic (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tulli<PERSON> Regge\"><PERSON><PERSON><PERSON></a>, Italian physicist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Reg<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physicist and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON>o <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ge"}]}, {"year": "1932", "text": "<PERSON>, French-born American folk singer and musician (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-born American folk singer and musician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-born American folk singer and musician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player and coach (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American football player and coach (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American admiral and politician, United States Secretary of the Navy (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Navy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy"}]}, {"year": "1934", "text": "<PERSON>, Italian fashion designer, founded the Armani Company", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fashion designer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>ani\" title=\"<PERSON>ani\">Armani Company</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fashion designer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>ani\" title=\"<PERSON><PERSON>\">Armani Company</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ani"}]}, {"year": "1934", "text": "<PERSON>, American politician (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American saxophonist and educator (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and educator (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and educator (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Northern Irish lawyer and politician (d. 2011)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish lawyer and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish lawyer and politician (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Chinese-Taiwanese author", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-yung\" title=\"<PERSON><PERSON>-yung\"><PERSON><PERSON>-y<PERSON></a>, Chinese-Taiwanese author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-yung\" title=\"<PERSON><PERSON>-yung\"><PERSON><PERSON>-y<PERSON></a>, Chinese-Taiwanese author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-yung"}]}, {"year": "1941", "text": "<PERSON>, American journalist and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English trumpet player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English trumpet player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English trumpet player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1942", "text": "<PERSON>, Australian sportscaster (d. 2018)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sportscaster (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sportscaster (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian journalist  (d. 2006)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American psychologist and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)"}]}, {"year": "1943", "text": "<PERSON>, Australian metropolitan", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Australian metropolitan", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Australian metropolitan", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>(bishop)"}]}, {"year": "1943", "text": "<PERSON>, Haitian businessman and politician, 5th Prime Minister of Haiti", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian businessman and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian businessman and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Haiti", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Haiti"}]}, {"year": "1943", "text": "<PERSON>, German racing driver (d. 1983)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American basketball player and coach (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Baron <PERSON>, English philanthropist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English philanthropist", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American author and illustrator", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American painter (d. 1999)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter, guitarist, and drummer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English author and critic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Swedish politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Pakistani physicist and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani physicist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1950", "text": "<PERSON><PERSON> <PERSON><PERSON>, Welsh author and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Welsh author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Welsh author and academic", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer (d. 2020)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1951", "text": "<PERSON>, American baseball player and coach (d. 2024)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor and playwright", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Thai businessman and politician, Thai Minister of Energy", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Amranand\" title=\"<PERSON>yas<PERSON><PERSON> Amranand\"><PERSON><PERSON><PERSON><PERSON></a>, Thai businessman and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Energy_(Thailand)\" title=\"Ministry of Energy (Thailand)\">Thai Minister of Energy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yas<PERSON><PERSON>_Amranand\" title=\"<PERSON>yasvasti Amranand\"><PERSON><PERSON><PERSON><PERSON></a>, Thai businessman and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Energy_(Thailand)\" title=\"Ministry of Energy (Thailand)\">Thai Minister of Energy</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Piyasvasti_Amranand"}, {"title": "Ministry of Energy (Thailand)", "link": "https://wikipedia.org/wiki/Ministry_of_Energy_(Thailand)"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Mexican film, television, and stage actress and singer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Ang%C3%A9lica_Arag%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican film, television, and stage actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ang%C3%A9lica_Arag%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican film, television, and stage actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ang%C3%A9lica_Arag%C3%B3n"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Indian accountant and politician, Indian Minister of Railways", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hu\"><PERSON><PERSON></a>, Indian accountant and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Railways_(India)\" title=\"Ministry of Railways (India)\">Indian Minister of Railways</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hu\"><PERSON><PERSON></a>, Indian accountant and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Railways_(India)\" title=\"Ministry of Railways (India)\">Indian Minister of Railways</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rabhu"}, {"title": "Ministry of Railways (India)", "link": "https://wikipedia.org/wiki/Ministry_of_Railways_(India)"}]}, {"year": "1953", "text": "<PERSON>, Mexican actress, director, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sp%C3%ADndola\" title=\"<PERSON>\"><PERSON></a>, Mexican actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sp%C3%ADndola\" title=\"<PERSON>\"><PERSON></a>, Mexican actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sp%C3%ADndola"}]}, {"year": "1953", "text": "<PERSON>, American boxer (d. 2021)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Sterling"}]}, {"year": "1953", "text": "<PERSON>, South African physician and activist (d. 2008)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African physician and activist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African physician and activist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, English-Canadian conductor and composer (d. 2022)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tovey\"><PERSON><PERSON></a>, English-Canadian conductor and composer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tovey\"><PERSON><PERSON></a>, English-Canadian conductor and composer (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English engineer and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Singaporean neurosurgeon and politician, Singaporean Minister of Health (d. 2010)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Singaporean neurosurgeon and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Singapore)\" title=\"Ministry of Health (Singapore)\">Singaporean Minister of Health</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Singaporean neurosurgeon and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Singapore)\" title=\"Ministry of Health (Singapore)\">Singaporean Minister of Health</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Health (Singapore)", "link": "https://wikipedia.org/wiki/Ministry_of_Health_(Singapore)"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Indian-American author and academic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Amitav_Ghosh\" title=\"Amitav Ghosh\"><PERSON><PERSON><PERSON></a>, Indian-American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amitav_Ghosh\" title=\"Amitav Ghosh\"><PERSON><PERSON><PERSON></a>, Indian-American author and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amitav_Ghosh"}]}, {"year": "1956", "text": "<PERSON>, French actor and director", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Se<PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Se<PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON>la <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Scottish educator and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English singer-songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1957", "text": "<PERSON><PERSON>,  Irish Republican hunger striker (d. 1981)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Hara\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a> hunger striker (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Hara\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a> hunger striker (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patsy_O%27Hara"}, {"title": "Irish Republican", "link": "https://wikipedia.org/wiki/Irish_Republican"}]}, {"year": "1957", "text": "<PERSON>, Jamaican singer-songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Jamaican singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Jamaican singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1958", "text": "<PERSON>, American ballerina (d. 2022)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballerina (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballerina (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Mexican footballer, coach, and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1nchez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1nchez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hugo_S%C3%A1<PERSON>ez"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter, composer, and musician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, composer, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, composer, and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English banker and businessman", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player (d. 2007)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Ga%C3%A9<PERSON>_<PERSON>\" title=\"G<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ga%C3%A9<PERSON>_<PERSON>\" title=\"Gaé<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ga%C3%A9tan_<PERSON>ne"}]}, {"year": "1962", "text": "<PERSON>, Irish actress and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Japanese music artist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fuji<PERSON>\"><PERSON><PERSON></a>, Japanese music artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fuji<PERSON>\"><PERSON><PERSON></a>, Japanese music artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>miya_Fujii"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_MacInnis"}]}, {"year": "1963", "text": "<PERSON>, English rugby player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1963", "text": "<PERSON>, American actress and talk show host", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English actor and TV presenter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and TV presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and TV presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English footballer, manager, and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Dutch kick-boxer and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch kick-boxer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch kick-boxer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Pakistani-English author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani-English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani-English author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Japanese author and illustrator (d. 2021)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and illustrator (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and illustrator (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American basketball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rod_<PERSON>land"}]}, {"year": "1966", "text": "<PERSON>, Northern Irish musician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Indian American novelist and short story writer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian American novelist and short story writer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1968", "text": "<PERSON>, Canadian journalist and academic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian singer-songwriter (d. 2008)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>ser<PERSON>_<PERSON>\" title=\"<PERSON>ser<PERSON>lo\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ser<PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eser<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, British sports journalist and television presenter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British sports journalist and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British sports journalist and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Boulting"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, English lawyer and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American opera singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bass-baritone)\" title=\"<PERSON> (bass-baritone)\"><PERSON></a>, American opera singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bass-baritone)\" title=\"<PERSON> (bass-baritone)\"><PERSON></a>, American opera singer", "links": [{"title": "<PERSON> (bass-baritone)", "link": "https://wikipedia.org/wiki/<PERSON>_(bass-baritone)"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian cricketer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, English-Irish singer-songwriter, guitarist, and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Cormac_Battle\" title=\"Cormac Battle\"><PERSON><PERSON><PERSON> <PERSON></a>, English-Irish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cormac_Battle\" title=\"Cormac Battle\">Co<PERSON><PERSON> <PERSON></a>, English-Irish singer-songwriter, guitarist, and producer", "links": [{"title": "Cormac Battle", "link": "https://wikipedia.org/wiki/Cormac_Battle"}]}, {"year": "1972", "text": "<PERSON>, American actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Greek runner", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Lithuanian singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1nau\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1nau\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alanas_Cho%C5%A1nau"}]}, {"year": "1974", "text": "<PERSON>, Icelandic footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Icelandic footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Icelandic footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hermann_H<PERSON>i%C3%B0<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Dutch footballer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_tackle)\" title=\"<PERSON> (offensive tackle)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_tackle)\" title=\"<PERSON> (offensive tackle)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (offensive tackle)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_tackle)"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Spanish footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Baraja\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Baraja\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Baraja"}]}, {"year": "1975", "text": "<PERSON><PERSON> <PERSON>, American rapper and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>%27_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Mexican-American basketball player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1jera\" title=\"<PERSON>\"><PERSON></a>, Mexican-American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1jera\" title=\"<PERSON>\"><PERSON></a>, Mexican-American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eduardo_N%C3%A1jera"}]}, {"year": "1977", "text": "<PERSON>, American football player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian swimmer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Raio_Piiroja\" title=\"Raio Piiroja\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raio_Piiroja\" title=\"Raio Piiroja\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "Raio <PERSON>", "link": "https://wikipedia.org/wiki/Raio_Piiroja"}]}, {"year": "1980", "text": "<PERSON>, Canadian wrestler", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kidd\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kidd\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American soldier and author", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Venezuelan judge", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan judge", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1983", "text": "<PERSON><PERSON>, German-Turkish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Engin_Baytar\" title=\"Engin Baytar\"><PERSON><PERSON>tar</a>, German-Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Engin_Baytar\" title=\"Engin Baytar\"><PERSON><PERSON> Baytar</a>, German-Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Engin_Baytar"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Swedish singer and dancer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Venezuelan baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Yorman <PERSON>o\"><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Yorman Bazardo\"><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Canadian-American ice dancer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American ice dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Belbin"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American football player (d. 2024)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, South African rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Morn%C3%A9_<PERSON>ey<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morn%C3%A9_<PERSON>ey<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Morn%C3%A9_<PERSON>eyn"}]}, {"year": "1985", "text": "<PERSON>, American actor, director, and producer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Garc%C3%<PERSON><PERSON>_(footballer,_born_1986)\" title=\"<PERSON><PERSON> (footballer, born 1986)\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Garc%C3%<PERSON><PERSON>_(footballer,_born_1986)\" title=\"<PERSON><PERSON> (footballer, born 1986)\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1986)", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_Garc%C3%<PERSON><PERSON>_(footballer,_born_1986)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, French footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Japanese singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1988", "text": "<PERSON>, French footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Dutch singer, songwriter and dancer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer, songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer, songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Swedish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, German tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Polish-Spanish actor and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Spanish actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Spanish actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Danish tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Egyptian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American gymnast", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Finnish tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Salonen\"><PERSON><PERSON></a>, Finnish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Salonen\"><PERSON><PERSON></a>, Finnish tennis player", "links": [{"title": "<PERSON><PERSON>en", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Salonen"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Bart%C5%82<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bart%C5%82<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bart%C5%82<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Argentinian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Canadian singer-songwriter and dancer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Canadian singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alessia_Cara"}]}, {"year": "1997", "text": "<PERSON>, American baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Ivorian footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)"}]}], "Deaths": [{"year": "472", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 420)", "html": "472 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 420)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 420)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anthemius"}]}, {"year": "937", "text": "<PERSON> of Burgundy (b. 880)", "html": "937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Burgundy\" title=\"<PERSON> II of Burgundy\"><PERSON> of Burgundy</a> (b. 880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Burgundy\" title=\"<PERSON> of Burgundy\"><PERSON> of Burgundy</a> (b. 880)", "links": [{"title": "<PERSON> of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Burgundy"}]}, {"year": "969", "text": "<PERSON> of Kiev (b. 890)", "html": "969 - <a href=\"https://wikipedia.org/wiki/Olga_of_Kiev\" title=\"<PERSON> of Kiev\"><PERSON> of Kiev</a> (b. 890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olga_of_Kiev\" title=\"<PERSON> of Kiev\"><PERSON> of Kiev</a> (b. 890)", "links": [{"title": "Olga of Kiev", "link": "https://wikipedia.org/wiki/Olga_of_Kiev"}]}, {"year": "1174", "text": "<PERSON><PERSON><PERSON> of Jerusalem (b. 1136)", "html": "1174 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Jerusalem\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Jerusalem\"><PERSON><PERSON><PERSON> of Jerusalem</a> (b. 1136)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Jerusalem\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Jerusalem\"><PERSON><PERSON><PERSON> of Jerusalem</a> (b. 1136)", "links": [{"title": "<PERSON><PERSON><PERSON> of Jerusalem", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Jerusalem"}]}, {"year": "1183", "text": "<PERSON>, Duke of Bavaria (b. 1117)", "html": "1183 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1117)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1117)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>bach,_Duke_of_Bavaria"}]}, {"year": "1302", "text": "<PERSON>, Count of Artois (b. 1250)", "html": "1302 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Artois\" title=\"<PERSON>, Count of Artois\"><PERSON>, Count of Artois</a> (b. 1250)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Artois\" title=\"<PERSON>, Count of Artois\"><PERSON>, Count of Artois</a> (b. 1250)", "links": [{"title": "<PERSON>, Count of Artois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1302", "text": "<PERSON>, French politician and lawyer", "html": "1302 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1344", "text": "<PERSON>, Count of Württemberg (b. c. 1286)", "html": "1344 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_W%C3%BCrttemberg\" title=\"<PERSON>, Count of Württemberg\"><PERSON>, Count of Württemberg</a> (b. c. 1286)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_W%C3%BCrttemberg\" title=\"<PERSON>, Count of Württemberg\"><PERSON>, Count of Württemberg</a> (b. c. 1286)", "links": [{"title": "<PERSON>, Count of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_W%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1362", "text": "<PERSON>, empress of <PERSON> (b. 1339)", "html": "1362 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, empress of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV</a> (b. 1339)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, empress of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV</a> (b. 1339)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1382", "text": "<PERSON>, French philosopher (b. 1325)", "html": "1382 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher (b. 1325)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher (b. 1325)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1451", "text": "<PERSON> of Cilli, Slovenian noblewoman", "html": "1451 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cilli\" title=\"<PERSON> of Cilli\"><PERSON> of Cilli</a>, Slovenian noblewoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Cilli\"><PERSON> of Cilli</a>, Slovenian noblewoman", "links": [{"title": "<PERSON> of Cilli", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1484", "text": "<PERSON><PERSON>, Italian sculptor (b. c. 1429)", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fi<PERSON>\"><PERSON><PERSON></a>, Italian sculptor (b. c. 1429)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fi<PERSON>\"><PERSON><PERSON></a>, Italian sculptor (b. c. 1429)", "links": [{"title": "<PERSON><PERSON> Fi<PERSON>ole", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ole"}]}, {"year": "1535", "text": "<PERSON>, Elector of Brandenburg (b. 1484)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (b. 1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (b. 1484)", "links": [{"title": "<PERSON>, Elector of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>ector_of_Brandenburg"}]}, {"year": "1581", "text": "<PERSON><PERSON><PERSON>, Danish admiral and politician (b. 1503)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish admiral and politician (b. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish admiral and politician (b. 1503)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1593", "text": "<PERSON>, Italian painter (b. 1527)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1527)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giuseppe_Arcimboldo"}]}, {"year": "1599", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese daimyō (b. 1539)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/Ch%C5%8Dsokabe_Motochika\" title=\"Chōsokabe Motochika\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ch%C5%8Dsokabe_Motochika\" title=\"Chōsokabe Motochika\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1539)", "links": [{"title": "Chōsokabe Motochika", "link": "https://wikipedia.org/wiki/Ch%C5%8Dsokabe_Motochika"}]}, {"year": "1688", "text": "<PERSON><PERSON>, Thai king (b. 1629)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/Narai\" title=\"Narai\"><PERSON><PERSON></a>, Thai king (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narai\" title=\"Narai\"><PERSON><PERSON></a>, Thai king (b. 1629)", "links": [{"title": "Narai", "link": "https://wikipedia.org/wiki/Narai"}]}, {"year": "1774", "text": "Sir <PERSON>, 1st Baronet, Irish-English general (b. 1715)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, Irish-English general (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, Irish-English general (b. 1715)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}]}, {"year": "1775", "text": "<PERSON>, American farmer and politician (b. 1724)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (b. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Romanian historian and philologist (b. 1740)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/Ien%C4%83chi%C8%9B%C4%83_V%C4%83c%C4%83rescu\" title=\"Ienăchiță Văcărescu\">Ienăchiț<PERSON></a>, Romanian historian and philologist (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ien%C4%83chi%C8%9B%C4%83_V%C4%83c%C4%83rescu\" title=\"Ienăchiță Văcărescu\">Ienăchiț<PERSON></a>, Romanian historian and philologist (b. 1740)", "links": [{"title": "Ienăchi<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ien%C4%83chi%C8%9B%C4%83_V%C4%83c%C4%83rescu"}]}, {"year": "1806", "text": "<PERSON>, Irish-American lawyer and politician (b. 1719)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(delegate)\" class=\"mw-redirect\" title=\"<PERSON> (delegate)\"><PERSON></a>, Irish-American lawyer and politician (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(delegate)\" class=\"mw-redirect\" title=\"<PERSON> (delegate)\"><PERSON></a>, Irish-American lawyer and politician (b. 1719)", "links": [{"title": "<PERSON> (delegate)", "link": "https://wikipedia.org/wiki/<PERSON>_(delegate)"}]}, {"year": "1825", "text": "<PERSON>, American soldier and politician (b. 1744)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Grosvenor"}]}, {"year": "1844", "text": "<PERSON><PERSON><PERSON>, Russian philosopher and poet (b. 1800)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian philosopher and poet (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian philosopher and poet (b. 1800)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Irish-Australian politician, 11th Premier of New South Wales (b. 1831)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1905", "text": "<PERSON>, Egyptian jurist and scholar (b. 1849)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian jurist and scholar (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian jurist and scholar (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, German sprinter and tennis player (b. 1876)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter and tennis player (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter and tennis player (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Canadian-American astronomer and mathematician (b. 1835)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American astronomer and mathematician (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American astronomer and mathematician (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English footballer and engraver (b. 1857)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and engraver (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and engraver (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American pianist, songwriter, and composer (b. 1898)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, songwriter, and composer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, songwriter, and composer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English cricketer, coach, and umpire (b. 1882)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer, coach, and umpire (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer, coach, and umpire (b. 1882)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American poet and short story writer (b. 1913)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Schwartz\"><PERSON><PERSON></a>, American poet and short story writer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Schwartz\"><PERSON><PERSON></a>, American poet and short story writer (b. 1913)", "links": [{"title": "<PERSON><PERSON> Schwartz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian lawyer, judge, and politician, 28th Canadian Minister of Justice (b. 1917)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 28th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 28th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1971", "text": "<PERSON>, American journalist and author (b. 1910)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Mexican racing driver (b. 1940)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Mexican racing driver (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Mexican racing driver (b. 1940)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/Pedro_Rodr%C3%<PERSON><PERSON><PERSON>_(racing_driver)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Swedish novelist, playwright, and poet Nobel Prize laureate (b. 1891)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/P%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish novelist, playwright, and poet <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish novelist, playwright, and poet <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A4<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1976", "text": "<PERSON>, Colombian poet and educator (b. 1895)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Le%C3%B3n_de_Greiff\" title=\"<PERSON> de Greiff\"><PERSON></a>, Colombian poet and educator (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le%C3%B3n_de_Greiff\" title=\"<PERSON> Greiff\"><PERSON></a>, Colombian poet and educator (b. 1895)", "links": [{"title": "León de Greiff", "link": "https://wikipedia.org/wiki/Le%C3%B3n_de_<PERSON>reiff"}]}, {"year": "1979", "text": "<PERSON>, Canadian lawyer, judge, and politician (b. 1925)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American-Canadian author (b. 1915)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Israeli footballer (b. 1963)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avi_Ran"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American rabbi and scholar (b. 1901)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rabbi and scholar (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rabbi and scholar (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English actor, director, and producer (b. 1907)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Malaysian footballer and coach (b. 1953)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian footballer and coach (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian footballer and coach (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American computer scientist, founded Digital Research (b. 1942)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, founded <a href=\"https://wikipedia.org/wiki/Digital_Research\" title=\"Digital Research\">Digital Research</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, founded <a href=\"https://wikipedia.org/wiki/Digital_Research\" title=\"Digital Research\">Digital Research</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Digital Research", "link": "https://wikipedia.org/wiki/Digital_Research"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek philosopher and author (b. 1943)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek philosopher and author (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek philosopher and author (b. 1943)", "links": [{"title": "Panagi<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>agiot<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American singer (b. 1917)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Dutch computer scientist and electronics technician (b. 1945)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch computer scientist and electronics technician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch computer scientist and electronics technician (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Dominican lawyer, author, and poet (b. 1913)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican lawyer, author, and poet (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican lawyer, author, and poet (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English archbishop (b. 1921)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Dutch musician and painter (b. 1946)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch musician and painter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch musician and painter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Iranian-Canadian freelance photographer (b. 1948)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-Canadian freelance photographer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-Canadian freelance photographer (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American financier and philanthropist (b. 1910)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American financier and philanthropist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American financier and philanthropist (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, French actress and producer (b. 1904)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress and producer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress and producer (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON>-Cyr"}]}, {"year": "2005", "text": "<PERSON>, English actress and dancer (b. 1911)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and dancer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and dancer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Argentinian racing driver (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Iglesias\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian racing driver (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Iglesias\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian racing driver (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Iglesias"}]}, {"year": "2005", "text": "<PERSON>, American actress and singer (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actor (b. 1915)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Australian sculptor (b. 1959)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Oliver\"><PERSON><PERSON><PERSON></a>, Australian sculptor (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian sculptor (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Oliver"}]}, {"year": "2006", "text": "<PERSON>, English snooker player and sportscaster (b. 1935)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player and sportscaster (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player and sportscaster (b. 1935)", "links": [{"title": "<PERSON> (snooker player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Australian author and academic (b. 1939)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Glenda_Adams\" title=\"Glenda Adams\"><PERSON><PERSON></a>, Australian author and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glenda_Adams\" title=\"Glenda Adams\"><PERSON><PERSON></a>, Australian author and academic (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Glen<PERSON>_Adams"}]}, {"year": "2007", "text": "<PERSON> <PERSON>, American beautification activist; 43rd First Lady of the United States (b. 1912)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American beautification activist; 43rd <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American beautification activist; 43rd <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (b. 1912)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "2007", "text": "<PERSON>, Colombian lawyer and politician, 32nd President of Colombia (b. 1913)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Alfonso_L%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonso_L%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfonso_L%C3%B3<PERSON><PERSON>_<PERSON>"}, {"title": "President of Colombia", "link": "https://wikipedia.org/wiki/President_of_Colombia"}]}, {"year": "2007", "text": "<PERSON>, American-Canadian businessman and philanthropist, founded Honest Ed's (b. 1914)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Honest_Ed%27s\" title=\"Honest Ed's\">Honest Ed's</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Honest_Ed%27s\" title=\"Honest Ed's\">Honest Ed's</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Honest Ed's", "link": "https://wikipedia.org/wiki/Honest_Ed%27s"}]}, {"year": "2008", "text": "<PERSON>, American surgeon and educator (b. 1908)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American surgeon and educator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American surgeon and educator (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Canadian-American ice hockey player (b. 1936)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Italian-Canadian boxer (b. 1972)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Canadian boxer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Canadian boxer (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Chinese linguist and paleographer (b. 1911)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese linguist and paleographer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ji <PERSON>\"><PERSON></a>, Chinese linguist and paleographer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Iranian-American inventor (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-American inventor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-American inventor (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>an"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, German mathematician and academic (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mathematician and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mathematician and academic (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American admiral (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American bassist and composer (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Swedish author and screenwriter (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and screenwriter (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American basketball player (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Hungarian-American drummer and producer (b. 1949)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American drummer and producer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American drummer and producer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American journalist and academic (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American architect, designed the Taubman Museum of Art (b. 1958)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Taubman_Museum_of_Art\" title=\"Taubman Museum of Art\">Taubman Museum of Art</a> (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Taubman_Museum_of_Art\" title=\"Taubman Museum of Art\">Taubman Museum of Art</a> (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Taubman Museum of Art", "link": "https://wikipedia.org/wiki/Taubman_Museum_of_Art"}]}, {"year": "2015", "text": "<PERSON>, Italian cardinal (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>i"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Japanese game programmer and businessman (b. 1959)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> I<PERSON>\"><PERSON><PERSON></a>, Japanese game programmer and businessman (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese game programmer and businessman (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "2015", "text": "<PERSON>, Belgian businessman (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Canadian poet (b. 1949)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American attorney and men's rights activist, Vice-president of the National Coalition for Men (b. 1968)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and men's rights activist, Vice-president of the <a href=\"https://wikipedia.org/wiki/National_Coalition_for_Men\" title=\"National Coalition for Men\">National Coalition for Men</a> (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and men's rights activist, Vice-president of the <a href=\"https://wikipedia.org/wiki/National_Coalition_for_Men\" title=\"National Coalition for Men\">National Coalition for Men</a> (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Coalition for Men", "link": "https://wikipedia.org/wiki/National_Coalition_for_Men"}]}, {"year": "2020", "text": "<PERSON>, American baseball second baseman (b. 1931)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball second baseman (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball second baseman (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor (b. 1945)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1945)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2021", "text": "<PERSON><PERSON>, French actress (b. 1911)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Czech-French writer (b. 1929)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Milan_Kundera\" title=\"<PERSON>\"><PERSON></a>, Czech-French writer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Kundera\" title=\"<PERSON>\"><PERSON></a>, Czech-French writer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milan_Kundera"}]}, {"year": "2024", "text": "<PERSON>, American actress (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American football coach (b. 1940)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}