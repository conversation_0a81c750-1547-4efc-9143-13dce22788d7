{"date": "March 8", "url": "https://wikipedia.org/wiki/March_8", "data": {"Events": [{"year": "1010", "text": "<PERSON><PERSON><PERSON><PERSON> completes his epic poem <PERSON><PERSON><PERSON>.", "html": "1010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>i\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> completes his epic poem <i><a href=\"https://wikipedia.org/wiki/Shahnameh\" title=\"Shah<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>i\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> completes his epic poem <i><a href=\"https://wikipedia.org/wiki/Shahnameh\" title=\"Shahname<PERSON>\">Shahname<PERSON></a></i>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferdowsi"}, {"title": "<PERSON>name<PERSON>", "link": "https://wikipedia.org/wiki/Shahnameh"}]}, {"year": "1126", "text": "Following the death of his mother, queen <PERSON><PERSON><PERSON> of León, <PERSON> is proclaimed king of León.", "html": "1126 - Following the death of his mother, queen <a href=\"https://wikipedia.org/wiki/Urraca_of_Le%C3%B3n\" class=\"mw-redirect\" title=\"Urraca of León\">Urraca of León</a>, <a href=\"https://wikipedia.org/wiki/Alfonso_VII_of_Le%C3%B3n_and_Castile\" title=\"<PERSON> VII of León and Castile\"><PERSON> VII</a> is proclaimed king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a>.", "no_year_html": "Following the death of his mother, queen <a href=\"https://wikipedia.org/wiki/Urraca_of_Le%C3%B3n\" class=\"mw-redirect\" title=\"Urraca of León\">Urraca of León</a>, <a href=\"https://wikipedia.org/wiki/Alfonso_VII_of_Le%C3%B3n_and_Castile\" title=\"<PERSON> VII of León and Castile\"><PERSON> VII</a> is proclaimed king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a>.", "links": [{"title": "Urraca of León", "link": "https://wikipedia.org/wiki/Urraca_of_Le%C3%B3n"}, {"title": "Alfonso VII of León and Castile", "link": "https://wikipedia.org/wiki/Alfonso_VII_of_Le%C3%B3n_and_Castile"}, {"title": "Kingdom of León", "link": "https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n"}]}, {"year": "1262", "text": "Battle of Hausbergen between bourgeois militias and the army of the bishop of Strasbourg.", "html": "1262 - <a href=\"https://wikipedia.org/wiki/Battle_of_Hausbergen\" title=\"Battle of Hausbergen\">Battle of Hausbergen</a> between bourgeois militias and the army of the bishop of <a href=\"https://wikipedia.org/wiki/Strasbourg\" title=\"Strasbourg\">Strasbourg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Hausbergen\" title=\"Battle of Hausbergen\">Battle of Hausbergen</a> between bourgeois militias and the army of the bishop of <a href=\"https://wikipedia.org/wiki/Strasbourg\" title=\"Strasbourg\">Strasbourg</a>.", "links": [{"title": "Battle of Hausbergen", "link": "https://wikipedia.org/wiki/Battle_of_Hausbergen"}, {"title": "Strasbourg", "link": "https://wikipedia.org/wiki/Strasbourg"}]}, {"year": "1558", "text": "The city of Pori (Swedish: Björneborg) is founded by Duke <PERSON> on the shores of the Gulf of Bothnia.", "html": "1558 - The city of <a href=\"https://wikipedia.org/wiki/Pori\" title=\"Pori\">Pori</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\">Björneborg</i>) is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Sweden)\" class=\"mw-redirect\" title=\"<PERSON> (Sweden)\">Duke <PERSON></a> on the shores of the <a href=\"https://wikipedia.org/wiki/Gulf_of_Bothnia\" title=\"Gulf of Bothnia\">Gulf of Bothnia</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Pori\" title=\"Pori\">Pori</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\">Björneborg</i>) is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Sweden)\" class=\"mw-redirect\" title=\"<PERSON> (Sweden)\">Duke <PERSON></a> on the shores of the <a href=\"https://wikipedia.org/wiki/Gulf_of_Bothnia\" title=\"Gulf of Bothnia\">Gulf of Bothnia</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ri"}, {"title": "Swedish language", "link": "https://wikipedia.org/wiki/Swedish_language"}, {"title": "<PERSON> (Sweden)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Sweden)"}, {"title": "Gulf of Bothnia", "link": "https://wikipedia.org/wiki/Gulf_of_Bothnia"}]}, {"year": "1658", "text": "Treaty of Roskilde: After a devastating defeat in the Northern Wars (1655-1661), <PERSON>, the King of Denmark-Norway is forced to give up nearly half his territory to Sweden.", "html": "1658 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Roskilde\" title=\"Treaty of Roskilde\">Treaty of Roskilde</a>: After a devastating defeat in the <a href=\"https://wikipedia.org/wiki/Northern_Wars\" title=\"Northern Wars\">Northern Wars</a> (1655-1661), <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"Frederick III of Denmark\"><PERSON> III</a>, the King of <a href=\"https://wikipedia.org/wiki/Denmark%E2%80%93Norway\" title=\"Denmark-Norway\">Denmark-Norway</a> is forced to give up nearly half his territory to Sweden.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Roskilde\" title=\"Treaty of Roskilde\">Treaty of Roskilde</a>: After a devastating defeat in the <a href=\"https://wikipedia.org/wiki/Northern_Wars\" title=\"Northern Wars\">Northern Wars</a> (1655-1661), <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"Frederick III of Denmark\"><PERSON></a>, the King of <a href=\"https://wikipedia.org/wiki/Denmark%E2%80%93Norway\" title=\"Denmark-Norway\">Denmark-Norway</a> is forced to give up nearly half his territory to Sweden.", "links": [{"title": "Treaty of Roskilde", "link": "https://wikipedia.org/wiki/Treaty_of_Roskilde"}, {"title": "Northern Wars", "link": "https://wikipedia.org/wiki/Northern_Wars"}, {"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark"}, {"title": "Denmark-Norway", "link": "https://wikipedia.org/wiki/Denmark%E2%80%93Norway"}]}, {"year": "1702", "text": "<PERSON> <PERSON>, the younger sister of <PERSON>, becomes Queen regnant of England, Scotland, and Ireland.", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Great_Britain\" title=\"<PERSON>, Queen of Great Britain\">Queen <PERSON></a>, the younger sister of <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" class=\"mw-redirect\" title=\"Mary II of England\"><PERSON> II</a>, becomes <a href=\"https://wikipedia.org/wiki/Queen_regnant\" title=\"Queen regnant\">Queen regnant</a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a>, and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ireland\" title=\"Kingdom of Ireland\">Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Great_Britain\" title=\"<PERSON>, Queen of Great Britain\">Queen <PERSON></a>, the younger sister of <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" class=\"mw-redirect\" title=\"Mary II of England\"><PERSON> II</a>, becomes <a href=\"https://wikipedia.org/wiki/Queen_regnant\" title=\"Queen regnant\">Queen regnant</a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a>, and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ireland\" title=\"Kingdom of Ireland\">Ireland</a>.", "links": [{"title": "<PERSON>, Queen of Great Britain", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Great_Britain"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Mary_<PERSON>_of_England"}, {"title": "Queen regnant", "link": "https://wikipedia.org/wiki/Queen_regnant"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}, {"title": "Kingdom of Ireland", "link": "https://wikipedia.org/wiki/Kingdom_of_Ireland"}]}, {"year": "1722", "text": "The Safavid Empire of Iran is defeated by an army from Afghanistan at the Battle of Gulnabad.", "html": "1722 - The <a href=\"https://wikipedia.org/wiki/Safavid_Empire\" class=\"mw-redirect\" title=\"Safavid Empire\">Safavid Empire</a> of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> is defeated by an army from <a href=\"https://wikipedia.org/wiki/History_of_Afghanistan\" title=\"History of Afghanistan\">Afghanistan</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Gulnabad\" title=\"Battle of Gulnabad\">Battle of Gulnabad</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Safavid_Empire\" class=\"mw-redirect\" title=\"Safavid Empire\">Safavid Empire</a> of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> is defeated by an army from <a href=\"https://wikipedia.org/wiki/History_of_Afghanistan\" title=\"History of Afghanistan\">Afghanistan</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Gulnabad\" title=\"Battle of Gulnabad\">Battle of Gulnabad</a>.", "links": [{"title": "Safavid Empire", "link": "https://wikipedia.org/wiki/Safavid_Empire"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "History of Afghanistan", "link": "https://wikipedia.org/wiki/History_of_Afghanistan"}, {"title": "Battle of Gulnabad", "link": "https://wikipedia.org/wiki/Battle_of_Gulnabad"}]}, {"year": "1736", "text": "<PERSON><PERSON>, founder of the Afsharid dynasty, is crowned Shah of Iran.", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/Afsharid_dynasty\" title=\"Afsharid dynasty\">Afsharid dynasty</a>, is crowned <a href=\"https://wikipedia.org/wiki/Shah_of_Iran\" class=\"mw-redirect\" title=\"Shah of Iran\">Shah of Iran</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/Afsharid_dynasty\" title=\"Afsharid dynasty\">Afsharid dynasty</a>, is crowned <a href=\"https://wikipedia.org/wiki/Shah_of_Iran\" class=\"mw-redirect\" title=\"Shah of Iran\">Shah of Iran</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Afsharid dynasty", "link": "https://wikipedia.org/wiki/Afsharid_dynasty"}, {"title": "Shah of Iran", "link": "https://wikipedia.org/wiki/Shah_of_Iran"}]}, {"year": "1775", "text": "An anonymous writer, thought by some to be <PERSON>, publishes \"African Slavery in America\", the first article in the American colonies calling for the emancipation of slaves and the abolition of slavery.", "html": "1775 - An anonymous writer, thought by some to be <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, publishes \"African Slavery in America\", the first article in the American colonies calling for the <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">emancipation</a> of slaves and the abolition of slavery.", "no_year_html": "An anonymous writer, thought by some to be <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, publishes \"African Slavery in America\", the first article in the American colonies calling for the <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">emancipation</a> of slaves and the abolition of slavery.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Abolitionism in the United States", "link": "https://wikipedia.org/wiki/Abolitionism_in_the_United_States"}]}, {"year": "1782", "text": "Gnadenhutten massacre: Ninety-six Native Americans in Gnadenhutten, Ohio, who had converted to Christianity, are killed by Pennsylvania militiamen in retaliation for raids carried out by other Indian tribes.", "html": "1782 - <a href=\"https://wikipedia.org/wiki/Gnadenhutten_massacre\" title=\"Gnadenhutten massacre\">Gnadenhutten massacre</a>: Ninety-six <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native Americans</a> in <a href=\"https://wikipedia.org/wiki/Gnadenhutten,_Ohio\" title=\"Gnadenhutten, Ohio\">Gnadenhutten, Ohio</a>, who had converted to <a href=\"https://wikipedia.org/wiki/Christianity\" title=\"Christianity\">Christianity</a>, are killed by <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> militiamen in retaliation for raids carried out by other Indian tribes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gnadenhutten_massacre\" title=\"Gnadenhutten massacre\">Gnadenhutten massacre</a>: Ninety-six <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native Americans</a> in <a href=\"https://wikipedia.org/wiki/Gnadenhutten,_Ohio\" title=\"Gnadenhutten, Ohio\">Gnadenhutten, Ohio</a>, who had converted to <a href=\"https://wikipedia.org/wiki/Christianity\" title=\"Christianity\">Christianity</a>, are killed by <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> militiamen in retaliation for raids carried out by other Indian tribes.", "links": [{"title": "Gnadenhutten massacre", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON><PERSON>_massacre"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}, {"title": "Gnadenhutten, Ohio", "link": "https://wikipedia.org/wiki/Gnadenhutten,_Ohio"}, {"title": "Christianity", "link": "https://wikipedia.org/wiki/Christianity"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}]}, {"year": "1801", "text": "War of the Second Coalition: At the Battle of Abukir, a British force under Sir <PERSON> lands in Egypt with the aim of ending the French campaign in Egypt and Syria.", "html": "1801 - <a href=\"https://wikipedia.org/wiki/War_of_the_Second_Coalition\" title=\"War of the Second Coalition\">War of the Second Coalition</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Abukir_(1801)\" title=\"Battle of Abukir (1801)\">Battle of Abukir</a>, a British force under <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in Egypt with the aim of ending the <a href=\"https://wikipedia.org/wiki/French_campaign_in_Egypt_and_Syria\" class=\"mw-redirect\" title=\"French campaign in Egypt and Syria\">French campaign in Egypt and Syria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Second_Coalition\" title=\"War of the Second Coalition\">War of the Second Coalition</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Abukir_(1801)\" title=\"Battle of Abukir (1801)\">Battle of Abukir</a>, a British force under <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in Egypt with the aim of ending the <a href=\"https://wikipedia.org/wiki/French_campaign_in_Egypt_and_Syria\" class=\"mw-redirect\" title=\"French campaign in Egypt and Syria\">French campaign in Egypt and Syria</a>.", "links": [{"title": "War of the Second Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Second_Coalition"}, {"title": "Battle of Abukir (1801)", "link": "https://wikipedia.org/wiki/Battle_of_Abukir_(1801)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "French campaign in Egypt and Syria", "link": "https://wikipedia.org/wiki/French_campaign_in_Egypt_and_Syria"}]}, {"year": "1844", "text": "King <PERSON> ascends to the thrones of Sweden and Norway.", "html": "1844 - King <a href=\"https://wikipedia.org/wiki/Oscar_I_of_Sweden\" title=\"Oscar I of Sweden\"><PERSON> I</a> ascends to the thrones of <a href=\"https://wikipedia.org/wiki/Union_between_Sweden_and_Norway\" title=\"Union between Sweden and Norway\">Sweden and Norway</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Oscar_<PERSON>_of_Sweden\" title=\"Oscar I of Sweden\"><PERSON> I</a> ascends to the thrones of <a href=\"https://wikipedia.org/wiki/Union_between_Sweden_and_Norway\" title=\"Union between Sweden and Norway\">Sweden and Norway</a>.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}, {"title": "Union between Sweden and Norway", "link": "https://wikipedia.org/wiki/Union_between_Sweden_and_Norway"}]}, {"year": "1844", "text": "The Althing, the parliament of Iceland, was reopened after 45 years of closure.", "html": "1844 - The <a href=\"https://wikipedia.org/wiki/Althing\" title=\"Althing\">Althing</a>, the parliament of <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a>, was reopened after 45 years of closure.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Althing\" title=\"Althing\">Althing</a>, the parliament of <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a>, was reopened after 45 years of closure.", "links": [{"title": "Alt<PERSON>", "link": "https://wikipedia.org/wiki/Althing"}, {"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}]}, {"year": "1868", "text": "Sakai incident: Japanese samurai kill 11 French sailors in the port of Sakai, Osaka.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Sakai_incident\" title=\"Sakai incident\">Sakai incident</a>: Japanese <a href=\"https://wikipedia.org/wiki/Samurai\" title=\"Samurai\">samurai</a> kill 11 French sailors in the port of <a href=\"https://wikipedia.org/wiki/Sakai,_Osaka\" class=\"mw-redirect\" title=\"Sakai, Osaka\">Sakai, Osaka</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sakai_incident\" title=\"Sakai incident\">Sakai incident</a>: Japanese <a href=\"https://wikipedia.org/wiki/Samurai\" title=\"Samurai\">samurai</a> kill 11 French sailors in the port of <a href=\"https://wikipedia.org/wiki/Sakai,_Osaka\" class=\"mw-redirect\" title=\"Sakai, Osaka\">Sakai, Osaka</a>.", "links": [{"title": "Sakai incident", "link": "https://wikipedia.org/wiki/Sa<PERSON>_incident"}, {"title": "Samurai", "link": "https://wikipedia.org/wiki/Samurai"}, {"title": "Sakai, Osaka", "link": "https://wikipedia.org/wiki/Sakai,_Osaka"}]}, {"year": "1910", "text": "French aviator <PERSON><PERSON> becomes the first woman to receive a pilot's license.", "html": "1910 - French <a href=\"https://wikipedia.org/wiki/Aviator\" class=\"mw-redirect\" title=\"Aviator\">aviator</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first woman to receive a <a href=\"https://wikipedia.org/wiki/Pilot_licensing_and_certification\" title=\"Pilot licensing and certification\">pilot's license</a>.", "no_year_html": "French <a href=\"https://wikipedia.org/wiki/Aviator\" class=\"mw-redirect\" title=\"Aviator\">aviator</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first woman to receive a <a href=\"https://wikipedia.org/wiki/Pilot_licensing_and_certification\" title=\"Pilot licensing and certification\">pilot's license</a>.", "links": [{"title": "Aviator", "link": "https://wikipedia.org/wiki/Aviator"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Pilot licensing and certification", "link": "https://wikipedia.org/wiki/Pilot_licensing_and_certification"}]}, {"year": "1916", "text": "World War I: A British force unsuccessfully attempts to relieve the siege of Kut (present-day Iraq) in the Battle of Dujaila.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: A British force unsuccessfully attempts to relieve the siege of <a href=\"https://wikipedia.org/wiki/Kut\" title=\"Kut\"><PERSON>t</a> (present-day <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>) in the <a href=\"https://wikipedia.org/wiki/Battle_of_Dujaila\" title=\"Battle of Dujaila\">Battle of Dujaila</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: A British force unsuccessfully attempts to relieve the siege of <a href=\"https://wikipedia.org/wiki/Kut\" title=\"Kut\"><PERSON><PERSON></a> (present-day <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>) in the <a href=\"https://wikipedia.org/wiki/Battle_of_Dujaila\" title=\"Battle of Dujaila\">Battle of Dujaila</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>t"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Battle of Dujaila", "link": "https://wikipedia.org/wiki/Battle_of_Dujaila"}]}, {"year": "1917", "text": "International Women's Day protests in Petrograd mark the beginning of the February Revolution (February 23 in the Julian calendar).", "html": "1917 - <a href=\"https://wikipedia.org/wiki/International_Women%27s_Day\" title=\"International Women's Day\">International Women's Day</a> protests in <a href=\"https://wikipedia.org/wiki/Petrograd\" class=\"mw-redirect\" title=\"Petrograd\">Petrograd</a> mark the beginning of the <a href=\"https://wikipedia.org/wiki/February_Revolution\" title=\"February Revolution\">February Revolution</a> (February 23 in the <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/International_Women%27s_Day\" title=\"International Women's Day\">International Women's Day</a> protests in <a href=\"https://wikipedia.org/wiki/Petrograd\" class=\"mw-redirect\" title=\"Petrograd\">Petrograd</a> mark the beginning of the <a href=\"https://wikipedia.org/wiki/February_Revolution\" title=\"February Revolution\">February Revolution</a> (February 23 in the <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>).", "links": [{"title": "International Women's Day", "link": "https://wikipedia.org/wiki/International_Women%27s_Day"}, {"title": "Petrograd", "link": "https://wikipedia.org/wiki/Petrograd"}, {"title": "February Revolution", "link": "https://wikipedia.org/wiki/February_Revolution"}, {"title": "Julian calendar", "link": "https://wikipedia.org/wiki/Julian_calendar"}]}, {"year": "1917", "text": "The United States Senate votes to limit filibusters by adopting the cloture rule.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> votes to limit <a href=\"https://wikipedia.org/wiki/Filibuster\" title=\"Filibuster\">filibusters</a> by adopting the <a href=\"https://wikipedia.org/wiki/Cloture_rule\" class=\"mw-redirect\" title=\"Cloture rule\">cloture rule</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> votes to limit <a href=\"https://wikipedia.org/wiki/Filibuster\" title=\"Filibuster\">filibusters</a> by adopting the <a href=\"https://wikipedia.org/wiki/Cloture_rule\" class=\"mw-redirect\" title=\"Cloture rule\">cloture rule</a>.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "Filibuster", "link": "https://wikipedia.org/wiki/Filibuster"}, {"title": "Cloture rule", "link": "https://wikipedia.org/wiki/Cloture_rule"}]}, {"year": "1921", "text": "Spanish Prime Minister <PERSON> is assassinated while on his way home from the parliament building in Madrid.", "html": "1921 - Spanish Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated while on his way home from the <a href=\"https://wikipedia.org/wiki/Cortes_Generales\" title=\"Cortes Generales\">parliament</a> building in <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>.", "no_year_html": "Spanish Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated while on his way home from the <a href=\"https://wikipedia.org/wiki/Cortes_Generales\" title=\"Cortes Generales\">parliament</a> building in <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cortes Generales", "link": "https://wikipedia.org/wiki/Cortes_Generales"}, {"title": "Madrid", "link": "https://wikipedia.org/wiki/Madrid"}]}, {"year": "1924", "text": "A mine disaster kills 172 coal miners near Castle Gate, Utah.", "html": "1924 - A <a href=\"https://wikipedia.org/wiki/Castle_Gate_Mine_disaster\" title=\"Castle Gate Mine disaster\">mine disaster</a> kills 172 coal miners near <a href=\"https://wikipedia.org/wiki/Castle_Gate,_Utah\" title=\"Castle Gate, Utah\">Castle Gate, Utah</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Castle_Gate_Mine_disaster\" title=\"Castle Gate Mine disaster\">mine disaster</a> kills 172 coal miners near <a href=\"https://wikipedia.org/wiki/Castle_Gate,_Utah\" title=\"Castle Gate, Utah\">Castle Gate, Utah</a>.", "links": [{"title": "Castle Gate Mine disaster", "link": "https://wikipedia.org/wiki/Castle_Gate_Mine_disaster"}, {"title": "Castle Gate, Utah", "link": "https://wikipedia.org/wiki/Castle_Gate,_Utah"}]}, {"year": "1936", "text": "Daytona Beach and Road Course holds its first oval stock car race.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Daytona_Beach_and_Road_Course\" title=\"Daytona Beach and Road Course\">Daytona Beach and Road Course</a> holds its first oval <a href=\"https://wikipedia.org/wiki/Stock_car_racing\" title=\"Stock car racing\">stock car</a> race.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Daytona_Beach_and_Road_Course\" title=\"Daytona Beach and Road Course\">Daytona Beach and Road Course</a> holds its first oval <a href=\"https://wikipedia.org/wiki/Stock_car_racing\" title=\"Stock car racing\">stock car</a> race.", "links": [{"title": "Daytona Beach and Road Course", "link": "https://wikipedia.org/wiki/Daytona_Beach_and_Road_Course"}, {"title": "Stock car racing", "link": "https://wikipedia.org/wiki/Stock_car_racing"}]}, {"year": "1937", "text": "Spanish Civil War: The Battle of Guadalajara begins.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Guadalajara\" title=\"Battle of Guadalajara\">Battle of Guadalajara</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Guadalajara\" title=\"Battle of Guadalajara\">Battle of Guadalajara</a> begins.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Battle of Guadalajara", "link": "https://wikipedia.org/wiki/Battle_of_Guadalajara"}]}, {"year": "1942", "text": "World War II: The Dutch East Indies surrender Java to the Imperial Japanese Army.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a> surrender <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a> to the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a> surrender <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a> to the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Dutch East Indies", "link": "https://wikipedia.org/wiki/Dutch_East_Indies"}, {"title": "Java", "link": "https://wikipedia.org/wiki/Java"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}]}, {"year": "1942", "text": "World War II: Imperial Japanese Army forces captured Rangoon, Burma from British.", "html": "1942 - World War II: Imperial Japanese Army forces captured <a href=\"https://wikipedia.org/wiki/Yangon\" title=\"Yangon\">Rangoon</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a> from <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">British</a>.", "no_year_html": "World War II: Imperial Japanese Army forces captured <a href=\"https://wikipedia.org/wiki/Yangon\" title=\"Yangon\">Rangoon</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a> from <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">British</a>.", "links": [{"title": "Yangon", "link": "https://wikipedia.org/wiki/Yangon"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}]}, {"year": "1950", "text": "The iconic Volkswagen Type 2 \"Bus\" begins production.", "html": "1950 - The iconic <a href=\"https://wikipedia.org/wiki/Volkswagen_Type_2\" title=\"Volkswagen Type 2\">Volkswagen Type 2</a> \"Bus\" begins production.", "no_year_html": "The iconic <a href=\"https://wikipedia.org/wiki/Volkswagen_Type_2\" title=\"Volkswagen Type 2\">Volkswagen Type 2</a> \"Bus\" begins production.", "links": [{"title": "Volkswagen Type 2", "link": "https://wikipedia.org/wiki/Volkswagen_Type_2"}]}, {"year": "1962", "text": "A Turkish Airlines Fokker F27 Friendship crashes into Mount Medetsiz in the Taurus Mountains of Turkey, killing all 11 people on board.", "html": "1962 - A <a href=\"https://wikipedia.org/wiki/Turkish_Airlines\" title=\"Turkish Airlines\">Turkish Airlines</a> <a href=\"https://wikipedia.org/wiki/Fokker_F27_Friendship\" title=\"Fokker F27 Friendship\">Fokker F27 Friendship</a> <a href=\"https://wikipedia.org/wiki/1962_Taurus_Mountains_Turkish_Airlines_F-27_crash\" title=\"1962 Taurus Mountains Turkish Airlines F-27 crash\">crashes</a> into <a href=\"https://wikipedia.org/wiki/Mount_Medetsiz\" title=\"Mount Medetsiz\">Mount Medetsiz</a> in the <a href=\"https://wikipedia.org/wiki/Taurus_Mountains\" title=\"Taurus Mountains\">Taurus Mountains</a> of Turkey, killing all 11 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Turkish_Airlines\" title=\"Turkish Airlines\">Turkish Airlines</a> <a href=\"https://wikipedia.org/wiki/Fokker_F27_Friendship\" title=\"Fokker F27 Friendship\">Fokker F27 Friendship</a> <a href=\"https://wikipedia.org/wiki/1962_Taurus_Mountains_Turkish_Airlines_F-27_crash\" title=\"1962 Taurus Mountains Turkish Airlines F-27 crash\">crashes</a> into <a href=\"https://wikipedia.org/wiki/Mount_Medetsiz\" title=\"Mount Medetsiz\">Mount Medetsiz</a> in the <a href=\"https://wikipedia.org/wiki/Taurus_Mountains\" title=\"Taurus Mountains\">Taurus Mountains</a> of Turkey, killing all 11 people on board.", "links": [{"title": "Turkish Airlines", "link": "https://wikipedia.org/wiki/Turkish_Airlines"}, {"title": "Fokker F27 Friendship", "link": "https://wikipedia.org/wiki/Fokker_F27_Friendship"}, {"title": "1962 Taurus Mountains Turkish Airlines F-27 crash", "link": "https://wikipedia.org/wiki/1962_Taurus_Mountains_Turkish_Airlines_F-27_crash"}, {"title": "Mount Medetsiz", "link": "https://wikipedia.org/wiki/Mount_Medetsiz"}, {"title": "Taurus Mountains", "link": "https://wikipedia.org/wiki/Taurus_Mountains"}]}, {"year": "1963", "text": "The Ba'ath Party comes to power in Syria in a coup d'état.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Ba%27ath_Party\" title=\"Ba'ath Party\">Ba'ath Party</a> comes to power in <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> in <a href=\"https://wikipedia.org/wiki/1963_Syrian_coup_d%27%C3%A9tat\" title=\"1963 Syrian coup d'état\">a coup d'état</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ba%27ath_Party\" title=\"Ba'ath Party\">Ba'ath Party</a> comes to power in <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> in <a href=\"https://wikipedia.org/wiki/1963_Syrian_coup_d%27%C3%A9tat\" title=\"1963 Syrian coup d'état\">a coup d'état</a>.", "links": [{"title": "Ba'ath Party", "link": "https://wikipedia.org/wiki/Ba%27ath_Party"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "1963 Syrian coup d'état", "link": "https://wikipedia.org/wiki/1963_Syrian_coup_d%27%C3%A9tat"}]}, {"year": "1965", "text": "Vietnam War: US Marines arrive at Da Nang.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">US Marines</a> arrive at <a href=\"https://wikipedia.org/wiki/<PERSON>_Nang\" title=\"Da Nang\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">US Marines</a> arrive at <a href=\"https://wikipedia.org/wiki/<PERSON>_Nang\" title=\"Da Nang\"><PERSON></a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "1965", "text": "Aeroflot Flight 513 crashes during takeoff from Kuybyshev Airport, killing 30 and injuring 9.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_513\" title=\"Aeroflot Flight 513\">Aeroflot Flight 513</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Kurumoch_International_Airport\" title=\"Kurumoch International Airport\">Kuybyshev Airport</a>, killing 30 and injuring 9.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_513\" title=\"Aeroflot Flight 513\">Aeroflot Flight 513</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Kurumoch_International_Airport\" title=\"Kurumoch International Airport\">Kuybyshev Airport</a>, killing 30 and injuring 9.", "links": [{"title": "Aeroflot Flight 513", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_513"}, {"title": "Kurumoch International Airport", "link": "https://wikipedia.org/wiki/Kurumoch_International_Airport"}]}, {"year": "1966", "text": "<PERSON>'s Pillar in Dublin, Ireland, is destroyed by a bomb.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Pillar\" title=\"<PERSON>'s Pillar\"><PERSON>'s Pillar</a> in Dublin, Ireland, is destroyed by a bomb.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Pillar\" title=\"<PERSON>'s Pillar\"><PERSON>'s <PERSON>llar</a> in Dublin, Ireland, is destroyed by a bomb.", "links": [{"title": "<PERSON>'s Pillar", "link": "https://wikipedia.org/wiki/<PERSON>%27s_Pillar"}]}, {"year": "1979", "text": "Philips demonstrates the compact disc publicly for the first time.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Philips\" title=\"Philips\">Philips</a> demonstrates the <a href=\"https://wikipedia.org/wiki/Compact_disc\" title=\"Compact disc\">compact disc</a> publicly for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philips\" title=\"Philips\">Philips</a> demonstrates the <a href=\"https://wikipedia.org/wiki/Compact_disc\" title=\"Compact disc\">compact disc</a> publicly for the first time.", "links": [{"title": "Philips", "link": "https://wikipedia.org/wiki/Philips"}, {"title": "Compact disc", "link": "https://wikipedia.org/wiki/Compact_disc"}]}, {"year": "1979", "text": "Images taken by Voyager 1 prove the existence of volcanoes on Io, a moon of Jupiter.", "html": "1979 - Images taken by <a href=\"https://wikipedia.org/wiki/Voyager_1\" title=\"Voyager 1\">Voyager 1</a> prove the <a href=\"https://wikipedia.org/wiki/Volcanism_on_Io\" title=\"Volcanism on Io\">existence of volcanoes</a> on <a href=\"https://wikipedia.org/wiki/Io_(moon)\" title=\"Io (moon)\">Io</a>, a <a href=\"https://wikipedia.org/wiki/Moon_of_Jupiter\" class=\"mw-redirect\" title=\"Moon of Jupiter\">moon of Jupiter</a>.", "no_year_html": "Images taken by <a href=\"https://wikipedia.org/wiki/Voyager_1\" title=\"Voyager 1\">Voyager 1</a> prove the <a href=\"https://wikipedia.org/wiki/Volcanism_on_Io\" title=\"Volcanism on Io\">existence of volcanoes</a> on <a href=\"https://wikipedia.org/wiki/Io_(moon)\" title=\"Io (moon)\">Io</a>, a <a href=\"https://wikipedia.org/wiki/Moon_of_Jupiter\" class=\"mw-redirect\" title=\"Moon of Jupiter\">moon of Jupiter</a>.", "links": [{"title": "Voyager 1", "link": "https://wikipedia.org/wiki/Voyager_1"}, {"title": "Volcanism on Io", "link": "https://wikipedia.org/wiki/Volcanism_on_Io"}, {"title": "<PERSON><PERSON> (moon)", "link": "https://wikipedia.org/wiki/Io_(moon)"}, {"title": "Moon of Jupiter", "link": "https://wikipedia.org/wiki/Moon_of_Jupiter"}]}, {"year": "1983", "text": "Cold War: While addressing a convention of Evangelicals, U.S. President <PERSON> labels the Soviet Union an \"evil empire\".", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: While addressing <a href=\"https://wikipedia.org/wiki/National_Association_of_Evangelicals\" title=\"National Association of Evangelicals\">a convention of Evangelicals</a>, U.S. <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> labels the Soviet Union an \"<a href=\"https://wikipedia.org/wiki/Evil_Empire_speech\" title=\"Evil Empire speech\">evil empire</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: While addressing <a href=\"https://wikipedia.org/wiki/National_Association_of_Evangelicals\" title=\"National Association of Evangelicals\">a convention of Evangelicals</a>, U.S. <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> labels the Soviet Union an \"<a href=\"https://wikipedia.org/wiki/Evil_Empire_speech\" title=\"Evil Empire speech\">evil empire</a>\".", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "National Association of Evangelicals", "link": "https://wikipedia.org/wiki/National_Association_of_Evangelicals"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Evil Empire speech", "link": "https://wikipedia.org/wiki/Evil_Empire_speech"}]}, {"year": "1985", "text": "A supposed failed assassination attempt on Islamic cleric <PERSON><PERSON> in Beirut, Lebanon kills 80 and injures 200 others.", "html": "1985 - A <a href=\"https://wikipedia.org/wiki/1985_Beirut_car_bombings\" title=\"1985 Beirut car bombings\">supposed failed assassination attempt</a> on Islamic cleric <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Say<PERSON></a> in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut, Lebanon</a> kills 80 and injures 200 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1985_Beirut_car_bombings\" title=\"1985 Beirut car bombings\">supposed failed assassination attempt</a> on Islamic cleric <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut, Lebanon</a> kills 80 and injures 200 others.", "links": [{"title": "1985 Beirut car bombings", "link": "https://wikipedia.org/wiki/1985_Beirut_car_bombings"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}]}, {"year": "1988", "text": "Aeroflot Flight 3379 is hijacked by the <PERSON><PERSON><PERSON><PERSON> family and diverted to Veshchevo in the Soviet Union.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_3739_(1988)\" title=\"Aeroflot Flight 3739 (1988)\">Aeroflot Flight 3379</a> is hijacked by the <PERSON><PERSON><PERSON><PERSON> family and diverted to <a href=\"https://wikipedia.org/wiki/Veshchevo\" title=\"Veshchevo\"><PERSON><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_3739_(1988)\" title=\"Aeroflot Flight 3739 (1988)\">Aeroflot Flight 3379</a> is hijacked by the <PERSON><PERSON><PERSON><PERSON> family and diverted to <a href=\"https://wikipedia.org/wiki/Veshchevo\" title=\"Vesh<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Aeroflot Flight 3739 (1988)", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_3739_(1988)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veshchevo"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1994", "text": "A collision at Indira Gandhi International Airport kills 9 people.", "html": "1994 - A collision at <a href=\"https://wikipedia.org/wiki/Indira_Gandhi_International_Airport\" title=\"Indira Gandhi International Airport\">Indira Gandhi International Airport</a> kills 9 people.", "no_year_html": "A collision at <a href=\"https://wikipedia.org/wiki/Indira_Gandhi_International_Airport\" title=\"Indira Gandhi International Airport\">Indira Gandhi International Airport</a> kills 9 people.", "links": [{"title": "Indira Gandhi International Airport", "link": "https://wikipedia.org/wiki/Indira_Gandhi_International_Airport"}]}, {"year": "2004", "text": "A new constitution is signed by Iraq's Governing Council.", "html": "2004 - A new <a href=\"https://wikipedia.org/wiki/Law_of_Administration_for_the_State_of_Iraq_for_the_Transitional_Period\" class=\"mw-redirect\" title=\"Law of Administration for the State of Iraq for the Transitional Period\">constitution</a> is signed by <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>'s <a href=\"https://wikipedia.org/wiki/Iraqi_Governing_Council\" title=\"Iraqi Governing Council\">Governing Council</a>.", "no_year_html": "A new <a href=\"https://wikipedia.org/wiki/Law_of_Administration_for_the_State_of_Iraq_for_the_Transitional_Period\" class=\"mw-redirect\" title=\"Law of Administration for the State of Iraq for the Transitional Period\">constitution</a> is signed by <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>'s <a href=\"https://wikipedia.org/wiki/Iraqi_Governing_Council\" title=\"Iraqi Governing Council\">Governing Council</a>.", "links": [{"title": "Law of Administration for the State of Iraq for the Transitional Period", "link": "https://wikipedia.org/wiki/Law_of_Administration_for_the_State_of_Iraq_for_the_Transitional_Period"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Iraqi Governing Council", "link": "https://wikipedia.org/wiki/Iraqi_Governing_Council"}]}, {"year": "2010", "text": "Headlined by <PERSON> and <PERSON><PERSON>, TNA Wrestling moved its flagship program, TNA Impact!, to Monday night. This effort to go \"big time live\" failed but is notable in the history of professional wrestling television.", "html": "2010 - Headlined by <a href=\"https://wikipedia.org/wiki/<PERSON>_Hogan\" title=\"<PERSON> Hogan\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lair\" title=\"<PERSON><PERSON>lair\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Total_Nonstop_Action_Wrestling\" title=\"Total Nonstop Action Wrestling\">TNA Wrestling</a> moved its flagship program, <a href=\"https://wikipedia.org/wiki/TNA_Impact!\" title=\"TNA Impact!\">TNA Impact!</a>, to Monday night. This effort to go \"big time live\" failed but is notable in the history of professional wrestling television.", "no_year_html": "Headlined by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hogan\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lair\" title=\"<PERSON><PERSON>lair\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Total_Nonstop_Action_Wrestling\" title=\"Total Nonstop Action Wrestling\">TNA Wrestling</a> moved its flagship program, <a href=\"https://wikipedia.org/wiki/TNA_Impact!\" title=\"TNA Impact!\">TNA Impact!</a>, to Monday night. This effort to go \"big time live\" failed but is notable in the history of professional wrestling television.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Total Nonstop Action Wrestling", "link": "https://wikipedia.org/wiki/Total_Nonstop_Action_Wrestling"}, {"title": "TNA Impact!", "link": "https://wikipedia.org/wiki/TNA_Impact!"}]}, {"year": "2014", "text": "In one of aviation's greatest mysteries, Malaysia Airlines Flight 370, carrying a total of 239 people, disappears en route from Kuala Lumpur to Beijing. The fate of the flight remains unknown.", "html": "2014 - In one of aviation's greatest mysteries, <a href=\"https://wikipedia.org/wiki/Malaysia_Airlines_Flight_370\" title=\"Malaysia Airlines Flight 370\">Malaysia Airlines Flight 370</a>, carrying a total of 239 people, disappears en route from <a href=\"https://wikipedia.org/wiki/Kuala_Lumpur\" title=\"Kuala Lumpur\">Kuala Lumpur</a> to Beijing. The fate of the flight remains unknown.", "no_year_html": "In one of aviation's greatest mysteries, <a href=\"https://wikipedia.org/wiki/Malaysia_Airlines_Flight_370\" title=\"Malaysia Airlines Flight 370\">Malaysia Airlines Flight 370</a>, carrying a total of 239 people, disappears en route from <a href=\"https://wikipedia.org/wiki/Kuala_Lumpur\" title=\"Kuala Lumpur\">Kuala Lumpur</a> to Beijing. The fate of the flight remains unknown.", "links": [{"title": "Malaysia Airlines Flight 370", "link": "https://wikipedia.org/wiki/Malaysia_Airlines_Flight_370"}, {"title": "Kuala Lumpur", "link": "https://wikipedia.org/wiki/Kuala_Lumpur"}]}, {"year": "2017", "text": "The Azure Window, a natural arch on the Maltese island of Gozo, collapses in stormy weather.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/Azure_Window\" title=\"Azure Window\">Azure Window</a>, a natural arch on the <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Maltese</a> island of <a href=\"https://wikipedia.org/wiki/Gozo\" title=\"Gozo\">Gozo</a>, collapses in stormy weather.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Azure_Window\" title=\"Azure Window\">Azure Window</a>, a natural arch on the <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Maltese</a> island of <a href=\"https://wikipedia.org/wiki/Gozo\" title=\"Gozo\">Gozo</a>, collapses in stormy weather.", "links": [{"title": "Azure Window", "link": "https://wikipedia.org/wiki/Azure_Window"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gozo"}]}, {"year": "2018", "text": "The first Aurat March (social/political demonstration) was held being International Women's Day in Karachi, Pakistan, since then annually held  across Pakistan and feminist slogan <PERSON>ra <PERSON> (My body, my choice), in demand for women's right to bodily autonomy and against gender-based violence came into vogue in Pakistan.", "html": "2018 - The first <a href=\"https://wikipedia.org/wiki/Aurat_March\" title=\"Aurat March\">Aurat March</a> (social/political demonstration) was held being <a href=\"https://wikipedia.org/wiki/International_Women%27s_Day\" title=\"International Women's Day\">International Women's Day</a> in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>, Pakistan, since then annually held across Pakistan and feminist slogan <a href=\"https://wikipedia.org/wiki/Mera_Jism_Meri_<PERSON>\" title=\"Mera Jism Meri <PERSON>\">Mera Jism <PERSON></a> (<a href=\"https://wikipedia.org/wiki/My_body,_my_choice\" title=\"My body, my choice\">My body, my choice</a>), in demand for <a href=\"https://wikipedia.org/wiki/Women%27s_right\" class=\"mw-redirect\" title=\"Women's right\">women's right</a> to <a href=\"https://wikipedia.org/wiki/Bodily_autonomy\" class=\"mw-redirect\" title=\"Bodily autonomy\">bodily autonomy</a> and against <a href=\"https://wikipedia.org/wiki/Gender-based_violence\" class=\"mw-redirect\" title=\"Gender-based violence\">gender-based violence</a> came into vogue in Pakistan.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Aurat_March\" title=\"Aurat March\">Aurat March</a> (social/political demonstration) was held being <a href=\"https://wikipedia.org/wiki/International_Women%27s_Day\" title=\"International Women's Day\">International Women's Day</a> in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>, Pakistan, since then annually held across Pakistan and feminist slogan <a href=\"https://wikipedia.org/wiki/Mera_Jism_Meri_<PERSON>\" title=\"Mera Jism Me<PERSON>\">Mera Jism <PERSON></a> (<a href=\"https://wikipedia.org/wiki/My_body,_my_choice\" title=\"My body, my choice\">My body, my choice</a>), in demand for <a href=\"https://wikipedia.org/wiki/Women%27s_right\" class=\"mw-redirect\" title=\"Women's right\">women's right</a> to <a href=\"https://wikipedia.org/wiki/Bodily_autonomy\" class=\"mw-redirect\" title=\"Bodily autonomy\">bodily autonomy</a> and against <a href=\"https://wikipedia.org/wiki/Gender-based_violence\" class=\"mw-redirect\" title=\"Gender-based violence\">gender-based violence</a> came into vogue in Pakistan.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurat_March"}, {"title": "International Women's Day", "link": "https://wikipedia.org/wiki/International_Women%27s_Day"}, {"title": "Karachi", "link": "https://wikipedia.org/wiki/Karachi"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "My body, my choice", "link": "https://wikipedia.org/wiki/My_body,_my_choice"}, {"title": "Women's right", "link": "https://wikipedia.org/wiki/Women%27s_right"}, {"title": "Bodily autonomy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_autonomy"}, {"title": "Gender-based violence", "link": "https://wikipedia.org/wiki/Gender-based_violence"}]}, {"year": "2021", "text": "International Women's Day marches in Mexico become violent with 62 police officers and 19 civilians injured in Mexico City alone.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/International_Women%27s_Day\" title=\"International Women's Day\">International Women's Day</a> marches in <a href=\"https://wikipedia.org/wiki/2021_in_Mexico\" title=\"2021 in Mexico\">Mexico</a> become violent with 62 police officers and 19 civilians injured in <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City</a> alone.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/International_Women%27s_Day\" title=\"International Women's Day\">International Women's Day</a> marches in <a href=\"https://wikipedia.org/wiki/2021_in_Mexico\" title=\"2021 in Mexico\">Mexico</a> become violent with 62 police officers and 19 civilians injured in <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City</a> alone.", "links": [{"title": "International Women's Day", "link": "https://wikipedia.org/wiki/International_Women%27s_Day"}, {"title": "2021 in Mexico", "link": "https://wikipedia.org/wiki/2021_in_Mexico"}, {"title": "Mexico City", "link": "https://wikipedia.org/wiki/Mexico_City"}]}, {"year": "2021", "text": "Twenty-eight political institutions in Myanmar establish the National Unity Consultative Council, a historic alliance of ethnic armed organizations and democratically elected leaders in response to the 2021 Myanmar coup d'état", "html": "2021 - Twenty-eight political institutions in Myanmar establish the <a href=\"https://wikipedia.org/wiki/National_Unity_Consultative_Council\" title=\"National Unity Consultative Council\">National Unity Consultative Council</a>, a historic alliance of ethnic armed organizations and democratically elected leaders in response to the <a href=\"https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat\" title=\"2021 Myanmar coup d'état\">2021 Myanmar coup d'état</a>", "no_year_html": "Twenty-eight political institutions in Myanmar establish the <a href=\"https://wikipedia.org/wiki/National_Unity_Consultative_Council\" title=\"National Unity Consultative Council\">National Unity Consultative Council</a>, a historic alliance of ethnic armed organizations and democratically elected leaders in response to the <a href=\"https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat\" title=\"2021 Myanmar coup d'état\">2021 Myanmar coup d'état</a>", "links": [{"title": "National Unity Consultative Council", "link": "https://wikipedia.org/wiki/National_Unity_Consultative_Council"}, {"title": "2021 Myanmar coup d'état", "link": "https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat"}]}], "Births": [{"year": "1495", "text": "<PERSON> of God, Portuguese friar and saint (d. 1550)", "html": "1495 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of God\"><PERSON> of God</a>, Portuguese friar and saint (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of God\"><PERSON> of God</a>, Portuguese friar and saint (d. 1550)", "links": [{"title": "John of God", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1712", "text": "<PERSON>, English physician and botanist (d. 1780)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician and botanist (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician and botanist (d. 1780)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}]}, {"year": "1714", "text": "<PERSON>, German pianist and composer (d. 1788)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON>, 1st <PERSON>, English admiral and politician, Treasurer of the Navy (d. 1799)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st <PERSON></a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Treasurer_of_the_Navy\" title=\"Treasurer of the Navy\">Treasurer of the Navy</a> (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Treasurer_of_the_Navy\" title=\"Treasurer of the Navy\">Treasurer of the Navy</a> (d. 1799)", "links": [{"title": "<PERSON>, 1st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>"}, {"title": "Treasurer of the Navy", "link": "https://wikipedia.org/wiki/Treasurer_of_the_Navy"}]}, {"year": "1746", "text": "<PERSON>, French botanist and explorer (d. 1802)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Mi<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and explorer (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and explorer (d. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1748", "text": "<PERSON>, Prince of Orange (d. 1806)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (d. 1806)", "links": [{"title": "<PERSON>, Prince of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange"}]}, {"year": "1761", "text": "<PERSON>, Polish ethnologist, historian, linguist, and author (d. 1815)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish ethnologist, historian, linguist, and author (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish ethnologist, historian, linguist, and author (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, American journalist and politician, United States Secretary of War (d. 1889)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}]}, {"year": "1804", "text": "<PERSON><PERSON>, American astronomer and optician (d. 1887)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astronomer and optician (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astronomer and optician (d. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON><PERSON><PERSON>, Polish inventor and businessman, invented the Kerosene lamp (d. 1882)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Ignacy_%C5%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish inventor and businessman, invented the <a href=\"https://wikipedia.org/wiki/Kerosene_lamp\" title=\"Kerosene lamp\">Kerosene lamp</a> (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ignacy_%C5%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish inventor and businessman, invented the <a href=\"https://wikipedia.org/wiki/Kerose<PERSON>_lamp\" title=\"Kerosene lamp\">Kerosene lamp</a> (d. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ignacy_%C5%<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Ke<PERSON>ne lamp", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, German linguist and anthropologist (d. 1875)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German linguist and anthropologist (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German linguist and anthropologist (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Portuguese poet and educator (d. 1896)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese poet and educator (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese poet and educator (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, English businesswoman and founder the jewellery retailer <PERSON><PERSON> (d. 1908)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman and founder the jewellery retailer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman and founder the jewellery retailer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, American lawyer and jurist (d. 1935)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and jurist (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American lawyer and jurist (d. 1935)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1851", "text": "<PERSON>, American librarian and educator (d. 1914)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian and educator (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian and educator (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON>, English 2nd General of The Salvation Army (d. 1929)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Bramwell Booth\"><PERSON><PERSON></a>, English 2nd <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Bramwell Booth\"><PERSON><PERSON></a>, English 2nd <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1856", "text": "<PERSON>, American painter and academic (d. 1937)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, American diarist and homesteader (d. 1915)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diarist and homesteader (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>l\"><PERSON></a>, American diarist and homesteader (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>l"}]}, {"year": "1859", "text": "<PERSON>, British author (d. 1932)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British author (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British author (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American type designer (d. 1947)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American type designer (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American type designer (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 1968)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1886", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 1972)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Uruguayan poet and author (d. 1979)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ibarbouro<PERSON>\"><PERSON><PERSON></a>, Uruguayan poet and author (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> de Ibarbourou\"><PERSON><PERSON></a>, Uruguayan poet and author (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Canadian journalist and politician, 46th Mayor of Ottawa (d. 1975)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 46th <a href=\"https://wikipedia.org/wiki/Mayor_of_Ottawa\" title=\"Mayor of Ottawa\">Mayor of Ottawa</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 46th <a href=\"https://wikipedia.org/wiki/Mayor_of_Ottawa\" title=\"Mayor of Ottawa\">Mayor of Ottawa</a> (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Ottawa", "link": "https://wikipedia.org/wiki/Mayor_of_Ottawa"}]}, {"year": "1902", "text": "<PERSON>, American actress and singer (d. 1962)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American journalist and politician (d. 1998)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Greek lawyer and politician, President of Greece (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}]}, {"year": "1909", "text": "<PERSON>, English motorcycle racer and engineer (d. 1990)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer and engineer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer and engineer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actress (d. 2000)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Armenian-American pianist and composer (d. 2000)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-American pianist and composer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-American pianist and composer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American businessman and politician, Governor of Texas (d. 2003)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American businessman and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American businessman and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (d. 2003)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}, {"title": "Governor of Texas", "link": "https://wikipedia.org/wiki/Governor_of_Texas"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, American publisher and politician, Governor of New Hampshire (d. 2001)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>.</a>, American publisher and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American publisher and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Jr."}, {"title": "Governor of New Hampshire", "link": "https://wikipedia.org/wiki/Governor_of_New_Hampshire"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Belarusian-Russian physicist and astronomer (d. 1987)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27dovich\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-Russian physicist and astronomer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27dovich\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-Russian physicist and astronomer (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27dovich"}]}, {"year": "1918", "text": "<PERSON>, Scottish-American actress (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American actress (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor and restaurateur (d. 1990)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor and restaurateur (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor and restaurateur (d. 1990)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "1922", "text": "<PERSON>, German-American video game designer, created the Magnavox Odyssey (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American video game designer, created the <a href=\"https://wikipedia.org/wiki/Magnavox_Odyssey\" title=\"Magnavox Odyssey\">Magnavox Odyssey</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American video game designer, created the <a href=\"https://wikipedia.org/wiki/Magnavox_Odyssey\" title=\"Magnavox Odyssey\">Magnavox Odyssey</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Magnavox Odyssey", "link": "https://wikipedia.org/wiki/Magnavox_Odyssey"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American actress and dancer (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American baseball player (d. 1989)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Japanese author and illustrator (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English sculptor and illustrator (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and illustrator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and illustrator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Irish-American actor and director (d. 2003)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor and director (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor and director (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American civil rights activist and labor leader (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American civil rights activist and labor leader (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American civil rights activist and labor leader (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American scholar, author, and academic (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, author, and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, author, and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Spanish actor, director, and screenwriter (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and screenwriter (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Filipino actor and politician (d. 2020)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, Filipino actor and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, Filipino actor and politician (d. 2020)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1928", "text": "<PERSON><PERSON>, American novelist (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>re_<PERSON>l"}]}, {"year": "1930", "text": "<PERSON>, American baseball player (d. 1996)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 1996)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1930", "text": "<PERSON>, English politician", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, South African cricketer (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American author and educator", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American author and social critic (d. 2003)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and social critic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and social critic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English-Canadian animator, director, and producer (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian animator, director, and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian animator, director, and producer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American baseball player and scout (d. 2006)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Marv_Breeding\" title=\"Marv Breeding\"><PERSON><PERSON></a>, American baseball player and scout (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marv_Breeding\" title=\"Marv Breeding\"><PERSON><PERSON></a>, American baseball player and scout (d. 2006)", "links": [{"title": "<PERSON><PERSON> Breeding", "link": "https://wikipedia.org/wiki/Marv_Breeding"}]}, {"year": "1935", "text": "<PERSON>, American saxophonist, composer, and bandleader", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian businessman (d. 1986)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Pandit<PERSON><PERSON>_<PERSON>\" title=\"Pan<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>dit<PERSON><PERSON>_<PERSON>\" title=\"Pandit<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panditra<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actress and singer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter and author (d. 1966)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and author (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and author (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_Fari%C3%B1a"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, Rwandan politician, President of Rwanda (d. 1994)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Juv%C3%A9nal_Habyarimana\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Rwandan politician, <a href=\"https://wikipedia.org/wiki/President_of_Rwanda\" class=\"mw-redirect\" title=\"President of Rwanda\">President of Rwanda</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juv%C3%A9nal_Habyarimana\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Rwandan politician, <a href=\"https://wikipedia.org/wiki/President_of_Rwanda\" class=\"mw-redirect\" title=\"President of Rwanda\">President of Rwanda</a> (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juv%C3%A9nal_Habyarimana"}, {"title": "President of Rwanda", "link": "https://wikipedia.org/wiki/President_of_Rwanda"}]}, {"year": "1939", "text": "<PERSON>, American baseball player and journalist (d. 2019)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian ballerina and choreographer (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ballerina and choreographer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ballerina and choreographer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Russian speed skater and coach", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian speed skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian speed skater and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Li<PERSON><PERSON>_<PERSON>likova"}]}, {"year": "1939", "text": "<PERSON>, Welsh tenor and conductor (d. 2011)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh tenor and conductor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh tenor and conductor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, British historian, author, and academic (d. 2019)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Stone\"><PERSON></a>, British historian, author, and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norman_Stone\" title=\"Norman Stone\"><PERSON></a>, British historian, author, and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American baseball player and tenor (d. 2020)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and tenor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and tenor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English sprinter, hurdler, and long jumper", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter, hurdler, and long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter, hurdler, and long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian actress and producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English-American actress and singer (d. 2010)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress and singer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress and singer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Russian singer-songwriter and guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Russian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Russian singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American singer-songwriter and actor", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, German painter and sculptor", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>sel<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter and sculptor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sel<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter and bass player (d. 2023)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author, founded Project Gutenberg (d. 2011)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Gutenberg\" title=\"Project Gutenberg\">Project Gutenberg</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Gutenberg\" title=\"Project Gutenberg\"><PERSON> Gutenberg</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Project Gutenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2008)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> G<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mel_Galley"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> March\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> March\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_March"}]}, {"year": "1948", "text": "<PERSON>, English rabbi, philosopher, and scholar (d. 2020)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English rabbi, philosopher, and scholar (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English rabbi, philosopher, and scholar (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Peruvian footballer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_C<PERSON>llas\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> C<PERSON>llas\"><PERSON><PERSON><PERSON></a>, Peruvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_C<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Cubillas\"><PERSON><PERSON><PERSON></a>, Peruvian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Zambian-English cricketer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American tap dancer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tap dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tap dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American baseball player, coach, and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American documentary filmmaker", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" class=\"mw-redirect\" title=\"<PERSON> (producer)\"><PERSON></a>, American documentary filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" class=\"mw-redirect\" title=\"<PERSON> (producer)\"><PERSON></a>, American documentary filmmaker", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "1954", "text": "<PERSON>, Sri Lankan-Scottish swimmer (d. 2024)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Sri Lankan-Scottish swimmer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Sri Lankan-Scottish swimmer (d. 2024)", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1956", "text": "<PERSON>, English footballer (d. 1989)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American economist and government official", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and government official", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and government official", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English rock drummer (d. 2013)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock drummer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock drummer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American pianist and composer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American journalist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Russian ballet dancer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ballet dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ballet dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American basketball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1962", "text": "<PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, <PERSON> of Battle, English politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Battle\" title=\"<PERSON>, <PERSON> of Battle\"><PERSON>, Baron <PERSON> of Battle</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_<PERSON>\" title=\"<PERSON>, <PERSON> of Battle\"><PERSON>, Baron <PERSON> of Battle</a>, English politician", "links": [{"title": "<PERSON>, <PERSON> of Battle", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German race car driver", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American football player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player and actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Swedish journalist and author", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sun<PERSON>tr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sun<PERSON>tr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lena_Sundstr%C3%B6m"}]}, {"year": "1973", "text": "<PERSON>, Austrian-German actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_(ice_hockey)"}]}, {"year": "1976", "text": "<PERSON>, Dominican baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>naci%C3%B3n\" title=\"Juan Encarnación\"><PERSON>nac<PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i%C3%B3n\" title=\"Juan Encarnación\"><PERSON></a>, Dominican baseball player", "links": [{"title": "Juan Encarnación", "link": "https://wikipedia.org/wiki/Juan_Encarnaci%C3%B3n"}]}, {"year": "1976", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1976", "text": "<PERSON><PERSON>, Korean-American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Hines_Ward\" title=\"Hines Ward\"><PERSON><PERSON></a>, Korean-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hines_Ward\" title=\"Hines Ward\"><PERSON><PERSON></a>, Korean-American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Swiss footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English singer-songwriter and musician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Swedish ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Greek footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leon<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American YouTuber", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Keemstar\" title=\"Keemstar\"><PERSON><PERSON><PERSON></a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Keemstar\" title=\"Keems<PERSON>\"><PERSON><PERSON><PERSON></a>, American YouTuber", "links": [{"title": "Keemstar", "link": "https://wikipedia.org/wiki/Keemstar"}]}, {"year": "1982", "text": "<PERSON>, American tattoo artist and model", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Kat <PERSON>\"><PERSON></a>, American tattoo artist and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Kat Von <PERSON>\"><PERSON></a>, American tattoo artist and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Brazilian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(baseball)\" title=\"<PERSON><PERSON><PERSON><PERSON> (baseball)\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(baseball)\" title=\"<PERSON><PERSON><PERSON><PERSON> (baseball)\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(baseball)"}]}, {"year": "1984", "text": "<PERSON>, New Zealand cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Slovenian basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Slovenian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Slovenian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vuja%C4%8Di%C4%87"}]}, {"year": "1985", "text": "<PERSON>, Finnish politician and researcher", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish politician and researcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish politician and researcher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American wrestler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gable\" title=\"<PERSON> G<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chad_Gable\" title=\"<PERSON> Gable\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chad_Gable"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Uzbekistani-American actress and comedian", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> V<PERSON>nt<PERSON>b\"><PERSON><PERSON></a>, Uzbekistani-American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uzbekistani-American actress and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milana_Vayntrub"}]}, {"year": "1987", "text": "<PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1988", "text": "<PERSON>, American record producer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Blanco\" title=\"<PERSON> Blanco\"><PERSON></a>, American record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Blanco\" title=\"Benny Blanco\"><PERSON></a>, American record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American basketball player and sportscaster", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>arge\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ar<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kristinia_DeBarge"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>endi\" title=\"<PERSON>ier Illarramendi\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>endi\" title=\"<PERSON><PERSON> Illarramendi\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asier_Illarramendi"}]}, {"year": "1990", "text": "<PERSON>, American-Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Czech tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_K<PERSON>tov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Petra_Kvitov%C3%A1"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, South Korean singer and actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-sung\" title=\"<PERSON><PERSON>-sung\"><PERSON><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-sung\" title=\"<PERSON><PERSON>-sung\"><PERSON><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON><PERSON>sung", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Japanese basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Scottish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Serbian basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marko_Guduri%C4%87"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Serbian volleyball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Tijana_Bo%C5%A1kovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tijana_Bo%C5%A1kovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian volleyball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tijana_Bo%C5%A1kovi%C4%87"}]}, {"year": "1999", "text": "<PERSON>, Australian cricketer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Montana_Jordan\" title=\"Montana Jordan\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montana_Jordan\" title=\"Montana Jordan\"><PERSON> Jordan</a>, American actor", "links": [{"title": "Montana Jordan", "link": "https://wikipedia.org/wiki/Montana_Jordan"}]}, {"year": "2004", "text": "<PERSON>, English actor", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "1126", "text": "<PERSON><PERSON><PERSON> of León and Castile (b. 1079)", "html": "1126 - <a href=\"https://wikipedia.org/wiki/Urraca_of_Le%C3%B3n_and_Castile\" title=\"Urraca of León and Castile\">Urraca of León and Castile</a> (b. 1079)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Urraca_of_Le%C3%B3n_and_Castile\" title=\"Urraca of León and Castile\">Urraca of León and Castile</a> (b. 1079)", "links": [{"title": "Urraca of León and Castile", "link": "https://wikipedia.org/wiki/Urraca_of_Le%C3%B3n_and_Castile"}]}, {"year": "1137", "text": "<PERSON><PERSON> of Normandy, by marriage countess of Blois (b. c. 1067)", "html": "1137 - <a href=\"https://wikipedia.org/wiki/Adela_of_Normandy\" title=\"<PERSON><PERSON> of Normandy\"><PERSON><PERSON> of Normandy</a>, by marriage <a href=\"https://wikipedia.org/wiki/Countess\" class=\"mw-redirect\" title=\"Countess\">countess</a> of <a href=\"https://wikipedia.org/wiki/Blois\" title=\"Blois\">B<PERSON><PERSON></a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1067</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Normandy\" title=\"<PERSON><PERSON> of Normandy\"><PERSON><PERSON> of Normandy</a>, by marriage <a href=\"https://wikipedia.org/wiki/Countess\" class=\"mw-redirect\" title=\"Countess\">countess</a> of <a href=\"https://wikipedia.org/wiki/Blois\" title=\"Blois\">B<PERSON><PERSON></a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1067</span>)", "links": [{"title": "<PERSON><PERSON> of Normandy", "link": "https://wikipedia.org/wiki/Adela_of_Normandy"}, {"title": "Countess", "link": "https://wikipedia.org/wiki/Countess"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>is"}]}, {"year": "1144", "text": "<PERSON> <PERSON><PERSON><PERSON> II", "html": "1144 - <a href=\"https://wikipedia.org/wiki/Pope_Ce<PERSON>tine_II\" title=\"Pope Celestine II\">Pope <PERSON><PERSON><PERSON> II</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_II\" title=\"Pope Celestine II\"><PERSON> <PERSON><PERSON><PERSON> II</a>", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1403", "text": "<PERSON><PERSON><PERSON>, Ottoman sultan (b. 1360)", "html": "1403 - <a href=\"https://wikipedia.org/wiki/Bayezid_I\" title=\"Bayezid I\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1360)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bayezid_I\" title=\"Bayezid I\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1360)", "links": [{"title": "Bayezid I", "link": "https://wikipedia.org/wiki/<PERSON>ez<PERSON>_I"}]}, {"year": "1466", "text": "<PERSON>, Duke of Milan (b. 1401)", "html": "1466 - <a href=\"https://wikipedia.org/wiki/<PERSON>_I_S<PERSON>rz<PERSON>\" title=\"Francesco I Sforza\"><PERSON></a>, Duke of Milan (b. 1401)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rz<PERSON>\" title=\"Francesco I Sforza\"><PERSON></a>, Duke of Milan (b. 1401)", "links": [{"title": "Francesco I Sforza", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1550", "text": "<PERSON> of God, Portuguese friar and saint (b. 1495)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of God\"><PERSON> of God</a>, Portuguese friar and saint (b. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of God\"><PERSON> of God</a>, Portuguese friar and saint (b. 1495)", "links": [{"title": "John of God", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1619", "text": "<PERSON><PERSON>, German baker and miller", "html": "1619 - <a href=\"https://wikipedia.org/wiki/Veit_Bach\" title=\"Veit Bach\"><PERSON><PERSON> <PERSON></a>, German baker and miller", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Veit_Bach\" title=\"Veit Bach\"><PERSON><PERSON> <PERSON></a>, German baker and miller", "links": [{"title": "Veit Bach", "link": "https://wikipedia.org/wiki/Veit_Bach"}]}, {"year": "1641", "text": "<PERSON>, Chinese geographer and explorer (b. 1587)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese geographer and explorer (b. 1587)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Xi<PERSON>\"><PERSON></a>, Chinese geographer and explorer (b. 1587)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1702", "text": "<PERSON> of England (b. 1650)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"William III of England\"><PERSON> of England</a> (b. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> (b. 1650)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1717", "text": "<PERSON>, English blacksmith (b. 1678)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English blacksmith (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English blacksmith (b. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1723", "text": "<PERSON><PERSON>, Norwegian civil servant (b. c.1673)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Po<PERSON> Juel\"><PERSON><PERSON></a>, Norwegian civil servant (b. c.1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Po<PERSON> Juel\"><PERSON><PERSON></a>, Norwegian civil servant (b. c.1673)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>vel_Juel"}]}, {"year": "1723", "text": "<PERSON>, English architect, designed St. Paul's Cathedral (b. 1632)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed St. Paul's Cathedral (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed St. Paul's Cathedral (b. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON> (b. 1763)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Charles_XIV_John_of_Sweden\" class=\"mw-redirect\" title=\"Charles XIV John of Sweden\"><PERSON> of <PERSON></a> (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_XIV_John_of_Sweden\" class=\"mw-redirect\" title=\"Charles XIV John of Sweden\"><PERSON> of <PERSON></a> (b. 1763)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1869", "text": "<PERSON>, French composer, conductor, and critic (b. 1803)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer, conductor, and critic (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer, conductor, and critic (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON>, British botanist (b. 1799)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British botanist (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British botanist (b. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Dutch-Canadian painter (b. 1815)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Canadian painter (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Canadian painter (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON>, American lawyer and politician, 13th President of the United States (b. 1800)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Millard_<PERSON>llmore\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1800)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Millard_Fillmore"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1887", "text": "<PERSON>, American minister and activist (b. 1813)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American engineer, designed the Eads Bridge (b. 1820)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, designed the <a href=\"https://wikipedia.org/wiki/E<PERSON>_Bridge\" title=\"Eads Bridge\">Eads Bridge</a> (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> E<PERSON>\"><PERSON></a>, American engineer, designed the <a href=\"https://wikipedia.org/wiki/E<PERSON>_Bridge\" title=\"Eads Bridge\">Eads Bridge</a> (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Eads Bridge", "link": "https://wikipedia.org/wiki/Eads_Bridge"}]}, {"year": "1889", "text": "<PERSON>, Swedish-American engineer (b. 1803)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American engineer (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American engineer (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, German general and businessman (b. 1838)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> von Zeppelin\"><PERSON> von <PERSON></a>, German general and businessman (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> von <PERSON>\"><PERSON> von <PERSON></a>, German general and businessman (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Dutch physicist and academic, Nobel Prize laureate (b. 1837)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1930", "text": "<PERSON>, American politician, 27th President of the United States (b. 1857)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 27th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 27th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1930", "text": "<PERSON>, American jurist and politician, United States Assistant Attorney General (b. 1865)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Attorney_General\" title=\"United States Assistant Attorney General\">United States Assistant Attorney General</a> (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Attorney_General\" title=\"United States Assistant Attorney General\">United States Assistant Attorney General</a> (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Assistant Attorney General", "link": "https://wikipedia.org/wiki/United_States_Assistant_Attorney_General"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Finnish socialite and spy (b. 1891)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish socialite and <a href=\"https://wikipedia.org/wiki/Spy\" class=\"mw-redirect\" title=\"Spy\">spy</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish socialite and <a href=\"https://wikipedia.org/wiki/Spy\" class=\"mw-redirect\" title=\"Spy\">spy</a> (b. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Spy", "link": "https://wikipedia.org/wiki/Spy"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Canadian ice hockey player (b. 1902)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American novelist and short story writer (b. 1876)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Cuban chess player (b. 1888)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ra%C3%BAl_Capablanca\" title=\"<PERSON>\"><PERSON></a>, Cuban chess player (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ra%C3%BAl_Capablanca\" title=\"<PERSON>\"><PERSON></a>, Cuban chess player (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Ra%C3%BAl_Capablanca"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Indonesian independence leader (b. 1886)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Cipto_Man<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian independence leader (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cipto_Man<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian independence leader (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cipto_Mangun<PERSON>umo"}]}, {"year": "1944", "text": "<PERSON><PERSON>, German Jewish athlete who helped thousands of Jewish children in the Holocaust (b. 1916)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German Jewish athlete who helped thousands of Jewish children in the Holocaust (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German Jewish athlete who helped thousands of Jewish children in the Holocaust (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Turkish dermatologist and scientist (b. 1889)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>lusi_Beh%C3%A7et\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish dermatologist and scientist (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lusi_Beh%C3%A7et\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish dermatologist and scientist (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hulusi_Beh%C3%A7et"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Swiss composer and conductor (b. 1886)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss composer and conductor (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss composer and conductor (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English conductor and composer (b. 1879)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and composer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and composer (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor, director, and producer (b. 1893)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American keyboard player and songwriter (b. 1945)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Pig<PERSON>%22_<PERSON><PERSON><PERSON><PERSON><PERSON>\" title='<PERSON>\" Mc<PERSON><PERSON><PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American keyboard player and songwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Pig<PERSON>%22_<PERSON><PERSON><PERSON><PERSON><PERSON>\" title='<PERSON> \"<PERSON>\" Mc<PERSON><PERSON><PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American keyboard player and songwriter (b. 1945)", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Pigpen%22_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American director, producer, and screenwriter (b. 1904)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Bengali politician (b. 1872)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jam<PERSON>\"><PERSON><PERSON></a>, Bengali politician (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jam<PERSON>\"><PERSON><PERSON></a>, Bengali politician (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Jamadar"}]}, {"year": "1983", "text": "<PERSON>, 1st Viscount <PERSON> of Merton, English lieutenant and politician (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>_of_Merton\" title=\"<PERSON>, 1st Viscount <PERSON> of Merton\"><PERSON>, 1st Viscount <PERSON> of Merton</a>, English lieutenant and politician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>_of_Merton\" title=\"<PERSON>, 1st Viscount <PERSON> of Merton\"><PERSON>, 1st Viscount <PERSON> of Merton</a>, English lieutenant and politician (b. 1904)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON> of Merton", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>_of_Merton"}]}, {"year": "1983", "text": "<PERSON>, English composer (b. 1902)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American jazz singer (b. 1914)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz singer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz singer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, British colonel (b. 1906)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British colonel (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British colonel (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American football player (b. 1936)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Argentinian journalist and author (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Casa<PERSON>\" title=\"<PERSON><PERSON> Bio<PERSON> Casa<PERSON>\"><PERSON><PERSON></a>, Argentinian journalist and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Casa<PERSON>\" title=\"<PERSON><PERSON> Bioy Casa<PERSON>\"><PERSON><PERSON></a>, Argentinian journalist and author (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolfo_Bioy_Casares"}]}, {"year": "1999", "text": "<PERSON>, American actress and comedian (b. 1924)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American baseball player and coach (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English singer (b. 1940)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Adam_Faith\" title=\"Adam Faith\"><PERSON></a>, English singer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adam_Faith\" title=\"Adam Faith\"><PERSON></a>, English singer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actress (b. 1909)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Syrian terrorist, founded the Palestine Liberation Front (b. 1948)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian terrorist, founded the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Front\" class=\"mw-redirect\" title=\"Palestine Liberation Front\">Palestine Liberation Front</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian terrorist, founded the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Front\" class=\"mw-redirect\" title=\"Palestine Liberation Front\">Palestine Liberation Front</a> (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Palestine Liberation Front", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Front"}]}, {"year": "2005", "text": "<PERSON>, Brazilian physicist and academic (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian physicist and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian physicist and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Lattes"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Chechen commander and politician, President of the Chechen Republic of Ichkeria (b. 1951)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chechen commander and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Chechen_Republic_of_Ichkeria\" class=\"mw-redirect\" title=\"President of the Chechen Republic of Ichkeria\">President of the Chechen Republic of Ichkeria</a> (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chechen commander and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Chechen_Republic_of_Ichkeria\" class=\"mw-redirect\" title=\"President of the Chechen Republic of Ichkeria\">President of the Chechen Republic of Ichkeria</a> (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of the Chechen Republic of Ichkeria", "link": "https://wikipedia.org/wiki/President_of_the_Chechen_Republic_of_Ichkeria"}]}, {"year": "2007", "text": "<PERSON>, English actor (b. 1935)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American baseball player and coach (b. 1947)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1918)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Iranian author and academic (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian author and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian author and academic (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Irish politician, Irish Minister of Health (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell_(Dublin_politician)\" title=\"<PERSON> (Dublin politician)\"><PERSON></a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Health_(Ireland)\" title=\"Minister for Health (Ireland)\">Irish Minister of Health</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell_(Dublin_politician)\" title=\"<PERSON> (Dublin politician)\"><PERSON></a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Health_(Ireland)\" title=\"Minister for Health (Ireland)\">Irish Minister of Health</a> (b. 1927)", "links": [{"title": "<PERSON> (Dublin politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell_(Dublin_politician)"}, {"title": "Minister for Health (Ireland)", "link": "https://wikipedia.org/wiki/Minister_for_Health_(Ireland)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, German soldier and publisher (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>-Schmenzin\" title=\"<PERSON><PERSON><PERSON><PERSON>-Schmenzin\"><PERSON><PERSON><PERSON><PERSON></a>, German soldier and publisher (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>-<PERSON>hmenzin\" title=\"<PERSON><PERSON><PERSON><PERSON>-Schmenzin\"><PERSON><PERSON><PERSON><PERSON></a>, German soldier and publisher (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Austrian-American Holocaust survivor and author (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">Holocaust</a> survivor and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">Holocaust</a> survivor and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_B<PERSON>z"}, {"title": "Holocaust", "link": "https://wikipedia.org/wiki/Holocaust"}]}, {"year": "2014", "text": "<PERSON>, American sergeant (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American director, producer, and screenwriter (b. 1955)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English composer, conductor, and producer (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, conductor, and producer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, conductor, and producer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American author (b. 1928)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American actor (b. 1934)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American football player and actor (b. 1948)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and actor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and actor (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Swedish actor (b. 1929)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}]}}