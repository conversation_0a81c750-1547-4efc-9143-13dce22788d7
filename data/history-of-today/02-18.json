{"date": "February 18", "url": "https://wikipedia.org/wiki/February_18", "data": {"Events": [{"year": "3102 BC", "text": "<PERSON>, the fourth and final yuga of Hinduism, starts with the death of <PERSON>.", "html": "3102 BC - 3102 BC - <a href=\"https://wikipedia.org/wiki/Kali_Yuga\" title=\"Kali Yuga\">Kali Yuga</a>, the fourth and final <a href=\"https://wikipedia.org/wiki/Yuga\" title=\"Yuga\">yuga</a> of <a href=\"https://wikipedia.org/wiki/Hinduism\" title=\"Hinduism\">Hinduism</a>, starts with the death of <a href=\"https://wikipedia.org/wiki/Krishna\" title=\"<PERSON>\">Krishna</a>.", "no_year_html": "3102 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ga\" title=\"Kali Yuga\"><PERSON> Yu<PERSON></a>, the fourth and final <a href=\"https://wikipedia.org/wiki/Yuga\" title=\"Yuga\">yuga</a> of <a href=\"https://wikipedia.org/wiki/Hinduism\" title=\"Hinduism\">Hinduism</a>, starts with the death of <a href=\"https://wikipedia.org/wiki/Krishna\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Kali Yu<PERSON>", "link": "https://wikipedia.org/wiki/Kali_<PERSON>ga"}, {"title": "Yuga", "link": "https://wikipedia.org/wiki/Yuga"}, {"title": "Hinduism", "link": "https://wikipedia.org/wiki/Hinduism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Krishna"}]}, {"year": "1229", "text": "The Sixth Crusade: <PERSON>, Holy Roman Emperor, signs a ten-year truce with al-Kamil, regaining Jerusalem, Nazareth, and Bethlehem with neither military engagements nor support from the papacy.", "html": "1229 - The <a href=\"https://wikipedia.org/wiki/Sixth_Crusade\" title=\"Sixth Crusade\">Sixth Crusade</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II, Holy Roman Emperor</a>, signs a ten-year <a href=\"https://wikipedia.org/wiki/Truce\" class=\"mw-redirect\" title=\"Truce\">truce</a> with <a href=\"https://wikipedia.org/wiki/Al-Kamil\" title=\"Al-Kamil\">al-Kamil</a>, regaining <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>, <a href=\"https://wikipedia.org/wiki/Nazareth\" title=\"Nazareth\">Nazareth</a>, and <a href=\"https://wikipedia.org/wiki/Bethlehem\" title=\"Bethlehem\">Bethlehem</a> with neither military engagements nor support from the <a href=\"https://wikipedia.org/wiki/Papacy\" class=\"mw-redirect\" title=\"Papa<PERSON>\">papacy</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sixth_Crusade\" title=\"Sixth Crusade\">Sixth Crusade</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II, Holy Roman Emperor</a>, signs a ten-year <a href=\"https://wikipedia.org/wiki/Truce\" class=\"mw-redirect\" title=\"Truce\">truce</a> with <a href=\"https://wikipedia.org/wiki/Al-Kamil\" title=\"Al-Kamil\">al-Ka<PERSON>l</a>, regaining <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>, <a href=\"https://wikipedia.org/wiki/Nazareth\" title=\"Nazareth\">Nazareth</a>, and <a href=\"https://wikipedia.org/wiki/Bethlehem\" title=\"Bethlehem\">Bethlehem</a> with neither military engagements nor support from the <a href=\"https://wikipedia.org/wiki/Papacy\" class=\"mw-redirect\" title=\"Papa<PERSON>\">papacy</a>.", "links": [{"title": "Sixth Crusade", "link": "https://wikipedia.org/wiki/Sixth_Crusade"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Truce", "link": "https://wikipedia.org/wiki/Truce"}, {"title": "Al-Ka<PERSON>l", "link": "https://wikipedia.org/wiki/Al-Kamil"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}, {"title": "Nazareth", "link": "https://wikipedia.org/wiki/Nazareth"}, {"title": "Bethlehem", "link": "https://wikipedia.org/wiki/Bethlehem"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Papacy"}]}, {"year": "1268", "text": "The Battle of Wesenberg is fought between the Livonian Order and Dovmont of Pskov.", "html": "1268 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Wesenberg_(1268)\" title=\"Battle of Wesenberg (1268)\">Battle of Wesenberg</a> is fought between the <a href=\"https://wikipedia.org/wiki/Livonian_Order\" title=\"Livonian Order\">Livonian Order</a> and <a href=\"https://wikipedia.org/wiki/Daumantas_of_Pskov\" title=\"Daumantas of Pskov\">Dovmont</a> of <a href=\"https://wikipedia.org/wiki/Pskov\" title=\"Pskov\">Pskov</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Wesenberg_(1268)\" title=\"Battle of Wesenberg (1268)\">Battle of Wesenberg</a> is fought between the <a href=\"https://wikipedia.org/wiki/Livonian_Order\" title=\"Livonian Order\">Livonian Order</a> and <a href=\"https://wikipedia.org/wiki/Daumantas_of_Pskov\" title=\"Daumantas of Pskov\">Dovmont</a> of <a href=\"https://wikipedia.org/wiki/Pskov\" title=\"Pskov\">Pskov</a>.", "links": [{"title": "Battle of Wesenberg (1268)", "link": "https://wikipedia.org/wiki/Battle_of_Wesenberg_(1268)"}, {"title": "Livonian Order", "link": "https://wikipedia.org/wiki/Livonian_Order"}, {"title": "Daumantas of Pskov", "link": "https://wikipedia.org/wiki/Daumantas_of_Pskov"}, {"title": "Pskov", "link": "https://wikipedia.org/wiki/Pskov"}]}, {"year": "1332", "text": "<PERSON><PERSON>, Emperor of Ethiopia begins his campaigns in the southern Muslim provinces.", "html": "1332 - <a href=\"https://wikipedia.org/wiki/Amda_Seyon_I\" title=\"Amda Seyon I\">Amda Seyon I</a>, <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a> begins his campaigns in the southern <a href=\"https://wikipedia.org/wiki/Muslim\" class=\"mw-redirect\" title=\"Muslim\">Muslim</a> provinces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amda_Seyon_I\" title=\"Amda Seyon I\">Amda Seyon I</a>, <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a> begins his campaigns in the southern <a href=\"https://wikipedia.org/wiki/Muslim\" class=\"mw-redirect\" title=\"Muslim\">Muslim</a> provinces.", "links": [{"title": "Amda Seyon I", "link": "https://wikipedia.org/wiki/Amda_Seyon_I"}, {"title": "Emperor of Ethiopia", "link": "https://wikipedia.org/wiki/Emperor_of_Ethiopia"}, {"title": "Muslim", "link": "https://wikipedia.org/wiki/Muslim"}]}, {"year": "1478", "text": "<PERSON>, Duke of Clarence, convicted of treason against his older brother <PERSON> of England, is executed in private at the Tower of London.", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Plant<PERSON>,_1st_Duke_<PERSON>_Clarence\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Clarence\"><PERSON>, Duke of Clarence</a>, convicted of <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a> against his older brother <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"Edward IV of England\"><PERSON> of England</a>, is <a href=\"https://wikipedia.org/wiki/Execution_(legal)\" class=\"mw-redirect\" title=\"Execution (legal)\">executed</a> in private at the <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Plant<PERSON>,_1st_Duke_<PERSON>_Clarence\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Clarence\"><PERSON>, Duke of Clarence</a>, convicted of <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a> against his older brother <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"Edward IV of England\">Edward IV of England</a>, is <a href=\"https://wikipedia.org/wiki/Execution_(legal)\" class=\"mw-redirect\" title=\"Execution (legal)\">executed</a> in private at the <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a>.", "links": [{"title": "<PERSON>, 1st Duke of Clarence", "link": "https://wikipedia.org/wiki/<PERSON>_Plantagenet,_1st_Duke_<PERSON>_Clarence"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treason"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Execution (legal)", "link": "https://wikipedia.org/wiki/Execution_(legal)"}, {"title": "Tower of London", "link": "https://wikipedia.org/wiki/Tower_of_London"}]}, {"year": "1637", "text": "Eighty Years' War: Off the coast of Cornwall, England, a Spanish fleet intercepts an important Anglo-Dutch merchant convoy of 44 vessels escorted by six warships, destroying or capturing 20 of them.", "html": "1637 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: Off the coast of <a href=\"https://wikipedia.org/wiki/Cornwall\" title=\"Cornwall\">Cornwall</a>, England, a <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish</a> fleet <a href=\"https://wikipedia.org/wiki/Battle_off_Lizard_Point\" title=\"Battle off Lizard Point\">intercepts</a> an important <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">Anglo</a>-<a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch</a> merchant convoy of 44 vessels escorted by six warships, destroying or capturing 20 of them.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: Off the coast of <a href=\"https://wikipedia.org/wiki/Cornwall\" title=\"Cornwall\">Cornwall</a>, England, a <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish</a> fleet <a href=\"https://wikipedia.org/wiki/Battle_off_Lizard_Point\" title=\"Battle off Lizard Point\">intercepts</a> an important <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">Anglo</a>-<a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch</a> merchant convoy of 44 vessels escorted by six warships, destroying or capturing 20 of them.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Cornwall", "link": "https://wikipedia.org/wiki/Cornwall"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}, {"title": "Battle off Lizard Point", "link": "https://wikipedia.org/wiki/Battle_off_Lizard_Point"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Dutch Republic", "link": "https://wikipedia.org/wiki/Dutch_Republic"}]}, {"year": "1735", "text": "The ballad opera called <PERSON>, or <PERSON><PERSON> in the Well went down in history as the first opera of any kind to be produced in North America (Charleston, S.C.)", "html": "1735 - The ballad opera called <i><PERSON>, or <PERSON><PERSON> in the Well</i> went down in history as the first opera of any kind to be produced in North America (Charleston, S.C.)", "no_year_html": "The ballad opera called <i><PERSON>, or <PERSON><PERSON> in the Well</i> went down in history as the first opera of any kind to be produced in North America (Charleston, S.C.)", "links": []}, {"year": "1781", "text": "Fourth Anglo-Dutch War: Captain <PERSON> opens his expedition against Dutch colonial outposts on the Gold Coast of Africa (present-day Ghana).", "html": "1781 - <a href=\"https://wikipedia.org/wiki/Fourth_Anglo-Dutch_War\" title=\"Fourth Anglo-Dutch War\">Fourth Anglo-Dutch War</a>: Captain <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Gold_Coast_expedition\" title=\"<PERSON>'s Gold Coast expedition\">opens his expedition</a> against <a href=\"https://wikipedia.org/wiki/Dutch_Gold_Coast\" title=\"Dutch Gold Coast\">Dutch colonial outposts</a> on the <a href=\"https://wikipedia.org/wiki/Gold_Coast_(British_colony)\" title=\"Gold Coast (British colony)\">Gold Coast</a> of Africa (present-day <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fourth_Anglo-Dutch_War\" title=\"Fourth Anglo-Dutch War\">Fourth Anglo-Dutch War</a>: Captain <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Gold_Coast_expedition\" title=\"<PERSON>'s Gold Coast expedition\">opens his expedition</a> against <a href=\"https://wikipedia.org/wiki/Dutch_Gold_Coast\" title=\"Dutch Gold Coast\">Dutch colonial outposts</a> on the <a href=\"https://wikipedia.org/wiki/Gold_Coast_(British_colony)\" title=\"Gold Coast (British colony)\">Gold Coast</a> of Africa (present-day <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a>).", "links": [{"title": "Fourth Anglo-Dutch War", "link": "https://wikipedia.org/wiki/Fourth_Anglo-Dutch_War"}, {"title": "<PERSON>'s Gold Coast expedition", "link": "https://wikipedia.org/wiki/Shirley%27s_Gold_Coast_expedition"}, {"title": "Dutch Gold Coast", "link": "https://wikipedia.org/wiki/Dutch_Gold_Coast"}, {"title": "Gold Coast (British colony)", "link": "https://wikipedia.org/wiki/Gold_Coast_(British_colony)"}, {"title": "Ghana", "link": "https://wikipedia.org/wiki/Ghana"}]}, {"year": "1791", "text": "Congress passes a law admitting the state of Vermont to the Union, effective 4 March, after that state had existed for 14 years as a de facto independent largely unrecognized state.", "html": "1791 - Congress passes a law <a href=\"https://wikipedia.org/wiki/Admission_to_the_Union\" title=\"Admission to the Union\">admitting</a> the state of <a href=\"https://wikipedia.org/wiki/Vermont\" title=\"Vermont\">Vermont</a> to the Union, effective 4 March, after that state had existed for 14 years as <a href=\"https://wikipedia.org/wiki/Vermont_Republic\" title=\"Vermont Republic\">a de facto independent largely unrecognized state</a>.", "no_year_html": "Congress passes a law <a href=\"https://wikipedia.org/wiki/Admission_to_the_Union\" title=\"Admission to the Union\">admitting</a> the state of <a href=\"https://wikipedia.org/wiki/Vermont\" title=\"Vermont\">Vermont</a> to the Union, effective 4 March, after that state had existed for 14 years as <a href=\"https://wikipedia.org/wiki/Vermont_Republic\" title=\"Vermont Republic\">a de facto independent largely unrecognized state</a>.", "links": [{"title": "Admission to the Union", "link": "https://wikipedia.org/wiki/Admission_to_the_Union"}, {"title": "Vermont", "link": "https://wikipedia.org/wiki/Vermont"}, {"title": "Vermont Republic", "link": "https://wikipedia.org/wiki/Vermont_Republic"}]}, {"year": "1797", "text": "French Revolutionary Wars: Sir <PERSON> and a fleet of 18 British warships invade Trinidad.", "html": "1797 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and a fleet of 18 <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">British</a> warships <a href=\"https://wikipedia.org/wiki/Invasion_of_Trinidad_(1797)\" title=\"Invasion of Trinidad (1797)\">invade</a> <a href=\"https://wikipedia.org/wiki/Trinidad\" title=\"Trinidad\">Trinidad</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and a fleet of 18 <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">British</a> warships <a href=\"https://wikipedia.org/wiki/Invasion_of_Trinidad_(1797)\" title=\"Invasion of Trinidad (1797)\">invade</a> <a href=\"https://wikipedia.org/wiki/Trinidad\" title=\"Trinidad\">Trinidad</a>.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Invasion of Trinidad (1797)", "link": "https://wikipedia.org/wiki/Invasion_of_Trinidad_(1797)"}, {"title": "Trinidad", "link": "https://wikipedia.org/wiki/Trinidad"}]}, {"year": "1814", "text": "Napoleonic Wars: The Battle of Montereau.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Montereau\" title=\"Battle of Montereau\">Battle of Montereau</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Montereau\" title=\"Battle of Montereau\">Battle of Montereau</a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Battle of Montereau", "link": "https://wikipedia.org/wiki/Battle_of_Montereau"}]}, {"year": "1861", "text": "In Montgomery, Alabama, <PERSON> is inaugurated as the provisional President of the Confederate States of America.", "html": "1861 - In <a href=\"https://wikipedia.org/wiki/Montgomery,_Alabama\" title=\"Montgomery, Alabama\">Montgomery, Alabama</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is inaugurated as the provisional President of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate States of America</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Montgomery,_Alabama\" title=\"Montgomery, Alabama\">Montgomery, Alabama</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is inaugurated as the provisional President of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate States of America</a>.", "links": [{"title": "Montgomery, Alabama", "link": "https://wikipedia.org/wiki/Montgomery,_Alabama"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1861", "text": "With Italian unification almost complete, <PERSON> of Piedmont, Savoy and Sardinia assumes the title of King of Italy.", "html": "1861 - With <a href=\"https://wikipedia.org/wiki/Italian_unification\" class=\"mw-redirect\" title=\"Italian unification\">Italian unification</a> almost complete, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Piedmont\" title=\"Piedmont\">Piedmont</a>, <a href=\"https://wikipedia.org/wiki/Savoy\" title=\"Savoy\">Savoy</a> and <a href=\"https://wikipedia.org/wiki/Sardinia\" title=\"Sardinia\">Sardinia</a> assumes the title of <a href=\"https://wikipedia.org/wiki/King_of_Italy\" title=\"King of Italy\">King of Italy</a>.", "no_year_html": "With <a href=\"https://wikipedia.org/wiki/Italian_unification\" class=\"mw-redirect\" title=\"Italian unification\">Italian unification</a> almost complete, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Piedmont\" title=\"Piedmont\">Piedmont</a>, <a href=\"https://wikipedia.org/wiki/Savoy\" title=\"Savoy\">Savoy</a> and <a href=\"https://wikipedia.org/wiki/Sardinia\" title=\"Sardinia\">Sardinia</a> assumes the title of <a href=\"https://wikipedia.org/wiki/King_of_Italy\" title=\"King of Italy\">King of Italy</a>.", "links": [{"title": "Italian unification", "link": "https://wikipedia.org/wiki/Italian_unification"}, {"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy"}, {"title": "Piedmont", "link": "https://wikipedia.org/wiki/Piedmont"}, {"title": "Savoy", "link": "https://wikipedia.org/wiki/Savoy"}, {"title": "Sardinia", "link": "https://wikipedia.org/wiki/Sardinia"}, {"title": "King of Italy", "link": "https://wikipedia.org/wiki/King_of_Italy"}]}, {"year": "1873", "text": "Bulgarian revolutionary leader <PERSON><PERSON><PERSON> is executed by hanging in Sofia by the Ottoman authorities.", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgarian</a> revolutionary leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is executed by <a href=\"https://wikipedia.org/wiki/Hanging\" title=\"Hanging\">hanging</a> in <a href=\"https://wikipedia.org/wiki/Sofia\" title=\"Sofia\">Sofia</a> by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> authorities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgarian</a> revolutionary leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is executed by <a href=\"https://wikipedia.org/wiki/Hanging\" title=\"Hanging\">hanging</a> in <a href=\"https://wikipedia.org/wiki/Sofia\" title=\"Sofia\">Sofia</a> by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> authorities.", "links": [{"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Hanging", "link": "https://wikipedia.org/wiki/Hanging"}, {"title": "Sofia", "link": "https://wikipedia.org/wiki/Sofia"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1878", "text": "<PERSON> is murdered by outlaw <PERSON>, sparking the Lincoln County War in Lincoln County, New Mexico.", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is murdered by <a href=\"https://wikipedia.org/wiki/Outlaw\" title=\"Outlaw\">outlaw</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sparking the <a href=\"https://wikipedia.org/wiki/Lincoln_County_War\" title=\"Lincoln County War\">Lincoln County War</a> in <a href=\"https://wikipedia.org/wiki/Lincoln_County,_New_Mexico\" title=\"Lincoln County, New Mexico\">Lincoln County, New Mexico</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is murdered by <a href=\"https://wikipedia.org/wiki/Outlaw\" title=\"Outlaw\">outlaw</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sparking the <a href=\"https://wikipedia.org/wiki/Lincoln_County_War\" title=\"Lincoln County War\">Lincoln County War</a> in <a href=\"https://wikipedia.org/wiki/Lincoln_County,_New_Mexico\" title=\"Lincoln County, New Mexico\">Lincoln County, New Mexico</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Outlaw", "link": "https://wikipedia.org/wiki/Outlaw"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lincoln County War", "link": "https://wikipedia.org/wiki/Lincoln_County_War"}, {"title": "Lincoln County, New Mexico", "link": "https://wikipedia.org/wiki/Lincoln_County,_New_Mexico"}]}, {"year": "1885", "text": "Adventures of Huckleberry Finn by <PERSON> is published in the United States.", "html": "1885 - <i><a href=\"https://wikipedia.org/wiki/Adventures_of_Huckleberry_Finn\" title=\"Adventures of Huckleberry Finn\">Adventures of Huckleberry Finn</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is published in the United States.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Adventures_of_Huckleberry_Finn\" title=\"Adventures of Huckleberry Finn\">Adventures of Huckleberry Finn</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is published in the United States.", "links": [{"title": "Adventures of <PERSON><PERSON><PERSON> Finn", "link": "https://wikipedia.org/wiki/Adventures_of_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "Second Boer War: Imperial forces suffer their worst single-day loss of life on Bloody Sunday, the first day of the Battle of Paardeberg.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: Imperial forces suffer their worst single-day loss of life on <a href=\"https://wikipedia.org/wiki/Bloody_Sunday_(1900)\" title=\"Bloody Sunday (1900)\">Bloody Sunday</a>, the first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Paardeberg\" title=\"Battle of Paardeberg\">Battle of Paardeberg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: Imperial forces suffer their worst single-day loss of life on <a href=\"https://wikipedia.org/wiki/Bloody_Sunday_(1900)\" title=\"Bloody Sunday (1900)\">Bloody Sunday</a>, the first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Paardeberg\" title=\"Battle of Paardeberg\">Battle of Paardeberg</a>.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Bloody Sunday (1900)", "link": "https://wikipedia.org/wiki/Bloody_Sunday_(1900)"}, {"title": "Battle of Paardeberg", "link": "https://wikipedia.org/wiki/Battle_of_Paardeberg"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON> forms the Belgian Olympic Committee in Brussels.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> forms the <a href=\"https://wikipedia.org/wiki/Belgian_Olympic_Committee\" class=\"mw-redirect\" title=\"Belgian Olympic Committee\">Belgian Olympic Committee</a> in <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> forms the <a href=\"https://wikipedia.org/wiki/Belgian_Olympic_Committee\" class=\"mw-redirect\" title=\"Belgian Olympic Committee\">Belgian Olympic Committee</a> in <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Belgian Olympic Committee", "link": "https://wikipedia.org/wiki/Belgian_Olympic_Committee"}, {"title": "Brussels", "link": "https://wikipedia.org/wiki/Brussels"}]}, {"year": "1911", "text": "The first official flight with airmail takes place from Allahabad, United Provinces, British India (now India), when <PERSON>, a 23-year-old pilot, delivers 6,500 letters to Naini, about 10 kilometres (6.2 mi) away.", "html": "1911 - The first official flight with <a href=\"https://wikipedia.org/wiki/Airmail\" title=\"Airmail\">airmail</a> takes place from <a href=\"https://wikipedia.org/wiki/Allahabad\" class=\"mw-redirect\" title=\"Allahabad\">Allahabad</a>, <a href=\"https://wikipedia.org/wiki/United_Provinces_of_Agra_and_Oudh\" title=\"United Provinces of Agra and Oudh\">United Provinces</a>, <a href=\"https://wikipedia.org/wiki/British_India\" class=\"mw-redirect\" title=\"British India\">British India</a> (now India), when <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a 23-year-old pilot, delivers 6,500 letters to <a href=\"https://wikipedia.org/wiki/Naini\" title=\"Naini\">Naini</a>, about 10 kilometres (6.2 mi) away.", "no_year_html": "The first official flight with <a href=\"https://wikipedia.org/wiki/Airmail\" title=\"Airmail\">airmail</a> takes place from <a href=\"https://wikipedia.org/wiki/Allahabad\" class=\"mw-redirect\" title=\"Allahabad\">Allahabad</a>, <a href=\"https://wikipedia.org/wiki/United_Provinces_of_Agra_and_Oudh\" title=\"United Provinces of Agra and Oudh\">United Provinces</a>, <a href=\"https://wikipedia.org/wiki/British_India\" class=\"mw-redirect\" title=\"British India\">British India</a> (now India), when <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a 23-year-old pilot, delivers 6,500 letters to <a href=\"https://wikipedia.org/wiki/Naini\" title=\"Naini\">Naini</a>, about 10 kilometres (6.2 mi) away.", "links": [{"title": "Airmail", "link": "https://wikipedia.org/wiki/Airmail"}, {"title": "Allahabad", "link": "https://wikipedia.org/wiki/Allahabad"}, {"title": "United Provinces of Agra and Oudh", "link": "https://wikipedia.org/wiki/United_Provinces_of_Agra_and_Oudh"}, {"title": "British India", "link": "https://wikipedia.org/wiki/British_India"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naini"}]}, {"year": "1915", "text": "U-boat Campaign: The Imperial German Navy institutes unrestricted submarine warfare in the waters around Great Britain and Ireland.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/U-boat_Campaign\" class=\"mw-redirect\" title=\"U-boat Campaign\">U-boat Campaign</a>: The Imperial German Navy institutes unrestricted submarine warfare in the waters around Great Britain and Ireland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U-boat_Campaign\" class=\"mw-redirect\" title=\"U-boat Campaign\">U-boat Campaign</a>: The Imperial German Navy institutes unrestricted submarine warfare in the waters around Great Britain and Ireland.", "links": [{"title": "U-boat Campaign", "link": "https://wikipedia.org/wiki/U-boat_Campaign"}]}, {"year": "1930", "text": "While studying photographs taken in January, <PERSON> discovers Pluto.", "html": "1930 - While studying photographs taken in <a href=\"https://wikipedia.org/wiki/January\" title=\"January\">January</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a>.", "no_year_html": "While studying photographs taken in <a href=\"https://wikipedia.org/wiki/January\" title=\"January\">January</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a>.", "links": [{"title": "January", "link": "https://wikipedia.org/wiki/January"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}]}, {"year": "1930", "text": "Elm Farm Ollie becomes the first cow to fly in a fixed-wing aircraft and also the first cow to be milked in an aircraft.", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Elm_Farm_Ollie\" title=\"Elm Farm Ollie\">Elm Farm Ollie</a> becomes the first <a href=\"https://wikipedia.org/wiki/Cattle\" title=\"Cattle\">cow</a> to fly in a <a href=\"https://wikipedia.org/wiki/Fixed-wing_aircraft\" title=\"Fixed-wing aircraft\">fixed-wing aircraft</a> and also the first cow to be milked in an aircraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elm_Farm_Ollie\" title=\"Elm Farm Ollie\">Elm Farm Ollie</a> becomes the first <a href=\"https://wikipedia.org/wiki/Cattle\" title=\"Cattle\">cow</a> to fly in a <a href=\"https://wikipedia.org/wiki/Fixed-wing_aircraft\" title=\"Fixed-wing aircraft\">fixed-wing aircraft</a> and also the first cow to be milked in an aircraft.", "links": [{"title": "Elm Farm Ollie", "link": "https://wikipedia.org/wiki/Elm_Farm_Ollie"}, {"title": "Cattle", "link": "https://wikipedia.org/wiki/Cattle"}, {"title": "Fixed-wing aircraft", "link": "https://wikipedia.org/wiki/Fixed-wing_aircraft"}]}, {"year": "1932", "text": "The Empire of Japan creates the independent state of Manzhouguo (the obsolete Chinese name for Manchuria) free from the Republic of China and installed former Chinese Emperor <PERSON><PERSON> as Chief Executive of the State.", "html": "1932 - The <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a> creates the independent state of <a href=\"https://wikipedia.org/wiki/Manzhouguo\" class=\"mw-redirect\" title=\"Manzhouguo\">Manzhouguo</a> (the obsolete Chinese name for <a href=\"https://wikipedia.org/wiki/Manchuria\" title=\"Manchuria\">Manchuria</a>) free from the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Republic of China</a> and installed former <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Chinese Emperor</a> <a href=\"https://wikipedia.org/wiki/Puyi\" title=\"Puyi\"><PERSON><PERSON></a> as Chief Executive of the State.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a> creates the independent state of <a href=\"https://wikipedia.org/wiki/Manzhouguo\" class=\"mw-redirect\" title=\"Manzhouguo\">Manzhouguo</a> (the obsolete Chinese name for <a href=\"https://wikipedia.org/wiki/Manchuria\" title=\"Manchuria\">Manchuria</a>) free from the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Republic of China</a> and installed former <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Chinese Emperor</a> <a href=\"https://wikipedia.org/wiki/Puyi\" title=\"Puyi\"><PERSON><PERSON></a> as Chief Executive of the State.", "links": [{"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Manzhouguo", "link": "https://wikipedia.org/wiki/Manzhouguo"}, {"title": "Manchuria", "link": "https://wikipedia.org/wiki/Manchuria"}, {"title": "Republic of China (1912-49)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)"}, {"title": "Emperor of China", "link": "https://wikipedia.org/wiki/Emperor_of_China"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Puyi"}]}, {"year": "1938", "text": "Second Sino-Japanese War: During the Nanking Massacre, the Nanking Safety Zone International Committee is renamed \"Nanking International Rescue Committee\", and the safety zone in place for refugees falls apart.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: During the <a href=\"https://wikipedia.org/wiki/Nanking_Massacre\" class=\"mw-redirect\" title=\"Nanking Massacre\">Nanking Massacre</a>, the <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanking</a> Safety Zone International Committee is renamed \"Nanking International Rescue Committee\", and the safety zone in place for refugees falls apart.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: During the <a href=\"https://wikipedia.org/wiki/Nanking_Massacre\" class=\"mw-redirect\" title=\"Nanking Massacre\">Nanking Massacre</a>, the <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanking</a> Safety Zone International Committee is renamed \"Nanking International Rescue Committee\", and the safety zone in place for refugees falls apart.", "links": [{"title": "Second Sino-Japanese War", "link": "https://wikipedia.org/wiki/Second_Sino-Japanese_War"}, {"title": "Nanking Massacre", "link": "https://wikipedia.org/wiki/Nanking_Massacre"}, {"title": "Nanjing", "link": "https://wikipedia.org/wiki/Nanjing"}]}, {"year": "1942", "text": "World War II: The Imperial Japanese Army begins the systematic extermination of perceived hostile elements among the Chinese in Singapore.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> begins the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sook Ching\">systematic extermination</a> of perceived hostile elements among the <a href=\"https://wikipedia.org/wiki/Chinese_in_Singapore\" class=\"mw-redirect\" title=\"Chinese in Singapore\">Chinese in Singapore</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> begins the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sook Ching\">systematic extermination</a> of perceived hostile elements among the <a href=\"https://wikipedia.org/wiki/Chinese_in_Singapore\" class=\"mw-redirect\" title=\"Chinese in Singapore\">Chinese in Singapore</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chinese in Singapore", "link": "https://wikipedia.org/wiki/Chinese_in_Singapore"}]}, {"year": "1943", "text": "World War II: The Nazis arrest the members of the White Rose movement.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a> arrest the members of the <a href=\"https://wikipedia.org/wiki/<PERSON>_Rose\" title=\"White Rose\">White Rose</a> movement.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a> arrest the members of the <a href=\"https://wikipedia.org/wiki/White_Rose\" title=\"White Rose\">White Rose</a> movement.", "links": [{"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}, {"title": "White Rose", "link": "https://wikipedia.org/wiki/<PERSON>_Rose"}]}, {"year": "1943", "text": "World War II: <PERSON> delivers his Sportpalast speech.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his <a href=\"https://wikipedia.org/wiki/Sportpalast_speech\" title=\"Sportpalast speech\"><i>Sportpalast</i> speech</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his <a href=\"https://wikipedia.org/wiki/Sportpalast_speech\" title=\"Sportpalast speech\"><i>Sportpalast</i> speech</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Sportpalast speech", "link": "https://wikipedia.org/wiki/Sportpalast_speech"}]}, {"year": "1945", "text": "World War II: American and Brazilian troops kick off Operation Encore in Northern Italy, a successful limited action in the Northern Apennines that prepares for the western portion of the Allied Spring offensive.", "html": "1945 - World War II: American and Brazilian troops kick off <a href=\"https://wikipedia.org/wiki/Operation_Encore\" title=\"Operation Encore\">Operation Encore</a> in Northern Italy, a successful limited action in the <a href=\"https://wikipedia.org/wiki/Northern_Apennines\" class=\"mw-redirect\" title=\"Northern Apennines\">Northern Apennines</a> that prepares for the western portion of the <a href=\"https://wikipedia.org/wiki/Spring_1945_offensive_in_Italy\" title=\"Spring 1945 offensive in Italy\">Allied Spring offensive</a>.", "no_year_html": "World War II: American and Brazilian troops kick off <a href=\"https://wikipedia.org/wiki/Operation_Encore\" title=\"Operation Encore\">Operation Encore</a> in Northern Italy, a successful limited action in the <a href=\"https://wikipedia.org/wiki/Northern_Apennines\" class=\"mw-redirect\" title=\"Northern Apennines\">Northern Apennines</a> that prepares for the western portion of the <a href=\"https://wikipedia.org/wiki/Spring_1945_offensive_in_Italy\" title=\"Spring 1945 offensive in Italy\">Allied Spring offensive</a>.", "links": [{"title": "Operation Encore", "link": "https://wikipedia.org/wiki/Operation_Encore"}, {"title": "Northern Apennines", "link": "https://wikipedia.org/wiki/Northern_Apennines"}, {"title": "Spring 1945 offensive in Italy", "link": "https://wikipedia.org/wiki/Spring_1945_offensive_in_Italy"}]}, {"year": "1946", "text": "Sailors of the Royal Indian Navy mutiny in Bombay harbour, from where the action spreads throughout the Provinces of British India, involving 78 ships, twenty shore establishments and 20,000 sailors", "html": "1946 - Sailors of the <a href=\"https://wikipedia.org/wiki/Royal_Indian_Navy_mutiny\" title=\"Royal Indian Navy mutiny\">Royal Indian Navy mutiny</a> in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Bombay</a> harbour, from where the action spreads throughout the <a href=\"https://wikipedia.org/wiki/Presidencies_and_provinces_of_British_India\" title=\"Presidencies and provinces of British India\">Provinces of British India</a>, involving 78 ships, twenty shore establishments and 20,000 sailors", "no_year_html": "Sailors of the <a href=\"https://wikipedia.org/wiki/Royal_Indian_Navy_mutiny\" title=\"Royal Indian Navy mutiny\">Royal Indian Navy mutiny</a> in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Bombay</a> harbour, from where the action spreads throughout the <a href=\"https://wikipedia.org/wiki/Presidencies_and_provinces_of_British_India\" title=\"Presidencies and provinces of British India\">Provinces of British India</a>, involving 78 ships, twenty shore establishments and 20,000 sailors", "links": [{"title": "Royal Indian Navy mutiny", "link": "https://wikipedia.org/wiki/Royal_Indian_Navy_mutiny"}, {"title": "Mumbai", "link": "https://wikipedia.org/wiki/Mumbai"}, {"title": "Presidencies and provinces of British India", "link": "https://wikipedia.org/wiki/Presidencies_and_provinces_of_British_India"}]}, {"year": "1947", "text": "First Indochina War: The French gain complete control of Hanoi after forcing the Viet Minh to withdraw to the mountains.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: The French <a href=\"https://wikipedia.org/wiki/Battle_of_Hanoi\" class=\"mw-redirect\" title=\"Battle of Hanoi\">gain complete control</a> of <a href=\"https://wikipedia.org/wiki/Hanoi\" title=\"Hanoi\">Han<PERSON></a> after forcing the <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\"><PERSON><PERSON> Minh</a> to withdraw to the mountains.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: The French <a href=\"https://wikipedia.org/wiki/Battle_of_Hanoi\" class=\"mw-redirect\" title=\"Battle of Hanoi\">gain complete control</a> of <a href=\"https://wikipedia.org/wiki/Hanoi\" title=\"Hanoi\">Hanoi</a> after forcing the <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\"><PERSON><PERSON> Minh</a> to withdraw to the mountains.", "links": [{"title": "First Indochina War", "link": "https://wikipedia.org/wiki/First_Indochina_War"}, {"title": "Battle of Hanoi", "link": "https://wikipedia.org/wiki/Battle_of_Hanoi"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oi"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viet_Minh"}]}, {"year": "1954", "text": "The first Church of Scientology is established in Los Angeles.", "html": "1954 - The first <a href=\"https://wikipedia.org/wiki/Church_of_Scientology\" title=\"Church of Scientology\">Church of Scientology</a> is established in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Church_of_Scientology\" title=\"Church of Scientology\">Church of Scientology</a> is established in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>.", "links": [{"title": "Church of Scientology", "link": "https://wikipedia.org/wiki/Church_of_Scientology"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}]}, {"year": "1955", "text": "Operation Teapot: Teapot test shot \"<PERSON>p\" is successfully detonated at the Nevada Test Site with a yield of 1.2 kilotons.  Wasp is the first of fourteen shots in the Teapot series.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Operation_Teapot\" title=\"Operation Teapot\">Operation Teapot</a>: Teapot test shot \"Wasp\" is successfully detonated at the <a href=\"https://wikipedia.org/wiki/Nevada_Test_Site\" title=\"Nevada Test Site\">Nevada Test Site</a> with a yield of <a href=\"https://wikipedia.org/wiki/Effects_of_nuclear_explosions#Summary_of_the_effects\" title=\"Effects of nuclear explosions\">1.2 kilotons</a>. Wasp is the first of fourteen shots in the Teapot series.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Teapot\" title=\"Operation Teapot\">Operation Teapot</a>: Teapot test shot \"Wasp\" is successfully detonated at the <a href=\"https://wikipedia.org/wiki/Nevada_Test_Site\" title=\"Nevada Test Site\">Nevada Test Site</a> with a yield of <a href=\"https://wikipedia.org/wiki/Effects_of_nuclear_explosions#Summary_of_the_effects\" title=\"Effects of nuclear explosions\">1.2 kilotons</a>. Wasp is the first of fourteen shots in the Teapot series.", "links": [{"title": "Operation Teapot", "link": "https://wikipedia.org/wiki/Operation_Teapot"}, {"title": "Nevada Test Site", "link": "https://wikipedia.org/wiki/Nevada_Test_Site"}, {"title": "Effects of nuclear explosions", "link": "https://wikipedia.org/wiki/Effects_of_nuclear_explosions#Summary_of_the_effects"}]}, {"year": "1957", "text": "Kenyan rebel leader <PERSON><PERSON> is executed by the British colonial government.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Kenya\" title=\"Kenya\">Kenyan</a> rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is executed by the British colonial government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenya\" title=\"Kenya\">Kenyan</a> rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is executed by the British colonial government.", "links": [{"title": "Kenya", "link": "https://wikipedia.org/wiki/Kenya"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1957", "text": "<PERSON> becomes the last person legally executed in New Zealand.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the last person <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_New_Zealand\" title=\"Capital punishment in New Zealand\">legally executed</a> in New Zealand.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the last person <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_New_Zealand\" title=\"Capital punishment in New Zealand\">legally executed</a> in New Zealand.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Capital punishment in New Zealand", "link": "https://wikipedia.org/wiki/Capital_punishment_in_New_Zealand"}]}, {"year": "1965", "text": "The Gambia becomes independent from the United Kingdom.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/The_Gambia\" title=\"The Gambia\">The Gambia</a> becomes independent from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Gambia\" title=\"The Gambia\">The Gambia</a> becomes independent from the United Kingdom.", "links": [{"title": "The Gambia", "link": "https://wikipedia.org/wiki/The_Gambia"}]}, {"year": "1970", "text": "The Chicago Seven are found not guilty of conspiring to incite riots at the 1968 Democratic National Convention.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/Chicago_Seven\" title=\"Chicago Seven\">Chicago Seven</a> are found not guilty of conspiring to incite riots at the <a href=\"https://wikipedia.org/wiki/1968_Democratic_National_Convention\" title=\"1968 Democratic National Convention\">1968 Democratic National Convention</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chicago_Seven\" title=\"Chicago Seven\">Chicago Seven</a> are found not guilty of conspiring to incite riots at the <a href=\"https://wikipedia.org/wiki/1968_Democratic_National_Convention\" title=\"1968 Democratic National Convention\">1968 Democratic National Convention</a>.", "links": [{"title": "Chicago Seven", "link": "https://wikipedia.org/wiki/Chicago_Seven"}, {"title": "1968 Democratic National Convention", "link": "https://wikipedia.org/wiki/1968_Democratic_National_Convention"}]}, {"year": "1972", "text": "The California Supreme Court in the case of <PERSON> v<PERSON>, (6 Cal.3d 628) invalidates the state's death penalty and commutes the sentences of all death row inmates to life imprisonment.", "html": "1972 - The <a href=\"https://wikipedia.org/wiki/California_Supreme_Court\" class=\"mw-redirect\" title=\"California Supreme Court\">California Supreme Court</a> in the case of <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON> v<PERSON>\">People v. <PERSON></a></i>, (6 Cal.3d 628) invalidates the state's <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">death penalty</a> and commutes the sentences of all <a href=\"https://wikipedia.org/wiki/Death_row\" title=\"Death row\">death row</a> inmates to life imprisonment.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/California_Supreme_Court\" class=\"mw-redirect\" title=\"California Supreme Court\">California Supreme Court</a> in the case of <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v._<PERSON>\" title=\"<PERSON> v<PERSON>\">People v. <PERSON></a></i>, (6 Cal.3d 628) invalidates the state's <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">death penalty</a> and commutes the sentences of all <a href=\"https://wikipedia.org/wiki/Death_row\" title=\"Death row\">death row</a> inmates to life imprisonment.", "links": [{"title": "California Supreme Court", "link": "https://wikipedia.org/wiki/California_Supreme_Court"}, {"title": "People v. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_v._<PERSON>"}, {"title": "Capital punishment", "link": "https://wikipedia.org/wiki/Capital_punishment"}, {"title": "Death row", "link": "https://wikipedia.org/wiki/Death_row"}]}, {"year": "1977", "text": "The Xinjiang 61st Regiment Farm fire started during Chinese New Year when a firecracker ignited memorial wreaths of the late <PERSON>, killing 694 personnel. It remains the deadliest fireworks accident in the world.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/Xinjiang_61st_Regiment_Farm_fire\" title=\"Xinjiang 61st Regiment Farm fire\">Xinjiang 61st Regiment Farm fire</a> started during Chinese New Year when a firecracker ignited memorial wreaths of the <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON>\" title=\"Death and state funeral of <PERSON>\">late <PERSON></a>, killing 694 personnel. It remains the <a href=\"https://wikipedia.org/wiki/List_of_fireworks_accidents_and_incidents\" title=\"List of fireworks accidents and incidents\">deadliest fireworks accident</a> in the world.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Xinjiang_61st_Regiment_Farm_fire\" title=\"Xinjiang 61st Regiment Farm fire\">Xinjiang 61st Regiment Farm fire</a> started during Chinese New Year when a firecracker ignited memorial wreaths of the <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>\" title=\"Death and state funeral of <PERSON>\">late <PERSON></a>, killing 694 personnel. It remains the <a href=\"https://wikipedia.org/wiki/List_of_fireworks_accidents_and_incidents\" title=\"List of fireworks accidents and incidents\">deadliest fireworks accident</a> in the world.", "links": [{"title": "Xinjiang 61st Regiment Farm fire", "link": "https://wikipedia.org/wiki/Xinjiang_61st_Regiment_Farm_fire"}, {"title": "Death and state funeral of <PERSON>", "link": "https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON>"}, {"title": "List of fireworks accidents and incidents", "link": "https://wikipedia.org/wiki/List_of_fireworks_accidents_and_incidents"}]}, {"year": "1977", "text": "A thousand armed soldiers raid Kalakuta Republic, the commune of Nigerian singer <PERSON><PERSON>, leading to the death of <PERSON><PERSON><PERSON><PERSON>.", "html": "1977 - A thousand armed soldiers raid <a href=\"https://wikipedia.org/wiki/Kalakuta_Republic\" title=\"Kalakuta Republic\">Kalakuta Republic</a>, the commune of Nigerian singer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, leading to the death of <a href=\"https://wikipedia.org/wiki/Funmilayo_An<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "A thousand armed soldiers raid <a href=\"https://wikipedia.org/wiki/Kalakuta_Republic\" title=\"Kalakuta Republic\">Kalakuta Republic</a>, the commune of Nigerian singer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, leading to the death of <a href=\"https://wikipedia.org/wiki/Funmilayo_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Kalakuta Republic", "link": "https://wikipedia.org/wiki/Kalakuta_Republic"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fun<PERSON>lay<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "The Space Shuttle Enterprise test vehicle is carried on its maiden \"flight\" on top of a Boeing 747.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Enterprise\" title=\"Space Shuttle Enterprise\">Space Shuttle <i>Enterprise</i></a> test vehicle is carried on its maiden \"flight\" on top of a <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Enterprise\" title=\"Space Shuttle Enterprise\">Space Shuttle <i>Enterprise</i></a> test vehicle is carried on its maiden \"flight\" on top of a <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a>.", "links": [{"title": "Space Shuttle Enterprise", "link": "https://wikipedia.org/wiki/Space_Shuttle_Enterprise"}, {"title": "Boeing 747", "link": "https://wikipedia.org/wiki/Boeing_747"}]}, {"year": "1979", "text": "<PERSON> wins a then-record sixth Daytona 500 after leaders <PERSON><PERSON> and <PERSON> crash on the final lap of the first NASCAR race televised live flag-to-flag.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins a then-record <a href=\"https://wikipedia.org/wiki/1979_Daytona_500\" title=\"1979 Daytona 500\">sixth</a> <a href=\"https://wikipedia.org/wiki/Daytona_500\" title=\"Daytona 500\">Daytona 500</a> after leaders <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> crash on the final lap of the first <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> race televised live flag-to-flag.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins a then-record <a href=\"https://wikipedia.org/wiki/1979_Daytona_500\" title=\"1979 Daytona 500\">sixth</a> <a href=\"https://wikipedia.org/wiki/Daytona_500\" title=\"Daytona 500\">Daytona 500</a> after leaders <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> crash on the final lap of the first <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> race televised live flag-to-flag.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1979 Daytona 500", "link": "https://wikipedia.org/wiki/1979_Daytona_500"}, {"title": "Daytona 500", "link": "https://wikipedia.org/wiki/Daytona_500"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "NASCAR", "link": "https://wikipedia.org/wiki/NASCAR"}]}, {"year": "1983", "text": "Thirteen people die and one is seriously injured in the Wah Mee massacre in Seattle. It is said to be the largest robbery-motivated mass-murder in U.S. history.", "html": "1983 - Thirteen people die and one is seriously injured in the <a href=\"https://wikipedia.org/wiki/Wah_Me<PERSON>_massacre\" title=\"Wah Mee massacre\">Wah Mee massacre</a> in <a href=\"https://wikipedia.org/wiki/Seattle\" title=\"Seattle\">Seattle</a>. It is said to be the largest robbery-motivated mass-murder in U.S. history.", "no_year_html": "Thirteen people die and one is seriously injured in the <a href=\"https://wikipedia.org/wiki/Wah_Mee_massacre\" title=\"Wah Mee massacre\">Wah Mee massacre</a> in <a href=\"https://wikipedia.org/wiki/Seattle\" title=\"Seattle\">Seattle</a>. It is said to be the largest robbery-motivated mass-murder in U.S. history.", "links": [{"title": "Wah Me<PERSON> massacre", "link": "https://wikipedia.org/wiki/Wah_<PERSON><PERSON>_massacre"}, {"title": "Seattle", "link": "https://wikipedia.org/wiki/Seattle"}]}, {"year": "1991", "text": "The IRA explodes bombs in the early morning at Paddington station and Victoria station in London.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">IRA</a> <a href=\"https://wikipedia.org/wiki/Victoria_station_and_Paddington_station_bombings\" class=\"mw-redirect\" title=\"Victoria station and Paddington station bombings\">explodes bombs</a> in the early morning at <a href=\"https://wikipedia.org/wiki/Paddington_station\" class=\"mw-redirect\" title=\"Paddington station\">Paddington station</a> and <a href=\"https://wikipedia.org/wiki/London_Victoria_station\" title=\"London Victoria station\">Victoria station</a> in London.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">IRA</a> <a href=\"https://wikipedia.org/wiki/Victoria_station_and_Paddington_station_bombings\" class=\"mw-redirect\" title=\"Victoria station and Paddington station bombings\">explodes bombs</a> in the early morning at <a href=\"https://wikipedia.org/wiki/Paddington_station\" class=\"mw-redirect\" title=\"Paddington station\">Paddington station</a> and <a href=\"https://wikipedia.org/wiki/London_Victoria_station\" title=\"London Victoria station\">Victoria station</a> in London.", "links": [{"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Victoria station and Paddington station bombings", "link": "https://wikipedia.org/wiki/Victoria_station_and_Paddington_station_bombings"}, {"title": "Paddington station", "link": "https://wikipedia.org/wiki/Paddington_station"}, {"title": "London Victoria station", "link": "https://wikipedia.org/wiki/London_Victoria_station"}]}, {"year": "2001", "text": "FBI agent <PERSON> is arrested for spying for the Soviet Union. He is ultimately convicted and sentenced to life imprisonment.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> agent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested for spying for the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>. He is ultimately convicted and sentenced to life imprisonment.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> agent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested for spying for the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>. He is ultimately convicted and sentenced to life imprisonment.", "links": [{"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "2001", "text": "Sampit conflict: Inter-ethnic violence between Dayaks and Madurese breaks out in Sampit, Central Kalimantan, Indonesia, ultimately resulting in more than 500 deaths and 100,000 Madurese displaced from their homes.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Sampit_conflict\" title=\"Sampit conflict\">Sampit conflict</a>: Inter-ethnic violence between <a href=\"https://wikipedia.org/wiki/Dayak_people\" title=\"Dayak people\">Dayaks</a> and <a href=\"https://wikipedia.org/wiki/Madura_Island\" class=\"mw-redirect\" title=\"Madura Island\">Madurese</a> breaks out in <a href=\"https://wikipedia.org/wiki/Sampit\" title=\"Sampit\">Sampit</a>, <a href=\"https://wikipedia.org/wiki/Central_Kalimantan\" title=\"Central Kalimantan\">Central Kalimantan</a>, Indonesia, ultimately resulting in more than 500 deaths and 100,000 Madurese displaced from their homes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sampit_conflict\" title=\"Sampit conflict\">Sampit conflict</a>: Inter-ethnic violence between <a href=\"https://wikipedia.org/wiki/Dayak_people\" title=\"Dayak people\">Dayaks</a> and <a href=\"https://wikipedia.org/wiki/Madura_Island\" class=\"mw-redirect\" title=\"Madura Island\">Madurese</a> breaks out in <a href=\"https://wikipedia.org/wiki/Sampit\" title=\"Sampit\">Sampit</a>, <a href=\"https://wikipedia.org/wiki/Central_Kalimantan\" title=\"Central Kalimantan\">Central Kalimantan</a>, Indonesia, ultimately resulting in more than 500 deaths and 100,000 Madurese displaced from their homes.", "links": [{"title": "Sampit conflict", "link": "https://wikipedia.org/wiki/Sampit_conflict"}, {"title": "Dayak people", "link": "https://wikipedia.org/wiki/Dayak_people"}, {"title": "Madura Island", "link": "https://wikipedia.org/wiki/Madura_Island"}, {"title": "Sampit", "link": "https://wikipedia.org/wiki/Sampit"}, {"title": "Central Kalimantan", "link": "https://wikipedia.org/wiki/Central_Kalimantan"}]}, {"year": "2003", "text": "192 people die when an arsonist sets fire to a subway train in Daegu, South Korea.", "html": "2003 - 192 people die when an arsonist <a href=\"https://wikipedia.org/wiki/Daegu_subway_fire\" title=\"Daegu subway fire\">sets fire to a subway train</a> in Daegu, South Korea.", "no_year_html": "192 people die when an arsonist <a href=\"https://wikipedia.org/wiki/Daegu_subway_fire\" title=\"Daegu subway fire\">sets fire to a subway train</a> in Daegu, South Korea.", "links": [{"title": "Daegu subway fire", "link": "https://wikipedia.org/wiki/Daegu_subway_fire"}]}, {"year": "2004", "text": "Up to 295 people, 182 of which being rescue workers, die near Nishapur, Iran, when a runaway freight train carrying sulfur, petrol and fertilizer catches fire and explodes.", "html": "2004 - Up to 295 people, 182 of which being rescue workers, die near <a href=\"https://wikipedia.org/wiki/Nishapur\" title=\"Nishapur\">Nishapur, Iran</a>, when a runaway freight train carrying <a href=\"https://wikipedia.org/wiki/Sulfur\" title=\"Sulfur\">sulfur</a>, <a href=\"https://wikipedia.org/wiki/Petrol\" class=\"mw-redirect\" title=\"Petrol\">petrol</a> and <a href=\"https://wikipedia.org/wiki/Fertilizer\" title=\"Fertilizer\">fertilizer</a> <a href=\"https://wikipedia.org/wiki/Nishapur_train_disaster\" title=\"Nishapur train disaster\">catches fire and explodes</a>.", "no_year_html": "Up to 295 people, 182 of which being rescue workers, die near <a href=\"https://wikipedia.org/wiki/Nishapur\" title=\"Nishapur\">Nishapur, Iran</a>, when a runaway freight train carrying <a href=\"https://wikipedia.org/wiki/Sulfur\" title=\"Sulfur\">sulfur</a>, <a href=\"https://wikipedia.org/wiki/Petrol\" class=\"mw-redirect\" title=\"Petrol\">petrol</a> and <a href=\"https://wikipedia.org/wiki/Fertilizer\" title=\"Fertilizer\">fertilizer</a> <a href=\"https://wikipedia.org/wiki/Nishapur_train_disaster\" title=\"Nishapur train disaster\">catches fire and explodes</a>.", "links": [{"title": "Nishapur", "link": "https://wikipedia.org/wiki/Nishapur"}, {"title": "Sulfur", "link": "https://wikipedia.org/wiki/Sulfur"}, {"title": "Petrol", "link": "https://wikipedia.org/wiki/Petrol"}, {"title": "Fertilizer", "link": "https://wikipedia.org/wiki/Fertilizer"}, {"title": "Nishapur train disaster", "link": "https://wikipedia.org/wiki/Nishapur_train_disaster"}]}, {"year": "2010", "text": "WikiLeaks publishes the first of hundreds of thousands of classified documents disclosed by the soldier now known as <PERSON>.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a> publishes the first of hundreds of thousands of <a href=\"https://wikipedia.org/wiki/Classified_information\" title=\"Classified information\">classified documents</a> disclosed by the soldier now known as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a> publishes the first of hundreds of thousands of <a href=\"https://wikipedia.org/wiki/Classified_information\" title=\"Classified information\">classified documents</a> disclosed by the soldier now known as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "WikiLeaks", "link": "https://wikipedia.org/wiki/WikiLeaks"}, {"title": "Classified information", "link": "https://wikipedia.org/wiki/Classified_information"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Manning"}]}, {"year": "2013", "text": "Armed robbers steal a haul of diamonds worth $50 million during a raid at Brussels Airport in Belgium.", "html": "2013 - Armed robbers <a href=\"https://wikipedia.org/wiki/Brussels_Airport_diamond_heist\" title=\"Brussels Airport diamond heist\">steal</a> a haul of diamonds worth $50 million during a raid at <a href=\"https://wikipedia.org/wiki/Brussels_Airport\" title=\"Brussels Airport\">Brussels Airport</a> in <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>.", "no_year_html": "Armed robbers <a href=\"https://wikipedia.org/wiki/Brussels_Airport_diamond_heist\" title=\"Brussels Airport diamond heist\">steal</a> a haul of diamonds worth $50 million during a raid at <a href=\"https://wikipedia.org/wiki/Brussels_Airport\" title=\"Brussels Airport\">Brussels Airport</a> in <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>.", "links": [{"title": "Brussels Airport diamond heist", "link": "https://wikipedia.org/wiki/Brussels_Airport_diamond_heist"}, {"title": "Brussels Airport", "link": "https://wikipedia.org/wiki/Brussels_Airport"}, {"title": "Belgium", "link": "https://wikipedia.org/wiki/Belgium"}]}, {"year": "2014", "text": "At least 76 people are killed and hundreds are injured in clashes between riot police and demonstrators in Kyiv, Ukraine.", "html": "2014 - At least 76 people are killed and hundreds are injured in <a href=\"https://wikipedia.org/wiki/February_2014_Euromaidan_riots\" class=\"mw-redirect\" title=\"February 2014 Euromaidan riots\">clashes</a> between riot police and demonstrators in <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv, Ukraine</a>.", "no_year_html": "At least 76 people are killed and hundreds are injured in <a href=\"https://wikipedia.org/wiki/February_2014_Euromaidan_riots\" class=\"mw-redirect\" title=\"February 2014 Euromaidan riots\">clashes</a> between riot police and demonstrators in <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv, Ukraine</a>.", "links": [{"title": "February 2014 Euromaidan riots", "link": "https://wikipedia.org/wiki/February_2014_Euromaidan_riots"}, {"title": "Kyiv", "link": "https://wikipedia.org/wiki/Kyiv"}]}, {"year": "2018", "text": "Iran Aseman Airlines Flight 3704 crashes in the Dena sub-range in the Zagros Mountains of Iran, Resulting in 66 Deaths", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Iran_Aseman_Airlines_Flight_3704\" title=\"Iran Aseman Airlines Flight 3704\">Iran Aseman Airlines Flight 3704</a> crashes in the <a href=\"https://wikipedia.org/wiki/Dena\" title=\"Dena\">Dena</a> sub-range in the <a href=\"https://wikipedia.org/wiki/Zagros_Mountains\" title=\"Zagros Mountains\">Zagros Mountains</a> of Iran, Resulting in 66 Deaths", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran_Aseman_Airlines_Flight_3704\" title=\"Iran Aseman Airlines Flight 3704\">Iran Aseman Airlines Flight 3704</a> crashes in the <a href=\"https://wikipedia.org/wiki/Dena\" title=\"Dena\">Dena</a> sub-range in the <a href=\"https://wikipedia.org/wiki/Zagros_Mountains\" title=\"Zagros Mountains\">Zagros Mountains</a> of Iran, Resulting in 66 Deaths", "links": [{"title": "Iran Aseman Airlines Flight 3704", "link": "https://wikipedia.org/wiki/Iran_Aseman_Airlines_Flight_3704"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dena"}, {"title": "Zagros Mountains", "link": "https://wikipedia.org/wiki/Zagros_Mountains"}]}, {"year": "2021", "text": "Perseverance, a Mars rover designed to explore Jezero crater on Mars, as part of NASA's Mars 2020 mission, lands successfully.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Perseverance_(rover)\" title=\"Perseverance (rover)\">Perseverance</a>, a <a href=\"https://wikipedia.org/wiki/Mars_rover\" title=\"Mars rover\">Mars rover</a> designed to <a href=\"https://wikipedia.org/wiki/Space_exploration\" title=\"Space exploration\">explore</a> <a href=\"https://wikipedia.org/wiki/Jezero_(crater)\" title=\"Jezero (crater)\">Jezero crater</a> on <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>, as part of <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mars_2020\" title=\"Mars 2020\">Mars 2020</a> mission, lands successfully.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Perseverance_(rover)\" title=\"Perseverance (rover)\">Perseverance</a>, a <a href=\"https://wikipedia.org/wiki/Mars_rover\" title=\"Mars rover\">Mars rover</a> designed to <a href=\"https://wikipedia.org/wiki/Space_exploration\" title=\"Space exploration\">explore</a> <a href=\"https://wikipedia.org/wiki/Jezero_(crater)\" title=\"Jezero (crater)\">Jezero crater</a> on <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>, as part of <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mars_2020\" title=\"Mars 2020\">Mars 2020</a> mission, lands successfully.", "links": [{"title": "Perseverance (rover)", "link": "https://wikipedia.org/wiki/Perseverance_(rover)"}, {"title": "Mars rover", "link": "https://wikipedia.org/wiki/Mars_rover"}, {"title": "Space exploration", "link": "https://wikipedia.org/wiki/Space_exploration"}, {"title": "<PERSON><PERSON><PERSON> (crater)", "link": "https://wikipedia.org/wiki/Jezero_(crater)"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mars 2020", "link": "https://wikipedia.org/wiki/Mars_2020"}]}], "Births": [{"year": "1201", "text": "<PERSON><PERSON> <PERSON>, Persian scientist and writer (d. 1274)", "html": "1201 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Persian scientist and writer (d. 1274)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Persian scientist and writer (d. 1274)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1372", "text": "<PERSON>, Egyptian jurist and scholar (d. 1448)", "html": "1372 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian jurist and scholar (d. 1448)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian jurist and scholar (d. 1448)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1486", "text": "<PERSON><PERSON><PERSON>, Indian monk and saint (d. 1534)", "html": "1486 - <a href=\"https://wikipedia.org/wiki/Chaitanya_Mahaprabhu\" title=\"Chaitanya Mahaprabhu\">Cha<PERSON><PERSON> Ma<PERSON>hu</a>, Indian monk and saint (d. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chaitanya_Mahaprabhu\" title=\"Chaitanya Mahaprabhu\">Chaita<PERSON> Ma<PERSON>hu</a>, Indian monk and saint (d. 1534)", "links": [{"title": "Cha<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chaitanya_Ma<PERSON><PERSON>hu"}]}, {"year": "1516", "text": "<PERSON> of England (d. 1558)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1558)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1530", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese daimyō (d. 1578)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/Uesug<PERSON>_<PERSON>\" title=\"Uesugi Kenshin\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uesug<PERSON>_<PERSON>\" title=\"Uesugi Kenshin\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1578)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>es<PERSON><PERSON>_<PERSON>"}]}, {"year": "1543", "text": "<PERSON>, Duke of Lorraine (d. 1608)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (d. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (d. 1608)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1547", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, founder of Isfahan School of Islamic Philosophy (d. 1621)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/Bah%C4%81%CA%BE_al-d%C4%ABn_al-%CA%BF%C4%80mil%C4%AB\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> al-dīn al-ʿĀ<PERSON>\"><PERSON><PERSON><PERSON><PERSON> al-dīn al-ʿĀ<PERSON></a>, founder of Isfahan School of Islamic Philosophy (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bah%C4%81%CA%BE_al-d%C4%ABn_al-%CA%BF%C4%80mil%C4%AB\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> al-dīn al-ʿĀ<PERSON>\"><PERSON><PERSON><PERSON><PERSON> al-dīn al<PERSON>ʿĀ<PERSON></a>, founder of Isfahan School of Islamic Philosophy (d. 1621)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> al-dīn al-ʿ<PERSON><PERSON><PERSON>ī", "link": "https://wikipedia.org/wiki/Bah%C4%81%CA%BE_al-d%C4%ABn_al-%CA%BF%C4%80mil%C4%AB"}]}, {"year": "1559", "text": "<PERSON>, Swiss philologist and scholar (d. 1614)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss philologist and scholar (d. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss philologist and scholar (d. 1614)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1589", "text": "<PERSON> the Elder, English politician (d. 1655)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the <PERSON>\"><PERSON> the Elder</a>, English politician (d. 1655)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the <PERSON></a>, English politician (d. 1655)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_<PERSON>"}]}, {"year": "1589", "text": "<PERSON><PERSON><PERSON>, Dutch explorer (d. 1646)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/Maarten_Gerritsz_Vries\" class=\"mw-redirect\" title=\"Maarten Gerritsz Vries\"><PERSON><PERSON><PERSON> Gerrits<PERSON></a>, Dutch explorer (d. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maarten_Gerritsz_Vries\" class=\"mw-redirect\" title=\"Maarten Gerritsz Vries\"><PERSON><PERSON><PERSON> Ger<PERSON></a>, Dutch explorer (d. 1646)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Gerritsz_<PERSON>"}]}, {"year": "1602", "text": "<PERSON> the Younger, Swedish soldier and politician, Governor-General of Finland (d. 1680)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Swedish soldier and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Finland\" title=\"Governor-General of Finland\">Governor-General of Finland</a> (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Swedish soldier and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Finland\" title=\"Governor-General of Finland\">Governor-General of Finland</a> (d. 1680)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_the_<PERSON>"}, {"title": "Governor-General of Finland", "link": "https://wikipedia.org/wiki/Governor-General_of_Finland"}]}, {"year": "1602", "text": "<PERSON><PERSON>, Italian painter (d. 1660)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian painter (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian painter (d. 1660)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1609", "text": "<PERSON>, 1st Earl of Clarendon, English historian and politician, Chancellor of the Exchequer (d. 1674)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Clarendon\" title=\"<PERSON>, 1st Earl of Clarendon\"><PERSON>, 1st Earl of Clarendon</a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Clarendon\" title=\"<PERSON>, 1st Earl of Clarendon\"><PERSON>, 1st Earl of Clarendon</a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1674)", "links": [{"title": "<PERSON>, 1st Earl of Clarendon", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Clarendon"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1626", "text": "<PERSON>, Italian physician (d. 1697)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician (d. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician (d. 1697)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_<PERSON>i"}]}, {"year": "1632", "text": "<PERSON>, Italian violinist and composer (d. 1692)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1642", "text": "<PERSON>, French actress (d. 1698)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 1698)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marie_Champmesl%C3%A9"}]}, {"year": "1658", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French philosopher and author (d. 1743)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>r%C3%A9n%C3%A9e_Castel_de_Saint-Pierre\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Castel de Saint-Pierre\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French philosopher and author (d. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9n%C3%A9e_Castel_de_Saint-Pierre\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> de Saint-Pierre\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French philosopher and author (d. 1743)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Charles-<PERSON>r%C3%A9n%C3%A9e_<PERSON><PERSON>_de_Saint-Pierre"}]}, {"year": "1732", "text": "<PERSON>, German organist and composer (d. 1809)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1745", "text": "<PERSON>, Italian physicist, invented the battery (d. 1827)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist, invented the <a href=\"https://wikipedia.org/wiki/Battery_(electricity)\" class=\"mw-redirect\" title=\"Battery (electricity)\">battery</a> (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist, invented the <a href=\"https://wikipedia.org/wiki/Battery_(electricity)\" class=\"mw-redirect\" title=\"Battery (electricity)\">battery</a> (d. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battery (electricity)", "link": "https://wikipedia.org/wiki/Battery_(electricity)"}]}, {"year": "1754", "text": "<PERSON>, Finnish church painter (d. 1797)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish church painter (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish church painter (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, American lawyer and politician (d. 1900)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, American general (d. 1863)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Armistead"}]}, {"year": "1818", "text": "<PERSON>, Irish-Australian politician, 2nd Premier of Victoria (d. 1883)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shanassy\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shanassy\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_O%27Shanassy"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1818", "text": "Ko<PERSON><PERSON><PERSON> Schmidt<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish collector and art connoisseur who donated a large collection to the National Museum in Kraków (d. 1889)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>i%C4%85%C5%BCy%C5%84ski\" title=\"Konstanty Schmidt-Ciążyński\">Konstant<PERSON> Schmidt-Ciążyński</a>, Polish collector and art connoisseur who donated a large collection to the <a href=\"https://wikipedia.org/wiki/National_Museum_in_Krak%C3%B3w\" title=\"National Museum in Kraków\">National Museum in Kraków</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-Ci%C4%85%C5%BCy%C5%84ski\" title=\"Konstanty Schmidt-Ciążyński\">Konst<PERSON><PERSON> Schmidt-Ciążyński</a>, Polish collector and art connoisseur who donated a large collection to the <a href=\"https://wikipedia.org/wiki/National_Museum_in_Krak%C3%B3w\" title=\"National Museum in Kraków\">National Museum in Kraków</a> (d. 1889)", "links": [{"title": "Konstanty Schmidt-Ciążyński", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Schmidt-Ci%C4%85%C5%BCy%C5%84ski"}, {"title": "National Museum in Kraków", "link": "https://wikipedia.org/wiki/National_Museum_in_Krak%C3%B3w"}]}, {"year": "1836", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian mystic and yogi (d. 1886)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Paramahamsa\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Paramah<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian mystic and yogi (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Paramahamsa\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Paramahamsa\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian mystic and yogi (d. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>mahamsa"}]}, {"year": "1838", "text": "<PERSON>, Austrian physicist and philosopher (d. 1916)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physicist and philosopher (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physicist and philosopher (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, English actor, playwright, and manager (d. 1904)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, playwright, and manager (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, playwright, and manager (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, American stained glass artist (d. 1933)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>fort_Tiffany\" title=\"Louis Comfort Tiffany\"><PERSON> Co<PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Stained_glass\" title=\"Stained glass\">stained glass</a> artist (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Louis Comfort Tiffany\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Stained_glass\" title=\"Stained glass\">stained glass</a> artist (d. 1933)", "links": [{"title": "Louis <PERSON>", "link": "https://wikipedia.org/wiki/Louis_Comfort_Tiffany"}, {"title": "Stained glass", "link": "https://wikipedia.org/wiki/Stained_glass"}]}, {"year": "1849", "text": "<PERSON>, Norwegian author, playwright, and politician (d. 1906)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author, playwright, and politician (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author, playwright, and politician (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, German-English singer-songwriter, pianist, and conductor (d. 1934)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English singer-songwriter, pianist, and conductor (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English singer-songwriter, pianist, and conductor (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, French historian, author, and diplomat, French Ambassador to the United States (d. 1932)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian, author, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_French_ambassadors_to_the_United_States\" class=\"mw-redirect\" title=\"List of French ambassadors to the United States\">French Ambassador to the United States</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian, author, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_French_ambassadors_to_the_United_States\" class=\"mw-redirect\" title=\"List of French ambassadors to the United States\">French Ambassador to the United States</a> (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of French ambassadors to the United States", "link": "https://wikipedia.org/wiki/List_of_French_ambassadors_to_the_United_States"}]}, {"year": "1860", "text": "<PERSON>, Swedish artist (d. 1920)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish artist (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish artist (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American businessman, co-founded Bethlehem Steel (d. 1939)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Bethlehem_Steel\" title=\"Bethlehem Steel\">Bethlehem Steel</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Bethlehem_Steel\" title=\"Bethlehem Steel\">Bethlehem Steel</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bethlehem Steel", "link": "https://wikipedia.org/wiki/Bethlehem_Steel"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, German writer (d. 1950)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, German writer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, German writer (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, American painter and author (d. 1924)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, English inventor (d. 1948)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English inventor (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English inventor (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Greek philosopher, author, and playwright (d. 1957)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek philosopher, author, and playwright (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek philosopher, author, and playwright (d. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, French sculptor and illustrator (d. 1954)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and illustrator (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and illustrator (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Belarusian prose writer, journalist and activist (d. 1938)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian prose writer, journalist and activist (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian prose writer, journalist and activist (d. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1890", "text": "<PERSON>, American actor (d. 1956)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1956)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, American actor (d. 1963)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American captain, lawyer, and politician (d. 1944)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Chinese educator and diplomat (d. 1970)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese educator and diplomat (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese educator and diplomat (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Puerto Rican poet and politician, 1st Governor of the Commonwealth of Puerto Rico (d. 1980)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz_Mar%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican poet and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_the_Commonwealth_of_Puerto_Rico\" class=\"mw-redirect\" title=\"Governor of the Commonwealth of Puerto Rico\">Governor of the Commonwealth of Puerto Rico</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz_Mar%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican poet and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_the_Commonwealth_of_Puerto_Rico\" class=\"mw-redirect\" title=\"Governor of the Commonwealth of Puerto Rico\">Governor of the Commonwealth of Puerto Rico</a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Mu%C3%B1oz_Mar%C3%ADn"}, {"title": "Governor of the Commonwealth of Puerto Rico", "link": "https://wikipedia.org/wiki/Governor_of_the_Commonwealth_of_Puerto_Rico"}]}, {"year": "1899", "text": "<PERSON>, English historian and journalist (d. 1985)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and journalist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and journalist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Ukrainian engineer and politician (d. 1983)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian engineer and politician (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian engineer and politician (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikolai_Podgorny"}]}, {"year": "1906", "text": "<PERSON>, Austrian pediatrician and academic (d. 1980)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pediatrician and academic (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pediatrician and academic (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American novelist, short story writer, and essayist (d. 1993)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and fiddler (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>ee_<PERSON><PERSON>_King\" title=\"Pee Wee King\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and fiddler (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ee_<PERSON><PERSON>_King\" title=\"Pee Wee King\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and fiddler (d. 2000)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_King"}]}, {"year": "1915", "text": "<PERSON>, English actress (d. 2002)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American baseball player and manager (d. 1978)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American wrestler and manager (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American wrestler and manager (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American boxer and actor (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Palance\"><PERSON></a>, American boxer and actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Palance\"><PERSON></a>, American boxer and actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, French organist, pianist, composer, and pedagogue (d. 2006)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French organist, pianist, composer, and pedagogue (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French organist, pianist, composer, and pedagogue (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American toxicologist and public health researcher (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American toxicologist and <a href=\"https://wikipedia.org/wiki/Public_health\" title=\"Public health\">public health</a> researcher (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American toxicologist and <a href=\"https://wikipedia.org/wiki/Public_health\" title=\"Public health\">public health</a> researcher (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Public health", "link": "https://wikipedia.org/wiki/Public_health"}]}, {"year": "1921", "text": "<PERSON>, Ukrainian-Russian pianist and composer (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian pianist and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian pianist and composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Grenadan politician, 1st Prime Minister of Grenada (d. 1997)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grenadan politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Grenada\" class=\"mw-redirect\" title=\"Prime Minister of Grenada\">Prime Minister of Grenada</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grenadan politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Grenada\" class=\"mw-redirect\" title=\"Prime Minister of Grenada\">Prime Minister of Grenada</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Grenada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Grenada"}]}, {"year": "1922", "text": "<PERSON>, American journalist and author (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American baseball player (d. 1995)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American painter and illustrator (d. 1976)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>al Ma<PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English historian and author", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian pianist and composer (d. 1968)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American cartoonist, co-created The Wizard of Id (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, co-created <i><a href=\"https://wikipedia.org/wiki/The_Wizard_of_Id\" title=\"The Wizard of Id\">The Wizard of Id</a></i> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hart\"><PERSON></a>, American cartoonist, co-created <i><a href=\"https://wikipedia.org/wiki/The_Wizard_of_Id\" title=\"The Wizard of Id\">The Wizard of Id</a></i> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Wizard of Id", "link": "https://wikipedia.org/wiki/The_Wizard_of_Id"}]}, {"year": "1931", "text": "<PERSON>, American novelist and editor, Nobel Prize laureate (d. 2019).", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and editor, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2019).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and editor, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2019).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Czech-American actor, director, and screenwriter (d. 2018)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Forman\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-American actor, director, and screenwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Forman\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-American actor, director, and screenwriter (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milo%C5%A1_Forman"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Japanese-American multimedia artist and musician", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American multimedia artist and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American multimedia artist and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1933", "text": "<PERSON>, English footballer and manager (d. 2009)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Scottish-English actress (d. 1975)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English actress (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English actress (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American writer and activist (d. 1992)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer and activist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer and activist (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1936", "text": "<PERSON>, American author", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Nigerian political scientist and academic (d. 1996)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian political scientist and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian political scientist and academic (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian singer-songwriter and guitarist (d. 1999)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_De_Andr%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Andr%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabrizio_De_Andr%C3%A9"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American novelist (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English journalist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American musician, singer, and songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Ghanaian queen mother and advocate", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>aba_<PERSON>\" title=\"<PERSON> Eyi<PERSON> I\"><PERSON></a>, Ghanaian queen mother and advocate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Eyi<PERSON> I\"><PERSON></a>, Ghanaian queen mother and advocate", "links": [{"title": "<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_I"}]}, {"year": "1950", "text": "<PERSON>, American director, producer, and screenwriter (d. 2009)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director, producer, and screenwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director, producer, and screenwriter (d. 2009)", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_(filmmaker)"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "Queen <PERSON><PERSON> of Nepal", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON>_of_Nepal\" title=\"Queen <PERSON><PERSON> of Nepal\">Queen <PERSON><PERSON> of Nepal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON>_of_Nepal\" title=\"Queen <PERSON><PERSON> of Nepal\">Queen <PERSON><PERSON> of Nepal</a>", "links": [{"title": "Queen <PERSON><PERSON> of Nepal", "link": "https://wikipedia.org/wiki/Queen_<PERSON><PERSON>_of_Nepal"}]}, {"year": "1951", "text": "<PERSON>, Filipino-Spanish journalist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-Spanish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-Spanish journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American jazz and R&B singer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz and R&amp;B singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz and R&amp;B singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American basketball player (d. 2010)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Newton\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Newton\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor, singer and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American writer and novelist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_See"}]}, {"year": "1957", "text": "<PERSON><PERSON>, German sprinter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Koch"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American television personality", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English-American actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Italian-Australian actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-Australian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON> <PERSON>, American rapper, record producer, and entrepreneur", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON>\" title=\"Dr. Dre\">Dr. <PERSON><PERSON></a>, American rapper, record producer, and entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON>\" title=\"Dr. <PERSON><PERSON>\">Dr. <PERSON><PERSON></a>, American rapper, record producer, and entrepreneur", "links": [{"title": "Dr. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Dominican-English cricketer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American suspected assassin", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American suspected assassin", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American suspected assassin", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Italian footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Welsh sprinter and hurdler", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sprinter and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sprinter and hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Russian ice hockey player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Danish golfer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bj%C3%B8rn\" title=\"<PERSON>\"><PERSON></a>, Danish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bj%C3%B8rn\" title=\"<PERSON>\"><PERSON></a>, Danish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bj%C3%B8rn"}]}, {"year": "1974", "text": "<PERSON>, American painter and academic", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Czech footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ra<PERSON>_%C4%8Cern%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%8Cern%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radek_%C4%8Cern%C3%BD"}]}, {"year": "1974", "text": "<PERSON>, American environmentalist and author", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Julia Butterfly Hill\"><PERSON></a>, American environmentalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Julia Butterfly Hill\"><PERSON></a>, American environmentalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American personal trainer and television personality", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American personal trainer and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American personal trainer and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American actor and comedian", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Kazakhstani-Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>pov\"><PERSON></a>, Kazakhstani-Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Antropov\"><PERSON></a>, Kazakhstani-Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>v"}]}, {"year": "1980", "text": "<PERSON>, Russian-American musician and songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ktor\" title=\"<PERSON> Spektor\"><PERSON></a>, Russian-American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ktor\" title=\"<PERSON> Spektor\"><PERSON></a>, Russian-American musician and songwriter", "links": [{"title": "Regina Spektor", "link": "https://wikipedia.org/wiki/Regina_Spektor"}]}, {"year": "1981", "text": "<PERSON>, Russian-American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Alex_R%C3%ADos\" title=\"Alex <PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_R%C3%ADos\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "Alex <PERSON>", "link": "https://wikipedia.org/wiki/Alex_R%C3%ADos"}]}, {"year": "1982", "text": "<PERSON>, German footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kara_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Cameroonian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German-Russian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Roman_Neust%C3%A4dter\" title=\"Roman Neustädter\"><PERSON></a>, German-Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Neust%C3%A4dter\" title=\"Roman Neustädter\"><PERSON></a>, German-Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Neust%C3%A4dter"}]}, {"year": "1988", "text": "<PERSON>, American actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American-Brazilian actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Brazilian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Brazilian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Serbian basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Sonja_Vasi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sonja_Vasi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sonja_Vasi%C4%87"}]}, {"year": "1990", "text": "<PERSON>, American saber fencer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saber fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saber fencer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Dutch baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Costa Rican footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bryan_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, German footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Le%27Veon_Bell\" title=\"Le'Veon Bell\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le%27V<PERSON>_Bell\" title=\"Le'Veon Bell\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "Le'Veon Bell", "link": "https://wikipedia.org/wiki/Le%27Veon_Bell"}]}, {"year": "1992", "text": "<PERSON>, Slovak ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Din\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Din\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Din"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>-<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, South Korean rapper, singer-songwriter, dancer, and record producer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean rapper, singer-songwriter, dancer, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean rapper, singer-songwriter, dancer, and record producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, German basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Dutch footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>k%C3%A9"}]}, {"year": "1996", "text": "<PERSON>, American-Greek basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Greek basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, South Korean singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1998", "text": "<PERSON>, South Korean and American rapper, singer and songwriter", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, South Korean and American rapper, singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, South Korean and American rapper, singer and songwriter", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>(rapper)"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Moroccan footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Zakaria_Aboukhlal\" title=\"Zakaria Aboukhlal\"><PERSON><PERSON><PERSON></a>, Moroccan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zaka<PERSON>_A<PERSON>hlal\" title=\"Zakaria Aboukhlal\"><PERSON><PERSON><PERSON></a>, Moroccan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zaka<PERSON>_<PERSON>uk<PERSON>al"}]}, {"year": "2000", "text": "<PERSON>, Italian footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>adori"}]}, {"year": "2001", "text": "<PERSON><PERSON>, French footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Coulibaly\" title=\"Tanguy Coulibaly\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Coulibaly\" title=\"Tanguy Coulibaly\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tanguy_Coulibaly"}]}, {"year": "2001", "text": "<PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2002", "text": "<PERSON><PERSON>, Indian sports shooter", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>haker\"><PERSON><PERSON></a>, Indian sports shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bhaker\"><PERSON><PERSON></a>, Indian sports shooter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>er"}]}], "Deaths": [{"year": "675", "text": "<PERSON><PERSON>, bishop of Lindisfarne", "html": "675 - <a href=\"https://wikipedia.org/wiki/Colm%C3%A1n_of_Lindisfarne\" title=\"<PERSON><PERSON> of Lindisfarne\"><PERSON><PERSON></a>, bishop of Lindisfarne", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colm%C3%A1n_of_Lindisfarne\" title=\"<PERSON><PERSON> of Lindisfarne\"><PERSON><PERSON></a>, bishop of Lindisfarne", "links": [{"title": "<PERSON><PERSON> of Lindisfarne", "link": "https://wikipedia.org/wiki/Colm%C3%A1n_of_Lindisfarne"}]}, {"year": "814", "text": "<PERSON><PERSON><PERSON>, Frankish monk and diplomat (b. 760)", "html": "814 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Frankish monk and diplomat (b. 760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Frankish monk and diplomat (b. 760)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>bert"}]}, {"year": "901", "text": "<PERSON><PERSON><PERSON><PERSON> ibn <PERSON>, Arab astronomer and physician (b. 826)", "html": "901 - <a href=\"https://wikipedia.org/wiki/Th%C4%81bit_ibn_<PERSON>ra\" title=\"Th<PERSON><PERSON> ibn Qurra\"><PERSON><PERSON><PERSON><PERSON> ibn <PERSON></a>, Arab astronomer and physician (b. 826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C4%81bit_ibn_Qurra\" title=\"Thā<PERSON> ibn Qurra\"><PERSON><PERSON><PERSON><PERSON> ibn <PERSON></a>, Arab astronomer and physician (b. 826)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/Th%C4%81bit_ibn_<PERSON>ra"}]}, {"year": "999", "text": "<PERSON>, pope of the Catholic Church (b. 972)", "html": "999 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Gregory V\"><PERSON></a>, pope of the Catholic Church (b. 972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1139", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Grand Prince of Kiev (b. 1082)", "html": "1139 - <a href=\"https://wikipedia.org/wiki/Yaropolk_II_of_Kiev\" title=\"Yaropolk II of Kiev\">Yaropolk II</a>, Grand Prince of Kiev (b. 1082)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yaropolk_II_of_Kiev\" title=\"Yaropolk II of Kiev\">Yaropolk II</a>, Grand Prince of Kiev (b. 1082)", "links": [{"title": "Yaropolk II of Kiev", "link": "https://wikipedia.org/wiki/Yaropolk_II_of_Kiev"}]}, {"year": "1218", "text": "<PERSON><PERSON>, duke of Zähringen (b. 1160)", "html": "1218 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Z%C3%A4hr<PERSON>\" title=\"<PERSON><PERSON> <PERSON>, Duke of Zähringen\"><PERSON><PERSON></a>, duke of Zähringen (b. 1160)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Z%C3%A4hr<PERSON>\" title=\"<PERSON><PERSON> <PERSON>, Duke of Zähringen\"><PERSON><PERSON></a>, duke of Zähringen (b. 1160)", "links": [{"title": "<PERSON><PERSON>, Duke of Zähringen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_V,_Duke_of_Z%C3%A4hringen"}]}, {"year": "1225", "text": "<PERSON>, 3rd Earl of Norfolk, Norman nobleman", "html": "1225 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Norfolk\" title=\"<PERSON>, 3rd Earl of Norfolk\"><PERSON>, 3rd Earl of Norfolk</a>, Norman nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Norfolk\" title=\"<PERSON>, 3rd Earl of Norfolk\"><PERSON>, 3rd Earl of Norfolk</a>, Norman nobleman", "links": [{"title": "<PERSON>, 3rd Earl of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Norfolk"}]}, {"year": "1294", "text": "<PERSON><PERSON><PERSON>, Mongol emperor (b. 1215)", "html": "1294 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, Mongol emperor (b. 1215)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, Mongol emperor (b. 1215)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1379", "text": "<PERSON>, duke of Mecklenburg (b. 1318)", "html": "1379 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON></a>, duke of Mecklenburg (b. 1318)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON></a>, duke of Mecklenburg (b. 1318)", "links": [{"title": "<PERSON>, Duke of Mecklenburg", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mecklenburg"}]}, {"year": "1397", "text": "<PERSON><PERSON><PERSON><PERSON>, French nobleman (b. 1340)", "html": "1397 - <a href=\"https://wikipedia.org/wiki/En<PERSON><PERSON><PERSON>_VII,_Lord_of_Coucy\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> VII, Lord of Coucy\"><PERSON><PERSON><PERSON><PERSON> VII</a>, French nobleman (b. 1340)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_VII,_Lord_of_Coucy\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> VII, Lord of Coucy\"><PERSON><PERSON><PERSON><PERSON> VII</a>, French nobleman (b. 1340)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Lord of Coucy", "link": "https://wikipedia.org/wiki/En<PERSON><PERSON><PERSON>_VII,_Lord_of_Co<PERSON>y"}]}, {"year": "1455", "text": "<PERSON><PERSON>, Italian priest and painter (b. 1395)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/Fra_Angelico\" title=\"Fra Angelico\"><PERSON><PERSON></a>, Italian priest and painter (b. 1395)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fra_Angel<PERSON>\" title=\"Fra Angelico\"><PERSON><PERSON></a>, Italian priest and painter (b. 1395)", "links": [{"title": "Fra Angelico", "link": "https://wikipedia.org/wiki/Fra_Angelico"}]}, {"year": "1478", "text": "<PERSON>, 1st Duke of Clarence, English nobleman (b. 1449)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_<PERSON>_Clarence\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke <PERSON> Clarence\"><PERSON>, 1st Duke of Clarence</a>, English nobleman (b. 1449)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_<PERSON>_Clarence\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Clarence\"><PERSON>, 1st Duke of Clarence</a>, English nobleman (b. 1449)", "links": [{"title": "<PERSON>, 1st Duke of Clarence", "link": "https://wikipedia.org/wiki/<PERSON>_Plantagenet,_1st_Duke_<PERSON>_Clarence"}]}, {"year": "1502", "text": "<PERSON><PERSON><PERSON>, duchess of Bavaria (b. 1457)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duchess_of_Bavaria\" title=\"<PERSON><PERSON><PERSON>, Duchess of Bavaria\"><PERSON><PERSON><PERSON></a>, duchess of Bavaria (b. 1457)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duchess_of_Bavaria\" title=\"<PERSON><PERSON><PERSON>, Duchess of Bavaria\"><PERSON><PERSON><PERSON></a>, duchess of Bavaria (b. 1457)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duchess of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duchess_of_Bavaria"}]}, {"year": "1535", "text": "<PERSON>, German magician, astrologer, and theologian (b. 1486)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German magician, astrologer, and theologian (b. 1486)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German magician, astrologer, and theologian (b. 1486)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1546", "text": "<PERSON>, German priest and theologian, leader of the Protestant Reformation (b. 1483)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and theologian, leader of the <a href=\"https://wikipedia.org/wiki/Protestant_Reformation\" class=\"mw-redirect\" title=\"Protestant Reformation\">Protestant Reformation</a> (b. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and theologian, leader of the <a href=\"https://wikipedia.org/wiki/Protestant_Reformation\" class=\"mw-redirect\" title=\"Protestant Reformation\">Protestant Reformation</a> (b. 1483)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Protestant Reformation", "link": "https://wikipedia.org/wiki/Protestant_Reformation"}]}, {"year": "1564", "text": "<PERSON><PERSON>, Italian sculptor and painter (b. 1475)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian sculptor and painter (b. 1475)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian sculptor and painter (b. 1475)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1654", "text": "<PERSON><PERSON><PERSON>, French author (b. 1594)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author (b. 1594)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author (b. 1594)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1658", "text": "<PERSON>, 1st Viscount <PERSON>, English courtier (b. c. 1591)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON><PERSON><PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English courtier (b. c. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English courtier (b. c. 1591)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1683", "text": "<PERSON><PERSON>, Dutch painter (b. 1620)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter (b. 1620)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1695", "text": "<PERSON>, governor of Massachusetts (b. 1650)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, governor of Massachusetts (b. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, governor of Massachusetts (b. 1650)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1712", "text": "<PERSON>, <PERSON><PERSON><PERSON> of France, (b. 1682)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, <PERSON><PERSON><PERSON> of France</a>, (b. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, <PERSON><PERSON><PERSON> of France</a>, (b. 1682)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1743", "text": "<PERSON>, Italian noble (b. 1667)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian noble (b. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian noble (b. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27_<PERSON>"}]}, {"year": "1748", "text": "<PERSON>, Austrian field marshal (b. 1677)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_und_Traun\" class=\"mw-redirect\" title=\"<PERSON> von <PERSON> und Traun\"><PERSON> und Tra<PERSON></a>, Austrian field marshal (b. 1677)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_und_Traun\" class=\"mw-redirect\" title=\"<PERSON> und Traun\"><PERSON> und Tra<PERSON></a>, Austrian field marshal (b. 1677)", "links": [{"title": "<PERSON> und T<PERSON>un", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>un"}]}, {"year": "1772", "text": "Count <PERSON>, Danish politician (b. 1712)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>\" title=\"Count <PERSON>\">Count <PERSON></a>, Danish politician (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Count <PERSON>\">Count <PERSON></a>, Danish politician (b. 1712)", "links": [{"title": "Count <PERSON>", "link": "https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, French economist and politician, Controller-General of Finances (b. 1715)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician, <a href=\"https://wikipedia.org/wiki/Controller-General_of_Finances\" title=\"Controller-General of Finances\">Controller-General of Finances</a> (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician, <a href=\"https://wikipedia.org/wiki/Controller-General_of_Finances\" title=\"Controller-General of Finances\">Controller-General of Finances</a> (b. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Controller-General of Finances", "link": "https://wikipedia.org/wiki/Controller-General_of_Finances"}]}, {"year": "1780", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian pastor and poet (b. 1714)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian pastor and poet (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian pastor and poet (b. 1714)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kristijon<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, English geologist and clockmaker (b. 1713)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist and clockmaker (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist and clockmaker (b. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, German poet and educator (b. 1719)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and educator (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and educator (b. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, German mathematician and academic (b. 1804)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Bulgarian activist, founded the Internal Revolutionary Organization (b. 1837)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian activist, founded the <a href=\"https://wikipedia.org/wiki/Internal_Revolutionary_Organization\" title=\"Internal Revolutionary Organization\">Internal Revolutionary Organization</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian activist, founded the <a href=\"https://wikipedia.org/wiki/Internal_Revolutionary_Organization\" title=\"Internal Revolutionary Organization\">Internal Revolutionary Organization</a> (b. 1837)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Internal Revolutionary Organization", "link": "https://wikipedia.org/wiki/Internal_Revolutionary_Organization"}]}, {"year": "1880", "text": "<PERSON><PERSON>, Russian organic chemist (b. 1812)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian organic chemist (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian organic chemist (b. 1812)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, American lawyer and politician, 1st Chief Justice of California (b. 1814)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_California\" class=\"mw-redirect\" title=\"Chief Justice of California\">Chief Justice of California</a> (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_California\" class=\"mw-redirect\" title=\"Chief Justice of California\">Chief Justice of California</a> (b. 1814)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Justice of California", "link": "https://wikipedia.org/wiki/Chief_Justice_of_California"}]}, {"year": "1902", "text": "<PERSON>, American businessman, founded Tiffany & Co. (b. 1812)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Tiffany_%26_Co.\" title=\"Tiffany &amp; Co.\">Tiffany &amp; Co.</a> (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Tiffany_%26_Co.\" title=\"Tiffany &amp; Co.\">Tiffany &amp; Co.</a> (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Tiffany & Co.", "link": "https://wikipedia.org/wiki/Tiffany_%26_Co."}]}, {"year": "1910", "text": "<PERSON>, American activist (b. 1831)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(abolitionist)\" title=\"<PERSON> (abolitionist)\"><PERSON></a>, American activist (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(abolitionist)\" title=\"<PERSON> (abolitionist)\"><PERSON></a>, American activist (b. 1831)", "links": [{"title": "<PERSON> (abolitionist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(abolitionist)"}]}, {"year": "1915", "text": "<PERSON>, American soldier and criminal (b. 1843)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and criminal (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and criminal (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American boxer and actor (b. 1866)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American missionary and politician (b. 1851)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/David_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary and politician (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/David_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary and politician (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>l"}]}, {"year": "1956", "text": "<PERSON><PERSON>, French composer (b. 1860)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French composer (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French composer (b. 1860)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American stage actress (b. c. 1885)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American stage actress (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1885</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American stage actress (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1885</span>)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Soviet pilot and military officer (b. 1934)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet pilot and military officer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet pilot and military officer (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON> <PERSON>, American physicist and academic (b. 1904)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and academic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and academic (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Serbian lawyer and politician, 17th Prime Minister of Yugoslavia (b. 1893)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Dragi%C5%A1a_Cvetkovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dragi%C5%A1a_Cvetkovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dragi%C5%A1a_Cvetkovi%C4%87"}, {"title": "Prime Minister of Yugoslavia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia"}]}, {"year": "1977", "text": "<PERSON>, American actor (b. 1905)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American engineer and businessman, founded the Northrop Corporation (b. 1895)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack <PERSON>\"><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Northrop_Corporation\" title=\"Northrop Corporation\">Northrop Corporation</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack <PERSON>\"><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Northrop_Corporation\" title=\"Northrop Corporation\">Northrop Corporation</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Northrop Corporation", "link": "https://wikipedia.org/wiki/Northrop_Corporation"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, New Zealand author (b. 1895)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Ngaio_Marsh\" title=\"Ngaio Marsh\"><PERSON><PERSON><PERSON></a>, New Zealand author (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ngaio_Marsh\" title=\"Ngaio Marsh\"><PERSON><PERSON><PERSON></a>, New Zealand author (b. 1895)", "links": [{"title": "Ngaio Marsh", "link": "https://wikipedia.org/wiki/Ngaio_Marsh"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Polish-Swiss painter and illustrator (b. 1908)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Balthus\" title=\"Balthus\"><PERSON><PERSON><PERSON></a>, Polish-Swiss painter and illustrator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Balthus\" title=\"Balthus\"><PERSON><PERSON><PERSON></a>, Polish-Swiss painter and illustrator (b. 1908)", "links": [{"title": "Balthus", "link": "https://wikipedia.org/wiki/Balthus"}]}, {"year": "2001", "text": "<PERSON>, American racer and seven-time NASCAR Cup Series champion (b. 1951)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racer and seven-time NASCAR Cup Series champion (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racer and seven-time NASCAR Cup Series champion (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Canadian-French author and playwright (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-French author and playwright (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-French author and playwright (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Austrian-American singer (b. 1914)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American singer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American singer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Czechoslovakian-born English rabbi (b. 1920 or 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czechoslovakian-born English rabbi (b. 1920 or 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czechoslovakian-born English rabbi (b. 1920 or 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Italian designer and architect (b. 1931)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian designer and architect (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian designer and architect (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, Italian actor and voice actor (b. 1947)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor and voice actor (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor and voice actor (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lav<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, award-winning American actor (b. 1930)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, award-winning American actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, award-winning American actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, Australian laicised Catholic priest and sex offender (b. 1934)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian laicised Catholic priest and sex offender (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian laicised Catholic priest and sex offender (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}