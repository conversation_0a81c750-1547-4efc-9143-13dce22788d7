{"date": "September 19", "url": "https://wikipedia.org/wiki/September_19", "data": {"Events": [{"year": "96", "text": "<PERSON><PERSON><PERSON>, suspected of complicity of the death of <PERSON><PERSON><PERSON>, is declared emperor by Senate. The Senate then annuls laws passed by <PERSON><PERSON><PERSON> and orders his statues to be destroyed.", "html": "96 - <a href=\"https://wikipedia.org/wiki/Nerva\" title=\"Nerva\"><PERSON><PERSON><PERSON></a>, suspected of complicity of the death of <a href=\"https://wikipedia.org/wiki/Domitian\" title=\"Domitian\">Dom<PERSON><PERSON></a>, is declared emperor by Senate. The Senate then annuls laws passed by <PERSON><PERSON><PERSON> and orders his statues to be destroyed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nerva\" title=\"Nerva\"><PERSON><PERSON><PERSON></a>, suspected of complicity of the death of <a href=\"https://wikipedia.org/wiki/Domitian\" title=\"Domitian\">Dom<PERSON><PERSON></a>, is declared emperor by Senate. The Senate then annuls laws passed by <PERSON><PERSON><PERSON> and orders his statues to be destroyed.", "links": [{"title": "Nerva", "link": "https://wikipedia.org/wiki/Nerva"}, {"title": "Domitian", "link": "https://wikipedia.org/wiki/Domitian"}]}, {"year": "634", "text": "Siege of Damascus: The Rashidun Arabs under <PERSON> capture Damascus from the Byzantine Empire.", "html": "634 - <a href=\"https://wikipedia.org/wiki/Siege_of_Damascus_(634)\" title=\"Siege of Damascus (634)\">Siege of Damascus</a>: The Rashidun Arabs under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a> capture Damascus from the Byzantine Empire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Damascus_(634)\" title=\"Siege of Damascus (634)\">Siege of Damascus</a>: The Rashidun Arabs under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON> ibn <PERSON></a> capture Damascus from the Byzantine Empire.", "links": [{"title": "Siege of Damascus (634)", "link": "https://wikipedia.org/wiki/Siege_of_Damascus_(634)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1356", "text": "Battle of Poitiers: An English army under the command of <PERSON> the Black Prince defeats a French army and captures King <PERSON>.", "html": "1356 - <a href=\"https://wikipedia.org/wiki/Battle_of_Poitiers\" title=\"Battle of Poitiers\">Battle of Poitiers</a>: An English army under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Black_Prince\" title=\"<PERSON> the Black Prince\"><PERSON> the Black Prince</a> defeats a French army and captures King <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Poitiers\" title=\"Battle of Poitiers\">Battle of Poitiers</a>: An English army under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Black_Prince\" title=\"<PERSON> the Black Prince\"><PERSON> the Black Prince</a> defeats a French army and captures King <PERSON>.", "links": [{"title": "Battle of Poitiers", "link": "https://wikipedia.org/wiki/Battle_of_Poitiers"}, {"title": "<PERSON> the Black Prince", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1410", "text": "End of the Siege of Marienburg: The State of the Teutonic Order repulses the joint Polish—Lithuanian forces.", "html": "1410 - End of the <a href=\"https://wikipedia.org/wiki/Siege_of_Marienburg_(1410)\" title=\"Siege of Marienburg (1410)\">Siege of Marienburg</a>: The <a href=\"https://wikipedia.org/wiki/State_of_the_Teutonic_Order\" title=\"State of the Teutonic Order\">State of the Teutonic Order</a> repulses the joint <a href=\"https://wikipedia.org/wiki/Kingdom_of_Poland_(1385%E2%80%931569)\" class=\"mw-redirect\" title=\"Kingdom of Poland (1385-1569)\">Polish</a>—<a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Lithuanian</a> forces.", "no_year_html": "End of the <a href=\"https://wikipedia.org/wiki/Siege_of_Marienburg_(1410)\" title=\"Siege of Marienburg (1410)\">Siege of Marienburg</a>: The <a href=\"https://wikipedia.org/wiki/State_of_the_Teutonic_Order\" title=\"State of the Teutonic Order\">State of the Teutonic Order</a> repulses the joint <a href=\"https://wikipedia.org/wiki/Kingdom_of_Poland_(1385%E2%80%931569)\" class=\"mw-redirect\" title=\"Kingdom of Poland (1385-1569)\">Polish</a>—<a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Lithuanian</a> forces.", "links": [{"title": "Siege of Marienburg (1410)", "link": "https://wikipedia.org/wiki/Siege_of_Marienburg_(1410)"}, {"title": "State of the Teutonic Order", "link": "https://wikipedia.org/wiki/State_of_the_Teutonic_Order"}, {"title": "Kingdom of Poland (1385-1569)", "link": "https://wikipedia.org/wiki/Kingdom_of_Poland_(1385%E2%80%931569)"}, {"title": "Grand Duchy of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania"}]}, {"year": "1676", "text": "Jamestown is burned to the ground by the forces of <PERSON> during <PERSON>'s Rebellion.", "html": "1676 - Jamestown is burned to the ground by the forces of <PERSON> during <a href=\"https://wikipedia.org/wiki/Bacon%27s_Rebellion\" title=\"<PERSON>'s Rebellion\"><PERSON>'s Rebellion</a>.", "no_year_html": "Jamestown is burned to the ground by the forces of <PERSON> during <a href=\"https://wikipedia.org/wiki/Bacon%27s_Rebellion\" title=\"<PERSON>'s Rebellion\"><PERSON>'s Rebellion</a>.", "links": [{"title": "<PERSON>'s Rebellion", "link": "https://wikipedia.org/wiki/Bacon%27s_Rebellion"}]}, {"year": "1777", "text": "American Revolutionary War: British forces win a tactically expensive victory over the Continental Army in the First Battle of Saratoga.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British forces win a tactically expensive victory over the Continental Army in the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Saratoga\" class=\"mw-redirect\" title=\"First Battle of Saratoga\">First Battle of Saratoga</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British forces win a tactically expensive victory over the Continental Army in the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Saratoga\" class=\"mw-redirect\" title=\"First Battle of Saratoga\">First Battle of Saratoga</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "First Battle of Saratoga", "link": "https://wikipedia.org/wiki/First_Battle_of_Saratoga"}]}, {"year": "1778", "text": "The Continental Congress passes the first United States federal budget.", "html": "1778 - The <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a> passes the first <a href=\"https://wikipedia.org/wiki/United_States_federal_budget\" title=\"United States federal budget\">United States federal budget</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a> passes the first <a href=\"https://wikipedia.org/wiki/United_States_federal_budget\" title=\"United States federal budget\">United States federal budget</a>.", "links": [{"title": "Continental Congress", "link": "https://wikipedia.org/wiki/Continental_Congress"}, {"title": "United States federal budget", "link": "https://wikipedia.org/wiki/United_States_federal_budget"}]}, {"year": "1796", "text": "<PERSON>'s <PERSON><PERSON>ell Address is printed across America as an open letter to the public.", "html": "1796 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>%27s_Farewell_Address\" title=\"<PERSON>'s Farewell Address\"><PERSON>'s Far<PERSON>ell Address</a> is printed across America as an open letter to the public.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_<PERSON>%27s_Farewell_Address\" title=\"<PERSON>'s Farewell Address\"><PERSON>'s Farewell Address</a> is printed across America as an open letter to the public.", "links": [{"title": "<PERSON>'s <PERSON><PERSON><PERSON> Address", "link": "https://wikipedia.org/wiki/George_<PERSON>%27s_<PERSON><PERSON><PERSON>_Address"}]}, {"year": "1799", "text": "French Revolutionary Wars: French-Dutch victory against the Russians and British in the Battle of Bergen.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: French-Dutch victory against the Russians and British in the <a href=\"https://wikipedia.org/wiki/Battle_of_Bergen_(1799)\" title=\"Battle of Bergen (1799)\">Battle of Bergen</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: French-Dutch victory against the Russians and British in the <a href=\"https://wikipedia.org/wiki/Battle_of_Bergen_(1799)\" title=\"Battle of Bergen (1799)\">Battle of Bergen</a>.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "Battle of Bergen (1799)", "link": "https://wikipedia.org/wiki/Battle_of_Bergen_(1799)"}]}, {"year": "1846", "text": "Two French shepherd children, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, experience a Marian apparition on a mountaintop near La Salette, France, now known as Our Lady of La Salette.", "html": "1846 - Two French shepherd children, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, experience a <a href=\"https://wikipedia.org/wiki/Marian_apparition\" title=\"Marian apparition\">Marian apparition</a> on a mountaintop near La Salette, France, now known as <a href=\"https://wikipedia.org/wiki/Our_Lady_of_La_Salette\" title=\"Our Lady of La Salette\">Our Lady of La Salette</a>.", "no_year_html": "Two French shepherd children, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, experience a <a href=\"https://wikipedia.org/wiki/Marian_apparition\" title=\"Marian apparition\">Marian apparition</a> on a mountaintop near La Salette, France, now known as <a href=\"https://wikipedia.org/wiki/Our_Lady_of_La_Salette\" title=\"Our Lady of La Salette\">Our Lady of La Salette</a>.", "links": [{"title": "Marian apparition", "link": "https://wikipedia.org/wiki/Marian_apparition"}, {"title": "Our Lady of La Salette", "link": "https://wikipedia.org/wiki/Our_Lady_of_La_<PERSON>"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON> discovers the asteroid <PERSON><PERSON> from the north dome of the Astronomical Observatory of Capodimonte.", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> discovers the asteroid <a href=\"https://wikipedia.org/wiki/20_Massalia\" title=\"20 Massalia\">Massalia</a> from the north dome of the Astronomical Observatory of Capodimonte.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> discovers the asteroid <a href=\"https://wikipedia.org/wiki/20_Massalia\" title=\"20 Massalia\">Massalia</a> from the north dome of the Astronomical Observatory of Capodimonte.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "20 Massalia", "link": "https://wikipedia.org/wiki/20_Massalia"}]}, {"year": "1862", "text": "American Civil War: Union troops under <PERSON> defeat a Confederate force commanded by <PERSON>.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Union troops under <PERSON> <a href=\"https://wikipedia.org/wiki/Battle_of_Iuka\" title=\"Battle of Iuka\">defeat</a> a Confederate force commanded by <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Union troops under <PERSON> <a href=\"https://wikipedia.org/wiki/Battle_of_Iuka\" title=\"Battle of Iuka\">defeat</a> a Confederate force commanded by <PERSON>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Iuka", "link": "https://wikipedia.org/wiki/Battle_of_Iuka"}]}, {"year": "1863", "text": "American Civil War: The first day of the Battle of Chickamauga, in northwestern Georgia, the bloodiest two-day battle of the conflict, and the only significant Confederate victory in the war's Western Theater.", "html": "1863 - American Civil War: The first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Chickamauga\" title=\"Battle of Chickamauga\">Battle of Chickamauga</a>, in northwestern Georgia, the bloodiest two-day battle of the conflict, and the only significant Confederate victory in the war's Western Theater.", "no_year_html": "American Civil War: The first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Chickamauga\" title=\"Battle of Chickamauga\">Battle of Chickamauga</a>, in northwestern Georgia, the bloodiest two-day battle of the conflict, and the only significant Confederate victory in the war's Western Theater.", "links": [{"title": "Battle of Chickamauga", "link": "https://wikipedia.org/wiki/Battle_of_Chickamauga"}]}, {"year": "1864", "text": "American Civil War: Union troops under <PERSON> defeat a Confederate force commanded by <PERSON><PERSON>. With over 50,000 troops engaged, it was the largest battle fought in the Shenandoah Valley.", "html": "1864 - American Civil War: Union troops under <PERSON> <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Winchester\" title=\"Third Battle of Winchester\">defeat</a> a Confederate force commanded by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Early\" title=\"<PERSON><PERSON> Early\"><PERSON><PERSON> Early</a>. With over 50,000 troops engaged, it was the largest battle fought in the Shenandoah Valley.", "no_year_html": "American Civil War: Union troops under <PERSON> <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Winchester\" title=\"Third Battle of Winchester\">defeat</a> a Confederate force commanded by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Early\" title=\"<PERSON><PERSON> Early\"><PERSON><PERSON> Early</a>. With over 50,000 troops engaged, it was the largest battle fought in the Shenandoah Valley.", "links": [{"title": "Third Battle of Winchester", "link": "https://wikipedia.org/wiki/Third_Battle_of_Winchester"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1868", "text": "La Gloriosa begins in Spain.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Glorious_Revolution_(Spain)\" title=\"Glorious Revolution (Spain)\">La Gloriosa</a> begins in Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glorious_Revolution_(Spain)\" title=\"Glorious Revolution (Spain)\">La Gloriosa</a> begins in Spain.", "links": [{"title": "Glorious Revolution (Spain)", "link": "https://wikipedia.org/wiki/Glorious_Revolution_(Spain)"}]}, {"year": "1870", "text": "Franco-Prussian War: The siege of Paris begins. The city held out for over four months before surrendering.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)\" class=\"mw-redirect\" title=\"Siege of Paris (1870-71)\">siege of Paris</a> begins. The city held out for over four months before surrendering.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)\" class=\"mw-redirect\" title=\"Siege of Paris (1870-71)\">siege of Paris</a> begins. The city held out for over four months before surrendering.", "links": [{"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}, {"title": "Siege of Paris (1870-71)", "link": "https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)"}]}, {"year": "1893", "text": "In New Zealand, the Electoral Act of 1893 is consented to by the governor, giving all women in New Zealand the right to vote.", "html": "1893 - In New Zealand, the <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_New_Zealand\" title=\"Women's suffrage in New Zealand\">Electoral Act of 1893</a> is consented to by the governor, giving all women in New Zealand the right to vote.", "no_year_html": "In New Zealand, the <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_New_Zealand\" title=\"Women's suffrage in New Zealand\">Electoral Act of 1893</a> is consented to by the governor, giving all women in New Zealand the right to vote.", "links": [{"title": "Women's suffrage in New Zealand", "link": "https://wikipedia.org/wiki/Women%27s_suffrage_in_New_Zealand"}]}, {"year": "1902", "text": "A stampede at Shiloh Baptist Church in Birmingham, Alabama, leads to the death of 115 attendees.", "html": "1902 - A <a href=\"https://wikipedia.org/wiki/Shiloh_Baptist_Church_stampede\" title=\"Shiloh Baptist Church stampede\">stampede at Shiloh Baptist Church</a> in Birmingham, Alabama, leads to the death of 115 attendees.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Shiloh_Baptist_Church_stampede\" title=\"Shiloh Baptist Church stampede\">stampede at Shiloh Baptist Church</a> in Birmingham, Alabama, leads to the death of 115 attendees.", "links": [{"title": "Shiloh Baptist Church stampede", "link": "https://wikipedia.org/wiki/Shiloh_Baptist_Church_stampede"}]}, {"year": "1916", "text": "World War I: During the East African Campaign, colonial forces of the Belgian Congo (Force Publique) under the command of <PERSON> capture the town of Tabora after heavy fighting.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: During the East African Campaign, colonial forces of the Belgian Congo (Force Publique) under the command of <PERSON> <a href=\"https://wikipedia.org/wiki/Battle_of_Tabora\" title=\"Battle of Tabora\">capture the town of Tabora</a> after heavy fighting.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: During the East African Campaign, colonial forces of the Belgian Congo (Force Publique) under the command of <PERSON> <a href=\"https://wikipedia.org/wiki/Battle_of_Tabora\" title=\"Battle of Tabora\">capture the town of Tabora</a> after heavy fighting.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Tabora", "link": "https://wikipedia.org/wiki/Battle_of_Tabora"}]}, {"year": "1939", "text": "World War II: The Battle of Kępa Oksywska concludes, with Polish losses reaching roughly 14% of all the forces engaged.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_K%C4%99pa_Oksywska\" title=\"Battle of Kępa Oksywska\">Battle of Kępa Oksywska</a> concludes, with Polish losses reaching roughly 14% of all the forces engaged.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_K%C4%99pa_Oksywska\" title=\"Battle of Kępa Oksywska\">Battle of Kępa Oksywska</a> concludes, with Polish losses reaching roughly 14% of all the forces engaged.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Kępa Oksywska", "link": "https://wikipedia.org/wiki/Battle_of_K%C4%99pa_Oksywska"}]}, {"year": "1940", "text": "World War II: <PERSON><PERSON><PERSON> is voluntarily captured and sent to Auschwitz concentration camp to gather and smuggle out information for the resistance movement.", "html": "1940 - World War II: <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">W<PERSON><PERSON></a> is voluntarily captured and sent to <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a> to gather and smuggle out information for the resistance movement.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is voluntarily captured and sent to <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a> to gather and smuggle out information for the resistance movement.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}]}, {"year": "1944", "text": "World War II: The Battle of Hürtgen Forest begins. It will become the second-longest individual battle that the U.S. Army has ever fought.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_H%C3%BCrtgen_Forest\" title=\"Battle of Hürtgen Forest\">Battle of Hürtgen Forest</a> begins. It will become the second-longest individual battle that the U.S. Army has ever fought.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_H%C3%BCrtgen_Forest\" title=\"Battle of Hürtgen Forest\">Battle of Hürtgen Forest</a> begins. It will become the second-longest individual battle that the U.S. Army has ever fought.", "links": [{"title": "Battle of Hürtgen Forest", "link": "https://wikipedia.org/wiki/Battle_of_H%C3%BCrtgen_Forest"}]}, {"year": "1944", "text": "World War II: The Moscow Armistice between Finland and the Soviet Union is signed, which officially ended the Continuation War.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Moscow_Armistice\" title=\"Moscow Armistice\">Moscow Armistice</a> between Finland and the Soviet Union is signed, which officially ended the <a href=\"https://wikipedia.org/wiki/Continuation_War\" title=\"Continuation War\">Continuation War</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Moscow_Armistice\" title=\"Moscow Armistice\">Moscow Armistice</a> between Finland and the Soviet Union is signed, which officially ended the <a href=\"https://wikipedia.org/wiki/Continuation_War\" title=\"Continuation War\">Continuation War</a>.", "links": [{"title": "Moscow Armistice", "link": "https://wikipedia.org/wiki/Moscow_Armistice"}, {"title": "Continuation War", "link": "https://wikipedia.org/wiki/Continuation_War"}]}, {"year": "1946", "text": "The Council of Europe is founded following a speech by <PERSON> at the University of Zurich.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Council_of_Europe\" title=\"Council of Europe\">Council of Europe</a> is founded following a speech by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the University of Zurich.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Council_of_Europe\" title=\"Council of Europe\">Council of Europe</a> is founded following a speech by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the University of Zurich.", "links": [{"title": "Council of Europe", "link": "https://wikipedia.org/wiki/Council_of_Europe"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1950", "text": "Korean War: An attack by North Korean forces was repelled at the Battle of Nam River.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: An attack by <a href=\"https://wikipedia.org/wiki/Korean_People%27s_Army\" title=\"Korean People's Army\">North Korean forces</a> was repelled at the <a href=\"https://wikipedia.org/wiki/Battle_of_Nam_River\" title=\"Battle of Nam River\">Battle of Nam River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: An attack by <a href=\"https://wikipedia.org/wiki/Korean_People%27s_Army\" title=\"Korean People's Army\">North Korean forces</a> was repelled at the <a href=\"https://wikipedia.org/wiki/Battle_of_Nam_River\" title=\"Battle of Nam River\">Battle of Nam River</a>.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Korean People's Army", "link": "https://wikipedia.org/wiki/Korean_People%27s_Army"}, {"title": "Battle of Nam River", "link": "https://wikipedia.org/wiki/Battle_of_Nam_River"}]}, {"year": "1960", "text": "Indian Prime Minister <PERSON><PERSON><PERSON><PERSON> and Pakistani President <PERSON><PERSON><PERSON> sign the Indus Waters Treaty for the control and management of the Indus, Chenab, Jhelum, Ravi, Sutlej and Beas rivers.", "html": "1960 - Indian Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and Pakistani President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Indus_Waters_Treaty\" title=\"Indus Waters Treaty\">Indus Waters Treaty</a> for the control and management of the <a href=\"https://wikipedia.org/wiki/Indus\" class=\"mw-redirect\" title=\"Indus\">Indus</a>, <a href=\"https://wikipedia.org/wiki/Chenab\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Jhelum_River\" title=\"Jhelum River\">Jhelum</a>, <a href=\"https://wikipedia.org/wiki/Ravi_River\" title=\"Ravi River\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sutlej\" title=\"Sutlej\">Sutlej</a> and <a href=\"https://wikipedia.org/wiki/Beas_River\" title=\"Beas River\">Beas</a> rivers.", "no_year_html": "Indian Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and Pakistani President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Indus_Waters_Treaty\" title=\"Indus Waters Treaty\">Indus Waters Treaty</a> for the control and management of the <a href=\"https://wikipedia.org/wiki/Indus\" class=\"mw-redirect\" title=\"Indus\">Indus</a>, <a href=\"https://wikipedia.org/wiki/Chenab\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Jhelum_River\" title=\"Jhelum River\">Jhelum</a>, <a href=\"https://wikipedia.org/wiki/Ravi_River\" title=\"Ravi River\">Ravi</a>, <a href=\"https://wikipedia.org/wiki/Sutlej\" title=\"Sutlej\">Sutlej</a> and <a href=\"https://wikipedia.org/wiki/Beas_River\" title=\"Beas River\">Beas</a> rivers.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Indus Waters Treaty", "link": "https://wikipedia.org/wiki/Indus_Waters_Treaty"}, {"title": "Indus", "link": "https://wikipedia.org/wiki/Indus"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ab"}, {"title": "Jhelum River", "link": "https://wikipedia.org/wiki/Jhelum_River"}, {"title": "Ravi River", "link": "https://wikipedia.org/wiki/Ravi_River"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sutlej"}, {"title": "Beas River", "link": "https://wikipedia.org/wiki/Beas_River"}]}, {"year": "1970", "text": "<PERSON> hosts the first Glastonbury Festival.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> hosts the first Glastonbury Festival.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> hosts the first Glastonbury Festival.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, a Greek student of geology, sets himself ablaze in Matteotti Square in Genoa, Italy, as a protest against the dictatorial regime of <PERSON><PERSON>.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a Greek student of geology, sets himself ablaze in Matteotti Square in Genoa, Italy, as a protest against the dictatorial regime of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a Greek student of geology, sets himself ablaze in Matteotti Square in Genoa, Italy, as a protest against the dictatorial regime of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "Turkish Airlines Flight 452 hits the Taurus Mountains, outskirt of Karatepe, Turkey, killing all 154 passengers and crew.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_452\" title=\"Turkish Airlines Flight 452\">Turkish Airlines Flight 452</a> hits the Taurus Mountains, outskirt of Karatepe, Turkey, killing all 154 passengers and crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_452\" title=\"Turkish Airlines Flight 452\">Turkish Airlines Flight 452</a> hits the Taurus Mountains, outskirt of Karatepe, Turkey, killing all 154 passengers and crew.", "links": [{"title": "Turkish Airlines Flight 452", "link": "https://wikipedia.org/wiki/Turkish_Airlines_Flight_452"}]}, {"year": "1976", "text": "Two Imperial Iranian Air Force F-4 Phantom II jets fly out to investigate an unidentified flying object.", "html": "1976 - Two Imperial Iranian Air Force F-4 Phantom II jets fly out to <a href=\"https://wikipedia.org/wiki/1976_Tehran_UFO_incident\" title=\"1976 Tehran UFO incident\">investigate an unidentified flying object</a>.", "no_year_html": "Two Imperial Iranian Air Force F-4 Phantom II jets fly out to <a href=\"https://wikipedia.org/wiki/1976_Tehran_UFO_incident\" title=\"1976 Tehran UFO incident\">investigate an unidentified flying object</a>.", "links": [{"title": "1976 Tehran UFO incident", "link": "https://wikipedia.org/wiki/1976_Tehran_UFO_incident"}]}, {"year": "1978", "text": "The Solomon Islands join the United Nations.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a> join the United Nations.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a> join the United Nations.", "links": [{"title": "Solomon Islands", "link": "https://wikipedia.org/wiki/Solomon_Islands"}]}, {"year": "1982", "text": "<PERSON> posts the first documented emoticons :-) and :-( on the Carnegie Mellon University bulletin board system.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> posts the first documented emoticons :-) and :-( on the Carnegie Mellon University bulletin board system.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> posts the first documented emoticons :-) and :-( on the Carnegie Mellon University bulletin board system.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "Saint Kitts and Nevis gains its independence.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Saint_Kitts_and_Nevis\" title=\"Saint Kitts and Nevis\">Saint Kitts and Nevis</a> gains its independence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Kitts_and_Nevis\" title=\"Saint Kitts and Nevis\">Saint Kitts and Nevis</a> gains its independence.", "links": [{"title": "Saint Kitts and Nevis", "link": "https://wikipedia.org/wiki/<PERSON>_Kitts_and_<PERSON>evis"}]}, {"year": "1985", "text": "A strong earthquake kills thousands and destroys about 400 buildings in Mexico City.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/1985_Mexico_City_earthquake\" title=\"1985 Mexico City earthquake\">A strong earthquake</a> kills thousands and destroys about 400 buildings in Mexico City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1985_Mexico_City_earthquake\" title=\"1985 Mexico City earthquake\">A strong earthquake</a> kills thousands and destroys about 400 buildings in Mexico City.", "links": [{"title": "1985 Mexico City earthquake", "link": "https://wikipedia.org/wiki/1985_Mexico_City_earthquake"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON> and other political wives form the Parents Music Resource Center as <PERSON>, <PERSON>, and other musicians testify at U.S. Congressional hearings on obscenity in rock music.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tip<PERSON>\"><PERSON><PERSON><PERSON></a> and other political wives form the <a href=\"https://wikipedia.org/wiki/Parents_Music_Resource_Center\" title=\"Parents Music Resource Center\">Parents Music Resource Center</a> as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/John_Denver\" title=\"John <PERSON>\"><PERSON></a>, and other musicians testify at U.S. Congressional hearings on obscenity in rock music.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ip<PERSON>\"><PERSON><PERSON><PERSON></a> and other political wives form the <a href=\"https://wikipedia.org/wiki/Parents_Music_Resource_Center\" title=\"Parents Music Resource Center\">Parents Music Resource Center</a> as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/John_Denver\" title=\"<PERSON>\"><PERSON></a>, and other musicians testify at U.S. Congressional hearings on obscenity in rock music.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}, {"title": "Parents Music Resource Center", "link": "https://wikipedia.org/wiki/Parents_Music_Resource_Center"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>"}]}, {"year": "1989", "text": "A bomb destroys UTA Flight 772 in mid-air above the Tùnùrù Desert, Niger, killing all 170 passengers and crew.", "html": "1989 - A bomb destroys <a href=\"https://wikipedia.org/wiki/UTA_Flight_772\" title=\"UTA Flight 772\">UTA Flight 772</a> in mid-air above the Tùnùrù Desert, Niger, killing all 170 passengers and crew.", "no_year_html": "A bomb destroys <a href=\"https://wikipedia.org/wiki/UTA_Flight_772\" title=\"UTA Flight 772\">UTA Flight 772</a> in mid-air above the Tùnùrù Desert, Niger, killing all 170 passengers and crew.", "links": [{"title": "UTA Flight 772", "link": "https://wikipedia.org/wiki/UTA_Flight_772"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON> the Iceman is discovered in the Alps on the border between Italy and Austria.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/%C3%96tzi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> the Iceman</a> is discovered in the Alps on the border between Italy and Austria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96tzi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> the Iceman</a> is discovered in the Alps on the border between Italy and Austria.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96tzi"}]}, {"year": "1995", "text": "The Washington Post and The New York Times publish the Unabomber manifesto.", "html": "1995 - <i>The Washington Post</i> and <i>The New York Times</i> publish the <a href=\"https://wikipedia.org/wiki/Unabomber_manifesto\" class=\"mw-redirect\" title=\"Unabomber manifesto\">Unabomber manifesto</a>.", "no_year_html": "<i>The Washington Post</i> and <i>The New York Times</i> publish the <a href=\"https://wikipedia.org/wiki/Unabomber_manifesto\" class=\"mw-redirect\" title=\"Unabomber manifesto\">Unabomber manifesto</a>.", "links": [{"title": "Unabomber manifesto", "link": "https://wikipedia.org/wiki/Unabomber_manifesto"}]}, {"year": "1997", "text": "The Guelb El-Kebir massacre in Algeria kills 53 people.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Guelb_El-Kebir_massacre\" title=\"Guelb El-Kebir massacre\">Guelb El-Kebir massacre</a> in Algeria kills 53 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Guelb_El-Kebir_massacre\" title=\"Guelb El-Kebir massacre\">Guelb El-Kebir massacre</a> in Algeria kills 53 people.", "links": [{"title": "Guelb El-Kebir massacre", "link": "https://wikipedia.org/wiki/Guelb_El-Kebir_massacre"}]}, {"year": "2006", "text": "The Thai army stages a coup. The Constitution is revoked and martial law is declared.", "html": "2006 - The Thai army <a href=\"https://wikipedia.org/wiki/2006_Thai_coup_d%27%C3%A9tat\" title=\"2006 Thai coup d'état\">stages a coup</a>. The Constitution is revoked and martial law is declared.", "no_year_html": "The Thai army <a href=\"https://wikipedia.org/wiki/2006_Thai_coup_d%27%C3%A9tat\" title=\"2006 Thai coup d'état\">stages a coup</a>. The Constitution is revoked and martial law is declared.", "links": [{"title": "2006 Thai coup d'état", "link": "https://wikipedia.org/wiki/2006_Thai_coup_d%27%C3%A9tat"}]}, {"year": "2008", "text": "A Learjet 60 carrying musicians <PERSON> and <PERSON> \"DJ <PERSON>\" <PERSON> crashes during a rejected takeoff from Colombia Metropolitan Airport in West Columbia, South Carolina, killing four of the six people on board. <PERSON> and <PERSON><PERSON> both survive.", "html": "2008 - A <a href=\"https://wikipedia.org/wiki/Learjet_60\" title=\"Learjet 60\">Learjet 60</a> carrying musicians <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/DJ_AM\" title=\"DJ AM\">Adam \"DJ AM\" <PERSON></a> <a href=\"https://wikipedia.org/wiki/2008_South_Carolina_Learjet_60_crash\" title=\"2008 South Carolina Learjet 60 crash\">crashes</a> during a <a href=\"https://wikipedia.org/wiki/Rejected_takeoff\" title=\"Rejected takeoff\">rejected takeoff</a> from <a href=\"https://wikipedia.org/wiki/Columbia_Metropolitan_Airport\" title=\"Columbia Metropolitan Airport\">Colombia Metropolitan Airport</a> in <a href=\"https://wikipedia.org/wiki/West_Columbia,_South_Carolina\" title=\"West Columbia, South Carolina\">West Columbia, South Carolina</a>, killing four of the six people on board. <PERSON> and <PERSON><PERSON> both survive.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Learjet_60\" title=\"Learjet 60\">Learjet 60</a> carrying musicians <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/DJ_AM\" title=\"DJ AM\"><PERSON> \"DJ AM\" <PERSON></a> <a href=\"https://wikipedia.org/wiki/2008_South_Carolina_Learjet_60_crash\" title=\"2008 South Carolina Learjet 60 crash\">crashes</a> during a <a href=\"https://wikipedia.org/wiki/Rejected_takeoff\" title=\"Rejected takeoff\">rejected takeoff</a> from <a href=\"https://wikipedia.org/wiki/Columbia_Metropolitan_Airport\" title=\"Columbia Metropolitan Airport\">Colombia Metropolitan Airport</a> in <a href=\"https://wikipedia.org/wiki/West_Columbia,_South_Carolina\" title=\"West Columbia, South Carolina\">West Columbia, South Carolina</a>, killing four of the six people on board. <PERSON> and <PERSON><PERSON> both survive.", "links": [{"title": "Learjet 60", "link": "https://wikipedia.org/wiki/Learjet_60"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "DJ AM", "link": "https://wikipedia.org/wiki/DJ_AM"}, {"title": "2008 South Carolina Learjet 60 crash", "link": "https://wikipedia.org/wiki/2008_South_Carolina_Learjet_60_crash"}, {"title": "Rejected takeoff", "link": "https://wikipedia.org/wiki/Rejected_takeoff"}, {"title": "Columbia Metropolitan Airport", "link": "https://wikipedia.org/wiki/Columbia_Metropolitan_Airport"}, {"title": "West Columbia, South Carolina", "link": "https://wikipedia.org/wiki/West_Columbia,_South_Carolina"}]}, {"year": "2010", "text": "The leaking oil well in the Deepwater Horizon oil spill is sealed.", "html": "2010 - The leaking oil well in the <a href=\"https://wikipedia.org/wiki/Deepwater_Horizon_oil_spill\" title=\"Deepwater Horizon oil spill\"><i>Deepwater Horizon</i> oil spill</a> is sealed.", "no_year_html": "The leaking oil well in the <a href=\"https://wikipedia.org/wiki/Deepwater_Horizon_oil_spill\" title=\"Deepwater Horizon oil spill\"><i>Deepwater Horizon</i> oil spill</a> is sealed.", "links": [{"title": "Deepwater Horizon oil spill", "link": "https://wikipedia.org/wiki/Deepwater_Horizon_oil_spill"}]}, {"year": "2011", "text": "<PERSON> of the New York Yankees surpasses <PERSON> to become Major League Baseball's all-time career saves leader with 602.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the New York Yankees surpasses <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to become Major League Baseball's all-time career saves leader with 602.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the New York Yankees surpasses <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to become Major League Baseball's all-time career saves leader with 602.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "In the wake of a manhunt, the suspect in a series of bombings in New York and New Jersey is apprehended after a shootout with police.", "html": "2016 - In the wake of a manhunt, the suspect in <a href=\"https://wikipedia.org/wiki/2016_New_York_and_New_Jersey_bombings\" title=\"2016 New York and New Jersey bombings\">a series of bombings in New York and New Jersey</a> is apprehended after a shootout with police.", "no_year_html": "In the wake of a manhunt, the suspect in <a href=\"https://wikipedia.org/wiki/2016_New_York_and_New_Jersey_bombings\" title=\"2016 New York and New Jersey bombings\">a series of bombings in New York and New Jersey</a> is apprehended after a shootout with police.", "links": [{"title": "2016 New York and New Jersey bombings", "link": "https://wikipedia.org/wiki/2016_New_York_and_New_Jersey_bombings"}]}, {"year": "2017", "text": "The 2017 Puebla earthquake strikes Mexico, causing 370 deaths and over 6,000 injuries, as well as extensive damage.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/2017_Puebla_earthquake\" title=\"2017 Puebla earthquake\">2017 Puebla earthquake</a> strikes Mexico, causing 370 deaths and over 6,000 injuries, as well as extensive damage.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2017_Puebla_earthquake\" title=\"2017 Puebla earthquake\">2017 Puebla earthquake</a> strikes Mexico, causing 370 deaths and over 6,000 injuries, as well as extensive damage.", "links": [{"title": "2017 Puebla earthquake", "link": "https://wikipedia.org/wiki/2017_Puebla_earthquake"}]}, {"year": "2019", "text": "A drone strike by the United States kills 30 civilian farmers in Afghanistan.", "html": "2019 - A drone strike by the United States kills 30 civilian farmers in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "no_year_html": "A drone strike by the United States kills 30 civilian farmers in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "links": [{"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "2021", "text": "The Cumbre Vieja volcano, on the island of La Palma in the Canary Islands, erupts. The eruption lasts for almost three months, ending on December 13.", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/Cumbre_Vieja\" title=\"Cumbre Vieja\">Cumbre Vieja</a> volcano, on the island of <a href=\"https://wikipedia.org/wiki/La_Palma\" title=\"La Palma\">La Palma</a> in the <a href=\"https://wikipedia.org/wiki/Canary_Islands\" title=\"Canary Islands\">Canary Islands</a>, <a href=\"https://wikipedia.org/wiki/2021_Cumbre_Vieja_volcanic_eruption\" title=\"2021 Cumbre Vieja volcanic eruption\">erupts</a>. The eruption lasts for almost three months, ending on December 13.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cumbre_Vieja\" title=\"Cumbre Vieja\">Cumbre Vieja</a> volcano, on the island of <a href=\"https://wikipedia.org/wiki/La_Palma\" title=\"La Palma\">La Palma</a> in the <a href=\"https://wikipedia.org/wiki/Canary_Islands\" title=\"Canary Islands\">Canary Islands</a>, <a href=\"https://wikipedia.org/wiki/2021_Cumbre_Vieja_volcanic_eruption\" title=\"2021 Cumbre Vieja volcanic eruption\">erupts</a>. The eruption lasts for almost three months, ending on December 13.", "links": [{"title": "Cumbre Vieja", "link": "https://wikipedia.org/wiki/Cumbre_Vieja"}, {"title": "La Palma", "link": "https://wikipedia.org/wiki/La_Palma"}, {"title": "Canary Islands", "link": "https://wikipedia.org/wiki/Canary_Islands"}, {"title": "2021 Cumbre Vieja volcanic eruption", "link": "https://wikipedia.org/wiki/2021_Cumbre_Vieja_volcanic_eruption"}]}, {"year": "2022", "text": "The state funeral of Queen <PERSON> of the United Kingdom is held at Westminster Abbey, London.", "html": "2022 - The <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_II\" title=\"Death and state funeral of <PERSON> II\">state funeral</a> of <a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_II\" class=\"mw-redirect\" title=\"Queen Elizabeth II\">Queen Elizabeth II</a> of the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> is held at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_II\" title=\"Death and state funeral of <PERSON> II\">state funeral</a> of <a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_II\" class=\"mw-redirect\" title=\"Queen Elizabeth II\">Queen <PERSON> II</a> of the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> is held at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "links": [{"title": "Death and state funeral of <PERSON> II", "link": "https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_II"}, {"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>_<PERSON>"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}], "Births": [{"year": "86", "text": "<PERSON><PERSON>, Roman emperor (d. 161)", "html": "86 - AD 86 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman emperor (d. 161)", "no_year_html": "AD 86 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman emperor (d. 161)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "866", "text": "<PERSON> the Wise, Byzantine emperor (d. 912)", "html": "866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Wise\" title=\"<PERSON> VI the Wise\"><PERSON> the Wise</a>, Byzantine emperor (d. 912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Wise\" title=\"<PERSON> VI the Wise\"><PERSON> <PERSON> the Wise</a>, Byzantine emperor (d. 912)", "links": [{"title": "<PERSON> the Wise", "link": "https://wikipedia.org/wiki/<PERSON>_VI_the_<PERSON>"}]}, {"year": "931", "text": "<PERSON>, emperor of the Liao Dynasty (d. 969)", "html": "931 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON>zong of Liao\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Liao_dynasty\" title=\"Liao dynasty\">Liao Dynasty</a> (d. 969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON>zong of Liao\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Liao_dynasty\" title=\"Liao dynasty\">Liao Dynasty</a> (d. 969)", "links": [{"title": "Emperor <PERSON><PERSON> of Liao", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao"}, {"title": "Liao dynasty", "link": "https://wikipedia.org/wiki/Liao_dynasty"}]}, {"year": "1377", "text": "<PERSON>, Duke of Austria (d. 1404)[citation needed]", "html": "1377 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (d. 1404)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (d. 1404)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}]}, {"year": "1426", "text": "<PERSON> Cleves, Duchess of Orléans, French noble (d. 1487)[citation needed]", "html": "1426 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_of_Orl%C3%A9ans\" title=\"<PERSON> Cleves, Duchess of Orléans\"><PERSON> Cleves, Duchess of Orléans</a>, French noble (d. 1487)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_of_Orl%C3%A9ans\" title=\"<PERSON> Cleves, Duchess of Orléans\"><PERSON> Cleves, Duchess of Orléans</a>, French noble (d. 1487)", "links": [{"title": "<PERSON> Cleves, Duchess of Orléans", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_<PERSON>_Orl%C3%A9ans"}]}, {"year": "1477", "text": "<PERSON><PERSON><PERSON>, Ferrarese nobleman and condottiero (d. 1540)", "html": "1477 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_d%27Este\" title=\"<PERSON><PERSON><PERSON> d'<PERSON>\"><PERSON><PERSON><PERSON></a>, Ferrarese nobleman and condottiero (d. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_d%27Este\" title=\"<PERSON><PERSON><PERSON> d'<PERSON>\"><PERSON><PERSON><PERSON></a>, Ferrarese nobleman and condottiero (d. 1540)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fe<PERSON><PERSON>_d%27Este"}]}, {"year": "1551", "text": "<PERSON> of France (d. 1589)", "html": "1551 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> III of France\"><PERSON> of France</a> (d. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1589)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1560", "text": "<PERSON>, English naval explorer, led the third expedition to circumnavigate the globe (d. 1592)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English naval explorer, led the third expedition to circumnavigate the globe (d. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English naval explorer, led the third expedition to circumnavigate the globe (d. 1592)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON>, Roman Catholic cardinal and archbishop (d. 1679)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman Catholic cardinal and archbishop (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman Catholic cardinal and archbishop (d. 1679)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfonso_Litta"}]}, {"year": "1638", "text": "<PERSON>, English minister (d. 1720)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister (d. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister (d. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1662", "text": "<PERSON><PERSON><PERSON>, French priest and man of letters (d. 1743)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and man of letters (d. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and man of letters (d. 1743)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, Scottish historian (d. 1793)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, Scottish historian (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, Scottish historian (d. 1793)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_(historian)"}]}, {"year": "1749", "text": "<PERSON>, French mathematician and astronomer (d. 1822)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and astronomer (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and astronomer (d. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, American lieutenant, lawyer, and judge (d. 1821)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and judge (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and judge (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1759", "text": "<PERSON>, English priest and entomologist (d. 1850)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(entomologist)\" title=\"<PERSON> (entomologist)\"><PERSON></a>, English priest and entomologist (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(entomologist)\" title=\"<PERSON> (entomologist)\"><PERSON></a>, English priest and entomologist (d. 1850)", "links": [{"title": "<PERSON> (entomologist)", "link": "https://wikipedia.org/wiki/<PERSON>(entomologist)"}]}, {"year": "1778", "text": "<PERSON>, 1st Baron <PERSON> and <PERSON>, Scottish lawyer and politician, Lord Chancellor of Great Britain (d. 1868)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON>, 1st Baron <PERSON> and <PERSON><PERSON>\"><PERSON>, 1st Baron <PERSON> and <PERSON><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of Great Britain</a> (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON>, 1st Baron <PERSON> and <PERSON><PERSON>\"><PERSON>, 1st Baron <PERSON> and <PERSON><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of Great Britain</a> (d. 1868)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> and <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_and_<PERSON><PERSON>"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1796", "text": "<PERSON>, English poet and author (d. 1849)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hartley Coleridge\"><PERSON></a>, English poet and author (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hartley Coleridge\"><PERSON></a>, English poet and author (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hartley_<PERSON>"}]}, {"year": "1802", "text": "<PERSON><PERSON>, Hungarian journalist, lawyer, and politician, Governor-President of Hungary (d. 1894)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian journalist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Hungary\" title=\"List of heads of state of Hungary\">Governor-President of Hungary</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian journalist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Hungary\" title=\"List of heads of state of Hungary\">Governor-President of Hungary</a> (d. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lajos_<PERSON>uth"}, {"title": "List of heads of state of Hungary", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Hungary"}]}, {"year": "1803", "text": "<PERSON> of Savoy (d. 1884)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a> (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a> (d. 1884)", "links": [{"title": "<PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Savoy"}]}, {"year": "1811", "text": "<PERSON><PERSON>, American mathematician and religious leader (d. 1881)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mathematician and religious leader (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mathematician and religious leader (d. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, American engineer, inventor, and businessperson (d. 1905)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, inventor, and businessperson (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, inventor, and businessperson (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON><PERSON><PERSON>, Swiss judge and politician, President of the Swiss National Council (d. 1880)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss judge and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_National_Council\" class=\"mw-redirect\" title=\"President of the Swiss National Council\">President of the Swiss National Council</a> (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss judge and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_National_Council\" class=\"mw-redirect\" title=\"President of the Swiss National Council\">President of the Swiss National Council</a> (d. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}, {"title": "President of the Swiss National Council", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_National_Council"}]}, {"year": "1856", "text": "<PERSON>, Australian politician, 16th Premier of Queensland (d. 1916)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Queensland politician)\"><PERSON></a>, Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Queensland politician)\"><PERSON></a>, Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1916)", "links": [{"title": "<PERSON> (Queensland politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_politician)"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1865", "text": "<PERSON>, American-German photographer (d. 1936)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-German photographer (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-German photographer (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, English illustrator (d. 1939)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American comedian and actor (d. 1940)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Swiss-American painter (d. 1938)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American painter (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American painter (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, English radio host (d. 1965)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio host (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio host (d. 1965)", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_(broadcaster)"}]}, {"year": "1883", "text": "<PERSON>, American educator and activist (d. 1975)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and activist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and activist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, American pianist, composer, and bandleader (d. 1972)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Lovie_Austin\" title=\"Lovie Austin\"><PERSON><PERSON></a>, American pianist, composer, and bandleader (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lovie_Austin\" title=\"Lovie Austin\"><PERSON><PERSON></a>, American pianist, composer, and bandleader (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lovie_Austin"}]}, {"year": "1887", "text": "<PERSON><PERSON>, American actor and singer  (d. 1943)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American mathematician and topologist (d. 1971)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and topologist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON></a>, American mathematician and topologist (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American actor (d. 1953)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Porter_Hall\" title=\"Porter Hall\"><PERSON></a>, American actor (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Porter_Hall\" title=\"Porter Hall\"><PERSON></a>, American actor (d. 1953)", "links": [{"title": "Porter Hall", "link": "https://wikipedia.org/wiki/Porter_Hall"}]}, {"year": "1889", "text": "<PERSON>, American physician and author (d. 1999)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American author and poet (d. 1942)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rachel Field\"><PERSON></a>, American author and poet (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rachel_<PERSON>\" title=\"Rachel Field\"><PERSON></a>, American author and poet (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rachel_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Italian lawyer and politician, 5th President of Italy (d. 1988)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "1900", "text": "<PERSON>, American actor (d. 1977)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, German World War II resistance fighter (d. 1944)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German World War II resistance fighter (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German World War II resistance fighter (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American lawyer, co-founded Fulbright & Jaworski (d. 1982)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, co-founded <a href=\"https://wikipedia.org/wiki/Fulbright_%26_<PERSON><PERSON><PERSON><PERSON>\" title=\"Fulbright &amp; <PERSON><PERSON><PERSON><PERSON>\">Fulbright &amp; <PERSON><PERSON><PERSON><PERSON></a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, co-founded <a href=\"https://wikipedia.org/wiki/Fulbright_%26_<PERSON><PERSON><PERSON><PERSON>\" title=\"Fulbright &amp; <PERSON><PERSON><PERSON><PERSON>\">Fulbright &amp; <PERSON><PERSON><PERSON><PERSON></a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fulbright & Jaworski", "link": "https://wikipedia.org/wiki/Fulbright_%26_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Jr., American lawyer and jurist (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and jurist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and jurist (d. 1998)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1908", "text": "<PERSON>, French historian, author, and critic (d. 2001)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian, author, and critic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian, author, and critic (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_B%C3%A9nichou"}]}, {"year": "1908", "text": "<PERSON>, French lawyer, judge, and politician, Lord Chancellor of France (d. 2004)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_France\" class=\"mw-redirect\" title=\"Lord Chancellor of France\">Lord Chancellor of France</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_France\" class=\"mw-redirect\" title=\"Lord Chancellor of France\">Lord Chancellor of France</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Chancellor of France", "link": "https://wikipedia.org/wiki/Lord_Chancellor_of_France"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Japanese martial artist, founded <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (d. 1975)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Isshin-ry%C5%AB\" title=\"Isshin-ryū\"><PERSON><PERSON>-ryū</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Isshin-ry%C5%AB\" title=\"Isshin-ryū\"><PERSON><PERSON>-ryū</a> (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Isshin-ry<PERSON>", "link": "https://wikipedia.org/wiki/Isshin-ry%C5%AB"}]}, {"year": "1909", "text": "<PERSON>, Austrian engineer and businessman (d. 1998)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian engineer and businessman (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian engineer and businessman (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actress (d. 1981)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Filipino diplomat and politician (d. 2004)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino diplomat and politician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino diplomat and politician (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arturo_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, British novelist, playwright, and poet, Nobel Prize laureate (d. 1993)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, playwright, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, playwright, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1912", "text": "<PERSON>, Indian veterinarian and zoo founder (d. 1989)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian veterinarian and zoo founder (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian veterinarian and zoo founder (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Polish-German conductor (d. 2011)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German conductor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German conductor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actress (d. 1970)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American singer (d. 1998)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (d. 1998)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Mexican actor, singer, and producer (d. 1973)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Germ%C3%A1n_Vald%C3%A9s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican actor, singer, and producer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Germ%C3%A1n_Vald%C3%A9s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican actor, singer, and producer (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Germ%C3%A1n_Vald%C3%A9s"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON><PERSON>, Santa Clara Pueblo (Native American) painter (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Pablita_Velarde\" title=\"Pablita Velarde\"><PERSON><PERSON><PERSON><PERSON> Vela<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Santa_Clara_Pueblo\" class=\"mw-redirect\" title=\"Santa Clara Pueblo\">Santa Clara Pueblo</a> (Native American) painter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pablit<PERSON>_Velarde\" title=\"Pablita Velarde\"><PERSON><PERSON><PERSON><PERSON> Velarde</a>, <a href=\"https://wikipedia.org/wiki/Santa_Clara_Pueblo\" class=\"mw-redirect\" title=\"Santa Clara Pueblo\">Santa Clara Pueblo</a> (Native American) painter (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>lit<PERSON>_Velarde"}, {"title": "Santa Clara Pueblo", "link": "https://wikipedia.org/wiki/Santa_Clara_Pueblo"}]}, {"year": "1919", "text": "<PERSON>, French journalist and author (d. 2017)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Mexican choreographer and dancer (d. 2000)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hern%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican choreographer and dancer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hern%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican choreographer and dancer (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amalia_Hern%C3%A1ndez"}]}, {"year": "1920", "text": "<PERSON>, American journalist, author, and editor (d. 2022) ", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and editor (d. 2022) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and editor (d. 2022) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Brazilian philosopher, theorist, and academic (d. 1997)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian philosopher, theorist, and academic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian philosopher, theorist, and academic (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American R&B singer-songwriter (d. 2002)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_his_Dominoes\" title=\"<PERSON> and his Dominoes\"><PERSON></a>, American R&amp;B singer-songwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_his_Dominoes\" title=\"<PERSON> and his Dominoes\"><PERSON></a>, American R&amp;B singer-songwriter (d. 2002)", "links": [{"title": "<PERSON> and his <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_his_<PERSON><PERSON><PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American author and critic (d. 2002)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Knight\"><PERSON></a>, American author and critic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American boxer and referee (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and referee (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and referee (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Czech runner (d. 2000)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1topek\" title=\"<PERSON>\"><PERSON></a>, Czech runner (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1topek\" title=\"<PERSON>\"><PERSON></a>, Czech runner (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emil_Z%C3%A1topek"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American baseball player, coach, and manager (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Benson\"><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Benson\"><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Canadian actor and screenwriter (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON> <PERSON>, Jr., American lawyer and academic (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>, Jr.\"><PERSON><PERSON> <PERSON>, Jr.</a>, American lawyer and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>, Jr.\"><PERSON><PERSON> <PERSON>, Jr.</a>, American lawyer and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1926", "text": "<PERSON>, Moldovan animated film director (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Victoria_Barb%C4%83\" title=\"Victoria Barbă\"><PERSON></a>, Moldovan animated film director (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Barb%C4%83\" title=\"Victoria Barbă\"><PERSON></a>, Moldovan animated film director (d. 2020)", "links": [{"title": "Victoria Barbă", "link": "https://wikipedia.org/wiki/Victoria_Barb%C4%83"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Japanese physicist and academic, Nobel Prize laureate  (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1926", "text": "<PERSON>, American actor, producer, and screenwriter (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American baseball player and sportscaster (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American singer (d. 1998)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actress", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor (d. 1997)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1997)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1927", "text": "<PERSON>, American singer and bass player (d. 2000)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor and businessman (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Adam_West\" title=\"Adam West\"><PERSON></a>, American actor and businessman (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adam_West\" title=\"Adam West\"><PERSON></a>, American actor and businessman (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adam_West"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American educator and politician (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American pianist, composer, and educator (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and educator (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and educator (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American photographer and journalist (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer and journalist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer and journalist (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Italian director, producer, and screenwriter (d. 2002)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director, producer, and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director, producer, and screenwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American pop/R&B/rock & roll singer-songwriter (d. 1988)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Brook_Benton\" title=\"Brook Benton\"><PERSON></a>, American pop/R&amp;B/rock &amp; roll singer-songwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brook_Benton\" title=\"Brook Benton\"><PERSON></a>, American pop/R&amp;B/rock &amp; roll singer-songwriter (d. 1988)", "links": [{"title": "<PERSON> Benton", "link": "https://wikipedia.org/wiki/<PERSON>_Benton"}]}, {"year": "1931", "text": "<PERSON>, English engineer (d. 2011)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, English engineer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, English engineer (d. 2011)", "links": [{"title": "<PERSON> (designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)"}]}, {"year": "1932", "text": "<PERSON>, American journalist and author (d. 1997)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, German journalist and author (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German journalist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German journalist and author (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Canadian journalist and author", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Scottish actor (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English businessman, The Beatles manager (d. 1967)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> manager (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> manager (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}]}, {"year": "1934", "text": "<PERSON>, English journalist, academic and politician (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mitchell\" title=\"<PERSON>\"><PERSON></a>, English journalist, academic and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mitchell\" title=\"<PERSON> Mitchell\"><PERSON></a>, English journalist, academic and politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Mitchell"}]}, {"year": "1935", "text": "<PERSON>, American admiral (d. 2003)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>cker\" title=\"<PERSON> Hacker\"><PERSON></a>, American admiral (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>cker\" title=\"<PERSON> Hacker\"><PERSON></a>, American admiral (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>cker"}]}, {"year": "1936", "text": "<PERSON>, Irish fiddler (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish fiddler (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish fiddler (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Canadian ice hockey player (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Milan_Marc<PERSON>\" title=\"Milan Marcetta\"><PERSON></a>, Canadian ice hockey player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Marcetta\" title=\"Milan Marcetta\"><PERSON></a>, Canadian ice hockey player (d. 2014)", "links": [{"title": "Milan <PERSON>", "link": "https://wikipedia.org/wiki/Milan_Marcetta"}]}, {"year": "1936", "text": "<PERSON>, American discus thrower (d. 2007)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American football player (d. 2024)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Hungarian-Australian director, producer, and screenwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, English fashion designer, founded the Fashion and Textile Museum", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English fashion designer, founded the <a href=\"https://wikipedia.org/wiki/Fashion_and_Textile_Museum\" title=\"Fashion and Textile Museum\">Fashion and Textile Museum</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English fashion designer, founded the <a href=\"https://wikipedia.org/wiki/Fashion_and_Textile_Museum\" title=\"Fashion and Textile Museum\">Fashion and Textile Museum</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Fashion and Textile Museum", "link": "https://wikipedia.org/wiki/Fashion_and_Textile_Museum"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter and actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_(songwriter)"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Italian politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bossi\" title=\"<PERSON><PERSON> Bossi\"><PERSON><PERSON></a>, Italian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bossi\" title=\"<PERSON><PERSON> Bossi\"><PERSON><PERSON></a>, Italian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1941", "text": "<PERSON>, American singer (d. 1974)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English pentathlete (d. 2023)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pentathlete)\" title=\"<PERSON> (pentathlete)\"><PERSON></a>, English pentathlete (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pentathlete)\" title=\"<PERSON> (pentathlete)\"><PERSON></a>, English pentathlete (d. 2023)", "links": [{"title": "<PERSON> (pentathlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pentathlete)"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Italian actress (d. 2013)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American singer and actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2019)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Boudrias\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Boudrias\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Boud<PERSON>s"}]}, {"year": "1943", "text": "<PERSON>, American baseball player (d. 2020)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Swedish politician, 25th Swedish Minister of Defence", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j%C3%B6rck\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)\" title=\"Minister for Defence (Sweden)\">Swedish Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j%C3%B6rck\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)\" title=\"Minister for Defence (Sweden)\">Swedish Minister of Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bj%C3%B6rck"}, {"title": "Minister for Defence (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)"}]}, {"year": "1944", "text": "<PERSON>, Faroese politician, 9th Prime Minister of the Faroe Islands", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Faroese politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Faroese politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Turkish poet and scholar", "html": "1944 - <a href=\"https://wikipedia.org/wiki/%C4%B0smet_%C3%96zel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0smet_%C3%96zel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and scholar", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%B0smet_%C3%96zel"}]}, {"year": "1945", "text": "<PERSON>, English journalist and author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American multi-instrumentalist, singer, and songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American multi-instrumentalist, singer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American multi-instrumentalist, singer, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>,  American novelist and screenwriter (d. 2013)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, English musician, songwriter, and music video director", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e\" title=\"Lo<PERSON> Creme\"><PERSON><PERSON></a>, English musician, songwriter, and music video director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e\" title=\"Lo<PERSON> Creme\"><PERSON><PERSON></a>, English musician, songwriter, and music video director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>l_Creme"}]}, {"year": "1947", "text": "<PERSON><PERSON>, English author (d. 2015)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American basketball player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian footballer and manager (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Irons\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, English model, actress, and singer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Twiggy\" title=\"Twiggy\">Twiggy</a>, English model, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Twiggy\" title=\"Twiggy\">Twiggy</a>, English model, actress, and singer", "links": [{"title": "Twiggy", "link": "https://wikipedia.org/wiki/Twiggy"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Mexican wrestler", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American lawyer, co-founded the Innocence Project", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, co-founded the <a href=\"https://wikipedia.org/wiki/Innocence_Project\" title=\"Innocence Project\">Innocence Project</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, co-founded the <a href=\"https://wikipedia.org/wiki/Innocence_Project\" title=\"Innocence Project\">Innocence Project</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Innocence Project", "link": "https://wikipedia.org/wiki/Innocence_Project"}]}, {"year": "1949", "text": "<PERSON>, American basketball player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> W<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wicks\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sidney_Wicks"}]}, {"year": "1950", "text": "<PERSON>, American television journalist,  anchor, and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist, anchor, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist, anchor, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English physicist, mathematician, and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)\" title=\"<PERSON> (academic)\"><PERSON></a>, English physicist, mathematician, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)\" title=\"<PERSON> (academic)\"><PERSON></a>, English physicist, mathematician, and academic", "links": [{"title": "<PERSON> (academic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)"}]}, {"year": "1951", "text": "<PERSON>, Canadian singer-songwriter, guitarist, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American trumpet player, guitarist, and composer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, guitarist, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, guitarist, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American guitarist and composer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and composer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1952", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rodgers\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American businessman (d. 2007)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian cricketer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American singer-songwriter and violinist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Sara<PERSON>_VerLin\" title=\"Sarana VerLin\"><PERSON><PERSON></a>, American singer-songwriter and violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sara<PERSON>_VerLin\" title=\"<PERSON>na VerLin\"><PERSON><PERSON></a>, American singer-songwriter and violinist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sarana_VerLin"}]}, {"year": "1954", "text": "<PERSON>, Welsh psychotherapist and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, Welsh psychotherapist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, Welsh psychotherapist and author", "links": [{"title": "<PERSON> (psychologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Greek singer-songwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>li"}]}, {"year": "1955", "text": "<PERSON>, American composer and engineer (d. 2006)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and engineer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and engineer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American football player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American basketball player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, English-American singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ford\" title=\"Lita Ford\"><PERSON><PERSON></a>, English-American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ford\" title=\"Lita Ford\"><PERSON><PERSON></a>, English-American singer-songwriter and guitarist", "links": [{"title": "Lita Ford", "link": "https://wikipedia.org/wiki/Lita_Ford"}]}, {"year": "1958", "text": "<PERSON>, American actor, director, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American chef and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, French aerodynamicist and engineer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Lo%C3%AFc_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French aerodynamicist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lo%C3%AFc_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French aerodynamicist and engineer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lo%C3%AFc_<PERSON>ois"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, American convicted murderer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Yo<PERSON><PERSON>_Sald%C3%ADvar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American convicted murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d%C3%ADvar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American convicted murderer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a_Sald%C3%ADvar"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Polish-British physicist and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>ur_<PERSON>\" title=\"<PERSON>ur E<PERSON>t\"><PERSON><PERSON></a>, Polish-British physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ur_<PERSON>\" title=\"Artur Ekert\"><PERSON><PERSON></a>, Polish-British physicist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ur_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American baseball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress, comedian, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>eri_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>eri_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, comedian, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cheri_<PERSON><PERSON>i"}]}, {"year": "1962", "text": "<PERSON>, American sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Co<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cocker\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English footballer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Estonian biologist and photographer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Urmas_Tartes\" title=\"Urma<PERSON> Tartes\"><PERSON><PERSON><PERSON></a>, Estonian biologist and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_Tartes\" title=\"Urma<PERSON> Tartes\"><PERSON><PERSON><PERSON></a>, Estonian biologist and photographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1964", "text": "<PERSON>, English actor, director, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wood\" title=\"Trisha Yearwood\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wood\" title=\"Trisha Yearwood\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Yearwood"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American captain, pilot, and astronaut", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American captain, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American captain, pilot, and astronaut", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American journalist and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Soledad_O%27Brien\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soledad_O%27Brien\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Soledad_O%27Brien"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler and mixed martial artist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler and mixed martial artist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Russian wrestler and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian wrestler and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian wrestler and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Dutch saxophonist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Du<PERSON>\"><PERSON></a>, Dutch saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dulf<PERSON>\"><PERSON></a>, Dutch saxophonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Polish footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%85<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%85<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jacek_Fr%C4%85<PERSON><PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Cypriot singer-songwriter and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American chef and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Russian-Australian boxer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tszyu\" title=\"<PERSON><PERSON><PERSON> Tszyu\"><PERSON><PERSON><PERSON></a>, Russian-Australian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tszyu\" title=\"<PERSON><PERSON><PERSON> Tszyu\"><PERSON><PERSON><PERSON></a>, Russian-Australian boxer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yu"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Finnish singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ta<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> W<PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ta<PERSON>_<PERSON>\" title=\"Tapio Wilska\"><PERSON><PERSON></a>, Finnish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ka"}]}, {"year": "1970", "text": "<PERSON>, Canadian ice hockey player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, German footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hey\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, German footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Armenian chess player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Ashot_Nadanian\" title=\"Ashot Nadanian\"><PERSON><PERSON></a>, Armenian chess player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashot_Nadanian\" title=\"Ashot Nadanian\"><PERSON><PERSON></a>, Armenian chess player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ashot_Nadanian"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Australian politician, 49th Premier of Victoria", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Australian politician, 49th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Australian politician, 49th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1973", "text": "<PERSON>, Irish footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Brazilian racing driver", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Mexican politician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Mexican actor, model and singer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actor, model and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actor, model and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American comedian and talk show host", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Japanese video game designer and executive", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>det<PERSON>_<PERSON>\" title=\"<PERSON>det<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese video game designer and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>det<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese video game designer and executive", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American director and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Czech ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Jan_<PERSON>%C3%A1%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_<PERSON>%C3%A1%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_Hlav%C3%A1%C4%8D"}]}, {"year": "1976", "text": "<PERSON>, American actress and television host", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Belarusian footballer and referee", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian footballer and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Hong Kong footballer and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American musician and record producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American musician and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American musician and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(2000s_pitcher)\" title=\"<PERSON> (2000s pitcher)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(2000s_pitcher)\" title=\"<PERSON> (2000s pitcher)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (2000s pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(2000s_pitcher)"}]}, {"year": "1977", "text": "<PERSON>, Israeli chess player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Spanish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3pez_Monta%C3%B1a\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3pez_Monta%C3%B1a\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jorge_L%C3%B3pez_Monta%C3%B1a"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Swedish ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON> <PERSON><PERSON>, American-Bosnian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American-Bosnian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American-Bosnian basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English motorcycle racer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcycle_racer)\" class=\"mw-redirect\" title=\"<PERSON> (motorcycle racer)\"><PERSON></a>, English motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcycle_racer)\" class=\"mw-redirect\" title=\"<PERSON> (motorcycle racer)\"><PERSON></a>, English motorcycle racer", "links": [{"title": "<PERSON> (motorcycle racer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(motorcycle_racer)"}]}, {"year": "1980", "text": "<PERSON>, French rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Italian cyclist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Portuguese footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Greek tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor and choreographer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Columbus_Short\" title=\"Columbus Short\"><PERSON></a>, American actor and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Columbus_Short\" title=\"Columbus Short\"><PERSON></a>, American actor and choreographer", "links": [{"title": "Columbus Short", "link": "https://wikipedia.org/wiki/Columbus_Short"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American singer and songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Austrian politician", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Joni_Pitk%C3%A4nen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jon<PERSON>_<PERSON>k%C3%A4nen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joni_Pitk%C3%A4nen"}]}, {"year": "1984", "text": "<PERSON>, American wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Mexican footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/%C3%81ngel_<PERSON>na\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81ngel_<PERSON>na\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%81ngel_<PERSON>na"}]}, {"year": "1984", "text": "<PERSON>, Canadian actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Welsh rugby player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, South Korean actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ki\" title=\"<PERSON> Joong-ki\"><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ki\" title=\"<PERSON> Joong-ki\"><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON>-ki", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ki"}]}, {"year": "1985", "text": "<PERSON><PERSON>, German politician", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian-American television personality", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leon Best\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leon Best\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian athlete", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, French footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Colombian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Katrina_Bowden"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Japanese actress and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Sav<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r\" title=\"<PERSON><PERSON>pier\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Trippier\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>c<PERSON>oll<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>c<PERSON>oll<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1992", "text": "<PERSON>, Mexican footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, American record producer and rapper", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Pi%27erre_<PERSON>\" title=\"<PERSON>'er<PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American record producer and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pi%27erre_<PERSON>\" title=\"<PERSON>'er<PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American record producer and rapper", "links": [{"title": "<PERSON><PERSON>erre <PERSON>", "link": "https://wikipedia.org/wiki/Pi%27erre_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American singer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Brent_Faiyaz\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brent_Faiyaz\" title=\"<PERSON> F<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brent_Faiyaz"}]}, {"year": "1995", "text": "<PERSON>, American actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian-American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American singer, songwriter, model, and actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter, model, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter, model, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Dejou<PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Gabonese basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gabonese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gabonese basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Canadian ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rae Young\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rae Young\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Nigerian basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Precious_A<PERSON>uwa\" title=\"Precious Achiuwa\">Pre<PERSON> <PERSON><PERSON><PERSON></a>, Nigerian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Precious_A<PERSON><PERSON>wa\" title=\"Precious Achiuwa\">Precious <PERSON><PERSON><PERSON><PERSON></a>, Nigerian basketball player", "links": [{"title": "Precious Achiuwa", "link": "https://wikipedia.org/wiki/Precious_<PERSON><PERSON><PERSON>wa"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Indonesian advocate, model, and influencer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian advocate, model, and influencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian advocate, model, and influencer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>h"}]}], "Deaths": [{"year": "643", "text": "<PERSON><PERSON> of Metz, Frankish bishop and saint", "html": "643 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Metz\" title=\"<PERSON><PERSON> of Metz\"><PERSON><PERSON> of Metz</a>, Frankish bishop and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Metz\" title=\"<PERSON><PERSON> of Metz\"><PERSON><PERSON> of Metz</a>, Frankish bishop and saint", "links": [{"title": "<PERSON><PERSON> of Metz", "link": "https://wikipedia.org/wiki/Goeric_of_Metz"}]}, {"year": "690", "text": "<PERSON> of Tarsus, English archbishop and saint (b. 602)", "html": "690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tarsus\" title=\"<PERSON> of Tarsus\"><PERSON> of Tarsus</a>, English archbishop and saint (b. 602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tarsus\" title=\"<PERSON> of Tarsus\"><PERSON> of Tarsus</a>, English archbishop and saint (b. 602)", "links": [{"title": "<PERSON> of Tarsus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tarsus"}]}, {"year": "961", "text": "<PERSON>, Byzantine empress", "html": "961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine empress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine empress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "979", "text": "<PERSON><PERSON><PERSON>, archbishop of Milan", "html": "979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(archbishop_of_Milan)\" title=\"<PERSON><PERSON><PERSON> (archbishop of Milan)\"><PERSON><PERSON><PERSON></a>, archbishop of Milan", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(archbishop_of_Milan)\" title=\"<PERSON><PERSON><PERSON> (archbishop of Milan)\"><PERSON><PERSON><PERSON> I</a>, archbishop of Milan", "links": [{"title": "<PERSON><PERSON><PERSON> (archbishop of Milan)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(archbishop_of_Milan)"}]}, {"year": "1123", "text": "Emperor <PERSON><PERSON> of Jin (b. 1068)", "html": "1123 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON> of Jin</a> (b. 1068)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON> of Jin</a> (b. 1068)", "links": [{"title": "Emperor <PERSON><PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin"}]}, {"year": "1147", "text": "<PERSON> of Kiev", "html": "1147 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Kiev\" title=\"<PERSON> II of Kiev\"><PERSON> of Kiev</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Kiev\" title=\"<PERSON> II of Kiev\"><PERSON> of Kiev</a>", "links": [{"title": "<PERSON> of Kiev", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Kiev"}]}, {"year": "1339", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (b. 1288)", "html": "1339 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Daigo\" title=\"Emperor Go-Daigo\">Emperor <PERSON><PERSON>Daigo</a> of Japan (b. 1288)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Daigo\" title=\"Emperor Go-Daigo\">Emperor <PERSON><PERSON>Dai<PERSON></a> of Japan (b. 1288)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>"}]}, {"year": "1356", "text": "<PERSON>, Duke of Bourbon (b. 1311)", "html": "1356 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a> (b. 1311)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a> (b. 1311)", "links": [{"title": "<PERSON>, Duke of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_Bourbon"}]}, {"year": "1356", "text": "<PERSON>, Count of Brienne (b. 1304)", "html": "1356 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Brienne\" title=\"<PERSON>, Count of Brienne\"><PERSON>, Count of Brienne</a> (b. 1304)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Brienne\" title=\"<PERSON>, Count of Brienne\"><PERSON>, Count of Brienne</a> (b. 1304)", "links": [{"title": "<PERSON>, Count of Brienne", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1580", "text": "<PERSON>, Duchess of Suffolk, English noblewoman (b. 1519)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Suffolk\" class=\"mw-redirect\" title=\"<PERSON>, Duchess of Suffolk\"><PERSON>, Duchess of Suffolk</a>, English noblewoman (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Suffolk\" class=\"mw-redirect\" title=\"<PERSON>, Duchess of Suffolk\"><PERSON>, Duchess of Suffolk</a>, English noblewoman (b. 1519)", "links": [{"title": "<PERSON>, Duchess of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Suffolk"}]}, {"year": "1589", "text": "<PERSON><PERSON><PERSON>, French poet (b. 1532)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%AFf\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%AFf\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet (b. 1532)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%AFf"}]}, {"year": "1605", "text": "<PERSON>, English politician (b. 1542)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1605)\" title=\"<PERSON> (died 1605)\"><PERSON></a>, English politician (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1605)\" title=\"<PERSON> (died 1605)\"><PERSON></a>, English politician (b. 1542)", "links": [{"title": "<PERSON> (died 1605)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1605)"}]}, {"year": "1668", "text": "<PERSON>, English general and politician (b. 1597)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (b. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (b. 1597)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1692", "text": "<PERSON>, American farmer and accused wizard (b. c. 1612)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and accused wizard (b. c. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and accused wizard (b. c. 1612)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, Danish astronomer and instrument maker (b. 1644)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/Ole_R%C3%B8mer\" title=\"<PERSON>\"><PERSON></a>, Danish astronomer and instrument maker (b. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ole_R%C3%B8mer\" title=\"<PERSON>\"><PERSON></a>, Danish astronomer and instrument maker (b. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ole_R%C3%B8mer"}]}, {"year": "1812", "text": "<PERSON>, German banker (b. 1744)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German banker (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German banker (b. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French mathematician,  physicist, and engineer (b. 1792)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Coriolis\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French mathematician, physicist, and engineer (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Coriolis\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French mathematician, physicist, and engineer (b. 1792)", "links": [{"title": "Gaspard<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Norwegian-American colonel and politician (b. 1829)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American colonel and politician (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American colonel and politician (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, American minister and politician (b. 1809)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Michigan_politician)\" title=\"<PERSON> (Michigan politician)\"><PERSON></a>, American minister and politician (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Michigan_politician)\" title=\"<PERSON> (Michigan politician)\"><PERSON></a>, American minister and politician (b. 1809)", "links": [{"title": "<PERSON> (Michigan politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Michigan_politician)"}]}, {"year": "1873", "text": "<PERSON>, English-Australian politician, 3rd Premier of Queensland (b. 1811)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_10th_Baronet\" title=\"Sir <PERSON>, 10th Baronet\"><PERSON></a>, English-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_10th_Baronet\" title=\"Sir <PERSON>, 10th Baronet\"><PERSON></a>, English-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1811)", "links": [{"title": "Sir <PERSON>, 10th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_10th_Baronet"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1881", "text": "<PERSON>, American general, lawyer, and politician, and the 20th President of the United States (b. 1831)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, and the 20th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, and the 20th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1893", "text": "<PERSON>, English-Canadian politician, 1st Canadian Minister of Finance (b. 1817)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian politician, 1st <a href=\"https://wikipedia.org/wiki/Canadian_Minister_of_Finance\" class=\"mw-redirect\" title=\"Canadian Minister of Finance\">Canadian Minister of Finance</a> (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian politician, 1st <a href=\"https://wikipedia.org/wiki/Canadian_Minister_of_Finance\" class=\"mw-redirect\" title=\"Canadian Minister of Finance\">Canadian Minister of Finance</a> (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Canadian Minister of Finance", "link": "https://wikipedia.org/wiki/Canadian_Minister_of_Finance"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Japanese poet, author, and critic (b. 1867)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Masa<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>sa<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet, author, and critic (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Masa<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>sa<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet, author, and critic (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Masaoka_Shiki"}]}, {"year": "1905", "text": "<PERSON>, Irish-English philanthropist (b. 1845)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English philanthropist (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English philanthropist (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English educator, founded the Girls' Day School Trust (b. 1816)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator, founded the <a href=\"https://wikipedia.org/wiki/Girls%27_Day_School_Trust\" title=\"Girls' Day School Trust\">Girls' Day School Trust</a> (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator, founded the <a href=\"https://wikipedia.org/wiki/Girls%27_Day_School_Trust\" title=\"Girls' Day School Trust\">Girls' Day School Trust</a> (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Girls' Day School Trust", "link": "https://wikipedia.org/wiki/Girls%27_Day_School_Trust"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Australian cricketer and coach (b. 1854)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bannerman\"><PERSON><PERSON></a>, Australian cricketer and coach (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>man\"><PERSON><PERSON></a>, Australian cricketer and coach (b. 1854)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Danish painter (b. 1849)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Russian scientist and engineer (b. 1857)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian scientist and engineer (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian scientist and engineer (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Indian singer and musicologist (b. 1860)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian singer and musicologist (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian singer and musicologist (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, American publisher, founded Condé Nast Publications (b. 1873)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Cond%C3%A9_Montrose_Nast\" class=\"mw-redirect\" title=\"Condé Montrose Nast\">Condé <PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/Cond%C3%A9_Nast_Publications\" class=\"mw-redirect\" title=\"Condé Nast Publications\">Condé Nast Publications</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cond%C3%A9_Montrose_Nast\" class=\"mw-redirect\" title=\"Condé Montrose Nast\">Condé <PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/Cond%C3%A9_Nast_Publications\" class=\"mw-redirect\" title=\"Condé Nast Publications\">Condé Nast Publications</a> (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cond%C3%A9_<PERSON><PERSON>_Nast"}, {"title": "Condé Nast Publications", "link": "https://wikipedia.org/wiki/Cond%C3%A9_Nast_Publications"}]}, {"year": "1944", "text": "<PERSON>, Indian-English commander, Victoria Cross recipient (b. 1918)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English commander, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English commander, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1949", "text": "<PERSON>, Irish-Canadian playwright (b. 1886)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian playwright (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian playwright (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Greek violinist and composer (b. 1901)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek violinist and composer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek violinist and composer (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nik<PERSON>_Skalkottas"}]}, {"year": "1955", "text": "<PERSON>, Sr., American journalist and politician (b. 1894)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American journalist and politician (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American journalist and politician (b. 1894)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>."}]}, {"year": "1965", "text": "<PERSON>, French mountaineer (b. 1921)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Ukrainian-French painter (b. 1884)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-French painter (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-French painter (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American physicist and lawyer (b. 1906)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and lawyer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and lawyer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and actor (b. 1910)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Foley"}]}, {"year": "1972", "text": "<PERSON>, French pianist and composer (b. 1899)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1946)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Parsons\"><PERSON></a>, American singer-songwriter and guitarist (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English actress (b. 1917)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (b. 1917)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1978", "text": "<PERSON>, French historian and philosopher (b. 1884)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and philosopher (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and philosopher (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Italian novelist, short story writer, and journalist (b. 1923)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Italo_Calvino\" title=\"Italo Calvino\"><PERSON><PERSON></a>, Italian novelist, short story writer, and journalist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italo_Calvino\" title=\"Italo Calvino\"><PERSON><PERSON></a>, Italian novelist, short story writer, and journalist (b. 1923)", "links": [{"title": "Italo <PERSON>", "link": "https://wikipedia.org/wiki/Italo_<PERSON>o"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Norwegian civil servant and politician, 1st Prime Minister of Norway (b. 1897)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian civil servant and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian civil servant and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a> (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Norway", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Norway"}]}, {"year": "1989", "text": "<PERSON>, American long jumper (b. 1923)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American dancer and choreographer (b. 1910)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pan\"><PERSON><PERSON></a>, American dancer and choreographer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pan\"><PERSON><PERSON></a>, American dancer and choreographer (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, French chef (b. 1932)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American businessman, founded his own eponymous brand (b. 1907)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Orville_Redenbacher\" title=\"Orville Redenbacher\"><PERSON><PERSON> Redenbacher</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Orville_Redenbacher%27s\" title=\"<PERSON><PERSON> Redenbacher's\">his own eponymous brand</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Or<PERSON>_Redenbacher\" title=\"Orville Redenbacher\"><PERSON><PERSON> Redenbacher</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Orville_Redenbacher%27s\" title=\"<PERSON><PERSON> Redenbacher's\">his own eponymous brand</a> (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Or<PERSON>_<PERSON>enbacher"}, {"title": "Orville <PERSON>enbacher's", "link": "https://wikipedia.org/wiki/Orville_Redenbacher%27s"}]}, {"year": "1998", "text": "<PERSON>, English actress (b. 1909)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actress (b. 1911)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Welsh-Australian archaeologist and academic (b. 1941)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archaeologist)\" title=\"<PERSON> (archaeologist)\"><PERSON></a>, Welsh-Australian archaeologist and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archaeologist)\" title=\"<PERSON> (archaeologist)\"><PERSON></a>, Welsh-Australian archaeologist and academic (b. 1941)", "links": [{"title": "<PERSON> (archaeologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archaeologist)"}]}, {"year": "2002", "text": "<PERSON>, Ivorian politician, 3rd President of Côte d'Ivoire (b. 1941)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9%C3%AF\" title=\"<PERSON>\"><PERSON></a>, Ivorian politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_C%C3%B4te_d%27Ivoire\" class=\"mw-redirect\" title=\"President of Côte d'Ivoire\">President of Côte d'Ivoire</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9%C3%AF\" title=\"<PERSON>\"><PERSON></a>, Ivorian politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_C%C3%B4te_d%27Ivoire\" class=\"mw-redirect\" title=\"President of Côte d'Ivoire\">President of Côte d'Ivoire</a> (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_Gu%C3%A9%C3%AF"}, {"title": "President of Côte d'Ivoire", "link": "https://wikipedia.org/wiki/President_of_C%C3%B4te_d%27Ivoire"}]}, {"year": "2003", "text": "<PERSON>, Australian singer-songwriter, guitarist, and producer (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Slim_Dusty\" title=\"Slim Dusty\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slim_Dusty\" title=\"Slim Dusty\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American photographer and journalist (b. 1933)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and journalist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and journalist (b. 1933)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Indian dancer and choreographer (b. 1928)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian dancer and choreographer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian dancer and choreographer (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "2004", "text": "<PERSON>, Sr., American businessman and activist (b. 1908)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American businessman and activist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American businessman and activist (b. 1908)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>."}]}, {"year": "2006", "text": "<PERSON>, American actress (b. 1929)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1929)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter and saxophonist (b. 1929)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and saxophonist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and saxophonist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American photographer and journalist (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and journalist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and journalist (b. 1923)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)"}]}, {"year": "2006", "text": "<PERSON>, Dutch cyclist and manager (b. 1950)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist and manager (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist and manager (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American rhythm and blues drummer (b. 1924)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rhythm and blues drummer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rhythm and blues drummer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American historian and author (b. 1915)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, German journalist (b. 1929)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American lawyer and politician (b. 1949)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American singer (b. 1909)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, 1st Prime Minister of Belize (b. 1919)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 1st Prime Minister of Belize (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 1st Prime Minister of Belize (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Italian footballer (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Romanian-Israeli historian and author (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-Israeli historian and author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-Israeli historian and author (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English author and critic (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Polish-American author and philanthropist, founded the National Envelope Corporation (b. 1913)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/National_Envelope_Corporation\" title=\"National Envelope Corporation\">National Envelope Corporation</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/National_Envelope_Corporation\" title=\"National Envelope Corporation\">National Envelope Corporation</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Envelope Corporation", "link": "https://wikipedia.org/wiki/National_Envelope_Corporation"}]}, {"year": "2013", "text": "<PERSON>, American banker and politician, 37th Governor of Colorado (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor of Colorado</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor of Colorado</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Colorado", "link": "https://wikipedia.org/wiki/Governor_of_Colorado"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Japanese businessman (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actress (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English novelist (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1966)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese economist and politician, 63rd Japanese Minister of Finance (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese economist and politician, 63rd <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Japan)\" title=\"Minister of Finance (Japan)\">Japanese Minister of Finance</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese economist and politician, 63rd <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Japan)\" title=\"Minister of Finance (Japan)\">Japanese Minister of Finance</a> (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Finance (Japan)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Japan)"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Russian bass-baritone (b. 1933)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Russian bass-baritone (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Russian bass-baritone (b. 1933)", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)"}]}, {"year": "2018", "text": "<PERSON>, American ballet dancer & choreographer (b. 1934) ", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, American ballet dancer &amp; choreographer (b. 1934) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, American ballet dancer &amp; choreographer (b. 1934) ", "links": [{"title": "<PERSON> (dancer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)"}]}, {"year": "2018", "text": "<PERSON> \"<PERSON>\" <PERSON>, Irish TV presenter (b. 1927) ", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> \"<PERSON>\" <PERSON></a>, Irish TV presenter (b. 1927) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> \"<PERSON>\" <PERSON></a>, Irish TV presenter (b. 1927) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Tunisian soldier, politician, 2nd President of Tunisia  (b. 1936) ", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>ine_El_Abidine_<PERSON>_<PERSON>\" title=\"Zine El Abidine <PERSON>\"><PERSON><PERSON></a>, Tunisian soldier, politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Tunisia\" title=\"President of Tunisia\">President of Tunisia</a> (b. 1936) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Abid<PERSON>_<PERSON>\" title=\"Zine El Abidine <PERSON>\"><PERSON><PERSON></a>, Tunisian soldier, politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Tunisia\" title=\"President of Tunisia\">President of Tunisia</a> (b. 1936) ", "links": [{"title": "<PERSON>ine <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Tunisia", "link": "https://wikipedia.org/wiki/President_of_Tunisia"}]}, {"year": "2020", "text": "<PERSON>, Canadian politician, 17th Prime Minister of Canada (b. 1929)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "2021", "text": "<PERSON>, English actor (b. 1942)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, English footballer (b. 1940)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON>, Filipino politician, 23rd Secretary of Social Welfare and Development (b. 1953)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino politician, 23rd <a href=\"https://wikipedia.org/wiki/Secretary_of_Social_Welfare_and_Development\" title=\"Secretary of Social Welfare and Development\">Secretary of Social Welfare and Development</a> (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino politician, 23rd <a href=\"https://wikipedia.org/wiki/Secretary_of_Social_Welfare_and_Development\" title=\"Secretary of Social Welfare and Development\">Secretary of Social Welfare and Development</a> (b. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Secretary of Social Welfare and Development", "link": "https://wikipedia.org/wiki/Secretary_of_Social_Welfare_and_Development"}]}]}}