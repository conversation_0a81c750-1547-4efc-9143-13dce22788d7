{"date": "November 12", "url": "https://wikipedia.org/wiki/November_12", "data": {"Events": [{"year": "954", "text": "The 13-year-old <PERSON><PERSON><PERSON> <PERSON> is crowned at the Abbey of Saint-Remi as king of the West Frankish Kingdom.", "html": "954 - The 13-year-old <a href=\"https://wikipedia.org/wiki/Lothair_of_France\" title=\"Lot<PERSON><PERSON> of France\"><PERSON><PERSON><PERSON> III</a> is crowned at the <a href=\"https://wikipedia.org/wiki/Abbey_of_Saint-Remi\" class=\"mw-redirect\" title=\"Abbey of Saint-Remi\">Abbey of Saint-Remi</a> as king of the <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Frankish Kingdom</a>.", "no_year_html": "The 13-year-old <a href=\"https://wikipedia.org/wiki/Lothair_of_France\" title=\"Lothai<PERSON> of France\"><PERSON><PERSON><PERSON> III</a> is crowned at the <a href=\"https://wikipedia.org/wiki/Abbey_of_Saint-Remi\" class=\"mw-redirect\" title=\"Abbey of Saint-Remi\">Abbey of Saint-Remi</a> as king of the <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Frankish Kingdom</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>r_of_France"}, {"title": "Abbey of Saint-Remi", "link": "https://wikipedia.org/wiki/Abbey_of_Saint-Remi"}, {"title": "West Francia", "link": "https://wikipedia.org/wiki/West_Francia"}]}, {"year": "1028", "text": "Future Byzantine empress <PERSON> takes the throne as empress consort to <PERSON><PERSON>.", "html": "1028 - Future <a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine empress</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>hy<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes the throne as empress consort to <a href=\"https://wikipedia.org/wiki/Romanos_III_Argyros\" title=\"Romanos III Argyros\"><PERSON><PERSON> III Argyros</a>.", "no_year_html": "Future <a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine empress</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>hy<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes the throne as empress consort to <a href=\"https://wikipedia.org/wiki/Romanos_III_Argyros\" title=\"Romanos III Argyros\"><PERSON><PERSON> III Argyros</a>.", "links": [{"title": "Byzantine emperor", "link": "https://wikipedia.org/wiki/Byzantine_emperor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Porphyrogenita"}, {"title": "Romanos III Argyros", "link": "https://wikipedia.org/wiki/Romanos_III_Argyros"}]}, {"year": "1330", "text": "Battle of Posada ends: Wallachian <PERSON><PERSON><PERSON><PERSON> defeats the Hungarian army by ambush.", "html": "1330 - <a href=\"https://wikipedia.org/wiki/Battle_of_Posada\" title=\"Battle of Posada\">Battle of Posada</a> ends: Wallachian Voievode <a href=\"https://wikipedia.org/wiki/Basarab_I\" class=\"mw-redirect\" title=\"Basarab I\">Basarab I</a> defeats the Hungarian army by ambush.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Posada\" title=\"Battle of Posada\">Battle of Posada</a> ends: Wallachian Voievode <a href=\"https://wikipedia.org/wiki/Basarab_I\" class=\"mw-redirect\" title=\"Basarab I\"><PERSON>sarab I</a> defeats the Hungarian army by ambush.", "links": [{"title": "Battle of Posada", "link": "https://wikipedia.org/wiki/Battle_of_Posada"}, {"title": "Basarab I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}]}, {"year": "1439", "text": "Plymouth becomes the first town incorporated by the English Parliament.", "html": "1439 - <a href=\"https://wikipedia.org/wiki/Plymouth\" title=\"Plymouth\">Plymouth</a> becomes the first town incorporated by the <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">English Parliament</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plymouth\" title=\"Plymouth\">Plymouth</a> becomes the first town incorporated by the <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">English Parliament</a>.", "links": [{"title": "Plymouth", "link": "https://wikipedia.org/wiki/Plymouth"}, {"title": "Parliament of England", "link": "https://wikipedia.org/wiki/Parliament_of_England"}]}, {"year": "1835", "text": "Construction is completed on the Wilberforce Monument in Kingston Upon Hull.", "html": "1835 - Construction is completed on the <a href=\"https://wikipedia.org/wiki/Wilberforce_Monument\" title=\"Wilberforce Monument\">Wilberforce Monument</a> in <a href=\"https://wikipedia.org/wiki/Kingston_Upon_Hull\" class=\"mw-redirect\" title=\"Kingston Upon Hull\">Kingston Upon Hull</a>.", "no_year_html": "Construction is completed on the <a href=\"https://wikipedia.org/wiki/Wilberforce_Monument\" title=\"Wilberforce Monument\">Wilberforce Monument</a> in <a href=\"https://wikipedia.org/wiki/Kingston_Upon_Hull\" class=\"mw-redirect\" title=\"Kingston Upon Hull\">Kingston Upon Hull</a>.", "links": [{"title": "Wilberforce Monument", "link": "https://wikipedia.org/wiki/Wilberforce_Monument"}, {"title": "Kingston Upon Hull", "link": "https://wikipedia.org/wiki/Kingston_Upon_Hull"}]}, {"year": "1892", "text": "<PERSON><PERSON> becomes the first professional American football player on record, participating in his first paid game for the Allegheny Athletic Association.", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lfinger\" title=\"<PERSON><PERSON>lfinger\"><PERSON><PERSON></a> becomes the first professional <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> player on record, participating in his first paid game for the <a href=\"https://wikipedia.org/wiki/Allegheny_Athletic_Association\" title=\"Allegheny Athletic Association\">Allegheny Athletic Association</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lfinger\" title=\"<PERSON><PERSON>lfinger\"><PERSON><PERSON></a> becomes the first professional <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> player on record, participating in his first paid game for the <a href=\"https://wikipedia.org/wiki/Allegheny_Athletic_Association\" title=\"Allegheny Athletic Association\">Allegheny Athletic Association</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "American football", "link": "https://wikipedia.org/wiki/American_football"}, {"title": "Allegheny Athletic Association", "link": "https://wikipedia.org/wiki/Allegheny_Athletic_Association"}]}, {"year": "1893", "text": "<PERSON><PERSON> accepts the Durand Line as the border between the Emirate of Afghanistan and the British Raj.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> accepts the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Line\" title=\"Durand Line\">Durand Line</a> as the border between the <a href=\"https://wikipedia.org/wiki/Emirate_of_Afghanistan\" title=\"Emirate of Afghanistan\">Emirate of Afghanistan</a> and the <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British Raj</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> accepts the <a href=\"https://wikipedia.org/wiki/Durand_Line\" title=\"Durand Line\">Durand Line</a> as the border between the <a href=\"https://wikipedia.org/wiki/Emirate_of_Afghanistan\" title=\"Emirate of Afghanistan\">Emirate of Afghanistan</a> and the <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British Raj</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Durand Line", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Line"}, {"title": "Emirate of Afghanistan", "link": "https://wikipedia.org/wiki/Emirate_of_Afghanistan"}, {"title": "British Raj", "link": "https://wikipedia.org/wiki/British_Raj"}]}, {"year": "1905", "text": "Norway holds a referendum resulting in popular approval of the Storting's decision to authorise the government to make the offer of the throne of the newly independent country.", "html": "1905 - Norway holds <a href=\"https://wikipedia.org/wiki/Norwegian_monarchy_plebiscite,_1905\" class=\"mw-redirect\" title=\"Norwegian monarchy plebiscite, 1905\">a referendum</a> resulting in popular approval of the <a href=\"https://wikipedia.org/wiki/Parliament_of_Norway\" class=\"mw-redirect\" title=\"Parliament of Norway\"><PERSON><PERSON><PERSON>'s</a> decision to authorise the government to make the offer of the throne of the newly independent country.", "no_year_html": "Norway holds <a href=\"https://wikipedia.org/wiki/Norwegian_monarchy_plebiscite,_1905\" class=\"mw-redirect\" title=\"Norwegian monarchy plebiscite, 1905\">a referendum</a> resulting in popular approval of the <a href=\"https://wikipedia.org/wiki/Parliament_of_Norway\" class=\"mw-redirect\" title=\"Parliament of Norway\"><PERSON><PERSON><PERSON>'s</a> decision to authorise the government to make the offer of the throne of the newly independent country.", "links": [{"title": "Norwegian monarchy plebiscite, 1905", "link": "https://wikipedia.org/wiki/Norwegian_monarchy_plebiscite,_1905"}, {"title": "Parliament of Norway", "link": "https://wikipedia.org/wiki/Parliament_of_Norway"}]}, {"year": "1912", "text": "First Balkan War: King <PERSON> of Greece makes a triumphal entry into Thessaloniki after its liberation from 482 years of Ottoman rule.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> makes a triumphal entry into <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a> after its liberation from 482 years of Ottoman rule.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> makes a triumphal entry into <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a> after its liberation from 482 years of Ottoman rule.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece"}, {"title": "Thessaloniki", "link": "https://wikipedia.org/wiki/Thessaloniki"}]}, {"year": "1912", "text": "The frozen bodies of <PERSON> and his men are found on the Ross Ice Shelf in Antarctica.", "html": "1912 - The frozen bodies of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Scott\"><PERSON></a> and his men are found on the <a href=\"https://wikipedia.org/wiki/Ross_Ice_Shelf\" title=\"Ross Ice Shelf\">Ross Ice Shelf</a> in Antarctica.", "no_year_html": "The frozen bodies of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Scott\"><PERSON></a> and his men are found on the <a href=\"https://wikipedia.org/wiki/Ross_Ice_Shelf\" title=\"Ross Ice Shelf\">Ross Ice Shelf</a> in Antarctica.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Ross Ice Shelf", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lf"}]}, {"year": "1918", "text": "Dissolution of Austria-Hungary: Austria becomes a republic. After the proclamation, a coup attempt by the communist Red Guard is defeated by the social-democratic Volkswehr.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Dissolution_of_Austria-Hungary\" title=\"Dissolution of Austria-Hungary\">Dissolution of Austria-Hungary</a>: Austria becomes a <a href=\"https://wikipedia.org/wiki/Republic_of_German-Austria\" title=\"Republic of German-Austria\">republic</a>. After the proclamation, a coup attempt by the communist <i>Red Guard</i> is defeated by the social-democratic <a href=\"https://wikipedia.org/wiki/Volkswehr\" class=\"mw-redirect\" title=\"Volkswehr\">Volkswehr</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dissolution_of_Austria-Hungary\" title=\"Dissolution of Austria-Hungary\">Dissolution of Austria-Hungary</a>: Austria becomes a <a href=\"https://wikipedia.org/wiki/Republic_of_German-Austria\" title=\"Republic of German-Austria\">republic</a>. After the proclamation, a coup attempt by the communist <i>Red Guard</i> is defeated by the social-democratic <a href=\"https://wikipedia.org/wiki/Volkswehr\" class=\"mw-redirect\" title=\"Volkswehr\">Volkswehr</a>.", "links": [{"title": "Dissolution of Austria-Hungary", "link": "https://wikipedia.org/wiki/Dissolution_of_Austria-Hungary"}, {"title": "Republic of German-Austria", "link": "https://wikipedia.org/wiki/Republic_of_German-Austria"}, {"title": "Volkswehr", "link": "https://wikipedia.org/wiki/Volkswehr"}]}, {"year": "1920", "text": "The 1920 Cork hunger strike by Irish republicans ends after three deaths.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/1920_Cork_hunger_strike\" title=\"1920 Cork hunger strike\">1920 Cork hunger strike</a> by <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish republicans</a> ends after three deaths.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1920_Cork_hunger_strike\" title=\"1920 Cork hunger strike\">1920 Cork hunger strike</a> by <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish republicans</a> ends after three deaths.", "links": [{"title": "1920 Cork hunger strike", "link": "https://wikipedia.org/wiki/1920_Cork_hunger_strike"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}]}, {"year": "1920", "text": "Italy and the Kingdom of Serbs, Croats and Slovenes sign the Treaty of Rapallo.", "html": "1920 - Italy and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Yugoslavia\" title=\"Kingdom of Yugoslavia\">Kingdom of Serbs, Croats and Slovenes</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Rapallo_(1920)\" title=\"Treaty of Rapallo (1920)\">Treaty of Rapallo</a>.", "no_year_html": "Italy and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Yugoslavia\" title=\"Kingdom of Yugoslavia\">Kingdom of Serbs, Croats and Slovenes</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Rapallo_(1920)\" title=\"Treaty of Rapallo (1920)\">Treaty of Rapallo</a>.", "links": [{"title": "Kingdom of Yugoslavia", "link": "https://wikipedia.org/wiki/Kingdom_of_Yugoslavia"}, {"title": "Treaty of Rapallo (1920)", "link": "https://wikipedia.org/wiki/Treaty_of_Rapallo_(1920)"}]}, {"year": "1927", "text": "<PERSON> is expelled from the Soviet Communist Party, leaving <PERSON> in undisputed control of the Soviet Union.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is expelled from the <a href=\"https://wikipedia.org/wiki/Soviet_Communist_Party\" class=\"mw-redirect\" title=\"Soviet Communist Party\">Soviet Communist Party</a>, leaving <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in undisputed control of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is expelled from the <a href=\"https://wikipedia.org/wiki/Soviet_Communist_Party\" class=\"mw-redirect\" title=\"Soviet Communist Party\">Soviet Communist Party</a>, leaving <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in undisputed control of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Communist Party", "link": "https://wikipedia.org/wiki/Soviet_Communist_Party"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1928", "text": "SS Vestris sinks approximately 200 miles (320 km) off Hampton Roads, Virginia, killing at least 110 passengers, mostly women and children who die after the vessel is abandoned.", "html": "1928 - <a href=\"https://wikipedia.org/wiki/SS_Vestris\" title=\"SS Vestris\">SS <i>Vestris</i></a> sinks approximately 200 miles (320 km) off <a href=\"https://wikipedia.org/wiki/Hampton_Roads,_Virginia\" class=\"mw-redirect\" title=\"Hampton Roads, Virginia\">Hampton Roads, Virginia</a>, killing at least 110 passengers, mostly women and children who die after the vessel is abandoned.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SS_Vestris\" title=\"SS Vestris\">SS <i>Vestris</i></a> sinks approximately 200 miles (320 km) off <a href=\"https://wikipedia.org/wiki/Hampton_Roads,_Virginia\" class=\"mw-redirect\" title=\"Hampton Roads, Virginia\">Hampton Roads, Virginia</a>, killing at least 110 passengers, mostly women and children who die after the vessel is abandoned.", "links": [{"title": "SS Vestris", "link": "https://wikipedia.org/wiki/SS_Vestris"}, {"title": "Hampton Roads, Virginia", "link": "https://wikipedia.org/wiki/Hampton_Roads,_Virginia"}]}, {"year": "1933", "text": "Nazi Germany uses a referendum to ratify its withdrawal from the League of Nations.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> uses a <a href=\"https://wikipedia.org/wiki/1933_German_referendum\" class=\"mw-redirect\" title=\"1933 German referendum\">referendum</a> to ratify its withdrawal from the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> uses a <a href=\"https://wikipedia.org/wiki/1933_German_referendum\" class=\"mw-redirect\" title=\"1933 German referendum\">referendum</a> to ratify its withdrawal from the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "1933 German referendum", "link": "https://wikipedia.org/wiki/1933_German_referendum"}, {"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1936", "text": "In California, the San Francisco-Oakland Bay Bridge opens to traffic.", "html": "1936 - In California, the <a href=\"https://wikipedia.org/wiki/San_Francisco%E2%80%93Oakland_Bay_Bridge\" title=\"San Francisco-Oakland Bay Bridge\">San Francisco-Oakland Bay Bridge</a> opens to traffic.", "no_year_html": "In California, the <a href=\"https://wikipedia.org/wiki/San_Francisco%E2%80%93Oakland_Bay_Bridge\" title=\"San Francisco-Oakland Bay Bridge\">San Francisco-Oakland Bay Bridge</a> opens to traffic.", "links": [{"title": "San Francisco-Oakland Bay Bridge", "link": "https://wikipedia.org/wiki/San_Francisco%E2%80%93Oakland_Bay_Bridge"}]}, {"year": "1938", "text": "Nazi Germany issues the Decree on the Elimination of Jews from Economic Life prohibiting Jews from selling goods and services or working in a trade, totally segregating Jews from the German economy.", "html": "1938 - Nazi Germany issues the Decree on the Elimination of Jews from Economic Life prohibiting Jews from selling goods and services or working in a trade, totally segregating Jews from the <a href=\"https://wikipedia.org/wiki/Economy_of_Nazi_Germany\" title=\"Economy of Nazi Germany\">German economy</a>.", "no_year_html": "Nazi Germany issues the Decree on the Elimination of Jews from Economic Life prohibiting Jews from selling goods and services or working in a trade, totally segregating Jews from the <a href=\"https://wikipedia.org/wiki/Economy_of_Nazi_Germany\" title=\"Economy of Nazi Germany\">German economy</a>.", "links": [{"title": "Economy of Nazi Germany", "link": "https://wikipedia.org/wiki/Economy_of_Nazi_Germany"}]}, {"year": "1940", "text": "World War II: The Battle of Gabon ends as Free French Forces take Libreville, Gabon, and all of French Equatorial Africa from Vichy French forces.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Gabon\" title=\"Battle of Gabon\">Battle of Gabon</a> ends as <a href=\"https://wikipedia.org/wiki/Free_French_Forces\" class=\"mw-redirect\" title=\"Free French Forces\">Free French Forces</a> take <a href=\"https://wikipedia.org/wiki/Libreville\" title=\"Libreville\">Libreville</a>, <a href=\"https://wikipedia.org/wiki/Gabon\" title=\"Gabon\">Gabon</a>, and all of <a href=\"https://wikipedia.org/wiki/French_Equatorial_Africa\" title=\"French Equatorial Africa\">French Equatorial Africa</a> from <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy French</a> forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Gabon\" title=\"Battle of Gabon\">Battle of Gabon</a> ends as <a href=\"https://wikipedia.org/wiki/Free_French_Forces\" class=\"mw-redirect\" title=\"Free French Forces\">Free French Forces</a> take <a href=\"https://wikipedia.org/wiki/Libreville\" title=\"Libreville\">Libreville</a>, <a href=\"https://wikipedia.org/wiki/Gabon\" title=\"Gabon\">Gabon</a>, and all of <a href=\"https://wikipedia.org/wiki/French_Equatorial_Africa\" title=\"French Equatorial Africa\">French Equatorial Africa</a> from <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy French</a> forces.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Gabon", "link": "https://wikipedia.org/wiki/Battle_of_Gabon"}, {"title": "Free French Forces", "link": "https://wikipedia.org/wiki/Free_French_Forces"}, {"title": "Libreville", "link": "https://wikipedia.org/wiki/Libreville"}, {"title": "Gabon", "link": "https://wikipedia.org/wiki/Gabon"}, {"title": "French Equatorial Africa", "link": "https://wikipedia.org/wiki/French_Equatorial_Africa"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vichy_France"}]}, {"year": "1940", "text": "World War II: Soviet Foreign Minister <PERSON><PERSON><PERSON><PERSON> arrives in Berlin to discuss the possibility of the Soviet Union joining the Axis Powers.", "html": "1940 - World War II: Soviet Foreign Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> arrives in Berlin to discuss <a href=\"https://wikipedia.org/wiki/German%E2%80%93Soviet_Axis_talks\" title=\"German-Soviet Axis talks\">the possibility of the Soviet Union joining the Axis Powers</a>.", "no_year_html": "World War II: Soviet Foreign Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> arrives in Berlin to discuss <a href=\"https://wikipedia.org/wiki/German%E2%80%93Soviet_Axis_talks\" title=\"German-Soviet Axis talks\">the possibility of the Soviet Union joining the Axis Powers</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "German-Soviet Axis talks", "link": "https://wikipedia.org/wiki/German%E2%80%93Soviet_Axis_talks"}]}, {"year": "1941", "text": "World War II: Temperatures around Moscow drop to −12 °C (10 °F) as the Soviet Union launches ski troops for the first time against the freezing German forces near the city.", "html": "1941 - World War II: Temperatures around Moscow drop to −12 °C (10 °F) as the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches <a href=\"https://wikipedia.org/wiki/Ski_troops\" class=\"mw-redirect\" title=\"Ski troops\">ski troops</a> for the first time against the freezing German forces near the city.", "no_year_html": "World War II: Temperatures around Moscow drop to −12 °C (10 °F) as the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches <a href=\"https://wikipedia.org/wiki/Ski_troops\" class=\"mw-redirect\" title=\"Ski troops\">ski troops</a> for the first time against the freezing German forces near the city.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Ski troops", "link": "https://wikipedia.org/wiki/Ski_troops"}]}, {"year": "1941", "text": "World War II: The Soviet cruiser <PERSON><PERSON><PERSON><PERSON> is destroyed during the Battle of Sevastopol.", "html": "1941 - World War II: The <a href=\"https://wikipedia.org/wiki/Soviet_cruiser_<PERSON><PERSON><PERSON><PERSON>_Ukraina\" title=\"Soviet cruiser <PERSON><PERSON><PERSON><PERSON> Ukraina\">Soviet cruiser <i><PERSON><PERSON><PERSON><PERSON> Ukraina</i></a> is destroyed during the <a href=\"https://wikipedia.org/wiki/Battle_of_Sevastopol\" class=\"mw-redirect\" title=\"Battle of Sevastopol\">Battle of Sevastopol</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Soviet_cruiser_<PERSON><PERSON><PERSON><PERSON>_Ukraina\" title=\"Soviet cruiser <PERSON><PERSON><PERSON><PERSON> Ukraina\">Soviet cruiser <i><PERSON><PERSON><PERSON><PERSON> Ukraina</i></a> is destroyed during the <a href=\"https://wikipedia.org/wiki/Battle_of_Sevastopol\" class=\"mw-redirect\" title=\"Battle of Sevastopol\">Battle of Sevastopol</a>.", "links": [{"title": "Soviet cruiser <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Soviet_cruiser_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Sevastopol", "link": "https://wikipedia.org/wiki/Battle_of_Sevastopol"}]}, {"year": "1942", "text": "World War II: Naval Battle of Guadalcanal between Japanese and American forces begins near Guadalcanal. The battle lasts for three days and ends with an American victory.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Naval_Battle_of_Guadalcanal\" title=\"Naval Battle of Guadalcanal\">Naval Battle of Guadalcanal</a> between Japanese and American forces begins near <a href=\"https://wikipedia.org/wiki/Guadalcanal\" title=\"Guadalcanal\">Guadalcanal</a>. The battle lasts for three days and ends with an American victory.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Naval_Battle_of_Guadalcanal\" title=\"Naval Battle of Guadalcanal\">Naval Battle of Guadalcanal</a> between Japanese and American forces begins near <a href=\"https://wikipedia.org/wiki/Guadalcanal\" title=\"Guadalcanal\">Guadalcanal</a>. The battle lasts for three days and ends with an American victory.", "links": [{"title": "Naval Battle of Guadalcanal", "link": "https://wikipedia.org/wiki/Naval_Battle_of_Guadalcanal"}, {"title": "Guadalcanal", "link": "https://wikipedia.org/wiki/Guadalcanal"}]}, {"year": "1944", "text": "World War II: The Royal Air Force launches 29 Avro Lancaster bombers, which sink the German battleship Tirpitz, with 12,000 lb Tallboy bombs off Tromsø, Norway.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> launches 29 <a href=\"https://wikipedia.org/wiki/Avro_Lancaster\" title=\"Avro Lancaster\">Avro Lancaster</a> bombers, which <a href=\"https://wikipedia.org/wiki/Operation_Catechism\" title=\"Operation Catechism\">sink</a> the German <a href=\"https://wikipedia.org/wiki/Battleship\" title=\"Battleship\">battleship</a> <i><a href=\"https://wikipedia.org/wiki/German_battleship_Tirpitz\" title=\"German battleship Tirpitz\">Tirpitz</a></i>, with 12,000 lb <a href=\"https://wikipedia.org/wiki/Tallboy_bomb\" class=\"mw-redirect\" title=\"Tallboy bomb\">Tallboy bombs</a> off <a href=\"https://wikipedia.org/wiki/Troms%C3%B8\" title=\"Tromsø\">Tromsø</a>, Norway.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> launches 29 <a href=\"https://wikipedia.org/wiki/Avro_Lancaster\" title=\"Avro Lancaster\">Av<PERSON></a> bombers, which <a href=\"https://wikipedia.org/wiki/Operation_Catechism\" title=\"Operation Catechism\">sink</a> the German <a href=\"https://wikipedia.org/wiki/Battleship\" title=\"Battleship\">battleship</a> <i><a href=\"https://wikipedia.org/wiki/German_battleship_Tirpitz\" title=\"German battleship Tirpitz\">Tirpitz</a></i>, with 12,000 lb <a href=\"https://wikipedia.org/wiki/Tallboy_bomb\" class=\"mw-redirect\" title=\"Tallboy bomb\">Tallboy bombs</a> off <a href=\"https://wikipedia.org/wiki/Troms%C3%B8\" title=\"Tromsø\">Tromsø</a>, Norway.", "links": [{"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}, {"title": "Avro Lancaster", "link": "https://wikipedia.org/wiki/Avro_Lancaster"}, {"title": "Operation Catechism", "link": "https://wikipedia.org/wiki/Operation_Catechism"}, {"title": "Battleship", "link": "https://wikipedia.org/wiki/Battleship"}, {"title": "German battleship Tirpitz", "link": "https://wikipedia.org/wiki/German_battleship_Tirpitz"}, {"title": "Tallboy bomb", "link": "https://wikipedia.org/wiki/Tallboy_bomb"}, {"title": "Tromsø", "link": "https://wikipedia.org/wiki/Troms%C3%B8"}]}, {"year": "1948", "text": "Aftermath of World War II: In Tokyo, the International Military Tribunal for the Far East sentences seven Japanese military and government officials, including General <PERSON><PERSON><PERSON>, to death for their roles in World War II.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Aftermath_of_World_War_II\" title=\"Aftermath of World War II\">Aftermath of World War II</a>: In Tokyo, the <a href=\"https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East\" title=\"International Military Tribunal for the Far East\">International Military Tribunal for the Far East</a> sentences seven Japanese military and government officials, including General <a href=\"https://wikipedia.org/wiki/Hideki_Tojo\" title=\"Hi<PERSON><PERSON> Tojo\"><PERSON><PERSON><PERSON></a>, to death for their roles in World War II.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aftermath_of_World_War_II\" title=\"Aftermath of World War II\">Aftermath of World War II</a>: In Tokyo, the <a href=\"https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East\" title=\"International Military Tribunal for the Far East\">International Military Tribunal for the Far East</a> sentences seven Japanese military and government officials, including General <a href=\"https://wikipedia.org/wiki/Hideki_Tojo\" title=\"Hidek<PERSON>jo\"><PERSON><PERSON><PERSON></a>, to death for their roles in World War II.", "links": [{"title": "Aftermath of World War II", "link": "https://wikipedia.org/wiki/Aftermath_of_World_War_II"}, {"title": "International Military Tribunal for the Far East", "link": "https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1954", "text": "Ellis Island ceases operations.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Ellis_Island\" title=\"Ellis Island\">Ellis Island</a> ceases operations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ellis_Island\" title=\"Ellis Island\">Ellis Island</a> ceases operations.", "links": [{"title": "Ellis Island", "link": "https://wikipedia.org/wiki/Ellis_Island"}]}, {"year": "1956", "text": "Morocco, Sudan and Tunisia join the United Nations.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>, <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a> and <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a> join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>, <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a> and <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a> join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}, {"title": "Sudan", "link": "https://wikipedia.org/wiki/Sudan"}, {"title": "Tunisia", "link": "https://wikipedia.org/wiki/Tunisia"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1956", "text": "In the midst of the Suez Crisis, Palestinian refugees are shot dead in Rafah by Israel Defense Force soldiers following the invasion of the Gaza Strip.", "html": "1956 - In the midst of the <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>, <a href=\"https://wikipedia.org/wiki/Palestinian_refugees\" title=\"Palestinian refugees\">Palestinian refugees</a> are <a href=\"https://wikipedia.org/wiki/1956_Rafah_massacre\" title=\"1956 Rafah massacre\">shot dead in Rafah</a> by <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel Defense Force</a> soldiers following the invasion of the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a>.", "no_year_html": "In the midst of the <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>, <a href=\"https://wikipedia.org/wiki/Palestinian_refugees\" title=\"Palestinian refugees\">Palestinian refugees</a> are <a href=\"https://wikipedia.org/wiki/1956_Rafah_massacre\" title=\"1956 Rafah massacre\">shot dead in Rafah</a> by <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel Defense Force</a> soldiers following the invasion of the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a>.", "links": [{"title": "Suez Crisis", "link": "https://wikipedia.org/wiki/Suez_Crisis"}, {"title": "Palestinian refugees", "link": "https://wikipedia.org/wiki/Palestinian_refugees"}, {"title": "1956 Rafah massacre", "link": "https://wikipedia.org/wiki/1956_Rafah_massacre"}, {"title": "Israel Defense Forces", "link": "https://wikipedia.org/wiki/Israel_Defense_Forces"}, {"title": "Gaza Strip", "link": "https://wikipedia.org/wiki/Gaza_Strip"}]}, {"year": "1958", "text": "A team of rock climbers led by <PERSON> completes the first ascent of The Nose on El Capitan in Yosemite Valley.", "html": "1958 - A team of rock climbers led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(climber)\" title=\"<PERSON> (climber)\"><PERSON></a> completes the first ascent of <a href=\"https://wikipedia.org/wiki/The_Nose_(El_Capitan)\" title=\"The Nose (El Capitan)\">The Nose</a> on <a href=\"https://wikipedia.org/wiki/El_Capitan\" title=\"El Capitan\">El Capitan</a> in <a href=\"https://wikipedia.org/wiki/Yosemite_Valley\" title=\"Yosemite Valley\">Yosemite Valley</a>.", "no_year_html": "A team of rock climbers led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(climber)\" title=\"<PERSON> (climber)\"><PERSON></a> completes the first ascent of <a href=\"https://wikipedia.org/wiki/The_Nose_(El_Capitan)\" title=\"The Nose (El Capitan)\">The Nose</a> on <a href=\"https://wikipedia.org/wiki/El_Capitan\" title=\"El Capitan\">El Capitan</a> in <a href=\"https://wikipedia.org/wiki/Yosemite_Valley\" title=\"Yosemite Valley\">Yosemite Valley</a>.", "links": [{"title": "<PERSON> (climber)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(climber)"}, {"title": "The Nose (El Capitan)", "link": "https://wikipedia.org/wiki/The_Nose_(El_Capitan)"}, {"title": "El Capitan", "link": "https://wikipedia.org/wiki/El_Capitan"}, {"title": "Yosemite Valley", "link": "https://wikipedia.org/wiki/Yosemite_Valley"}]}, {"year": "1961", "text": "<PERSON> is the sole survivor of a series of brutal murders aboard the ketch Bluebelle.", "html": "1961 - <PERSON> is the sole survivor of a series of brutal murders aboard the <a href=\"https://wikipedia.org/wiki/Ketch\" title=\"Ketch\">ketch</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(ship)\" title=\"<PERSON><PERSON><PERSON> (ship)\"><i><PERSON><PERSON><PERSON></i></a>.", "no_year_html": "<PERSON> is the sole survivor of a series of brutal murders aboard the <a href=\"https://wikipedia.org/wiki/Ketch\" title=\"Ketch\">ketch</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(ship)\" title=\"<PERSON><PERSON><PERSON> (ship)\"><i><PERSON><PERSON><PERSON></i></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ketch"}, {"title": "<PERSON><PERSON><PERSON> (ship)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(ship)"}]}, {"year": "1969", "text": "Vietnam War: Independent investigative journalist <PERSON> breaks the story of the My Lai Massacre.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Independent investigative journalist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> breaks the story of the <a href=\"https://wikipedia.org/wiki/My_Lai_Massacre\" class=\"mw-redirect\" title=\"My Lai Massacre\">My Lai Massacre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Independent investigative journalist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> breaks the story of the <a href=\"https://wikipedia.org/wiki/My_Lai_Massacre\" class=\"mw-redirect\" title=\"My Lai Massacre\">My Lai Massacre</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "My Lai Massacre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "The Oregon Highway Division attempts to destroy a rotting beached sperm whale with explosives, leading to the now infamous \"exploding whale\" incident.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/Oregon_Highway_Division\" class=\"mw-redirect\" title=\"Oregon Highway Division\">Oregon Highway Division</a> attempts to destroy a rotting beached <a href=\"https://wikipedia.org/wiki/Sperm_whale\" title=\"Sperm whale\">sperm whale</a> with explosives, leading to the now infamous <a href=\"https://wikipedia.org/wiki/Exploding_whale#Sperm_whale_explosion_by_<PERSON>_<PERSON>_of_the_Florence-Oregon_whale\" title=\"Exploding whale\">\"exploding whale\"</a> incident.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Oregon_Highway_Division\" class=\"mw-redirect\" title=\"Oregon Highway Division\">Oregon Highway Division</a> attempts to destroy a rotting beached <a href=\"https://wikipedia.org/wiki/Sperm_whale\" title=\"Sperm whale\">sperm whale</a> with explosives, leading to the now infamous <a href=\"https://wikipedia.org/wiki/Exploding_whale#Sperm_whale_explosion_by_<PERSON>_<PERSON>_of_the_Florence-Oregon_whale\" title=\"Exploding whale\">\"exploding whale\"</a> incident.", "links": [{"title": "Oregon Highway Division", "link": "https://wikipedia.org/wiki/Oregon_Highway_Division"}, {"title": "Sperm whale", "link": "https://wikipedia.org/wiki/Sperm_whale"}, {"title": "Exploding whale", "link": "https://wikipedia.org/wiki/Exploding_whale#Sperm_whale_explosion_by_<PERSON>_<PERSON>_of_the_Florence-Oregon_whale"}]}, {"year": "1970", "text": "The 1970 Bhola cyclone makes landfall on the coast of East Pakistan, becoming the deadliest tropical cyclone in history.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/1970_Bhola_cyclone\" title=\"1970 Bhola cyclone\">1970 Bhola cyclone</a> makes landfall on the coast of <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a>, becoming the deadliest tropical cyclone in history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1970_Bhola_cyclone\" title=\"1970 Bhola cyclone\">1970 Bhola cyclone</a> makes landfall on the coast of <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a>, becoming the deadliest tropical cyclone in history.", "links": [{"title": "1970 Bhola cyclone", "link": "https://wikipedia.org/wiki/1970_Bhola_cyclone"}, {"title": "East Pakistan", "link": "https://wikipedia.org/wiki/East_Pakistan"}]}, {"year": "1971", "text": "Vietnam War: As part of Vietnamization, U.S. President <PERSON> sets February 1, 1972 as the deadline for the removal of another 45,000 American troops from Vietnam.", "html": "1971 - Vietnam War: As part of <a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets February 1, 1972 as the deadline for the removal of another 45,000 American troops from <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "no_year_html": "Vietnam War: As part of <a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets February 1, 1972 as the deadline for the removal of another 45,000 American troops from <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "links": [{"title": "Vietnamization", "link": "https://wikipedia.org/wiki/Vietnamization"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1971", "text": "Aeroflot Flight N-63 crashes on approach to Vinnytsia Airport, killing 48.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_N-63\" title=\"Aeroflot Flight N-63\">Aeroflot Flight N-63</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Havryshivka_Vinnytsia_International_Airport\" title=\"Havryshivka Vinnytsia International Airport\">Vinnytsia Airport</a>, killing 48.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_N-63\" title=\"Aeroflot Flight N-63\">Aeroflot Flight N-63</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Havryshivka_Vinnytsia_International_Airport\" title=\"Havryshivka Vinnytsia International Airport\">Vinnytsia Airport</a>, killing 48.", "links": [{"title": "Aeroflot Flight N-63", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_N-63"}, {"title": "Havryshivka Vinnytsia International Airport", "link": "https://wikipedia.org/wiki/Havryshivka_Vinnytsia_International_Airport"}]}, {"year": "1975", "text": "The Comoros joins the United Nations.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Comoros\" title=\"Comoros\"><PERSON><PERSON></a> joins the United Nations.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Comoros\" title=\"Comoros\">Como<PERSON></a> joins the United Nations.", "links": [{"title": "Comoros", "link": "https://wikipedia.org/wiki/Comoros"}]}, {"year": "1977", "text": "France conducts the Oreste nuclear test as 14th in the group of 29, 1975-78 French nuclear tests series.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> conducts the <i>Oreste</i> nuclear test as 14th in the group of 29, <a href=\"https://wikipedia.org/wiki/1975%E2%80%9378_French_nuclear_tests\" title=\"1975-78 French nuclear tests\">1975-78 French nuclear tests</a> series.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> conducts the <i>Oreste</i> nuclear test as 14th in the group of 29, <a href=\"https://wikipedia.org/wiki/1975%E2%80%9378_French_nuclear_tests\" title=\"1975-78 French nuclear tests\">1975-78 French nuclear tests</a> series.", "links": [{"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "1975-78 French nuclear tests", "link": "https://wikipedia.org/wiki/1975%E2%80%9378_French_nuclear_tests"}]}, {"year": "1979", "text": "Iran hostage crisis: In response to the hostage situation in Tehran, U.S. President <PERSON> orders a halt to all petroleum imports into the United States from Iran.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>: In response to the hostage situation in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders a halt to all petroleum imports into the United States from <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>: In response to the hostage situation in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders a halt to all petroleum imports into the United States from <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>.", "links": [{"title": "Iran hostage crisis", "link": "https://wikipedia.org/wiki/Iran_hostage_crisis"}, {"title": "Tehran", "link": "https://wikipedia.org/wiki/Tehran"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "1980", "text": "The NASA space probe Voyager I makes its closest approach to Saturn and takes the first images of its rings.", "html": "1980 - The <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> space probe <i><a href=\"https://wikipedia.org/wiki/Voyager_I\" class=\"mw-redirect\" title=\"Voyager I\">Voyager I</a></i> makes its closest approach to <a href=\"https://wikipedia.org/wiki/Saturn\" title=\"Saturn\">Saturn</a> and takes the first images of its <a href=\"https://wikipedia.org/wiki/Rings_of_Saturn\" title=\"Rings of Saturn\">rings</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> space probe <i><a href=\"https://wikipedia.org/wiki/Voyager_I\" class=\"mw-redirect\" title=\"Voyager I\">Voyager I</a></i> makes its closest approach to <a href=\"https://wikipedia.org/wiki/Saturn\" title=\"Saturn\">Saturn</a> and takes the first images of its <a href=\"https://wikipedia.org/wiki/Rings_of_Saturn\" title=\"Rings of Saturn\">rings</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Voyager I", "link": "https://wikipedia.org/wiki/<PERSON>_I"}, {"title": "Saturn", "link": "https://wikipedia.org/wiki/Saturn"}, {"title": "Rings of Saturn", "link": "https://wikipedia.org/wiki/Rings_of_Saturn"}]}, {"year": "1981", "text": "Space Shuttle program: Mission STS-2, utilizing the Space Shuttle Columbia, marks the first time a crewed spacecraft is launched into space twice.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: Mission <a href=\"https://wikipedia.org/wiki/STS-2\" title=\"STS-2\">STS-2</a>, utilizing the <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Columbia</a></i>, marks the first time a crewed spacecraft is launched into space twice.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: Mission <a href=\"https://wikipedia.org/wiki/STS-2\" title=\"STS-2\">STS-2</a>, utilizing the <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Columbia</a></i>, marks the first time a crewed spacecraft is launched into space twice.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-2", "link": "https://wikipedia.org/wiki/STS-2"}, {"title": "Space Shuttle", "link": "https://wikipedia.org/wiki/Space_Shuttle"}, {"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}]}, {"year": "1982", "text": "USSR: <PERSON> becomes the General Secretary of the Communist Party's Central Committee, succeeding <PERSON><PERSON>.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/USSR\" class=\"mw-redirect\" title=\"USSR\">USSR</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">General Secretary</a> of the <a href=\"https://wikipedia.org/wiki/Central_Committee_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Central Committee of the Communist Party of the Soviet Union\">Communist Party's Central Committee</a>, succeeding <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USSR\" class=\"mw-redirect\" title=\"USSR\">USSR</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">General Secretary</a> of the <a href=\"https://wikipedia.org/wiki/Central_Committee_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Central Committee of the Communist Party of the Soviet Union\">Communist Party's Central Committee</a>, succeeding <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "USSR", "link": "https://wikipedia.org/wiki/USSR"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "General Secretary of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union"}, {"title": "Central Committee of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Central_Committee_of_the_Communist_Party_of_the_Soviet_Union"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "Crown Prince <PERSON><PERSON><PERSON><PERSON> is formally installed as Emperor <PERSON><PERSON><PERSON><PERSON> of Japan, becoming the 125th Japanese monarch.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Crown_Prince\" class=\"mw-redirect\" title=\"Crown Prince\">Crown Prince</a> <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is formally installed as Emperor <PERSON><PERSON><PERSON><PERSON> of Japan, becoming the 125th Japanese monarch.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crown_Prince\" class=\"mw-redirect\" title=\"Crown Prince\">Crown Prince</a> <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is formally installed as Emperor <PERSON><PERSON><PERSON><PERSON> of Japan, becoming the 125th Japanese monarch.", "links": [{"title": "Crown Prince", "link": "https://wikipedia.org/wiki/Crown_Prince"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>hito"}]}, {"year": "1990", "text": "<PERSON> publishes a formal proposal for the World Wide Web.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes a formal proposal for the <a href=\"https://wikipedia.org/wiki/World_Wide_Web\" title=\"World Wide Web\">World Wide Web</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes a formal proposal for the <a href=\"https://wikipedia.org/wiki/World_Wide_Web\" title=\"World Wide Web\">World Wide Web</a>.", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "World Wide Web", "link": "https://wikipedia.org/wiki/World_Wide_Web"}]}, {"year": "1991", "text": "Santa Cruz massacre: The Indonesian Army open fire on a crowd of student protesters in Dili, East Timor.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Santa_Cruz_massacre\" title=\"Santa Cruz massacre\">Santa Cruz massacre</a>: The <a href=\"https://wikipedia.org/wiki/Indonesian_Army\" title=\"Indonesian Army\">Indonesian Army</a> open fire on a crowd of student protesters in <a href=\"https://wikipedia.org/wiki/Dili\" title=\"Dili\">Dili</a>, East Timor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santa_Cruz_massacre\" title=\"Santa Cruz massacre\">Santa Cruz massacre</a>: The <a href=\"https://wikipedia.org/wiki/Indonesian_Army\" title=\"Indonesian Army\">Indonesian Army</a> open fire on a crowd of student protesters in <a href=\"https://wikipedia.org/wiki/Dili\" title=\"Dili\">Dili</a>, East Timor.", "links": [{"title": "Santa Cruz massacre", "link": "https://wikipedia.org/wiki/Santa_Cruz_massacre"}, {"title": "Indonesian Army", "link": "https://wikipedia.org/wiki/Indonesian_Army"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dili"}]}, {"year": "1995", "text": "Erdut Agreement regarding the peaceful resolution to the Croatian War of Independence is reached.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Erdut_Agreement\" title=\"Erdut Agreement\">Erdut Agreement</a> regarding the peaceful resolution to the <a href=\"https://wikipedia.org/wiki/Croatian_War_of_Independence\" title=\"Croatian War of Independence\">Croatian War of Independence</a> is reached.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erdut_Agreement\" title=\"Erdut Agreement\">Erdut Agreement</a> regarding the peaceful resolution to the <a href=\"https://wikipedia.org/wiki/Croatian_War_of_Independence\" title=\"Croatian War of Independence\">Croatian War of Independence</a> is reached.", "links": [{"title": "Erdut Agreement", "link": "https://wikipedia.org/wiki/Erdut_Agreement"}, {"title": "Croatian War of Independence", "link": "https://wikipedia.org/wiki/Croatian_War_of_Independence"}]}, {"year": "1995", "text": "Space Shuttle Atlantis launches on STS-74 to deliver the Mir Docking Module to the Russian space station Mir.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-74\" title=\"STS-74\">STS-74</a> to deliver the <a href=\"https://wikipedia.org/wiki/Mir_Docking_Module\" title=\"Mir Docking Module\"><i>Mir</i> Docking Module</a> to the Russian space station <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-74\" title=\"STS-74\">STS-74</a> to deliver the <a href=\"https://wikipedia.org/wiki/Mir_Docking_Module\" title=\"Mir Docking Module\"><i>Mir</i> Docking Module</a> to the Russian space station <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-74", "link": "https://wikipedia.org/wiki/STS-74"}, {"title": "Mir Docking Module", "link": "https://wikipedia.org/wiki/Mir_Docking_Module"}, {"title": "Mir", "link": "https://wikipedia.org/wiki/Mir"}]}, {"year": "1996", "text": "A Saudi Arabian Airlines Boeing 747 and a Kazakh Ilyushin Il-76 cargo plane collide in mid-air near New Delhi, killing 349 in the deadliest mid-air collision to date.", "html": "1996 - A <a href=\"https://wikipedia.org/wiki/Saudi_Arabian_Airlines\" class=\"mw-redirect\" title=\"Saudi Arabian Airlines\">Saudi Arabian Airlines</a> <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a> and a Kazakh <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> cargo plane <a href=\"https://wikipedia.org/wiki/1996_Charkhi_Dadri_mid-air_collision\" title=\"1996 Charkhi Dadri mid-air collision\">collide in mid-air</a> near New Delhi, killing 349 in the deadliest mid-air collision to date.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Saudi_Arabian_Airlines\" class=\"mw-redirect\" title=\"Saudi Arabian Airlines\">Saudi Arabian Airlines</a> <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a> and a Kazakh <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> cargo plane <a href=\"https://wikipedia.org/wiki/1996_Charkhi_Dadri_mid-air_collision\" title=\"1996 Charkhi Dadri mid-air collision\">collide in mid-air</a> near New Delhi, killing 349 in the deadliest mid-air collision to date.", "links": [{"title": "Saudi Arabian Airlines", "link": "https://wikipedia.org/wiki/Saudi_Arabian_Airlines"}, {"title": "Boeing 747", "link": "https://wikipedia.org/wiki/Boeing_747"}, {"title": "Ilyushin Il-76", "link": "https://wikipedia.org/wiki/Ilyushin_Il-76"}, {"title": "1996 Chark<PERSON> mid-air collision", "link": "https://wikipedia.org/wiki/1996_Charkhi_Dadri_mid-air_collision"}]}, {"year": "1997", "text": "<PERSON><PERSON> is found guilty of masterminding the 1993 World Trade Center bombing.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is found guilty of masterminding the <a href=\"https://wikipedia.org/wiki/1993_World_Trade_Center_bombing\" title=\"1993 World Trade Center bombing\">1993 World Trade Center bombing</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is found guilty of masterminding the <a href=\"https://wikipedia.org/wiki/1993_World_Trade_Center_bombing\" title=\"1993 World Trade Center bombing\">1993 World Trade Center bombing</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "1993 World Trade Center bombing", "link": "https://wikipedia.org/wiki/1993_World_Trade_Center_bombing"}]}, {"year": "1999", "text": "The 7.2 Mw  Düzce earthquake shakes northwestern Turkey with a maximum Mercalli intensity of IX (Violent). At least 845 people are killed and almost 5,000 are injured.", "html": "1999 - The 7.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1999_D%C3%BCzce_earthquake\" title=\"1999 Düzce earthquake\">Düzce earthquake</a> shakes northwestern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>). At least 845 people are killed and almost 5,000 are injured.", "no_year_html": "The 7.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1999_D%C3%BCzce_earthquake\" title=\"1999 Düzce earthquake\">Düzce earthquake</a> shakes northwestern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>). At least 845 people are killed and almost 5,000 are injured.", "links": [{"title": "1999 Düzce earthquake", "link": "https://wikipedia.org/wiki/1999_D%C3%BCzce_earthquake"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "2001", "text": "In New York City, American Airlines Flight 587, an Airbus A300 en route to the Dominican Republic, crashes minutes after takeoff from John F. Kennedy International Airport, killing all 260 on board and five on the ground.", "html": "2001 - In New York City, <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_587\" title=\"American Airlines Flight 587\">American Airlines Flight 587</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A300\" title=\"Airbus A300\">Airbus A300</a> <i>en route</i> to the <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a>, crashes minutes after takeoff from <a href=\"https://wikipedia.org/wiki/John_<PERSON>._Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">John F. Kennedy International Airport</a>, killing all 260 on board and five on the ground.", "no_year_html": "In New York City, <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_587\" title=\"American Airlines Flight 587\">American Airlines Flight 587</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A300\" title=\"Airbus A300\">Airbus A300</a> <i>en route</i> to the <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a>, crashes minutes after takeoff from <a href=\"https://wikipedia.org/wiki/John_<PERSON>._Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">John F. Kennedy International Airport</a>, killing all 260 on board and five on the ground.", "links": [{"title": "American Airlines Flight 587", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_587"}, {"title": "Airbus A300", "link": "https://wikipedia.org/wiki/Airbus_A300"}, {"title": "Dominican Republic", "link": "https://wikipedia.org/wiki/Dominican_Republic"}, {"title": "<PERSON> International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Kennedy_International_Airport"}]}, {"year": "2001", "text": "War in Afghanistan: Taliban forces abandon Kabul, ahead of advancing Afghan Northern Alliance troops.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)#Fall_of_Kabul\" title=\"War in Afghanistan (2001-2021)\">War in Afghanistan</a>: <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> forces abandon <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>, ahead of advancing <a href=\"https://wikipedia.org/wiki/Afghan_Northern_Alliance\" class=\"mw-redirect\" title=\"Afghan Northern Alliance\">Afghan Northern Alliance</a> troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)#Fall_of_Kabul\" title=\"War in Afghanistan (2001-2021)\">War in Afghanistan</a>: <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> forces abandon <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>, ahead of advancing <a href=\"https://wikipedia.org/wiki/Afghan_Northern_Alliance\" class=\"mw-redirect\" title=\"Afghan Northern Alliance\">Afghan Northern Alliance</a> troops.", "links": [{"title": "War in Afghanistan (2001-2021)", "link": "https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)#Fall_of_Kabul"}, {"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}, {"title": "Kabul", "link": "https://wikipedia.org/wiki/Kabul"}, {"title": "Afghan Northern Alliance", "link": "https://wikipedia.org/wiki/Afghan_Northern_Alliance"}]}, {"year": "2003", "text": "Iraq War: In Nasiriyah, Iraq, at least 23 people, among them the first Italian casualties of the 2003 invasion of Iraq, are killed in a suicide bomb attack on an Italian police base.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: In <a href=\"https://wikipedia.org/wiki/Nasiriyah\" title=\"Nasiriyah\">Nasiriyah</a>, Iraq, at least 23 people, among them the first Italian casualties of the <a href=\"https://wikipedia.org/wiki/2003_invasion_of_Iraq\" title=\"2003 invasion of Iraq\">2003 invasion of Iraq</a>, are killed in a <a href=\"https://wikipedia.org/wiki/2003_Nasiriyah_bombing\" title=\"2003 Nasiriyah bombing\">suicide bomb attack</a> on an Italian police base.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: In <a href=\"https://wikipedia.org/wiki/Nasiriyah\" title=\"Nasiriyah\">Nasiriyah</a>, Iraq, at least 23 people, among them the first Italian casualties of the <a href=\"https://wikipedia.org/wiki/2003_invasion_of_Iraq\" title=\"2003 invasion of Iraq\">2003 invasion of Iraq</a>, are killed in a <a href=\"https://wikipedia.org/wiki/2003_Nasiriyah_bombing\" title=\"2003 Nasiriyah bombing\">suicide bomb attack</a> on an Italian police base.", "links": [{"title": "Iraq War", "link": "https://wikipedia.org/wiki/Iraq_War"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nasiriyah"}, {"title": "2003 invasion of Iraq", "link": "https://wikipedia.org/wiki/2003_invasion_of_Iraq"}, {"title": "2003 Nasiriyah bombing", "link": "https://wikipedia.org/wiki/2003_Nasiriyah_bombing"}]}, {"year": "2003", "text": "Shanghai Transrapid sets a new world speed record of 501 kilometres per hour (311 mph) for commercial railway systems, which remains the fastest for unmodified commercial rail vehicles.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Shanghai_Transrapid\" class=\"mw-redirect\" title=\"Shanghai Transrapid\">Shanghai Transrapid</a> sets a new <a href=\"https://wikipedia.org/wiki/Land_speed_record_for_rail_vehicles\" class=\"mw-redirect\" title=\"Land speed record for rail vehicles\">world speed record</a> of 501 kilometres per hour (311 mph) for commercial railway systems, which remains the fastest for unmodified commercial rail vehicles.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shanghai_Transrapid\" class=\"mw-redirect\" title=\"Shanghai Transrapid\">Shanghai Transrapid</a> sets a new <a href=\"https://wikipedia.org/wiki/Land_speed_record_for_rail_vehicles\" class=\"mw-redirect\" title=\"Land speed record for rail vehicles\">world speed record</a> of 501 kilometres per hour (311 mph) for commercial railway systems, which remains the fastest for unmodified commercial rail vehicles.", "links": [{"title": "Shanghai Transrapid", "link": "https://wikipedia.org/wiki/Shanghai_Transrapid"}, {"title": "Land speed record for rail vehicles", "link": "https://wikipedia.org/wiki/Land_speed_record_for_rail_vehicles"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON> tenders his resignation as Prime Minister of Italy, effective November 16, due in large part to the European sovereign debt crisis.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> tenders his resignation as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>, effective November 16, due in large part to the <a href=\"https://wikipedia.org/wiki/European_sovereign_debt_crisis\" class=\"mw-redirect\" title=\"European sovereign debt crisis\">European sovereign debt crisis</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> tenders his resignation as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>, effective November 16, due in large part to the <a href=\"https://wikipedia.org/wiki/European_sovereign_debt_crisis\" class=\"mw-redirect\" title=\"European sovereign debt crisis\">European sovereign debt crisis</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}, {"title": "European sovereign debt crisis", "link": "https://wikipedia.org/wiki/European_sovereign_debt_crisis"}]}, {"year": "2011", "text": "A blast in Iran's Shahid <PERSON> missile base leads to the death of 17 of the Revolutionary Guards members, including <PERSON>, a key figure in Iran's missile program.", "html": "2011 - A <a href=\"https://wikipedia.org/wiki/B<PERSON>_<PERSON>_explosion\" title=\"<PERSON><PERSON> <PERSON> explosion\">blast</a> in Iran's Shahid Modarres missile base leads to the death of 17 of the <a href=\"https://wikipedia.org/wiki/IRGC\" class=\"mw-redirect\" title=\"IRGC\">Revolutionary Guards</a> members, including <a href=\"https://wikipedia.org/wiki/Hassan_Tehrani_Moghaddam\" title=\"Hassan <PERSON>\"><PERSON></a>, a key figure in Iran's missile program.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/B<PERSON>_<PERSON><PERSON>_explosion\" title=\"B<PERSON> <PERSON> explosion\">blast</a> in Iran's Shahid Modarres missile base leads to the death of 17 of the <a href=\"https://wikipedia.org/wiki/IRGC\" class=\"mw-redirect\" title=\"IRGC\">Revolutionary Guards</a> members, including <a href=\"https://wikipedia.org/wiki/Hassan_Tehrani_Moghaddam\" title=\"Hassan <PERSON>\"><PERSON></a>, a key figure in Iran's missile program.", "links": [{"title": "B<PERSON> explosion", "link": "https://wikipedia.org/wiki/Bid_<PERSON><PERSON>_explosion"}, {"title": "IRGC", "link": "https://wikipedia.org/wiki/IRGC"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hassan_Tehrani_<PERSON>ghaddam"}]}, {"year": "2014", "text": "The Philae lander, deployed from the European Space Agency's <PERSON>tta probe, reaches the surface of Comet 67P/Churyumov-Gerasimenko.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON> (spacecraft)\"><PERSON><PERSON> lander</a>, deployed from the <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a>'s <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON> (spacecraft)\"><PERSON><PERSON> probe</a>, reaches the surface of Comet <a href=\"https://wikipedia.org/wiki/67P/Churyumov%E2%80%93Gerasimenko\" title=\"67P/Chu<PERSON>umov-Gerasimenko\">67P/<PERSON><PERSON><PERSON>ov-Gerasimenko</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON> (spacecraft)\"><PERSON><PERSON> lander</a>, deployed from the <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a>'s <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON> (spacecraft)\"><PERSON><PERSON> probe</a>, reaches the surface of Comet <a href=\"https://wikipedia.org/wiki/67P/Churyumov%E2%80%93Gerasimenko\" title=\"67P/Churyumov-Gerasimenko\">67P/<PERSON><PERSON>umov-Gerasimenko</a>.", "links": [{"title": "<PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON>ae_(spacecraft)"}, {"title": "European Space Agency", "link": "https://wikipedia.org/wiki/European_Space_Agency"}, {"title": "<PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(spacecraft)"}, {"title": "67P/Churyumov-Gerasimenko", "link": "https://wikipedia.org/wiki/67P/Churyumov%E2%80%93Gerasimenko"}]}, {"year": "2014", "text": "An Armenian Mil Mi-24 attack helicopter is shot down by Azerbaijani forces, killing all three people on board.", "html": "2014 - An Armenian <a href=\"https://wikipedia.org/wiki/Mil_Mi-24\" title=\"Mil Mi-24\">Mil Mi-24</a> attack helicopter is <a href=\"https://wikipedia.org/wiki/2014_Armenian_Mil_Mi-24_shootdown\" title=\"2014 Armenian Mil Mi-24 shootdown\">shot down</a> by Azerbaijani forces, killing all three people on board.", "no_year_html": "An Armenian <a href=\"https://wikipedia.org/wiki/Mil_Mi-24\" title=\"Mil Mi-24\">Mil Mi-24</a> attack helicopter is <a href=\"https://wikipedia.org/wiki/2014_Armenian_Mil_Mi-24_shootdown\" title=\"2014 Armenian Mil Mi-24 shootdown\">shot down</a> by Azerbaijani forces, killing all three people on board.", "links": [{"title": "Mil Mi-24", "link": "https://wikipedia.org/wiki/Mil_Mi-24"}, {"title": "2014 Armenian Mil Mi-24 shootdown", "link": "https://wikipedia.org/wiki/2014_Armenian_Mil_Mi-24_shootdown"}]}, {"year": "2015", "text": " Two suicide bombers detonate explosives in Bourj el-Barajneh, Beirut, killing 43 people and injuring over 200 others.", "html": "2015 - Two <a href=\"https://wikipedia.org/wiki/Suicide_bomber\" class=\"mw-redirect\" title=\"Suicide bomber\">suicide bombers</a> <a href=\"https://wikipedia.org/wiki/2015_Beirut_bombings\" title=\"2015 Beirut bombings\">detonate explosives</a> in <a href=\"https://wikipedia.org/wiki/Bourj_el-Barajneh\" title=\"Bourj el-Barajneh\"><PERSON><PERSON><PERSON>-<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>, killing 43 people and injuring over 200 others.", "no_year_html": "Two <a href=\"https://wikipedia.org/wiki/Suicide_bomber\" class=\"mw-redirect\" title=\"Suicide bomber\">suicide bombers</a> <a href=\"https://wikipedia.org/wiki/2015_Beirut_bombings\" title=\"2015 Beirut bombings\">detonate explosives</a> in <a href=\"https://wikipedia.org/wiki/Bourj_el-Barajneh\" title=\"Bourj el-Barajneh\"><PERSON><PERSON><PERSON>-<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>, killing 43 people and injuring over 200 others.", "links": [{"title": "Suicide bomber", "link": "https://wikipedia.org/wiki/Suicide_bomber"}, {"title": "2015 Beirut bombings", "link": "https://wikipedia.org/wiki/2015_Beirut_bombings"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}]}, {"year": "2017", "text": "The 7.3 Mw  Kermanshah earthquake shakes the northern Iran-Iraq border with a maximum Mercalli intensity of VIII (Severe). At least 410 people are killed and over 7,000 are injured.", "html": "2017 - The 7.3 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2017_Kermanshah_earthquake\" class=\"mw-redirect\" title=\"2017 Kermanshah earthquake\">Kermanshah earthquake</a> shakes the northern <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>-<a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> border with a maximum <a href=\"https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale\" title=\"Modified Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>). At least 410 people are killed and over 7,000 are injured.", "no_year_html": "The 7.3 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2017_Kermanshah_earthquake\" class=\"mw-redirect\" title=\"2017 Kermanshah earthquake\">Kermanshah earthquake</a> shakes the northern <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>-<a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> border with a maximum <a href=\"https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale\" title=\"Modified Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>). At least 410 people are killed and over 7,000 are injured.", "links": [{"title": "2017 Kermanshah earthquake", "link": "https://wikipedia.org/wiki/2017_Kermanshah_earthquake"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Modified Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale"}]}, {"year": "2021", "text": "The Los Angeles Superior Court formally ends the 14-year conservatorship to pop singer <PERSON><PERSON><PERSON>.", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/Los_Angeles_Superior_Court\" class=\"mw-redirect\" title=\"Los Angeles Superior Court\">Los Angeles Superior Court</a> formally ends <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Spears_conservatorship_dispute\" class=\"mw-redirect\" title=\"Britney Spears conservatorship dispute\">the 14-year conservatorship</a> to pop singer <a href=\"https://wikipedia.org/wiki/Britney_Spears\" title=\"Britney Spears\"><PERSON><PERSON><PERSON> <PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Los_Angeles_Superior_Court\" class=\"mw-redirect\" title=\"Los Angeles Superior Court\">Los Angeles Superior Court</a> formally ends <a href=\"https://wikipedia.org/wiki/B<PERSON>ney_Spears_conservatorship_dispute\" class=\"mw-redirect\" title=\"Britney Spears conservatorship dispute\">the 14-year conservatorship</a> to pop singer <a href=\"https://wikipedia.org/wiki/Britney_Spears\" title=\"Britney Spears\">Brit<PERSON> <PERSON></a>.", "links": [{"title": "Los Angeles Superior Court", "link": "https://wikipedia.org/wiki/Los_Angeles_Superior_Court"}, {"title": "Britney Spears conservatorship dispute", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_conservatorship_dispute"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "A Boeing B-17 Flying Fortress and a Bell P-63 Kingcobra collide in mid-air over Dallas Executive Airport during an airshow, killing six.", "html": "2022 - A <a href=\"https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress\" title=\"Boeing B-17 Flying Fortress\">Boeing B-17 Flying Fortress</a> and a <a href=\"https://wikipedia.org/wiki/Bell_P-63_Kingcobra\" title=\"Bell P-63 Kingcobra\">Bell P-63 Kingcobra</a> <a href=\"https://wikipedia.org/wiki/2022_Dallas_air_show_mid-air_collision\" title=\"2022 Dallas air show mid-air collision\">collide in mid-air</a> over <a href=\"https://wikipedia.org/wiki/Dallas_Executive_Airport\" title=\"Dallas Executive Airport\">Dallas Executive Airport</a> during an airshow, killing six.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress\" title=\"Boeing B-17 Flying Fortress\">Boeing B-17 Flying Fortress</a> and a <a href=\"https://wikipedia.org/wiki/Bell_P-63_Kingcobra\" title=\"Bell P-63 Kingcobra\">Bell P-63 Kingcobra</a> <a href=\"https://wikipedia.org/wiki/2022_Dallas_air_show_mid-air_collision\" title=\"2022 Dallas air show mid-air collision\">collide in mid-air</a> over <a href=\"https://wikipedia.org/wiki/Dallas_Executive_Airport\" title=\"Dallas Executive Airport\">Dallas Executive Airport</a> during an airshow, killing six.", "links": [{"title": "Boeing B-17 Flying Fortress", "link": "https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress"}, {"title": "Bell P-63 Kingcobra", "link": "https://wikipedia.org/wiki/Bell_P-63_Kingcobra"}, {"title": "2022 Dallas air show mid-air collision", "link": "https://wikipedia.org/wiki/2022_Dallas_air_show_mid-air_collision"}, {"title": "Dallas Executive Airport", "link": "https://wikipedia.org/wiki/Dallas_Executive_Airport"}]}], "Births": [{"year": "1450", "text": "<PERSON> of Savoy, Count of Romont, Prince of Savoy (d. 1486)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy,_Count_of_Romont\" title=\"<PERSON> of Savoy, Count of Romont\"><PERSON> of Savoy, Count of Romont</a>, Prince of Savoy (d. 1486)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy,_Count_of_Romont\" title=\"<PERSON> of Savoy, Count of Romont\"><PERSON> of Savoy, Count of Romont</a>, Prince of Savoy (d. 1486)", "links": [{"title": "<PERSON> of Savoy, Count of Romont", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_<PERSON>_Romont"}]}, {"year": "1492", "text": "<PERSON>, German general (d. 1565)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1565)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1494", "text": "<PERSON> of Anhalt-<PERSON><PERSON><PERSON><PERSON>, Princess of Anhalt by birth, by marriage Duchess of Saxony (d. 1521)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Anhalt-K%C3%B6then\" title=\"Margaret of Anhalt-Köthen\"><PERSON> of Anhalt-Köthen</a>, Princess of Anhalt by birth, by marriage Duchess of Saxony (d. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Anhalt-K%C3%B6then\" title=\"Margaret of Anhalt-Köthen\">Margaret of Anhalt-Köthen</a>, Princess of Anhalt by birth, by marriage Duchess of Saxony (d. 1521)", "links": [{"title": "Margaret of Anhalt-Köthen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anhalt-K%C3%B6then"}]}, {"year": "1528", "text": "<PERSON>, Chinese general (d. 1588)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (d. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (d. 1588)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Qi_<PERSON>ng"}]}, {"year": "1547", "text": "<PERSON> of Valois, French princess (d. 1575)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Val<PERSON>\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, French princess (d. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, French princess (d. 1575)", "links": [{"title": "<PERSON> Valois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1579", "text": "<PERSON><PERSON> of Hanau-Münzenberg, German nobleman (d. 1635)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hanau-M%C3%BCnzenberg\" title=\"<PERSON><PERSON> of Hanau-Münzenberg\"><PERSON><PERSON> of Hanau-Münzenberg</a>, German nobleman (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hanau-M%C3%BCnzenberg\" title=\"<PERSON><PERSON> of Hanau-Münzenberg\"><PERSON><PERSON> of Hanau-Münzenberg</a>, German nobleman (d. 1635)", "links": [{"title": "<PERSON><PERSON> of Hanau-Münzenberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hanau-M%C3%BCnzenberg"}]}, {"year": "1606", "text": "<PERSON>, French-Canadian nurse, founded the Hôtel-Dieu de Montréal (d. 1673)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian nurse, founded the <a href=\"https://wikipedia.org/wiki/H%C3%B4tel-Dieu_de_Montr%C3%A9al\" title=\"Hôtel-Dieu de Montréal\">Hôtel-Dieu de Montréal</a> (d. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian nurse, founded the <a href=\"https://wikipedia.org/wiki/H%C3%B4tel-Dieu_de_Montr%C3%A9al\" title=\"Hôtel-Dieu de Montréal\">Hôtel-Dieu de Montréal</a> (d. 1673)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hôtel-Dieu de Montréal", "link": "https://wikipedia.org/wiki/H%C3%B4tel-Dieu_de_Montr%C3%A9al"}]}, {"year": "1615", "text": "<PERSON>, English minister, poet, and theologian (d. 1691)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister, poet, and theologian (d. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister, poet, and theologian (d. 1691)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1627", "text": "<PERSON>, Spanish Jesuit missionary (d. 1672)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_San_Vitores\" title=\"Diego Luis de San Vitores\"><PERSON>itor<PERSON></a>, Spanish Jesuit missionary (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Vitores\" title=\"Diego Luis de San Vitores\"><PERSON> Vitores</a>, Spanish Jesuit missionary (d. 1672)", "links": [{"title": "Diego <PERSON> San Vitores", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Vitor<PERSON>"}]}, {"year": "1651", "text": "<PERSON><PERSON>, Mexican nun, poet, and scholar (d. 1695)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/Juana_In%C3%A9s_de_la_Cruz\" title=\"Juana Inés de la Cruz\"><PERSON><PERSON></a>, Mexican nun, poet, and scholar (d. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juana_In%C3%A9s_de_la_Cruz\" title=\"Juana Inés de la Cruz\"><PERSON><PERSON></a>, Mexican nun, poet, and scholar (d. 1695)", "links": [{"title": "Juana Inés de la Cruz", "link": "https://wikipedia.org/wiki/Juana_In%C3%A9s_de_la_Cruz"}]}, {"year": "1655", "text": "<PERSON>, British Army general and colonial administrator (d. 1727)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Army general and colonial administrator (d. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Army general and colonial administrator (d. 1727)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1684", "text": "<PERSON>, English admiral and politician (d. 1757)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (d. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, French admiral and explorer (d. 1811)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral and explorer (d. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral and explorer (d. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, Prussian general and politician, Prussian Minister of War (d. 1813)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian general and politician, <a href=\"https://wikipedia.org/wiki/Prussian_Minister_of_War\" class=\"mw-redirect\" title=\"Prussian Minister of War\">Prussian Minister of War</a> (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian general and politician, <a href=\"https://wikipedia.org/wiki/Prussian_Minister_of_War\" class=\"mw-redirect\" title=\"Prussian Minister of War\">Prussian Minister of War</a> (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prussian Minister of War", "link": "https://wikipedia.org/wiki/Prussian_Minister_of_War"}]}, {"year": "1774", "text": "<PERSON>, Scottish surgeon and artist  (d. 1842)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and artist (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and artist (d. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1780", "text": "<PERSON><PERSON>, South African ruler (d. 1838)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/Piet_Retief\" title=\"Piet Retief\"><PERSON><PERSON></a>, South African ruler (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pie<PERSON>_Retief\" title=\"Piet Retief\"><PERSON><PERSON></a>, South African ruler (d. 1838)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pie<PERSON>_Retief"}]}, {"year": "1793", "text": "<PERSON>, Livonian physician and botanist (d. 1831)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Livonian physician and botanist (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Livonian physician and botanist (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON><PERSON><PERSON><PERSON>, American entomologist and botanist (d. 1856)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/Thad<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Thad<PERSON><PERSON>\">Thad<PERSON><PERSON></a>, American entomologist and botanist (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thad<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Thad<PERSON><PERSON>\">Thad<PERSON><PERSON></a>, American entomologist and botanist (d. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thad<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, American activist (d. 1902)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Persian spiritual leader, founded the Baháʼí Faith (d. 1892)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%27u%27ll%C3%A1h\" class=\"mw-redirect\" title=\"Bahá'u'll<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian spiritual leader, founded the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bah%C3%A1%27u%27ll%C3%A1h\" class=\"mw-redirect\" title=\"Bahá'u'll<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian spiritual leader, founded the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Bahá<PERSON>í <PERSON></a> (d. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bah%C3%A1%27u%27ll%C3%A1h"}, {"title": "Baháʼí Faith", "link": "https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith"}]}, {"year": "1833", "text": "<PERSON>, Russian composer and chemist (d. 1887)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and chemist (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and chemist (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, French sculptor and illustrator, created <PERSON> Thinker (d. 1917)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and illustrator, created <a href=\"https://wikipedia.org/wiki/The_Thinker\" title=\"The Thinker\">The Thinker</a> (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and illustrator, created <a href=\"https://wikipedia.org/wiki/The_Thinker\" title=\"The Thinker\">The Thinker</a> (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Thinker", "link": "https://wikipedia.org/wiki/The_Thinker"}]}, {"year": "1842", "text": "<PERSON>, 3rd Baron <PERSON>, English physicist and academic, Nobel Prize laureate (d. 1919)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1919)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1848", "text": "<PERSON>, Swiss lawyer and politician, 51st President of the Swiss Confederation (d. 1919)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(Swiss_politician)\" title=\"<PERSON> (Swiss politician)\"><PERSON></a>, Swiss lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(Swiss_politician)\" title=\"<PERSON> (Swiss politician)\"><PERSON></a>, Swiss lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1919)", "links": [{"title": "<PERSON> (Swiss politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(Swiss_politician)"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "1850", "text": "<PERSON>, Russian chess player and theoretician (d. 1908)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and theoretician (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and theoretician (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Chinese physician and politician, 1st President of the Republic of China (d. 1925)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Yat-sen\" title=\"<PERSON> Yat-sen\"><PERSON></a>, Chinese physician and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Yat-sen\" title=\"<PERSON> Yat-sen\"><PERSON></a>, Chinese physician and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (d. 1925)", "links": [{"title": "<PERSON>sen", "link": "https://wikipedia.org/wiki/Sun_Yat-sen"}, {"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}]}, {"year": "1872", "text": "<PERSON>, Irish actor and producer (d. 1947)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor and producer (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor and producer (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON>, Estonian-Swedish architect (d. 1948)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Ole<PERSON>_<PERSON>\" title=\"Ole<PERSON> Siin<PERSON>\"><PERSON><PERSON></a>, Estonian-Swedish architect (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ole<PERSON> Siin<PERSON>\"><PERSON><PERSON></a>, Estonian-Swedish architect (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ole<PERSON>_<PERSON>a"}]}, {"year": "1881", "text": "<PERSON>, German field marshal (d. 1954)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON><PERSON>, German geologist and mountaineer (d. 1975)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_D<PERSON>hrenfurth\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German geologist and mountaineer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_D<PERSON>furth\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German geologist and mountaineer (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, English author and playwright (d. 1980)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Travers\"><PERSON></a>, English author and playwright (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Travers\"><PERSON></a>, English author and playwright (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, American publisher and philanthropist, co-founded Reader's Digest (d. 1981)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American publisher and philanthropist, co-founded <i><a href=\"https://wikipedia.org/wiki/Reader%27s_Digest\" title=\"Reader's Digest\">Reader's Digest</a></i> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American publisher and philanthropist, co-founded <i><a href=\"https://wikipedia.org/wiki/Reader%27s_Digest\" title=\"Reader's Digest\">Reader's Digest</a></i> (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Reader's Digest", "link": "https://wikipedia.org/wiki/Reader%27s_Digest"}]}, {"year": "1890", "text": "<PERSON>, Hungarian figure skater (d. 1974)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian figure skater (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian figure skater (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Welsh tenor and actor (d. 1958)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh tenor and actor (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Davies\"><PERSON></a>, Welsh tenor and actor (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian zoologist and comparative psychologist (d. 1976)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian zoologist and comparative psychologist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian zoologist and comparative psychologist (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Spanish tennis player (d. 1984)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Australian zoologist (d. 1982)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, Australian zoologist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, Australian zoologist (d. 1982)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(scientist)"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Iranian poet and academic (d. 1960)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian poet and academic (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian poet and academic (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>j"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Indian ornithologist and author (d. 1987)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian ornithologist and author (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian ornithologist and author (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German composer and conductor (d. 1985)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, German composer and conductor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, German composer and conductor (d. 1985)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1898", "text": "<PERSON>, Slovenian gymnast (d. 1999)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0tukelj\" title=\"<PERSON>\"><PERSON></a>, Slovenian gymnast (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0tukelj\" title=\"<PERSON>\"><PERSON></a>, Slovenian gymnast (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leon_%C5%A0tukelj"}]}, {"year": "1900", "text": "<PERSON>, New Zealand mass murderer (d. 1941)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand mass murderer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand mass murderer (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American minister and theologian (d. 1994)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and theologian (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and theologian (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American actor (d. 1978)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Austrian-born car importer and businessman (d. 1981)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born car importer and businessman (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born car importer and businessman (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American pilot (d. 1979)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American soldier and poet (d. 1968)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American soldier and poet (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American soldier and poet (d. 1968)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1908", "text": "<PERSON>, American lawyer and judge (d. 1999)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, South African cricketer (d. 1981)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American trumpet player and academic (d. 1991)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and academic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and academic (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, French philosopher, theorist, and critic (d. 1980)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher, theorist, and critic (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher, theorist, and critic (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English racing driver (d. 1993)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Canadian composer and academic (d. 2000)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American singer (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Slovenian film director and screenwriter (d. 1993)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/France_%C5%A0tiglic\" title=\"France Štiglic\">France Štiglic</a>, Slovenian film director and screenwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France_%C5%A0tiglic\" title=\"France Štiglic\"><PERSON> Štigli<PERSON></a>, Slovenian film director and screenwriter (d. 1993)", "links": [{"title": "France Štiglic", "link": "https://wikipedia.org/wiki/France_%C5%A0tiglic"}]}, {"year": "1920", "text": "<PERSON>, American actor, director, and screenwriter (d. 1989)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Polish poet, author, and journalist (d. 1951)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet, author, and journalist (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet, author, and journalist (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actress (d. 2002)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunter\"><PERSON></a>, American actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English archaeologist and explorer (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and explorer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and explorer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, German humorist, actor, and director (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German humorist, actor, and director (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German humorist, actor, and director (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Mexican poet and scholar (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Bonifaz_Nu%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican poet and scholar (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Bonifaz_Nu%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican poet and scholar (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Bonifaz_Nu%C3%B1o"}]}, {"year": "1924", "text": "<PERSON>, American bassist, cellist, and composer (d. 1981)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bassist, cellist, and composer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bassist, cellist, and composer (d. 1981)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1926", "text": "<PERSON>, Baron <PERSON> of Chieveley, English lawyer and judge (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON><PERSON>_of_Chieveley\" title=\"<PERSON>, Baron <PERSON> of Chieveley\"><PERSON>, Baron <PERSON> of Chieveley</a>, English lawyer and judge (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON><PERSON>_of_Chieveley\" title=\"<PERSON>, Baron <PERSON> of Chieveley\"><PERSON>, Baron <PERSON> of Chieveley</a>, English lawyer and judge (d. 2016)", "links": [{"title": "<PERSON>, Baron <PERSON> of Chieveley", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Chieveley"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech motorcycle racer and sportscaster (d. 2000)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_%C5%A0%C5%A5astn%C3%BD\" title=\"Františ<PERSON>ný\"><PERSON><PERSON><PERSON><PERSON></a>, Czech motorcycle racer and sportscaster (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_%C5%A0%C5%A5astn%C3%BD\" title=\"<PERSON>anti<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech motorcycle racer and sportscaster (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_%C5%A0%C5%A5astn%C3%BD"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Japanese mathematician and theorist (d. 1958)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mathematician and theorist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mathematician and theorist (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German author and fiction writer (d. 1995)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and fiction writer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and fiction writer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American actress, later Princess <PERSON> of Monaco (d. 1982)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, later Princess <PERSON> of Monaco (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, later Princess <PERSON> of Monaco (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American singer-songwriter and producer (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American cult leader (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Irish author and educator (d. 2006)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author and educator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author and educator (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and manager (d. 2002)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Vav%C3%A1\" title=\"Vav<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vav%C3%A1\" title=\"Vav<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2002)", "links": [{"title": "Vavá", "link": "https://wikipedia.org/wiki/Vav%C3%A1"}]}, {"year": "1937", "text": "<PERSON>, NASA astronaut (d. 2024)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ruly\" title=\"<PERSON>\"><PERSON></a>, NASA astronaut (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ruly\" title=\"<PERSON>\"><PERSON></a>, NASA astronaut (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ruly"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American diplomat (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American diplomat (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American diplomat (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Tanzanian journalist and politician, 3rd President of Tanzania (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Tanzania\" class=\"mw-redirect\" title=\"President of Tanzania\">President of Tanzania</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Tanzania\" class=\"mw-redirect\" title=\"President of Tanzania\">President of Tanzania</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Tanzania", "link": "https://wikipedia.org/wiki/President_of_Tanzania"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American singer-songwriter and pianist (d. 1991)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Slovak soprano (d. 1993)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak soprano (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak soprano (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian economist and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Indian actor & director (d. 1992)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Indian actor &amp; director (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Indian actor &amp; director (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actor)"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, German judge and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_Todenh%C3%B6fer\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German judge and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_Todenh%C3%B6fer\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German judge and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCrgen_Todenh%C3%B6fer"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Jamaican-English singer-songwriter (d. 2015)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican-English singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican-English singer-songwriter (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American pop singer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor, comedian and playwright", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish racing driver (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B6rn_Waldeg%C3%A5rd\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish racing driver (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B6rn_Waldeg%C3%A5rd\" title=\"<PERSON><PERSON>ö<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish racing driver (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B6rn_Waldeg%C3%A5rd"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2011)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1944", "text": "<PERSON>, American football player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American pianist, saxophonist, songwriter, and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, saxophonist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, saxophonist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American sportscaster", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American author and educator (d. 2023)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and educator (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and educator (d. 2023)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1945", "text": "<PERSON>, American mathematician and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian singer-songwriter, guitarist, and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Swedish businesswoman", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Buck Dharma\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, French director and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French director and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Iranian lawyer and politician; 7th President of Iran", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian lawyer and politician; 7th <a href=\"https://wikipedia.org/wiki/President_of_Iran\" title=\"President of Iran\">President of Iran</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian lawyer and politician; 7th <a href=\"https://wikipedia.org/wiki/President_of_Iran\" title=\"President of Iran\">President of Iran</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Iran", "link": "https://wikipedia.org/wiki/President_of_Iran"}]}, {"year": "1949", "text": "<PERSON>, Canadian ice hockey player and coach (d. 1992)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American soldier and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Rhode_Island_politician)\" title=\"<PERSON> (Rhode Island politician)\"><PERSON></a>, American soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Rhode_Island_politician)\" title=\"<PERSON> (Rhode Island politician)\"><PERSON></a>, American soldier and politician", "links": [{"title": "<PERSON> (Rhode Island politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Rhode_Island_politician)"}]}, {"year": "1950", "text": "<PERSON>, American country and gospel singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country and gospel singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country and gospel singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Senegalese singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ba<PERSON> Maal\"><PERSON><PERSON></a>, Senegalese singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ba<PERSON> Maal\"><PERSON><PERSON></a>, Senegalese singer-songwriter and guitarist", "links": [{"title": "<PERSON>aba <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al"}]}, {"year": "1954", "text": "<PERSON>, Australian tennis player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Scottish pop singer (d. 2021)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish pop singer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish pop singer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American engineer, storm chaser (d. 2013)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, storm chaser (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, storm chaser (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Croatian politician and economist (d. 2023)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0uker\" title=\"<PERSON>\"><PERSON></a>, Croatian politician and economist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0uker\" title=\"<PERSON>\"><PERSON></a>, Croatian politician and economist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ivan_%C5%A0uker"}]}, {"year": "1958", "text": "<PERSON>, American actress and singer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Ukrainian race walker", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON></a>, Ukrainian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON></a>, Ukrainian race walker", "links": [{"title": "<PERSON><PERSON><PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(athlete)"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Japanese composer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Belgian singer and actress (d. 2018)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian singer and actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian singer and actress (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ne"}]}, {"year": "1961", "text": "<PERSON>, Romanian gymnast and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83neci\" title=\"<PERSON>\"><PERSON></a>, Romanian gymnast and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83neci\" title=\"<PERSON>\"><PERSON></a>, Romanian gymnast and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nadia_Com%C4%83neci"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Uruguayan footballer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Japanese actress and television host", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and television host", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American porn actor, director, and producer (d. 2006)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American porn actor, director, and producer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American porn actor, director, and producer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, British journalist and actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British journalist and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British journalist and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian ice hockey player, coach, and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player, coach, and manager", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1962", "text": "<PERSON>, American author and poet", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American author and activist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wolf\"><PERSON></a>, American author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2009)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American bass player and songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Taiwanese baseball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hui\" title=\"<PERSON>-h<PERSON>\"><PERSON></a>, Taiwanese baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, Taiwanese baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hui"}]}, {"year": "1964", "text": "<PERSON>, German musicologist, church musician and writer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Barbara_St%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German musicologist, church musician and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barbara_St%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German musicologist, church musician and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barbara_St%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American voice actor and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Iraqi Eulogy Reciter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>Karbalaei\" class=\"mw-redirect\" title=\"<PERSON><PERSON>ala<PERSON>\"><PERSON><PERSON></a>, Iraqi Eulogy Reciter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rbalaei\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Al<PERSON>Karbalaei\"><PERSON><PERSON></a>, Iraqi Eulogy Reciter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>alaei"}]}, {"year": "1967", "text": "Disco Inferno, American wrestler and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Disco_Inferno_(wrestler)\" title=\"Disco Inferno (wrestler)\">Disco Inferno</a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Disco_Inferno_(wrestler)\" title=\"Disco Inferno (wrestler)\">Disco Inferno</a>, American wrestler and manager", "links": [{"title": "Disco Inferno (wrestler)", "link": "https://wikipedia.org/wiki/Disco_Inferno_(wrestler)"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Belarusian journalist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American boxer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Welsh singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Dominican-American baseball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English-German singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-German singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American political scientist and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English footballer and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American writer and artist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American wrestler, model, and dancer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(wrestling)\" title=\"<PERSON><PERSON><PERSON> (wrestling)\"><PERSON><PERSON><PERSON></a>, American wrestler, model, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(wrestling)\" title=\"<PERSON><PERSON><PERSON> (wrestling)\"><PERSON><PERSON><PERSON></a>, American wrestler, model, and dancer", "links": [{"title": "<PERSON><PERSON><PERSON> (wrestling)", "link": "https://wikipedia.org/wiki/Elektra_(wrestling)"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American figure skater", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, French-Argentine composer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Argentine composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Argentine composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Chinese-American lawyer and activist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American lawyer and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American lawyer and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chen_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Vass<PERSON><PERSON>_T<PERSON>rtas\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> T<PERSON>rtas\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> T<PERSON>rtas\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Australian actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American pro soccer player and Survivor: Africa winner", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/United_States_Soccer_Federation\" title=\"United States Soccer Federation\">pro soccer</a> player and <i><a href=\"https://wikipedia.org/wiki/Survivor:_Africa\" title=\"Survivor: Africa\">Survivor: Africa</a></i> winner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/United_States_Soccer_Federation\" title=\"United States Soccer Federation\">pro soccer</a> player and <i><a href=\"https://wikipedia.org/wiki/Survivor:_Africa\" title=\"Survivor: Africa\">Survivor: Africa</a></i> winner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Soccer Federation", "link": "https://wikipedia.org/wiki/United_States_Soccer_Federation"}, {"title": "Survivor: Africa", "link": "https://wikipedia.org/wiki/Survivor:_Africa"}]}, {"year": "1974", "text": "<PERSON>, Italian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jones\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jones\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Canadian mountain biker", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian mountain biker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> B<PERSON>\"><PERSON><PERSON></a>, Canadian mountain biker", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American swimmer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American politician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American R&B singer-songwriter and actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American R&amp;B singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American R&amp;B singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, German singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American author and educator", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Polish footballer and journalist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Miros%C5%82aw_S<PERSON>mkowiak\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miros%C5%82aw_S<PERSON>mkowiak\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miros%C5%82aw_<PERSON><PERSON>m<PERSON>wiak"}]}, {"year": "1977", "text": "<PERSON><PERSON>, South African footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English mixed martial artist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Romanian-German actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-German actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-German actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)"}]}, {"year": "1978", "text": "<PERSON>, American actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1979", "text": "<PERSON>, American wrestler and trainer (d. 2018)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Chilean actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Cote de <PERSON>\"><PERSON><PERSON></a>, Chilean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Cote de <PERSON>\"><PERSON><PERSON></a>, Chilean actress", "links": [{"title": "Cote de Pablo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American golfer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American basketball player and sportscaster", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ette"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer and umpire", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American bass player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, German-Turkish journalist and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Nur_Fettaho%C4%9Flu\" title=\"<PERSON><PERSON> Fettahoğlu\"><PERSON><PERSON></a>, German-Turkish journalist and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nur_Fettaho%C4%9Flu\" title=\"<PERSON><PERSON> Fettahoğlu\"><PERSON><PERSON></a>, German-Turkish journalist and actress", "links": [{"title": "Nur Fe<PERSON>ho<PERSON>", "link": "https://wikipedia.org/wiki/Nur_Fettaho%C4%9Flu"}]}, {"year": "1980", "text": "<PERSON>, Canadian actor, producer and singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, German pole vaulter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German pole vaulter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/DJ_<PERSON>\" title=\"DJ <PERSON>\">DJ <PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"DJ <PERSON>\">DJ <PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Italian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Sep<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American Mixed Martial Artist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Mixed Martial Artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Mixed Martial Artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American singer, songwriter, actor and dancer ", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter, actor and dancer ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter, actor and dancer ", "links": [{"title": "Omarion", "link": "https://wikipedia.org/wiki/Omarion"}]}, {"year": "1984", "text": "<PERSON><PERSON>, South Korean singer, dancer, and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Sandara_Park\" title=\"Sandara Park\">Sandara Park</a>, South Korean singer, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sandara_Park\" title=\"Sandara Park\">Sandara Park</a>, South Korean singer, dancer, and actress", "links": [{"title": "Sandara Park", "link": "https://wikipedia.org/wiki/Sandara_Park"}]}, {"year": "1984", "text": "<PERSON>, Zimbabwean racing driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American model and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, French-Algerian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Adl%C3%A8ne_Guedioura\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adl%C3%A8ne_Guedioura\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Algerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adl%C3%A8ne_Guedioura"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Igna<PERSON> Abate\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Igna<PERSON> Abate\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>um Onuo<PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>uoha"}]}, {"year": "1987", "text": "<PERSON>, Australian golfer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" class=\"mw-redirect\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" class=\"mw-redirect\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Japanese actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kengo_<PERSON>ra"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American soccer player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Franch"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, French swimmer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Florent_<PERSON>\" title=\"Florent <PERSON>\"><PERSON><PERSON><PERSON></a>, French swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florent_<PERSON>\" title=\"Florent <PERSON>\"><PERSON><PERSON><PERSON></a>, French swimmer", "links": [{"title": "Florent <PERSON>", "link": "https://wikipedia.org/wiki/Florent_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Estonian basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Estonian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Estonian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Brazilian gridiron football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Cairo_Santos\" title=\"Cairo Santos\">Cairo Santos</a>, Brazilian gridiron football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cairo_Santos\" title=\"Cairo Santos\"><PERSON> Santos</a>, Brazilian gridiron football player", "links": [{"title": "Cairo Santos", "link": "https://wikipedia.org/wiki/Cairo_Santos"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian cyclist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Gi<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gi<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gij<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Latvian basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/D%C4%81vis_Bert%C4%81ns\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C4%81vis_Bert%C4%81ns\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C4%81vis_Bert%C4%81ns"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Swedish ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Dominican sprinter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Luguel%C3%ADn_Santos\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luguel%C3%ADn_Santos\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican sprinter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luguel%C3%ADn_Santos"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Hertl\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Hertl\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Hertl"}]}, {"year": "1994", "text": "<PERSON>, French ice dancer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guillaume_Cizeron"}]}, {"year": "1995", "text": "<PERSON>, French footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Canadian online streamer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/XQc\" title=\"XQc\">xQc</a>, Canadian online streamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/XQc\" title=\"XQc\">xQc</a>, Canadian online streamer", "links": [{"title": "XQc", "link": "https://wikipedia.org/wiki/XQc"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, French footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9"}]}, {"year": "1998", "text": "<PERSON>, Swedish ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, South Korean singer, dancer, rapper, and actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ung_(singer)\" title=\"<PERSON>ung (singer)\"><PERSON></a>, South Korean singer, dancer, rapper, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON>ung (singer)\"><PERSON></a>, South Korean singer, dancer, rapper, and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-jung_(singer)"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Italian-American basketball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, English footballer ", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ram<PERSON>o\" title=\"Tino Livramento\"><PERSON><PERSON></a>, English footballer ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tin<PERSON>_<PERSON>\" title=\"Tino Livramento\"><PERSON><PERSON></a>, English footballer ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tino_Livramento"}]}], "Deaths": [{"year": "607", "text": "<PERSON><PERSON> III", "html": "607 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>iface_III\" title=\"<PERSON> <PERSON>ace III\"><PERSON> <PERSON><PERSON><PERSON> III</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_III\" title=\"Pope Boniface III\"><PERSON> <PERSON><PERSON><PERSON> III</a>", "links": [{"title": "<PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>iface_III"}]}, {"year": "657", "text": "<PERSON><PERSON>, Irish apostle (b. c.580)", "html": "657 - <a href=\"https://wikipedia.org/wiki/Livinus\" title=\"Liv<PERSON>\"><PERSON><PERSON></a>, Irish <a href=\"https://wikipedia.org/wiki/Apostle\" title=\"Apostle\">apostle</a> (b. c.580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Livinus\" title=\"Liv<PERSON>\"><PERSON><PERSON></a>, Irish <a href=\"https://wikipedia.org/wiki/Apostle\" title=\"Apostle\">apostle</a> (b. c.580)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Livinus"}, {"title": "Apostle", "link": "https://wikipedia.org/wiki/Apostle"}]}, {"year": "973", "text": "<PERSON><PERSON><PERSON> <PERSON>, Frankish nobleman (b. c.915)", "html": "973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Swabia\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Swabia\"><PERSON><PERSON><PERSON></a>, Frankish nobleman (b. c.915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Swabia\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Swabia\"><PERSON><PERSON><PERSON></a>, Frankish nobleman (b. c.915)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Duke of Swabia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Swabia"}]}, {"year": "975", "text": "<PERSON><PERSON>, Swiss painter", "html": "975 - <a href=\"https://wikipedia.org/wiki/Notker_Physicus\" title=\"Notker Physicus\"><PERSON><PERSON> Physicus</a>, Swiss painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Notker_Physicus\" title=\"Notker Physicus\"><PERSON><PERSON> Physicus</a>, Swiss painter", "links": [{"title": "Notker Physicus", "link": "https://wikipedia.org/wiki/Notker_Physicus"}]}, {"year": "1035", "text": "<PERSON><PERSON> the Great, Danish-English king (b. c.995)", "html": "1035 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON> the Great</a>, Danish-English king (b. c.995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON> the Great</a>, Danish-English king (b. c.995)", "links": [{"title": "<PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great"}]}, {"year": "1087", "text": "<PERSON>, Count of Burgundy (b. 1020)", "html": "1087 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Burgundy\" title=\"<PERSON>, Count of Burgundy\"><PERSON>, Count of Burgundy</a> (b. 1020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Burgundy\" title=\"<PERSON>, Count of Burgundy\"><PERSON>, Count of Burgundy</a> (b. 1020)", "links": [{"title": "<PERSON>, Count of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Burgundy"}]}, {"year": "1094", "text": "<PERSON> of Scotland (b. 1060)", "html": "1094 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> of Scotland</a> (b. 1060)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> of Scotland</a> (b. 1060)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Scotland"}]}, {"year": "1202", "text": "<PERSON><PERSON> VI of Denmark (b. 1163)", "html": "1202 - <a href=\"https://wikipedia.org/wiki/Canute_VI_of_Denmark\" title=\"Canute VI of Denmark\">Canute VI of Denmark</a> (b. 1163)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canute_VI_of_Denmark\" title=\"Canute VI of Denmark\">Canute VI of Denmark</a> (b. 1163)", "links": [{"title": "Canute VI of Denmark", "link": "https://wikipedia.org/wiki/Canute_VI_of_Denmark"}]}, {"year": "1209", "text": "<PERSON>, Grand Master of the Knights Templar (b. 1165)", "html": "1209 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Master of the Knights Templar (b. 1165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Master of the Knights Templar (b. 1165)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1218", "text": "<PERSON>, Prior of Abergavenny and Bishop of Llandaff", "html": "1218 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prior of Abergavenny and Bishop of Llandaff", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prior of Abergavenny and Bishop of Llandaff", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1347", "text": "<PERSON> of Viktring, Austrian chronicler and political advisor (b. c.1270)", "html": "1347 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Viktring\"><PERSON> Viktring</a>, Austrian chronicler and political advisor (b. c.1270)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Viktring\"><PERSON> V<PERSON>tring</a>, Austrian chronicler and political advisor (b. c.1270)", "links": [{"title": "<PERSON> of Viktring", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1375", "text": "<PERSON>, Margrave of Moravia (b. 1322)", "html": "1375 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Mar<PERSON>_of_Moravia\" title=\"<PERSON>, Margrave of Moravia\"><PERSON>, Margrave of Moravia</a> (b. 1322)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Moravia\" title=\"<PERSON>, Margrave of Moravia\"><PERSON>, Margrave of Moravia</a> (b. 1322)", "links": [{"title": "<PERSON>, Margrave of Moravia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Moravia"}]}, {"year": "1434", "text": "<PERSON> of Anjou (b. 1403)", "html": "1434 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (b. 1403)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (b. 1403)", "links": [{"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Anjou"}]}, {"year": "1555", "text": "<PERSON>, English bishop and politician, English Secretary of State (b. 1497)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">English Secretary of State</a> (b. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">English Secretary of State</a> (b. 1497)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State (England)", "link": "https://wikipedia.org/wiki/Secretary_of_State_(England)"}]}, {"year": "1555", "text": "<PERSON> (b. 1516), Ming dynasty official and Confucian martyr", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ming_dynasty)\" title=\"<PERSON> (Ming dynasty)\"><PERSON></a> (b. 1516), Ming dynasty official and Confucian martyr", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ming_dynasty)\" title=\"<PERSON> (Ming dynasty)\"><PERSON></a> (b. 1516), Ming dynasty official and Confucian martyr", "links": [{"title": "<PERSON> (Ming dynasty)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ming_dynasty)"}]}, {"year": "1555", "text": "<PERSON>, Ming Chinese general", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ming_dynasty)\" title=\"<PERSON> (Ming dynasty)\"><PERSON></a>, Ming Chinese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ming_dynasty)\" title=\"<PERSON> (Ming dynasty)\"><PERSON></a>, Ming Chinese general", "links": [{"title": "<PERSON> (Ming dynasty)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ming_dynasty)"}]}, {"year": "1562", "text": "<PERSON>, Italian theologian (b. 1500)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vermigli\" class=\"mw-redirect\" title=\"<PERSON> Vermigli\"><PERSON></a>, Italian theologian (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vermigli\" class=\"mw-redirect\" title=\"<PERSON> Vermigli\"><PERSON></a>, Italian theologian (b. 1500)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pietro_Martire_Vermigli"}]}, {"year": "1567", "text": "<PERSON>, French general and diplomat (b. 1493)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general and diplomat (b. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general and diplomat (b. 1493)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1572", "text": "<PERSON> of Stolberg, German nobleman (b. 1509)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stolberg\" title=\"<PERSON> of Stolberg\"><PERSON> of Stolberg</a>, German nobleman (b. 1509)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stolberg\" title=\"<PERSON> of Stolberg\"><PERSON> of Stolberg</a>, German nobleman (b. 1509)", "links": [{"title": "<PERSON> of Stolberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1595", "text": "<PERSON>, English admiral and shipbuilder (b. 1532)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(naval_commander)\" title=\"<PERSON> (naval commander)\"><PERSON></a>, English admiral and shipbuilder (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(naval_commander)\" title=\"<PERSON> (naval commander)\"><PERSON></a>, English admiral and shipbuilder (b. 1532)", "links": [{"title": "<PERSON> (naval commander)", "link": "https://wikipedia.org/wiki/<PERSON>_(naval_commander)"}]}, {"year": "1623", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian archbishop (b. c. 1582)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian archbishop (b. c. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian archbishop (b. c. 1582)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1667", "text": "<PERSON>, Danish politician (b. 1598)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish politician (b. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish politician (b. 1598)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1671", "text": "<PERSON>, English general and politician (b. 1612)", "html": "1671 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (b. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (b. 1612)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON>, German physician and chemist (b. 1660)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and chemist (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and chemist (b. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, French astronomer, mathematician, and politician, 1st Mayor of Paris (b. 1736)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer, mathematician, and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Paris\" title=\"Mayor of Paris\">Mayor of Paris</a> (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer, mathematician, and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Paris\" title=\"Mayor of Paris\">Mayor of Paris</a> (b. 1736)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Paris", "link": "https://wikipedia.org/wiki/Mayor_of_Paris"}]}, {"year": "1793", "text": "Lord <PERSON>, English politician (b. 1751)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English politician (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English politician (b. 1751)", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, Argentinian general and politician, 6th Governor of Buenos Aires Province (b. 1773)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Ba<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province\" title=\"Governor of Buenos Aires Province\">Governor of Buenos Aires Province</a> (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Ba<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province\" title=\"Governor of Buenos Aires Province\">Governor of Buenos Aires Province</a> (b. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Balcarce"}, {"title": "Governor of Buenos Aires Province", "link": "https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province"}]}, {"year": "1847", "text": "<PERSON>, Danish chemist who prepared <PERSON><PERSON><PERSON>'s salt, one of the first organometallic compounds (b. 1789)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish chemist who prepared <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_salt\" title=\"<PERSON><PERSON><PERSON>'s salt\"><PERSON><PERSON><PERSON>'s salt</a>, one of the first organometallic compounds (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish chemist who prepared <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_salt\" title=\"<PERSON><PERSON><PERSON>'s salt\"><PERSON><PERSON><PERSON>'s salt</a>, one of the first organometallic compounds (b. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>'s salt", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_salt"}]}, {"year": "1865", "text": "<PERSON>, English author (b. 1810)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Liberian politician, 12th President of Liberia (b. 1843)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}]}, {"year": "1902", "text": "<PERSON>, English engineer (b. 1812)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, American astronomer, mathematician, and author (b. 1855)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Per<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American astronomer, mathematician, and author (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Per<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American astronomer, mathematician, and author (b. 1855)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Percival_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American golfer (b. 1866)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (b. 1866)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American photographer and publisher (b. 1864)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/F._Holland_Day\" title=\"F. Holland Day\"><PERSON><PERSON></a>, American photographer and publisher (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F._Holland_Day\" title=\"F. Holland Day\"><PERSON><PERSON></a>, American photographer and publisher (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F._Holland_Day"}]}, {"year": "1939", "text": "<PERSON>, Canadian physician and humanitarian (b. 1890)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and humanitarian (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and humanitarian (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, executed Irish Republican", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, executed <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, executed <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a>", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(Irish_republican)"}, {"title": "Irish Republican", "link": "https://wikipedia.org/wiki/Irish_Republican"}]}, {"year": "1946", "text": "<PERSON>, American golfer and pilot (b. 1875)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and pilot (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and pilot (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Indian academic and politician, President of the Indian National Congress (b. 1861)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian academic and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Indian_National_Congress\" class=\"mw-redirect\" title=\"President of the Indian National Congress\">President of the Indian National Congress</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian academic and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Indian_National_Congress\" class=\"mw-redirect\" title=\"President of the Indian National Congress\">President of the Indian National Congress</a> (b. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the Indian National Congress", "link": "https://wikipedia.org/wiki/President_of_the_Indian_National_Congress"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Italian composer (b. 1867)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Umberto_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umberto_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer (b. 1867)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Umberto_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American hurdler (b. 1883)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English-American actress (b. 1865)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian swimmer and architect, designed the Grand Hotel Aranybika (b. 1878)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Alfr%C3%A9d_Haj%C3%B3s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian swimmer and architect, designed the <a href=\"https://wikipedia.org/wiki/Grand_Hotel_Aranybika\" title=\"Grand Hotel Aranybika\">Grand Hotel Aranybika</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfr%C3%A9d_Haj%C3%B3s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian swimmer and architect, designed the <a href=\"https://wikipedia.org/wiki/Grand_Hotel_Aranybika\" title=\"Grand Hotel Aranybika\">Grand Hotel Aranybika</a> (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alfr%C3%A9d_Haj%C3%B3s"}, {"title": "Grand Hotel Aranybika", "link": "https://wikipedia.org/wiki/Grand_Hotel_Aranybika"}]}, {"year": "1955", "text": "<PERSON>, Croatian poet and translator (b. 1891)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Tin_Ujevi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian poet and translator (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ujevi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian poet and translator (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tin_Ujevi%C4%87"}]}, {"year": "1955", "text": "<PERSON>, American political scientist, world authority on plebiscites (b. 1882)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, world authority on plebiscites (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, world authority on plebiscites (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Swedish shot putter, discus thrower, and tug of war competitor (b. 1865)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Gustaf_S%C3%B6derstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish shot putter, discus thrower, and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gustaf_S%C3%B6derstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish shot putter, discus thrower, and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gustaf_S%C3%B6derstr%C3%B6m"}, {"title": "Tug of war", "link": "https://wikipedia.org/wiki/Tug_of_war"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Mexican general and acting president (1915) (b. 1885)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1lez_Garza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican general and acting president (1915) (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1lez_Garza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican general and acting president (1915) (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roque_Gonz%C3%A1lez_Garza"}]}, {"year": "1965", "text": "<PERSON>, French painter (b. 1873)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ben<PERSON>\" title=\"Many Benner\"><PERSON></a>, French painter (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ben<PERSON>\" title=\"Many Benner\"><PERSON></a>, French painter (b. 1873)", "links": [{"title": "Many Benner", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Indian spiritual leader, 51st <PERSON><PERSON><PERSON> (b. 1888)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian spiritual leader, 51st <a href=\"https://wikipedia.org/wiki/Da%27i_al-Mutl<PERSON>\" title=\"<PERSON><PERSON><PERSON> al-Mutlaq\"><PERSON><PERSON><PERSON> <PERSON></a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian spiritual leader, 51st <a href=\"https://wikipedia.org/wiki/Da%27i_al-Mutl<PERSON>\" title=\"<PERSON><PERSON><PERSON> al-Mutlaq\"><PERSON><PERSON><PERSON> <PERSON>-<PERSON>tl<PERSON></a> (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> al-<PERSON>", "link": "https://wikipedia.org/wiki/Da%27i_<PERSON>-<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Chinese politician, 2nd Chairman of the People's Republic of China (b. 1898)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 2nd <a href=\"https://wikipedia.org/wiki/Chairman_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Chairman of the People's Republic of China\">Chairman of the People's Republic of China</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 2nd <a href=\"https://wikipedia.org/wiki/Chairman_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Chairman of the People's Republic of China\">Chairman of the People's Republic of China</a> (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chairman of the People's Republic of China", "link": "https://wikipedia.org/wiki/Chairman_of_the_People%27s_Republic_of_China"}]}, {"year": "1971", "text": "<PERSON>, German mathematician (b. 1914)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Czech-American pianist and composer (b. 1879)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American pianist and composer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American pianist and composer (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English racing driver and journalist (b. 1906)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wisdom\"><PERSON></a>, English racing driver and journalist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wisdom\"><PERSON></a>, English racing driver and journalist (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Russian engineer, co-founded <PERSON><PERSON><PERSON> (b. 1893)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aircraft_designer)\" title=\"<PERSON> (aircraft designer)\"><PERSON></a>, Russian engineer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(aircraft_designer)\" title=\"<PERSON> (aircraft designer)\"><PERSON></a>, Russian engineer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1893)", "links": [{"title": "<PERSON> (aircraft designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aircraft_designer)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>yan"}]}, {"year": "1976", "text": "<PERSON>, American composer and academic (b. 1894)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor (b. 1918)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American lawyer and activist (b. 1916)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and activist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and activist (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actress and comedian (b. 1908)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Italian actor (b. 1932)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, Italian actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, Italian actor (b. 1932)", "links": [{"title": "<PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)"}]}, {"year": "1993", "text": "<PERSON><PERSON> <PERSON><PERSON>, American diplomat, 4th White House Chief of Staff (b. 1926)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American diplomat, 4th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American diplomat, 4th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "White House Chief of Staff", "link": "https://wikipedia.org/wiki/White_House_Chief_of_Staff"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American sprinter and educator (b. 1940)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sprinter and educator (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sprinter and educator (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Spanish-American composer and conductor (b. 1915)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-American composer and conductor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-American composer and conductor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English footballer (b. 1925)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American mathematician and engineer (b. 1938)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and engineer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and engineer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, French conductor and composer (b. 1913)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>urcel\" title=\"Fran<PERSON> Pourcel\"><PERSON><PERSON><PERSON></a>, French conductor and composer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Fran<PERSON> Pourcel\"><PERSON><PERSON><PERSON></a>, French conductor and composer (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an<PERSON>_Pourcel"}]}, {"year": "2001", "text": "<PERSON>, German-American actor and composer (b. 1920)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Albert_Hague\" title=\"Albert Hague\"><PERSON></a>, German-American actor and composer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Albert_Hague\" title=\"Albert Hague\"><PERSON></a>, German-American actor and composer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Albert_Hague"}]}, {"year": "2001", "text": "<PERSON>, English chess player and theoretician (b. 1955)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chess player and theoretician (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chess player and theoretician (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor (b. 1976)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, New Zealand director and screenwriter (b. 1986)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand director and screenwriter (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand director and screenwriter (b. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actress (b. 1908)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American drummer (b. 1954)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer (b. 1954)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "2007", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian cricketer (b. 1919)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian cricketer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian cricketer (b. 1919)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American novelist, playwright, and songwriter (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and songwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and songwriter (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American educator and politician, 30th Lieutenant Governor of Pennsylvania (b. 1930)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 30th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Pennsylvania\" title=\"Lieutenant Governor of Pennsylvania\">Lieutenant Governor of Pennsylvania</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 30th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Pennsylvania\" title=\"Lieutenant Governor of Pennsylvania\">Lieutenant Governor of Pennsylvania</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Pennsylvania", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Pennsylvania"}]}, {"year": "2008", "text": "<PERSON>, English drummer (b. 1947)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Polish composer (b. 1933)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Henryk_G%C3%B3recki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish composer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henryk_G%C3%B3recki\" title=\"<PERSON><PERSON> G<PERSON>\"><PERSON><PERSON></a>, Polish composer (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Henryk_G%C3%B3recki"}]}, {"year": "2012", "text": "<PERSON>, Swedish photographer (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ld\" title=\"<PERSON>\"><PERSON></a>, Swedish photographer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ld\" title=\"<PERSON>\"><PERSON></a>, Swedish photographer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ld"}]}, {"year": "2012", "text": "<PERSON>, Cuban-American bodybuilder (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American bodybuilder (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American bodybuilder (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American psychologist and theorist (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, American psychologist and theorist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, American psychologist and theorist (b. 1934)", "links": [{"title": "<PERSON> (psychologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(psychologist)"}]}, {"year": "2013", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Polish astronomer and academic (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish astronomer and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish astronomer and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Russian engineer and astronaut (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and astronaut (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and astronaut (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English composer and educator (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Danish painter and sculptor (b. 1943)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and sculptor (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and sculptor (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ach"}]}, {"year": "2014", "text": "<PERSON>, Indian director and producer (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian director and producer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian director and producer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English actor, director, and producer (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American educator and politician (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Russian mathematician and academic (b. 1945)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Senderov\" title=\"<PERSON><PERSON> Senderov\"><PERSON><PERSON></a>, Russian mathematician and academic (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>erov\" title=\"<PERSON><PERSON> Senderov\"><PERSON><PERSON></a>, Russian mathematician and academic (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer (b. 1983)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/M%C3%A1rton_F%C3%BCl%C3%B6p\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1rton_F%C3%BCl%C3%B6p\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer (b. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1rton_F%C3%BCl%C3%B6p"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, terrorist (b. 1988)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ji<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, terrorist (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ji<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, terrorist (b. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Mexican-American actress (b. 1910)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican-American actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican-American actress (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lupita_Tovar"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Egyptian Actor (b. 1946)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian Actor (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian Actor (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American comic book writer, editor, and publisher (b. 1922)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic book writer, editor, and publisher (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic book writer, editor, and publisher (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Wood scientist (b. 1935)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Wood scientist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Wood scientist (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American drummer and composer (b. 1925)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Canadian politician and diplomat, 36th Premier of British Columbia (b. 1959)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician and diplomat, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician and diplomat, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of British Columbia", "link": "https://wikipedia.org/wiki/Premier_of_British_Columbia"}]}, {"year": "2024", "text": "<PERSON>, South Korean actor and model (b. 1985)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor and model (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor and model (b. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American computer scientist and educator (b. 1928)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and educator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and educator (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, English actor (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}