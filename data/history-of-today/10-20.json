{"date": "October 20", "url": "https://wikipedia.org/wiki/October_20", "data": {"Events": [{"year": "1568", "text": "The Spanish Duke of Alba defeats a Dutch rebel force under <PERSON>.", "html": "1568 - The Spanish <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Alba\" title=\"<PERSON>, 3rd Duke of Alba\">Duke of Alba</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Jodoigne\" title=\"Battle of Jodoigne\">defeats</a> a Dutch rebel force under <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Silent\" title=\"William the Silent\"><PERSON> the <PERSON></a>.", "no_year_html": "The Spanish <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Alba\" title=\"<PERSON>, 3rd Duke of Alba\">Duke of Alba</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Jodoigne\" title=\"Battle of Jodoigne\">defeats</a> a Dutch rebel force under <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Silent\" title=\"<PERSON> the Silent\"><PERSON> the Silent</a>.", "links": [{"title": "<PERSON>, 3rd Duke of Alba", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Alba"}, {"title": "Battle of Jodoigne", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON><PERSON>e"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Silent"}]}, {"year": "1572", "text": "Eighty Years' War: Three thousand Spanish soldiers wade through fifteen miles of water in one night to effect the relief of Goes.", "html": "1572 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: Three thousand Spanish soldiers wade through fifteen miles of water in one night to effect the <a href=\"https://wikipedia.org/wiki/Relief_of_Goes\" title=\"Relief of Goes\">relief of Goes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: Three thousand Spanish soldiers wade through fifteen miles of water in one night to effect the <a href=\"https://wikipedia.org/wiki/Relief_of_Goes\" title=\"Relief of Goes\">relief of Goes</a>.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Relief of Goes", "link": "https://wikipedia.org/wiki/Relief_of_Goes"}]}, {"year": "1740", "text": "France, Prussia, Bavaria and Saxony refuse to honour the Pragmatic Sanction, and the War of the Austrian Succession begins.", "html": "1740 - France, Prussia, Bavaria and Saxony refuse to honour the <a href=\"https://wikipedia.org/wiki/Pragmatic_Sanction_of_1713\" title=\"Pragmatic Sanction of 1713\">Pragmatic Sanction</a>, and the <a href=\"https://wikipedia.org/wiki/War_of_the_Austrian_Succession\" title=\"War of the Austrian Succession\">War of the Austrian Succession</a> begins.", "no_year_html": "France, Prussia, Bavaria and Saxony refuse to honour the <a href=\"https://wikipedia.org/wiki/Pragmatic_Sanction_of_1713\" title=\"Pragmatic Sanction of 1713\">Pragmatic Sanction</a>, and the <a href=\"https://wikipedia.org/wiki/War_of_the_Austrian_Succession\" title=\"War of the Austrian Succession\">War of the Austrian Succession</a> begins.", "links": [{"title": "Pragmatic Sanction of 1713", "link": "https://wikipedia.org/wiki/Pragmatic_Sanction_of_1713"}, {"title": "War of the Austrian Succession", "link": "https://wikipedia.org/wiki/War_of_the_Austrian_Succession"}]}, {"year": "1774", "text": "American Revolution: The Continental Association, a nonconsumption and nonimportation agreement against the British Isles and the British West Indies, is adopted by the First Continental Congress.", "html": "1774 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Continental_Association\" title=\"Continental Association\">Continental Association</a>, a <a href=\"https://wikipedia.org/wiki/Nonconsumption_agreements\" title=\"Nonconsumption agreements\">nonconsumption</a> and nonimportation agreement against the <a href=\"https://wikipedia.org/wiki/British_Isles\" title=\"British Isles\">British Isles</a> and the <a href=\"https://wikipedia.org/wiki/British_West_Indies\" title=\"British West Indies\">British West Indies</a>, is adopted by the <a href=\"https://wikipedia.org/wiki/First_Continental_Congress\" title=\"First Continental Congress\">First Continental Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Continental_Association\" title=\"Continental Association\">Continental Association</a>, a <a href=\"https://wikipedia.org/wiki/Nonconsumption_agreements\" title=\"Nonconsumption agreements\">nonconsumption</a> and nonimportation agreement against the <a href=\"https://wikipedia.org/wiki/British_Isles\" title=\"British Isles\">British Isles</a> and the <a href=\"https://wikipedia.org/wiki/British_West_Indies\" title=\"British West Indies\">British West Indies</a>, is adopted by the <a href=\"https://wikipedia.org/wiki/First_Continental_Congress\" title=\"First Continental Congress\">First Continental Congress</a>.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "Continental Association", "link": "https://wikipedia.org/wiki/Continental_Association"}, {"title": "Nonconsumption agreements", "link": "https://wikipedia.org/wiki/Nonconsumption_agreements"}, {"title": "British Isles", "link": "https://wikipedia.org/wiki/British_Isles"}, {"title": "British West Indies", "link": "https://wikipedia.org/wiki/British_West_Indies"}, {"title": "First Continental Congress", "link": "https://wikipedia.org/wiki/First_Continental_Congress"}]}, {"year": "1781", "text": "The Patent of Toleration, providing limited freedom of worship, is approved in Austria.", "html": "1781 - The <a href=\"https://wikipedia.org/wiki/Patent_of_Toleration\" title=\"Patent of Toleration\">Patent of Toleration</a>, providing limited freedom of worship, is approved in Austria.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Patent_of_Toleration\" title=\"Patent of Toleration\">Patent of Toleration</a>, providing limited freedom of worship, is approved in Austria.", "links": [{"title": "Patent of Toleration", "link": "https://wikipedia.org/wiki/Patent_of_Toleration"}]}, {"year": "1803", "text": "The United States Senate ratifies the Louisiana Purchase.", "html": "1803 - The United States Senate ratifies the <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">Louisiana Purchase</a>.", "no_year_html": "The United States Senate ratifies the <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">Louisiana Purchase</a>.", "links": [{"title": "Louisiana Purchase", "link": "https://wikipedia.org/wiki/Louisiana_Purchase"}]}, {"year": "1818", "text": "The Convention of 1818 is signed between the United States and the United Kingdom, which settles the Canada-United States border on the 49th parallel for most of its length.", "html": "1818 - <a href=\"https://wikipedia.org/wiki/Treaty_of_1818\" title=\"Treaty of 1818\">The Convention of 1818</a> is signed between the United States and the United Kingdom, which settles the <a href=\"https://wikipedia.org/wiki/Canada%E2%80%93United_States_border\" title=\"Canada-United States border\">Canada-United States border</a> on the <a href=\"https://wikipedia.org/wiki/49th_parallel_north\" title=\"49th parallel north\">49th parallel</a> for most of its length.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_1818\" title=\"Treaty of 1818\">The Convention of 1818</a> is signed between the United States and the United Kingdom, which settles the <a href=\"https://wikipedia.org/wiki/Canada%E2%80%93United_States_border\" title=\"Canada-United States border\">Canada-United States border</a> on the <a href=\"https://wikipedia.org/wiki/49th_parallel_north\" title=\"49th parallel north\">49th parallel</a> for most of its length.", "links": [{"title": "Treaty of 1818", "link": "https://wikipedia.org/wiki/Treaty_of_1818"}, {"title": "Canada-United States border", "link": "https://wikipedia.org/wiki/Canada%E2%80%93United_States_border"}, {"title": "49th parallel north", "link": "https://wikipedia.org/wiki/49th_parallel_north"}]}, {"year": "1827", "text": "Greek War of Independence: In the Battle of Navarino, a combined Turkish and Egyptian fleet is defeated by British, French and Russian naval forces in the last significant battle fought with wooden sailing ships.", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Navarino\" title=\"Battle of Navarino\">Battle of Navarino</a>, a combined Turkish and Egyptian fleet is defeated by British, French and Russian naval forces in the last significant battle fought with wooden sailing ships.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Navarino\" title=\"Battle of Navarino\">Battle of Navarino</a>, a combined Turkish and Egyptian fleet is defeated by British, French and Russian naval forces in the last significant battle fought with wooden sailing ships.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "Battle of Navarino", "link": "https://wikipedia.org/wiki/Battle_of_Navarino"}]}, {"year": "1883", "text": "Peru and Chile sign the Treaty of Ancón, by which the Tarapacá province is ceded to the latter, bringing an end to Peru's involvement in the War of the Pacific.", "html": "1883 - Peru and Chile sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Anc%C3%B3n\" title=\"Treaty of Ancón\">Treaty of Ancón</a>, by which the Tarapacá province is ceded to the latter, bringing an end to Peru's involvement in the War of the Pacific.", "no_year_html": "Peru and Chile sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Anc%C3%B3n\" title=\"Treaty of Ancón\">Treaty of Ancón</a>, by which the Tarapacá province is ceded to the latter, bringing an end to Peru's involvement in the War of the Pacific.", "links": [{"title": "Treaty of Ancón", "link": "https://wikipedia.org/wiki/Treaty_of_Anc%C3%B3n"}]}, {"year": "1904", "text": "Chile and Bolivia sign the Treaty of Peace and Friendship, delimiting the border between the two countries.", "html": "1904 - Chile and Bolivia sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Peace_and_Friendship_(1904)\" title=\"Treaty of Peace and Friendship (1904)\">Treaty of Peace and Friendship</a>, delimiting the border between the two countries.", "no_year_html": "Chile and Bolivia sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Peace_and_Friendship_(1904)\" title=\"Treaty of Peace and Friendship (1904)\">Treaty of Peace and Friendship</a>, delimiting the border between the two countries.", "links": [{"title": "Treaty of Peace and Friendship (1904)", "link": "https://wikipedia.org/wiki/Treaty_of_Peace_and_Friendship_(1904)"}]}, {"year": "1910", "text": "British ocean liner RMS Olympic is launched.", "html": "1910 - British <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">ocean liner</a> <a href=\"https://wikipedia.org/wiki/RMS_Olympic\" title=\"RMS Olympic\">RMS <i>Olympic</i></a> is launched.", "no_year_html": "British <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">ocean liner</a> <a href=\"https://wikipedia.org/wiki/RMS_Olympic\" title=\"RMS Olympic\">RMS <i>Olympic</i></a> is launched.", "links": [{"title": "Ocean liner", "link": "https://wikipedia.org/wiki/Ocean_liner"}, {"title": "RMS Olympic", "link": "https://wikipedia.org/wiki/RMS_Olympic"}]}, {"year": "1935", "text": "The Long March, a mammoth retreat undertaken by the armed forces of the Chinese Communist Party a year prior, ends.", "html": "1935 - The <a href=\"https://wikipedia.org/wiki/Long_March\" title=\"Long March\">Long March</a>, a mammoth retreat undertaken by the <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">armed forces</a> of the <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> a year prior, ends.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Long_March\" title=\"Long March\">Long March</a>, a mammoth retreat undertaken by the <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">armed forces</a> of the <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> a year prior, ends.", "links": [{"title": "Long March", "link": "https://wikipedia.org/wiki/Long_March"}, {"title": "People's Liberation Army", "link": "https://wikipedia.org/wiki/People%27s_Liberation_Army"}, {"title": "Chinese Communist Party", "link": "https://wikipedia.org/wiki/Chinese_Communist_Party"}]}, {"year": "1941", "text": "World War II: Thousands of civilians in German-occupied Serbia are murdered in the Kragujevac massacre.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Thousands of civilians in <a href=\"https://wikipedia.org/wiki/Territory_of_the_Military_Commander_in_Serbia\" title=\"Territory of the Military Commander in Serbia\">German-occupied Serbia</a> are murdered in the <a href=\"https://wikipedia.org/wiki/Kragujevac_massacre\" title=\"Kragujevac massacre\">Kragujevac massacre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Thousands of civilians in <a href=\"https://wikipedia.org/wiki/Territory_of_the_Military_Commander_in_Serbia\" title=\"Territory of the Military Commander in Serbia\">German-occupied Serbia</a> are murdered in the <a href=\"https://wikipedia.org/wiki/Kragujevac_massacre\" title=\"Kragujevac massacre\">Kragujevac massacre</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Territory of the Military Commander in Serbia", "link": "https://wikipedia.org/wiki/Territory_of_the_Military_Commander_in_Serbia"}, {"title": "Kragujevac massacre", "link": "https://wikipedia.org/wiki/Kragujevac_massacre"}]}, {"year": "1944", "text": "World War II: The Soviet Red Army and Yugoslav Partisans liberate Belgrade.", "html": "1944 - World War II: The Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> and <a href=\"https://wikipedia.org/wiki/Yugoslav_Partisans\" title=\"Yugoslav Partisans\">Yugoslav Partisans</a> <a href=\"https://wikipedia.org/wiki/Belgrade_Offensive\" class=\"mw-redirect\" title=\"Belgrade Offensive\">liberate Belgrade</a>.", "no_year_html": "World War II: The Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> and <a href=\"https://wikipedia.org/wiki/Yugoslav_Partisans\" title=\"Yugoslav Partisans\">Yugoslav Partisans</a> <a href=\"https://wikipedia.org/wiki/Belgrade_Offensive\" class=\"mw-redirect\" title=\"Belgrade Offensive\">liberate Belgrade</a>.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Yugoslav Partisans", "link": "https://wikipedia.org/wiki/Yugoslav_Partisans"}, {"title": "Belgrade Offensive", "link": "https://wikipedia.org/wiki/Belgrade_Offensive"}]}, {"year": "1944", "text": "Liquefied natural gas leaks from storage tanks in Cleveland and then explodes, leveling 30 blocks and killing 130 people.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Liquefied_natural_gas\" title=\"Liquefied natural gas\">Liquefied natural gas</a> leaks from storage tanks in Cleveland and then <a href=\"https://wikipedia.org/wiki/Cleveland_East_Ohio_Gas_explosion\" title=\"Cleveland East Ohio Gas explosion\">explodes</a>, leveling 30 blocks and killing 130 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liquefied_natural_gas\" title=\"Liquefied natural gas\">Liquefied natural gas</a> leaks from storage tanks in Cleveland and then <a href=\"https://wikipedia.org/wiki/Cleveland_East_Ohio_Gas_explosion\" title=\"Cleveland East Ohio Gas explosion\">explodes</a>, leveling 30 blocks and killing 130 people.", "links": [{"title": "Liquefied natural gas", "link": "https://wikipedia.org/wiki/Liquefied_natural_gas"}, {"title": "Cleveland East Ohio Gas explosion", "link": "https://wikipedia.org/wiki/Cleveland_East_Ohio_Gas_explosion"}]}, {"year": "1944", "text": "American general <PERSON> fulfills his promise to return to the Philippines when he comes ashore during the Battle of Leyte.", "html": "1944 - American general <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> fulfills his promise to return to the Philippines when he comes ashore during the <a href=\"https://wikipedia.org/wiki/Battle_of_Leyte\" title=\"Battle of Leyte\">Battle of Leyte</a>.", "no_year_html": "American general <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> fulfills his promise to return to the Philippines when he comes ashore during the <a href=\"https://wikipedia.org/wiki/Battle_of_Leyte\" title=\"Battle of Leyte\">Battle of Leyte</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Leyte", "link": "https://wikipedia.org/wiki/Battle_of_Leyte"}]}, {"year": "1947", "text": "Cold War: The House Un-American Activities Committee begins its investigation into Communist infiltration of the Hollywood film industry, resulting in a blacklist that prevents some from working in the industry for years.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Un-American Activities Committee</a> begins its investigation into Communist infiltration of the <a href=\"https://wikipedia.org/wiki/Cinema_of_the_United_States\" title=\"Cinema of the United States\">Hollywood film industry</a>, resulting in <a href=\"https://wikipedia.org/wiki/Hollywood_blacklist\" title=\"Hollywood blacklist\">a blacklist</a> that prevents some from working in the industry for years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Un-American Activities Committee</a> begins its investigation into Communist infiltration of the <a href=\"https://wikipedia.org/wiki/Cinema_of_the_United_States\" title=\"Cinema of the United States\">Hollywood film industry</a>, resulting in <a href=\"https://wikipedia.org/wiki/Hollywood_blacklist\" title=\"Hollywood blacklist\">a blacklist</a> that prevents some from working in the industry for years.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "House Un-American Activities Committee", "link": "https://wikipedia.org/wiki/House_Un-American_Activities_Committee"}, {"title": "Cinema of the United States", "link": "https://wikipedia.org/wiki/Cinema_of_the_United_States"}, {"title": "Hollywood blacklist", "link": "https://wikipedia.org/wiki/Hollywood_blacklist"}]}, {"year": "1948", "text": "A KLM Lockheed L-049 Constellation crashes on approach to Glasgow Prestwick Airport, killing 40.", "html": "1948 - A <a href=\"https://wikipedia.org/wiki/KLM\" title=\"KLM\">KLM</a> <a href=\"https://wikipedia.org/wiki/Lockheed_L-049_Constellation\" title=\"Lockheed L-049 Constellation\">Lockheed L-049 Constellation</a> <a href=\"https://wikipedia.org/wiki/1948_KLM_Constellation_air_disaster\" title=\"1948 KLM Constellation air disaster\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Glasgow_Prestwick_Airport\" title=\"Glasgow Prestwick Airport\">Glasgow Prestwick Airport</a>, killing 40.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/KLM\" title=\"KLM\">KLM</a> <a href=\"https://wikipedia.org/wiki/Lockheed_L-049_Constellation\" title=\"Lockheed L-049 Constellation\">Lockheed L-049 Constellation</a> <a href=\"https://wikipedia.org/wiki/1948_KLM_Constellation_air_disaster\" title=\"1948 KLM Constellation air disaster\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Glasgow_Prestwick_Airport\" title=\"Glasgow Prestwick Airport\">Glasgow Prestwick Airport</a>, killing 40.", "links": [{"title": "KLM", "link": "https://wikipedia.org/wiki/KLM"}, {"title": "Lockheed L-049 Constellation", "link": "https://wikipedia.org/wiki/Lockheed_L-049_Constellation"}, {"title": "1948 KLM Constellation air disaster", "link": "https://wikipedia.org/wiki/1948_KLM_Constellation_air_disaster"}, {"title": "Glasgow Prestwick Airport", "link": "https://wikipedia.org/wiki/Glasgow_Prestwick_Airport"}]}, {"year": "1951", "text": "The \"<PERSON> incident\" occurs during a football game between the Drake Bulldogs and Oklahoma A&M Aggies.", "html": "1951 - The \"<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_incident\" title=\"<PERSON> incident\"><PERSON> incident</a>\" occurs during a football game between the <a href=\"https://wikipedia.org/wiki/Drake_Bulldogs_football\" title=\"Drake Bulldogs football\">Drake Bulldogs</a> and <a href=\"https://wikipedia.org/wiki/Oklahoma_State_Cowboys_football\" title=\"Oklahoma State Cowboys football\">Oklahoma A&amp;M Aggies</a>.", "no_year_html": "The \"<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_incident\" title=\"<PERSON> incident\"><PERSON> incident</a>\" occurs during a football game between the <a href=\"https://wikipedia.org/wiki/Drake_Bulldogs_football\" title=\"Drake Bulldogs football\">Drake Bulldogs</a> and <a href=\"https://wikipedia.org/wiki/Oklahoma_State_Cowboys_football\" title=\"Oklahoma State Cowboys football\">Oklahoma A&amp;M Aggies</a>.", "links": [{"title": "<PERSON> incident", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_incident"}, {"title": "Drake Bulldogs football", "link": "https://wikipedia.org/wiki/Drake_Bulldogs_football"}, {"title": "Oklahoma State Cowboys football", "link": "https://wikipedia.org/wiki/Oklahoma_State_Cowboys_football"}]}, {"year": "1952", "text": "The Governor of Kenya <PERSON> declares a state of emergency and begins arresting hundreds of suspected leaders of the Mau Mau Uprising.", "html": "1952 - The Governor of Kenya <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON><PERSON>_of_Glendale\" title=\"<PERSON>, 1st Baron <PERSON> of Glendale\"><PERSON></a> declares a state of emergency and begins arresting hundreds of suspected leaders of the <a href=\"https://wikipedia.org/wiki/Mau_Mau_Uprising\" class=\"mw-redirect\" title=\"Mau Mau Uprising\">Mau Mau Uprising</a>.", "no_year_html": "The Governor of Kenya <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Glendale\" title=\"<PERSON>, 1st Baron <PERSON> of Glendale\"><PERSON></a> declares a state of emergency and begins arresting hundreds of suspected leaders of the <a href=\"https://wikipedia.org/wiki/Mau_Mau_Uprising\" class=\"mw-redirect\" title=\"Mau Mau Uprising\">Mau Mau Uprising</a>.", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Glendale", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON><PERSON>_of_Glendale"}, {"title": "Mau Mau Uprising", "link": "https://wikipedia.org/wiki/Mau_Mau_Uprising"}]}, {"year": "1961", "text": "The Soviet Navy performs the first armed test of a submarine-launched ballistic missile, launching an R-13 from a Golf-class submarine.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/Soviet_Navy\" title=\"Soviet Navy\">Soviet Navy</a> performs the first armed test of a <a href=\"https://wikipedia.org/wiki/Submarine-launched_ballistic_missile\" title=\"Submarine-launched ballistic missile\">submarine-launched ballistic missile</a>, launching an <a href=\"https://wikipedia.org/wiki/R-13_(missile)\" title=\"R-13 (missile)\">R-13</a> from a <a href=\"https://wikipedia.org/wiki/Golf-class_submarine\" title=\"Golf-class submarine\">Golf-class submarine</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Navy\" title=\"Soviet Navy\">Soviet Navy</a> performs the first armed test of a <a href=\"https://wikipedia.org/wiki/Submarine-launched_ballistic_missile\" title=\"Submarine-launched ballistic missile\">submarine-launched ballistic missile</a>, launching an <a href=\"https://wikipedia.org/wiki/R-13_(missile)\" title=\"R-13 (missile)\">R-13</a> from a <a href=\"https://wikipedia.org/wiki/Golf-class_submarine\" title=\"Golf-class submarine\">Golf-class submarine</a>.", "links": [{"title": "Soviet Navy", "link": "https://wikipedia.org/wiki/Soviet_Navy"}, {"title": "Submarine-launched ballistic missile", "link": "https://wikipedia.org/wiki/Submarine-launched_ballistic_missile"}, {"title": "R-13 (missile)", "link": "https://wikipedia.org/wiki/R-13_(missile)"}, {"title": "Golf-class submarine", "link": "https://wikipedia.org/wiki/Golf-class_submarine"}]}, {"year": "1962", "text": "China launches simultaneous offensives in Ladakh and across the McMahon Line, igniting the Sino-Indian War.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> launches simultaneous offensives in <a href=\"https://wikipedia.org/wiki/Ladakh\" title=\"Ladakh\">Ladakh</a> and across the <a href=\"https://wikipedia.org/wiki/McMahon_Line\" title=\"McMahon Line\">McMahon Line</a>, igniting the <a href=\"https://wikipedia.org/wiki/Sino-Indian_War\" title=\"Sino-Indian War\">Sino-Indian War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> launches simultaneous offensives in <a href=\"https://wikipedia.org/wiki/Ladakh\" title=\"Ladakh\">Ladakh</a> and across the <a href=\"https://wikipedia.org/wiki/McMahon_Line\" title=\"McMahon Line\">McMahon Line</a>, igniting the <a href=\"https://wikipedia.org/wiki/Sino-Indian_War\" title=\"Sino-Indian War\">Sino-Indian War</a>.", "links": [{"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "Ladakh", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>h"}, {"title": "McMahon Line", "link": "https://wikipedia.org/wiki/McMahon_Line"}, {"title": "Sino-Indian War", "link": "https://wikipedia.org/wiki/Sino-Indian_War"}]}, {"year": "1973", "text": "Watergate scandal: \"Saturday Night Massacre\": United States President <PERSON> fires U.S. Attorney General <PERSON> and Deputy Attorney General <PERSON> after they refuse to fire special prosecutor <PERSON>, who is finally fired by Solicitor General <PERSON>.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: \"<a href=\"https://wikipedia.org/wiki/Saturday_Night_Massacre\" title=\"Saturday Night Massacre\">Saturday Night Massacre</a>\": United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> fires U.S. Attorney General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Deputy Attorney General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> after they refuse to fire special prosecutor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who is finally fired by Solicitor General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: \"<a href=\"https://wikipedia.org/wiki/Saturday_Night_Massacre\" title=\"Saturday Night Massacre\">Saturday Night Massacre</a>\": United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> fires U.S. Attorney General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Deputy Attorney General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> after they refuse to fire special prosecutor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who is finally fired by Solicitor General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "Saturday Night Massacre", "link": "https://wikipedia.org/wiki/Saturday_Night_Massacre"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "The Sydney Opera House is opened by Elizabeth II after 14 years of construction.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/Sydney_Opera_House\" title=\"Sydney Opera House\">Sydney Opera House</a> is opened by <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a> after 14 years of construction.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sydney_Opera_House\" title=\"Sydney Opera House\">Sydney Opera House</a> is opened by <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a> after 14 years of construction.", "links": [{"title": "Sydney Opera House", "link": "https://wikipedia.org/wiki/Sydney_Opera_House"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}]}, {"year": "1976", "text": "The Luling-Destrehan Ferry MV George Prince is struck by the Norwegian freighter SS Frosta while crossing the Mississippi River in St. Charles Parish, Louisiana. Seventy-eight passengers and crew die, and only 18 people aboard the ferry survive.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/Luling%E2%80%93Destrehan_Ferry\" title=\"Luling-Destrehan Ferry\">Luling-Destrehan Ferry</a> <a href=\"https://wikipedia.org/wiki/MV_<PERSON>_<PERSON>_ferry_disaster\" title=\"MV George Prince ferry disaster\">MV <i><PERSON></i> is struck</a> by the Norwegian freighter <a href=\"https://wikipedia.org/wiki/SS_Frosta\" title=\"SS Frosta\">SS <i>Frosta</i></a> while crossing the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> in <a href=\"https://wikipedia.org/wiki/St._Charles_Parish,_Louisiana\" title=\"St. Charles Parish, Louisiana\">St. Charles Parish, Louisiana</a>. Seventy-eight passengers and crew die, and only 18 people aboard the ferry survive.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Luling%E2%80%93Destrehan_Ferry\" title=\"Luling-Destrehan Ferry\">Luling-Destrehan Ferry</a> <a href=\"https://wikipedia.org/wiki/MV_<PERSON>_<PERSON>_ferry_disaster\" title=\"MV George Prince ferry disaster\">MV <i><PERSON></i> is struck</a> by the Norwegian freighter <a href=\"https://wikipedia.org/wiki/SS_Frosta\" title=\"SS Frosta\">SS <i>Frosta</i></a> while crossing the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> in <a href=\"https://wikipedia.org/wiki/St._Charles_Parish,_Louisiana\" title=\"St. Charles Parish, Louisiana\">St. Charles Parish, Louisiana</a>. Seventy-eight passengers and crew die, and only 18 people aboard the ferry survive.", "links": [{"title": "Luling-<PERSON><PERSON><PERSON> Ferry", "link": "https://wikipedia.org/wiki/Luling%E2%80%93Dest<PERSON><PERSON>_Ferry"}, {"title": "MV George Prince ferry disaster", "link": "https://wikipedia.org/wiki/MV_<PERSON>_<PERSON>_ferry_disaster"}, {"title": "SS Frosta", "link": "https://wikipedia.org/wiki/SS_Frosta"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}, {"title": "St. Charles Parish, Louisiana", "link": "https://wikipedia.org/wiki/St._Charles_Parish,_Louisiana"}]}, {"year": "1977", "text": "A plane carrying the rock band Lynyrd Skynyrd crashes in woodland in Mississippi, United States. Six people, including three band members, are killed.", "html": "1977 - A plane carrying the rock band <a href=\"https://wikipedia.org/wiki/Lynyrd_Skynyrd\" title=\"Lynyrd Skynyrd\">Lynyrd Skynyrd</a> <a href=\"https://wikipedia.org/wiki/Lynyrd_Skynyrd_plane_crash\" title=\"Lynyrd Skynyrd plane crash\">crashes</a> in woodland in <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>, United States. Six people, including three band members, are killed.", "no_year_html": "A plane carrying the rock band <a href=\"https://wikipedia.org/wiki/Lynyrd_Skynyrd\" title=\"Lynyrd Skynyrd\">Lynyrd Skynyrd</a> <a href=\"https://wikipedia.org/wiki/Lynyrd_Skynyrd_plane_crash\" title=\"Lynyrd Skynyrd plane crash\">crashes</a> in woodland in <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>, United States. Six people, including three band members, are killed.", "links": [{"title": "Lynyrd Skynyrd", "link": "https://wikipedia.org/wiki/Lynyrd_Skynyrd"}, {"title": "Lynyrd Skynyrd plane crash", "link": "https://wikipedia.org/wiki/Lynyrd_Skynyrd_plane_crash"}, {"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}]}, {"year": "1981", "text": "Two police officers and a <PERSON><PERSON><PERSON>'s armored car guard are killed during an armed robbery carried out by members of the Black Liberation Army and Weather Underground in Nanuet, New York.", "html": "1981 - Two police officers and a <a href=\"https://wikipedia.org/wiki/Brink%27s\" title=\"<PERSON><PERSON><PERSON>'s\"><PERSON><PERSON><PERSON>'s</a> armored car guard are killed during an <a href=\"https://wikipedia.org/wiki/Brink%27s_robbery_(1981)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s robbery (1981)\">armed robbery</a> carried out by members of the <a href=\"https://wikipedia.org/wiki/Black_Liberation_Army\" title=\"Black Liberation Army\">Black Liberation Army</a> and <a href=\"https://wikipedia.org/wiki/Weather_Underground\" title=\"Weather Underground\">Weather Underground</a> in <a href=\"https://wikipedia.org/wiki/Nanuet,_New_York\" title=\"Nanuet, New York\">Nanuet, New York</a>.", "no_year_html": "Two police officers and a <a href=\"https://wikipedia.org/wiki/Brink%27s\" title=\"<PERSON><PERSON><PERSON>'s\"><PERSON><PERSON><PERSON>'s</a> armored car guard are killed during an <a href=\"https://wikipedia.org/wiki/Brink%27s_robbery_(1981)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s robbery (1981)\">armed robbery</a> carried out by members of the <a href=\"https://wikipedia.org/wiki/Black_Liberation_Army\" title=\"Black Liberation Army\">Black Liberation Army</a> and <a href=\"https://wikipedia.org/wiki/Weather_Underground\" title=\"Weather Underground\">Weather Underground</a> in <a href=\"https://wikipedia.org/wiki/Nanuet,_New_York\" title=\"Nanuet, New York\">Nanuet, New York</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>'s", "link": "https://wikipedia.org/wiki/Brink%27s"}, {"title": "<PERSON><PERSON><PERSON>'s robbery (1981)", "link": "https://wikipedia.org/wiki/Brink%27s_robbery_(1981)"}, {"title": "Black Liberation Army", "link": "https://wikipedia.org/wiki/Black_Liberation_Army"}, {"title": "Weather Underground", "link": "https://wikipedia.org/wiki/Weather_Underground"}, {"title": "Nanuet, New York", "link": "https://wikipedia.org/wiki/Nanuet,_New_York"}]}, {"year": "1982", "text": "During the UEFA Cup match between FC Spartak Moscow and HFC Haarlem, 66 people are crushed to death in the Luzhniki disaster.", "html": "1982 - During the UEFA Cup match between <a href=\"https://wikipedia.org/wiki/FC_Spartak_Moscow\" title=\"FC Spartak Moscow\">FC Spartak Moscow</a> and <a href=\"https://wikipedia.org/wiki/HFC_Haarlem\" title=\"HFC Haarlem\">HFC Haarlem</a>, 66 people are crushed to death in the <a href=\"https://wikipedia.org/wiki/Luzhniki_disaster\" title=\"Luzhniki disaster\">Luzhniki disaster</a>.", "no_year_html": "During the UEFA Cup match between <a href=\"https://wikipedia.org/wiki/FC_Spartak_Moscow\" title=\"FC Spartak Moscow\">FC Spartak Moscow</a> and <a href=\"https://wikipedia.org/wiki/HFC_Haarlem\" title=\"HFC Haarlem\">HFC Haarlem</a>, 66 people are crushed to death in the <a href=\"https://wikipedia.org/wiki/Luzhniki_disaster\" title=\"Luzhniki disaster\">Luzhniki disaster</a>.", "links": [{"title": "FC Spartak Moscow", "link": "https://wikipedia.org/wiki/FC_Spartak_Moscow"}, {"title": "HFC Haarlem", "link": "https://wikipedia.org/wiki/HFC_Haarlem"}, {"title": "Luzhniki disaster", "link": "https://wikipedia.org/wiki/Luzhniki_disaster"}]}, {"year": "1986", "text": "Aeroflot Flight 6502 crashes while landing at Kuibyshev Airport (now Kuromoch International Airport) in Kuibyshev (now present-day Samara, Russia), killing 70 people.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_6502\" title=\"Aeroflot Flight 6502\">Aeroflot Flight 6502</a> crashes while landing at Kuibyshev Airport (now <a href=\"https://wikipedia.org/wiki/Kurumoch_International_Airport\" title=\"Kurumoch International Airport\">Kuromoch International Airport</a>) in Kuibyshev (now present-day <a href=\"https://wikipedia.org/wiki/Samara\" title=\"Samara\">Samara</a>, <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>), killing 70 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_6502\" title=\"Aeroflot Flight 6502\">Aeroflot Flight 6502</a> crashes while landing at Kuibyshev Airport (now <a href=\"https://wikipedia.org/wiki/Kurumoch_International_Airport\" title=\"Kurumoch International Airport\">Kuromoch International Airport</a>) in Kuibyshev (now present-day <a href=\"https://wikipedia.org/wiki/Samara\" title=\"Samara\">Samara</a>, <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>), killing 70 people.", "links": [{"title": "Aeroflot Flight 6502", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_6502"}, {"title": "Kurumoch International Airport", "link": "https://wikipedia.org/wiki/Kurumoch_International_Airport"}, {"title": "Samara", "link": "https://wikipedia.org/wiki/Samara"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}]}, {"year": "1991", "text": "A 6.8 Mw earthquake strikes the Uttarkashi region of India, killing more than 1,000 people.", "html": "1991 - A 6.8 <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">M<sub>w</sub></a> earthquake <a href=\"https://wikipedia.org/wiki/1991_Uttarkashi_earthquake\" title=\"1991 Uttarkashi earthquake\">strikes</a> the <a href=\"https://wikipedia.org/wiki/Uttarkashi_district\" title=\"Uttarkashi district\">Uttarkashi</a> region of India, killing more than 1,000 people.", "no_year_html": "A 6.8 <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">M<sub>w</sub></a> earthquake <a href=\"https://wikipedia.org/wiki/1991_Uttarkashi_earthquake\" title=\"1991 Uttarkashi earthquake\">strikes</a> the <a href=\"https://wikipedia.org/wiki/Uttarkashi_district\" title=\"Uttarkashi district\">Uttarkashi</a> region of India, killing more than 1,000 people.", "links": [{"title": "Moment magnitude scale", "link": "https://wikipedia.org/wiki/Moment_magnitude_scale"}, {"title": "1991 Uttarkashi earthquake", "link": "https://wikipedia.org/wiki/1991_Uttarkashi_earthquake"}, {"title": "Uttarkashi district", "link": "https://wikipedia.org/wiki/Uttarkashi_district"}]}, {"year": "1991", "text": "A massive firestorm breaks out in the hills of Oakland and Berkeley, California killing 25 people and destroying more than 3,000 homes, apartments and condominiums.", "html": "1991 - A massive <a href=\"https://wikipedia.org/wiki/Firestorm\" title=\"Firestorm\">firestorm</a> breaks out <a href=\"https://wikipedia.org/wiki/Oakland_firestorm_of_1991\" title=\"Oakland firestorm of 1991\">in the hills of Oakland and Berkeley, California</a> killing 25 people and destroying more than 3,000 homes, apartments and condominiums.", "no_year_html": "A massive <a href=\"https://wikipedia.org/wiki/Firestorm\" title=\"Firestorm\">firestorm</a> breaks out <a href=\"https://wikipedia.org/wiki/Oakland_firestorm_of_1991\" title=\"Oakland firestorm of 1991\">in the hills of Oakland and Berkeley, California</a> killing 25 people and destroying more than 3,000 homes, apartments and condominiums.", "links": [{"title": "Firestorm", "link": "https://wikipedia.org/wiki/Firestorm"}, {"title": "Oakland firestorm of 1991", "link": "https://wikipedia.org/wiki/Oakland_firestorm_of_1991"}]}, {"year": "1995", "text": "Space Shuttle Columbia launches on STS-73.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-73\" title=\"STS-73\">STS-73</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-73\" title=\"STS-73\">STS-73</a>.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-73", "link": "https://wikipedia.org/wiki/STS-73"}]}, {"year": "2002", "text": "Top Gear, the revived popular British TV motoring magazine, premieres on BBC.", "html": "2002 - <i><a href=\"https://wikipedia.org/wiki/Top_Gear_(2002_TV_series)\" title=\"Top Gear (2002 TV series)\">Top Gear</a></i>, the revived popular British TV motoring magazine, premieres on <a href=\"https://wikipedia.org/wiki/BBC\" title=\"BBC\">BBC</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Top_Gear_(2002_TV_series)\" title=\"Top Gear (2002 TV series)\">Top Gear</a></i>, the revived popular British TV motoring magazine, premieres on <a href=\"https://wikipedia.org/wiki/BBC\" title=\"BBC\">BBC</a>.", "links": [{"title": "Top Gear (2002 TV series)", "link": "https://wikipedia.org/wiki/Top_Gear_(2002_TV_series)"}, {"title": "BBC", "link": "https://wikipedia.org/wiki/BBC"}]}, {"year": "2003", "text": "The Sloan Great Wall, once the largest cosmic structures known to humanity, is discovered by students at Princeton University.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_Great_Wall\" title=\"Sloan Great Wall\">Sloan Great Wall</a>, once the <a href=\"https://wikipedia.org/wiki/List_of_largest_cosmic_structures\" title=\"List of largest cosmic structures\">largest cosmic structures known to humanity</a>, is discovered by students at <a href=\"https://wikipedia.org/wiki/Princeton_University\" title=\"Princeton University\">Princeton University</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_Great_Wall\" title=\"Sloan Great Wall\">Sloan Great Wall</a>, once the <a href=\"https://wikipedia.org/wiki/List_of_largest_cosmic_structures\" title=\"List of largest cosmic structures\">largest cosmic structures known to humanity</a>, is discovered by students at <a href=\"https://wikipedia.org/wiki/Princeton_University\" title=\"Princeton University\">Princeton University</a>.", "links": [{"title": "Sloan Great Wall", "link": "https://wikipedia.org/wiki/<PERSON>_Great_Wall"}, {"title": "List of largest cosmic structures", "link": "https://wikipedia.org/wiki/List_of_largest_cosmic_structures"}, {"title": "Princeton University", "link": "https://wikipedia.org/wiki/Princeton_University"}]}, {"year": "2005", "text": "The general conference of the United Nations Educational, Scientific and Cultural Organization (UNESCO) passes the Convention on the Protection and Promotion of the Diversity of Cultural Expressions.", "html": "2005 - The general conference of the <a href=\"https://wikipedia.org/wiki/United_Nations_Educational,_Scientific_and_Cultural_Organization\" class=\"mw-redirect\" title=\"United Nations Educational, Scientific and Cultural Organization\">United Nations Educational, Scientific and Cultural Organization</a> (UNESCO) passes the <a href=\"https://wikipedia.org/wiki/Convention_on_the_Protection_and_Promotion_of_the_Diversity_of_Cultural_Expressions\" title=\"Convention on the Protection and Promotion of the Diversity of Cultural Expressions\">Convention on the Protection and Promotion of the Diversity of Cultural Expressions</a>.", "no_year_html": "The general conference of the <a href=\"https://wikipedia.org/wiki/United_Nations_Educational,_Scientific_and_Cultural_Organization\" class=\"mw-redirect\" title=\"United Nations Educational, Scientific and Cultural Organization\">United Nations Educational, Scientific and Cultural Organization</a> (UNESCO) passes the <a href=\"https://wikipedia.org/wiki/Convention_on_the_Protection_and_Promotion_of_the_Diversity_of_Cultural_Expressions\" title=\"Convention on the Protection and Promotion of the Diversity of Cultural Expressions\">Convention on the Protection and Promotion of the Diversity of Cultural Expressions</a>.", "links": [{"title": "United Nations Educational, Scientific and Cultural Organization", "link": "https://wikipedia.org/wiki/United_Nations_Educational,_Scientific_and_Cultural_Organization"}, {"title": "Convention on the Protection and Promotion of the Diversity of Cultural Expressions", "link": "https://wikipedia.org/wiki/Convention_on_the_Protection_and_Promotion_of_the_Diversity_of_Cultural_Expressions"}]}, {"year": "2011", "text": "Libyan Crisis: Rebel forces capture Libyan dictator <PERSON><PERSON><PERSON> and his son <PERSON><PERSON><PERSON><PERSON> in his hometown of Sirte and kill him shortly thereafter, ending the first Libyan civil war.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Libyan_Crisis_(2011%E2%80%93present)\" class=\"mw-redirect\" title=\"Libyan Crisis (2011-present)\">Libyan Crisis</a>: Rebel forces capture Libyan dictator <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and his son <a href=\"https://wikipedia.org/wiki/Mutassim_Gaddafi\" title=\"Mutassim Gaddafi\"><PERSON><PERSON><PERSON><PERSON></a> in his hometown of Sirte and <a href=\"https://wikipedia.org/wiki/Death_of_Muammar_Gaddafi\" class=\"mw-redirect\" title=\"Death of Muammar <PERSON>ad<PERSON>\">kill him</a> shortly thereafter, ending the <a href=\"https://wikipedia.org/wiki/First_Libyan_civil_war\" class=\"mw-redirect\" title=\"First Libyan civil war\">first Libyan civil war</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Libyan_Crisis_(2011%E2%80%93present)\" class=\"mw-redirect\" title=\"Libyan Crisis (2011-present)\">Libyan Crisis</a>: Rebel forces capture Libyan dictator <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and his son <a href=\"https://wikipedia.org/wiki/Mutassim_Gaddafi\" title=\"Mutassim Gaddafi\"><PERSON><PERSON><PERSON><PERSON></a> in his hometown of Sirte and <a href=\"https://wikipedia.org/wiki/Death_of_Muammar_Gaddafi\" class=\"mw-redirect\" title=\"Death of Muammar <PERSON>\">kill him</a> shortly thereafter, ending the <a href=\"https://wikipedia.org/wiki/First_Libyan_civil_war\" class=\"mw-redirect\" title=\"First Libyan civil war\">first Libyan civil war</a>.", "links": [{"title": "Libyan Crisis (2011-present)", "link": "https://wikipedia.org/wiki/Libyan_Crisis_(2011%E2%80%93present)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mutassim_Gaddafi"}, {"title": "Death of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "First Libyan civil war", "link": "https://wikipedia.org/wiki/First_Libyan_civil_war"}]}, {"year": "2017", "text": "Syrian civil war: The Syrian Democratic Forces (SDF) declare victory in the Raqqa campaign.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) declare victory in the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_campaign_(2016%E2%80%932017)\" title=\"<PERSON><PERSON><PERSON> campaign (2016-2017)\"><PERSON><PERSON><PERSON> campaign</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) declare victory in the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_campaign_(2016%E2%80%932017)\" title=\"Raqqa campaign (2016-2017)\">Raqqa campaign</a>.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Syrian Democratic Forces", "link": "https://wikipedia.org/wiki/Syrian_Democratic_Forces"}, {"title": "<PERSON><PERSON><PERSON> campaign (2016-2017)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_campaign_(2016%E2%80%932017)"}]}, {"year": "2022", "text": "<PERSON> steps down as British Prime Minister and leader of the Conservative Party amid the country's political crisis, serving for the least time of any British Prime Minister [49 days].", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> steps down as <a href=\"https://wikipedia.org/wiki/British_prime_minister\" class=\"mw-redirect\" title=\"British prime minister\">British Prime Minister</a> and leader of the <a href=\"https://wikipedia.org/wiki/Conservative_Party_(UK)\" title=\"Conservative Party (UK)\">Conservative Party</a> amid the country's political crisis, serving for the least time of any British Prime Minister [49 days].", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> steps down as <a href=\"https://wikipedia.org/wiki/British_prime_minister\" class=\"mw-redirect\" title=\"British prime minister\">British Prime Minister</a> and leader of the <a href=\"https://wikipedia.org/wiki/Conservative_Party_(UK)\" title=\"Conservative Party (UK)\">Conservative Party</a> amid the country's political crisis, serving for the least time of any British Prime Minister [49 days].", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "British prime minister", "link": "https://wikipedia.org/wiki/British_prime_minister"}, {"title": "Conservative Party (UK)", "link": "https://wikipedia.org/wiki/Conservative_Party_(UK)"}]}], "Births": [{"year": "888", "text": "<PERSON>, emperor of Later Liang (d. 923)", "html": "888 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/Later_Liang_(Five_Dynasties)\" title=\"Later Liang (Five Dynasties)\"><PERSON> Liang</a> (d. 923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/Later_Liang_(Five_Dynasties)\" title=\"<PERSON> Liang (Five Dynasties)\"><PERSON> Liang</a> (d. 923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Liang (Five Dynasties)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Five_Dynasties)"}]}, {"year": "1475", "text": "<PERSON>, Italian poet and playwright (d. 1525)", "html": "1475 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> di <PERSON>\"><PERSON></a>, Italian poet and playwright (d. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> di <PERSON>\"><PERSON> di <PERSON></a>, Italian poet and playwright (d. 1525)", "links": [{"title": "<PERSON> di <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1496", "text": "<PERSON>, Duke of Guise (d. 1550)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Guise\" title=\"<PERSON>, Duke of Guise\"><PERSON>, Duke of Guise</a> (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Guise\" title=\"<PERSON>, Duke of Guise\"><PERSON>, Duke of Guise</a> (d. 1550)", "links": [{"title": "<PERSON>, Duke of Guise", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_G<PERSON>"}]}, {"year": "1554", "text": "<PERSON><PERSON><PERSON>, Hungarian writer and noble (d. 1594)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/B%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian writer and noble (d. 1594)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian writer and noble (d. 1594)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A1lint_Balassi"}]}, {"year": "1612", "text": "<PERSON>, 1st Earl of Burlington, Anglo-Irish nobleman, Lord High Treasurer of Ireland, Cavalier (d. 1698)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Burlington\" title=\"<PERSON>, 1st Earl of Burlington\"><PERSON>, 1st Earl of Burlington</a>, Anglo-Irish nobleman, Lord High Treasurer of Ireland, Cavalier (d. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Burlington\" title=\"<PERSON>, 1st Earl of Burlington\"><PERSON>, 1st Earl of Burlington</a>, Anglo-Irish nobleman, Lord High Treasurer of Ireland, Cavalier (d. 1698)", "links": [{"title": "<PERSON>, 1st Earl of Burlington", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Burlington"}]}, {"year": "1616", "text": "<PERSON>, Danish physician, mathematician, and theologian (d. 1680)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish physician, mathematician, and theologian (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish physician, mathematician, and theologian (d. 1680)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1620", "text": "<PERSON><PERSON><PERSON>, Dutch painter (d. 1691)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1691)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1632", "text": "<PERSON>, English politician (d. 1711)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(spendthrift)\" title=\"<PERSON> (spendthrift)\"><PERSON></a>, English politician (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(spendthrift)\" title=\"<PERSON> (spendthrift)\"><PERSON></a>, English politician (d. 1711)", "links": [{"title": "<PERSON> (spendthrift)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(spendthrift)"}]}, {"year": "1660", "text": "<PERSON>, 1st Duke of Ancaster and Kesteven, English politician, Chancellor of the Duchy of Lancaster (d. 1723)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Ancaster_and_Kesteven\" title=\"<PERSON>, 1st Duke of Ancaster and Kesteven\"><PERSON>, 1st Duke of Ancaster and Kesteven</a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Ancaster_and_Kesteven\" title=\"<PERSON>, 1st Duke of Ancaster and Kesteven\"><PERSON>, 1st Duke of Ancaster and Kesteven</a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (d. 1723)", "links": [{"title": "<PERSON>, 1st Duke of Ancaster and Kesteven", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Ancaster_and_<PERSON><PERSON><PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1677", "text": "<PERSON><PERSON>, King of Poland (d. 1766)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Leszczy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, King of Poland (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Leszczy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, King of Poland (d. 1766)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_Leszczy%C5%84ski"}]}, {"year": "1679", "text": "<PERSON>, Prussian jurist and statesman (d. 1755)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian jurist and statesman (d. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian jurist and statesman (d. 1755)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1711", "text": "<PERSON>, American lawyer, jurist, and politician, (d. 1795)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician, (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician, (d. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, Duchess of Gordon, Scottish aristocrat (d. 1779)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Gordon\" title=\"<PERSON>, Duchess of Gordon\"><PERSON>, Duchess of Gordon</a>, Scottish aristocrat (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Gordon\" title=\"<PERSON>, Duchess of Gordon\"><PERSON>, Duchess of Gordon</a>, Scottish aristocrat (d. 1779)", "links": [{"title": "<PERSON>, Duchess of Gordon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_<PERSON>_<PERSON>"}]}, {"year": "1719", "text": "<PERSON><PERSON><PERSON>, German historian, economist, and jurist (d. 1772)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German historian, economist, and jurist (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German historian, economist, and jurist (d. 1772)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON>, Dutch author and poet (d. 1805)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Dutch author and poet (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Dutch author and poet (d. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1759", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician, 8th Lieutenant Governor of Connecticut (d. 1815)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Lieutenant Governor of Connecticut\">Lieutenant Governor of Connecticut</a> (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Lieutenant Governor of Connecticut\">Lieutenant Governor of Connecticut</a> (d. 1815)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Connecticut", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Connecticut"}]}, {"year": "1780", "text": "<PERSON>, French sister of <PERSON> (d. 1825)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sister of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sister of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}]}, {"year": "1784", "text": "<PERSON>, 3rd Viscount <PERSON>, English academic and politician, Prime Minister of the United Kingdom (d. 1865)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Viscount_<PERSON>\" title=\"<PERSON>, 3rd Viscount <PERSON>\"><PERSON>, 3rd Viscount <PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Viscount_<PERSON>\" title=\"<PERSON>, 3rd Viscount <PERSON>\"><PERSON>, 3rd Viscount <PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1865)", "links": [{"title": "<PERSON>, 3rd Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Viscount_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1785", "text": "<PERSON>, English historian and author (d. 1873)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON> Scottish farmer and biologist (d. 1874)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. Scottish farmer and biologist (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. Scottish farmer and biologist (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON><PERSON><PERSON>, Swiss architect and educator, designed the Natural History Museum of Basel (d. 1854)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss architect and educator, designed the <a href=\"https://wikipedia.org/wiki/Natural_History_Museum_of_Basel\" title=\"Natural History Museum of Basel\">Natural History Museum of Basel</a> (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss architect and educator, designed the <a href=\"https://wikipedia.org/wiki/Natural_History_Museum_of_Basel\" title=\"Natural History Museum of Basel\">Natural History Museum of Basel</a> (d. 1854)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Natural History Museum of Basel", "link": "https://wikipedia.org/wiki/Natural_History_Museum_of_Basel"}]}, {"year": "1808", "text": "<PERSON>, German geographer and journalist (d. 1875)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and journalist (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and journalist (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON><PERSON><PERSON>, Iranian religious leader, founded Bábism (d. 1850)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/B%C3%A1b\" title=\"Báb\"><PERSON><PERSON><PERSON></a>, Iranian religious leader, founded <a href=\"https://wikipedia.org/wiki/B%C3%A1bism\" title=\"Bábism\">Bábism</a> (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A1b\" title=\"Báb\"><PERSON><PERSON><PERSON></a>, Iranian religious leader, founded <a href=\"https://wikipedia.org/wiki/B%C3%A1bism\" title=\"Bábism\">Bábism</a> (d. 1850)", "links": [{"title": "B<PERSON>b", "link": "https://wikipedia.org/wiki/B%C3%A1b"}, {"title": "Bábism", "link": "https://wikipedia.org/wiki/B%C3%A1bism"}]}, {"year": "1822", "text": "<PERSON>, English lawyer and judge (d. 1896)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "Con<PERSON><PERSON>, German architect and theorist (d. 1894)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/Con<PERSON>in_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Con<PERSON><PERSON></a>, German architect and theorist (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_<PERSON>\" title=\"Constantin <PERSON>\"><PERSON><PERSON><PERSON></a>, German architect and theorist (d. 1894)", "links": [{"title": "Constant<PERSON>", "link": "https://wikipedia.org/wiki/Constantin_<PERSON><PERSON><PERSON>"}]}, {"year": "1847", "text": "<PERSON><PERSON>, Norwegian painter (d. 1906)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Frits_Thaulow\" title=\"Frits Thaulow\"><PERSON><PERSON> Thaulow</a>, Norwegian painter (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frits_Thaulow\" title=\"Frits Thaulow\"><PERSON><PERSON> Thaulow</a>, Norwegian painter (d. 1906)", "links": [{"title": "Frits Thaulow", "link": "https://wikipedia.org/wiki/Frits_Thaulow"}]}, {"year": "1854", "text": "<PERSON>, French soldier and poet (d. 1891)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and poet (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and poet (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, English union leader and politician, President of the Board of Trade (d. 1943)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English union leader and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Board_of_Trade\" title=\"President of the Board of Trade\">President of the Board of Trade</a> (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English union leader and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Board_of_Trade\" title=\"President of the Board of Trade\">President of the Board of Trade</a> (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Board of Trade", "link": "https://wikipedia.org/wiki/President_of_the_Board_of_Trade"}]}, {"year": "1859", "text": "<PERSON>, American psychologist and philosopher (d. 1952)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and philosopher (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and philosopher (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American banker and politician, 6th Governor of New Mexico (d. 1951)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico\" title=\"Governor of New Mexico\">Governor of New Mexico</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico\" title=\"Governor of New Mexico\">Governor of New Mexico</a> (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of New Mexico", "link": "https://wikipedia.org/wiki/Governor_of_New_Mexico"}]}, {"year": "1873", "text": "<PERSON><PERSON>, Canadian author and suffragist (d. 1951)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian author and suffragist (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian author and suffragist (d. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON>, Finnish politician (d. 1918)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1874", "text": "<PERSON>, American composer (d. 1954)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American businessman (d. 1915)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American actress (d. 1965)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Hungarian-American actor (d. 1956)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lugos<PERSON>\"><PERSON><PERSON></a>, Hungarian-American actor (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lugosi\"><PERSON><PERSON></a>, Hungarian-American actor (d. 1956)", "links": [{"title": "Bela Lugosi", "link": "https://wikipedia.org/wiki/Bela_Lugosi"}]}, {"year": "1883", "text": "<PERSON>, American engineer (d. 1963)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON> <PERSON><PERSON><PERSON> of Japan (d. 1981)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON><PERSON><PERSON></a> of Japan (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON><PERSON><PERSON></a> of Japan (d. 1981)", "links": [{"title": "Prince <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Austrian priest and saint (d. 1944)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian priest and saint (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian priest and saint (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian bagpipe player (d. 1968)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Maaker\" title=\"Aleksander Maaker\">Alek<PERSON><PERSON> Maaker</a>, Estonian <a href=\"https://wikipedia.org/wiki/Bagpipe\" class=\"mw-redirect\" title=\"Bagpipe\">bagpipe</a> player (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Maaker\" title=\"Aleksander Maaker\"><PERSON><PERSON><PERSON><PERSON> Maaker</a>, Estonian <a href=\"https://wikipedia.org/wiki/Bagpipe\" class=\"mw-redirect\" title=\"Bagpipe\">bagpipe</a> player (d. 1968)", "links": [{"title": "Aleksander Maaker", "link": "https://wikipedia.org/wiki/Aleksander_Maaker"}, {"title": "Bagpipe", "link": "https://wikipedia.org/wiki/Bagpipe"}]}, {"year": "1891", "text": "<PERSON>, American historian and author (d. 1973)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, English physicist and academic, Nobel Prize laureate (d. 1974)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1893", "text": "<PERSON>, American actor, director, and screenwriter (d. 1940)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chase\"><PERSON></a>, American actor, director, and screenwriter (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chase\"><PERSON></a>, American actor, director, and screenwriter (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American model and actress (d. 1920)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thomas"}]}, {"year": "1895", "text": "<PERSON>, American actor (d. 1969)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1969)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1895", "text": "<PERSON><PERSON>, American writer/director (d. 1985)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Morrie Ryskind\"><PERSON><PERSON></a>, American writer/director (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Morrie Ryskind\"><PERSON><PERSON></a>, American writer/director (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, South Korean general (d. 1970)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Un\"><PERSON></a>, South Korean general (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Un\"><PERSON></a>, South Korean general (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Sudanese politician, 3rd President of Sudan (d. 1969)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Sudan\" class=\"mw-redirect\" title=\"President of Sudan\">President of Sudan</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Sudan\" class=\"mw-redirect\" title=\"President of Sudan\">President of Sudan</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Sudan", "link": "https://wikipedia.org/wiki/President_of_Sudan"}]}, {"year": "1900", "text": "<PERSON>, American lieutenant, lawyer, and politician (d. 1974)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American film composer (d. 1942)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film composer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film composer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American-English singer, actress, and dancer (d. 1993)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Adelaide_Hall\" title=\"Adelaide Hall\">Adelaide Hall</a>, American-English singer, actress, and dancer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_Hall\" title=\"Adelaide Hall\">Adelaide Hall</a>, American-English singer, actress, and dancer (d. 1993)", "links": [{"title": "Adelaide Hall", "link": "https://wikipedia.org/wiki/Adelaide_Hall"}]}, {"year": "1904", "text": "<PERSON>, Scottish-Canadian minister and politician, 7th Premier of Saskatchewan (d. 1986)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian minister and politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Saskatchewan\" title=\"Premier of Saskatchewan\">Premier of Saskatchewan</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian minister and politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Saskatchewan\" title=\"Premier of Saskatchewan\">Premier of Saskatchewan</a> (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Saskatchewan", "link": "https://wikipedia.org/wiki/Premier_of_Saskatchewan"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American educator and activist (d. 2006)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>cMillan\"><PERSON><PERSON></a>, American educator and activist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>Millan\" title=\"<PERSON><PERSON> McMillan\"><PERSON><PERSON></a>, American educator and activist (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enolia_M<PERSON>Millan"}]}, {"year": "1904", "text": "<PERSON>, English actress, singer, and producer (d. 1986)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and producer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and producer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American actress and television personality (d. 2001)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and television personality (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and television personality (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American singer-songwriter, actor, and radio show host (d. 1989)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, actor, and radio show host (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, actor, and radio show host (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American actress and photographer (d. 2014)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and photographer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and photographer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Japanese painter (d. 1993)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Chinese director and playwright (d. 2013)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese director and playwright (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese director and playwright (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Turkish singer-songwriter (d. 1985)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>uh<PERSON>_<PERSON>\" title=\"<PERSON>uh<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ruhi_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American singer-songwriter and banjo player (d. 1998)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Grandpa_<PERSON>\" title=\"Grandpa <PERSON>\">Grandpa <PERSON></a>, American singer-songwriter and banjo player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grandpa_<PERSON>\" title=\"Grandpa <PERSON>\">Grandpa <PERSON></a>, American singer-songwriter and banjo player (d. 1998)", "links": [{"title": "Grandpa <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American actor, dancer, and choreographer (d. 2006)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, dancer, and choreographer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, dancer, and choreographer (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON><PERSON>, German-French activist and diplomat (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-French activist and diplomat (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-French activist and diplomat (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9<PERSON>ne_<PERSON>l"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Estonian anti-communist, freedom fighter and forest brother (d. 1951)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian anti-communist, freedom fighter and <a href=\"https://wikipedia.org/wiki/Forest_brother\" class=\"mw-redirect\" title=\"Forest brother\">forest brother</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian anti-communist, freedom fighter and <a href=\"https://wikipedia.org/wiki/Forest_brother\" class=\"mw-redirect\" title=\"Forest brother\">forest brother</a> (d. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Forest brother", "link": "https://wikipedia.org/wiki/<PERSON>_brother"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, French actor, director, producer, and screenwriter (d. 1973)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor, director, producer, and screenwriter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor, director, producer, and screenwriter (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1918", "text": "<PERSON>, German soldier and pilot (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American-German soldier and journalist (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-German soldier and journalist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-German soldier and journalist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American chemist and academic (d. 2008)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Tracy_Hall\" title=\"Tracy Hall\"><PERSON></a>, American chemist and academic (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tracy_Hall\" title=\"Tracy Hall\"><PERSON></a>, American chemist and academic (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tracy_Hall"}]}, {"year": "1920", "text": "<PERSON>, American illustrator (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Estonian-French linguist and academic (d. 2011)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-French linguist and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-French linguist and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, 6th President of Guyana (d. 2009)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 6th President of Guyana (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 6th President of Guyana (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, Chief Minister of West Bengal (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "1921", "text": "<PERSON>, American race car driver (d. 1955)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Dutch poet and author (d. 2001)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actor (d. 1992)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1992)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1922", "text": "<PERSON>, American opera singer (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Franco_Ventriglia\" title=\"<PERSON>\"><PERSON></a>, American opera singer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco_Ventriglia\" title=\"Franco Ventriglia\"><PERSON></a>, American opera singer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Ventriglia"}]}, {"year": "1923", "text": "<PERSON>, American conductor and musicologist (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Craft\"><PERSON></a>, American conductor and musicologist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Robert Craft\"><PERSON></a>, American conductor and musicologist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American poet, playwright, and critic (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" class=\"mw-redirect\" title=\"<PERSON> (playwright)\"><PERSON></a>, American poet, playwright, and critic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" class=\"mw-redirect\" title=\"<PERSON> (playwright)\"><PERSON></a>, American poet, playwright, and critic (d. 2014)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_(playwright)"}]}, {"year": "1925", "text": "<PERSON>, American soldier and journalist (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Art_<PERSON>wald\" title=\"<PERSON> Buchwald\"><PERSON></a>, American soldier and journalist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Buchwald\" title=\"<PERSON> Buch<PERSON>\"><PERSON></a>, American soldier and journalist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Buchwald"}]}, {"year": "1925", "text": "<PERSON>, American record producer and engineer (d. 2002)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer and engineer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer and engineer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Algerian-French actor, director, and screenwriter (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French actor, director, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French actor, director, and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Hungarian journalist and human rights activist (d. 2024)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A1s_B%C3%ADr%C3%B3\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian journalist and human rights activist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A1s_B%C3%ADr%C3%B3\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian journalist and human rights activist (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A1s_B%C3%ADr%C3%B3"}]}, {"year": "1926", "text": "<PERSON>, 3rd <PERSON> of Beaulieu, English lieutenant and politician, founded the National Motor Museum (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>_of_Beaulieu\" title=\"<PERSON>, 3rd Baron <PERSON> of Beaulieu\"><PERSON>, 3rd Baron <PERSON> of Beaulieu</a>, English lieutenant and politician, founded the <a href=\"https://wikipedia.org/wiki/National_Motor_Museum,_Beaulieu\" title=\"National Motor Museum, Beaulieu\">National Motor Museum</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>_of_Beaulieu\" title=\"<PERSON>, 3rd Baron <PERSON> of Beaulieu\"><PERSON>, 3rd Baron <PERSON> of Beaulieu</a>, English lieutenant and politician, founded the <a href=\"https://wikipedia.org/wiki/National_Motor_Museum,_Beaulieu\" title=\"National Motor Museum, Beaulieu\">National Motor Museum</a> (d. 2015)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON> of Beaulieu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>_of_Beaulieu"}, {"title": "National Motor Museum, Beaulieu", "link": "https://wikipedia.org/wiki/National_Motor_Museum,_Beaulieu"}]}, {"year": "1927", "text": "<PERSON>, American psychologist, author, and actress (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Brothers\" title=\"Joyce Brothers\"><PERSON></a>, American psychologist, author, and actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joyce_Brothers\" title=\"Joyce Brothers\"><PERSON></a>, American psychologist, author, and actress (d. 2013)", "links": [{"title": "Joyce Brothers", "link": "https://wikipedia.org/wiki/<PERSON>_Brothers"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Indian poet and critic (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and critic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and critic (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English physician, author, and journalist (d. 2019)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician, author, and journalist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician, author, and journalist (d. 2019)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27D<PERSON>_(physician)"}]}, {"year": "1931", "text": "<PERSON>, American lawyer and politician, 54th Mayor of Pittsburgh (d. 1988)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 54th <a href=\"https://wikipedia.org/wiki/Mayor_of_Pittsburgh\" class=\"mw-redirect\" title=\"Mayor of Pittsburgh\">Mayor of Pittsburgh</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 54th <a href=\"https://wikipedia.org/wiki/Mayor_of_Pittsburgh\" class=\"mw-redirect\" title=\"Mayor of Pittsburgh\">Mayor of Pittsburgh</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Pittsburgh", "link": "https://wikipedia.org/wiki/Mayor_of_Pittsburgh"}]}, {"year": "1931", "text": "<PERSON>, American baseball player and sportscaster (d. 1995)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English businessman (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American football player and coach (d. 2004)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor and singer (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Japanese voice actor (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Rokur%C5%8D_Naya\" title=\"<PERSON><PERSON><PERSON> Naya\"><PERSON><PERSON><PERSON></a>, Japanese voice actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rokur%C5%8D_Naya\" title=\"<PERSON><PERSON><PERSON> Naya\"><PERSON><PERSON><PERSON></a>, Japanese voice actor (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rokur%C5%8D_Naya"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American actress and dancer", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American trumpet player (d. 1974)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American saxophonist (d. 1996)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actor (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actor and singer (d. 2004)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Filipino lawyer and jurist (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and jurist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and jurist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Dominican baseball player and sportscaster", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English author (d. 2017)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tennant\"><PERSON></a>, English author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Emma Tennant\"><PERSON></a>, English author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Italian director and screenwriter (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian director and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian director and screenwriter (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>id<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actress and nun", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and nun", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and nun", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Scottish photographer and educator (d. 2006)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish photographer and educator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish photographer and educator (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English painter, illustrator, and photographer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter, illustrator, and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter, illustrator, and photographer", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Cameroonian filmmaker", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9_Pipa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9_Pipa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian filmmaker", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9_Pipa"}]}, {"year": "1940", "text": "<PERSON>, English singer (d. 2011)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American poet and critic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, English actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor (d. 2003)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, German biologist and geneticist, Nobel Prize laureate", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>-<PERSON>hard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German biologist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German biologist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Christiane_N%C3%<PERSON><PERSON><PERSON>-<PERSON>hard"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1942", "text": "<PERSON>, Dutch cyclist (d. 1992)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Croatian soprano and actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Du<PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du<PERSON>_<PERSON>vi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian soprano and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dunja_Vejzovi%C4%87"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Sri Lankan physicist and philosopher (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan physicist and philosopher (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan physicist and philosopher (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American party planner, created The Loft (d. 2016)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American party planner, created <a href=\"https://wikipedia.org/wiki/The_Loft_(New_York_City)\" title=\"The Loft (New York City)\">The Loft</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American party planner, created <a href=\"https://wikipedia.org/wiki/The_Loft_(New_York_City)\" title=\"The Loft (New York City)\">The Loft</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Loft (New York City)", "link": "https://wikipedia.org/wiki/The_Loft_(New_York_City)"}]}, {"year": "1945", "text": "<PERSON><PERSON>, English drummer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English drummer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American-English sociologist, author, and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English sociologist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English sociologist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American comedian and author (d. 1994)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and author (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Austrian author and playwright, Nobel Prize laureate", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian author and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian author and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1946", "text": "<PERSON>, English director and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Belgian cyclist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English civil servant and academic (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian entertainer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian entertainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American-English actress and composer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English actress and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English actress and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Dutch jurist and politician, Dutch Minister of Justice", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch jurist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Security_and_Justice_(Netherlands)\" class=\"mw-redirect\" title=\"Ministry of Security and Justice (Netherlands)\">Dutch Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch jurist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Security_and_Justice_(Netherlands)\" class=\"mw-redirect\" title=\"Ministry of Security and Justice (Netherlands)\">Dutch Minister of Justice</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Security and Justice (Netherlands)", "link": "https://wikipedia.org/wiki/Ministry_of_Security_and_Justice_(Netherlands)"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Turkish journalist and politician, Mayor of Ankara", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Melih_G%C3%B6k%C3%A7ek\" title=\"<PERSON><PERSON> Gökçek\"><PERSON><PERSON></a>, Turkish journalist and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Ankara\" title=\"List of mayors of Ankara\">Mayor of Ankara</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Melih_G%C3%B6k%C3%A7ek\" title=\"<PERSON><PERSON> Gökçek\"><PERSON><PERSON></a>, Turkish journalist and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Ankara\" title=\"List of mayors of Ankara\">Mayor of Ankara</a>", "links": [{"title": "Melih Gökçek", "link": "https://wikipedia.org/wiki/Melih_G%C3%B6k%C3%A7ek"}, {"title": "List of mayors of Ankara", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Ankara"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian sprinter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (d. 2017)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Petty\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor and director", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American politician (d. 2024)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American keyboard player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Greenwood"}]}, {"year": "1951", "text": "<PERSON>, English lawyer and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "1951", "text": "<PERSON>, Australian-American evangelist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American evangelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American evangelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Swedish businessman and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish businessman and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Italian footballer and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actress and director", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English photographer and art director", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and art director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and art director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Ecuadorian politician and economist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian politician and economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian politician and economist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American baseball player and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American businessman and philanthropist, co-founded the Upper Deck Company (d. 2013)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded the <a href=\"https://wikipedia.org/wiki/Upper_Deck_Company\" title=\"Upper Deck Company\">Upper Deck Company</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded the <a href=\"https://wikipedia.org/wiki/Upper_Deck_Company\" title=\"Upper Deck Company\">Upper Deck Company</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Upper Deck Company", "link": "https://wikipedia.org/wiki/Upper_Deck_Company"}]}, {"year": "1953", "text": "<PERSON>, American actor (d. 2016)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American composer and conductor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American composer and conductor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English author and academic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American boxer (d. 2016)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English guitarist", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_(guitarist)"}]}, {"year": "1957", "text": "<PERSON>-<PERSON>, Baroness <PERSON>-<PERSON> of Yarnbury, English politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_Baroness_<PERSON><PERSON>-<PERSON>_of_Yarnbury\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of Yarnbury\"><PERSON>, Baroness <PERSON>-<PERSON> of Yarnbury</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_Baroness_<PERSON><PERSON>-<PERSON>_of_Yarnbury\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of Yarnbury\"><PERSON>, Baroness <PERSON> of Yarnbury</a>, English politician", "links": [{"title": "<PERSON>-<PERSON>, <PERSON>-<PERSON> of Yarnbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_Baroness_<PERSON><PERSON>-<PERSON>_of_Yarnbury"}]}, {"year": "1957", "text": "<PERSON>, English cricketer and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American academic and politician, 25th United States Secretary of Labor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 25th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 25th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Labor", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Labor"}]}, {"year": "1958", "text": "<PERSON>, American director and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American author and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>lew<PERSON>\"><PERSON></a>, American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>lew<PERSON>\"><PERSON></a>, American author and academic", "links": [{"title": "<PERSON>lew<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lew<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American wrestler (d. 2022)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Scott Hall\"><PERSON></a>, American wrestler (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scott_<PERSON>\" title=\"Scott Hall\"><PERSON></a>, American wrestler (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Scott_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter and bass player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1958", "text": "<PERSON>, American football player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, American-Danish actor and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Vig<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Danish actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Danish actor and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vig<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian comedian, actor, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)\" title=\"<PERSON> (Australian actor)\"><PERSON></a>, Australian comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)\" title=\"<PERSON> (Australian actor)\"><PERSON></a>, Australian comedian, actor, and screenwriter", "links": [{"title": "<PERSON> (Australian actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)"}]}, {"year": "1960", "text": "<PERSON>, Russian chess player and trainer (d. 2004)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and trainer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and trainer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Norwegian drummer and composer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>du<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian drummer and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>du<PERSON>_<PERSON>\" title=\"<PERSON>du<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian drummer and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Audu<PERSON>_<PERSON>ive"}]}, {"year": "1961", "text": "<PERSON>, English author and playwright", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Welsh footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian director, producer, and harmonica player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stroud\"><PERSON></a>, Canadian director, producer, and harmonica player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stroud\"><PERSON></a>, Canadian director, producer, and harmonica player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Les_Stroud"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American director and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Hong Kong-Taiwanese singer-songwriter and actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-Taiwanese singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-Taiwanese singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian engineer, astronaut, and 29th Governor General of Canada", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer, astronaut, and 29th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer, astronaut, and 29th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Greek footballer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Dutch footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American politician and lawyer, 49th Vice President of the United States", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician and lawyer, 49th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician and lawyer, 49th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Japanese actress and singer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_musician)\" title=\"<PERSON> (Scottish musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_musician)\" title=\"<PERSON> (Scottish musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON> (Scottish musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_musician)"}]}, {"year": "1965", "text": "<PERSON>, American businessman", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Russian ice hockey player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, South African cricketer and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American computer scientist and engineer, founded Slackware", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer, founded <a href=\"https://wikipedia.org/wiki/Slackware\" title=\"Slackware\">Slackware</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer, founded <a href=\"https://wikipedia.org/wiki/Slackware\" title=\"Slackware\">Slackware</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>kerding"}, {"title": "Slackware", "link": "https://wikipedia.org/wiki/Slackware"}]}, {"year": "1967", "text": "<PERSON>, English actress and singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian rugby league player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English actress, director, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian rugby league player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Puerto Rican baseball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_(baseball)"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Greek high jumper", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek high jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English-Chinese businessman (d. 2011)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Chinese businessman (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Chinese businessman (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Estonian poet and illustrator", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>ap<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ap<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet and illustrator", "links": [{"title": "Aapo <PERSON>", "link": "https://wikipedia.org/wiki/Aapo_Ilves"}]}, {"year": "1970", "text": "<PERSON>, American blogger and author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blogger and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blogger and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>-<PERSON>, American basketball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American rapper, producer, and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Snoop_Dogg\" title=\"Snoop Dogg\"><PERSON>no<PERSON> <PERSON><PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Snoop_Dogg\" title=\"Snoop Dogg\"><PERSON>no<PERSON> <PERSON><PERSON></a>, American rapper, producer, and actor", "links": [{"title": "Snoop Dogg", "link": "https://wikipedia.org/wiki/Snoop_Dogg"}]}, {"year": "1971", "text": "<PERSON>, American basketball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Dutch runner", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Australian singer-songwriter and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ogue\" title=\"<PERSON><PERSON><PERSON> Minogue\"><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Minogue\"><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Dutch swimmer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English rugby player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American academic and politician, 11th Lieutenant Governor of Hawaii", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 11th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Hawaii\" title=\"Lieutenant Governor of Hawaii\">Lieutenant Governor of Hawaii</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 11th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Hawaii\" title=\"Lieutenant Governor of Hawaii\">Lieutenant Governor of Hawaii</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Hawaii", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Hawaii"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter, writer and socio-political activist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, writer and socio-political activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, writer and socio-political activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Scottish comedian and writer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish comedian and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish comedian and writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>my"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Emirati-American actor and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Emirati-American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Emirati-American actor and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Greek footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Italian footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Canadian-American violinist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American violinist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Erk<PERSON>_<PERSON>\" title=\"<PERSON>rk<PERSON> Sa<PERSON>uk\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rk<PERSON>_<PERSON>uk\" title=\"Erk<PERSON> Saviauk\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erko_<PERSON>uk"}]}, {"year": "1977", "text": "<PERSON>, American actor and musician", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Witwer"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vire<PERSON>_<PERSON>\" title=\"<PERSON>ire<PERSON>\">V<PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vire<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Scottish bass player and songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish bass player and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor, director, and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Irish rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_O%27Connell"}]}, {"year": "1979", "text": "<PERSON>, American decathlete", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player (d. 2016)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Dominican baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Veras"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Greek footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1981", "text": "<PERSON>, Mexican footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%ADguez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADguez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADguez"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Danish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Welsh golfer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Italian tennis player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Flavio_Cipolla\" title=\"<PERSON><PERSON><PERSON> Cipoll<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flavio_Cipolla\" title=\"<PERSON>lavio Cipoll<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Flavio_Cipolla"}]}, {"year": "1983", "text": "<PERSON>, Ecuadorian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Israeli actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Alona_Tal\" title=\"Alona Tal\"><PERSON><PERSON></a>, Israeli actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al<PERSON>_Tal\" title=\"Alona Tal\"><PERSON><PERSON></a>, Israeli actress", "links": [{"title": "Alona Tal", "link": "https://wikipedia.org/wiki/Alona_Tal"}]}, {"year": "1983", "text": "<PERSON>, Dutch footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter (d. 2012)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Florent_Sinama_Pongolle\" title=\"Florent Sinama Pongolle\">Florent <PERSON> Pongolle</a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florent_Sinama_Pongolle\" title=\"Florent Sinama Pongolle\">Florent <PERSON> Pongolle</a>, French footballer", "links": [{"title": "Florent <PERSON>", "link": "https://wikipedia.org/wiki/Florent_Sinama_Pongolle"}]}, {"year": "1984", "text": "<PERSON>, Irish rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English race car driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Thai footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Saechio\"><PERSON><PERSON></a>, Thai footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Saechio\"><PERSON><PERSON></a>, Thai footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wanlop_Saechio"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Australian model", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German rugby player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1987", "text": "<PERSON>, German historian and blogger who falsely claimed to be descended from Holocaust survivors", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and blogger who falsely claimed to be descended from <a href=\"https://wikipedia.org/wiki/Holocaust_survivor\" class=\"mw-redirect\" title=\"Holocaust survivor\">Holocaust survivors</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and blogger who falsely claimed to be descended from <a href=\"https://wikipedia.org/wiki/Holocaust_survivor\" class=\"mw-redirect\" title=\"Holocaust survivor\">Holocaust survivors</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Holocaust survivor", "link": "https://wikipedia.org/wiki/Holocaust_survivor"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American rapper", "html": "1988 - <a href=\"https://wikipedia.org/wiki/ASAP_Ferg\" title=\"ASAP Ferg\">AS<PERSON> <PERSON>rg</a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ASAP_Ferg\" title=\"ASAP Ferg\">ASAP Ferg</a>, American rapper", "links": [{"title": "ASAP Ferg", "link": "https://wikipedia.org/wiki/ASAP_Ferg"}]}, {"year": "1988", "text": "<PERSON><PERSON>, South African supermodel and philanthropist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African supermodel and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African supermodel and philanthropist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Can<PERSON>_<PERSON>oel"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1989", "text": "<PERSON>, English singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Cook Islands rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Sam_Mataora\" title=\"Sam Mataora\"><PERSON></a>, Cook Islands rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sam_Mataora\" title=\"Sam Mataora\"><PERSON></a>, Cook Islands rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Russian gymnast", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian-American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferhat_<PERSON>gan"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Mexican wrestler", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Indonesian badminton player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>int<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>int<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian badminton player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ginting"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Russian tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Australian rules footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ridley"}]}, {"year": "1999", "text": "<PERSON><PERSON>, South Korean singer and television personality", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>u\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>u\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chuu"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON> Broke Again, American rapper", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Never_Broke_Again\" title=\"YoungBoy Never Broke Again\"><PERSON><PERSON><PERSON> Never Broke Again</a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Never_Broke_Again\" title=\"YoungBoy Never Broke Again\"><PERSON><PERSON><PERSON> Never Broke Again</a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON> Never Broke Again", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Never_Broke_Again"}]}, {"year": "2000", "text": "<PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (running back)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(running_back)"}]}, {"year": "2001", "text": "<PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "460", "text": "<PERSON><PERSON><PERSON>, Byzantine wife of <PERSON><PERSON><PERSON> (b. 401)", "html": "460 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine wife of <a href=\"https://wikipedia.org/wiki/Theo<PERSON><PERSON>_II\" title=\"Theodosius II\"><PERSON><PERSON><PERSON> II</a> (b. 401)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine wife of <a href=\"https://wikipedia.org/wiki/Theodosius_II\" title=\"Theodosius II\"><PERSON><PERSON><PERSON> II</a> (b. 401)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theodosius_II"}]}, {"year": "967", "text": "<PERSON>, Chinese governor", "html": "967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Yixing\"><PERSON></a>, Chinese governor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Yixing\"><PERSON></a>, Chinese governor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1122", "text": "<PERSON>, archbishop of Canterbury", "html": "1122 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Escures\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">archbishop of Canterbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Escures\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">archbishop of Canterbury</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Escures"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}]}, {"year": "1139", "text": "<PERSON>, Duke of Bavaria (b. 1108)", "html": "1139 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1108)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1108)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1187", "text": "<PERSON>", "html": "1187 - <a href=\"https://wikipedia.org/wiki/Pope_Urban_III\" title=\"Pope Urban III\"><PERSON> Urban III</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Urban_III\" title=\"Pope Urban III\"><PERSON> III</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Urban_III"}]}, {"year": "1327", "text": "<PERSON>, Countess of Urgell (b. 1300)", "html": "1327 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Enten%C3%A7a\" title=\"<PERSON>\"><PERSON></a>, Countess of Urgell (b. 1300)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Enten%C3%A7a\" title=\"<PERSON>\"><PERSON></a>, Countess <PERSON> Urgell (b. 1300)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Enten%C3%A7a"}]}, {"year": "1401", "text": "<PERSON>, German pirate", "html": "1401 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pirate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pirate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Klaus_St%C3%B6<PERSON><PERSON><PERSON>"}]}, {"year": "1423", "text": "<PERSON>, Archbishop of York", "html": "1423 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Archbishop of York", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Archbishop of York", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1438", "text": "<PERSON><PERSON><PERSON>, Sienese sculptor (b. c. 1374)", "html": "1438 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Quercia\" title=\"<PERSON><PERSON><PERSON> Quercia\"><PERSON><PERSON><PERSON> Quercia</a>, Sienese sculptor (b. c. 1374)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Quercia\" title=\"<PERSON><PERSON><PERSON> Quercia\"><PERSON><PERSON><PERSON> Quercia</a>, Sienese sculptor (b. c. 1374)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>rcia"}]}, {"year": "1439", "text": "<PERSON> the Camaldulian, Italian theologian", "html": "1439 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Camaldulian\" class=\"mw-redirect\" title=\"<PERSON> the Camaldulian\"><PERSON> the Cam<PERSON>ulian</a>, Italian theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Camaldulian\" class=\"mw-redirect\" title=\"<PERSON> the Camaldulian\"><PERSON> the Camaldulian</a>, Italian theologian", "links": [{"title": "<PERSON> the Camaldulian", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1524", "text": "<PERSON>, English physician and scholar (b. 1460)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and scholar (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and scholar (b. 1460)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1538", "text": "<PERSON>, Duke of Urbino, condottiero (b. 1490)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Duke_of_Urbino\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Urbino\"><PERSON>, Duke of Urbino</a>, condo<PERSON><PERSON> (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Duke_of_Urbino\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Urbino\"><PERSON>, Duke of Urbino</a>, condo<PERSON><PERSON> (b. 1490)", "links": [{"title": "<PERSON>, Duke of Urbino", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Duke_of_Urbino"}]}, {"year": "1570", "text": "<PERSON>, Portuguese historian and author (b. 1496)", "html": "1570 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese historian and author (b. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese historian and author (b. 1496)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1602", "text": "<PERSON>, Elizabethan member of parliament, Shropshire landowner (b. 1550)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Elizabethan member of parliament, Shropshire landowner (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Elizabethan member of parliament, Shropshire landowner (b. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1640", "text": "<PERSON>, English clergyman and theologian (b. 1585)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Puritan)\" title=\"<PERSON> (Puritan)\"><PERSON></a>, English clergyman and theologian (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Puritan)\" title=\"<PERSON> (Puritan)\"><PERSON></a>, English clergyman and theologian (b. 1585)", "links": [{"title": "<PERSON> (Puritan)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Puritan)"}]}, {"year": "1652", "text": "<PERSON>, Spanish poet and playwright (b. 1611)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright (b. 1611)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, Scottish physician and academic (b. 1652)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician and academic (b. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician and academic (b. 1652)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Archibald_<PERSON>cairne"}]}, {"year": "1740", "text": "<PERSON>, Holy Roman Emperor (b. 1685)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VI, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VI, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1685)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1865", "text": "<PERSON><PERSON>, American guerrilla leader (b. 1821)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Guerrilla_warfare_in_the_American_Civil_War\" title=\"Guerrilla warfare in the American Civil War\">guerrilla</a> leader (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Guerrilla_warfare_in_the_American_Civil_War\" title=\"Guerrilla warfare in the American Civil War\">guerrilla</a> leader (b. 1821)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Guerrilla warfare in the American Civil War", "link": "https://wikipedia.org/wiki/Guerrilla_warfare_in_the_American_Civil_War"}]}, {"year": "1870", "text": "<PERSON>, Irish violinist and composer (b. 1808)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish violinist and composer (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish violinist and composer (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Latvian-German theologian and academic (b. 1793)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-German theologian and academic (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-German theologian and academic (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American journalist, author, and activist (b. 1802)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and activist (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and activist (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, 3rd Marquess of Donegall (b. 1797)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Marquess_of_Donegall\" title=\"<PERSON>, 3rd Marquess of Donegall\"><PERSON>, 3rd Marquess of Donegall</a> (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Marquess_of_Donegall\" title=\"<PERSON>, 3rd Marquess of Donegall\"><PERSON>, 3rd Marquess of Donegall</a> (b. 1797)", "links": [{"title": "<PERSON>, 3rd Marquess of Donegall", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Marquess_of_Donegall"}]}, {"year": "1890", "text": "<PERSON>, English-Italian geographer and explorer (b. 1821)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian geographer and explorer (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian geographer and explorer (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English historian, novelist, biographer and editor (b. 1818)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, novelist, biographer and editor (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, novelist, biographer and editor (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Albanian poet and translator (b. 1846)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>%C3%ABri\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian poet and translator (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>%C3%ABri\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian poet and translator (b. 1846)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>_<PERSON>%C3%ABri"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Australian politician, 21st Premier of South Australia (b. 1853)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1853)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1910", "text": "<PERSON>, American lawyer and politician, 29th Governor of New York (b. 1843)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1926", "text": "<PERSON>, American union leader and politician (b. 1855)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American union leader and politician (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American union leader and politician (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_V._Debs"}]}, {"year": "1928", "text": "<PERSON>, Scottish footballer (b. 1876)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Scottish-English politician, Secretary of State for Foreign Affairs, Nobel Prize laureate (b. 1863)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign Affairs</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign Affairs</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Foreign and Commonwealth Affairs", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1936", "text": "<PERSON>, American educator (b. 1866)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Swedish architect and academic, co-designed Skogskyrkogården (b. 1885)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish architect and academic, co-designed <a href=\"https://wikipedia.org/wiki/Skogskyrkog%C3%A5rden\" title=\"Skogskyrkogården\">Skogskyrkogården</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish architect and academic, co-designed <a href=\"https://wikipedia.org/wiki/Skogskyrkog%C3%A5rden\" title=\"Skogskyrkogården\">Skogskyrkogården</a> (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Skogskyrkogården", "link": "https://wikipedia.org/wiki/Skogskyrkog%C3%A5rden"}]}, {"year": "1941", "text": "<PERSON>, English cricketer and soldier (b. 1911)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American colonel, lawyer, and politician, 46th United States Secretary of State (b. 1867)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, lawyer, and politician, 46th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, lawyer, and politician, 46th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1953", "text": "<PERSON>, German colonel and pilot (b. 1916)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American industrialist and founder of Bell Aircraft Corporation (b. 1894)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American industrialist and founder of Bell Aircraft Corporation (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American industrialist and founder of Bell Aircraft Corporation (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Greek-American javelin thrower and football player (b. 1890)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-American javelin thrower and football player (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-American javelin thrower and football player (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American engineer and politician, 31st President of the United States (b. 1874)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician, 31st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician, 31st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Japanese politician and diplomat, 32nd Prime Minister of Japan (b. 1878)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician and diplomat, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician and diplomat, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1968", "text": "<PERSON>, English actor and screenwriter (b. 1896)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bud_<PERSON>gan"}]}, {"year": "1972", "text": "<PERSON>, American astronomer and academic (b. 1885)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American guitarist (b. 1949)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter (b. 1948)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Swedish race car driver (b. 1948)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish race car driver (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish race car driver (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian author (b. 1915)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Yves_Th%C3%A<PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1917)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Czech-American biochemist and pharmacologist, Nobel Prize laureate (b. 1896)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American biochemist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American biochemist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1984", "text": "<PERSON>, English-American physicist and mathematician, Nobel Prize laureate (b. 1902)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Russian mathematician and academic (b. 1903)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and academic (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and academic (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English pilot and author (b. 1922)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot and author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English actor and director (b. 1913)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor (b. 1905)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, German-American conductor (b. 1926)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American conductor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American conductor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Japanese painter (b. 1909)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor (b. 1913)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actor, director, and screenwriter (b. 1942)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and screenwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and screenwriter (b. 1942)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1995", "text": "<PERSON>, Australian politician, 20th Premier of Western Australia (b. 1902)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1999", "text": "<PERSON>, Canadian-American businessman (b. 1911)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Irish footballer, lawyer, and politician, 5th Taoiseach of Ireland (b. 1917)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer, lawyer, and politician, 5th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer, lawyer, and politician, 5th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoise<PERSON>\">Taoiseach of Ireland</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "2001", "text": "<PERSON>, American financier and banker (b. 1949)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and banker (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and banker (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American poet and educator (b. 1923)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American baseball player, coach, and manager (b. 1934)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American singer and pianist (b. 1934)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Horn\"><PERSON></a>, American singer and pianist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Horn\"><PERSON></a>, American singer and pianist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Czech painter and poet (b. 1940)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Eva_%C5%A0vankmajerov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech painter and poet (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eva_%C5%A0vankmajerov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech painter and poet (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_%C5%A0vankmajerov%C3%A1"}]}, {"year": "2005", "text": "<PERSON>, Dutch lawyer and politician, 16th Mayor of Rotterdam (b. 1933)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Mayor_of_Rotterdam\" class=\"mw-redirect\" title=\"Mayor of Rotterdam\">Mayor of Rotterdam</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Mayor_of_Rotterdam\" class=\"mw-redirect\" title=\"Mayor of Rotterdam\">Mayor of Rotterdam</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Rotterdam", "link": "https://wikipedia.org/wiki/Mayor_of_Rotterdam"}]}, {"year": "2006", "text": "<PERSON>, Estonian shot putter and discus thrower (b. 1911)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian shot putter and discus thrower (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian shot putter and discus thrower (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ii<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actress (b. 1910)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American football player and sportscaster (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American football player (b. 1935)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American politician (b. 1944)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American politician (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American publisher, founded Penthouse magazine (b. 1930)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <i><a href=\"https://wikipedia.org/wiki/Penthouse_(magazine)\" title=\"Penthouse (magazine)\">Penthouse magazine</a></i> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <i><a href=\"https://wikipedia.org/wiki/Penthouse_(magazine)\" title=\"Penthouse (magazine)\">Penthouse magazine</a></i> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Penthouse (magazine)", "link": "https://wikipedia.org/wiki/Penthouse_(magazine)"}]}, {"year": "2010", "text": "<PERSON>, Austrian-English author (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English author (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>on"}]}, {"year": "2010", "text": "<PERSON>, Dutch historian and diplomat (b. 1914)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian and diplomat (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian and diplomat (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Pakistani politician, 8th President of Pakistan (b. 1940)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Libyan colonel and politician, Prime Minister of Libya (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Libyan colonel and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Libya\" class=\"mw-redirect\" title=\"Prime Minister of Libya\">Prime Minister of Libya</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Libyan colonel and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Libya\" class=\"mw-redirect\" title=\"Prime Minister of Libya\">Prime Minister of Libya</a> (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Libya", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Libya"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON><PERSON>, Libyan colonel (b. 1974)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Mu<PERSON>sim_Gaddafi\" title=\"Mutassim Gaddafi\"><PERSON><PERSON><PERSON><PERSON></a>, Libyan colonel (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>sim_Gadda<PERSON>\" title=\"Mutassim Gaddafi\"><PERSON><PERSON><PERSON><PERSON></a>, Libyan colonel (b. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mutassim_Gaddafi"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Libyan politician (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>-Bak<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Libyan politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abu-Bak<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>k<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Libyan politician (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Croatian-Slovenian handball player (b. 1966)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Iztok_Puc\" title=\"Iztok Puc\"><PERSON><PERSON><PERSON></a>, Croatian-Slovenian handball player (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iztok_Puc\" title=\"Iztok Puc\"><PERSON><PERSON><PERSON></a>, Croatian-Slovenian handball player (b. 1966)", "links": [{"title": "Iztok Puc", "link": "https://wikipedia.org/wiki/Iztok_Puc"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish poet and composer (b. 1951)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Przemys%C5%82aw_Gint<PERSON>ki\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and composer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Przemys%C5%82aw_Gint<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and composer (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Przemys%C5%82aw_<PERSON><PERSON><PERSON>ki"}]}, {"year": "2012", "text": "<PERSON>, American philosopher and academic (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "2012", "text": "<PERSON>, American activist, created <PERSON> Day (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(peace_activist)\" title=\"<PERSON> (peace activist)\"><PERSON></a>, American activist, created <a href=\"https://wikipedia.org/wiki/Earth_Day\" title=\"Earth Day\">Earth Day</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(peace_activist)\" title=\"<PERSON> (peace activist)\"><PERSON></a>, American activist, created <a href=\"https://wikipedia.org/wiki/Earth_Day\" title=\"Earth Day\">Earth Day</a> (b. 1915)", "links": [{"title": "<PERSON> (peace activist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(peace_activist)"}, {"title": "Earth Day", "link": "https://wikipedia.org/wiki/Earth_Day"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American physician and academic, Nobel Prize laureate (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2012", "text": "<PERSON>, American businessman (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Croatian-Serbian colonel (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>van<PERSON>_B<PERSON>z\" title=\"<PERSON><PERSON><PERSON> Broz\"><PERSON><PERSON><PERSON></a>, Croatian-Serbian colonel (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Broz\"><PERSON><PERSON><PERSON></a>, Croatian-Serbian colonel (b. 1924)", "links": [{"title": "Jovanka Broz", "link": "https://wikipedia.org/wiki/Jovan<PERSON>_Broz"}]}, {"year": "2013", "text": "<PERSON>, American football player and coach (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1932)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2013", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Kenyan race car driver (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(rally_driver)\" title=\"<PERSON><PERSON><PERSON> (rally driver)\"><PERSON><PERSON><PERSON></a>, Kenyan race car driver (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(rally_driver)\" title=\"<PERSON><PERSON><PERSON> (rally driver)\"><PERSON><PERSON><PERSON></a>, Kenyan race car driver (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON> (rally driver)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(rally_driver)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American actress and dancer (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American journalist, founded <PERSON> (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, founded <i><a href=\"https://wikipedia.org/wiki/Roll_Call\" title=\"Roll Call\">Roll Call</a></i> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, founded <i><a href=\"https://wikipedia.org/wiki/Roll_Call\" title=\"Roll Call\">Roll Call</a></i> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Roll Call", "link": "https://wikipedia.org/wiki/Roll_Call"}]}, {"year": "2014", "text": "<PERSON>, Swiss photographer and journalist (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss photographer and journalist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss photographer and journalist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>i"}]}, {"year": "2014", "text": "<PERSON>, Dominican-American fashion designer (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> de la Renta\"><PERSON></a>, Dominican-American fashion designer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Renta\"><PERSON></a>, Dominican-American fashion designer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, French businessman (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Greek basketball player and coach (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and coach (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and coach (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, German-Swiss psychologist and psychoanalyst (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Arno_Gruen\" title=\"<PERSON>rno Gruen\"><PERSON><PERSON></a>, German-Swiss psychologist and psychoanalyst (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>n\" title=\"<PERSON><PERSON> G<PERSON>\"><PERSON><PERSON></a>, German-Swiss psychologist and psychoanalyst (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>ruen"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish-Austrian economist and academic (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON>rz_%C5%81aski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-Austrian economist and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_%C5%81aski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-Austrian economist and academic (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON>rz_%C5%81aski"}]}, {"year": "2015", "text": "<PERSON>, English academic and politician, Secretary of State for the Environment, Transport and the Regions (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment,_Transport_and_the_Regions\" title=\"Secretary of State for the Environment, Transport and the Regions\">Secretary of State for the Environment, Transport and the Regions</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment,_Transport_and_the_Regions\" title=\"Secretary of State for the Environment, Transport and the Regions\">Secretary of State for the Environment, Transport and the Regions</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Secretary of State for the Environment, Transport and the Regions", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment,_Transport_and_the_Regions"}]}, {"year": "2015", "text": "<PERSON>, Scottish cyclist and manager (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish cyclist and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish cyclist and manager (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, former United States Coast Guard admiral (b. 1939)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former United States Coast Guard admiral (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former United States Coast Guard admiral (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1952)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Japanese mountaineer (b. 1939)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese mountaineer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese mountaineer (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Dutch prime minister (b. 1938)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Dutch prime minister (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON><PERSON>\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Dutch prime minister (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON>k"}]}, {"year": "2020", "text": "<PERSON>, Canadian-American stage magician and author (b. 1928)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American stage magician and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American stage magician and author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American composer and songwriter (b. 1940)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American folk, blues and jazz singer (b. 1927)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk, blues and jazz singer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk, blues and jazz singer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish preacher and theologian (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Fethullah_G%C3%BClen\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish preacher and theologian (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fethullah_G%C3%BClen\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish preacher and theologian (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fethullah_G%C3%BClen"}]}, {"year": "2024", "text": "<PERSON>, American Reform rabbi (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Reform rabbi (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Reform rabbi (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Polish classical pianist and actor (b. 1952)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish classical pianist and actor (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish classical pianist and actor (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Baron <PERSON>, British life peer (b. 1940)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, British life peer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, British life peer (b. 1940)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}]}}