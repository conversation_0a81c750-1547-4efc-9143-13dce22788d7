{"date": "December 31", "url": "https://wikipedia.org/wiki/December_31", "data": {"Events": [{"year": "406", "text": "Vandals, Alans and Suebians cross the Rhine, beginning an invasion of Gaul.", "html": "406 - <a href=\"https://wikipedia.org/wiki/Vandals\" title=\"Vandals\">Vandals</a>, <a href=\"https://wikipedia.org/wiki/Alans\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">Suebians</a> <a href=\"https://wikipedia.org/wiki/Crossing_of_the_Rhine\" title=\"Crossing of the Rhine\">cross the Rhine</a>, beginning an invasion of <a href=\"https://wikipedia.org/wiki/Gaul\" title=\"Gaul\">Gaul</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vandals\" title=\"Vandals\">Vandals</a>, <a href=\"https://wikipedia.org/wiki/Alans\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">Suebians</a> <a href=\"https://wikipedia.org/wiki/Crossing_of_the_Rhine\" title=\"Crossing of the Rhine\">cross the Rhine</a>, beginning an invasion of <a href=\"https://wikipedia.org/wiki/Gaul\" title=\"Gaul\">Gaul</a>.", "links": [{"title": "Vandals", "link": "https://wikipedia.org/wiki/Vandals"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>s"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Crossing of the Rhine", "link": "https://wikipedia.org/wiki/Crossing_of_the_Rhine"}, {"title": "Gaul", "link": "https://wikipedia.org/wiki/Gaul"}]}, {"year": "535", "text": "Byzantine general <PERSON><PERSON><PERSON> completes the conquest of Sicily, defeating the Gothic garrison of Palermo (Panormos), and ending his consulship for the year.", "html": "535 - <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> general <a href=\"https://wikipedia.org/wiki/Belisarius\" title=\"Belisarius\"><PERSON><PERSON><PERSON></a> completes the conquest of <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a>, defeating the <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Gothic</a> garrison of <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a> (Panormos), and ending his <a href=\"https://wikipedia.org/wiki/Consul\" title=\"Consul\">consulship</a> for the year.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> general <a href=\"https://wikipedia.org/wiki/Belisarius\" title=\"Belisarius\"><PERSON><PERSON><PERSON></a> completes the conquest of <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a>, defeating the <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Gothic</a> garrison of <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a> (Panormos), and ending his <a href=\"https://wikipedia.org/wiki/Consul\" title=\"Consul\">consulship</a> for the year.", "links": [{"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Belisarius"}, {"title": "Sicily", "link": "https://wikipedia.org/wiki/Sicily"}, {"title": "Ostrogoths", "link": "https://wikipedia.org/wiki/Ostrogoths"}, {"title": "Palermo", "link": "https://wikipedia.org/wiki/Palermo"}, {"title": "Consul", "link": "https://wikipedia.org/wiki/Consul"}]}, {"year": "870", "text": "Battle of Englefield: The Vikings clash with ealdo<PERSON> of Berkshire. The invaders are driven back to Reading (East Anglia); many Danes are killed.", "html": "870 - <a href=\"https://wikipedia.org/wiki/Battle_of_Englefield\" title=\"Battle of Englefield\">Battle of Englefield</a>: The <a href=\"https://wikipedia.org/wiki/Vikings\" title=\"Vikings\">Vikings</a> clash with <a href=\"https://wikipedia.org/wiki/Ealdorman\" title=\"Ealdorman\">ealdorman</a> <a href=\"https://wikipedia.org/wiki/%C3%86thelwulf_of_Berkshire\" title=\"Æthelwulf of Berkshire\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Berkshire</a>. The invaders are driven back to <a href=\"https://wikipedia.org/wiki/Reading,_Berkshire\" title=\"Reading, Berkshire\">Reading</a> (<a href=\"https://wikipedia.org/wiki/Kingdom_of_East_Anglia\" title=\"Kingdom of East Anglia\">East Anglia</a>); many <a href=\"https://wikipedia.org/wiki/Danes\" title=\"Dane<PERSON>\">Danes</a> are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Englefield\" title=\"Battle of Englefield\">Battle of Englefield</a>: The <a href=\"https://wikipedia.org/wiki/Vikings\" title=\"Vikings\">Vikings</a> clash with <a href=\"https://wikipedia.org/wiki/Ealdorman\" title=\"Ealdorman\">ealdorman</a> <a href=\"https://wikipedia.org/wiki/%C3%86thelwulf_of_Berkshire\" title=\"Æthelwulf of Berkshire\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Berkshire</a>. The invaders are driven back to <a href=\"https://wikipedia.org/wiki/Reading,_Berkshire\" title=\"Reading, Berkshire\">Reading</a> (<a href=\"https://wikipedia.org/wiki/Kingdom_of_East_Anglia\" title=\"Kingdom of East Anglia\">East Anglia</a>); many <a href=\"https://wikipedia.org/wiki/Danes\" title=\"Dane<PERSON>\">Danes</a> are killed.", "links": [{"title": "Battle of Englefield", "link": "https://wikipedia.org/wiki/Battle_of_Englefield"}, {"title": "Vikings", "link": "https://wikipedia.org/wiki/Vikings"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Berkshire", "link": "https://wikipedia.org/wiki/%C3%86thelwulf_of_Berkshire"}, {"title": "Reading, Berkshire", "link": "https://wikipedia.org/wiki/Reading,_Berkshire"}, {"title": "Kingdom of East Anglia", "link": "https://wikipedia.org/wiki/Kingdom_of_East_Anglia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Danes"}]}, {"year": "1105", "text": "Holy Roman Emperor <PERSON> is forced to abdicate in favor of his son, <PERSON>, in Ingelheim.", "html": "1105 - <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV</a> is forced to abdicate in favor of his son, <a href=\"https://wikipedia.org/wiki/<PERSON>_V,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON></a>, in <a href=\"https://wikipedia.org/wiki/Ingelheim\" class=\"mw-redirect\" title=\"Ingelheim\">Ingelheim</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV</a> is forced to abdicate in favor of his son, <a href=\"https://wikipedia.org/wiki/<PERSON>_V,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON></a>, in <a href=\"https://wikipedia.org/wiki/Ingelheim\" class=\"mw-redirect\" title=\"Ingelheim\">Ingelheim</a>.", "links": [{"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Ingelheim", "link": "https://wikipedia.org/wiki/Ingelheim"}]}, {"year": "1225", "text": "The Lý dynasty of Vietnam ends after 216 years by the enthronement of the boy emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, husband of the last Lý monarch, <PERSON><PERSON>, starting the Trần dynasty.[citation needed]", "html": "1225 - The <a href=\"https://wikipedia.org/wiki/L%C3%BD_dynasty\" title=\"Lý dynasty\">Lý dynasty</a> of Vietnam ends after 216 years by the enthronement of the boy emperor <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Th%C3%A1i_T%C3%B4ng\" title=\"Trần Thái Tông\">Trần <PERSON>h<PERSON><PERSON></a>, husband of the last Lý monarch, <a href=\"https://wikipedia.org/wiki/L%C3%BD_Chi%C3%AAu_Ho%C3%A0ng\" title=\"L<PERSON> Chiêu Ho<PERSON>\">L<PERSON> Chiê<PERSON></a>, starting the <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty\" title=\"Trần dynasty\">Trần dynasty</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/L%C3%BD_dynasty\" title=\"Lý dynasty\">Lý dynasty</a> of Vietnam ends after 216 years by the enthronement of the boy emperor <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Th%C3%A1i_T%C3%B4ng\" title=\"Trần Thái Tông\">Trần <PERSON><PERSON><PERSON></a>, husband of the last Lý monarch, <a href=\"https://wikipedia.org/wiki/L%C3%BD_Chi%C3%AAu_Ho%C3%A0ng\" title=\"<PERSON><PERSON> Chi<PERSON>\"><PERSON><PERSON></a>, starting the <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty\" title=\"Trần dynasty\">Trần dynasty</a>.", "links": [{"title": "Lý dynasty", "link": "https://wikipedia.org/wiki/L%C3%BD_dynasty"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_Th%C3%A1i_T%C3%B4ng"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%BD_Chi%C3%AAu_Ho%C3%A0ng"}, {"title": "Trần dynasty", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty"}]}, {"year": "1229", "text": "<PERSON> the Conqueror, King of Aragon, enters Medina Mayurqa (now known as Palma, Spain), thus consummating the Christian reconquest of the island of Majorca.", "html": "1229 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> the Conqueror</a>, <a href=\"https://wikipedia.org/wiki/King_of_Aragon\" class=\"mw-redirect\" title=\"King of Aragon\">King of Aragon</a>, enters Medina Mayurqa (now known as <a href=\"https://wikipedia.org/wiki/Palma,_Majorca\" class=\"mw-redirect\" title=\"Palma, Majorca\">Palma</a>, Spain), thus consummating the <a href=\"https://wikipedia.org/wiki/Conquest_of_Majorca\" title=\"Conquest of Majorca\">Christian reconquest</a> of the island of <a href=\"https://wikipedia.org/wiki/Majorca\" class=\"mw-redirect\" title=\"Majorca\">Majorca</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> the Conqueror</a>, <a href=\"https://wikipedia.org/wiki/King_of_Aragon\" class=\"mw-redirect\" title=\"King of Aragon\">King of Aragon</a>, enters Medina Mayurqa (now known as <a href=\"https://wikipedia.org/wiki/Palma,_Majorca\" class=\"mw-redirect\" title=\"Palma, Majorca\">Palma</a>, Spain), thus consummating the <a href=\"https://wikipedia.org/wiki/Conquest_of_Majorca\" title=\"Conquest of Majorca\">Christian reconquest</a> of the island of <a href=\"https://wikipedia.org/wiki/Majorca\" class=\"mw-redirect\" title=\"Majorca\">Majorca</a>.", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}, {"title": "King of Aragon", "link": "https://wikipedia.org/wiki/King_of_Aragon"}, {"title": "Palma, Majorca", "link": "https://wikipedia.org/wiki/Palma,_Majorca"}, {"title": "Conquest of Majorca", "link": "https://wikipedia.org/wiki/Conquest_of_Majorca"}, {"title": "Majorca", "link": "https://wikipedia.org/wiki/Majorca"}]}, {"year": "1501", "text": "The First Battle of Cannanore commences, seeing the first use of the naval line of battle.[citation needed]", "html": "1501 - The <a href=\"https://wikipedia.org/wiki/First_Battle_of_Cannanore\" title=\"First Battle of Cannanore\">First Battle of Cannanore</a> commences, seeing the first use of the naval <a href=\"https://wikipedia.org/wiki/Line_of_battle\" title=\"Line of battle\">line of battle</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Battle_of_Cannanore\" title=\"First Battle of Cannanore\">First Battle of Cannanore</a> commences, seeing the first use of the naval <a href=\"https://wikipedia.org/wiki/Line_of_battle\" title=\"Line of battle\">line of battle</a>.", "links": [{"title": "First Battle of Cannanore", "link": "https://wikipedia.org/wiki/First_Battle_of_Cannanore"}, {"title": "Line of battle", "link": "https://wikipedia.org/wiki/Line_of_battle"}]}, {"year": "1600", "text": "The British East India Company is chartered.", "html": "1600 - The British <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> is chartered.", "no_year_html": "The British <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> is chartered.", "links": [{"title": "East India Company", "link": "https://wikipedia.org/wiki/East_India_Company"}]}, {"year": "1660", "text": "<PERSON>, Duke of York is named Duke of Normandy by <PERSON> of France.", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON>, Duke of York</a> is named <a href=\"https://wikipedia.org/wiki/Duke_of_Normandy\" title=\"Duke of Normandy\">Duke of Normandy</a> by <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON>, Duke of York</a> is named <a href=\"https://wikipedia.org/wiki/Duke_of_Normandy\" title=\"Duke of Normandy\">Duke of Normandy</a> by <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"<PERSON> XIV of France\"><PERSON> of France</a>.", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Duke of Normandy", "link": "https://wikipedia.org/wiki/Duke_of_Normandy"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XIV_of_France"}]}, {"year": "1670", "text": "The expedition of <PERSON> leaves Corral Bay, having surveyed the coast and lost four hostages to the Spanish.", "html": "1670 - The <a href=\"https://wikipedia.org/wiki/English_expedition_to_Valdivia\" title=\"English expedition to Valdivia\">expedition of <PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Corral_Bay\" title=\"Corral Bay\">Corral Bay</a>, having surveyed the coast and lost four hostages to the Spanish.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/English_expedition_to_Valdivia\" title=\"English expedition to Valdivia\">expedition of <PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Corral_Bay\" title=\"Corral Bay\">Corral Bay</a>, having surveyed the coast and lost four hostages to the Spanish.", "links": [{"title": "English expedition to Valdivia", "link": "https://wikipedia.org/wiki/English_expedition_to_Valdivia"}, {"title": "Corral Bay", "link": "https://wikipedia.org/wiki/Corral_Bay"}]}, {"year": "1687", "text": "The first Huguenots set sail from France to the Cape of Good Hope.", "html": "1687 - The first <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a> set sail from <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> to the <a href=\"https://wikipedia.org/wiki/Cape_of_Good_Hope\" title=\"Cape of Good Hope\">Cape of Good Hope</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a> set sail from <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> to the <a href=\"https://wikipedia.org/wiki/Cape_of_Good_Hope\" title=\"Cape of Good Hope\">Cape of Good Hope</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>not"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "Cape of Good Hope", "link": "https://wikipedia.org/wiki/Cape_of_Good_Hope"}]}, {"year": "1757", "text": "Empress <PERSON> of Russia issues her ukase incorporating Königsberg into Russia.", "html": "1757 - Empress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> issues her <a href=\"https://wikipedia.org/wiki/Ukase\" title=\"Ukase\">ukase</a> incorporating <a href=\"https://wikipedia.org/wiki/Russian_Prussia\" title=\"Russian Prussia\">Königsberg into Russia</a>.", "no_year_html": "Empress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> I of Russia\"><PERSON> of Russia</a> issues her <a href=\"https://wikipedia.org/wiki/Ukase\" title=\"Ukase\">ukase</a> incorporating <a href=\"https://wikipedia.org/wiki/Russian_Prussia\" title=\"Russian Prussia\">Königsberg into Russia</a>.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}, {"title": "Ukas<PERSON>", "link": "https://wikipedia.org/wiki/Ukase"}, {"title": "Russian Prussia", "link": "https://wikipedia.org/wiki/Russian_Prussia"}]}, {"year": "1759", "text": "Arthur Guinness signs a 9,000-year lease at £45 per annum and starts brewing Guinness.", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur Guinness\"><PERSON></a> signs a 9,000-year lease at <a href=\"https://wikipedia.org/wiki/Pound_sterling\" title=\"Pound sterling\">£</a>45 per annum and starts brewing <a href=\"https://wikipedia.org/wiki/Guinness\" title=\"Guinness\">Guinness</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur Guinness\"><PERSON></a> signs a 9,000-year lease at <a href=\"https://wikipedia.org/wiki/Pound_sterling\" title=\"Pound sterling\">£</a>45 per annum and starts brewing <a href=\"https://wikipedia.org/wiki/Guinness\" title=\"Guinness\">Guinness</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pound sterling", "link": "https://wikipedia.org/wiki/<PERSON>_sterling"}, {"title": "Guinness", "link": "https://wikipedia.org/wiki/Guinness"}]}, {"year": "1775", "text": "American Revolutionary War: Battle of Quebec: British forces under General <PERSON> repulse an attack by Continental Army General <PERSON> in a snowstorm.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Quebec_(1775)\" title=\"Battle of Quebec (1775)\">Battle of Quebec</a>: British forces under General <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON></a> repulse an attack by <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in a snowstorm.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Quebec_(1775)\" title=\"Battle of Quebec (1775)\">Battle of Quebec</a>: British forces under General <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON></a> repulse an attack by <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in a snowstorm.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Quebec (1775)", "link": "https://wikipedia.org/wiki/Battle_of_Quebec_(1775)"}, {"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "Efimeris, the oldest Greek newspaper of which issues have survived till today, is published for the first time.", "html": "1790 - <i><a href=\"https://wikipedia.org/wiki/E<PERSON>meris\" title=\"Efimeris\">Efimeris</a></i>, the oldest Greek newspaper of which issues have survived till today, is published for the first time.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/E<PERSON>mer<PERSON>\" title=\"Efimeris\">Efimeris</a></i>, the oldest Greek newspaper of which issues have survived till today, is published for the first time.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Efimeris"}]}, {"year": "1796", "text": "The incorporation of Baltimore as a city.", "html": "1796 - The incorporation of <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a> as a city.", "no_year_html": "The incorporation of <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a> as a city.", "links": [{"title": "Baltimore", "link": "https://wikipedia.org/wiki/Baltimore"}]}, {"year": "1831", "text": "Gramercy Park is deeded to New York City.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/Gramercy_Park\" title=\"Gramercy Park\">Gramercy Park</a> is deeded to <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gramercy_Park\" title=\"Gramercy Park\">Gramercy Park</a> is deeded to <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "links": [{"title": "Gramercy Park", "link": "https://wikipedia.org/wiki/Gramercy_Park"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1844", "text": "The Philippines skipped this date in order to align the country with the rest of Asia, as the trading interest switched to China, Dutch East Indies and neighboring territories after Mexico gained independence from Spain on 27 September 1821. In the islands, Monday, 30 December 1844 was immediately followed by Wednesday, 1 January 1845.", "html": "1844 - The <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> skipped this date in order to align the country with the rest of Asia, as the trading interest switched to China, Dutch East Indies and neighboring territories after Mexico gained independence from Spain on 27 September 1821. In the islands, Monday, 30 December 1844 was immediately followed by Wednesday, 1 January 1845.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> skipped this date in order to align the country with the rest of Asia, as the trading interest switched to China, Dutch East Indies and neighboring territories after Mexico gained independence from Spain on 27 September 1821. In the islands, Monday, 30 December 1844 was immediately followed by Wednesday, 1 January 1845.", "links": [{"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1853", "text": "A dinner party is held inside a life-size model of an iguanodon created by <PERSON> and Sir <PERSON> in south London, England.", "html": "1853 - A dinner party is held inside a life-size model of an <a href=\"https://wikipedia.org/wiki/Iguanodon\" title=\"Iguanodon\">iguanodon</a> created by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/South_London\" title=\"South London\">south London</a>, England.", "no_year_html": "A dinner party is held inside a life-size model of an <a href=\"https://wikipedia.org/wiki/Iguanodon\" title=\"Iguanodon\">iguanodon</a> created by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/South_London\" title=\"South London\">south London</a>, England.", "links": [{"title": "Iguanodon", "link": "https://wikipedia.org/wiki/Iguanodon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hawkins"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "South London", "link": "https://wikipedia.org/wiki/South_London"}]}, {"year": "1857", "text": "Queen <PERSON> chooses Ottawa, then a small logging town, as the capital of the Province of Canada.", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> chooses <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a>, then a small <a href=\"https://wikipedia.org/wiki/Logging\" title=\"Logging\">logging</a> town, as the capital of the <a href=\"https://wikipedia.org/wiki/Province_of_Canada\" title=\"Province of Canada\">Province of Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen Victoria\">Queen <PERSON></a> chooses <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a>, then a small <a href=\"https://wikipedia.org/wiki/Logging\" title=\"Logging\">logging</a> town, as the capital of the <a href=\"https://wikipedia.org/wiki/Province_of_Canada\" title=\"Province of Canada\">Province of Canada</a>.", "links": [{"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}, {"title": "Ottawa", "link": "https://wikipedia.org/wiki/Ottawa"}, {"title": "Logging", "link": "https://wikipedia.org/wiki/Logging"}, {"title": "Province of Canada", "link": "https://wikipedia.org/wiki/Province_of_Canada"}]}, {"year": "1862", "text": "American Civil War: The three-day Battle of Stones River begins near Murfreesboro, Tennessee between the Confederate Army of Tennessee under General <PERSON><PERSON><PERSON> and the Union Army of the Cumberland under General <PERSON>.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The three-day <a href=\"https://wikipedia.org/wiki/Battle_of_Stones_River\" title=\"Battle of Stones River\">Battle of Stones River</a> begins near <a href=\"https://wikipedia.org/wiki/Murfreesboro,_Tennessee\" title=\"Murfreesboro, Tennessee\">Murfreesboro, Tennessee</a> between the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Army_of_Tennessee\" title=\"Army of Tennessee\">Army of Tennessee</a> under General <a href=\"https://wikipedia.org/wiki/Braxton_Bragg\" title=\"Braxton Bragg\">Braxton Bragg</a> and the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> <a href=\"https://wikipedia.org/wiki/Army_of_the_Cumberland\" title=\"Army of the Cumberland\">Army of the Cumberland</a> under General <a href=\"https://wikipedia.org/wiki/William_Rosecrans\" title=\"William Rosecrans\">William S. Rosecrans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The three-day <a href=\"https://wikipedia.org/wiki/Battle_of_Stones_River\" title=\"Battle of Stones River\">Battle of Stones River</a> begins near <a href=\"https://wikipedia.org/wiki/Murfreesboro,_Tennessee\" title=\"Murfreesboro, Tennessee\">Murfreesboro, Tennessee</a> between the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Army_of_Tennessee\" title=\"Army of Tennessee\">Army of Tennessee</a> under General <a href=\"https://wikipedia.org/wiki/Braxton_Bragg\" title=\"Braxton Bragg\">Braxton Bragg</a> and the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> <a href=\"https://wikipedia.org/wiki/Army_of_the_Cumberland\" title=\"Army of the Cumberland\">Army of the Cumberland</a> under General <a href=\"https://wikipedia.org/wiki/William_Rosecrans\" title=\"William <PERSON>crans\">William S. Rosecrans</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Stones River", "link": "https://wikipedia.org/wiki/Battle_of_Stones_River"}, {"title": "Murfreesboro, Tennessee", "link": "https://wikipedia.org/wiki/Murfreesboro,_Tennessee"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Army of Tennessee", "link": "https://wikipedia.org/wiki/Army_of_Tennessee"}, {"title": "Braxton Bragg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Army of the Cumberland", "link": "https://wikipedia.org/wiki/Army_of_the_Cumberland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "American Civil War: <PERSON> signs an enabling act that would admit West Virginia to the Union, thus dividing Virginia in two.", "html": "1862 - American Civil War: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs an enabling act that would admit <a href=\"https://wikipedia.org/wiki/West_Virginia\" title=\"West Virginia\">West Virginia</a> to the Union, thus dividing <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> in two.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs an enabling act that would admit <a href=\"https://wikipedia.org/wiki/West_Virginia\" title=\"West Virginia\">West Virginia</a> to the Union, thus dividing <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> in two.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "West Virginia", "link": "https://wikipedia.org/wiki/West_Virginia"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}]}, {"year": "1878", "text": "<PERSON>, working in Mannheim, Germany, files for a patent on his first reliable two-stroke gas engine. He was granted the patent in 1879.", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Karl Benz\"><PERSON></a>, working in <a href=\"https://wikipedia.org/wiki/Mannheim\" title=\"Mannheim\">Mannheim</a>, <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">Germany</a>, files for a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> on his first reliable <a href=\"https://wikipedia.org/wiki/Two-stroke_engine\" title=\"Two-stroke engine\">two-stroke gas engine</a>. He was granted the patent in 1879.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"Karl Benz\"><PERSON></a>, working in <a href=\"https://wikipedia.org/wiki/Mannheim\" title=\"Mannheim\">Mannheim</a>, <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">Germany</a>, files for a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> on his first reliable <a href=\"https://wikipedia.org/wiki/Two-stroke_engine\" title=\"Two-stroke engine\">two-stroke gas engine</a>. He was granted the patent in 1879.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mannheim", "link": "https://wikipedia.org/wiki/Mannheim"}, {"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Two-stroke engine", "link": "https://wikipedia.org/wiki/Two-stroke_engine"}]}, {"year": "1879", "text": "Thomas <PERSON> demonstrates incandescent lighting to the public for the first time, in Menlo Park, New Jersey.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> demonstrates <a href=\"https://wikipedia.org/wiki/Incandescent_light_bulb\" title=\"Incandescent light bulb\">incandescent lighting</a> to the public for the first time, in <a href=\"https://wikipedia.org/wiki/Menlo_Park,_New_Jersey\" title=\"Menlo Park, New Jersey\">Menlo Park, New Jersey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> demonstrates <a href=\"https://wikipedia.org/wiki/Incandescent_light_bulb\" title=\"Incandescent light bulb\">incandescent lighting</a> to the public for the first time, in <a href=\"https://wikipedia.org/wiki/Menlo_Park,_New_Jersey\" title=\"Menlo Park, New Jersey\">Menlo Park, New Jersey</a>.", "links": [{"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Incandescent light bulb", "link": "https://wikipedia.org/wiki/Incandescent_light_bulb"}, {"title": "Menlo Park, New Jersey", "link": "https://wikipedia.org/wiki/Menlo_Park,_New_Jersey"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> signs the Persian Constitution of 1906.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Persian_Constitution_of_1906\" title=\"Persian Constitution of 1906\">Persian Constitution of 1906</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Persian_Constitution_of_1906\" title=\"Persian Constitution of 1906\">Persian Constitution of 1906</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>_<PERSON>"}, {"title": "Persian Constitution of 1906", "link": "https://wikipedia.org/wiki/Persian_Constitution_of_1906"}]}, {"year": "1907", "text": "The first ever ball drop in Times Square.", "html": "1907 - The first ever <a href=\"https://wikipedia.org/wiki/Times_Square_Ball\" title=\"Times Square Ball\">ball drop</a> in <a href=\"https://wikipedia.org/wiki/Times_Square\" title=\"Times Square\">Times Square</a>.", "no_year_html": "The first ever <a href=\"https://wikipedia.org/wiki/Times_Square_Ball\" title=\"Times Square Ball\">ball drop</a> in <a href=\"https://wikipedia.org/wiki/Times_Square\" title=\"Times Square\">Times Square</a>.", "links": [{"title": "Times Square Ball", "link": "https://wikipedia.org/wiki/Times_Square_Ball"}, {"title": "Times Square", "link": "https://wikipedia.org/wiki/Times_Square"}]}, {"year": "1942", "text": "USS Essex, first aircraft carrier of a 24-ship class, is commissioned.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/USS_Essex_(CV-9)\" title=\"USS Essex (CV-9)\">USS <i>Essex</i></a>, first aircraft carrier of a 24-ship class, is <a href=\"https://wikipedia.org/wiki/Ship_commissioning\" title=\"Ship commissioning\">commissioned.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USS_Essex_(CV-9)\" title=\"USS Essex (CV-9)\">USS <i>Essex</i></a>, first aircraft carrier of a 24-ship class, is <a href=\"https://wikipedia.org/wiki/Ship_commissioning\" title=\"Ship commissioning\">commissioned.</a>", "links": [{"title": "USS Essex (CV-9)", "link": "https://wikipedia.org/wiki/USS_Essex_(CV-9)"}, {"title": "Ship commissioning", "link": "https://wikipedia.org/wiki/Ship_commissioning"}]}, {"year": "1942", "text": "World War II: The Royal Navy defeats the Kriegsmarine at the Battle of the Barents Sea. This leads to the resignation of Grand Admiral <PERSON> a month later.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> defeats the <a href=\"https://wikipedia.org/wiki/Kriegsmarine\" title=\"Kriegsmarine\">Kriegsmarine</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Barents_Sea\" title=\"Battle of the Barents Sea\">Battle of the Barents Sea</a>. This leads to the resignation of <a href=\"https://wikipedia.org/wiki/Grand_Admiral\" class=\"mw-redirect\" title=\"Grand Admiral\">Grand Admiral</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> a month later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> defeats the <a href=\"https://wikipedia.org/wiki/Kriegsmarine\" title=\"Kriegsmarine\">Kriegsmarine</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Barents_Sea\" title=\"Battle of the Barents Sea\">Battle of the Barents Sea</a>. This leads to the resignation of <a href=\"https://wikipedia.org/wiki/Grand_Admiral\" class=\"mw-redirect\" title=\"Grand Admiral\">Grand Admiral</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> a month later.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Kriegsmarine", "link": "https://wikipedia.org/wiki/Kriegsmarine"}, {"title": "Battle of the Barents Sea", "link": "https://wikipedia.org/wiki/Battle_of_the_Barents_Sea"}, {"title": "Grand Admiral", "link": "https://wikipedia.org/wiki/Grand_Admiral"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "World War II: Operation Nordwind, the last major Wehrmacht offensive on the Western Front, begins.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Operation_Nordwind\" class=\"mw-redirect\" title=\"Operation Nordwind\">Operation Nordwind</a>, the last major <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a> offensive on the <a href=\"https://wikipedia.org/wiki/Western_Front_(World_War_II)\" title=\"Western Front (World War II)\">Western Front</a>, begins.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operation_Nordwind\" class=\"mw-redirect\" title=\"Operation Nordwind\">Operation Nordwind</a>, the last major <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a> offensive on the <a href=\"https://wikipedia.org/wiki/Western_Front_(World_War_II)\" title=\"Western Front (World War II)\">Western Front</a>, begins.", "links": [{"title": "Operation Nordwind", "link": "https://wikipedia.org/wiki/Operation_Nordwind"}, {"title": "Wehrmacht", "link": "https://wikipedia.org/wiki/Wehrmacht"}, {"title": "Western Front (World War II)", "link": "https://wikipedia.org/wiki/Western_Front_(World_War_II)"}]}, {"year": "1946", "text": "President <PERSON> officially proclaims the end of hostilities in World War II.", "html": "1946 - President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> officially proclaims the <a href=\"https://wikipedia.org/wiki/Proclamation_2714\" title=\"Proclamation 2714\">end of hostilities</a> in World War II.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> officially proclaims the <a href=\"https://wikipedia.org/wiki/Proclamation_2714\" title=\"Proclamation 2714\">end of hostilities</a> in World War II.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Proclamation 2714", "link": "https://wikipedia.org/wiki/Proclamation_2714"}]}, {"year": "1951", "text": "Cold War: The Marshall Plan expires after distributing more than US$13.3 billion in foreign aid to rebuild Western Europe.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Marshall_Plan\" title=\"Marshall Plan\">Marshall Plan</a> expires after distributing more than US$13.3 billion in foreign aid to rebuild <a href=\"https://wikipedia.org/wiki/Western_Europe\" title=\"Western Europe\">Western Europe</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Marshall_Plan\" title=\"Marshall Plan\">Marshall Plan</a> expires after distributing more than US$13.3 billion in foreign aid to rebuild <a href=\"https://wikipedia.org/wiki/Western_Europe\" title=\"Western Europe\">Western Europe</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Marshall Plan", "link": "https://wikipedia.org/wiki/Marshall_Plan"}, {"title": "Western Europe", "link": "https://wikipedia.org/wiki/Western_Europe"}]}, {"year": "1955", "text": "General Motors becomes the first U.S. corporation to make over US$1 billion in a year.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> becomes the first U.S. corporation to make over US$1 billion in a year.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> becomes the first U.S. corporation to make over US$1 billion in a year.", "links": [{"title": "General Motors", "link": "https://wikipedia.org/wiki/General_Motors"}]}, {"year": "1956", "text": "The Romanian Television network begins its first broadcast in Bucharest.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/Romanian_Television\" class=\"mw-redirect\" title=\"Romanian Television\">Romanian Television</a> network begins its first broadcast in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Romanian_Television\" class=\"mw-redirect\" title=\"Romanian Television\">Romanian Television</a> network begins its first broadcast in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>.", "links": [{"title": "Romanian Television", "link": "https://wikipedia.org/wiki/Romanian_Television"}, {"title": "Bucharest", "link": "https://wikipedia.org/wiki/Bucharest"}]}, {"year": "1961", "text": "RTÉ, Ireland's state broadcaster, launches its first national television service.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/RT%C3%89\" title=\"RTÉ\">RTÉ</a>, Ireland's state broadcaster, launches its first national television service.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/RT%C3%89\" title=\"RTÉ\">RTÉ</a>, Ireland's state broadcaster, launches its first national television service.", "links": [{"title": "RTÉ", "link": "https://wikipedia.org/wiki/RT%C3%89"}]}, {"year": "1963", "text": "The Central African Federation officially collapses, subsequently becoming Zambia, Malawi and Rhodesia.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Federation_of_Rhodesia_and_Nyasaland\" title=\"Federation of Rhodesia and Nyasaland\">The Central African Federation</a> officially collapses, subsequently becoming <a href=\"https://wikipedia.org/wiki/Zambia\" title=\"Zambia\">Zambia</a>, <a href=\"https://wikipedia.org/wiki/Malawi\" title=\"Malawi\">Malawi</a> and <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Federation_of_Rhodesia_and_Nyasaland\" title=\"Federation of Rhodesia and Nyasaland\">The Central African Federation</a> officially collapses, subsequently becoming <a href=\"https://wikipedia.org/wiki/Zambia\" title=\"Zambia\">Zambia</a>, <a href=\"https://wikipedia.org/wiki/Malawi\" title=\"Malawi\">Malawi</a> and <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a>.", "links": [{"title": "Federation of Rhodesia and Nyasaland", "link": "https://wikipedia.org/wiki/Federation_of_Rhodesia_and_Nyasaland"}, {"title": "Zambia", "link": "https://wikipedia.org/wiki/Zambia"}, {"title": "Malawi", "link": "https://wikipedia.org/wiki/Malawi"}, {"title": "Rhodesia", "link": "https://wikipedia.org/wiki/Rhodesia"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, leader of the Central African Republic army, and his military officers begin a coup d'état against the government of President <PERSON>.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a> army, and his military officers <a href=\"https://wikipedia.org/wiki/Saint-Sylvestre_coup_d%27%C3%A9tat\" title=\"Saint-Sylvestre coup d'état\">begin a coup d'état</a> against the government of <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Central_African_Republic_and_Central_African_Empire\" class=\"mw-redirect\" title=\"List of heads of state of the Central African Republic and Central African Empire\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a> army, and his military officers <a href=\"https://wikipedia.org/wiki/Saint-Sylvestre_coup_d%27%C3%A9tat\" title=\"Saint-Sylvestre coup d'état\">begin a coup d'état</a> against the government of <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Central_African_Republic_and_Central_African_Empire\" class=\"mw-redirect\" title=\"List of heads of state of the Central African Republic and Central African Empire\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9del_<PERSON>"}, {"title": "Central African Republic", "link": "https://wikipedia.org/wiki/Central_African_Republic"}, {"title": "Saint-Sylvestre coup d'état", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_coup_d%27%C3%A9tat"}, {"title": "List of heads of state of the Central African Republic and Central African Empire", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Central_African_Republic_and_Central_African_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "The first flight of the Tupolev Tu-144, the first civilian supersonic transport in the world.", "html": "1968 - The first flight of the <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-144\" title=\"Tupolev Tu-144\">Tupolev Tu-144</a>, the first civilian supersonic transport in the world.", "no_year_html": "The first flight of the <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-144\" title=\"Tupolev Tu-144\">Tupolev Tu-144</a>, the first civilian supersonic transport in the world.", "links": [{"title": "Tupolev Tu-144", "link": "https://wikipedia.org/wiki/Tupolev_Tu-144"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>on Miller Airlines Flight 1750 crashes near Port Hedland, Western Australia, killing all 26 people on board.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Miller_Airlines_Flight_1750\" title=\"MacRobertson Miller Airlines Flight 1750\">MacR<PERSON>rtson Miller Airlines Flight 1750</a> crashes near <a href=\"https://wikipedia.org/wiki/Port_Hedland,_Western_Australia\" title=\"Port Hedland, Western Australia\">Port Hedland, Western Australia</a>, killing all 26 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Miller_Airlines_Flight_1750\" title=\"MacRobertson Miller Airlines Flight 1750\">MacR<PERSON>rtson Miller Airlines Flight 1750</a> crashes near <a href=\"https://wikipedia.org/wiki/Port_Hedland,_Western_Australia\" title=\"Port Hedland, Western Australia\">Port Hedland, Western Australia</a>, killing all 26 people on board.", "links": [{"title": "MacRobertson Miller Airlines Flight 1750", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Miller_Airlines_Flight_1750"}, {"title": "Port Hedland, Western Australia", "link": "https://wikipedia.org/wiki/Port_Hedland,_Western_Australia"}]}, {"year": "1981", "text": "A coup d'état in Ghana removes President <PERSON><PERSON>'s PNP government and replaces it with the Provisional National Defence Council led by Flight lieutenant <PERSON>.", "html": "1981 - A <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> in <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a> removes <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ghana\" title=\"List of heads of state of Ghana\">President</a> <a href=\"https://wikipedia.org/wiki/Hill<PERSON>_<PERSON>nn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/People%27s_National_Party_(Ghana)\" title=\"People's National Party (Ghana)\">PNP</a> <a href=\"https://wikipedia.org/wiki/Limann_government\" title=\"Limann government\">government</a> and replaces it with the <a href=\"https://wikipedia.org/wiki/Provisional_National_Defence_Council\" title=\"Provisional National Defence Council\">Provisional National Defence Council</a> led by <a href=\"https://wikipedia.org/wiki/Flight_lieutenant\" title=\"Flight lieutenant\">Flight lieutenant</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lings\" title=\"<PERSON> Rawlings\"><PERSON> <PERSON>lings</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> in <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a> removes <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ghana\" title=\"List of heads of state of Ghana\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/People%27s_National_Party_(Ghana)\" title=\"People's National Party (Ghana)\">PNP</a> <a href=\"https://wikipedia.org/wiki/Limann_government\" title=\"Limann government\">government</a> and replaces it with the <a href=\"https://wikipedia.org/wiki/Provisional_National_Defence_Council\" title=\"Provisional National Defence Council\">Provisional National Defence Council</a> led by <a href=\"https://wikipedia.org/wiki/Flight_lieutenant\" title=\"Flight lieutenant\">Flight lieutenant</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lings\" title=\"Jerry Rawlings\"><PERSON> Rawlings</a>.", "links": [{"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "Ghana", "link": "https://wikipedia.org/wiki/Ghana"}, {"title": "List of heads of state of Ghana", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Ghana"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "People's National Party (Ghana)", "link": "https://wikipedia.org/wiki/People%27s_National_Party_(Ghana)"}, {"title": "<PERSON>nn government", "link": "https://wikipedia.org/wiki/Limann_government"}, {"title": "Provisional National Defence Council", "link": "https://wikipedia.org/wiki/Provisional_National_Defence_Council"}, {"title": "Flight lieutenant", "link": "https://wikipedia.org/wiki/Flight_lieutenant"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "The AT&T Bell System is broken up by the United States Government.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/AT%26T_Corporation\" title=\"AT&amp;T Corporation\">AT&amp;T</a> <a href=\"https://wikipedia.org/wiki/Bell_System\" title=\"Bell System\">Bell System</a> is broken up by the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">United States Government</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/AT%26T_Corporation\" title=\"AT&amp;T Corporation\">AT&amp;T</a> <a href=\"https://wikipedia.org/wiki/Bell_System\" title=\"Bell System\">Bell System</a> is broken up by the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">United States Government</a>.", "links": [{"title": "AT&T Corporation", "link": "https://wikipedia.org/wiki/AT%26T_Corporation"}, {"title": "Bell System", "link": "https://wikipedia.org/wiki/Bell_System"}, {"title": "Federal government of the United States", "link": "https://wikipedia.org/wiki/Federal_government_of_the_United_States"}]}, {"year": "1983", "text": "<PERSON> is appointed New York City Police Department's first ever African American police commissioner.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Benjamin_<PERSON>\" title=\"Benjamin Ward\"><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/New_York_City_Police_Department\" title=\"New York City Police Department\">New York City Police Department</a>'s first ever <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African American</a> <a href=\"https://wikipedia.org/wiki/New_York_City_Police_Commissioner\" title=\"New York City Police Commissioner\">police commissioner</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Benjamin_<PERSON>\" title=\"Benjamin Ward\"><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/New_York_City_Police_Department\" title=\"New York City Police Department\">New York City Police Department</a>'s first ever <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African American</a> <a href=\"https://wikipedia.org/wiki/New_York_City_Police_Commissioner\" title=\"New York City Police Commissioner\">police commissioner</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New York City Police Department", "link": "https://wikipedia.org/wiki/New_York_City_Police_Department"}, {"title": "African Americans", "link": "https://wikipedia.org/wiki/African_Americans"}, {"title": "New York City Police Commissioner", "link": "https://wikipedia.org/wiki/New_York_City_Police_Commissioner"}]}, {"year": "1983", "text": "In Nigeria, a coup d'état led by Major General <PERSON><PERSON> ends the Second Nigerian Republic.", "html": "1983 - In <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>, a <a href=\"https://wikipedia.org/wiki/1983_Nigerian_coup_d%27%C3%A9tat\" title=\"1983 Nigerian coup d'état\">coup d'état</a> led by Major General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> ends the <a href=\"https://wikipedia.org/wiki/Second_Nigerian_Republic\" title=\"Second Nigerian Republic\">Second Nigerian Republic</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>, a <a href=\"https://wikipedia.org/wiki/1983_Nigerian_coup_d%27%C3%A9tat\" title=\"1983 Nigerian coup d'état\">coup d'état</a> led by Major General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> ends the <a href=\"https://wikipedia.org/wiki/Second_Nigerian_Republic\" title=\"Second Nigerian Republic\">Second Nigerian Republic</a>.", "links": [{"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "1983 Nigerian coup d'état", "link": "https://wikipedia.org/wiki/1983_Nigerian_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Second Nigerian Republic", "link": "https://wikipedia.org/wiki/Second_Nigerian_Republic"}]}, {"year": "1991", "text": "All official Soviet Union institutions have ceased operations by this date, five days after the Soviet Union is officially dissolved.", "html": "1991 - All official <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> institutions have ceased operations by this date, five days after the Soviet Union is <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union\" title=\"Dissolution of the Soviet Union\">officially dissolved</a>.", "no_year_html": "All official <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> institutions have ceased operations by this date, five days after the Soviet Union is <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union\" title=\"Dissolution of the Soviet Union\">officially dissolved</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Dissolution of the Soviet Union", "link": "https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union"}]}, {"year": "1992", "text": "Czechoslovakia is peacefully dissolved in what is dubbed by media as the Velvet Divorce, resulting in the creation of the Czech Republic and the Slovak Republic.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> is <a href=\"https://wikipedia.org/wiki/Dissolution_of_Czechoslovakia\" title=\"Dissolution of Czechoslovakia\">peacefully dissolved</a> in what is dubbed by media as the <i>Velvet Divorce</i>, resulting in the creation of the <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a> and the <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovak Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> is <a href=\"https://wikipedia.org/wiki/Dissolution_of_Czechoslovakia\" title=\"Dissolution of Czechoslovakia\">peacefully dissolved</a> in what is dubbed by media as the <i>Velvet Divorce</i>, resulting in the creation of the <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a> and the <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovak Republic</a>.", "links": [{"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Dissolution of Czechoslovakia", "link": "https://wikipedia.org/wiki/Dissolution_of_Czechoslovakia"}, {"title": "Czech Republic", "link": "https://wikipedia.org/wiki/Czech_Republic"}, {"title": "Slovakia", "link": "https://wikipedia.org/wiki/Slovakia"}]}, {"year": "1994", "text": "This date is skipped altogether in Kiribati as the Phoenix Islands and Line Islands change time zones from UTC−11:00 to UTC+13:00 and UTC−10:00 to UTC+14:00, respectively.", "html": "1994 - This date is skipped altogether in <a href=\"https://wikipedia.org/wiki/Kiribati\" title=\"Kiribati\">Kiribati</a> as the <a href=\"https://wikipedia.org/wiki/Phoenix_Islands\" title=\"Phoenix Islands\">Phoenix Islands</a> and <a href=\"https://wikipedia.org/wiki/Line_Islands\" title=\"Line Islands\">Line Islands</a> change time zones from <a href=\"https://wikipedia.org/wiki/UTC%E2%88%9211:00\" title=\"UTC−11:00\">UTC−11:00</a> to <a href=\"https://wikipedia.org/wiki/UTC%2B13:00\" title=\"UTC+13:00\">UTC+13:00</a> and <a href=\"https://wikipedia.org/wiki/UTC%E2%88%9210:00\" title=\"UTC−10:00\">UTC−10:00</a> to <a href=\"https://wikipedia.org/wiki/UTC%2B14:00\" title=\"UTC+14:00\">UTC+14:00</a>, respectively.", "no_year_html": "This date is skipped altogether in <a href=\"https://wikipedia.org/wiki/Kiribati\" title=\"Kiribati\">Kiribati</a> as the <a href=\"https://wikipedia.org/wiki/Phoenix_Islands\" title=\"Phoenix Islands\">Phoenix Islands</a> and <a href=\"https://wikipedia.org/wiki/Line_Islands\" title=\"Line Islands\">Line Islands</a> change time zones from <a href=\"https://wikipedia.org/wiki/UTC%E2%88%9211:00\" title=\"UTC−11:00\">UTC−11:00</a> to <a href=\"https://wikipedia.org/wiki/UTC%2B13:00\" title=\"UTC+13:00\">UTC+13:00</a> and <a href=\"https://wikipedia.org/wiki/UTC%E2%88%9210:00\" title=\"UTC−10:00\">UTC−10:00</a> to <a href=\"https://wikipedia.org/wiki/UTC%2B14:00\" title=\"UTC+14:00\">UTC+14:00</a>, respectively.", "links": [{"title": "Kiribati", "link": "https://wikipedia.org/wiki/Kiribati"}, {"title": "Phoenix Islands", "link": "https://wikipedia.org/wiki/Phoenix_Islands"}, {"title": "Line Islands", "link": "https://wikipedia.org/wiki/Line_Islands"}, {"title": "UTC−11:00", "link": "https://wikipedia.org/wiki/UTC%E2%88%9211:00"}, {"title": "UTC+13:00", "link": "https://wikipedia.org/wiki/UTC%2B13:00"}, {"title": "UTC−10:00", "link": "https://wikipedia.org/wiki/UTC%E2%88%9210:00"}, {"title": "UTC+14:00", "link": "https://wikipedia.org/wiki/UTC%2B14:00"}]}, {"year": "1994", "text": "The First Chechen War: The Russian Ground Forces begin a New Year's storming of Grozny.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/First_Chechen_War\" title=\"First Chechen War\">First Chechen War</a>: The <a href=\"https://wikipedia.org/wiki/Russian_Ground_Forces\" title=\"Russian Ground Forces\">Russian Ground Forces</a> begin a <a href=\"https://wikipedia.org/wiki/Battle_of_Grozny_(1994%E2%80%9395)\" class=\"mw-redirect\" title=\"Battle of Grozny (1994-95)\">New Year's storming</a> of <a href=\"https://wikipedia.org/wiki/Grozny\" title=\"Grozny\">Grozny</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Chechen_War\" title=\"First Chechen War\">First Chechen War</a>: The <a href=\"https://wikipedia.org/wiki/Russian_Ground_Forces\" title=\"Russian Ground Forces\">Russian Ground Forces</a> begin a <a href=\"https://wikipedia.org/wiki/Battle_of_Grozny_(1994%E2%80%9395)\" class=\"mw-redirect\" title=\"Battle of Grozny (1994-95)\">New Year's storming</a> of <a href=\"https://wikipedia.org/wiki/Grozny\" title=\"Grozny\">Grozny</a>.", "links": [{"title": "First Chechen War", "link": "https://wikipedia.org/wiki/First_Chechen_War"}, {"title": "Russian Ground Forces", "link": "https://wikipedia.org/wiki/Russian_Ground_Forces"}, {"title": "Battle of Grozny (1994-95)", "link": "https://wikipedia.org/wiki/Battle_of_Grozny_(1994%E2%80%9395)"}, {"title": "Grozny", "link": "https://wikipedia.org/wiki/Grozny"}]}, {"year": "1995", "text": "The final comic of <PERSON> and <PERSON><PERSON><PERSON> is published.", "html": "1995 - The final comic of <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON> and <PERSON><PERSON><PERSON>\"><PERSON> and <PERSON><PERSON></a> is published.", "no_year_html": "The final comic of <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON> and <PERSON><PERSON><PERSON>\"><PERSON> and <PERSON><PERSON><PERSON></a> is published.", "links": [{"title": "<PERSON> and <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "The European Exchange Rate Mechanism freezes the values of the legacy currencies in the Eurozone, and establishes the value of the euro currency.", "html": "1998 - The <a href=\"https://wikipedia.org/wiki/European_Exchange_Rate_Mechanism\" title=\"European Exchange Rate Mechanism\">European Exchange Rate Mechanism</a> freezes the values of the legacy currencies in the <a href=\"https://wikipedia.org/wiki/Eurozone\" title=\"Eurozone\">Eurozone</a>, and establishes the value of the <a href=\"https://wikipedia.org/wiki/Euro\" title=\"Euro\">euro</a> currency.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/European_Exchange_Rate_Mechanism\" title=\"European Exchange Rate Mechanism\">European Exchange Rate Mechanism</a> freezes the values of the legacy currencies in the <a href=\"https://wikipedia.org/wiki/Eurozone\" title=\"Eurozone\">Eurozone</a>, and establishes the value of the <a href=\"https://wikipedia.org/wiki/Euro\" title=\"Euro\">euro</a> currency.", "links": [{"title": "European Exchange Rate Mechanism", "link": "https://wikipedia.org/wiki/European_Exchange_Rate_Mechanism"}, {"title": "Eurozone", "link": "https://wikipedia.org/wiki/Eurozone"}, {"title": "Euro", "link": "https://wikipedia.org/wiki/Euro"}]}, {"year": "1999", "text": "The first President of Russia, <PERSON>, resigns from office, leaving Prime Minister <PERSON> as the acting President and successor.", "html": "1999 - The first <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">President of Russia</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, resigns from office, leaving <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the <a href=\"https://wikipedia.org/wiki/Acting_President_of_Russia\" title=\"Acting President of Russia\">acting President</a> and successor.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">President of Russia</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, resigns from office, leaving <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the <a href=\"https://wikipedia.org/wiki/Acting_President_of_Russia\" title=\"Acting President of Russia\">acting President</a> and successor.", "links": [{"title": "President of Russia", "link": "https://wikipedia.org/wiki/President_of_Russia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Russia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Russia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Acting President of Russia", "link": "https://wikipedia.org/wiki/Acting_President_of_Russia"}]}, {"year": "1999", "text": "The U.S. government hands control of the Panama Canal (as well all the adjacent land to the canal known as the Panama Canal Zone) to Panama. This act complied with the signing of the 1977 Torrijos-Carter Treaties.", "html": "1999 - The U.S. government hands control of the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a> (as well all the adjacent land to the canal known as the <a href=\"https://wikipedia.org/wiki/Panama_Canal_Zone\" title=\"Panama Canal Zone\">Panama Canal Zone</a>) to <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>. This act complied with the signing of the 1977 <a href=\"https://wikipedia.org/wiki/Torrijos%E2%80%93Carter_Treaties\" title=\"<PERSON><PERSON><PERSON>-<PERSON> Treaties\"><PERSON><PERSON><PERSON>-<PERSON> Treaties</a>.", "no_year_html": "The U.S. government hands control of the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a> (as well all the adjacent land to the canal known as the <a href=\"https://wikipedia.org/wiki/Panama_Canal_Zone\" title=\"Panama Canal Zone\">Panama Canal Zone</a>) to <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>. This act complied with the signing of the 1977 <a href=\"https://wikipedia.org/wiki/Torrijos%E2%80%93Carter_Treaties\" title=\"<PERSON><PERSON><PERSON>-<PERSON> Treaties\"><PERSON><PERSON><PERSON>-<PERSON> Treaties</a>.", "links": [{"title": "Panama Canal", "link": "https://wikipedia.org/wiki/Panama_Canal"}, {"title": "Panama Canal Zone", "link": "https://wikipedia.org/wiki/Panama_Canal_Zone"}, {"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "Torrijos<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Torrijos%E2%80%93Carter_Treaties"}]}, {"year": "1999", "text": "Indian Airlines Flight 814 hijacking ends after seven days with the release of 190 survivors at Kandahar Airport, Afghanistan.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Indian_Airlines_Flight_814\" title=\"Indian Airlines Flight 814\">Indian Airlines Flight 814</a> hijacking ends after seven days with the release of 190 survivors at <a href=\"https://wikipedia.org/wiki/Kandahar_Airport\" class=\"mw-redirect\" title=\"Kandahar Airport\">Kandahar Airport</a>, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indian_Airlines_Flight_814\" title=\"Indian Airlines Flight 814\">Indian Airlines Flight 814</a> hijacking ends after seven days with the release of 190 survivors at <a href=\"https://wikipedia.org/wiki/Kandahar_Airport\" class=\"mw-redirect\" title=\"Kandahar Airport\">Kandahar Airport</a>, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "links": [{"title": "Indian Airlines Flight 814", "link": "https://wikipedia.org/wiki/Indian_Airlines_Flight_814"}, {"title": "Kandahar Airport", "link": "https://wikipedia.org/wiki/Kandahar_Airport"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "2001", "text": "Rwanda adopts a new national flag and anthem.", "html": "2001 - Rwanda adopts a new <a href=\"https://wikipedia.org/wiki/Flag_of_Rwanda\" title=\"Flag of Rwanda\">national flag</a> and <a href=\"https://wikipedia.org/wiki/Rwanda_Nziza\" title=\"Rwanda Nziza\">anthem</a>.", "no_year_html": "Rwanda adopts a new <a href=\"https://wikipedia.org/wiki/Flag_of_Rwanda\" title=\"Flag of Rwanda\">national flag</a> and <a href=\"https://wikipedia.org/wiki/Rwanda_Nziza\" title=\"Rwanda Nziza\">anthem</a>.", "links": [{"title": "Flag of Rwanda", "link": "https://wikipedia.org/wiki/Flag_of_Rwanda"}, {"title": "Rwanda Nziza", "link": "https://wikipedia.org/wiki/Rwanda_Nziza"}]}, {"year": "2004", "text": "The official opening of Taipei 101, the tallest skyscraper at that time in the world, standing at a height of 509 metres (1,670 ft).", "html": "2004 - The official opening of <a href=\"https://wikipedia.org/wiki/Taipei_101\" title=\"Taipei 101\">Taipei 101</a>, the <a href=\"https://wikipedia.org/wiki/List_of_tallest_buildings_and_structures_in_the_world\" class=\"mw-redirect\" title=\"List of tallest buildings and structures in the world\">tallest</a> <a href=\"https://wikipedia.org/wiki/Skyscraper\" title=\"Skyscraper\">skyscraper</a> at that time in the world, standing at a height of 509 metres (1,670 ft).", "no_year_html": "The official opening of <a href=\"https://wikipedia.org/wiki/Taipei_101\" title=\"Taipei 101\">Taipei 101</a>, the <a href=\"https://wikipedia.org/wiki/List_of_tallest_buildings_and_structures_in_the_world\" class=\"mw-redirect\" title=\"List of tallest buildings and structures in the world\">tallest</a> <a href=\"https://wikipedia.org/wiki/Skyscraper\" title=\"Skyscraper\">skyscraper</a> at that time in the world, standing at a height of 509 metres (1,670 ft).", "links": [{"title": "Taipei 101", "link": "https://wikipedia.org/wiki/Taipei_101"}, {"title": "List of tallest buildings and structures in the world", "link": "https://wikipedia.org/wiki/List_of_tallest_buildings_and_structures_in_the_world"}, {"title": "Skyscraper", "link": "https://wikipedia.org/wiki/Skyscraper"}]}, {"year": "2009", "text": "Both a blue moon and a lunar eclipse occur.", "html": "2009 - Both a <a href=\"https://wikipedia.org/wiki/Blue_moon\" title=\"Blue moon\">blue moon</a> and a <a href=\"https://wikipedia.org/wiki/December_2009_lunar_eclipse\" title=\"December 2009 lunar eclipse\">lunar eclipse</a> occur.", "no_year_html": "Both a <a href=\"https://wikipedia.org/wiki/Blue_moon\" title=\"Blue moon\">blue moon</a> and a <a href=\"https://wikipedia.org/wiki/December_2009_lunar_eclipse\" title=\"December 2009 lunar eclipse\">lunar eclipse</a> occur.", "links": [{"title": "Blue moon", "link": "https://wikipedia.org/wiki/Blue_moon"}, {"title": "December 2009 lunar eclipse", "link": "https://wikipedia.org/wiki/December_2009_lunar_eclipse"}]}, {"year": "2010", "text": "Tornadoes touch down in midwestern and southern United States, including Washington County, Arkansas; Greater St. Louis, Sunset Hills, Missouri, Illinois, and Oklahoma, with a few tornadoes in the early hours. A total of 36 tornadoes touched down, resulting in the deaths of nine people and $113 million in damages.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/2010_New_Year%27s_Eve_tornado_outbreak\" title=\"2010 New Year's Eve tornado outbreak\">Tornadoes</a> touch down in <a href=\"https://wikipedia.org/wiki/Midwestern_United_States\" title=\"Midwestern United States\">midwestern</a> and <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">southern United States</a>, including <a href=\"https://wikipedia.org/wiki/Washington_County,_Arkansas\" title=\"Washington County, Arkansas\">Washington County, Arkansas</a>; <a href=\"https://wikipedia.org/wiki/Greater_St._Louis\" title=\"Greater St. Louis\">Greater St. Louis</a>, <a href=\"https://wikipedia.org/wiki/Sunset_Hills,_Missouri\" title=\"Sunset Hills, Missouri\">Sunset Hills, Missouri</a>, <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>, and <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a>, with a few tornadoes in the early hours. A total of 36 tornadoes touched down, resulting in the deaths of nine people and $113 million in damages.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2010_New_Year%27s_Eve_tornado_outbreak\" title=\"2010 New Year's Eve tornado outbreak\">Tornadoes</a> touch down in <a href=\"https://wikipedia.org/wiki/Midwestern_United_States\" title=\"Midwestern United States\">midwestern</a> and <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">southern United States</a>, including <a href=\"https://wikipedia.org/wiki/Washington_County,_Arkansas\" title=\"Washington County, Arkansas\">Washington County, Arkansas</a>; <a href=\"https://wikipedia.org/wiki/Greater_St._Louis\" title=\"Greater St. Louis\">Greater St. Louis</a>, <a href=\"https://wikipedia.org/wiki/Sunset_Hills,_Missouri\" title=\"Sunset Hills, Missouri\">Sunset Hills, Missouri</a>, <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>, and <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a>, with a few tornadoes in the early hours. A total of 36 tornadoes touched down, resulting in the deaths of nine people and $113 million in damages.", "links": [{"title": "2010 New Year's Eve tornado outbreak", "link": "https://wikipedia.org/wiki/2010_New_Year%27s_Eve_tornado_outbreak"}, {"title": "Midwestern United States", "link": "https://wikipedia.org/wiki/Midwestern_United_States"}, {"title": "Southern United States", "link": "https://wikipedia.org/wiki/Southern_United_States"}, {"title": "Washington County, Arkansas", "link": "https://wikipedia.org/wiki/Washington_County,_Arkansas"}, {"title": "Greater St. Louis", "link": "https://wikipedia.org/wiki/Greater_St._Louis"}, {"title": "Sunset Hills, Missouri", "link": "https://wikipedia.org/wiki/Sunset_Hills,_Missouri"}, {"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}, {"title": "Oklahoma", "link": "https://wikipedia.org/wiki/Oklahoma"}]}, {"year": "2011", "text": "NASA succeeds in putting the first of two Gravity Recovery and Interior Laboratory satellites in orbit around the Moon.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> succeeds in putting the first of two <a href=\"https://wikipedia.org/wiki/Gravity_Recovery_and_Interior_Laboratory\" class=\"mw-redirect\" title=\"Gravity Recovery and Interior Laboratory\">Gravity Recovery and Interior Laboratory</a> <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellites</a> in orbit around the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> succeeds in putting the first of two <a href=\"https://wikipedia.org/wiki/Gravity_Recovery_and_Interior_Laboratory\" class=\"mw-redirect\" title=\"Gravity Recovery and Interior Laboratory\">Gravity Recovery and Interior Laboratory</a> <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellites</a> in orbit around the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Gravity Recovery and Interior Laboratory", "link": "https://wikipedia.org/wiki/Gravity_Recovery_and_Interior_Laboratory"}, {"title": "Satellite", "link": "https://wikipedia.org/wiki/Satellite"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "2014", "text": "A New Year's Eve celebration stampede in Shanghai kills at least 36 people and injures 49 others.", "html": "2014 - A New Year's Eve celebration <a href=\"https://wikipedia.org/wiki/2014_Shanghai_stampede\" title=\"2014 Shanghai stampede\">stampede</a> in <a href=\"https://wikipedia.org/wiki/Shanghai\" title=\"Shanghai\">Shanghai</a> kills at least 36 people and injures 49 others.", "no_year_html": "A New Year's Eve celebration <a href=\"https://wikipedia.org/wiki/2014_Shanghai_stampede\" title=\"2014 Shanghai stampede\">stampede</a> in <a href=\"https://wikipedia.org/wiki/Shanghai\" title=\"Shanghai\">Shanghai</a> kills at least 36 people and injures 49 others.", "links": [{"title": "2014 Shanghai stampede", "link": "https://wikipedia.org/wiki/2014_Shanghai_stampede"}, {"title": "Shanghai", "link": "https://wikipedia.org/wiki/Shanghai"}]}, {"year": "2015", "text": "A fire breaks out at the Downtown Address Hotel in Downtown Dubai, United Arab Emirates, located near the Burj Khalifa, two hours before the fireworks display is due to commence. Sixteen injuries were reported; one had a heart attack, another suffered a major injury, and fourteen others with minor injuries.", "html": "2015 - A fire breaks out at the Downtown Address Hotel in <a href=\"https://wikipedia.org/wiki/Downtown_Dubai\" title=\"Downtown Dubai\">Downtown Dubai</a>, <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a>, located near the <a href=\"https://wikipedia.org/wiki/Burj_Khalifa\" title=\"B<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, two hours before the <a href=\"https://wikipedia.org/wiki/Fireworks\" title=\"Fireworks\">fireworks</a> display is due to commence. Sixteen injuries were reported; one had a heart attack, another suffered a major injury, and fourteen others with minor injuries.", "no_year_html": "A fire breaks out at the Downtown Address Hotel in <a href=\"https://wikipedia.org/wiki/Downtown_Dubai\" title=\"Downtown Dubai\">Downtown Dubai</a>, <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a>, located near the <a href=\"https://wikipedia.org/wiki/Burj_Khalifa\" title=\"Burj <PERSON>\"><PERSON><PERSON><PERSON></a>, two hours before the <a href=\"https://wikipedia.org/wiki/Fireworks\" title=\"Fireworks\">fireworks</a> display is due to commence. Sixteen injuries were reported; one had a heart attack, another suffered a major injury, and fourteen others with minor injuries.", "links": [{"title": "Downtown Dubai", "link": "https://wikipedia.org/wiki/Downtown_Dubai"}, {"title": "United Arab Emirates", "link": "https://wikipedia.org/wiki/United_Arab_Emirates"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Fireworks", "link": "https://wikipedia.org/wiki/Fireworks"}]}, {"year": "2018", "text": "Thirty-nine people are killed after a ten-story building collapses in the industrial city of Magnitogorsk, Russia.", "html": "2018 - Thirty-nine people are killed after <a href=\"https://wikipedia.org/wiki/2018_Magnitogorsk_building_collapse\" title=\"2018 Magnitogorsk building collapse\">a ten-story building collapses</a> in the industrial city of <a href=\"https://wikipedia.org/wiki/Magnitogorsk\" title=\"Magnitogorsk\">Magnitogorsk</a>, Russia.", "no_year_html": "Thirty-nine people are killed after <a href=\"https://wikipedia.org/wiki/2018_Magnitogorsk_building_collapse\" title=\"2018 Magnitogorsk building collapse\">a ten-story building collapses</a> in the industrial city of <a href=\"https://wikipedia.org/wiki/Magnitogorsk\" title=\"Magnitogorsk\">Magnitogorsk</a>, Russia.", "links": [{"title": "2018 Magnitogorsk building collapse", "link": "https://wikipedia.org/wiki/2018_Magnitogorsk_building_collapse"}, {"title": "Magnitogorsk", "link": "https://wikipedia.org/wiki/Magnitogorsk"}]}, {"year": "2019", "text": "The World Health Organization is informed of cases of pneumonia with an unknown cause, detected in Wuhan. This later turned out to be COVID-19, the cause of the COVID-19 pandemic.", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> is informed of cases of pneumonia with an unknown cause, detected in <a href=\"https://wikipedia.org/wiki/<PERSON>han\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>. This later turned out to be <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a>, the cause of the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> is informed of cases of pneumonia with an unknown cause, detected in <a href=\"https://wikipedia.org/wiki/<PERSON>han\" title=\"<PERSON>han\"><PERSON><PERSON></a>. This later turned out to be <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a>, the cause of the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>.", "links": [{"title": "World Health Organization", "link": "https://wikipedia.org/wiki/World_Health_Organization"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>han"}, {"title": "COVID-19", "link": "https://wikipedia.org/wiki/COVID-19"}, {"title": "COVID-19 pandemic", "link": "https://wikipedia.org/wiki/COVID-19_pandemic"}]}, {"year": "2020", "text": "The World Health Organization issues its first emergency use validation for a COVID-19 vaccine.", "html": "2020 - The World Health Organization issues its first emergency use validation for a <a href=\"https://wikipedia.org/wiki/COVID-19_vaccine\" title=\"COVID-19 vaccine\">COVID-19 vaccine</a>.", "no_year_html": "The World Health Organization issues its first emergency use validation for a <a href=\"https://wikipedia.org/wiki/COVID-19_vaccine\" title=\"COVID-19 vaccine\">COVID-19 vaccine</a>.", "links": [{"title": "COVID-19 vaccine", "link": "https://wikipedia.org/wiki/COVID-19_vaccine"}]}], "Births": [{"year": "695", "text": "<PERSON> ibn <PERSON>, Umayyad general (d. 715)", "html": "695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_ibn_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Umayyad general (d. 715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_ibn_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Umayyad general (d. 715)", "links": [{"title": "<PERSON> ibn <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1378", "text": "<PERSON> <PERSON><PERSON><PERSON> (d. 1458)", "html": "1378 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Callixtus_III\" title=\"<PERSON> <PERSON><PERSON>tus III\"><PERSON> <PERSON><PERSON><PERSON> III</a> (d. 1458)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Callixtus_III\" title=\"Pope <PERSON>ixtus III\"><PERSON> <PERSON><PERSON><PERSON> III</a> (d. 1458)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_Callixtus_III"}]}, {"year": "1491", "text": "<PERSON>, French navigator and explorer (d. 1557)", "html": "1491 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French navigator and explorer (d. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French navigator and explorer (d. 1557)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1493", "text": "<PERSON><PERSON><PERSON>, Duchess of Urbino (d. 1570)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duchess_of_Urbino\" title=\"<PERSON><PERSON><PERSON>, Duchess of Urbino\"><PERSON><PERSON><PERSON>, Duchess of Urbino</a> (d. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duchess_of_Urbino\" title=\"<PERSON><PERSON><PERSON>, Duchess of Urbino\"><PERSON><PERSON><PERSON>, Duchess of Urbino</a> (d. 1570)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duchess of Urbino", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duchess_of_Urbino"}]}, {"year": "1504", "text": "<PERSON> of Portugal, Duchess of Savoy (d. 1538)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Duchess_of_Savoy\" title=\"<PERSON> of Portugal, Duchess of Savoy\"><PERSON> of Portugal, Duchess of Savoy</a> (d. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beatrice_of_Portugal,_Duchess_of_Savoy\" title=\"Beatrice of Portugal, Duchess of Savoy\"><PERSON> of Portugal, Duchess of Savoy</a> (d. 1538)", "links": [{"title": "<PERSON> of Portugal, Duchess of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Portugal,_Duchess_of_Savoy"}]}, {"year": "1514", "text": "<PERSON>, Belgian anatomist, physician, and author (d. 1564)", "html": "1514 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian anatomist, physician, and author (d. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian anatomist, physician, and author (d. 1564)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1539", "text": "<PERSON>, English politician (d. 1568)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1568)\" class=\"mw-redirect\" title=\"<PERSON> (died 1568)\"><PERSON></a>, English politician (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1568)\" class=\"mw-redirect\" title=\"<PERSON> (died 1568)\"><PERSON></a>, English politician (d. 1568)", "links": [{"title": "<PERSON> (died 1568)", "link": "https://wikipedia.org/wiki/<PERSON>(died_1568)"}]}, {"year": "1550", "text": "<PERSON>, Duke of Guise (d. 1588)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Guise\"><PERSON>, Duke of Guise</a> (d. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Guise\"><PERSON>, Duke of Guise</a> (d. 1588)", "links": [{"title": "<PERSON>, Duke of Guise", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_Guise"}]}, {"year": "1552", "text": "<PERSON>, English occultist and astrologer (d. 1611)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English occultist and astrologer (d. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English occultist and astrologer (d. 1611)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1572", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan, (d. 1617)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Y%C5%8Dzei\" title=\"Emperor <PERSON>-Yōzei\">Emperor <PERSON><PERSON>Y<PERSON><PERSON><PERSON></a> of Japan, (d. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Go-Y%C5%8Dzei\" title=\"Emperor Go-Yōzei\">Emperor <PERSON><PERSON>Yō<PERSON><PERSON></a> of Japan, (d. 1617)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Go-Y%C5%8Dzei"}]}, {"year": "1585", "text": "<PERSON><PERSON><PERSON>, Spanish general and politician, 24th Governor of the Duchy of Milan (d. 1645)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/Go<PERSON>lo_Fern%C3%A1ndez_de_C%C3%B3rdoba_(1585%E2%80%931645)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (1585-1645)\"><PERSON><PERSON><PERSON></a>, Spanish general and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_the_Duchy_of_Milan\" class=\"mw-redirect\" title=\"Governor of the Duchy of Milan\">Governor of the Duchy of Milan</a> (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Go<PERSON>lo_Fern%C3%A1ndez_de_C%C3%B3rdoba_(1585%E2%80%931645)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (1585-1645)\"><PERSON><PERSON><PERSON></a>, Spanish general and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_the_Duchy_of_Milan\" class=\"mw-redirect\" title=\"Governor of the Duchy of Milan\">Governor of the Duchy of Milan</a> (d. 1645)", "links": [{"title": "<PERSON><PERSON><PERSON> (1585-1645)", "link": "https://wikipedia.org/wiki/Gonzalo_Fern%C3%A1ndez_de_C%C3%B3rdoba_(1585%E2%80%931645)"}, {"title": "Governor of the Duchy of Milan", "link": "https://wikipedia.org/wiki/Governor_of_the_Duchy_of_Milan"}]}, {"year": "1668", "text": "<PERSON>, Dutch botanist and physician (d. 1738)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch botanist and physician (d. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch botanist and physician (d. 1738)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1714", "text": "<PERSON><PERSON>, Japanese mathematician and educator (d. 1783)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese mathematician and educator (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese mathematician and educator (d. 1783)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1720", "text": "<PERSON>, Scottish claimant to the throne of England (d. 1788)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish claimant to the throne of England (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish claimant to the throne of England (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1738", "text": "<PERSON>, 1st <PERSON>, English general and politician, 3rd Governor-General of India (d. 1805)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_<PERSON>\" title=\"<PERSON>, 1st Marquess <PERSON>\"><PERSON>, 1st Marquess <PERSON></a>, English general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_<PERSON>\" title=\"<PERSON>, 1st Marquess <PERSON>\"><PERSON>, 1st Marquess <PERSON></a>, English general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (d. 1805)", "links": [{"title": "<PERSON>, 1st Marquess <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_<PERSON>"}, {"title": "Governor-General of India", "link": "https://wikipedia.org/wiki/Governor-General_of_India"}]}, {"year": "1741", "text": "<PERSON><PERSON><PERSON>, German poet and academic (d. 1794)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_August_B%C3%BCrger\" title=\"Gott<PERSON> August Bürger\"><PERSON><PERSON><PERSON> August <PERSON></a>, German poet and academic (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_August_B%C3%BCrger\" title=\"<PERSON><PERSON><PERSON> August Bürger\"><PERSON><PERSON><PERSON> August <PERSON></a>, German poet and academic (d. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_August_B%C3%BCrger"}]}, {"year": "1763", "text": "<PERSON><PERSON><PERSON>, French admiral (d. 1806)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French admiral (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French admiral (d. 1806)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1776", "text": "<PERSON>, German-American physician and phrenologist (d. 1832)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physician and phrenologist (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physician and phrenologist (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, Estonian physician, philologist, and academic (d. 1850)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physician, philologist, and academic (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physician, philologist, and academic (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, German-French historian and author (d. 1876)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Agoult\" title=\"<PERSON>\"><PERSON></a>, German-French historian and author (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ago<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French historian and author (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Agoult"}]}, {"year": "1815", "text": "<PERSON>, American general and engineer (d. 1872)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and engineer (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and engineer (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON><PERSON>, Egyptian ruler (d. 1895)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian ruler (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian ruler (d. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isma%27il_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Scottish poet and critic (d. 1867)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and critic (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and critic (d. 1867)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1833", "text": "<PERSON>-Australian politician, 11th Premier of Queensland (d. 1906)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a> Scottish-Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a> Scottish-Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1906)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_politician)"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1834", "text": "<PERSON> <PERSON><PERSON><PERSON> of Hawaii (d. 1899)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" class=\"mw-redirect\" title=\"Queen <PERSON>\">Queen <PERSON></a> of Hawaii (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\">Queen <PERSON></a> of Hawaii (d. 1899)", "links": [{"title": "Queen <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1838", "text": "<PERSON><PERSON>, French lawyer and politician, 7th President of France (d. 1929)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Loubet"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1842", "text": "<PERSON>, Italian painter (d. 1931)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, American economist and academic (d. 1921)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, Italian poet and scholar (d. 1912)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and scholar (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and scholar (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, American baseball player and manager (d. 1894)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, American businessman, co-founded Texaco (d. 1937)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Texaco\" title=\"Texaco\">Texaco</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Texaco\" title=\"Texaco\">Texaco</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Texaco", "link": "https://wikipedia.org/wiki/Texaco"}]}, {"year": "1864", "text": "<PERSON>, American astronomer and academic (d. 1951)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, French painter and sculptor (d. 1954)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, American race car driver (d. 1956)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Estonian surgeon and politician, 19th Estonian Minister of Education (d. 1936)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian surgeon and politician, 19th <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_Education\" class=\"mw-redirect\" title=\"Estonian Minister of Education\">Estonian Minister of Education</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian surgeon and politician, 19th <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_Education\" class=\"mw-redirect\" title=\"Estonian Minister of Education\">Estonian Minister of Education</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Estonian Minister of Education", "link": "https://wikipedia.org/wiki/Estonian_Minister_of_Education"}]}, {"year": "1874", "text": "<PERSON>, American businessman and politician, 20th Governor of Oregon (d. 1937)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1877", "text": "<PERSON>, English journalist and author (d. 1967)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Canadian businesswoman, founded Elizabeth Arden, Inc. (d. 1966)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Inc.\" title=\"Elizabeth <PERSON>rden, Inc.\">Elizabeth <PERSON>, Inc.</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Inc.\" title=\"Elizabeth <PERSON>rden, Inc.\">Elizabeth <PERSON>, Inc.</a> (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Elizabeth Arden, Inc.", "link": "https://wikipedia.org/wiki/<PERSON>_Arden,_Inc."}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Uruguayan-Argentinian author, poet, and playwright (d. 1937)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan-Argentinian author, poet, and playwright (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan-Argentinian author, poet, and playwright (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American baseball player and coach (d. 1957)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American general and politician, 50th United States Secretary of State, Nobel Prize laureate (d. 1959)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 50th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 50th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1881", "text": "<PERSON>, German painter and academic (d. 1955)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and academic (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and academic (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American baseball and soccer player (d. 1964)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball and soccer player (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball and soccer player (d. 1964)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian actor, screenwriter, and film director (d. 1960)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Mih%C3%<PERSON><PERSON>_<PERSON>kete\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian actor, screenwriter, and film director (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mih%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian actor, screenwriter, and film director (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mih%C3%A1ly_Fekete"}]}, {"year": "1885", "text": "Princess <PERSON> of Schleswig-Holstein (d. 1970)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_Adelaide_of_Schleswig-Holstein\" title=\"Princess <PERSON> of Schleswig-Holstein\">Princess <PERSON> of Schleswig-Holstein</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_Adelaide_of_Schleswig-Holstein\" title=\"Princess <PERSON> of Schleswig-Holstein\">Princess <PERSON> of Schleswig-Holstein</a> (d. 1970)", "links": [{"title": "Princess <PERSON> of Schleswig-Holstein", "link": "https://wikipedia.org/wiki/Princess_Victoria_Adelaide_of_Schleswig-Holstein"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Mexican violinist, composer, and conductor (d. 1940)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican violinist, composer, and conductor (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican violinist, composer, and conductor (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Finnish politician, val<PERSON><PERSON><PERSON>, the Speaker of the Parliament and the Prime Minister of Finland (d. 1984)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON>-<PERSON>\">Karl-<PERSON></a>, Finnish politician, <i><a href=\"https://wikipedia.org/wiki/Valtioneuvos\" title=\"Valtioneuvos\">valtioneuvos</a></i>, the <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Finland\" title=\"Speaker of the Parliament of Finland\">Speaker of the Parliament</a> and the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a> (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician, <i><a href=\"https://wikipedia.org/wiki/Valtioneuvos\" title=\"Valtioneuvos\">valtioneuvos</a></i>, the <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Finland\" title=\"Speaker of the Parliament of Finland\">Speaker of the Parliament</a> and the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a> (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "Valtioneuvos", "link": "https://wikipedia.org/wiki/Valtioneuvos"}, {"title": "Speaker of the Parliament of Finland", "link": "https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Finland"}, {"title": "Prime Minister of Finland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Finland"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Greek educator and politician (d. 1954)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek educator and politician (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek educator and politician (d. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mpidis"}]}, {"year": "1902", "text": "<PERSON>, Canadian singer-songwriter (d. 1982)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English footballer (d. 1982)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English engineer (d. 1989)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American astronomer and academic (d. 2002)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Ukrainian-Austrian Nazi hunter and author (d. 2005)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Austrian <a href=\"https://wikipedia.org/wiki/Nazi_hunter\" title=\"Nazi hunter\">Nazi hunter</a> and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Austrian <a href=\"https://wikipedia.org/wiki/Nazi_hunter\" title=\"Nazi hunter\">Nazi hunter</a> and author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nazi hunter", "link": "https://wikipedia.org/wiki/Nazi_hunter"}]}, {"year": "1909", "text": "<PERSON>, American trumpet player and saxophonist (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and saxophonist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and saxophonist (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American director, producer, and screenwriter (d. 1973)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Spanish tennis player (d. 1981)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Australian soldier and author (d. 1997)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_St<PERSON>\" title=\"<PERSON> St<PERSON>\"><PERSON></a>, Australian soldier and author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_St<PERSON>ns\" title=\"<PERSON> St<PERSON>\"><PERSON></a>, Australian soldier and author (d. 1997)", "links": [{"title": "Dal St<PERSON>", "link": "https://wikipedia.org/wiki/Dal_Stivens"}]}, {"year": "1912", "text": "<PERSON>, Indian-English general (d. 1993)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Indian-English general (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Indian-English general (d. 1993)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "1914", "text": "<PERSON>, American neuroembryologist (d. 1966)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroembryologist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroembryologist (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American journalist, author, and poet (d. 1996)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and poet (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and poet (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American singer (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (d. 2007)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON><PERSON>, English mountaineer and author (d. 1962)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">Wil<PERSON><PERSON></a>, English mountaineer and author (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, English mountaineer and author (d. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American football player and coach (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American baseball player, coach, and politician (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and politician (d. 2007)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American soldier (d. 2017)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American soldier (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American soldier (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor and singer-songwriter (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer-songwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer-songwriter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Brazilian bishop (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Balduino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian bishop (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Balduino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian bishop (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Balduino"}]}, {"year": "1922", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish pianist and educator (d. 2001)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Halina_Czerny-Stefa%C5%84ska\" title=\"<PERSON><PERSON> Czerny-Stefańska\"><PERSON><PERSON>ef<PERSON>ń<PERSON></a>, Polish pianist and educator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hal<PERSON>_<PERSON>ny-Stefa%C5%84ska\" title=\"<PERSON><PERSON>ny-Stefańska\"><PERSON><PERSON></a>, Polish pianist and educator (d. 2001)", "links": [{"title": "Halina <PERSON>ny-Stefańska", "link": "https://wikipedia.org/wiki/Halina_Czerny-Stefa%C5%84ska"}]}, {"year": "1922", "text": "<PERSON>, Venezuelan baseball player (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Greek actor, director, and screenwriter (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor, director, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor, director, and screenwriter (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor and poet (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mead\"><PERSON></a>, American actor and poet (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mead\"><PERSON></a>, American actor and poet (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, German author and screenwriter (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and screenwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Indian author (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Sri_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sri Lal <PERSON>\">Sri <PERSON></a>, Indian author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sri_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sri Lal Su<PERSON>\"><PERSON></a>, Indian author (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, British composer and electronic musician (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British composer and electronic musician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British composer and electronic musician (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English historian and academic (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Australian lawyer and politician, 17th Attorney-General for Australia (d. 1987)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Attorney-General_for_Australia\" class=\"mw-redirect\" title=\"Attorney-General for Australia\">Attorney-General for Australia</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Attorney-General_for_Australia\" class=\"mw-redirect\" title=\"Attorney-General for Australia\">Attorney-General for Australia</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney-General for Australia", "link": "https://wikipedia.org/wiki/Attorney-General_for_Australia"}]}, {"year": "1928", "text": "<PERSON>, American pop singer (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American pop singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American pop singer (d. 2011)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1928", "text": "<PERSON>, American football player (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Finnish author and translator (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish author and translator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish author and translator (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Russian actress and singer (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian actress and singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian actress and singer (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ga"}]}, {"year": "1928", "text": "<PERSON><PERSON>, French cartoonist (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Sin%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cartoonist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sin%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cartoonist (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sin%C3%A9"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Dutch television host (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch television host (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch television host (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English cricketer (d. 1994)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1994)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1930", "text": "<PERSON>, Bolivian-American educator (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian-American educator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian-American educator (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, guitarist, and actress (d. 2008)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actress (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>detta"}]}, {"year": "1931", "text": "<PERSON>, Northern Irish journalist and author (d. 1996)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish journalist and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish journalist and author (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American football player and coach (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2013)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1932", "text": "<PERSON>, German journalist and author (d. 1992)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American author, screenwriter, and actor (d. 2005)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, screenwriter, and actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, screenwriter, and actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Indian author, poet, and scholar (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author, poet, and scholar (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author, poet, and scholar (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Hungarian-Israeli biochemist and physician, Nobel Prize laureate", "html": "1937 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Israeli biochemist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Israeli biochemist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1937", "text": "<PERSON>, Welsh footballer and manager (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Austrian-English painter and educator", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English painter and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English painter and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American singer and actress (d. 1995)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cash\"><PERSON><PERSON></a>, American singer and actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cash\"><PERSON><PERSON></a>, American singer and actress (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Dutch speed skater (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/At<PERSON>_<PERSON>-<PERSON>\" title=\"At<PERSON>-<PERSON>\"><PERSON><PERSON></a>, Dutch speed skater (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/At<PERSON>_<PERSON>-<PERSON>\" title=\"Atje <PERSON>n-Deelstra\"><PERSON><PERSON></a>, Dutch speed skater (d. 2013)", "links": [{"title": "Atje <PERSON>-Deelstra", "link": "https://wikipedia.org/wiki/Atje_Keulen-Dee<PERSON>tra"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American sprinter and long jumper (d. 2007)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter and long jumper (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter and long jumper (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, German drummer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German drummer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "Sir <PERSON>, Scottish footballer and manager", "html": "1941 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English journalist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Northern Irish footballer and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, South African international development academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African international development academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African international development academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Zimbabwean archbishop", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean archbishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pius_<PERSON>cube"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Russian ice dancer (d. 1986)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian ice dancer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian ice dancer (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American tennis player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Scottish journalist and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English businessman, founded Williams Holdings", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Williams_Holdings\" title=\"Williams Holdings\">Williams Holdings</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Williams_Holdings\" title=\"Williams Holdings\">Williams Holdings</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Williams Holdings", "link": "https://wikipedia.org/wiki/Williams_Holdings"}]}, {"year": "1946", "text": "<PERSON>, English bishop", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Scottish footballer and manager (d. 2014)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> J<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> J<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sandy_<PERSON>ine"}]}, {"year": "1949", "text": "<PERSON>, American anthologist and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthologist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Bissau-Guinean filmmaker", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Go<PERSON>\"><PERSON></a>, Bissau-Guinean filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gomes\"><PERSON></a>, Bissau-Guinean filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Flora_Gomes"}]}, {"year": "1949", "text": "<PERSON>, American author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American golfer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, German sprinter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Inge_<PERSON>\" title=\"Inge Helten\">In<PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inge_<PERSON>\" title=\"Inge Helten\">In<PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Inge_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American businesswoman", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American motorcycle racer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, New Zealand mathematician and academic (d. 2020)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand mathematician and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand mathematician and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, French rugby player, painter, and sculptor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French rugby player, painter, and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French rugby player, painter, and sculptor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Scottish economist and politician, First Minister of Scotland (d. 2024)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Minister of Scotland", "link": "https://wikipedia.org/wiki/First_Minister_of_Scotland"}]}, {"year": "1954", "text": "<PERSON>, German racing driver, architect and engineer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver, architect and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver, architect and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, 9th governor of American Samoa", "html": "1955 - <a href=\"https://wikipedia.org/wiki/1955\" title=\"1955\">1955</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nikolao_Pula\" title=\"Pula Nikolao Pula\"><PERSON><PERSON></a>, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_American_Samoa\" class=\"mw-redirect\" title=\"Governor of American Samoa\">governor of American Samoa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1955\" title=\"1955\">1955</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nikola<PERSON>_Pula\" title=\"Pula Nikolao Pula\"><PERSON><PERSON></a>, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_American_Samoa\" class=\"mw-redirect\" title=\"Governor of American Samoa\">governor of American Samoa</a>", "links": [{"title": "1955", "link": "https://wikipedia.org/wiki/1955"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of American Samoa", "link": "https://wikipedia.org/wiki/Governor_of_American_Samoa"}]}, {"year": "1956", "text": "<PERSON>, English farmer and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English farmer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English farmer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, German shot putter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German shot putter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American author and illustrator", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Australian cricketer and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_And<PERSON>\" title=\"Liveris Andritsos\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_And<PERSON>\" title=\"Liveris Andritsos\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Andrits<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American lawyer and politician, Kansas Attorney General", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Kansas_Attorney_General\" title=\"Kansas Attorney General\">Kansas Attorney General</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Kansas_Attorney_General\" title=\"Kansas Attorney General\">Kansas Attorney General</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Kline"}, {"title": "Kansas Attorney General", "link": "https://wikipedia.org/wiki/Kansas_Attorney_General"}]}, {"year": "1959", "text": "<PERSON>, Nauruan composer and politician, 14th President of Nauru", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Baron_<PERSON>\" title=\"Baron <PERSON>\">Baron <PERSON></a>, Nauruan composer and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baron_<PERSON>\" title=\"Baron <PERSON>\">Baron <PERSON></a>, Nauruan composer and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}, {"title": "President of Nauru", "link": "https://wikipedia.org/wiki/President_of_Nauru"}]}, {"year": "1960", "text": "<PERSON>, English footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American baseball player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English economist and civil servant (d. 2018)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and civil servant (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and civil servant (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Hong Kong actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English-Welsh swimmer and wheelchair racer (d. 2013)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh swimmer and wheelchair racer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh swimmer and wheelchair racer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American composer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Antiguan cricketer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Antiguan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Antiguan cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American comedian, actor, and director", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and director", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_(comedian)"}]}, {"year": "1965", "text": "<PERSON>, Australian-English footballer and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian cartoonist and author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cartoonist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cartoonist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Lax<PERSON>_<PERSON>\" title=\"<PERSON>x<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lax<PERSON>_<PERSON>\" title=\"<PERSON>x<PERSON>\">Lax<PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian rugby league player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1968", "text": "<PERSON>, Canadian comedian, actor, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian, actor, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Dominican-born American novelist, short story writer, and essayist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican-born American novelist, short story writer, and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican-born American novelist, short story writer, and essayist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Jorj%C3%A3<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jorj%C3%A3<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Jorj%C3%A3o_(footballer)"}]}, {"year": "1970", "text": "<PERSON>, English singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1970", "text": "<PERSON>, Spanish-Danish architect and sailor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish-Danish architect and sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish-Danish architect and sailor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American basketball player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Mexican baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Esteban_Loaiza\" title=\"Esteban Loaiza\"><PERSON><PERSON><PERSON></a>, Mexican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Loaiza\" title=\"Esteban Loaiza\"><PERSON><PERSON><PERSON></a>, Mexican baseball player", "links": [{"title": "Esteban Loaiza", "link": "https://wikipedia.org/wiki/Esteban_Lo<PERSON>za"}]}, {"year": "1971", "text": "<PERSON>, American football player and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Gr%C3%A9gory_<PERSON>t\" title=\"<PERSON><PERSON><PERSON><PERSON>t\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gr%C3%A9gory_<PERSON>t\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gr%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Scottish YouTube personality", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish YouTube personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish YouTube personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian swimmer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Belgian cyclist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Brazilian race car driver", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Japanese-American wrestler and trainer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American wrestler and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American wrestler and trainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Finnish footballer and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Dutch footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Dutch runner", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Portuguese motorcycle racer (d. 2012)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Carreira\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese motorcycle racer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%AD<PERSON>_Carreira\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese motorcycle racer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lu%C3%ADs_Carreira"}]}, {"year": "1976", "text": "<PERSON>, English cricketer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Costa Rican footballer and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wardy_Alfaro"}]}, {"year": "1979", "text": "<PERSON>, English racing driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(racing_driver)"}]}, {"year": "1979", "text": "<PERSON>, American lawyer and politician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>iche<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, British actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>hit<PERSON>\"><PERSON></a>, British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>hittle\"><PERSON></a>, British actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hittle"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American wrestler", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1980", "text": "<PERSON>, New Zealand rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, German runner", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ang<PERSON>\" title=\"Carsten Schlangen\"><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Schlangen\"><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Dominican basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Francisco_Garc%C3%ADa_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Dominican basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Garc%C3%ADa_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Dominican basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/Francisco_Garc%C3%ADa_(basketball)"}]}, {"year": "1981", "text": "<PERSON>, Australian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Ghanaian heptathlete", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian heptathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Dominican baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Scottish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON> Rocket Summer, American singer-songwriter, guitarist, and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/The_Rocket_Summer\" title=\"The Rocket Summer\">The Rocket Summer</a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Rocket_Summer\" title=\"The Rocket Summer\">The Rocket Summer</a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "The Rocket Summer", "link": "https://wikipedia.org/wiki/The_Rocket_Summer"}]}, {"year": "1983", "text": "<PERSON>, Czech basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>l%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jana_Vesel%C3%A1"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1984)\" title=\"<PERSON><PERSON> (footballer, born 1984)\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1984)\" title=\"<PERSON><PERSON> (footballer, born 1984)\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1984)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1984)"}]}, {"year": "1984", "text": "<PERSON>, Congolese footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American gymnast", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Dutch singer and television host", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Dutch singer and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Dutch singer and television host", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ka<PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Ivorian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Seydou_Doumbia\" title=\"Seydou Doumbia\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seyd<PERSON>_Doumbia\" title=\"Seydou Doumbia\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Doumbia"}]}, {"year": "1987", "text": "<PERSON>, Dutch footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Nemanja_Nikoli%C4%87_(footballer,_born_1987)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1987)\"><PERSON><PERSON><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nemanja_Nikoli%C4%87_(footballer,_born_1987)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1987)\"><PERSON><PERSON><PERSON></a>, Hungarian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1987)", "link": "https://wikipedia.org/wiki/Nemanja_Nikoli%C4%87_(footballer,_born_1987)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Mi<PERSON>_%C5%98ep%C3%ADk\" title=\"<PERSON><PERSON> Řepík\"><PERSON><PERSON> Řepík</a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mi<PERSON>_%C5%98ep%C3%ADk\" title=\"<PERSON><PERSON> Řepík\"><PERSON><PERSON> Řepík</a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON> Řepík", "link": "https://wikipedia.org/wiki/Michal_%C5%98ep%C3%ADk"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Japanese kickboxer and professional wrestler", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese kickboxer and professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese kickboxer and professional wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Swedish ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Togolese footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Djen%C3%A9\" title=\"<PERSON>jen<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Togolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Djen%C3%A9\" title=\"<PERSON>jen<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Togolese footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Djen%C3%A9"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American cartoonist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/ND_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American cartoonist", "links": [{"title": "ND <PERSON>", "link": "https://wikipedia.org/wiki/ND_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian track cyclist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Amy Cure\"><PERSON></a>, Australian track cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Amy Cure\"><PERSON></a>, Australian track cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Estonian racing driver", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American gymnast", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON> <PERSON><PERSON>-Whiteside, Spanish-American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>-Whiteside\" title=\"<PERSON><PERSON> <PERSON><PERSON>-Whiteside\"><PERSON><PERSON> <PERSON><PERSON>-<PERSON></a>, Spanish-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>-<PERSON>side\" title=\"<PERSON><PERSON> <PERSON><PERSON>-Whiteside\"><PERSON><PERSON> <PERSON><PERSON>-<PERSON></a>, Spanish-American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>-Whiteside", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON><PERSON><PERSON>-Whiteside"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, English-American soccer player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>-<PERSON>, Nigerian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Italian-Nigerian footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, English footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>if_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Parks\"><PERSON><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tennis player", "links": [{"title": "Al<PERSON>cia <PERSON>", "link": "https://wikipedia.org/wiki/Alycia_Parks"}]}, {"year": "2001", "text": "<PERSON>, American tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Dutch footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American soccer player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "45 BC", "text": "<PERSON><PERSON><PERSON>, consul suffectus", "html": "45 BC - 45 BC - <a href=\"https://wikipedia.org/wiki/Q<PERSON>tus_<PERSON><PERSON><PERSON>_Maximus_(consul_45_BC)\" title=\"<PERSON><PERSON>tus Fabius Maximus (consul 45 BC)\"><PERSON><PERSON><PERSON> Fabius Maximus</a>, consul suffectus", "no_year_html": "45 BC - <a href=\"https://wikipedia.org/wiki/Q<PERSON>tus_<PERSON><PERSON>us_Maximus_(consul_45_BC)\" title=\"<PERSON><PERSON>tus <PERSON>abius Maximus (consul 45 BC)\"><PERSON><PERSON><PERSON> Fabius Maximus</a>, consul suffectus", "links": [{"title": "<PERSON><PERSON><PERSON> (consul 45 BC)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(consul_45_BC)"}]}, {"year": "192", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 161)", "html": "192 - <a href=\"https://wikipedia.org/wiki/Commodus\" title=\"Commodus\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 161)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Commodus\" title=\"Commodus\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 161)", "links": [{"title": "Commodus", "link": "https://wikipedia.org/wiki/Commodus"}]}, {"year": "335", "text": "<PERSON>", "html": "335 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Sylvester I\"><PERSON> I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Sylvester I\"><PERSON> I</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "669", "text": "<PERSON>, Chinese general (b. 594)", "html": "669 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Shiji\"><PERSON></a>, Chinese general (b. 594)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shiji\"><PERSON></a>, Chinese general (b. 594)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_<PERSON>ji"}]}, {"year": "914", "text": "<PERSON>, founder of the Isma'ili community in Yemen", "html": "914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27ili\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\">Is<PERSON><PERSON><PERSON>i</a> community in Yemen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27ili\" class=\"mw-redirect\" title=\"<PERSON><PERSON>'il<PERSON>\">Is<PERSON><PERSON>ili</a> community in Yemen", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isma%27ili"}]}, {"year": "1032", "text": "<PERSON>, Persian statesman, vizier of the Ghaznavid Empire", "html": "1032 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Persian statesman, vizier of the <a href=\"https://wikipedia.org/wiki/Ghaznavid_Empire\" class=\"mw-redirect\" title=\"Ghaznavid Empire\">Ghaznavid Empire</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Persian statesman, vizier of the <a href=\"https://wikipedia.org/wiki/Ghaznavid_Empire\" class=\"mw-redirect\" title=\"Ghaznavid Empire\">Ghaznavid Empire</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ghaznavid Empire", "link": "https://wikipedia.org/wiki/Ghaznavid_Empire"}]}, {"year": "1164", "text": "<PERSON><PERSON> of Styria (b. 1124)", "html": "1164 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_of_Styria\" title=\"<PERSON><PERSON> III of Styria\"><PERSON><PERSON> III of Styria</a> (b. 1124)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_of_Styria\" title=\"<PERSON><PERSON> III of Styria\"><PERSON><PERSON> III of Styria</a> (b. 1124)", "links": [{"title": "<PERSON><PERSON> of Styria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_III_of_Styria"}]}, {"year": "1194", "text": "<PERSON>, Duke of Austria (b. 1157)", "html": "1194 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1157)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1157)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/Leopold_<PERSON>,_Duke_of_Austria"}]}, {"year": "1298", "text": "<PERSON>, 3rd Earl of Hereford, English politician, Lord High Constable of England (b. 1249)", "html": "1298 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Hereford\" title=\"<PERSON>, 3rd Earl of Hereford\"><PERSON>, 3rd Earl of Hereford</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (b. 1249)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Hereford\" title=\"<PERSON>, 3rd Earl of Hereford\"><PERSON>, 3rd Earl of Hereford</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (b. 1249)", "links": [{"title": "<PERSON>, 3rd Earl of Hereford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Earl_of_Hereford"}, {"title": "Lord High Constable of England", "link": "https://wikipedia.org/wiki/Lord_High_Constable_of_England"}]}, {"year": "1299", "text": "<PERSON>, Countess of Anjou (b. 1273)", "html": "1299 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Anjou\" title=\"<PERSON>, Countess of Anjou\"><PERSON>, Countess of Anjou</a> (b. 1273)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Anjou\" title=\"<PERSON>, Countess of Anjou\"><PERSON>, Countess of Anjou</a> (b. 1273)", "links": [{"title": "<PERSON>, Countess of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_of_Anjou"}]}, {"year": "1302", "text": "<PERSON>, Duke of Lorraine (b. 1238)", "html": "1302 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1238)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1238)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1384", "text": "<PERSON>, English philosopher, theologian, and translator (b. 1331)", "html": "1384 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, theologian, and translator (b. 1331)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, theologian, and translator (b. 1331)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1386", "text": "<PERSON> of Bavaria, Queen of Bohemia (b. c. 1362)", "html": "1386 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bavaria,_Queen_of_Bohemia\" class=\"mw-redirect\" title=\"<PERSON> of Bavaria, Queen of Bohemia\"><PERSON> of Bavaria, Queen of Bohemia</a> (b. c. 1362)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bavaria,_Queen_of_Bohemia\" class=\"mw-redirect\" title=\"<PERSON> of Bavaria, Queen of Bohemia\"><PERSON> of Bavaria, Queen of Bohemia</a> (b. c. 1362)", "links": [{"title": "<PERSON> of Bavaria, Queen of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria,_Queen_of_Bohemia"}]}, {"year": "1426", "text": "<PERSON>, Duke of Exeter (b. 1377)", "html": "1426 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Exeter\" title=\"<PERSON>, Duke of Exeter\"><PERSON>, Duke of Exeter</a> (b. 1377)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Exeter\" title=\"<PERSON>, Duke of Exeter\"><PERSON>, Duke of Exeter</a> (b. 1377)", "links": [{"title": "<PERSON>, Duke of Exeter", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Exeter"}]}, {"year": "1439", "text": "<PERSON>, English noblewoman (b. 1385)", "html": "1439 - <a href=\"https://wikipedia.org/wiki/Margaret_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (b. 1385)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (b. 1385)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Margaret_<PERSON>"}]}, {"year": "1460", "text": "<PERSON>, 5th Earl of Salisbury, English politician, Lord Chancellor of the United Kingdom (b. 1400)", "html": "1460 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Salisbury\" title=\"<PERSON>, 5th Earl of Salisbury\"><PERSON>, 5th Earl of Salisbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of the United Kingdom</a> (b. 1400)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Salisbury\" title=\"<PERSON>, 5th Earl of Salisbury\"><PERSON>, 5th Earl of Salisbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of the United Kingdom</a> (b. 1400)", "links": [{"title": "<PERSON>, 5th Earl of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Salisbury"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1510", "text": "<PERSON>, Holy Roman <PERSON> (b. 1472)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Holy Roman <PERSON> (b. 1472)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Holy Roman <PERSON> (b. 1472)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1535", "text": "<PERSON>, English-Irish politician, Lord Deputy of Ireland (b. 1465)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish politician, <a href=\"https://wikipedia.org/wiki/Lord_Deputy_of_Ireland\" title=\"Lord Deputy of Ireland\">Lord Deputy of Ireland</a> (b. 1465)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish politician, <a href=\"https://wikipedia.org/wiki/Lord_Deputy_of_Ireland\" title=\"Lord Deputy of Ireland\">Lord Deputy of Ireland</a> (b. 1465)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lord Deputy of Ireland", "link": "https://wikipedia.org/wiki/Lord_Deputy_of_Ireland"}]}, {"year": "1568", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (b. 1493)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1493)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1575", "text": "<PERSON><PERSON>, Italian commander and jurist (b. 1502)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian commander and jurist (b. 1502)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian commander and jurist (b. 1502)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pier<PERSON>_Belli"}]}, {"year": "1583", "text": "<PERSON>, Swiss physician and theologian (b. 1524)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and theologian (b. 1524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and theologian (b. 1524)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1610", "text": "<PERSON><PERSON><PERSON><PERSON>, German-Dutch mathematician and academic (b. 1540)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-Dutch mathematician and academic (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-Dutch mathematician and academic (b. 1540)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1637", "text": "<PERSON>, Count of Waldeck-Wildungen, German count (b. 1585)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Waldeck-Wildungen\" title=\"<PERSON>, Count of Waldeck-Wildungen\"><PERSON>, Count of Waldeck-Wildungen</a>, German count (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Waldeck-Wildungen\" title=\"<PERSON>, Count of Waldeck-Wildungen\"><PERSON>, Count of Waldeck-Wildungen</a>, German count (b. 1585)", "links": [{"title": "<PERSON>, Count of Waldeck-Wildungen", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_Waldeck-<PERSON>ungen"}]}, {"year": "1650", "text": "<PERSON><PERSON>, Chinese emperor (b. 1612)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Dorgon\"><PERSON><PERSON></a>, Chinese emperor (b. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Dorgon\"><PERSON><PERSON></a>, Chinese emperor (b. 1612)", "links": [{"title": "Dorgon", "link": "https://wikipedia.org/wiki/Do<PERSON>"}]}, {"year": "1655", "text": "<PERSON><PERSON><PERSON>, Polish-Lithuanian politician (b. 1612)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Radziwi%C5%82%C5%82_(1612%E2%80%931655)\" title=\"<PERSON><PERSON><PERSON> (1612-1655)\"><PERSON><PERSON><PERSON></a>, Polish-Lithuanian politician (b. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Radziwi%C5%82%C5%82_(1612%E2%80%931655)\" title=\"<PERSON><PERSON><PERSON> (1612-1655)\"><PERSON><PERSON><PERSON></a>, Polish-Lithuanian politician (b. 1612)", "links": [{"title": "<PERSON><PERSON><PERSON> (1612-1655)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Radziwi%C5%82%C5%82_(1612%E2%80%931655)"}]}, {"year": "1655", "text": "Sir <PERSON>, 2nd Baronet, English politicians and Roundheads supporter (b. 1586) ", "html": "1655 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English politicians and Roundheads supporter (b. 1586) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English politicians and Roundheads supporter (b. 1586) ", "links": [{"title": "Sir <PERSON>, 2nd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet"}]}, {"year": "1673", "text": "<PERSON>, English judge and politician, Chief Justice of the Common Pleas (b. 1598)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/Oliver_<PERSON>_John\" title=\"Oliver St John\"><PERSON></a>, English judge and politician, <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Common_Pleas\" title=\"Chief Justice of the Common Pleas\">Chief Justice of the Common Pleas</a> (b. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_John\" title=\"Oliver St John\"><PERSON></a>, English judge and politician, <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Common_Pleas\" title=\"Chief Justice of the Common Pleas\">Chief Justice of the Common Pleas</a> (b. 1598)", "links": [{"title": "Oliver St John", "link": "https://wikipedia.org/wiki/Oliver_St_John"}, {"title": "Chief Justice of the Common Pleas", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_Common_Pleas"}]}, {"year": "1679", "text": "<PERSON>, Italian physiologist and physicist (b. 1608)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physiologist and physicist (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physiologist and physicist (b. 1608)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1691", "text": "<PERSON>, Anglo-Irish chemist and physicist (b. 1627)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish chemist and physicist (b. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish chemist and physicist (b. 1627)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1691", "text": "<PERSON>, English merchant and economist (b. 1641)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English merchant and economist (b. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dudley_North_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English merchant and economist (b. 1641)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)"}]}, {"year": "1705", "text": "<PERSON> Braganza (b. 1638)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Braganza\" title=\"<PERSON> of Braganza\"><PERSON> of Braganza</a> (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Braganza\" title=\"<PERSON> of Braganza\"><PERSON> of Braganza</a> (b. 1638)", "links": [{"title": "Catherine of Braganza", "link": "https://wikipedia.org/wiki/Catherine_of_Braganza"}]}, {"year": "1719", "text": "<PERSON>, English astronomer and academic (b. 1646)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic (b. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON>, Maltese architect, engineer and poet (b. 1651)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese architect, engineer and poet (b. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese architect, engineer and poet (b. 1651)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON>, Elector <PERSON> (b. 1661)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a> (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>ector <PERSON>\"><PERSON>, Elector <PERSON></a> (b. 1661)", "links": [{"title": "<PERSON>, Elector <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON>, American general (b. 1738)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1738)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, French historian and author (b. 1723)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7ois_Marmontel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and author (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7ois_Marmontel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and author (b. 1723)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A7ois_Marmontel"}]}, {"year": "1818", "text": "<PERSON><PERSON><PERSON>, French cellist (b. 1741)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cellist (b. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cellist (b. 1741)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON>, Finnish author and playwright (b. 1834)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish author and playwright (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish author and playwright (b. 1834)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, French nun and saint (b. 1806)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French nun and saint (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French nun and saint (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Catherine_Labour%C3%A9"}]}, {"year": "1877", "text": "<PERSON><PERSON>, French-Swiss painter and sculptor (b. 1819)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Swiss painter and sculptor (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Swiss painter and sculptor (b. 1819)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, German rabbi and scholar (b. 1808)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Samson <PERSON>\"><PERSON></a>, German rabbi and scholar (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Samson <PERSON>\"><PERSON></a>, German rabbi and scholar (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Romanian author and educator (b. 1837)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83\" title=\"<PERSON>\"><PERSON></a>, Romanian author and educator (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83\" title=\"<PERSON>\"><PERSON></a>, Romanian author and educator (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ion_Creang%C4%83"}]}, {"year": "1889", "text": "<PERSON>, English-Australian politician, 10th Premier of Victoria (b. 1831)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1890", "text": "<PERSON><PERSON>, Costa Rican soldier (b. 1826)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican soldier (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican soldier (b. 1826)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>asco"}]}, {"year": "1891", "text": "<PERSON>, Nigerian bishop and linguist (b. 1809)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian bishop and linguist (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian bishop and linguist (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Dutch mathematician and academic (b. 1856)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician and academic (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician and academic (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American financier and philanthropist (b. 1844)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and philanthropist (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and philanthropist (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American pilot (b. 1884)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American pilot and engineer (b. 1868)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and engineer (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and engineer (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American lawyer and politician (b. 1860)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (b. 1860)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Boies_Penrose"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, American marine biologist (b. 1849)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Cornelia_Clapp\" title=\"Cornelia Clapp\"><PERSON><PERSON><PERSON></a>, American marine biologist (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornelia_Clapp\" title=\"Cornelia Clapp\"><PERSON><PERSON><PERSON></a>, American marine biologist (b. 1849)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cornelia_Clapp"}]}, {"year": "1936", "text": "<PERSON>, Spanish philosopher, author, and poet (b. 1864)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish philosopher, author, and poet (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish philosopher, author, and poet (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English racing driver and journalist (b. 1885)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and journalist (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and journalist (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Turkish philosopher, poet, and politician (b. 1869)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/R%C4%B1za_Tevfik_B%C3%B6l%C3%BCkba%C5%9F%C4%B1\" title=\"<PERSON><PERSON>za Tevfik Bölükbaşı\"><PERSON><PERSON><PERSON>v<PERSON></a>, Turkish philosopher, poet, and politician (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C4%B1za_Tevfik_B%C3%B6l%C3%BCkba%C5%9F%C4%B1\" title=\"<PERSON><PERSON>za Tevfik Bölükbaşı\"><PERSON><PERSON><PERSON>ş<PERSON></a>, Turkish philosopher, poet, and politician (b. 1869)", "links": [{"title": "Rıza Tevfik Bölükbaşı", "link": "https://wikipedia.org/wiki/R%C4%B1za_Tevfik_B%C3%B6l%C3%BCkba%C5%9F%C4%B1"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Estonian pianist and composer (b. 1913)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pianist and composer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pianist and composer (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, French composer and educator (b. 1867)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Indian Muslim scholar (b. 1868)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Muslim scholar (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Muslim scholar (b. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Dutch businessman, founded KLM (b. 1889)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman, founded <a href=\"https://wikipedia.org/wiki/KLM\" title=\"K<PERSON>\">KLM</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman, founded <a href=\"https://wikipedia.org/wiki/KLM\" title=\"K<PERSON>\">KLM</a> (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "KLM", "link": "https://wikipedia.org/wiki/KLM"}]}, {"year": "1964", "text": "<PERSON>, American baseball and soccer player (b. 1884)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball and soccer player (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball and soccer player (b. 1884)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic lawyer and politician, 8th Prime Minister of Iceland (b. 1892)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/%C3%93lafu<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93lafu<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93lafur_<PERSON>s"}, {"title": "Prime Minister of Iceland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iceland"}]}, {"year": "1964", "text": "<PERSON>, English field marshal (b. 1881)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field marshal (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field marshal (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American clarinet player and composer (b. 1900)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clarinetist)\" title=\"<PERSON> (clarinetist)\"><PERSON></a>, American clarinet player and composer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clarinetist)\" title=\"<PERSON> (clarinetist)\"><PERSON></a>, American clarinet player and composer (b. 1900)", "links": [{"title": "<PERSON> (clarinetist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clarinetist)"}]}, {"year": "1970", "text": "<PERSON>, English composer, writer, and poet (b. 1879)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, writer, and poet (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, writer, and poet (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Puerto Rican-American baseball player and Marine (b. 1934)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and <PERSON> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and <PERSON> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, German-American activist, founded the Society for Human Rights (b. 1892)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American activist, founded the <a href=\"https://wikipedia.org/wiki/Society_for_Human_Rights\" title=\"Society for Human Rights\">Society for Human Rights</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American activist, founded the <a href=\"https://wikipedia.org/wiki/Society_for_Human_Rights\" title=\"Society for Human Rights\">Society for Human Rights</a> (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Society for Human Rights", "link": "https://wikipedia.org/wiki/Society_for_Human_Rights"}]}, {"year": "1978", "text": "<PERSON>, American illustrator (b. 1909)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Basil_Wolverton"}]}, {"year": "1980", "text": "<PERSON>, Canadian philosopher and theorist (b. 1911)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher and theorist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher and theorist (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1887)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Turkish author and playwright (b. 1931)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish author and playwright (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish author and playwright (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>k"}]}, {"year": "1985", "text": "<PERSON>, American singer-songwriter, guitarist, and actor (b. 1940)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American journalist (b. 1929)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anchorman)\" title=\"<PERSON> (anchorman)\"><PERSON></a>, American journalist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anchorman)\" title=\"<PERSON> (anchorman)\"><PERSON></a>, American journalist (b. 1929)", "links": [{"title": "<PERSON> (anchorman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anchorman)"}]}, {"year": "1988", "text": "<PERSON>, Greek-American poet and critic (b. 1907)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American poet and critic (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American poet and critic (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player and coach (b. 1918)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (b. 1918)", "links": [{"title": "<PERSON> (American football coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Russian physician, colonel, and astronaut (b. 1928)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physician, colonel, and astronaut (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physician, colonel, and astronaut (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Italian architect and urban planner, designed the Firenze Santa Maria Novella railway station (b. 1891)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and urban planner, designed the <a href=\"https://wikipedia.org/wiki/Firenze_Santa_Maria_Novella_railway_station\" title=\"Firenze Santa Maria Novella railway station\">Firenze Santa Maria Novella railway station</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and urban planner, designed the <a href=\"https://wikipedia.org/wiki/Firenze_Santa_Maria_Novella_railway_station\" title=\"Firenze Santa Maria Novella railway station\">Firenze Santa Maria Novella railway station</a> (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Firenze Santa Maria Novella railway station", "link": "https://wikipedia.org/wiki/Firenze_Santa_Maria_Novella_railway_station"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Georgian anthropologist and politician, 1st President of Georgia (b. 1939)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Zviad_Gamsakhurdia\" title=\"<PERSON>via<PERSON> Gamsakhur<PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian anthropologist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President of Georgia</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zvia<PERSON>_Gamsakhurdia\" title=\"<PERSON>via<PERSON> Gamsakhurdia\"><PERSON><PERSON><PERSON></a>, Georgian anthropologist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President of Georgia</a> (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zviad_Gamsakhurdia"}, {"title": "President of Georgia", "link": "https://wikipedia.org/wiki/President_of_Georgia"}]}, {"year": "1993", "text": "<PERSON>, American murder victim (b. 1972)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Teen<PERSON>\"><PERSON></a>, American murder victim (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Teena\"><PERSON></a>, American murder victim (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Irish cattle and twice Guinness World Record holder (oldest cow, cow with most offspring) (b. 1945)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(cow)\" title=\"<PERSON> <PERSON><PERSON> (cow)\"><PERSON> Bert<PERSON></a>, Irish <a href=\"https://wikipedia.org/wiki/Cattle\" title=\"Cattle\">cattle</a> and twice <a href=\"https://wikipedia.org/wiki/Guinness_World_Record\" class=\"mw-redirect\" title=\"Guinness World Record\">Guinness World Record</a> holder (oldest cow, cow with most offspring) (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(cow)\" title=\"<PERSON> <PERSON><PERSON> (cow)\"><PERSON> Bertha</a>, Irish <a href=\"https://wikipedia.org/wiki/Cattle\" title=\"Cattle\">cattle</a> and twice <a href=\"https://wikipedia.org/wiki/Guinness_World_Record\" class=\"mw-redirect\" title=\"Guinness World Record\">Guinness World Record</a> holder (oldest cow, cow with most offspring) (b. 1945)", "links": [{"title": "<PERSON> (cow)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(cow)"}, {"title": "Cattle", "link": "https://wikipedia.org/wiki/Cattle"}, {"title": "Guinness World Record", "link": "https://wikipedia.org/wiki/Guinness_World_Record"}]}, {"year": "1994", "text": "<PERSON>, American football player, wrestler, and actor (b. 1914)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, wrestler, and actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, wrestler, and actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1996", "text": "<PERSON>, American actor (b. 1913)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American singer-songwriter and pianist (b. 1933)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actress (b. 1903)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dove\"><PERSON></a>, American actress (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dove\"><PERSON></a>, American actress (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Australian rugby league player and coach (b. 1934)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American lawyer and politician, 69th United States Attorney General (b. 1920)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 69th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 69th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Indian Muslim scholar and author (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian Muslim scholar and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian Muslim scholar and author (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American journalist and politician (b. 1914)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Italian-American dancer and choreographer (b. 1918)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Greco\" title=\"<PERSON>\"><PERSON></a>, Italian-American dancer and choreographer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Greco\" title=\"<PERSON>\"><PERSON></a>, Italian-American dancer and choreographer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Greco"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American-Israeli rabbi and scholar (b. 1966)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27ev_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>v <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American-Israeli rabbi and scholar (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27ev_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>ev <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American-Israeli rabbi and scholar (b. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bin<PERSON>in_<PERSON>e%27ev_<PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American actress (b. 1919)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian guitarist, songwriter, and producer (b. 1951)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist, songwriter, and producer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist, songwriter, and producer (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON> German-American physicist and author (b. 1898)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> German-American physicist and author (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> German-American physicist and author (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, French economist and mathematician, Nobel Prize laureate (b. 1921)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>u\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French economist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French economist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_De<PERSON>u"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2005", "text": "<PERSON>, American tenor and educator (b. 1932)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and educator (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and educator (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English screenwriter, producer, and politician (b. 1937)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter, producer, and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter, producer, and politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli footballer (b. 1927)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Ya%27<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>'<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli footballer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya%27<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>'<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli footballer (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya%27<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American sociologist, author, and academic (b. 1922)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, author, and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, author, and academic (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Jr., American businessman (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman (b. 1917)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2007", "text": "<PERSON>, American scientific researcher (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientific researcher (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientific researcher (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American painter and educator (b. 1924)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter and educator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter and educator (b. 1924)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>(painter)"}]}, {"year": "2007", "text": "<PERSON>, American actor, producer, and screenwriter (b. 1919)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Canadian lawyer and politician (b. 1910)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Austrian-Italian architect and designer (b. 1917)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Ettore_Sottsass\" title=\"Ettore Sottsass\"><PERSON><PERSON><PERSON></a>, Austrian-Italian architect and designer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ettore_<PERSON>ass\" title=\"Ettore Sottsass\"><PERSON><PERSON><PERSON></a>, Austrian-Italian architect and designer (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ettore_<PERSON>ttsass"}]}, {"year": "2008", "text": "<PERSON>, American author and screenwriter (b. 1933)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Irish cardinal and philosopher, Archbishop of Armagh (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish cardinal and philosopher, <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Armagh\" title=\"Roman Catholic Archdiocese of Armagh\">Archbishop of Armagh</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish cardinal and philosopher, <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Armagh\" title=\"Roman Catholic Archdiocese of Armagh\">Archbishop of Armagh</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Roman Catholic Archdiocese of Armagh", "link": "https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Armagh"}]}, {"year": "2009", "text": "<PERSON>, Irish surgeon, journalist, and politician, Minister for Industry and Commerce (b. 1930)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish surgeon, journalist, and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Enterprise,_Trade_and_Employment\" title=\"Minister for Enterprise, Trade and Employment\">Minister for Industry and Commerce</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish surgeon, journalist, and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Enterprise,_Trade_and_Employment\" title=\"Minister for Enterprise, Trade and Employment\">Minister for Industry and Commerce</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Enterprise, Trade and Employment", "link": "https://wikipedia.org/wiki/Minister_for_Enterprise,_Trade_and_Employment"}]}, {"year": "2010", "text": "<PERSON>, Belgian cyclist (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>anis"}]}, {"year": "2010", "text": "<PERSON>, Swedish actor, director, producer, and screenwriter (b. 1927)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor, director, producer, and screenwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor, director, producer, and screenwriter (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Tunisian businessman and politician (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tunisian businessman and politician (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tunisian businessman and politician (b. 1958)", "links": [{"title": "Tarak <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ki"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Canadian author and playwright (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian author and playwright (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian author and playwright (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, German photographer and journalist (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/G%C3%BCnter_R%C3%B6<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German photographer and journalist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%BCnter_R%C3%B6<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German photographer and journalist (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCnter_R%C3%B6ssler"}]}, {"year": "2013", "text": "<PERSON>, American actor (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1945)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "2013", "text": "<PERSON>, Italian guitarist and composer (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian guitarist and composer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian guitarist and composer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American radio host (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(radio_host)\" title=\"<PERSON> (radio host)\"><PERSON></a>, American radio host (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(radio_host)\" title=\"<PERSON> (radio host)\"><PERSON></a>, American radio host (b. 1929)", "links": [{"title": "<PERSON> (radio host)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(radio_host)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, German author and screenwriter (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and screenwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and screenwriter (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actor (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Malaysian author (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American author and activist (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and activist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and activist (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American captain, lawyer, and judge (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American captain, lawyer, and judge (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American captain, lawyer, and judge (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, 8th Duke of Wellington, British soldier and politician (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_8th_Duke_of_Wellington\" title=\"<PERSON><PERSON>, 8th Duke of Wellington\"><PERSON><PERSON>, 8th Duke <PERSON> Wellington</a>, British soldier and politician (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_8th_Duke_of_Wellington\" title=\"<PERSON><PERSON>, 8th Duke of Wellington\"><PERSON><PERSON>, 8th Duke of Wellington</a>, British soldier and politician (b. 1915)", "links": [{"title": "<PERSON><PERSON>, 8th Duke of Wellington", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_8th_Duke_of_Wellington"}]}, {"year": "2015", "text": "<PERSON>, American singer-songwriter and actress (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actor and investor (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and investor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and investor (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Indian actor (b. 1937)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actress, comedian and producer (b. 1922)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_White\" title=\"<PERSON> White\"><PERSON></a>, American actress, comedian and producer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON> <PERSON>, German Roman Catholic cardinal and theologian, pope (2005-2013) and archbishop of Munich and Freising (1977-1982) (b. 1927)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>, German Roman Catholic cardinal and theologian, pope (2005-2013) and archbishop of Munich and Freising (1977-1982) (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>, German Roman Catholic cardinal and theologian, pope (2005-2013) and archbishop of Munich and Freising (1977-1982) (b. 1927)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, English golfer (b. 1960)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Barry Lane\"><PERSON></a>, English golfer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barry_Lane\" title=\"Barry Lane\"><PERSON></a>, English golfer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American Hall of Fame racing driver and founder of <PERSON> Motorsports, NASCAR Cup Series champion (1976, 1977, 1978) (b. 1939)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/2023\" title=\"2023\">2023</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/International_Motorsports_Hall_of_Fame\" title=\"International Motorsports Hall of Fame\">Hall of Fame</a> racing driver and founder of <a href=\"https://wikipedia.org/wiki/<PERSON>_Ya<PERSON>orough_Motorsports\" title=\"<PERSON> Motorsports\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/NASCAR_Cup_Series\" title=\"NASCAR Cup Series\">NASCAR Cup Series</a> champion (<a href=\"https://wikipedia.org/wiki/1976_NASCAR_Cup_Series\" class=\"mw-redirect\" title=\"1976 NASCAR Cup Series\">1976</a>, <a href=\"https://wikipedia.org/wiki/1977_NASCAR_Cup_Series\" class=\"mw-redirect\" title=\"1977 NASCAR Cup Series\">1977</a>, <a href=\"https://wikipedia.org/wiki/1978_NASCAR_Cup_Series\" class=\"mw-redirect\" title=\"1978 NASCAR Cup Series\">1978</a>) (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2023\" title=\"2023\">2023</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/International_Motorsports_Hall_of_Fame\" title=\"International Motorsports Hall of Fame\">Hall of Fame</a> racing driver and founder of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ugh_Motorsports\" title=\"<PERSON> Motorsports\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/NASCAR_Cup_Series\" title=\"NASCAR Cup Series\">NASCAR Cup Series</a> champion (<a href=\"https://wikipedia.org/wiki/1976_NASCAR_Cup_Series\" class=\"mw-redirect\" title=\"1976 NASCAR Cup Series\">1976</a>, <a href=\"https://wikipedia.org/wiki/1977_NASCAR_Cup_Series\" class=\"mw-redirect\" title=\"1977 NASCAR Cup Series\">1977</a>, <a href=\"https://wikipedia.org/wiki/1978_NASCAR_Cup_Series\" class=\"mw-redirect\" title=\"1978 NASCAR Cup Series\">1978</a>) (b. 1939)", "links": [{"title": "2023", "link": "https://wikipedia.org/wiki/2023"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "International Motorsports Hall of Fame", "link": "https://wikipedia.org/wiki/International_Motorsports_Hall_of_Fame"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Motorsports"}, {"title": "NASCAR Cup Series", "link": "https://wikipedia.org/wiki/NASCAR_Cup_Series"}, {"title": "1976 NASCAR Cup Series", "link": "https://wikipedia.org/wiki/1976_NASCAR_Cup_Series"}, {"title": "1977 NASCAR Cup Series", "link": "https://wikipedia.org/wiki/1977_NASCAR_Cup_Series"}, {"title": "1978 NASCAR Cup Series", "link": "https://wikipedia.org/wiki/1978_NASCAR_Cup_Series"}]}, {"year": "2024", "text": "<PERSON>, Estonian politician, 3rd President of Estonia (b. 1928)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC%C3%BCtel\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Estonia\" title=\"President of Estonia\">President of Estonia</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC%C3%BCtel\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Estonia\" title=\"President of Estonia\">President of Estonia</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arnold_R%C3%BC%C3%BCtel"}, {"title": "President of Estonia", "link": "https://wikipedia.org/wiki/President_of_Estonia"}]}, {"year": "2024", "text": "<PERSON><PERSON>, British radio DJ (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(DJ)\" title=\"<PERSON><PERSON> (DJ)\"><PERSON><PERSON></a>, British radio DJ (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(DJ)\" title=\"<PERSON><PERSON> (DJ)\"><PERSON><PERSON></a>, British radio DJ (b. 1945)", "links": [{"title": "<PERSON><PERSON> (DJ)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(DJ)"}]}]}}