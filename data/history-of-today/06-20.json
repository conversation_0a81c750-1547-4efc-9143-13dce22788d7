{"date": "June 20", "url": "https://wikipedia.org/wiki/June_20", "data": {"Events": [{"year": "451", "text": "Battle of Chalons: <PERSON><PERSON><PERSON> battles Attila the Hun. After the battle, which was inconclusive, <PERSON><PERSON><PERSON> retreats, causing the Romans to interpret it as a victory.", "html": "451 - <a href=\"https://wikipedia.org/wiki/Battle_of_Chalons\" class=\"mw-redirect\" title=\"Battle of Chalons\">Battle of Chalons</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Aetius\" title=\"<PERSON><PERSON><PERSON> Aetius\"><PERSON><PERSON><PERSON> Aetius</a> battles <a href=\"https://wikipedia.org/wiki/Attila_the_Hun\" class=\"mw-redirect\" title=\"Attila the Hun\">Attila the Hun</a>. After the battle, which was inconclusive, <PERSON><PERSON><PERSON> retreats, causing the Romans to interpret it as a victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Chalons\" class=\"mw-redirect\" title=\"Battle of Chalons\">Battle of Chalons</a>: <a href=\"https://wikipedia.org/wiki/F<PERSON><PERSON>_Aetius\" title=\"<PERSON>lav<PERSON> Aetius\"><PERSON><PERSON><PERSON> Aetius</a> battles <a href=\"https://wikipedia.org/wiki/Attila_the_Hun\" class=\"mw-redirect\" title=\"Attila the Hun\"><PERSON><PERSON>a the Hun</a>. After the battle, which was inconclusive, <PERSON><PERSON><PERSON> retreats, causing the Romans to interpret it as a victory.", "links": [{"title": "Battle of Chalons", "link": "https://wikipedia.org/wiki/Battle_of_Chalons"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>etius"}, {"title": "<PERSON><PERSON><PERSON> the Hun", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Hun"}]}, {"year": "1180", "text": "First Battle of Uji, starting the Genpei War in Japan.", "html": "1180 - <a href=\"https://wikipedia.org/wiki/Battle_of_Uji_(1180)\" title=\"Battle of Uji (1180)\">First Battle of Uji</a>, starting the <a href=\"https://wikipedia.org/wiki/Genpei_War\" title=\"Genpei War\">Genpei War</a> in <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Uji_(1180)\" title=\"Battle of Uji (1180)\">First Battle of Uji</a>, starting the <a href=\"https://wikipedia.org/wiki/Genpei_War\" title=\"Genpei War\">Genpei War</a> in <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>.", "links": [{"title": "Battle of Uji (1180)", "link": "https://wikipedia.org/wiki/Battle_of_Uji_(1180)"}, {"title": "Genpei War", "link": "https://wikipedia.org/wiki/Genpei_War"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}]}, {"year": "1295", "text": "The Treaty of Anagni, an attempt mediated by the papacy to end the War of the Sicilian Vespers, is signed by the crown of Aragon, the kingdom of France and kingdom of Naples.", "html": "1295 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Anagni\" title=\"Treaty of Anagni\">Treaty of Anagni</a>, an attempt mediated by the papacy to end the <a href=\"https://wikipedia.org/wiki/War_of_the_Sicilian_Vespers\" title=\"War of the Sicilian Vespers\">War of the Sicilian Vespers</a>, is signed by the <a href=\"https://wikipedia.org/wiki/Crown_of_Aragon\" title=\"Crown of Aragon\">crown of Aragon</a>, the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">kingdom of France</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Naples\" title=\"Kingdom of Naples\">kingdom of Naples</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Anagni\" title=\"Treaty of Anagni\">Treaty of Anagni</a>, an attempt mediated by the papacy to end the <a href=\"https://wikipedia.org/wiki/War_of_the_Sicilian_Vespers\" title=\"War of the Sicilian Vespers\">War of the Sicilian Vespers</a>, is signed by the <a href=\"https://wikipedia.org/wiki/Crown_of_Aragon\" title=\"Crown of Aragon\">crown of Aragon</a>, the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">kingdom of France</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Naples\" title=\"Kingdom of Naples\">kingdom of Naples</a>.", "links": [{"title": "Treaty of Anagni", "link": "https://wikipedia.org/wiki/Treaty_of_Anagni"}, {"title": "War of the Sicilian Vespers", "link": "https://wikipedia.org/wiki/War_of_the_Sicilian_Vespers"}, {"title": "Crown of Aragon", "link": "https://wikipedia.org/wiki/Crown_of_Aragon"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Kingdom of Naples", "link": "https://wikipedia.org/wiki/Kingdom_of_Naples"}]}, {"year": "1622", "text": "The Battle of Höchst takes place during the  Thirty Years' War.", "html": "1622 - The <a href=\"https://wikipedia.org/wiki/Battle_of_H%C3%B6chst\" title=\"Battle of Höchst\">Battle of Höchst</a> takes place during the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_H%C3%B6chst\" title=\"Battle of Höchst\">Battle of Höchst</a> takes place during the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "links": [{"title": "Battle of Höchst", "link": "https://wikipedia.org/wiki/Battle_of_H%C3%B6chst"}, {"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}]}, {"year": "1631", "text": "The Sack of Baltimore: The Irish village of Baltimore is attacked by Barbary slave traders.", "html": "1631 - The <a href=\"https://wikipedia.org/wiki/Sack_of_Baltimore\" title=\"Sack of Baltimore\">Sack of Baltimore</a>: The Irish village of <a href=\"https://wikipedia.org/wiki/Baltimore,_County_Cork\" title=\"Baltimore, County Cork\">Baltimore</a> is attacked by <a href=\"https://wikipedia.org/wiki/Barbary_slave_trade\" title=\"Barbary slave trade\">Barbary slave traders</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sack_of_Baltimore\" title=\"Sack of Baltimore\">Sack of Baltimore</a>: The Irish village of <a href=\"https://wikipedia.org/wiki/Baltimore,_County_Cork\" title=\"Baltimore, County Cork\">Baltimore</a> is attacked by <a href=\"https://wikipedia.org/wiki/Barbary_slave_trade\" title=\"Barbary slave trade\">Barbary slave traders</a>.", "links": [{"title": "Sack of Baltimore", "link": "https://wikipedia.org/wiki/Sack_of_Baltimore"}, {"title": "Baltimore, County Cork", "link": "https://wikipedia.org/wiki/Baltimore,_County_Cork"}, {"title": "Barbary slave trade", "link": "https://wikipedia.org/wiki/Barbary_slave_trade"}]}, {"year": "1652", "text": "<PERSON><PERSON><PERSON><PERSON> is appointed Grand Vizier of the Ottoman Empire.", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/Grand_Vizier\" class=\"mw-redirect\" title=\"Grand Vizier\">Grand Vizier</a> of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/Grand_Vizier\" class=\"mw-redirect\" title=\"Grand Vizier\">Grand Vizier</a> of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Grand Vizier", "link": "https://wikipedia.org/wiki/Grand_Vizier"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1685", "text": "Monmouth Rebellion: <PERSON>, 1st Duke of Monmouth declares himself King of England at Bridgwater.", "html": "1685 - <a href=\"https://wikipedia.org/wiki/Monmouth_Rebellion\" title=\"Monmouth Rebellion\">Monmouth Rebellion</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Monmouth\" title=\"<PERSON>, 1st Duke of Monmouth\"><PERSON>, 1st Duke of Monmouth</a> declares himself King of England at <a href=\"https://wikipedia.org/wiki/Bridgwater\" title=\"Bridgwater\">Bridgwater</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Monmouth_Rebellion\" title=\"Monmouth Rebellion\">Monmouth Rebellion</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Monmouth\" title=\"<PERSON>, 1st Duke of Monmouth\"><PERSON>, 1st Duke of Monmouth</a> declares himself King of England at <a href=\"https://wikipedia.org/wiki/Bridgwater\" title=\"Bridgwater\">Bridgwater</a>.", "links": [{"title": "Monmouth Rebellion", "link": "https://wikipedia.org/wiki/Monmouth_Rebellion"}, {"title": "<PERSON>, 1st Duke of Monmouth", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Monmouth"}, {"title": "Bridgwater", "link": "https://wikipedia.org/wiki/Bridgwater"}]}, {"year": "1756", "text": "A British garrison is imprisoned in the Black Hole of Calcutta.", "html": "1756 - A <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> <a href=\"https://wikipedia.org/wiki/Garrison\" title=\"Garrison\">garrison</a> is imprisoned in the <a href=\"https://wikipedia.org/wiki/Black_Hole_of_Calcutta\" title=\"Black Hole of Calcutta\">Black Hole of Calcutta</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> <a href=\"https://wikipedia.org/wiki/Garrison\" title=\"Garrison\">garrison</a> is imprisoned in the <a href=\"https://wikipedia.org/wiki/Black_Hole_of_Calcutta\" title=\"Black Hole of Calcutta\">Black Hole of Calcutta</a>.", "links": [{"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "Garrison", "link": "https://wikipedia.org/wiki/Garrison"}, {"title": "Black Hole of Calcutta", "link": "https://wikipedia.org/wiki/Black_Hole_of_Calcutta"}]}, {"year": "1782", "text": "The U.S. Congress adopts the Great Seal of the United States.", "html": "1782 - The <a href=\"https://wikipedia.org/wiki/Congress_of_the_Confederation\" title=\"Congress of the Confederation\">U.S. Congress</a> adopts the <a href=\"https://wikipedia.org/wiki/Great_Seal_of_the_United_States\" title=\"Great Seal of the United States\">Great Seal of the United States</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Congress_of_the_Confederation\" title=\"Congress of the Confederation\">U.S. Congress</a> adopts the <a href=\"https://wikipedia.org/wiki/Great_Seal_of_the_United_States\" title=\"Great Seal of the United States\">Great Seal of the United States</a>.", "links": [{"title": "Congress of the Confederation", "link": "https://wikipedia.org/wiki/Congress_of_the_Confederation"}, {"title": "Great Seal of the United States", "link": "https://wikipedia.org/wiki/Great_Seal_of_the_United_States"}]}, {"year": "1787", "text": "<PERSON> moves at the Federal Convention to call the government the 'United States'.", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> moves at the <a href=\"https://wikipedia.org/wiki/Federal_Convention\" class=\"mw-redirect\" title=\"Federal Convention\">Federal Convention</a> to call the government the 'United States'.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> moves at the <a href=\"https://wikipedia.org/wiki/Federal_Convention\" class=\"mw-redirect\" title=\"Federal Convention\">Federal Convention</a> to call the government the 'United States'.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Convention", "link": "https://wikipedia.org/wiki/Federal_Convention"}]}, {"year": "1789", "text": "Deputies of the French Third Estate take the Tennis Court Oath.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/Deputy_(legislator)\" class=\"mw-redirect\" title=\"Deputy (legislator)\">Deputies</a> of the <a href=\"https://wikipedia.org/wiki/History_of_France\" title=\"History of France\">French</a> <a href=\"https://wikipedia.org/wiki/Estates-General_of_1789\" class=\"mw-redirect\" title=\"Estates-General of 1789\">Third Estate</a> take the <a href=\"https://wikipedia.org/wiki/Tennis_Court_Oath\" title=\"Tennis Court Oath\">Tennis Court Oath</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deputy_(legislator)\" class=\"mw-redirect\" title=\"Deputy (legislator)\">Deputies</a> of the <a href=\"https://wikipedia.org/wiki/History_of_France\" title=\"History of France\">French</a> <a href=\"https://wikipedia.org/wiki/Estates-General_of_1789\" class=\"mw-redirect\" title=\"Estates-General of 1789\">Third Estate</a> take the <a href=\"https://wikipedia.org/wiki/Tennis_Court_Oath\" title=\"Tennis Court Oath\">Tennis Court Oath</a>.", "links": [{"title": "Deputy (legislator)", "link": "https://wikipedia.org/wiki/Deputy_(legislator)"}, {"title": "History of France", "link": "https://wikipedia.org/wiki/History_of_France"}, {"title": "Estates-General of 1789", "link": "https://wikipedia.org/wiki/Estates-General_of_1789"}, {"title": "Tennis Court Oath", "link": "https://wikipedia.org/wiki/Tennis_Court_Oath"}]}, {"year": "1791", "text": "King <PERSON>, disguised as a valet, and the French royal family attempt to flee Paris during the French Revolution.", "html": "1791 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_XVI\" title=\"<PERSON> XVI\"><PERSON></a>, disguised as a valet, and the French royal family attempt to <a href=\"https://wikipedia.org/wiki/Flight_to_Varennes\" title=\"Flight to Varennes\">flee Paris</a> during the French Revolution.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis XVI\"><PERSON></a>, disguised as a valet, and the French royal family attempt to <a href=\"https://wikipedia.org/wiki/Flight_to_Varennes\" title=\"Flight to Varennes\">flee Paris</a> during the French Revolution.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Flight to Varennes", "link": "https://wikipedia.org/wiki/Flight_to_Varennes"}]}, {"year": "1819", "text": "The U.S. vessel SS Savannah arrives at Liverpool, United Kingdom. It is the first steam-propelled vessel to cross the Atlantic, although most of the journey is made under sail.", "html": "1819 - The U.S. vessel <a href=\"https://wikipedia.org/wiki/SS_Savannah\" title=\"SS Savannah\">SS <i>Savannah</i></a> arrives at <a href=\"https://wikipedia.org/wiki/Liverpool\" title=\"Liverpool\">Liverpool</a>, United Kingdom. It is the first steam-propelled vessel to cross the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic</a>, although most of the journey is made under sail.", "no_year_html": "The U.S. vessel <a href=\"https://wikipedia.org/wiki/SS_Savannah\" title=\"SS Savannah\">SS <i>Savannah</i></a> arrives at <a href=\"https://wikipedia.org/wiki/Liverpool\" title=\"Liverpool\">Liverpool</a>, United Kingdom. It is the first steam-propelled vessel to cross the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic</a>, although most of the journey is made under sail.", "links": [{"title": "SS Savannah", "link": "https://wikipedia.org/wiki/SS_Savannah"}, {"title": "Liverpool", "link": "https://wikipedia.org/wiki/Liverpool"}, {"title": "Atlantic Ocean", "link": "https://wikipedia.org/wiki/Atlantic_Ocean"}]}, {"year": "1837", "text": "King <PERSON> dies, and is succeeded by his niece, <PERSON>.", "html": "1837 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" title=\"William IV\"><PERSON> IV</a> dies, and is succeeded by his niece, <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Victoria</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William IV\"><PERSON></a> dies, and is succeeded by his niece, <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Victoria</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}]}, {"year": "1840", "text": "<PERSON> receives the patent for the telegraph.", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives the patent for the <a href=\"https://wikipedia.org/wiki/Telegraph\" class=\"mw-redirect\" title=\"Telegraph\">telegraph</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives the patent for the <a href=\"https://wikipedia.org/wiki/Telegraph\" class=\"mw-redirect\" title=\"Telegraph\">telegraph</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Telegraph", "link": "https://wikipedia.org/wiki/Telegraph"}]}, {"year": "1862", "text": "<PERSON><PERSON>, the Prime Minister of Romania, is assassinated.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Barbu_Catargiu\" title=\"Barbu Catargiu\">Barbu Catargiu</a>, the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a>, is assassinated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barbu_Catargiu\" title=\"Barbu Catargiu\">Barbu Catargiu</a>, the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a>, is assassinated.", "links": [{"title": "Barbu Catargiu", "link": "https://wikipedia.org/wiki/Barbu_Catargiu"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "1863", "text": "American Civil War: West Virginia is admitted as the 35th U.S. state.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/West_Virginia\" title=\"West Virginia\">West Virginia</a> is admitted as the 35th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/West_Virginia\" title=\"West Virginia\">West Virginia</a> is admitted as the 35th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "West Virginia", "link": "https://wikipedia.org/wiki/West_Virginia"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1877", "text": "<PERSON> installs the world's first commercial telephone service in Hamilton, Ontario, Canada.", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> installs the world's first commercial <a href=\"https://wikipedia.org/wiki/Telephone\" title=\"Telephone\">telephone</a> service in <a href=\"https://wikipedia.org/wiki/Hamilton,_Ontario\" title=\"Hamilton, Ontario\">Hamilton, Ontario</a>, Canada.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> installs the world's first commercial <a href=\"https://wikipedia.org/wiki/Telephone\" title=\"Telephone\">telephone</a> service in <a href=\"https://wikipedia.org/wiki/Hamilton,_Ontario\" title=\"Hamilton, Ontario\">Hamilton, Ontario</a>, Canada.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Telephone", "link": "https://wikipedia.org/wiki/Telephone"}, {"title": "Hamilton, Ontario", "link": "https://wikipedia.org/wiki/Hamilton,_Ontario"}]}, {"year": "1893", "text": "<PERSON> is acquitted of the murders of her father and stepmother.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is acquitted of the murders of her father and stepmother.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is acquitted of the murders of her father and stepmother.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "The Kiel Canal, crossing the base of the Jutland peninsula and the busiest artificial waterway in the world, is officially opened.", "html": "1895 - The <a href=\"https://wikipedia.org/wiki/Kiel_Canal\" title=\"Kiel Canal\">Kiel Canal</a>, crossing the base of the <a href=\"https://wikipedia.org/wiki/Jutland\" title=\"Jutland\">Jutland</a> peninsula and the busiest artificial waterway in the world, is officially opened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kiel_Canal\" title=\"Kiel Canal\">Kiel Canal</a>, crossing the base of the <a href=\"https://wikipedia.org/wiki/Jutland\" title=\"Jutland\">Jutland</a> peninsula and the busiest artificial waterway in the world, is officially opened.", "links": [{"title": "Kiel Canal", "link": "https://wikipedia.org/wiki/Kiel_Canal"}, {"title": "Jutland", "link": "https://wikipedia.org/wiki/Jutland"}]}, {"year": "1900", "text": "Boxer Rebellion: The Imperial Chinese Army begins a 55-day siege of the Legation Quarter in Beijing, China.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Boxer_Rebellion\" title=\"Boxer Rebellion\">Boxer Rebellion</a>: The <a href=\"https://wikipedia.org/wiki/Imperial_Chinese_Army\" class=\"mw-redirect\" title=\"Imperial Chinese Army\">Imperial Chinese Army</a> begins a <a href=\"https://wikipedia.org/wiki/Siege_of_the_International_Legations\" title=\"Siege of the International Legations\">55-day siege</a> of the <a href=\"https://wikipedia.org/wiki/Beijing_Legation_Quarter\" class=\"mw-redirect\" title=\"Beijing Legation Quarter\">Legation Quarter</a> in Beijing, China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boxer_Rebellion\" title=\"Boxer Rebellion\">Boxer Rebellion</a>: The <a href=\"https://wikipedia.org/wiki/Imperial_Chinese_Army\" class=\"mw-redirect\" title=\"Imperial Chinese Army\">Imperial Chinese Army</a> begins a <a href=\"https://wikipedia.org/wiki/Siege_of_the_International_Legations\" title=\"Siege of the International Legations\">55-day siege</a> of the <a href=\"https://wikipedia.org/wiki/Beijing_Legation_Quarter\" class=\"mw-redirect\" title=\"Beijing Legation Quarter\">Legation Quarter</a> in Beijing, China.", "links": [{"title": "Boxer Rebellion", "link": "https://wikipedia.org/wiki/Boxer_Rebellion"}, {"title": "Imperial Chinese Army", "link": "https://wikipedia.org/wiki/Imperial_Chinese_Army"}, {"title": "Siege of the International Legations", "link": "https://wikipedia.org/wiki/Siege_of_the_International_Legations"}, {"title": "Beijing Legation Quarter", "link": "https://wikipedia.org/wiki/Beijing_Legation_Quarter"}]}, {"year": "1900", "text": "Baron <PERSON>, leader of the Russian Polar Expedition of 1900, departs Saint Petersburg in Russia on the explorer ship <PERSON><PERSON><PERSON>, never to return.", "html": "1900 - Baron <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Russian_polar_expedition_of_1900%E2%80%9302\" class=\"mw-redirect\" title=\"Russian polar expedition of 1900-02\">Russian Polar Expedition of 1900</a>, departs <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a> in Russia on the explorer ship <i><a href=\"https://wikipedia.org/wiki/Zary<PERSON>_(polar_ship)\" title=\"<PERSON><PERSON><PERSON> (polar ship)\">Z<PERSON><PERSON></a></i>, never to return.", "no_year_html": "Baron <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Russian_polar_expedition_of_1900%E2%80%9302\" class=\"mw-redirect\" title=\"Russian polar expedition of 1900-02\">Russian Polar Expedition of 1900</a>, departs <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a> in Russia on the explorer ship <i><a href=\"https://wikipedia.org/wiki/Zary<PERSON>_(polar_ship)\" title=\"<PERSON><PERSON><PERSON> (polar ship)\">Zary<PERSON></a></i>, never to return.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Russian polar expedition of 1900-02", "link": "https://wikipedia.org/wiki/Russian_polar_expedition_of_1900%E2%80%9302"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}, {"title": "<PERSON><PERSON><PERSON> (polar ship)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(polar_ship)"}]}, {"year": "1921", "text": "Workers of Buckingham and Carnatic Mills in the city of Chennai, India, begin a four-month strike.", "html": "1921 - Workers of <a href=\"https://wikipedia.org/wiki/Buckingham_and_Carnatic_Mills\" title=\"Buckingham and Carnatic Mills\">Buckingham and Carnatic Mills</a> in the city of <a href=\"https://wikipedia.org/wiki/Chennai\" title=\"Chennai\">Chennai</a>, India, begin a <a href=\"https://wikipedia.org/wiki/1921_Buckingham_and_Carnatic_Mills_Strike\" class=\"mw-redirect\" title=\"1921 Buckingham and Carnatic Mills Strike\">four-month strike</a>.", "no_year_html": "Workers of <a href=\"https://wikipedia.org/wiki/Buckingham_and_Carnatic_Mills\" title=\"Buckingham and Carnatic Mills\">Buckingham and Carnatic Mills</a> in the city of <a href=\"https://wikipedia.org/wiki/Chennai\" title=\"Chennai\">Chennai</a>, India, begin a <a href=\"https://wikipedia.org/wiki/1921_Buckingham_and_Carnatic_Mills_Strike\" class=\"mw-redirect\" title=\"1921 Buckingham and Carnatic Mills Strike\">four-month strike</a>.", "links": [{"title": "Buckingham and Carnatic Mills", "link": "https://wikipedia.org/wiki/Buckingham_and_Carnatic_Mills"}, {"title": "Chennai", "link": "https://wikipedia.org/wiki/Chennai"}, {"title": "1921 Buckingham and Carnatic Mills Strike", "link": "https://wikipedia.org/wiki/1921_Buckingham_and_Carnatic_Mills_Strike"}]}, {"year": "1926", "text": "The 28th International Eucharistic Congress begins in Chicago, with over 250,000 spectators attending the opening procession.", "html": "1926 - The <a href=\"https://wikipedia.org/wiki/28th_International_Eucharistic_Congress\" title=\"28th International Eucharistic Congress\">28th International Eucharistic Congress</a> begins in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, with over 250,000 spectators attending the opening procession.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/28th_International_Eucharistic_Congress\" title=\"28th International Eucharistic Congress\">28th International Eucharistic Congress</a> begins in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, with over 250,000 spectators attending the opening procession.", "links": [{"title": "28th International Eucharistic Congress", "link": "https://wikipedia.org/wiki/28th_International_Eucharistic_Congress"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}]}, {"year": "1942", "text": "The Holocaust: <PERSON><PERSON><PERSON><PERSON> and three others, dressed as members of the SS-Totenkopfverbände, steal an SS staff car and escape from the Auschwitz concentration camp.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and three others, dressed as members of the <a href=\"https://wikipedia.org/wiki/SS-Totenkopfverb%C3%A4nde\" title=\"SS-Totenkopfverbände\">SS-Totenkopfverbände</a>, steal an <a href=\"https://wikipedia.org/wiki/Schutzstaffel\" title=\"Schutzstaffel\">SS</a> staff car and escape from the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and three others, dressed as members of the <a href=\"https://wikipedia.org/wiki/SS-Totenkopfverb%C3%A4nde\" title=\"SS-Totenkopfverbände\">SS-Totenkopfverbände</a>, steal an <a href=\"https://wikipedia.org/wiki/Schutzstaffel\" title=\"Schutzstaffel\">SS</a> staff car and escape from the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "SS-Totenkopfverbände", "link": "https://wikipedia.org/wiki/SS-Totenkopfverb%C3%A4nde"}, {"title": "<PERSON><PERSON><PERSON>sta<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>tzstaffel"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}]}, {"year": "1943", "text": "The Detroit race riot breaks out and continues for three more days.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/Detroit_race_riot_of_1943\" class=\"mw-redirect\" title=\"Detroit race riot of 1943\">Detroit race riot</a> breaks out and continues for three more days.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Detroit_race_riot_of_1943\" class=\"mw-redirect\" title=\"Detroit race riot of 1943\">Detroit race riot</a> breaks out and continues for three more days.", "links": [{"title": "Detroit race riot of 1943", "link": "https://wikipedia.org/wiki/Detroit_race_riot_of_1943"}]}, {"year": "1943", "text": "World War II: The Royal Air Force launches Operation Bellicose, the first shuttle bombing raid of the war. Avro Lancaster bombers damage the V-2 rocket production facilities at the Zeppelin Works while en route to an air base in Algeria.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> launches <a href=\"https://wikipedia.org/wiki/Operation_Bellicose\" title=\"Operation Bellicose\">Operation Bellicose</a>, the first <a href=\"https://wikipedia.org/wiki/Shuttle_bombing\" title=\"Shuttle bombing\">shuttle bombing</a> raid of the war. <a href=\"https://wikipedia.org/wiki/Avro_Lancaster\" title=\"Avro Lancaster\">Avro Lancaster</a> bombers damage the <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> production facilities at the <a href=\"https://wikipedia.org/wiki/Luftschiffbau_Zeppelin\" title=\"Luftschiffbau Zeppelin\">Zeppelin Works</a> while en route to an air base in Algeria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> launches <a href=\"https://wikipedia.org/wiki/Operation_Bellicose\" title=\"Operation Bellicose\">Operation Bellicose</a>, the first <a href=\"https://wikipedia.org/wiki/Shuttle_bombing\" title=\"Shuttle bombing\">shuttle bombing</a> raid of the war. <a href=\"https://wikipedia.org/wiki/Avro_Lancaster\" title=\"Avro Lancaster\">Avro Lancaster</a> bombers damage the <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> production facilities at the <a href=\"https://wikipedia.org/wiki/Luftschiffbau_Zeppelin\" title=\"Luftschiffbau Zeppelin\">Zeppelin Works</a> while en route to an air base in Algeria.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}, {"title": "Operation Bellicose", "link": "https://wikipedia.org/wiki/Operation_Bellicose"}, {"title": "Shuttle bombing", "link": "https://wikipedia.org/wiki/Shuttle_bombing"}, {"title": "Avro Lancaster", "link": "https://wikipedia.org/wiki/Avro_Lancaster"}, {"title": "V-2 rocket", "link": "https://wikipedia.org/wiki/V-2_rocket"}, {"title": "Luftschiffbau Zeppelin", "link": "https://wikipedia.org/wiki/Luftschiffbau_Zeppelin"}]}, {"year": "1944", "text": "World War II: The Battle of the Philippine Sea concludes with a decisive U.S. naval victory. The lopsided naval air battle is also known as the \"Great Marianas Turkey Shoot\".", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Philippine_Sea\" title=\"Battle of the Philippine Sea\">Battle of the Philippine Sea</a> concludes with a decisive U.S. naval victory. The lopsided naval air battle is also known as the \"Great Marianas Turkey Shoot\".", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Philippine_Sea\" title=\"Battle of the Philippine Sea\">Battle of the Philippine Sea</a> concludes with a decisive U.S. naval victory. The lopsided naval air battle is also known as the \"Great Marianas Turkey Shoot\".", "links": [{"title": "Battle of the Philippine Sea", "link": "https://wikipedia.org/wiki/Battle_of_the_Philippine_Sea"}]}, {"year": "1944", "text": "World War II: During the Continuation War, the Soviet Union demands unconditional surrender from Finland during the beginning of partially successful Vyborg-Petrozavodsk Offensive. The Finnish government refuses.", "html": "1944 - World War II: During the <a href=\"https://wikipedia.org/wiki/Continuation_War\" title=\"Continuation War\">Continuation War</a>, the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> demands unconditional surrender from Finland during the beginning of partially successful <a href=\"https://wikipedia.org/wiki/Vyborg%E2%80%93Petrozavodsk_Offensive\" class=\"mw-redirect\" title=\"Vyborg-Petrozavodsk Offensive\">Vyborg-Petrozavodsk Offensive</a>. The Finnish government refuses.", "no_year_html": "World War II: During the <a href=\"https://wikipedia.org/wiki/Continuation_War\" title=\"Continuation War\">Continuation War</a>, the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> demands unconditional surrender from Finland during the beginning of partially successful <a href=\"https://wikipedia.org/wiki/Vyborg%E2%80%93Petrozavodsk_Offensive\" class=\"mw-redirect\" title=\"Vyborg-Petrozavodsk Offensive\">Vyborg-Petrozavodsk Offensive</a>. The Finnish government refuses.", "links": [{"title": "Continuation War", "link": "https://wikipedia.org/wiki/Continuation_War"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Vyborg-Petrozavodsk Offensive", "link": "https://wikipedia.org/wiki/Vyborg%E2%80%93Petrozavodsk_Offensive"}]}, {"year": "1944", "text": "The experimental MW 18014 V-2 rocket reaches an altitude of 176 km, becoming the first man-made object to reach outer space.", "html": "1944 - The experimental <a href=\"https://wikipedia.org/wiki/MW_18014\" title=\"MW 18014\">MW 18014</a> <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> reaches an altitude of 176 km, becoming the first man-made object to reach outer space.", "no_year_html": "The experimental <a href=\"https://wikipedia.org/wiki/MW_18014\" title=\"MW 18014\">MW 18014</a> <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> reaches an altitude of 176 km, becoming the first man-made object to reach outer space.", "links": [{"title": "MW 18014", "link": "https://wikipedia.org/wiki/MW_18014"}, {"title": "V-2 rocket", "link": "https://wikipedia.org/wiki/V-2_rocket"}]}, {"year": "1945", "text": "The United States Secretary of State approves the transfer of <PERSON><PERSON><PERSON> and his team of Nazi rocket scientists to the U.S. under Operation Paperclip.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> approves the transfer of <a href=\"https://wikipedia.org/wiki/We<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a> and his team of Nazi rocket scientists to the U.S. under <a href=\"https://wikipedia.org/wiki/Operation_Paperclip\" title=\"Operation Paperclip\">Operation Paperclip</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> approves the transfer of <a href=\"https://wikipedia.org/wiki/We<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a> and his team of Nazi rocket scientists to the U.S. under <a href=\"https://wikipedia.org/wiki/Operation_Paperclip\" title=\"Operation Paperclip\">Operation Paperclip</a>.", "links": [{"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}, {"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/We<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Operation Paperclip", "link": "https://wikipedia.org/wiki/Operation_Paperclip"}]}, {"year": "1948", "text": "The Deutsche Mark is introduced in Western Allied-occupied Germany. The Soviet Military Administration in Germany responded by imposing the Berlin Blockade four days later.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Deutsche_Mark\" title=\"Deutsche Mark\">Deutsche Mark</a> is introduced in Western <a href=\"https://wikipedia.org/wiki/Allied-occupied_Germany\" title=\"Allied-occupied Germany\">Allied-occupied Germany</a>. The <a href=\"https://wikipedia.org/wiki/Soviet_Military_Administration_in_Germany\" title=\"Soviet Military Administration in Germany\">Soviet Military Administration in Germany</a> responded by imposing the <a href=\"https://wikipedia.org/wiki/Berlin_Blockade\" title=\"Berlin Blockade\">Berlin Blockade</a> four days later.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Deutsche_Mark\" title=\"Deutsche Mark\">Deutsche Mark</a> is introduced in Western <a href=\"https://wikipedia.org/wiki/Allied-occupied_Germany\" title=\"Allied-occupied Germany\">Allied-occupied Germany</a>. The <a href=\"https://wikipedia.org/wiki/Soviet_Military_Administration_in_Germany\" title=\"Soviet Military Administration in Germany\">Soviet Military Administration in Germany</a> responded by imposing the <a href=\"https://wikipedia.org/wiki/Berlin_Blockade\" title=\"Berlin Blockade\">Berlin Blockade</a> four days later.", "links": [{"title": "Deutsche Mark", "link": "https://wikipedia.org/wiki/Deutsche_Mark"}, {"title": "Allied-occupied Germany", "link": "https://wikipedia.org/wiki/Allied-occupied_Germany"}, {"title": "Soviet Military Administration in Germany", "link": "https://wikipedia.org/wiki/Soviet_Military_Administration_in_Germany"}, {"title": "Berlin Blockade", "link": "https://wikipedia.org/wiki/Berlin_Blockade"}]}, {"year": "1956", "text": "A Venezuelan Super-Constellation crashes in the Atlantic Ocean off Asbury Park, New Jersey, killing 74 people.", "html": "1956 - A <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuelan</a> <a href=\"https://wikipedia.org/wiki/Lockheed_Constellation\" title=\"Lockheed Constellation\">Super-Constellation</a> <a href=\"https://wikipedia.org/wiki/Linea_Aeropostal_Venezolana_Flight_253_(June_1956)\" title=\"Linea Aeropostal Venezolana Flight 253 (June 1956)\">crashes</a> in the Atlantic Ocean off <a href=\"https://wikipedia.org/wiki/Asbury_Park,_New_Jersey\" title=\"Asbury Park, New Jersey\">Asbury Park, New Jersey</a>, killing 74 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuelan</a> <a href=\"https://wikipedia.org/wiki/Lockheed_Constellation\" title=\"Lockheed Constellation\">Super-Constellation</a> <a href=\"https://wikipedia.org/wiki/Linea_Aeropostal_Venezolana_Flight_253_(June_1956)\" title=\"Linea Aeropostal Venezolana Flight 253 (June 1956)\">crashes</a> in the Atlantic Ocean off <a href=\"https://wikipedia.org/wiki/Asbury_Park,_New_Jersey\" title=\"Asbury Park, New Jersey\">Asbury Park, New Jersey</a>, killing 74 people.", "links": [{"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}, {"title": "Lockheed Constellation", "link": "https://wikipedia.org/wiki/Lockheed_Constellation"}, {"title": "Linea Aeropostal Venezolana Flight 253 (June 1956)", "link": "https://wikipedia.org/wiki/Linea_Aeropostal_Venezolana_Flight_253_(June_1956)"}, {"title": "Asbury Park, New Jersey", "link": "https://wikipedia.org/wiki/Asbury_Park,_New_Jersey"}]}, {"year": "1959", "text": "A rare June hurricane strikes Canada's Gulf of St. Lawrence killing 35.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/1959_Escuminac_disaster\" title=\"1959 Escuminac disaster\">A rare June hurricane</a> strikes Canada's <a href=\"https://wikipedia.org/wiki/Gulf_of_St._Lawrence\" title=\"Gulf of St. Lawrence\">Gulf of St. Lawrence</a> killing 35.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1959_Escuminac_disaster\" title=\"1959 Escuminac disaster\">A rare June hurricane</a> strikes Canada's <a href=\"https://wikipedia.org/wiki/Gulf_of_St._Lawrence\" title=\"Gulf of St. Lawrence\">Gulf of St. Lawrence</a> killing 35.", "links": [{"title": "1959 Escuminac disaster", "link": "https://wikipedia.org/wiki/1959_Escuminac_disaster"}, {"title": "Gulf of St. Lawrence", "link": "https://wikipedia.org/wiki/Gulf_of_St._Lawrence"}]}, {"year": "1960", "text": "The Mali Federation gains independence from France (it later splits into Mali and Senegal).", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Mali_Federation\" title=\"Mali Federation\">Mali Federation</a> gains independence from France (it later splits into <a href=\"https://wikipedia.org/wiki/Mali\" title=\"Mali\">Mali</a> and <a href=\"https://wikipedia.org/wiki/Senegal\" title=\"Senegal\">Senegal</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mali_Federation\" title=\"Mali Federation\">Mali Federation</a> gains independence from France (it later splits into <a href=\"https://wikipedia.org/wiki/Mali\" title=\"Mali\">Mali</a> and <a href=\"https://wikipedia.org/wiki/Senegal\" title=\"Senegal\">Senegal</a>).", "links": [{"title": "Mali Federation", "link": "https://wikipedia.org/wiki/Mali_Federation"}, {"title": "Mali", "link": "https://wikipedia.org/wiki/Mali"}, {"title": "Senegal", "link": "https://wikipedia.org/wiki/Senegal"}]}, {"year": "1963", "text": "Following the Cuban Missile Crisis, the Soviet Union and the United States sign an agreement to establish the so-called \"red telephone\" link between Washington, D.C., and Moscow.", "html": "1963 - Following the <a href=\"https://wikipedia.org/wiki/Cuban_Missile_Crisis\" title=\"Cuban Missile Crisis\">Cuban Missile Crisis</a>, the Soviet Union and the United States sign an agreement to establish the so-called \"<a href=\"https://wikipedia.org/wiki/Moscow%E2%80%93Washington_hotline\" title=\"Moscow-Washington hotline\">red telephone</a>\" link between <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, and <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>.", "no_year_html": "Following the <a href=\"https://wikipedia.org/wiki/Cuban_Missile_Crisis\" title=\"Cuban Missile Crisis\">Cuban Missile Crisis</a>, the Soviet Union and the United States sign an agreement to establish the so-called \"<a href=\"https://wikipedia.org/wiki/Moscow%E2%80%93Washington_hotline\" title=\"Moscow-Washington hotline\">red telephone</a>\" link between <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, and <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>.", "links": [{"title": "Cuban Missile Crisis", "link": "https://wikipedia.org/wiki/Cuban_Missile_Crisis"}, {"title": "Moscow-Washington hotline", "link": "https://wikipedia.org/wiki/Moscow%E2%80%93Washington_hotline"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}]}, {"year": "1964", "text": "A Curtiss C-46 Commando crashes in the Shengang District of Taiwan, killing 57 people.", "html": "1964 - A <a href=\"https://wikipedia.org/wiki/Curtiss_C-46_Commando\" title=\"Curtiss C-46 Commando\">Curtiss C-46 Commando</a> <a href=\"https://wikipedia.org/wiki/Civil_Air_Transport_Flight_106\" title=\"Civil Air Transport Flight 106\">crashes</a> in the <a href=\"https://wikipedia.org/wiki/Shengang_District\" title=\"Shengang District\">Shengang District</a> of Taiwan, killing 57 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Curtiss_C-46_Commando\" title=\"Curtiss C-46 Commando\">Curtiss C-46 Commando</a> <a href=\"https://wikipedia.org/wiki/Civil_Air_Transport_Flight_106\" title=\"Civil Air Transport Flight 106\">crashes</a> in the <a href=\"https://wikipedia.org/wiki/Shengang_District\" title=\"Shengang District\">Shengang District</a> of Taiwan, killing 57 people.", "links": [{"title": "Curtiss C-46 Commando", "link": "https://wikipedia.org/wiki/Curtiss_C-46_Commando"}, {"title": "Civil Air Transport Flight 106", "link": "https://wikipedia.org/wiki/Civil_Air_Transport_Flight_106"}, {"title": "Shengang District", "link": "https://wikipedia.org/wiki/Shengang_District"}]}, {"year": "1972", "text": "Watergate scandal: An .mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}18+1⁄2-minute gap appears in the tape recording of the conversations between U.S. President <PERSON> and his advisers regarding the recent arrests of his operatives while breaking into the Watergate complex.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: An <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">18<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span>-minute gap appears in the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_tapes\" title=\"<PERSON> tapes\">tape recording of the conversations</a> between U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON> <PERSON></a> and his advisers regarding the recent arrests of his operatives while breaking into the <a href=\"https://wikipedia.org/wiki/Watergate_complex\" title=\"Watergate complex\">Watergate complex</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: An <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">18<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span>-minute gap appears in the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_tapes\" title=\"<PERSON> House tapes\">tape recording of the conversations</a> between U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON> <PERSON></a> and his advisers regarding the recent arrests of his operatives while breaking into the <a href=\"https://wikipedia.org/wiki/Watergate_complex\" title=\"Watergate complex\">Watergate complex</a>.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "<PERSON> White House tapes", "link": "https://wikipedia.org/wiki/<PERSON>_White_House_tapes"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Watergate complex", "link": "https://wikipedia.org/wiki/Watergate_complex"}]}, {"year": "1973", "text": "Snipers fire upon left-wing Peronists in Buenos Aires, Argentina, in what is known as the Ezeiza massacre. At least 13 are killed and more than 300 are injured.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Sniper\" title=\"Sniper\">Snipers</a> fire upon left-wing <a href=\"https://wikipedia.org/wiki/Peronism\" title=\"Peronism\">Peronists</a> in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, in what is known as the <a href=\"https://wikipedia.org/wiki/Ezeiza_massacre\" title=\"Ezeiza massacre\">Ezeiza massacre</a>. At least 13 are killed and more than 300 are injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sniper\" title=\"Sniper\">Snipers</a> fire upon left-wing <a href=\"https://wikipedia.org/wiki/Peronism\" title=\"Peronism\">Peronists</a> in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, in what is known as the <a href=\"https://wikipedia.org/wiki/Ezeiza_massacre\" title=\"Ezeiza massacre\">Ezeiza massacre</a>. At least 13 are killed and more than 300 are injured.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sniper"}, {"title": "Peronism", "link": "https://wikipedia.org/wiki/Peronism"}, {"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "Ezeiza massacre", "link": "https://wikipedia.org/wiki/E<PERSON>iza_massacre"}]}, {"year": "1973", "text": "Aeroméxico Flight 229 crashes on approach to Licenciado Gustavo Díaz Ordaz International Airport, killing all 27 people on board.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Aerom%C3%A9xico_Flight_229\" title=\"Aeroméxico Flight 229\">Aeroméxico Flight 229</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Licenciado_Gustavo_D%C3%ADaz_Ordaz_International_Airport\" title=\"Licenciado Gustavo Díaz Ordaz International Airport\">Licenciado Gustavo Díaz Ordaz International Airport</a>, killing all 27 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aerom%C3%A9xico_Flight_229\" title=\"Aeroméxico Flight 229\">Aeroméxico Flight 229</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Licenciado_Gustavo_D%C3%ADaz_Ordaz_International_Airport\" title=\"Licenciado Gustavo Díaz Ordaz International Airport\">Licenciado Gustavo Díaz Ordaz International Airport</a>, killing all 27 people on board.", "links": [{"title": "Aeroméxico Flight 229", "link": "https://wikipedia.org/wiki/Aerom%C3%A9xico_Flight_229"}, {"title": "Licenciado Gustavo <PERSON> Ordaz International Airport", "link": "https://wikipedia.org/wiki/Licenciado_Gustavo_D%C3%ADaz_Ordaz_International_Airport"}]}, {"year": "1975", "text": "The film <PERSON><PERSON> is released in the United States, becoming the highest-grossing film of that time and starting the trend of films known as \"summer blockbusters\".", "html": "1975 - The film <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(film)\" title=\"<PERSON><PERSON> (film)\"><PERSON><PERSON></a></i> is released in the United States, becoming the highest-grossing film of that time and starting the trend of films known as \"<a href=\"https://wikipedia.org/wiki/Blockbuster_(entertainment)\" title=\"Blockbuster (entertainment)\">summer blockbusters</a>\".", "no_year_html": "The film <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(film)\" title=\"<PERSON><PERSON> (film)\"><PERSON><PERSON></a></i> is released in the United States, becoming the highest-grossing film of that time and starting the trend of films known as \"<a href=\"https://wikipedia.org/wiki/Blockbuster_(entertainment)\" title=\"Blockbuster (entertainment)\">summer blockbusters</a>\".", "links": [{"title": "<PERSON><PERSON> (film)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(film)"}, {"title": "Blockbuster (entertainment)", "link": "https://wikipedia.org/wiki/Blockbuster_(entertainment)"}]}, {"year": "1979", "text": "ABC News correspondent <PERSON> is shot dead by a Nicaraguan National Guard soldier under the regime of <PERSON><PERSON><PERSON> during the Nicaraguan Revolution. The murder is caught on tape and sparks an international outcry against the regime.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/ABC_News_(United_States)\" title=\"ABC News (United States)\">ABC News</a> correspondent <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a> is shot dead by a Nicaraguan <a href=\"https://wikipedia.org/wiki/National_Guard_(Nicaragua)\" title=\"National Guard (Nicaragua)\">National Guard</a> soldier under the regime of <a href=\"https://wikipedia.org/wiki/Anastasio_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Anastasio Somoza <PERSON>bayle\">Anasta<PERSON><PERSON></a> during the <a href=\"https://wikipedia.org/wiki/Nicaraguan_Revolution\" title=\"Nicaraguan Revolution\">Nicaraguan Revolution</a>. The murder is caught on tape and sparks an international outcry against the regime.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ABC_News_(United_States)\" title=\"ABC News (United States)\">ABC News</a> correspondent <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a> is shot dead by a Nicaraguan <a href=\"https://wikipedia.org/wiki/National_Guard_(Nicaragua)\" title=\"National Guard (Nicaragua)\">National Guard</a> soldier under the regime of <a href=\"https://wikipedia.org/wiki/Anastasio_So<PERSON><PERSON>_<PERSON>\" title=\"Anastasio Somoza <PERSON>bayle\">Anastasio <PERSON><PERSON><PERSON></a> during the <a href=\"https://wikipedia.org/wiki/Nicaraguan_Revolution\" title=\"Nicaraguan Revolution\">Nicaraguan Revolution</a>. The murder is caught on tape and sparks an international outcry against the regime.", "links": [{"title": "ABC News (United States)", "link": "https://wikipedia.org/wiki/ABC_News_(United_States)"}, {"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}, {"title": "National Guard (Nicaragua)", "link": "https://wikipedia.org/wiki/National_Guard_(Nicaragua)"}, {"title": "Anastasio So<PERSON>", "link": "https://wikipedia.org/wiki/Anastasio_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nicaraguan Revolution", "link": "https://wikipedia.org/wiki/Nicaraguan_Revolution"}]}, {"year": "1982", "text": "The International Conference on the Holocaust and Genocide opens in Tel Aviv, despite attempts by the Turkish government to cancel it, as it included presentations on the Armenian genocide.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/International_Conference_on_the_Holocaust_and_Genocide\" title=\"International Conference on the Holocaust and Genocide\">International Conference on the Holocaust and Genocide</a> opens in <a href=\"https://wikipedia.org/wiki/Tel_Aviv\" title=\"Tel Aviv\">Tel Aviv</a>, despite attempts by the Turkish government to cancel it, as it included presentations on the <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Conference_on_the_Holocaust_and_Genocide\" title=\"International Conference on the Holocaust and Genocide\">International Conference on the Holocaust and Genocide</a> opens in <a href=\"https://wikipedia.org/wiki/Tel_Aviv\" title=\"Tel Aviv\">Tel Aviv</a>, despite attempts by the Turkish government to cancel it, as it included presentations on the <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a>.", "links": [{"title": "International Conference on the Holocaust and Genocide", "link": "https://wikipedia.org/wiki/International_Conference_on_the_Holocaust_and_Genocide"}, {"title": "Tel Aviv", "link": "https://wikipedia.org/wiki/Tel_Aviv"}, {"title": "Armenian genocide", "link": "https://wikipedia.org/wiki/Armenian_genocide"}]}, {"year": "1982", "text": "The Argentine Corbeta Uruguay base on Southern Thule surrenders to Royal Marine commandos in the final action of the Falklands War.", "html": "1982 - The Argentine <a href=\"https://wikipedia.org/wiki/Corbeta_Uruguay_base\" title=\"Corbeta Uruguay base\">Corbeta Uruguay base</a> on <a href=\"https://wikipedia.org/wiki/Southern_Thule\" title=\"Southern Thule\">Southern Thule</a> surrenders to Royal Marine commandos in the final action of the <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>.", "no_year_html": "The Argentine <a href=\"https://wikipedia.org/wiki/Corbeta_Uruguay_base\" title=\"Corbeta Uruguay base\">Corbeta Uruguay base</a> on <a href=\"https://wikipedia.org/wiki/Southern_Thule\" title=\"Southern Thule\">Southern Thule</a> surrenders to Royal Marine commandos in the final action of the <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>.", "links": [{"title": "Corbeta Uruguay base", "link": "https://wikipedia.org/wiki/Corbeta_Uruguay_base"}, {"title": "Southern Thule", "link": "https://wikipedia.org/wiki/Southern_Thule"}, {"title": "Falklands War", "link": "https://wikipedia.org/wiki/Falklands_War"}]}, {"year": "1988", "text": "Haitian president <PERSON> is ousted from power in a coup d'état led by Lieutenant General <PERSON>.", "html": "1988 - Haitian president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is ousted from power in a <a href=\"https://wikipedia.org/wiki/June_1988_Haitian_coup_d%27%C3%A9tat\" title=\"June 1988 Haitian coup d'état\">coup d'état</a> led by Lieutenant General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Haitian president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is ousted from power in a <a href=\"https://wikipedia.org/wiki/June_1988_Haitian_coup_d%27%C3%A9tat\" title=\"June 1988 Haitian coup d'état\">coup d'état</a> led by Lieutenant General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "June 1988 Haitian coup d'état", "link": "https://wikipedia.org/wiki/June_1988_Haitian_coup_d%27%C3%A9tat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "Asteroid Eureka is discovered.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Asteroid\" title=\"Asteroid\">Asteroid</a> <a href=\"https://wikipedia.org/wiki/5261_Eureka\" title=\"5261 Eureka\">Eureka</a> is discovered.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asteroid\" title=\"Asteroid\">Asteroid</a> <a href=\"https://wikipedia.org/wiki/5261_Eureka\" title=\"5261 Eureka\">Eureka</a> is discovered.", "links": [{"title": "Asteroid", "link": "https://wikipedia.org/wiki/Asteroid"}, {"title": "5261 Eureka", "link": "https://wikipedia.org/wiki/5261_Eureka"}]}, {"year": "1990", "text": "The 7.4 Mw  Manjil-Rudbar earthquake affects northern Iran with a maximum Mercalli intensity of X (Extreme), killing 35,000-50,000, and injuring 60,000-105,000.", "html": "1990 - The 7.4 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1990_Manjil%E2%80%93Rudbar_earthquake\" title=\"1990 Manjil-Rudbar earthquake\">Manjil-Rudbar earthquake</a> affects northern <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>), killing 35,000-50,000, and injuring 60,000-105,000.", "no_year_html": "The 7.4 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1990_Manjil%E2%80%93Rudbar_earthquake\" title=\"1990 Manjil-Rudbar earthquake\">Manjil-Rudbar earthquake</a> affects northern <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>), killing 35,000-50,000, and injuring 60,000-105,000.", "links": [{"title": "1990 Manjil-Rudbar earthquake", "link": "https://wikipedia.org/wiki/1990_Manjil%E2%80%93Rudbar_earthquake"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1991", "text": "The German Bundestag votes to move seat of government from the former West German capital of Bonn to the present capital of Berlin.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Bundestag\" title=\"Bundestag\">German Bundestag</a> <a href=\"https://wikipedia.org/wiki/Decision_on_the_Capital_of_Germany\" title=\"Decision on the Capital of Germany\">votes</a> to move seat of government from the former <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West German</a> capital of <a href=\"https://wikipedia.org/wiki/Bonn\" title=\"Bonn\">Bonn</a> to the present capital of <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bundestag\" title=\"Bundestag\">German Bundestag</a> <a href=\"https://wikipedia.org/wiki/Decision_on_the_Capital_of_Germany\" title=\"Decision on the Capital of Germany\">votes</a> to move seat of government from the former <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West German</a> capital of <a href=\"https://wikipedia.org/wiki/Bonn\" title=\"Bonn\">Bonn</a> to the present capital of <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a>.", "links": [{"title": "Bundestag", "link": "https://wikipedia.org/wiki/Bundestag"}, {"title": "Decision on the Capital of Germany", "link": "https://wikipedia.org/wiki/Decision_on_the_Capital_of_Germany"}, {"title": "West Germany", "link": "https://wikipedia.org/wiki/West_Germany"}, {"title": "Bonn", "link": "https://wikipedia.org/wiki/Bonn"}, {"title": "Berlin", "link": "https://wikipedia.org/wiki/Berlin"}]}, {"year": "1994", "text": "The 1994 <PERSON> shrine bomb explosion in Iran leaves at least 25 dead and 70 to 300 injured.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/1994_Imam_<PERSON><PERSON>_shrine_bomb_explosion\" class=\"mw-redirect\" title=\"1994 Imam <PERSON> shrine bomb explosion\">1994 Imam <PERSON> shrine bomb explosion</a> in <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> leaves at least 25 dead and 70 to 300 injured.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1994_<PERSON>_<PERSON><PERSON>_shrine_bomb_explosion\" class=\"mw-redirect\" title=\"1994 Imam <PERSON> shrine bomb explosion\">1994 Imam <PERSON> shrine bomb explosion</a> in <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> leaves at least 25 dead and 70 to 300 injured.", "links": [{"title": "1994 Imam <PERSON> shrine bomb explosion", "link": "https://wikipedia.org/wiki/1994_<PERSON>_<PERSON><PERSON>_shrine_bomb_explosion"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "2003", "text": "The Wikimedia Foundation is founded in St. Petersburg, Florida.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Wikimedia_Foundation\" title=\"Wikimedia Foundation\">Wikimedia Foundation</a> is founded in <a href=\"https://wikipedia.org/wiki/St._Petersburg,_Florida\" title=\"St. Petersburg, Florida\">St. Petersburg, Florida</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wikimedia_Foundation\" title=\"Wikimedia Foundation\">Wikimedia Foundation</a> is founded in <a href=\"https://wikipedia.org/wiki/St._Petersburg,_Florida\" title=\"St. Petersburg, Florida\">St. Petersburg, Florida</a>.", "links": [{"title": "Wikimedia Foundation", "link": "https://wikipedia.org/wiki/Wikimedia_Foundation"}, {"title": "St. Petersburg, Florida", "link": "https://wikipedia.org/wiki/St._Petersburg,_Florida"}]}, {"year": "2011", "text": "RusAir Flight 9605 crashes in Besovets during approach to Petrozavodsk Airport, killing 47.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/RusAir_Flight_9605\" title=\"RusAir Flight 9605\">RusAir Flight 9605</a> crashes in <a href=\"https://wikipedia.org/wiki/Besovets\" title=\"Besovets\">Besovets</a> during approach to <a href=\"https://wikipedia.org/wiki/Petrozavodsk_Airport\" title=\"Petrozavodsk Airport\">Petrozavodsk Airport</a>, killing 47.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/RusAir_Flight_9605\" title=\"RusAir Flight 9605\">RusAir Flight 9605</a> crashes in <a href=\"https://wikipedia.org/wiki/Besovets\" title=\"Besovets\">Besovets</a> during approach to <a href=\"https://wikipedia.org/wiki/Petrozavodsk_Airport\" title=\"Petrozavodsk Airport\">Petrozavodsk Airport</a>, killing 47.", "links": [{"title": "RusAir Flight 9605", "link": "https://wikipedia.org/wiki/RusAir_Flight_9605"}, {"title": "Besovets", "link": "https://wikipedia.org/wiki/Besovets"}, {"title": "Petrozavodsk Airport", "link": "https://wikipedia.org/wiki/Petrozavodsk_Airport"}]}, {"year": "2019", "text": "Iran's Air Defense Forces shoot down an American surveillance drone over the Strait of Hormuz amid rising tensions between the two countries.", "html": "2019 - Iran's <a href=\"https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Defense_Force\" title=\"Islamic Republic of Iran Air Defense Force\">Air Defense Forces</a> <a href=\"https://wikipedia.org/wiki/2019_Iranian_shoot-down_of_American_drone\" title=\"2019 Iranian shoot-down of American drone\">shoot down an American surveillance drone</a> over the <a href=\"https://wikipedia.org/wiki/Strait_of_Hormuz\" title=\"Strait of Hormuz\">Strait of Hormuz</a> amid <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93United_States_relations#2019-2020_escalation_in_tensions\" title=\"Iran-United States relations\">rising tensions between the two countries</a>.", "no_year_html": "Iran's <a href=\"https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Defense_Force\" title=\"Islamic Republic of Iran Air Defense Force\">Air Defense Forces</a> <a href=\"https://wikipedia.org/wiki/2019_Iranian_shoot-down_of_American_drone\" title=\"2019 Iranian shoot-down of American drone\">shoot down an American surveillance drone</a> over the <a href=\"https://wikipedia.org/wiki/Strait_of_Hormuz\" title=\"Strait of Hormuz\">Strait of Hormuz</a> amid <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93United_States_relations#2019-2020_escalation_in_tensions\" title=\"Iran-United States relations\">rising tensions between the two countries</a>.", "links": [{"title": "Islamic Republic of Iran Air Defense Force", "link": "https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Defense_Force"}, {"title": "2019 Iranian shoot-down of American drone", "link": "https://wikipedia.org/wiki/2019_Iranian_shoot-down_of_American_drone"}, {"title": "Strait of Hormuz", "link": "https://wikipedia.org/wiki/Strait_of_Hormuz"}, {"title": "Iran-United States relations", "link": "https://wikipedia.org/wiki/Iran%E2%80%93United_States_relations#2019-2020_escalation_in_tensions"}]}], "Births": [{"year": "1005", "text": "<PERSON>, <PERSON><PERSON><PERSON> caliph of Egypt (d. 1036)", "html": "1005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> caliph of Egypt (d. 1036)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> caliph of Egypt (d. 1036)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1389", "text": "<PERSON> Lancaster, 1st Duke of Bedford, English statesman (d. 1435)", "html": "1389 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Bedford\" class=\"mw-redirect\" title=\"<PERSON> of Lancaster, 1st Duke of Bedford\"><PERSON> Lancaster, 1st Duke of Bedford</a>, English statesman (d. 1435)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Bedford\" class=\"mw-redirect\" title=\"<PERSON> of Lancaster, 1st Duke of Bedford\"><PERSON> Lancaster, 1st Duke of Bedford</a>, English statesman (d. 1435)", "links": [{"title": "<PERSON> Lancaster, 1st Duke of Bedford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Bedford"}]}, {"year": "1469", "text": "<PERSON><PERSON>, duke of Milan (d. 1494)", "html": "1469 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, duke of Milan (d. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, duke of Milan (d. 1494)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Sforza"}]}, {"year": "1566", "text": "<PERSON><PERSON><PERSON>, Polish and Swedish king (d. 1632)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_<PERSON>\" title=\"<PERSON><PERSON><PERSON> III Vasa\"><PERSON><PERSON><PERSON></a>, Polish and Swedish king (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_<PERSON>\" title=\"<PERSON><PERSON><PERSON> III Vasa\"><PERSON><PERSON><PERSON></a>, Polish and Swedish king (d. 1632)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1583", "text": "<PERSON>, Swedish soldier and politician, Lord High Constable of Sweden (d. 1652)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_Sweden\" title=\"Lord High Constable of Sweden\">Lord High Constable of Sweden</a> (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_Sweden\" title=\"Lord High Constable of Sweden\">Lord High Constable of Sweden</a> (d. 1652)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lord High Constable of Sweden", "link": "https://wikipedia.org/wiki/Lord_High_Constable_of_Sweden"}]}, {"year": "1634", "text": "<PERSON>, duke of Savoy (d. 1675)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON></a>, duke of Savoy (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON></a>, duke of Savoy (d. 1675)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy"}]}, {"year": "1642", "text": "(O.S.) <PERSON>, English minister and scholar (d. 1715)", "html": "1642 - (<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(divine)\" title=\"<PERSON> (divine)\"><PERSON></a>, English minister and scholar (d. 1715)", "no_year_html": "(<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(divine)\" title=\"<PERSON> (divine)\"><PERSON></a>, English minister and scholar (d. 1715)", "links": [{"title": "Old Style", "link": "https://wikipedia.org/wiki/Old_Style"}, {"title": "<PERSON> (divine)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(divine)"}]}, {"year": "1647", "text": "(O.S.) <PERSON>, Elector of Saxony (d. 1691)", "html": "1647 - (<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON></a>, Elector of Saxony (d. 1691)", "no_year_html": "(<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON></a>, Elector of Saxony (d. 1691)", "links": [{"title": "Old Style", "link": "https://wikipedia.org/wiki/Old_Style"}, {"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1717", "text": "<PERSON>, French sculptor and painter (d. 1776)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and painter (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and painter (d. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1723", "text": "(O.S.) <PERSON>, Scottish philosopher and historian (d. 1816)", "html": "1723 - (<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish philosopher and historian (d. 1816)", "no_year_html": "(<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish philosopher and historian (d. 1816)", "links": [{"title": "Old Style", "link": "https://wikipedia.org/wiki/Old_Style"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON>, Japanese shōgun (d. 1786)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/Tokugawa_Ieharu\" title=\"Tokugawa Ieharu\"><PERSON></a>, Japanese shōgun (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_I<PERSON><PERSON>\" title=\"Tokugawa Ieharu\"><PERSON></a>, Japanese shōgun (d. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1754", "text": "<PERSON><PERSON><PERSON> of Hesse-Darmstadt, princess of Baden (d. 1832)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>_of_Hesse-Darmstadt\" title=\"Princess <PERSON><PERSON><PERSON> of Hesse-Darmstadt\"><PERSON><PERSON><PERSON> of Hesse-Darmstadt</a>, princess of Baden (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>_of_Hesse-Darmstadt\" title=\"Princess <PERSON><PERSON><PERSON> of Hesse-Darmstadt\"><PERSON><PERSON><PERSON> of Hesse-Darmstadt</a>, princess of Baden (d. 1832)", "links": [{"title": "Princess <PERSON><PERSON><PERSON> of Hesse-Darmstadt", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>_of_Hesse-Darmstadt"}]}, {"year": "1756", "text": "<PERSON>, German-Swedish composer and educator (d. 1792)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swedish composer and educator (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swedish composer and educator (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON>, German entomologist and author (d. 1826)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German entomologist and author (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German entomologist and author (d. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacob_H%C3%BCbner"}]}, {"year": "1763", "text": "<PERSON>, Irish rebel leader (d. 1798)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> To<PERSON>\"><PERSON></a>, Irish rebel leader (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wolfe Tone\"><PERSON></a>, Irish rebel leader (d. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, American minister and academic (d. 1840)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and academic (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and academic (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON>, 5th Earl of Selkirk, Scottish philanthropist and politician, Lord Lieutenant of Kirkcudbright (d. 1820)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Selkirk\" title=\"<PERSON>, 5th Earl of Selkirk\"><PERSON>, 5th Earl of Selkirk</a>, Scottish philanthropist and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Kirkcudbright\" title=\"Lord Lieutenant of Kirkcudbright\">Lord Lieutenant of Kirkcudbright</a> (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Selkirk\" title=\"<PERSON>, 5th Earl of Selkirk\"><PERSON>, 5th Earl of Selkirk</a>, Scottish philanthropist and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Kirkcudbright\" title=\"Lord Lieutenant of Kirkcudbright\">Lord Lieutenant of Kirkcudbright</a> (d. 1820)", "links": [{"title": "<PERSON>, 5th Earl of Selkirk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Selkirk"}, {"title": "Lord Lieutenant of Kirkcudbright", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Kirkcudbright"}]}, {"year": "1771", "text": "<PERSON>, Prussian general and politician, Prussian Minister of War (d. 1848)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian general and politician, <a href=\"https://wikipedia.org/wiki/Prussian_Minister_of_War\" class=\"mw-redirect\" title=\"Prussian Minister of War\">Prussian Minister of War</a> (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian general and politician, <a href=\"https://wikipedia.org/wiki/Prussian_Minister_of_War\" class=\"mw-redirect\" title=\"Prussian Minister of War\">Prussian Minister of War</a> (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prussian Minister of War", "link": "https://wikipedia.org/wiki/Prussian_Minister_of_War"}]}, {"year": "1777", "text": "<PERSON><PERSON><PERSON>, Canadian bishop (d. 1840)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian bishop (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian bishop (d. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1778", "text": "<PERSON>, vico<PERSON><PERSON>, French politician, 7th Prime Minister of France (d. 1832)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_vicomte_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, vicomte de Martignac\"><PERSON>, vicomte <PERSON></a>, French politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_vicomte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, vicomte de Martignac\"><PERSON>, vicomte <PERSON></a>, French politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1832)", "links": [{"title": "<PERSON>, vico<PERSON><PERSON> de Martignac", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_vicom<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1786", "text": "<PERSON><PERSON>-<PERSON><PERSON>, French poet and author (d. 1859)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, French poet and author (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and author (d. 1859)", "links": [{"title": "<PERSON><PERSON>-Valmore", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>more"}]}, {"year": "1796", "text": "<PERSON> San Filippo <PERSON>, Italian cardinal (d. 1878)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_Sorso\" title=\"Luigi Amat di San Filippo e Sorso\"><PERSON> di San Filippo e Sorso</a>, Italian cardinal (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Fi<PERSON>_<PERSON>_Sorso\" title=\"Luigi Amat di San Filippo e Sorso\"><PERSON> San Filippo e Sorso</a>, Italian cardinal (d. 1878)", "links": [{"title": "<PERSON> di San Filippo e Sorso", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>o"}]}, {"year": "1808", "text": "<PERSON>, German rabbi and scholar (d. 1888)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Samson <PERSON>\"><PERSON></a>, German rabbi and scholar (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Samson <PERSON>\"><PERSON></a>, German rabbi and scholar (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON><PERSON> <PERSON>, German theologian and academic (d. 1884)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_August_<PERSON>\" title=\"<PERSON><PERSON> August <PERSON>\"><PERSON><PERSON> <PERSON></a>, German theologian and academic (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_August_<PERSON>\" title=\"<PERSON>ak August <PERSON>\"><PERSON><PERSON> <PERSON></a>, German theologian and academic (d. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_August_<PERSON><PERSON>"}]}, {"year": "1813", "text": "<PERSON>, French poet and author (d. 1877)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, German-French cellist and composer (d. 1880)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French cellist and composer (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French cellist and composer (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, Norwegian suffragist and women's rights activist (d. 1916)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian suffragist and women's rights activist (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian suffragist and women's rights activist (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, English historian and academic (d. 1936)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Richard <PERSON>\"><PERSON></a>, English historian and academic (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Richard <PERSON>\"><PERSON></a>, English historian and academic (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, American novelist and short story writer (d. 1932)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>nut<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1859", "text": "<PERSON>, Austrian philosopher (d. 1932)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Scottish-American race car driver and engineer (d. 1932)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American race car driver and engineer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American race car driver and engineer (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Australian cricketer, footballer, and coach (d. 1937)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, footballer, and coach (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, footballer, and coach (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, English biochemist and academic, Nobel Prize laureate (d. 1947)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1865", "text": "<PERSON>, English biologist and physician (d. 1939)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and physician (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and physician (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, English cricketer (d. 1957)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1957)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1867", "text": "<PERSON>, Polish scientist and medical examiner (d. 1942)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish scientist and medical examiner (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish scientist and medical examiner (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian businessman, founded the Kirloskar Group (d. 1956)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/La<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>rl<PERSON>kar_Group\" title=\"Kirloskar Group\">Kirloskar Group</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>rl<PERSON>kar_Group\" title=\"Kirloskar Group\">Kirloskar Group</a> (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Kirloskar Group", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Group"}]}, {"year": "1870", "text": "<PERSON>, French painter and academic (d. 1943)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9noy\" title=\"<PERSON>\"><PERSON></a>, French painter and academic (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9noy\" title=\"<PERSON>\"><PERSON></a>, French painter and academic (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9noy"}]}, {"year": "1872", "text": "<PERSON>, American 5th General of The Salvation Army (d. 1948)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Salvation_Army)\" title=\"<PERSON> (Salvation Army)\"><PERSON></a>, American 5th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Salvation_Army)\" title=\"<PERSON> (Salvation Army)\"><PERSON></a>, American 5th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1948)", "links": [{"title": "<PERSON> (Salvation Army)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Salvation_Army)"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1875", "text": "<PERSON>, English geneticist, statistician, and academic (d. 1967)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist, statistician, and academic (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist, statistician, and academic (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American golfer (d. 1937)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American astronomer and author (d. 1974)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and author (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and author (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, German psychiatrist and psychotherapist (d. 1970)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and psychotherapist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and psychotherapist (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Polish linguist and academic (d. 1927)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish linguist and academic (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish linguist and academic (d. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ro%C5%84ski"}]}, {"year": "1887", "text": "<PERSON>, German painter and illustrator (d. 1948)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Greek-South African astronomer and academic (d. 1951)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-South African astronomer and academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-South African astronomer and academic (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>, Italian soprano (d. 1951)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian soprano (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian soprano (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>i"}]}, {"year": "1891", "text": "<PERSON>, Irish lawyer and politician, 3rd Taoiseach of Ireland (d. 1976)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\"><PERSON>iseach of Ireland</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "1893", "text": "<PERSON>, German soldier and politician (d. 1958)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American chemist and academic (d. 1971)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Lloyd_Hall\" title=\"Lloyd Hall\"><PERSON> Hall</a>, American chemist and academic (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lloyd_Hall\" title=\"Lloyd Hall\"><PERSON> Hall</a>, American chemist and academic (d. 1971)", "links": [{"title": "Lloyd Hall", "link": "https://wikipedia.org/wiki/Lloyd_Hall"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian pianist, composer, and conductor (d. 1982)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian pianist, composer, and conductor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian pianist, composer, and conductor (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German author and playwright (d. 1973)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, French soldier and engineer (d. 1943)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and engineer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and engineer (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English wrestler, sculptor, and singer (d. 1991)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English wrestler, sculptor, and singer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English wrestler, sculptor, and singer (d. 1991)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1905", "text": "<PERSON>, American playwright and screenwriter (d. 1984)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American high jumper and obstetrician (d. 1965)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American high jumper and obstetrician (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American high jumper and obstetrician (d. 1965)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1906", "text": "<PERSON>, Scottish mining engineer (d. 1985)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mining_engineer)\" title=\"<PERSON> (mining engineer)\"><PERSON></a>, Scottish mining engineer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(mining_engineer)\" title=\"<PERSON> (mining engineer)\"><PERSON></a>, Scottish mining engineer (d. 1985)", "links": [{"title": "<PERSON> (mining engineer)", "link": "https://wikipedia.org/wiki/<PERSON>(mining_engineer)"}]}, {"year": "1907", "text": "<PERSON>, American singer-songwriter and banjo player (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and banjo player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and banjo player (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American baseball player (d. 2009)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American actor (d. 1957)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Australian-American actor (d. 1959)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-American actor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-American actor (d. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American author and poet (d. 1990)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American actress (d. 1980)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English author (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American shot putter and football player (d. 1969)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American shot putter and football player (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American shot putter and football player (d. 1969)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1912", "text": "<PERSON>, English Field Marshal and Chief of the General Staff of the British Army (d. 1980)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Field_marshal_(United_Kingdom)\" title=\"Field marshal (United Kingdom)\">Field Marshal</a> and <a href=\"https://wikipedia.org/wiki/Chief_of_the_General_Staff_(United_Kingdom)\" title=\"Chief of the General Staff (United Kingdom)\">Chief of the General Staff</a> of the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Field_marshal_(United_Kingdom)\" title=\"Field marshal (United Kingdom)\">Field Marshal</a> and <a href=\"https://wikipedia.org/wiki/Chief_of_the_General_Staff_(United_Kingdom)\" title=\"Chief of the General Staff (United Kingdom)\">Chief of the General Staff</a> of the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> (d. 1980)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)"}, {"title": "Field marshal (United Kingdom)", "link": "https://wikipedia.org/wiki/Field_marshal_(United_Kingdom)"}, {"title": "Chief of the General Staff (United Kingdom)", "link": "https://wikipedia.org/wiki/Chief_of_the_General_Staff_(United_Kingdom)"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}]}, {"year": "1914", "text": "<PERSON>, Canadian ice hockey player (d. 1994)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish archaeologist and academic (d. 2024)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Muazzez_%C4%B0lmiye_%C3%87%C4%B1%C4%9F\" title=\"Muazzez İlmiye Çığ\">Muazzez İlmiye Çığ</a>, Turkish archaeologist and academic (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Muazzez_%C4%B0lmiye_%C3%87%C4%B1%C4%9F\" title=\"Muazzez İlmiye Çığ\">Muazzez İlmiye Çığ</a>, Turkish archaeologist and academic (d. 2024)", "links": [{"title": "Muazzez İlmiye Çığ", "link": "https://wikipedia.org/wiki/Muazzez_%C4%B0lmiye_%C3%87%C4%B1%C4%9F"}]}, {"year": "1915", "text": "<PERSON>, Australian footballer and coach (d. 2002)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Chinese-English director and screenwriter (d. 1994)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Chinese-English director and screenwriter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Chinese-English director and screenwriter (d. 1994)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 21st Premier of Quebec (d. 1973)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American country music singer-songwriter and guitarist (d. 1972)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/T._<PERSON>_Tyler\" title=\"T. Texas Tyler\"><PERSON><PERSON></a>, American country music singer-songwriter and guitarist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._Texas_Tyler\" title=\"T. Texas Tyler\"><PERSON><PERSON></a>, American country music singer-songwriter and guitarist (d. 1972)", "links": [{"title": "T. <PERSON>", "link": "https://wikipedia.org/wiki/T._Texas_Tyler"}]}, {"year": "1917", "text": "<PERSON>, Austrian-Polish mathematician and academic (d. 1994)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Polish mathematician and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Polish mathematician and academic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>wa"}]}, {"year": "1918", "text": "<PERSON>, American race car driver (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 1997)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Hungarian-American author (d. 2011)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Szt%C3%A1ray\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Szt%C3%A1ray\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American author (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zolt%C3%A1n_Szt%C3%A1ray"}]}, {"year": "1920", "text": "<PERSON>, American guitarist and bandleader (d. 1954)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and bandleader (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and bandleader (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American trumpet player (d. 1986)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trumpet player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trumpet player (d. 1986)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1921", "text": "<PERSON>, American historian and author (d. 1999)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Ecuadorian tennis player (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian tennis player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian tennis player (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ra"}]}, {"year": "1923", "text": "<PERSON>, German-American historian, author, and academic (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian, author, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian, author, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Polish actor and educator (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish actor and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish actor and educator (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American guitarist and record producer (d. 2001)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>et <PERSON>\"><PERSON><PERSON></a>, American guitarist and record producer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>et <PERSON>\"><PERSON><PERSON></a>, American guitarist and record producer (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chet_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, German sculptor and academic, designed The Sphere (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and academic, designed <a href=\"https://wikipedia.org/wiki/The_Sphere\" title=\"The Sphere\">The Sphere</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and academic, designed <a href=\"https://wikipedia.org/wiki/The_Sphere\" title=\"The Sphere\">The Sphere</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Sphere", "link": "https://wikipedia.org/wiki/The_Sphere"}]}, {"year": "1925", "text": "<PERSON>, American tennis player and educator (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and educator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and educator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American lieutenant and actor, Medal of Honor recipient (d. 1971)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lieutenant and actor, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lieutenant and actor, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli general and politician, 9th Israeli Minister of Tourism (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Ze%27evi\" title=\"<PERSON><PERSON><PERSON><PERSON>'evi\"><PERSON><PERSON><PERSON><PERSON>e<PERSON></a>, Israeli general and politician, 9th <a href=\"https://wikipedia.org/wiki/Tourism_Ministry_(Israel)\" class=\"mw-redirect\" title=\"Tourism Ministry (Israel)\">Israeli Minister of Tourism</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Ze%27evi\" title=\"<PERSON><PERSON><PERSON><PERSON>'evi\"><PERSON><PERSON><PERSON><PERSON>evi</a>, Israeli general and politician, 9th <a href=\"https://wikipedia.org/wiki/Tourism_Ministry_(Israel)\" class=\"mw-redirect\" title=\"Tourism Ministry (Israel)\">Israeli Minister of Tourism</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rehavam_Ze%27evi"}, {"title": "Tourism Ministry (Israel)", "link": "https://wikipedia.org/wiki/Tourism_Ministry_(Israel)"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Iranian poet and activist (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian poet and activist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian poet and activist (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American saxophonist, flute player, and composer (d. 1964)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, flute player, and composer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, flute player, and composer (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor and producer (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, French intelligence officer and politician (d. 2025)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French intelligence officer and politician (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French intelligence officer and politician (d. 2025)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Ethiopian surgeon and educator (d. 1999)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Asrat_Woldeyes\" title=\"Asrat Woldeyes\"><PERSON><PERSON></a>, Ethiopian surgeon and educator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asrat_Woldeyes\" title=\"Asrat Woldeyes\"><PERSON><PERSON></a>, Ethiopian surgeon and educator (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asrat_Woldeyes"}]}, {"year": "1929", "text": "<PERSON>, Sr., Canadian-American businessman and philanthropist (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, Canadian-American businessman and philanthropist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, Canadian-American businessman and philanthropist (d. 2013)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>."}]}, {"year": "1929", "text": "<PERSON>, English journalist and author (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American lesbian, gay, bisexual and transgender rights activist (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lesbian, gay, bisexual and transgender rights activist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lesbian, gay, bisexual and transgender rights activist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Polish sculptor and academic (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish sculptor and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish sculptor and academic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Magdalena_<PERSON>owicz"}]}, {"year": "1930", "text": "<PERSON>, English bishop (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actress (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Olympia_Dukakis"}]}, {"year": "1931", "text": "<PERSON>, American actor and director", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Russian poet and author (d. 1994)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American actor (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English journalist and author", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actress", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American politician (d. 2005)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician (d. 2005)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1935", "text": "<PERSON>, American football player (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Italian footballer and coach (d. 1971)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and coach (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and coach (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer (d. 2002)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Guy\"><PERSON></a>, American singer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Guy\" title=\"Billy Guy\"><PERSON></a>, American singer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Estonian author and screenwriter (d. 2017)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/En<PERSON>_<PERSON>\" title=\"En<PERSON>\"><PERSON><PERSON></a>, Estonian author and screenwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/En<PERSON>_<PERSON>\" title=\"En<PERSON>\"><PERSON><PERSON></a>, Estonian author and screenwriter (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enn_<PERSON>ete<PERSON>a"}]}, {"year": "1937", "text": "<PERSON>, English actor and singer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dean\"><PERSON></a>, English actor and singer", "links": [{"title": "<PERSON> Dean", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Australian educator and politician, 42nd Premier of Victoria (d. 2015)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 42nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 42nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1938", "text": "<PERSON><PERSON>, English music producer (d. 2003)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English music producer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Most\"><PERSON><PERSON></a>, English music producer (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (d. 1998)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Desai"}]}, {"year": "1939", "text": "<PERSON><PERSON>, English rugby player and manager", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English rugby player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English rugby player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, German priest and theologian", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German priest and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German priest and theologian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English-born American actor (d. 2018)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born American actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born American actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English actor, director, and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, German physicist and astronaut", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and astronaut", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>old"}]}, {"year": "1942", "text": "<PERSON>, Australian mathematician and theorist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian mathematician and theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian mathematician and theorist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer, songwriter and producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian singer and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Timorese soldier and politician, 1st President of East Timor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>anana_Gusm%C3%A3o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Timorese soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_East_Timor\" class=\"mw-redirect\" title=\"President of East Timor\">President of East Timor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>anana_Gusm%C3%A3o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Timorese soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_East_Timor\" class=\"mw-redirect\" title=\"President of East Timor\">President of East Timor</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Xanana_Gusm%C3%A3o"}, {"title": "President of East Timor", "link": "https://wikipedia.org/wiki/President_of_East_Timor"}]}, {"year": "1946", "text": "<PERSON>, Russian-Israeli mathematician and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Israeli mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Israeli mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American television host", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bob_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American pianist and educator (d. 2023)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and educator (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and educator (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>"}]}, {"year": "1947", "text": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>, American pop singer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22La<PERSON>a%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON></a>, American pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22LaLa%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON></a>, American pop singer", "links": [{"title": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22LaLa%22_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, American bishop (d. 2014)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_B._Flores\" title=\"<PERSON><PERSON><PERSON> B. Flores\"><PERSON><PERSON><PERSON></a>, American bishop (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>._<PERSON>\" title=\"C<PERSON>lo B. Flores\"><PERSON><PERSON><PERSON></a>, American bishop (d. 2014)", "links": [{"title": "Cirilo B. Flores", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_B._Flores"}]}, {"year": "1948", "text": "<PERSON>, Scottish bass player and songwriter (d. 2018)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bass player and songwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bass player and songwriter (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Nauruan politician, 10th President of Nauru", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nauruan politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nauruan politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Nauru", "link": "https://wikipedia.org/wiki/President_of_Nauru"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, 8th president of Sri Lanka", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Gotabaya_Rajapaksa\" title=\"Gotabaya Rajapaksa\"><PERSON><PERSON><PERSON> Raja<PERSON>ks<PERSON></a>, 8th president of Sri Lanka", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gotabaya_Rajapaksa\" title=\"Gotabaya Rajapaksa\"><PERSON><PERSON><PERSON></a>, 8th president of Sri Lanka", "links": [{"title": "Gotabaya Rajapaksa", "link": "https://wikipedia.org/wiki/Gotabaya_Rajapaksa"}]}, {"year": "1949", "text": "<PERSON>, American singer, songwriter, pianist, producer, and actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, pianist, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Richie\"><PERSON></a>, American singer, songwriter, pianist, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Iraqi politician, 76th Prime Minister of Iraq", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iraqi politician, 76th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iraq\" title=\"Prime Minister of Iraq\">Prime Minister of Iraq</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iraqi politician, 76th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iraq\" title=\"Prime Minister of Iraq\">Prime Minister of Iraq</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "Prime Minister of Iraq", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iraq"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American voice actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Tress_<PERSON>\" title=\"T<PERSON>\">T<PERSON></a>, American voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON>\" title=\"Tress <PERSON>\">T<PERSON></a>, American voice actress", "links": [{"title": "T<PERSON>", "link": "https://wikipedia.org/wiki/Tress_<PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish scholar and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish scholar and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish scholar and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Irish poet and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Indian author and poet", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and poet", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American author and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Mexican tennis player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Ram%C3%ADrez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Ram%C3%ADrez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_Ram%C3%ADrez"}]}, {"year": "1953", "text": "<PERSON>, German engineer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American musician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1954", "text": "<PERSON>, South African-English cricketer and sportscaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Israeli colonel, pilot, and astronaut (d. 2003)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli colonel, pilot, and astronaut (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli colonel, pilot, and astronaut (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American geneticist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American geneticist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American geneticist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American author (d. 2009)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American author (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English footballer and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, South Korean newscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k-hee\" title=\"<PERSON><PERSON> Suk-hee\"><PERSON><PERSON>-hee</a>, South Korean newscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k-hee\" title=\"<PERSON><PERSON> Suk-hee\"><PERSON><PERSON>-hee</a>, South Korean newscaster", "links": [{"title": "<PERSON><PERSON>hee", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-hee"}]}, {"year": "1958", "text": "<PERSON>, English hard rock guitarist and songwriter (d. 2007)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English hard rock guitarist and songwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English hard rock guitarist and songwriter (d. 2007)", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_(guitarist)"}]}, {"year": "1959", "text": "<PERSON>, American screenwriter, producer and director", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, producer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, producer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American economist and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English bass player and actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bass_guitarist)\" title=\"<PERSON> (bass guitarist)\"><PERSON></a>, English bass player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bass_guitarist)\" title=\"<PERSON> (bass guitarist)\"><PERSON></a>, English bass player and actor", "links": [{"title": "<PERSON> (bass guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_(bass_guitarist)"}]}, {"year": "1963", "text": "<PERSON>, American sprinter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, British author and broadcaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British author and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British author and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian motorcycle racer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Pierfrancesco_Chili\" title=\"Pierfrancesco Chili\"><PERSON><PERSON><PERSON><PERSON></a>, Italian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pierfrancesco_Chili\" title=\"Pierfrancesco Chili\"><PERSON><PERSON><PERSON><PERSON></a>, Italian motorcycle racer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pierfrancesco_Chili"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German runner", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Silke_M%C3%B6ller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Silke_M%C3%B6ller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Silke_M%C3%B6ller"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American director, producer, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>az_<PERSON>kin"}]}, {"year": "1967", "text": "<PERSON>, American-Australian actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kidman\"><PERSON></a>, American-Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kidman\"><PERSON></a>, American-Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Portuguese footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Russian mathematician and academic", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, American tennis player and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/MaliVai_Washington\" title=\"MaliVai Washington\"><PERSON><PERSON><PERSON></a>, American tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/MaliVai_Washington\" title=\"MaliVai Washington\"><PERSON><PERSON><PERSON> Washington</a>, American tennis player and sportscaster", "links": [{"title": "MaliVai Washington", "link": "https://wikipedia.org/wiki/MaliVai_Washington"}]}, {"year": "1970", "text": "<PERSON>, German politician, German Minister of Labour and Social Affairs", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/List_of_German_labour_ministers\" class=\"mw-redirect\" title=\"List of German labour ministers\">German Minister of Labour and Social Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/List_of_German_labour_ministers\" class=\"mw-redirect\" title=\"List of German labour ministers\">German Minister of Labour and Social Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of German labour ministers", "link": "https://wikipedia.org/wiki/List_of_German_labour_ministers"}]}, {"year": "1970", "text": "<PERSON><PERSON>, South African poet and social philosopher", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African poet and social philosopher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African poet and social philosopher", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American basketball player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Belgian politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American songwriter, guitarist, and bass player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American songwriter, guitarist, and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American songwriter, guitarist, and bass player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Greek footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American singer, guitarist and lyricist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, guitarist and lyricist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, guitarist and lyricist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Spanish tennis player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Czech footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADtka\" title=\"<PERSON>\"><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADtka\" title=\"<PERSON>\"><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Daniel_Z%C3%ADtka"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Panamanian baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Croatian basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Gordan_Giri%C4%8Dek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gorda<PERSON>_<PERSON>iri%C4%8Dek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gordan_Giri%C4%8Dek"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American mixed martial artist and actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mixed martial artist and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mixed martial artist and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American golfer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Italian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Semioli\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Semioli"}]}, {"year": "1980", "text": "<PERSON>, German cyclist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Norwegian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Brede_Hang<PERSON>nd\" title=\"Brede Hang<PERSON>nd\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bred<PERSON>_<PERSON>nd\" title=\"Bred<PERSON> Hang<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Brede_Hangeland"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Russian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Aleksei_Berezutski\" title=\"Aleksei Berezutski\"><PERSON>ek<PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksei_Berezutski\" title=\"Aleksei Berezutski\"><PERSON><PERSON><PERSON></a>, Russian footballer", "links": [{"title": "Aleksei <PERSON>", "link": "https://wikipedia.org/wiki/Aleksei_Berezutski"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Russian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "Example, English singer/rapper", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Example_(musician)\" title=\"Example (musician)\">Example</a>, English singer/rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Example_(musician)\" title=\"Example (musician)\">Example</a>, English singer/rapper", "links": [{"title": "Example (musician)", "link": "https://wikipedia.org/wiki/Example_(musician)"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bu"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Cameroonian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Aur%C3%A9<PERSON><PERSON>_Chedjou\" title=\"<PERSON><PERSON><PERSON><PERSON>edjou\"><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aur%C3%A9<PERSON><PERSON>_Chedjou\" title=\"<PERSON><PERSON><PERSON><PERSON> Chedjou\"><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aur%C3%A9<PERSON><PERSON>_<PERSON><PERSON>jou"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1985", "text": "<PERSON>, American singer and songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Taiwanese singer and songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/A-fu\" class=\"mw-redirect\" title=\"A-fu\">A-fu</a>, Taiwanese singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A-fu\" class=\"mw-redirect\" title=\"A-fu\">A-fu</a>, Taiwanese singer and songwriter", "links": [{"title": "A-fu", "link": "https://wikipedia.org/wiki/A-fu"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Australian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ball\" title=\"Carsten Ball\"><PERSON><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ball\" title=\"Carsten Ball\"><PERSON><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Bosnian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asmir_Begovi%C4%87"}]}, {"year": "1987", "text": "<PERSON>, Kenyan runner", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Argentinian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>r\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ryor\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Senegalese writer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Senegalese writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Senegalese writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Senegalese footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>y\" title=\"<PERSON><PERSON><PERSON>bal<PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>y\" title=\"<PERSON><PERSON><PERSON>bal<PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>y"}]}, {"year": "1991", "text": "<PERSON>, Dutch footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)\" class=\"mw-redirect\" title=\"<PERSON> (defensive end)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)\" class=\"mw-redirect\" title=\"<PERSON> (defensive end)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (defensive end)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)"}]}, {"year": "1995", "text": "<PERSON>, Scottish footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Canadian tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Hungarian sprint canoeist", "html": "1997 - <a href=\"https://wikipedia.org/wiki/B%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian sprint canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian sprint canoeist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A1lint_Kopasz"}]}, {"year": "2003", "text": "<PERSON>, American chess player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "465", "text": "Emperor <PERSON><PERSON> of Northern Wei (b. 440)", "html": "465 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei\" title=\"Emperor <PERSON><PERSON> of Northern Wei\">Emperor <PERSON><PERSON> of Northern Wei</a> (b. 440)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei\" title=\"Emperor <PERSON><PERSON> of Northern Wei\">Emperor <PERSON><PERSON> of Northern Wei</a> (b. 440)", "links": [{"title": "Emperor <PERSON><PERSON> of Northern Wei", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei"}]}, {"year": "840", "text": "<PERSON> the <PERSON>, Carolingian emperor (b. 778)", "html": "840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pi<PERSON>\" title=\"<PERSON> Pi<PERSON>\"><PERSON> the Pi<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Carolingian_Empire\" title=\"Carolingian Empire\"><PERSON>ing<PERSON></a> emperor (b. 778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Pi<PERSON>\"><PERSON> the <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Carolingian_Empire\" title=\"Carolingian Empire\">Carolingian</a> emperor (b. 778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Carolingian Empire", "link": "https://wikipedia.org/wiki/Carolingian_Empire"}]}, {"year": "930", "text": "<PERSON><PERSON><PERSON><PERSON>, Frankish monk and music theorist", "html": "930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish monk and <a href=\"https://wikipedia.org/wiki/Music_theory\" title=\"Music theory\">music theorist</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish monk and <a href=\"https://wikipedia.org/wiki/Music_theory\" title=\"Music theory\">music theorist</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Music theory", "link": "https://wikipedia.org/wiki/Music_theory"}]}, {"year": "981", "text": "<PERSON><PERSON>, archbishop of Magdeburg", "html": "981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Magdeburg\" title=\"<PERSON><PERSON> of Magdeburg\"><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Archbishopric_of_Magdeburg\" title=\"Archbishopric of Magdeburg\">Magdeburg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Magdeburg\" title=\"<PERSON><PERSON> of Magdeburg\"><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Archbishopric_of_Magdeburg\" title=\"Archbishopric of Magdeburg\">Magdeburg</a>", "links": [{"title": "<PERSON><PERSON> of Magdeburg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Magdeburg"}, {"title": "Archbishopric of Magdeburg", "link": "https://wikipedia.org/wiki/Archbishopric_of_Magdeburg"}]}, {"year": "1176", "text": "<PERSON> of Vladimir, Russian prince", "html": "1176 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vladimir\" title=\"Mikhail of Vladimir\"><PERSON> of Vladimir</a>, Russian prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vladimir\" title=\"<PERSON> of Vladimir\"><PERSON> of Vladimir</a>, Russian prince", "links": [{"title": "<PERSON> of Vladimir", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vladimir"}]}, {"year": "1351", "text": "<PERSON><PERSON>, German nun and mystic (b. 1291)", "html": "1351 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German nun and mystic (b. 1291)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German nun and mystic (b. 1291)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1597", "text": "<PERSON>, Dutch cartographer and explorer (b. 1550)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cartographer and explorer (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cartographer and explorer (b. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON><PERSON><PERSON> of Russia (b. 1589)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Russia\" title=\"<PERSON><PERSON><PERSON> II of Russia\"><PERSON>odor II of Russia</a> (b. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Russia\" title=\"<PERSON><PERSON>or II of Russia\"><PERSON>odor II of Russia</a> (b. 1589)", "links": [{"title": "<PERSON><PERSON><PERSON> II of Russia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Russia"}]}, {"year": "1668", "text": "<PERSON>, German missionary and scholar (b. 1620)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German missionary and scholar (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German missionary and scholar (b. 1620)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON>, English businessman (b. 1704)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, German viol player and composer (b. 1723)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Viol\" title=\"Viol\">viol</a> player and composer (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Viol\" title=\"Viol\">viol</a> player and composer (b. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Viol", "link": "https://wikipedia.org/wiki/Viol"}]}, {"year": "1800", "text": "<PERSON>, German mathematician and academic (b. 1719)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4stner\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4stner\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4stner"}]}, {"year": "1810", "text": "<PERSON> the Younger, Swedish general and politician (b. 1755)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Swedish general and politician (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Swedish general and politician (b. 1755)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, French general (b. 1766)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, Argentinian general, economist, and politician (b. 1770)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general, economist, and politician (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general, economist, and politician (b. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON> of the United Kingdom (b. 1765)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"William IV of the United Kingdom\"><PERSON> of the United Kingdom</a> (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"William IV of the United Kingdom\"><PERSON> IV of the United Kingdom</a> (b. 1765)", "links": [{"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom"}]}, {"year": "1840", "text": "<PERSON>, French historian and politician (b. 1761)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and politician (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and politician (b. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, Argentinian captain and politician (b. 1782)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Argentinian captain and politician (b. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Argentinian captain and politician (b. 1782)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Japanese commander (b. 1835)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Hi<PERSON><PERSON>_Toshiz%C5%8D\" title=\"<PERSON><PERSON><PERSON> Toshizō\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hi<PERSON><PERSON>_Toshiz%C5%8D\" title=\"<PERSON><PERSON><PERSON> Toshizō\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hijikata_Toshiz%C5%8D"}]}, {"year": "1870", "text": "<PERSON>, French historian and author (b. 1830)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON>, French general (b. 1804)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/%C3%89lie_Fr%C3%A9d%C3%A9ric_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French general (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lie_Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French general (b. 1804)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lie_Fr%C3%A9d%C3%A9ric_<PERSON>ey"}]}, {"year": "1875", "text": "<PERSON>, American police officer and politician (b. 1810)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and politician (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and politician (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American writer, critic, editor, lecturer, and activist (b. 1793)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, critic, editor, lecturer, and activist (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, critic, editor, lecturer, and activist (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Polish-English chess player (b. 1842)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English chess player (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English chess player (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English painter (b. 1840)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Estonian-Russian historian, lawyer, and diplomat (b. 1845)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Russian historian, lawyer, and diplomat (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Russian historian, lawyer, and diplomat (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Austrian physician and psychologist (b. 1842)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and psychologist (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and psychologist (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Greek merchant and politician, 35th Mayor of Athens (b. 1843)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek merchant and politician, 35th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Athens\" title=\"List of mayors of Athens\">Mayor of Athens</a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek merchant and politician, 35th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Athens\" title=\"List of mayors of Athens\">Mayor of Athens</a> (b. 1843)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of mayors of Athens", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Athens"}]}, {"year": "1945", "text": "<PERSON>, German author, poet, and playwright (b. 1878)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and playwright (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and playwright (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American mobster (b. 1906)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mobster (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mobster (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1952", "text": "<PERSON>, Italian race car driver (b. 1898)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (b. 1902)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_for_Chemistry\" class=\"mw-redirect\" title=\"Nobel Prize for Chemistry\">Nobel Prize</a> laureate (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_for_Chemistry\" class=\"mw-redirect\" title=\"Nobel Prize for Chemistry\">Nobel Prize</a> laureate (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize for Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_for_Chemistry"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Greek-French mathematician and academic (b. 1898)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Salem\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-French mathematician and academic (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Salem\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-French mathematician and academic (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rapha%C3%ABl_Salem"}]}, {"year": "1965", "text": "<PERSON>, American financier and politician (b. 1870)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and politician (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and politician (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Belgian priest, physicist, and astronomer (b. 1894)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AEtre\" title=\"<PERSON>\"><PERSON></a>, Belgian priest, physicist, and astronomer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AEtre\" title=\"<PERSON>\"><PERSON></a>, Belgian priest, physicist, and astronomer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AEtre"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Indian artist, painter, actor, dancer, writer, music composer and politician (b. 1909)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian artist, painter, actor, dancer, writer, music composer and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Indian artist, painter, actor, dancer, writer, music composer and politician (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian snooker player (b. 1912)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian snooker player (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian snooker player (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Haitian anthropologist (b. 1898)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian anthropologist (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian anthropologist (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian-American director and producer (b. 1913)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, Canadian-American director and producer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, Canadian-American director and producer (b. 1913)", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, English actress (b. 1883)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Romanian-French philosopher and educator (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French philosopher and educator (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French philosopher and educator (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Turkish poet and author (b. 1917)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Cahit_K%C3%BClebi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cahit_K%C3%BClebi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cahit_K%C3%BClebi"}]}, {"year": "1999", "text": "<PERSON>, American game show host, author, and critic (b. 1902)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host, author, and critic (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host, author, and critic (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Austrian-American biochemist and academic (b. 1905)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American biochemist and academic (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American biochemist and academic (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Dutch runner (b. 1916)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch runner (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch runner (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Australian politician, 41st Premier of Tasmania (b. 1950)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 41st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 41st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1950)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "2005", "text": "<PERSON>, American journalist, historian, and author (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American journalist, historian, and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American journalist, historian, and author (b. 1929)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "2005", "text": "<PERSON>, American physicist and engineer, Nobel Prize laureate (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2010", "text": "<PERSON>, Italian footballer (b. 1943)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English palaeontologist and academic (b. 1916)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English palaeontologist and academic (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English palaeontologist and academic (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American television personality (b. 1977)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Second Lady of the United States. (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Second_Lady_of_the_United_States\" class=\"mw-redirect\" title=\"Second Lady of the United States\">Second Lady of the United States</a>. (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Second_Lady_of_the_United_States\" class=\"mw-redirect\" title=\"Second Lady of the United States\">Second Lady of the United States</a>. (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Second Lady of the United States", "link": "https://wikipedia.org/wiki/Second_Lady_of_the_United_States"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American painter (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American painter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American painter (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Prince <PERSON> of Köstritz (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_<PERSON><PERSON>_of_K%C3%B6stritz\" title=\"<PERSON>, Prince <PERSON> of Köstritz\"><PERSON>, Prince <PERSON> of Köstritz</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_<PERSON><PERSON>_of_K%C3%B6stritz\" title=\"<PERSON>, Prince <PERSON> of Köstritz\"><PERSON>, Prince <PERSON> of Köstritz</a> (b. 1919)", "links": [{"title": "<PERSON>, Prince <PERSON><PERSON> of Köstritz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_<PERSON><PERSON>_of_K%C3%B6st<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American critic (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Swedish footballer (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ingvar_<PERSON>\" title=\"Ing<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish footballer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ing<PERSON>_<PERSON>\" title=\"Ing<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish footballer (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ing<PERSON>_<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Romanian footballer and manager (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer and manager (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer and manager (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian-American painter and sculptor (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter and sculptor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter and sculptor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "Prodigy, American music artist (b. 1974)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Prodigy_(rapper)\" title=\"Prodigy (rapper)\">Prodigy</a>, American music artist (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prodigy_(rapper)\" title=\"Prodigy (rapper)\">Prodigy</a>, American music artist (b. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/Prodigy_(rapper)"}]}, {"year": "2022", "text": "<PERSON>, American basketball player (b. 1997)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Canadian actor and producer (b. 1935)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American actor, sumo wrestler and mixed martial artist (b. 1968)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, sumo wrestler and mixed martial artist (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, sumo wrestler and mixed martial artist (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ily"}]}]}}