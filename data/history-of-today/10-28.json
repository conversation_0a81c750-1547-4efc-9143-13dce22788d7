{"date": "October 28", "url": "https://wikipedia.org/wiki/October_28", "data": {"Events": [{"year": "97", "text": "Roman emperor <PERSON><PERSON><PERSON> is forced by the Praetorian Guard to adopt general <PERSON> as his heir and successor.", "html": "97 - Roman emperor <a href=\"https://wikipedia.org/wiki/Nerva\" title=\"Nerva\"><PERSON><PERSON><PERSON></a> is forced by the <a href=\"https://wikipedia.org/wiki/Praetorian_Guard\" title=\"Praetorian Guard\">Praetorian Guard</a> to adopt general <a href=\"https://wikipedia.org/wiki/Trajan\" title=\"<PERSON>raj<PERSON>\"><PERSON></a> as his heir and successor.", "no_year_html": "Roman emperor <a href=\"https://wikipedia.org/wiki/Nerva\" title=\"Nerva\">Nerva</a> is forced by the <a href=\"https://wikipedia.org/wiki/Praetorian_Guard\" title=\"Praetorian Guard\">Praetorian Guard</a> to adopt general <a href=\"https://wikipedia.org/wiki/Trajan\" title=\"Traj<PERSON>\"><PERSON></a> as his heir and successor.", "links": [{"title": "Nerva", "link": "https://wikipedia.org/wiki/Nerva"}, {"title": "Praetorian Guard", "link": "https://wikipedia.org/wiki/Praetorian_Guard"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "306", "text": "<PERSON><PERSON><PERSON> is proclaimed Roman emperor.", "html": "306 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is proclaimed <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is proclaimed <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ent<PERSON>"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}]}, {"year": "312", "text": "<PERSON> I defeats <PERSON><PERSON><PERSON>, becoming the sole Roman emperor in the West.", "html": "312 - <PERSON> I <a href=\"https://wikipedia.org/wiki/Battle_of_the_Milvian_Bridge\" title=\"Battle of the Milvian Bridge\">defeats <PERSON><PERSON><PERSON></a>, becoming the sole Roman emperor in the West.", "no_year_html": "<PERSON> I <a href=\"https://wikipedia.org/wiki/Battle_of_the_Milvian_Bridge\" title=\"Battle of the Milvian Bridge\">defeats <PERSON><PERSON><PERSON></a>, becoming the sole Roman emperor in the West.", "links": [{"title": "Battle of the Milvian Bridge", "link": "https://wikipedia.org/wiki/Battle_of_the_Milvian_Bridge"}]}, {"year": "969", "text": "The Byzantine Empire recovers Antioch from Arab rule.", "html": "969 - The <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Antioch_(968%E2%80%93969)\" title=\"Siege of Antioch (968-969)\">recovers Antioch</a> from Arab rule.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Antioch_(968%E2%80%93969)\" title=\"Siege of Antioch (968-969)\">recovers Antioch</a> from Arab rule.", "links": [{"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Siege of Antioch (968-969)", "link": "https://wikipedia.org/wiki/Siege_of_Antioch_(968%E2%80%93969)"}]}, {"year": "1344", "text": "The lower town of Smyrna is captured by Latin Christians in response to Aydınid piracy during the Smyrniote crusades.", "html": "1344 - The lower town of Smyrna is captured by Latin Christians in response to <a href=\"https://wikipedia.org/wiki/Ayd%C4%B1nids\" class=\"mw-redirect\" title=\"Aydınids\">Aydınid</a> piracy during the <a href=\"https://wikipedia.org/wiki/Smyrniote_crusades\" title=\"Smyrniote crusades\">Smyrniote crusades</a>.", "no_year_html": "The lower town of Smyrna is captured by Latin Christians in response to <a href=\"https://wikipedia.org/wiki/Ayd%C4%B1nids\" class=\"mw-redirect\" title=\"Aydınids\">Aydınid</a> piracy during the <a href=\"https://wikipedia.org/wiki/Smyrniote_crusades\" title=\"Smyrniote crusades\">Smyrniote crusades</a>.", "links": [{"title": "Aydınids", "link": "https://wikipedia.org/wiki/Ayd%C4%B1nids"}, {"title": "Smyrniote crusades", "link": "https://wikipedia.org/wiki/Smyrniote_crusades"}]}, {"year": "1420", "text": "Beijing is officially designated the capital of the Ming dynasty when the Forbidden City is completed.", "html": "1420 - <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Beijing</a> is officially designated the capital of the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> when the <a href=\"https://wikipedia.org/wiki/Forbidden_City\" title=\"Forbidden City\">Forbidden City</a> is completed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Beijing</a> is officially designated the capital of the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> when the <a href=\"https://wikipedia.org/wiki/Forbidden_City\" title=\"Forbidden City\">Forbidden City</a> is completed.", "links": [{"title": "Beijing", "link": "https://wikipedia.org/wiki/Beijing"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "Forbidden City", "link": "https://wikipedia.org/wiki/Forbidden_City"}]}, {"year": "1449", "text": "<PERSON> is crowned king of Denmark.", "html": "1449 - <a href=\"https://wikipedia.org/wiki/Christian_I_of_Denmark\" title=\"Christian I of Denmark\"><PERSON> I</a> is crowned king of Denmark.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_I_of_Denmark\" title=\"Christian I of Denmark\"><PERSON> I</a> is crowned king of Denmark.", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_I_of_Denmark"}]}, {"year": "1453", "text": "<PERSON><PERSON><PERSON> the Posthumous is crowned king of Bohemia in Prague.", "html": "1453 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Posthumous\" title=\"<PERSON><PERSON><PERSON> the Posthumous\"><PERSON><PERSON><PERSON> the Posthumous</a> is crowned king of Bohemia in Prague.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Posthumous\" title=\"<PERSON><PERSON><PERSON> the Posthumous\"><PERSON><PERSON><PERSON> the Posthumous</a> is crowned king of Bohemia in Prague.", "links": [{"title": "<PERSON><PERSON><PERSON> the Posthumous", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Posthumous"}]}, {"year": "1492", "text": "<PERSON> lands in Cuba on his first voyage to the New World, surmising that it is Japan.", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> on his <a href=\"https://wikipedia.org/wiki/Voyages_of_<PERSON>_Columbus\" title=\"Voyages of Christopher Columbus\">first voyage to the New World</a>, surmising that it is Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Columbus\"><PERSON></a> lands in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> on his <a href=\"https://wikipedia.org/wiki/Voyages_of_Christopher_Columbus\" title=\"Voyages of Christopher Columbus\">first voyage to the New World</a>, surmising that it is Japan.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Voyages of <PERSON>", "link": "https://wikipedia.org/wiki/Voyages_of_<PERSON>_<PERSON>"}]}, {"year": "1516", "text": "Second Ottoman-Mamluk War: Mamluks fail to stop the Ottoman advance towards Egypt at the Battle of Yaunis Khan.", "html": "1516 - <a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Mamluk_War_(1516%E2%80%9317)\" class=\"mw-redirect\" title=\"Ottoman-Mamluk War (1516-17)\">Second Ottoman-Mamluk War</a>: Mamluks fail to stop the Ottoman advance towards Egypt at the <a href=\"https://wikipedia.org/wiki/Battle_of_Yaunis_Khan\" title=\"Battle of Yaunis Khan\">Battle of Yaunis Khan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Mamluk_War_(1516%E2%80%9317)\" class=\"mw-redirect\" title=\"Ottoman-Mamluk War (1516-17)\">Second Ottoman-Mamluk War</a>: Mamluks fail to stop the Ottoman advance towards Egypt at the <a href=\"https://wikipedia.org/wiki/Battle_of_Yaunis_Khan\" title=\"Battle of Yaunis Khan\">Battle of Yaunis Khan</a>.", "links": [{"title": "Ottoman-Mamluk War (1516-17)", "link": "https://wikipedia.org/wiki/Ottoman%E2%80%93Mamluk_War_(1516%E2%80%9317)"}, {"title": "Battle of Yaunis Khan", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1520", "text": "<PERSON> reaches the Pacific Ocean.", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> reaches the Pacific Ocean.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> reaches the Pacific Ocean.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1531", "text": "Abyssinian-Adal war: The Adal Sultanate seizes southern Ethiopia.", "html": "1531 - <a href=\"https://wikipedia.org/wiki/Abyssinian%E2%80%93Adal_war\" class=\"mw-redirect\" title=\"Abyssinian-Adal war\">Abyssinian-Adal war</a>: The <a href=\"https://wikipedia.org/wiki/Adal_Sultanate\" title=\"Adal Sultanate\">Adal Sultanate</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Amba_Sel\" title=\"Battle of Amba Sel\">seizes</a> southern Ethiopia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abyssinian%E2%80%93Adal_war\" class=\"mw-redirect\" title=\"Abyssinian-Adal war\">Abyssinian-Adal war</a>: The <a href=\"https://wikipedia.org/wiki/Adal_Sultanate\" title=\"Adal Sultanate\">Adal Sultanate</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Amba_Sel\" title=\"Battle of Amba Sel\">seizes</a> southern Ethiopia.", "links": [{"title": "Abyssinian-Adal war", "link": "https://wikipedia.org/wiki/Abyssinian%E2%80%93Adal_war"}, {"title": "Adal Sultanate", "link": "https://wikipedia.org/wiki/Adal_Sultanate"}, {"title": "Battle of Amba Sel", "link": "https://wikipedia.org/wiki/Battle_of_Amba_Sel"}]}, {"year": "1538", "text": "The Universidad Santo Tomás de Aquino is founded in what is now the Dominican Republic.", "html": "1538 - The <a href=\"https://wikipedia.org/wiki/Universidad_Santo_Tom%C3%A1s_de_Aquino\" title=\"Universidad Santo Tomás de Aquino\">Universidad Santo Tomás de Aquino</a> is founded in what is now the Dominican Republic.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Universidad_Santo_Tom%C3%A1s_de_Aquino\" title=\"Universidad Santo Tomás de Aquino\">Universidad Santo Tomás de Aquino</a> is founded in what is now the Dominican Republic.", "links": [{"title": "Universidad Santo Tomás de Aquino", "link": "https://wikipedia.org/wiki/Universidad_Santo_Tom%C3%<PERSON>s_<PERSON>_Aquino"}]}, {"year": "1628", "text": "French Wars of Religion: The Siege of La Rochelle ends with the surrender of the Huguenots after fourteen months.", "html": "1628 - <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_La_Rochelle\" title=\"Siege of La Rochelle\">Siege of La Rochelle</a> ends with the surrender of the Huguenots after fourteen months.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_La_Rochelle\" title=\"Siege of La Rochelle\">Siege of La Rochelle</a> ends with the surrender of the Huguenots after fourteen months.", "links": [{"title": "French Wars of Religion", "link": "https://wikipedia.org/wiki/French_Wars_of_Religion"}, {"title": "Siege of La Rochelle", "link": "https://wikipedia.org/wiki/Siege_of_La_Rochelle"}]}, {"year": "1636", "text": "The Massachusetts Bay Colony votes to establish a theological college, which would later become Harvard University.", "html": "1636 - The <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> votes to establish a theological college, which would later become <a href=\"https://wikipedia.org/wiki/Harvard_University\" title=\"Harvard University\">Harvard University</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> votes to establish a theological college, which would later become <a href=\"https://wikipedia.org/wiki/Harvard_University\" title=\"Harvard University\">Harvard University</a>.", "links": [{"title": "Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Bay_Colony"}, {"title": "Harvard University", "link": "https://wikipedia.org/wiki/Harvard_University"}]}, {"year": "1640", "text": "The Treaty of Ripon is signed, ending the hostilities of the Second Bishops’ War.", "html": "1640 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Ripon\" title=\"Treaty of Ripon\">Treaty of Ripon</a> is signed, ending the hostilities of the <a href=\"https://wikipedia.org/wiki/Second_Bishops%E2%80%99_War\" class=\"mw-redirect\" title=\"Second Bishops’ War\">Second Bishops’ War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Ripon\" title=\"Treaty of Ripon\">Treaty of Ripon</a> is signed, ending the hostilities of the <a href=\"https://wikipedia.org/wiki/Second_Bishops%E2%80%99_War\" class=\"mw-redirect\" title=\"Second Bishops’ War\">Second Bishops’ War</a>.", "links": [{"title": "Treaty of Ripon", "link": "https://wikipedia.org/wiki/Treaty_of_Ripon"}, {"title": "Second Bishops’ War", "link": "https://wikipedia.org/wiki/Second_Bishops%E2%80%99_War"}]}, {"year": "1664", "text": "The Duke of York and Albany's Maritime Regiment of Foot, later to be known as the Royal Marines, is established.", "html": "1664 - The Duke of York and Albany's Maritime Regiment of Foot, later to be known as the <a href=\"https://wikipedia.org/wiki/Royal_Marines\" title=\"Royal Marines\">Royal Marines</a>, is established.", "no_year_html": "The Duke of York and Albany's Maritime Regiment of Foot, later to be known as the <a href=\"https://wikipedia.org/wiki/Royal_Marines\" title=\"Royal Marines\">Royal Marines</a>, is established.", "links": [{"title": "Royal Marines", "link": "https://wikipedia.org/wiki/Royal_Marines"}]}, {"year": "1707", "text": "The 1707 Hōei earthquake causes more than 5,000 deaths in Japan.", "html": "1707 - The <a href=\"https://wikipedia.org/wiki/1707_H%C5%8Dei_earthquake\" title=\"1707 Hōei earthquake\">1707 Hōei earthquake</a> causes more than 5,000 deaths in Japan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1707_H%C5%8Dei_earthquake\" title=\"1707 Hōei earthquake\">1707 Hōei earthquake</a> causes more than 5,000 deaths in Japan.", "links": [{"title": "1707 Hōei earthquake", "link": "https://wikipedia.org/wiki/1707_H%C5%8Dei_earthquake"}]}, {"year": "1726", "text": "The novel <PERSON><PERSON><PERSON>'s Travels written by <PERSON> is published.", "html": "1726 - The novel <i><a href=\"https://wikipedia.org/wiki/G<PERSON>iver%27s_Travels\" title=\"<PERSON><PERSON><PERSON>'s Travels\"><PERSON><PERSON><PERSON>'s Travels</a></i> written by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is published.", "no_year_html": "The novel <i><a href=\"https://wikipedia.org/wiki/G<PERSON>iver%27s_Travels\" title=\"<PERSON><PERSON><PERSON>'s Travels\"><PERSON><PERSON><PERSON>'s Travels</a></i> written by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is published.", "links": [{"title": "<PERSON><PERSON><PERSON>'s Travels", "link": "https://wikipedia.org/wiki/Gulliver%27s_Travels"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "American Revolutionary War: British troops attack and capture Chatterton Hill from the Continental Army.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British troops <a href=\"https://wikipedia.org/wiki/Battle_of_White_Plains\" title=\"Battle of White Plains\">attack and capture</a> Chatterton Hill from the Continental Army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British troops <a href=\"https://wikipedia.org/wiki/Battle_of_White_Plains\" title=\"Battle of White Plains\">attack and capture</a> Chatterton Hill from the Continental Army.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of White Plains", "link": "https://wikipedia.org/wiki/Battle_of_White_Plains"}]}, {"year": "1834", "text": "The Pinjarra massacre occurs in the Swan River Colony. An estimated 30 Noongar people are killed by British colonists.", "html": "1834 - The <a href=\"https://wikipedia.org/wiki/Pinjarra_massacre\" title=\"Pinjarra massacre\">Pinjarra massacre</a> occurs in the Swan River Colony. An estimated 30 Noongar people are killed by British colonists.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pinjarra_massacre\" title=\"Pinjarra massacre\">Pinjarra massacre</a> occurs in the Swan River Colony. An estimated 30 Noongar people are killed by British colonists.", "links": [{"title": "Pinjarra massacre", "link": "https://wikipedia.org/wiki/Pinjarra_massacre"}]}, {"year": "1835", "text": "The United Tribes of New Zealand are established with the signature of the Declaration of Independence.", "html": "1835 - The <a href=\"https://wikipedia.org/wiki/United_Tribes_of_New_Zealand\" title=\"United Tribes of New Zealand\">United Tribes of New Zealand</a> are established with the signature of the <a href=\"https://wikipedia.org/wiki/Declaration_of_the_Independence_of_New_Zealand\" title=\"Declaration of the Independence of New Zealand\">Declaration of Independence</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Tribes_of_New_Zealand\" title=\"United Tribes of New Zealand\">United Tribes of New Zealand</a> are established with the signature of the <a href=\"https://wikipedia.org/wiki/Declaration_of_the_Independence_of_New_Zealand\" title=\"Declaration of the Independence of New Zealand\">Declaration of Independence</a>.", "links": [{"title": "United Tribes of New Zealand", "link": "https://wikipedia.org/wiki/United_Tribes_of_New_Zealand"}, {"title": "Declaration of the Independence of New Zealand", "link": "https://wikipedia.org/wiki/Declaration_of_the_Independence_of_New_Zealand"}]}, {"year": "1864", "text": "American Civil War: A Union attack on the Confederate capital of Richmond is repulsed.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: A <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Fair_Oaks_%26_Darbytown_Road\" title=\"Battle of Fair Oaks &amp; Darbytown Road\">attack</a> on the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> capital of <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond</a> is repulsed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: A <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Fair_Oaks_%26_Darbytown_Road\" title=\"Battle of Fair Oaks &amp; Darbytown Road\">attack</a> on the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> capital of <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond</a> is repulsed.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Battle of Fair Oaks & Darbytown Road", "link": "https://wikipedia.org/wiki/Battle_of_Fair_Oaks_%26_Darbytown_Road"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Richmond, Virginia", "link": "https://wikipedia.org/wiki/Richmond,_Virginia"}]}, {"year": "1886", "text": "US president <PERSON><PERSON> dedicates the Statue of Liberty.", "html": "1886 - US president <a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a> dedicates the <a href=\"https://wikipedia.org/wiki/Statue_of_Liberty\" title=\"Statue of Liberty\">Statue of Liberty</a>.", "no_year_html": "US president <a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a> dedicates the <a href=\"https://wikipedia.org/wiki/Statue_of_Liberty\" title=\"Statue of Liberty\">Statue of Liberty</a>.", "links": [{"title": "Grover <PERSON>", "link": "https://wikipedia.org/wiki/Grover_Cleveland"}, {"title": "Statue of Liberty", "link": "https://wikipedia.org/wiki/Statue_of_Liberty"}]}, {"year": "1891", "text": "The Mino-Owari earthquake, the largest inland earthquake in Japan's history, occurs.", "html": "1891 - The <a href=\"https://wikipedia.org/wiki/1891_Mino%E2%80%93Owari_earthquake\" title=\"1891 Mino-Owari earthquake\">Mino-Owari earthquake</a>, the largest inland earthquake in Japan's history, occurs.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1891_Mino%E2%80%93Owari_earthquake\" title=\"1891 Mino-Owari earthquake\">Mino-Owari earthquake</a>, the largest inland earthquake in Japan's history, occurs.", "links": [{"title": "1891 Mino-Owari earthquake", "link": "https://wikipedia.org/wiki/1891_Mino%E2%80%93Owari_earthquake"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>'s Symphony No. 6 in B Minor, Pathétique receives its première performance only nine days before the composer's death.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Symphony_No._6_(<PERSON><PERSON><PERSON><PERSON>)\" title=\"Symphony No. 6 (<PERSON><PERSON><PERSON><PERSON>)\">Symphony No. 6 in B Minor, <i>Pathétique</i></a> receives its première performance only nine days before the composer's death.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Symphony_No._6_(<PERSON><PERSON><PERSON><PERSON>)\" title=\"Symphony No. 6 (<PERSON><PERSON><PERSON><PERSON>)\">Symphony No. 6 in B Minor, <i>Pathétique</i></a> receives its première performance only nine days before the composer's death.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Symphony No. 6 (<PERSON><PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._6_(<PERSON><PERSON><PERSON><PERSON>)"}]}, {"year": "1918", "text": "World War I: A new Polish government in western Galicia is established, triggering the Polish-Ukrainian War.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: A new Polish government in western Galicia is established, triggering the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Ukrainian_War\" title=\"Polish-Ukrainian War\">Polish-Ukrainian War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: A new Polish government in western Galicia is established, triggering the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Ukrainian_War\" title=\"Polish-Ukrainian War\">Polish-Ukrainian War</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Polish-Ukrainian War", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Ukrainian_War"}]}, {"year": "1918", "text": "World War I: Czech politicians peacefully take over the city of Prague, thus establishing the First Czechoslovak Republic.", "html": "1918 - World War I: Czech politicians peacefully take over the city of Prague, thus establishing the <a href=\"https://wikipedia.org/wiki/First_Czechoslovak_Republic\" title=\"First Czechoslovak Republic\">First Czechoslovak Republic</a>.", "no_year_html": "World War I: Czech politicians peacefully take over the city of Prague, thus establishing the <a href=\"https://wikipedia.org/wiki/First_Czechoslovak_Republic\" title=\"First Czechoslovak Republic\">First Czechoslovak Republic</a>.", "links": [{"title": "First Czechoslovak Republic", "link": "https://wikipedia.org/wiki/First_Czechoslovak_Republic"}]}, {"year": "1919", "text": "The U.S. Congress passes the Volstead Act over President <PERSON>'s veto, paving the way for Prohibition to begin the following January.", "html": "1919 - The U.S. Congress passes the <a href=\"https://wikipedia.org/wiki/Volstead_Act\" title=\"Volstead Act\">Volstead Act</a> over President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s veto, paving the way for <a href=\"https://wikipedia.org/wiki/Prohibition_in_the_United_States\" title=\"Prohibition in the United States\">Prohibition</a> to begin the following January.", "no_year_html": "The U.S. Congress passes the <a href=\"https://wikipedia.org/wiki/Volstead_Act\" title=\"Volstead Act\">Volstead Act</a> over President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s veto, paving the way for <a href=\"https://wikipedia.org/wiki/Prohibition_in_the_United_States\" title=\"Prohibition in the United States\">Prohibition</a> to begin the following January.", "links": [{"title": "Volstead Act", "link": "https://wikipedia.org/wiki/Volstead_Act"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prohibition in the United States", "link": "https://wikipedia.org/wiki/Prohibition_in_the_United_States"}]}, {"year": "1922", "text": "Italian fascists led by <PERSON> march on Rome and take over the Italian government.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Italian_Fascism\" class=\"mw-redirect\" title=\"Italian Fascism\">Italian fascists</a> led by <PERSON> <a href=\"https://wikipedia.org/wiki/March_on_Rome\" title=\"March on Rome\">march on Rome</a> and take over the Italian government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italian_Fascism\" class=\"mw-redirect\" title=\"Italian Fascism\">Italian fascists</a> led by <PERSON> <a href=\"https://wikipedia.org/wiki/March_on_Rome\" title=\"March on Rome\">march on Rome</a> and take over the Italian government.", "links": [{"title": "Italian Fascism", "link": "https://wikipedia.org/wiki/Italian_Fascism"}, {"title": "March on Rome", "link": "https://wikipedia.org/wiki/March_on_Rome"}]}, {"year": "1928", "text": "Indonesia Raya, now the national anthem of Indonesia, is first played during the Second Indonesian Youth Congress.", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Indonesia_Raya\" title=\"Indonesia Raya\">Indonesia Raya</a>, now the national anthem of Indonesia, is first played during the <a href=\"https://wikipedia.org/wiki/Youth_Pledge\" title=\"Youth Pledge\">Second Indonesian Youth Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indonesia_Raya\" title=\"Indonesia Raya\">Indonesia Raya</a>, now the national anthem of Indonesia, is first played during the <a href=\"https://wikipedia.org/wiki/Youth_Pledge\" title=\"Youth Pledge\">Second Indonesian Youth Congress</a>.", "links": [{"title": "Indonesia Raya", "link": "https://wikipedia.org/wiki/Indonesia_Raya"}, {"title": "Youth Pledge", "link": "https://wikipedia.org/wiki/Youth_Pledge"}]}, {"year": "1940", "text": "World War II: Greece rejects Italy's ultimatum. Italy invades Greece through Albania a few hours later.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Greece <a href=\"https://wikipedia.org/wiki/Ohi_Day\" title=\"Ohi Day\">rejects</a> Italy's ultimatum. <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_Greece\" class=\"mw-redirect\" title=\"Italian invasion of Greece\">Italy invades Greece</a> through <a href=\"https://wikipedia.org/wiki/Italian_Albania\" class=\"mw-redirect\" title=\"Italian Albania\">Albania</a> a few hours later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Greece <a href=\"https://wikipedia.org/wiki/Ohi_Day\" title=\"Ohi Day\">rejects</a> Italy's ultimatum. <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_Greece\" class=\"mw-redirect\" title=\"Italian invasion of Greece\">Italy invades Greece</a> through <a href=\"https://wikipedia.org/wiki/Italian_Albania\" class=\"mw-redirect\" title=\"Italian Albania\">Albania</a> a few hours later.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Ohi Day", "link": "https://wikipedia.org/wiki/Ohi_Day"}, {"title": "Italian invasion of Greece", "link": "https://wikipedia.org/wiki/Italian_invasion_of_Greece"}, {"title": "Italian Albania", "link": "https://wikipedia.org/wiki/Italian_Albania"}]}, {"year": "1942", "text": "The Alaska Highway first connects Alaska to the North American railway network at Dawson Creek in Canada.", "html": "1942 - The <a href=\"https://wikipedia.org/wiki/Alaska_Highway\" title=\"Alaska Highway\">Alaska Highway</a> first connects Alaska to the North American railway network at <a href=\"https://wikipedia.org/wiki/Dawson_Creek\" title=\"Dawson Creek\">Dawson Creek</a> in Canada.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Alaska_Highway\" title=\"Alaska Highway\">Alaska Highway</a> first connects Alaska to the North American railway network at <a href=\"https://wikipedia.org/wiki/Dawson_Creek\" title=\"Dawson Creek\">Dawson Creek</a> in Canada.", "links": [{"title": "Alaska Highway", "link": "https://wikipedia.org/wiki/Alaska_Highway"}, {"title": "Dawson Creek", "link": "https://wikipedia.org/wiki/Dawson_Creek"}]}, {"year": "1948", "text": "<PERSON> is awarded the Nobel Prize in Physiology or Medicine for his discovery of the insecticidal properties of DDT.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a> is awarded the Nobel Prize in Physiology or Medicine for his discovery of the insecticidal properties of DDT.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a> is awarded the Nobel Prize in Physiology or Medicine for his discovery of the insecticidal properties of DDT.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller"}]}, {"year": "1948", "text": "Ecological disaster in Donora, Pennsylvania.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/1948_Donora_smog\" title=\"1948 Donora smog\">Ecological disaster</a> in <a href=\"https://wikipedia.org/wiki/Donora,_Pennsylvania\" title=\"Donora, Pennsylvania\">Donora, Pennsylvania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1948_Donora_smog\" title=\"1948 Donora smog\">Ecological disaster</a> in <a href=\"https://wikipedia.org/wiki/Donora,_Pennsylvania\" title=\"Donora, Pennsylvania\">Donora, Pennsylvania</a>.", "links": [{"title": "1948 Donora smog", "link": "https://wikipedia.org/wiki/1948_<PERSON><PERSON>_smog"}, {"title": "Donora, Pennsylvania", "link": "https://wikipedia.org/wiki/Donora,_Pennsylvania"}]}, {"year": "1949", "text": "An Air France Lockheed Constellation crashes in the Azores, killing all 48 people on board.", "html": "1949 - An <a href=\"https://wikipedia.org/wiki/Air_France\" title=\"Air France\">Air France</a> <a href=\"https://wikipedia.org/wiki/Lockheed_Constellation\" title=\"Lockheed Constellation\">Lockheed Constellation</a> <a href=\"https://wikipedia.org/wiki/Air_France_Flight_009\" title=\"Air France Flight 009\">crashes</a> in the Azores, killing all 48 people on board.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Air_France\" title=\"Air France\">Air France</a> <a href=\"https://wikipedia.org/wiki/Lockheed_Constellation\" title=\"Lockheed Constellation\">Lockheed Constellation</a> <a href=\"https://wikipedia.org/wiki/Air_France_Flight_009\" title=\"Air France Flight 009\">crashes</a> in the Azores, killing all 48 people on board.", "links": [{"title": "Air France", "link": "https://wikipedia.org/wiki/Air_France"}, {"title": "Lockheed Constellation", "link": "https://wikipedia.org/wiki/Lockheed_Constellation"}, {"title": "Air France Flight 009", "link": "https://wikipedia.org/wiki/Air_France_Flight_009"}]}, {"year": "1956", "text": "Hungarian Revolution: A de facto ceasefire comes into effect between armed revolutionaries and Soviet troops, who begin to withdraw from Budapest. Communist officials and facilities come under attack by revolutionaries.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>: A <i>de facto</i> ceasefire comes into effect between armed revolutionaries and Soviet troops, who begin to withdraw from Budapest. Communist officials and facilities come under attack by revolutionaries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>: A <i>de facto</i> ceasefire comes into effect between armed revolutionaries and Soviet troops, who begin to withdraw from Budapest. Communist officials and facilities come under attack by revolutionaries.", "links": [{"title": "Hungarian Revolution of 1956", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1956"}]}, {"year": "1954", "text": "Aeroflot Flight 136 crashes near Krasnoyarsk, killing 19.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_136\" title=\"Aeroflot Flight 136\">Aeroflot Flight 136</a> crashes near <a href=\"https://wikipedia.org/wiki/Krasnoyarsk\" title=\"Krasnoyarsk\">Krasnoyarsk</a>, killing 19.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_136\" title=\"Aeroflot Flight 136\">Aeroflot Flight 136</a> crashes near <a href=\"https://wikipedia.org/wiki/Krasnoyarsk\" title=\"Krasnoyarsk\">Krasnoyarsk</a>, killing 19.", "links": [{"title": "Aeroflot Flight 136", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_136"}, {"title": "Krasnoyarsk", "link": "https://wikipedia.org/wiki/Krasnoyarsk"}]}, {"year": "1958", "text": "<PERSON> is elected <PERSON>.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XXIII\" title=\"Pope John XXIII\">John XXIII</a> is <a href=\"https://wikipedia.org/wiki/1958_papal_conclave\" title=\"1958 papal conclave\">elected Pope</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XXIII\" title=\"Pope John XXIII\"><PERSON> XXIII</a> is <a href=\"https://wikipedia.org/wiki/1958_papal_conclave\" title=\"1958 papal conclave\">elected Pope</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>III"}, {"title": "1958 papal conclave", "link": "https://wikipedia.org/wiki/1958_papal_conclave"}]}, {"year": "1962", "text": "The Cuban Missile Crisis ends and Premier <PERSON><PERSON> orders the removal of Soviet missiles from Cuba.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Cuban_Missile_Crisis\" title=\"Cuban Missile Crisis\">Cuban Missile Crisis</a> ends and Premier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> orders the removal of Soviet missiles from Cuba.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cuban_Missile_Crisis\" title=\"Cuban Missile Crisis\">Cuban Missile Crisis</a> ends and Premier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> orders the removal of Soviet missiles from Cuba.", "links": [{"title": "Cuban Missile Crisis", "link": "https://wikipedia.org/wiki/Cuban_Missile_Crisis"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "Pope <PERSON> promulgates Nostra aetate, by which the Roman Catholic Church officially recognizes the legitimacy of non-Christian faiths.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul <PERSON>\">Pope <PERSON> VI</a> promulgates <i><a href=\"https://wikipedia.org/wiki/Nostra_aetate\" title=\"Nostra aetate\">Nostra aetate</a></i>, by which the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic Church</a> officially recognizes the legitimacy of non-Christian faiths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul <PERSON>\">Pope <PERSON> VI</a> promulgates <i><a href=\"https://wikipedia.org/wiki/Nostra_aetate\" title=\"Nostra aetate\">Nostra aetate</a></i>, by which the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic Church</a> officially recognizes the legitimacy of non-Christian faiths.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nostra aetate", "link": "https://wikipedia.org/wiki/Nostra_aetate"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "1971", "text": "Prospero becomes the only British satellite to be launched by a British rocket.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Prospero_(spacecraft)\" title=\"Prospero (spacecraft)\"><PERSON>spero</a> becomes the only British satellite to be launched by a British rocket.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prospero_(spacecraft)\" title=\"Prospero (spacecraft)\"><PERSON>spero</a> becomes the only British satellite to be launched by a British rocket.", "links": [{"title": "<PERSON><PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/Prospero_(spacecraft)"}]}, {"year": "1982", "text": "The Spanish general election begins fourteen years of rule by the Spanish Socialist Workers' Party.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/1982_Spanish_general_election\" title=\"1982 Spanish general election\">Spanish general election</a> begins fourteen years of rule by the <a href=\"https://wikipedia.org/wiki/Spanish_Socialist_Workers%27_Party\" title=\"Spanish Socialist Workers' Party\">Spanish Socialist Workers' Party</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1982_Spanish_general_election\" title=\"1982 Spanish general election\">Spanish general election</a> begins fourteen years of rule by the <a href=\"https://wikipedia.org/wiki/Spanish_Socialist_Workers%27_Party\" title=\"Spanish Socialist Workers' Party\">Spanish Socialist Workers' Party</a>.", "links": [{"title": "1982 Spanish general election", "link": "https://wikipedia.org/wiki/1982_Spanish_general_election"}, {"title": "Spanish Socialist Workers' Party", "link": "https://wikipedia.org/wiki/Spanish_Socialist_Workers%27_Party"}]}, {"year": "1989", "text": "Aloha Island Air Flight 1712, a Twin Otter 300, crashed into terrain at night in Hawaii killing all 20 occupants onboard.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Aloha_Island_Air_Flight_1712\" class=\"mw-redirect\" title=\"Aloha Island Air Flight 1712\">Aloha Island Air Flight 1712</a>, a <a href=\"https://wikipedia.org/wiki/De_Havilland_Canada_DHC-6_Twin_Otter\" title=\"De Havilland Canada DHC-6 Twin Otter\">Twin Otter 300</a>, crashed into terrain at night in Hawaii killing all 20 occupants onboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aloha_Island_Air_Flight_1712\" class=\"mw-redirect\" title=\"Aloha Island Air Flight 1712\">Aloha Island Air Flight 1712</a>, a <a href=\"https://wikipedia.org/wiki/De_Havilland_Canada_DHC-6_Twin_Otter\" title=\"De Havilland Canada DHC-6 Twin Otter\">Twin Otter 300</a>, crashed into terrain at night in Hawaii killing all 20 occupants onboard.", "links": [{"title": "Aloha Island Air Flight 1712", "link": "https://wikipedia.org/wiki/Aloha_Island_Air_Flight_1712"}, {"title": "De Havilland Canada DHC-6 Twin Otter", "link": "https://wikipedia.org/wiki/<PERSON>_Havilland_Canada_DHC-6_<PERSON>_<PERSON>tter"}]}, {"year": "1990", "text": "Georgia holds its only free election under Soviet rule.", "html": "1990 - Georgia holds its <a href=\"https://wikipedia.org/wiki/1990_Georgian_Supreme_Soviet_election\" title=\"1990 Georgian Supreme Soviet election\">only free election</a> under Soviet rule.", "no_year_html": "Georgia holds its <a href=\"https://wikipedia.org/wiki/1990_Georgian_Supreme_Soviet_election\" title=\"1990 Georgian Supreme Soviet election\">only free election</a> under Soviet rule.", "links": [{"title": "1990 Georgian Supreme Soviet election", "link": "https://wikipedia.org/wiki/1990_Georgian_Supreme_Soviet_election"}]}, {"year": "1995", "text": "The Baku Metro fire sees 289 people killed and 270 injured.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/1995_Baku_Metro_fire\" title=\"1995 Baku Metro fire\">Baku Metro fire</a> sees 289 people killed and 270 injured.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1995_Baku_Metro_fire\" title=\"1995 Baku Metro fire\">Baku Metro fire</a> sees 289 people killed and 270 injured.", "links": [{"title": "1995 Baku Metro fire", "link": "https://wikipedia.org/wiki/1995_Baku_Metro_fire"}]}, {"year": "2006", "text": "A funeral service takes place at the Bykivnia graves for Ukrainians who were killed by the Soviet secret police.", "html": "2006 - A funeral service takes place at the <a href=\"https://wikipedia.org/wiki/Bykivnia_graves\" title=\"Bykivnia graves\">Bykivnia graves</a> for Ukrainians who were killed by the Soviet secret police.", "no_year_html": "A funeral service takes place at the <a href=\"https://wikipedia.org/wiki/Bykivnia_graves\" title=\"Bykivnia graves\">Bykivnia graves</a> for Ukrainians who were killed by the Soviet secret police.", "links": [{"title": "Bykivnia graves", "link": "https://wikipedia.org/wiki/Bykivnia_graves"}]}, {"year": "2007", "text": "<PERSON> becomes the first directly elected female President of Argentina.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first directly elected female <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first directly elected female <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cristina_Fern%C3%A1<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "2009", "text": "The 28 October 2009 Peshawar bombing kills 117 and wounds 213.", "html": "2009 - The <a href=\"https://wikipedia.org/wiki/28_October_2009_Peshawar_bombing\" title=\"28 October 2009 Peshawar bombing\">28 October 2009 Peshawar bombing</a> kills 117 and wounds 213.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/28_October_2009_Peshawar_bombing\" title=\"28 October 2009 Peshawar bombing\">28 October 2009 Peshawar bombing</a> kills 117 and wounds 213.", "links": [{"title": "28 October 2009 Peshawar bombing", "link": "https://wikipedia.org/wiki/28_October_2009_Peshawar_bombing"}]}, {"year": "2009", "text": "NASA successfully launches the Ares I-X mission, the only rocket launch for its short-lived Constellation program.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> successfully launches the <a href=\"https://wikipedia.org/wiki/Ares_I-X\" title=\"Ares I-X\">Ares I-X</a> mission, the only rocket launch for its short-lived <a href=\"https://wikipedia.org/wiki/Constellation_program\" title=\"Constellation program\">Constellation program</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> successfully launches the <a href=\"https://wikipedia.org/wiki/Ares_I-X\" title=\"Ares I-X\">Ares I-X</a> mission, the only rocket launch for its short-lived <a href=\"https://wikipedia.org/wiki/Constellation_program\" title=\"Constellation program\">Constellation program</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Ares I-X", "link": "https://wikipedia.org/wiki/Ares_I-X"}, {"title": "Constellation program", "link": "https://wikipedia.org/wiki/Constellation_program"}]}, {"year": "2009", "text": "US President <PERSON> signs the <PERSON> and <PERSON> Jr. Hate Crimes Prevention Act.", "html": "2009 - US President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>_<PERSON>._Hate_Crimes_Prevention_Act\" title=\"<PERSON> and <PERSON> Jr. Hate Crimes Prevention Act\"><PERSON> and <PERSON> Jr. Hate Crimes Prevention Act</a>.", "no_year_html": "US President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>_<PERSON>._Hate_Crimes_Prevention_Act\" title=\"<PERSON> and <PERSON> Jr. Hate Crimes Prevention Act\"><PERSON> and <PERSON> Jr. Hate Crimes Prevention Act</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barack<PERSON>Obama"}, {"title": "<PERSON> and <PERSON> Jr. Hate Crimes Prevention Act", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>_<PERSON>._Hate_Crimes_Prevention_Act"}]}, {"year": "2013", "text": "Five people are killed and 38 are injured after a car crashes into barriers at Tiananmen Square in China.", "html": "2013 - Five people are killed and 38 are injured after a <a href=\"https://wikipedia.org/wiki/2013_Tiananmen_Square_attack\" title=\"2013 Tiananmen Square attack\">car crashes</a> into barriers at <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a> in China.", "no_year_html": "Five people are killed and 38 are injured after a <a href=\"https://wikipedia.org/wiki/2013_Tiananmen_Square_attack\" title=\"2013 Tiananmen Square attack\">car crashes</a> into barriers at <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a> in China.", "links": [{"title": "2013 Tiananmen Square attack", "link": "https://wikipedia.org/wiki/2013_Tiananmen_Square_attack"}, {"title": "Tiananmen Square", "link": "https://wikipedia.org/wiki/Tiananmen_Square"}]}, {"year": "2014", "text": "A rocket carrying NASA's Cygnus CRS Orb-3 resupply mission to the International Space Station explodes seconds after taking off from the Mid-Atlantic Regional Spaceport in Wallops Island, Virginia.", "html": "2014 - A rocket carrying NASA's <a href=\"https://wikipedia.org/wiki/Cygnus_Orb-3\" title=\"Cygnus Orb-3\">Cygnus CRS Orb-3</a> resupply mission to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a> explodes seconds after taking off from the <a href=\"https://wikipedia.org/wiki/Mid-Atlantic_Regional_Spaceport\" title=\"Mid-Atlantic Regional Spaceport\">Mid-Atlantic Regional Spaceport</a> in <a href=\"https://wikipedia.org/wiki/Wallops_Island\" title=\"Wallops Island\">Wallops Island, Virginia</a>.", "no_year_html": "A rocket carrying NASA's <a href=\"https://wikipedia.org/wiki/Cygnus_Orb-3\" title=\"Cygnus Orb-3\">Cygnus CRS Orb-3</a> resupply mission to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a> explodes seconds after taking off from the <a href=\"https://wikipedia.org/wiki/Mid-Atlantic_Regional_Spaceport\" title=\"Mid-Atlantic Regional Spaceport\">Mid-Atlantic Regional Spaceport</a> in <a href=\"https://wikipedia.org/wiki/Wallops_Island\" title=\"Wallops Island\">Wallops Island, Virginia</a>.", "links": [{"title": "Cygnus Orb-3", "link": "https://wikipedia.org/wiki/Cygnus_Orb-3"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}, {"title": "Mid-Atlantic Regional Spaceport", "link": "https://wikipedia.org/wiki/Mid-Atlantic_Regional_Spaceport"}, {"title": "Wallops Island", "link": "https://wikipedia.org/wiki/Wallops_Island"}]}, {"year": "2018", "text": "<PERSON><PERSON> is elected president of Brazil with 57 million votes, with Workers' Party candidate <PERSON> as the runner-up. It is the first time in 16 years that a Workers' Party candidate is not elected president.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">Jai<PERSON></a> is <a href=\"https://wikipedia.org/wiki/2018_Brazilian_general_election\" title=\"2018 Brazilian general election\">elected</a> <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">president of</a> <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> with 57 million votes, with <a href=\"https://wikipedia.org/wiki/Workers%27_Party_(Brazil)\" title=\"Workers' Party (Brazil)\">Workers' Party</a> candidate <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the runner-up. It is the first time in 16 years that a Workers' Party candidate is not elected president.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2018_Brazilian_general_election\" title=\"2018 Brazilian general election\">elected</a> <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">president of</a> <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> with 57 million votes, with <a href=\"https://wikipedia.org/wiki/Workers%27_Party_(Brazil)\" title=\"Workers' Party (Brazil)\">Workers' Party</a> candidate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the runner-up. It is the first time in 16 years that a Workers' Party candidate is not elected president.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "2018 Brazilian general election", "link": "https://wikipedia.org/wiki/2018_Brazilian_general_election"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}, {"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "Workers' Party (Brazil)", "link": "https://wikipedia.org/wiki/Workers%27_Party_(Brazil)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "The 2023 Rugby World Cup final is held at the Stade de France in Saint-Denis, France. It saw South Africa defeat New Zealand 11 to 12, claiming their fourth Webb Ellis Cup, becoming the first nation to do so.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/2023_Rugby_World_Cup_final\" title=\"2023 Rugby World Cup final\">2023 Rugby World Cup final</a> is held at the <a href=\"https://wikipedia.org/wiki/Stade_de_France\" title=\"Stade de France\">Stade de France</a> in <a href=\"https://wikipedia.org/wiki/Saint-Denis,_Seine-Saint-Denis\" title=\"Saint-Denis, Seine-Saint-Denis\"><PERSON>-Denis</a>, <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>. It saw <a href=\"https://wikipedia.org/wiki/South_Africa_national_rugby_union_team\" title=\"South Africa national rugby union team\">South Africa</a> defeat <a href=\"https://wikipedia.org/wiki/New_Zealand_national_rugby_union_team\" title=\"New Zealand national rugby union team\">New Zealand</a> 11 to 12, claiming their fourth <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cup\" title=\"Webb Ellis Cup\">Webb Ellis Cup</a>, becoming the first nation to do so.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2023_Rugby_World_Cup_final\" title=\"2023 Rugby World Cup final\">2023 Rugby World Cup final</a> is held at the <a href=\"https://wikipedia.org/wiki/Stade_de_France\" title=\"Stade de France\">Stade de France</a> in <a href=\"https://wikipedia.org/wiki/Saint-Denis,_Seine-Saint-Denis\" title=\"Saint-Denis, Seine-Saint-Denis\">Saint-Denis</a>, <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>. It saw <a href=\"https://wikipedia.org/wiki/South_Africa_national_rugby_union_team\" title=\"South Africa national rugby union team\">South Africa</a> defeat <a href=\"https://wikipedia.org/wiki/New_Zealand_national_rugby_union_team\" title=\"New Zealand national rugby union team\">New Zealand</a> 11 to 12, claiming their fourth <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Webb Ellis Cup\">Webb Ellis Cup</a>, becoming the first nation to do so.", "links": [{"title": "2023 Rugby World Cup final", "link": "https://wikipedia.org/wiki/2023_Rugby_World_Cup_final"}, {"title": "Stade de France", "link": "https://wikipedia.org/wiki/Stade_de_France"}, {"title": "Saint-Denis, Seine-Saint-Denis", "link": "https://wikipedia.org/wiki/<PERSON>-Denis,_Seine-Saint-Denis"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "South Africa national rugby union team", "link": "https://wikipedia.org/wiki/South_Africa_national_rugby_union_team"}, {"title": "New Zealand national rugby union team", "link": "https://wikipedia.org/wiki/New_Zealand_national_rugby_union_team"}, {"title": "<PERSON> Ellis Cup", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}], "Births": [{"year": "1017", "text": "<PERSON>, Holy Roman Emperor (d. 1056)", "html": "1017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1056)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1056)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1466", "text": "<PERSON><PERSON>, Dutch philosopher (d. 1536)", "html": "1466 - <a href=\"https://wikipedia.org/wiki/Erasmus\" title=\"Era<PERSON>\"><PERSON><PERSON></a>, Dutch philosopher (d. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erasmus\" title=\"Era<PERSON>\"><PERSON><PERSON></a>, Dutch philosopher (d. 1536)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erasmus"}]}, {"year": "1479", "text": "<PERSON>, English courtier (d. 1556)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Tudor_politician)\" title=\"<PERSON> (Tudor politician)\"><PERSON></a>, English courtier (d. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Tudor_politician)\" title=\"<PERSON> (Tudor politician)\"><PERSON></a>, English courtier (d. 1556)", "links": [{"title": "<PERSON> (Tudor politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Tudor_politician)"}]}, {"year": "1510", "text": "<PERSON>, 4th Duke of Gandía, Spanish priest and saint, 3rd Superior General of the Society of Jesus (d. 1572)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Gand%C3%ADa\" class=\"mw-redirect\" title=\"<PERSON>, 4th Duke of Gandía\"><PERSON>, 4th Duke of Gandía</a>, Spanish priest and saint, 3rd <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Gand%C3%ADa\" class=\"mw-redirect\" title=\"<PERSON>, 4th Duke of Gandía\"><PERSON>, 4th Duke of Gandía</a>, Spanish priest and saint, 3rd <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (d. 1572)", "links": [{"title": "<PERSON>, 4th Duke of Gandía", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Gand%C3%ADa"}, {"title": "Superior General of the Society of Jesus", "link": "https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus"}]}, {"year": "1550", "text": "<PERSON><PERSON><PERSON>, Polish saint (d. 1568)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish saint (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish saint (d. 1568)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1585", "text": "<PERSON>, Dutch bishop and theologian (d. 1638)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch bishop and theologian (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch bishop and theologian (d. 1638)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1599", "text": "<PERSON> of the Incarnation, foundress of the Ursuline Monastery in Quebec (d. 1672)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Incarnation_(Ursuline)\" title=\"Marie of the Incarnation (Ursuline)\"><PERSON> of the Incarnation</a>, foundress of the Ursuline Monastery in Quebec (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Incarnation_(Ursuline)\" title=\"Marie of the Incarnation (Ursuline)\"><PERSON> of the Incarnation</a>, foundress of the Ursuline Monastery in Quebec (d. 1672)", "links": [{"title": "Marie of the Incarnation (Ursuline)", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Incarnation_(<PERSON><PERSON><PERSON><PERSON>)"}]}, {"year": "1610", "text": "<PERSON>, 3rd duke of Courland and Semigallia (d. 1682)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 3rd <a href=\"https://wikipedia.org/wiki/Duchy_of_Courland_and_Semigallia\" title=\"Duchy of Courland and Semigallia\">duke of Courland and Semigallia</a> (d. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 3rd <a href=\"https://wikipedia.org/wiki/Duchy_of_Courland_and_Semigallia\" title=\"Duchy of Courland and Semigallia\">duke of Courland and Semigallia</a> (d. 1682)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Duchy of Courland and Semigallia", "link": "https://wikipedia.org/wiki/Duchy_of_Courland_and_Semigallia"}]}, {"year": "1667", "text": "<PERSON> of Neuburg, Queen consort of Spain", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Neuburg\" title=\"<PERSON> of Neuburg\"><PERSON> of Neuburg</a>, Queen consort of Spain", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Neuburg\" title=\"<PERSON> of Neuburg\"><PERSON> of Neuburg</a>, Queen consort of Spain", "links": [{"title": "<PERSON> of Neuburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Neuburg"}]}, {"year": "1690", "text": "<PERSON>, Norwegian admiral (d. 1720)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian admiral (d. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian admiral (d. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1693", "text": "<PERSON><PERSON><PERSON>, Czech composer (d. 1735)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/%C5%A0imon_Brixi\" title=\"Šimon Brixi\"><PERSON><PERSON><PERSON></a>, Czech composer (d. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%A0imon_Brixi\" title=\"Šimon Brixi\"><PERSON><PERSON><PERSON></a>, Czech composer (d. 1735)", "links": [{"title": "Šimon Brixi", "link": "https://wikipedia.org/wiki/%C5%A0imon_Brixi"}]}, {"year": "1696", "text": "<PERSON>, French general (d. 1750)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1703", "text": "<PERSON>, French mathematician and engineer (d. 1768)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and engineer (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and engineer (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1718", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian priest, mathematician, astronomer, and explorer (d. 1793)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/Igna<PERSON><PERSON>_Szentmartony\" title=\"Igna<PERSON>je Szentmartony\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian priest, mathematician, astronomer, and explorer (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON><PERSON>_<PERSON>zentmartony\" title=\"<PERSON>gna<PERSON><PERSON>tmart<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian priest, mathematician, astronomer, and explorer (d. 1793)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Igna<PERSON>je_Szentmartony"}]}, {"year": "1733", "text": "<PERSON>, German composer (d. 1803)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, American soldier (d. 1782)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1782)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "<PERSON> of Hesse-Kassel (d. 1852)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Hesse-Kassel\" title=\"<PERSON> of Hesse-Kassel\"><PERSON> of Hesse-Kassel</a> (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Hesse-Kassel\" title=\"<PERSON> of Hesse-Kassel\"><PERSON> of Hesse-Kassel</a> (d. 1852)", "links": [{"title": "Marie of Hesse-Kassel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hesse-Kassel"}]}, {"year": "1793", "text": "<PERSON><PERSON><PERSON> Remington, American businessman, founded Remington Arms (d. 1861)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/Eliphalet_Remington\" title=\"Eliphalet Remington\"><PERSON><PERSON><PERSON> Remington</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Remington_Arms\" title=\"Remington Arms\">Remington Arms</a> (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eliphalet_Remington\" title=\"Eliphalet Remington\"><PERSON><PERSON><PERSON> Remington</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Remington_Arms\" title=\"Remington Arms\">Remington Arms</a> (d. 1861)", "links": [{"title": "Eliphalet Remington", "link": "https://wikipedia.org/wiki/Eliphalet_Remington"}, {"title": "Remington Arms", "link": "https://wikipedia.org/wiki/Remington_Arms"}]}, {"year": "1794", "text": "<PERSON>, Scottish surgeon (d. 1847)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon (d. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, Belgian mathematician and theorist (d. 1849)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois_V<PERSON>hulst\" title=\"<PERSON>\"><PERSON></a>, Belgian mathematician and theorist (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois_Verhulst\" title=\"<PERSON>\"><PERSON></a>, Belgian mathematician and theorist (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>hulst"}]}, {"year": "1815", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Slovak philologist and politician (d. 1856)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/%C4%BDudov%C3%ADt_%C5%A0t%C3%BAr\" title=\"<PERSON>udo<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak philologist and politician (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%BDudov%C3%ADt_%C5%A0t%C3%BAr\" title=\"<PERSON>udo<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak philologist and politician (d. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%BDudov%C3%ADt_%C5%A0t%C3%BAr"}]}, {"year": "1816", "text": "<PERSON><PERSON><PERSON>, German writer (d. 1903)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>bu<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>bug\"><PERSON><PERSON><PERSON></a>, German writer (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>bu<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>bug\"><PERSON><PERSON><PERSON></a>, German writer (d. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, Japanese shōgun (d. 1913)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Tokugawa_Yoshinobu\" title=\"Tokugawa Yoshinobu\"><PERSON></a>, Japanese shōgun (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_Yoshinobu\" title=\"Tokugawa Yoshinobu\"><PERSON></a>, Japanese shōgun (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1839", "text": "<PERSON>, American captain, lawyer, and politician (d. 1909)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, Polish physicist and chemist (d. 1888)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Zygmunt_Florenty_Wr%C3%B3<PERSON><PERSON>\" title=\"Zygmunt Florenty <PERSON>\">Zygmunt <PERSON>lore<PERSON><PERSON></a>, Polish physicist and chemist (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zygmunt_Florenty_Wr%C3%B3<PERSON><PERSON>\" title=\"Zygmunt Florenty W<PERSON>\">Zygmunt <PERSON>lore<PERSON><PERSON></a>, Polish physicist and chemist (d. 1888)", "links": [{"title": "<PERSON><PERSON>g<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zygmunt_Florenty_Wr%C3%B3<PERSON><PERSON>"}]}, {"year": "1846", "text": "<PERSON>, French chef and author (d. 1935)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef and author (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef and author (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, French philosopher and poet (d. 1888)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and poet (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and poet (d. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, Japanese martial artist (d. 1938)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Kan%C5%8D_Jigor%C5%8D\" title=\"Kan<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kan%C5%8D_Jigor%C5%8D\" title=\"Kan<PERSON>gor<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist (d. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kan%C5%8D_<PERSON>gor%C5%8D"}]}, {"year": "1864", "text": "<PERSON><PERSON>, Mexican-American rancher and philanthropist (d. 1958)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American rancher and philanthropist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American rancher and philanthropist (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "Sister <PERSON><PERSON><PERSON>, Irish-Indian nurse, author, and educator (d. 1911)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Sister_<PERSON><PERSON><PERSON>\" title=\"Sister <PERSON><PERSON><PERSON>\">Sister <PERSON><PERSON><PERSON></a>, Irish-Indian nurse, author, and educator (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sister_<PERSON><PERSON><PERSON>\" title=\"Sister <PERSON><PERSON><PERSON>\">Sister <PERSON><PERSON><PERSON></a>, Irish-Indian nurse, author, and educator (d. 1911)", "links": [{"title": "Sister <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Turkish-Canadian journalist (d. 1966)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>venor\" title=\"<PERSON>\"><PERSON></a>, Turkish-Canadian journalist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>venor\" title=\"<PERSON>\"><PERSON></a>, Turkish-Canadian journalist (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Grosvenor"}]}, {"year": "1877", "text": "<PERSON>, American baseball player and manager (d. 1952)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 1952)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1879", "text": "<PERSON><PERSON> <PERSON><PERSON>, American lawyer and politician, 49th Governor of Massachusetts (d. 1968)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician, 49th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician, 49th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1968)", "links": [{"title": "Channing <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1880", "text": "<PERSON>, Belarusian-Estonian astrophysicist and astronomer (d. 1940)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Estonian astrophysicist and astronomer (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Estonian astrophysicist and astronomer (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, English footballer (d. 1965)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Australian footballer (d. 1951)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, New Zealand horticulturalist, founded Eastwoodhill Arboretum (d. 1967)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand horticulturalist, founded <a href=\"https://wikipedia.org/wiki/Eastwoodhill_Arboretum\" title=\"Eastwoodhill Arboretum\">Eastwoodhill Arboretum</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand horticulturalist, founded <a href=\"https://wikipedia.org/wiki/Eastwoodhill_Arboretum\" title=\"Eastwoodhill Arboretum\">Eastwoodhill Arboretum</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Eastwoodhill Arboretum", "link": "https://wikipedia.org/wiki/Eastwoodhill_Arboretum"}]}, {"year": "1886", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, British archaeologist (d. 1957)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/O._G._<PERSON><PERSON>_<PERSON>\" title=\"O. G. S. <PERSON>\">O. G. <PERSON><PERSON></a>, British archaeologist (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O._G._<PERSON><PERSON>_<PERSON>\" title=\"O. G. S. Crawford\">O. G. <PERSON><PERSON></a>, British archaeologist (d. 1957)", "links": [{"title": "O. G<PERSON> <PERSON><PERSON> Crawford", "link": "https://wikipedia.org/wiki/O._G._<PERSON><PERSON>_Crawford"}]}, {"year": "1886", "text": "<PERSON>, English soldier and engineer (d. 1946)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and engineer (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and engineer (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, 10th Baron <PERSON>, English soldier and politician, Lord Lieutenant of Durham (d. 1964)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Baron_<PERSON>\" title=\"<PERSON>, 10th Baron <PERSON>\"><PERSON>, 10th Baron <PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Durham\" title=\"Lord Lieutenant of Durham\">Lord Lieutenant of Durham</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Baron_<PERSON>\" title=\"<PERSON>, 10th Baron <PERSON>\"><PERSON>, 10th Baron <PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Durham\" title=\"Lord Lieutenant of Durham\">Lord Lieutenant of Durham</a> (d. 1964)", "links": [{"title": "<PERSON>, 10th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_10th_Baron_<PERSON>"}, {"title": "Lord Lieutenant of Durham", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Durham"}]}, {"year": "1889", "text": "<PERSON>, Canadian actress and singer (d. 1975)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Juliette_B%C3%A9liveau\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juliette_B%C3%A9liveau\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juliette_B%C3%A9liveau"}]}, {"year": "1892", "text": "<PERSON><PERSON>, American pianist, drummer, and clarinet player (d. 1954)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, drummer, and clarinet player (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, drummer, and clarinet player (d. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, British chemist (d. 1970)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chemist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chemist (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gold"}]}, {"year": "1896", "text": "<PERSON>, American composer, conductor, and educator (d. 1981)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, conductor, and educator (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, conductor, and educator (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American costume designer (d. 1981)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American costume designer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Head\"><PERSON></a>, American costume designer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German general (d. 1984)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Italian partigiano and priest (d. 1969)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Am<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>bro<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian partigiano and priest (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian partigiano and priest (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Irish poet (d. 1979)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English-American actress and singer (d. 1986)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> La<PERSON>\"><PERSON></a>, English-American actress and singer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> La<PERSON>\"><PERSON></a>, English-American actress and singer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elsa_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American historian, journalist, and critic (d. 1995)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American historian, journalist, and critic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American historian, journalist, and critic (d. 1995)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1903", "text": "<PERSON>, English journalist, author, and critic (d. 1966)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and critic (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and critic (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English-American historian, journalist, and author (d. 1986)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American historian, journalist, and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American historian, journalist, and author (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, Dutch mathematician (d. 1984)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Taty<PERSON>_<PERSON>fest\" title=\"Ta<PERSON><PERSON> E<PERSON>fest\"><PERSON><PERSON><PERSON></a>, Dutch mathematician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taty<PERSON>_<PERSON>fest\" title=\"Taty<PERSON> E<PERSON>fest\"><PERSON><PERSON><PERSON></a>, Dutch mathematician (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>fest"}]}, {"year": "1907", "text": "<PERSON>, Irish poet, playwright, and critic (d. 1987)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Irish poet, playwright, and critic (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Irish poet, playwright, and critic (d. 1987)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1908", "text": "<PERSON>, Argentinian lawyer and politician, 32nd President of Argentina (d. 1995)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ondiz<PERSON>"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1909", "text": "<PERSON>, Irish painter and illustrator (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Irish painter and illustrator (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Irish painter and illustrator (d. 1992)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1912", "text": "<PERSON>, English physiologist and epidemiologist (d. 2005)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Doll\"><PERSON></a>, English physiologist and epidemiologist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Doll\"><PERSON></a>, English physiologist and epidemiologist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American lieutenant and politician (d. 1988)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American biologist and physician (d. 1995)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and physician (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and physician (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English biochemist and academic, Nobel Prize laureate (d. 1994)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Synge\" title=\"<PERSON> Synge\"><PERSON>ynge</a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Synge\" title=\"<PERSON> Synge\"><PERSON>ynge</a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1994)", "links": [{"title": "<PERSON> Synge", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Synge"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1916", "text": "<PERSON>, English actress (d. 2009)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actor and singer (d. 1979)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American race car driver (d. 1966)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, German race car driver (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 40th <PERSON><PERSON><PERSON><PERSON> (d. 1973)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/A<PERSON>ma<PERSON>ji_Kin%27ichi\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 40th <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yokozuna (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>ji_Kin%27ichi\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 40th <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yokozuna (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Azumafuji_Kin%27ichi"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (sumo)", "link": "https://wikipedia.org/wiki/Yokozuna_(sumo)"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, German-American pianist, composer, and conductor (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American pianist, composer, and conductor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American pianist, composer, and conductor (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Zimbabwe politician, 1st Vice President of Zimbabwe (d. 2003)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwe politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_President_of_Zimbabwe\" class=\"mw-redirect\" title=\"Vice President of Zimbabwe\">Vice President of Zimbabwe</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwe politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_President_of_Zimbabwe\" class=\"mw-redirect\" title=\"Vice President of Zimbabwe\">Vice President of Zimbabwe</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of Zimbabwe", "link": "https://wikipedia.org/wiki/Vice_President_of_Zimbabwe"}]}, {"year": "1922", "text": "<PERSON>, American basketball player and coach (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actor (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2015)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1924", "text": "<PERSON>, Spanish race car driver and motorcycle racer (d. 1996)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver and motorcycle racer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver and motorcycle racer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Telugu actress (d. 1994)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(actress)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (actress)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Telugu actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(actress)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (actress)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Telugu actress (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(actress)"}]}, {"year": "1925", "text": "<PERSON>, Bahamian-Scottish poet, sculptor, and gardener (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian-Scottish poet, sculptor, and gardener (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian-Scottish poet, sculptor, and gardener (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American lawyer and businessman, 5th Commissioner of Baseball (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman, 5th <a href=\"https://wikipedia.org/wiki/Commissioner_of_Baseball\" title=\"Commissioner of Baseball\">Commissioner of Baseball</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman, 5th <a href=\"https://wikipedia.org/wiki/Commissioner_of_Baseball\" title=\"Commissioner of Baseball\">Commissioner of Baseball</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Commissioner of Baseball", "link": "https://wikipedia.org/wiki/Commissioner_of_Baseball"}]}, {"year": "1927", "text": "<PERSON><PERSON>, English singer and actress", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Romanian general (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian general (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian general (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, <PERSON> of Quarry Bank, English politician, Shadow Secretary of State for Defence", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Quarry_Bank\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Quarry Bank\"><PERSON>, Baron <PERSON> of Quarry Bank</a>, English politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence\" title=\"Shadow Secretary of State for Defence\">Shadow Secretary of State for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Quarry_Bank\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Quarry Bank\"><PERSON>, Baron <PERSON> of Quarry Bank</a>, English politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence\" title=\"Shadow Secretary of State for Defence\">Shadow Secretary of State for Defence</a>", "links": [{"title": "<PERSON>, <PERSON> of Quarry Bank", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Quarry_Bank"}, {"title": "Shadow Secretary of State for Defence", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence"}]}, {"year": "1929", "text": "<PERSON>, French actor, director, and screenwriter (d. 1988)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American philosopher, author, and academic", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Virginia_Held\" title=\"Virginia Held\">Virginia Held</a>, American philosopher, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Held\" title=\"Virginia Held\">Virginia Held</a>, American philosopher, author, and academic", "links": [{"title": "Virginia Held", "link": "https://wikipedia.org/wiki/Virginia_Held"}]}, {"year": "1929", "text": "<PERSON>, American poet, critic, and educator (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, critic, and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, critic, and educator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English actress (d. 2025)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English businessman", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American saxophonist, pianist, and composer (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, pianist, and composer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, pianist, and composer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Cypriot lawyer and politician, 2nd President of Cyprus (d. 2002)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ou"}, {"title": "President of Cyprus", "link": "https://wikipedia.org/wiki/President_of_Cyprus"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American model and actress (d. 2003)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer (d. 1983)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>rin<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>rin<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gar<PERSON>cha"}]}, {"year": "1933", "text": "<PERSON>, English painter and illustrator (d. 2018)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American diplomat, businessman and government official", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat, businessman and government official", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat, businessman and government official", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English director and screenwriter (d. 1990)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter, fiddle-player and guitarist (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, fiddle-player and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, fiddle-player and guitarist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American soul-blues singer-songwriter and guitarist (d. 1995)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul-blues singer-songwriter and guitarist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul-blues singer-songwriter and guitarist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English keyboard player, singer, and saxophonist (d. 1974)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, singer, and saxophonist (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, singer, and saxophonist (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American basketball player and coach", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Japanese martial artist and coach (d. 2019)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese martial artist and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese martial artist and coach (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Liberian journalist, founded The Daily Observer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian journalist, founded <i><a href=\"https://wikipedia.org/wiki/The_Daily_Observer\" title=\"The Daily Observer\">The Daily Observer</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian journalist, founded <i><a href=\"https://wikipedia.org/wiki/The_Daily_Observer\" title=\"The Daily Observer\">The Daily Observer</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Daily Observer", "link": "https://wikipedia.org/wiki/The_Daily_Observer"}]}, {"year": "1938", "text": "<PERSON>, English composer and conductor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American basketball player", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian golfer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English journalist", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English author (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actress and producer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer and pianist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Slovenian gymnast and lawyer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian gymnast and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian gymnast and lawyer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter (d. 2015)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American screenwriter and producer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English singer and guitarist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English-Australian actor", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-Australian actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Algerian footballer (d. 2012)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Abdelkader_Fr%C3%A9ha\" title=\"Abdelkader Fréha\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abdelkader_Fr%C3%A9ha\" title=\"Abdelkader Fréha\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian footballer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abdelkader_Fr%C3%A9ha"}]}, {"year": "1942", "text": "<PERSON>, English computer scientist and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Dutch speed skater", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> V<PERSON>\"><PERSON><PERSON></a>, Dutch speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch speed skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Scottish race car driver", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, English psychologist and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English psychologist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Irish radio and television host (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, Irish radio and television host (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, Irish radio and television host (d. 2014)", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_(broadcaster)"}]}, {"year": "1944", "text": "<PERSON><PERSON>, French comedian and actor (d. 1986)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Coluche\" title=\"Coluche\"><PERSON><PERSON></a>, French comedian and actor (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coluche\" title=\"Coluche\"><PERSON><PERSON></a>, French comedian and actor (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Coluche"}]}, {"year": "1944", "text": "<PERSON>, American actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German businessman, founded the Schlecker Company", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Schlecker Company</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>er\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Schlecker Company</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American lawyer and politician, 19th United States National Security Advisor (d. 2015)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/National_Security_Advisor_(United_States)\" title=\"National Security Advisor (United States)\">United States National Security Advisor</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/National_Security_Advisor_(United_States)\" title=\"National Security Advisor (United States)\">United States National Security Advisor</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Security Advisor (United States)", "link": "https://wikipedia.org/wiki/National_Security_Advisor_(United_States)"}]}, {"year": "1945", "text": "<PERSON>, English pop singer (d. 2020)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pop singer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pop singer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American golfer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian economist and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Dutch footballer and manager (d. 2022)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian poet and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American singer and actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American basketball player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, American decathlete and actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American decathlete and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American decathlete and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Tunisian journalist and activist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tunisian journalist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>rine\"><PERSON><PERSON></a>, Tunisian journalist and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rine"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Belgian cyclist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Lu<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English journalist and author", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American martial artist and author", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American martial artist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American martial artist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian businessman", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American musician, songwriter, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Desmond Child\"><PERSON></a>, American musician, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Desmond Child\"><PERSON></a>, American musician, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American businessman and philanthropist, co-founded Microsoft", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Microsoft", "link": "https://wikipedia.org/wiki/Microsoft"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indian-American businesswoman", "html": "1955 - <a href=\"https://wikipedia.org/wiki/In<PERSON>_No<PERSON>i\" title=\"Indra Nooyi\"><PERSON><PERSON></a>, Indian-American businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indra_Nooyi\" title=\"Indra Nooyi\"><PERSON><PERSON></a>, Indian-American businesswoman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON>_<PERSON>i"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Iranian engineer and politician, 6th President of Iran", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian engineer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Iran\" title=\"President of Iran\">President of Iran</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian engineer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Iran\" title=\"President of Iran\">President of Iran</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Iran", "link": "https://wikipedia.org/wiki/President_of_Iran"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Austrian philosopher, scholar, and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z\" title=\"Vol<PERSON> Zotz\"><PERSON><PERSON></a>, Austrian philosopher, scholar, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z\" title=\"Vol<PERSON> Zotz\"><PERSON><PERSON></a>, Austrian philosopher, scholar, and author", "links": [{"title": "Volker Zotz", "link": "https://wikipedia.org/wiki/Volker_Z<PERSON>z"}]}, {"year": "1957", "text": "<PERSON>, English economist and academic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)"}]}, {"year": "1957", "text": "<PERSON>, English drummer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1957", "text": "<PERSON>, American businessman and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wamp\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>amp"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Spanish journalist (d. 2013)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Concha_Garc%C3%ADa_Campoy\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish journalist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Concha_Garc%C3%ADa_Campoy\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish journalist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Concha_Garc%C3%ADa_Campoy"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Indian businessman and politician, 16th Chief Minister of Maharashtra", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>van\" title=\"<PERSON>ok Chavan\"><PERSON><PERSON></a>, Indian businessman and politician, 16th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Chavan\"><PERSON><PERSON></a>, Indian businessman and politician, 16th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ash<PERSON>_<PERSON>van"}, {"title": "Chief Minister of Maharashtra", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra"}]}, {"year": "1958", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1959", "text": "<PERSON>, Canadian singer-songwriter and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Japanese composer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a>, Japanese composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a>, Japanese composer", "links": [{"title": "<PERSON><PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_(composer)"}]}, {"year": "1959", "text": "<PERSON>, American basketball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American computer scientist and mathematician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Norwegian footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American comedian, actress, and talk show host", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian, actress, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian, actress, and talk show host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English soldier and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian rugby league player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z"}]}, {"year": "1965", "text": "<PERSON>, English composer, businessman, and politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, businessman, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, businessman, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Japanese ballerina", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese ballerina", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese ballerina", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American football player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American blogger and activist, founded the Drudge Report", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blogger and activist, founded the <i><a href=\"https://wikipedia.org/wiki/Drudge_Report\" title=\"Drudge Report\">Drudge Report</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blogger and activist, founded the <i><a href=\"https://wikipedia.org/wiki/Drudge_Report\" title=\"Drudge Report\">Drudge Report</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Drudge Report", "link": "https://wikipedia.org/wiki/Drudge_Report"}]}, {"year": "1966", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Greek politician, Greek Minister of Education and Religious Affairs", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Education_and_Religious_Affairs_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Education and Religious Affairs (Greece)\">Greek Minister of Education and Religious Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Education_and_Religious_Affairs_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Education and Religious Affairs (Greece)\">Greek Minister of Education and Religious Affairs</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oulos"}, {"title": "Ministry of Education and Religious Affairs (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Education_and_Religious_Affairs_(Greece)"}]}, {"year": "1967", "text": "<PERSON>, Scottish director, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Scottish director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Scottish director, producer, and screenwriter", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>(director)"}]}, {"year": "1967", "text": "<PERSON>, American actress and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American video game designer, co-founded Id Software", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer, co-founded <a href=\"https://wikipedia.org/wiki/Id_Software\" title=\"Id Software\">Id Software</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer, co-founded <a href=\"https://wikipedia.org/wiki/Id_Software\" title=\"Id Software\">Id Software</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Id Software", "link": "https://wikipedia.org/wiki/Id_Software"}]}, {"year": "1968", "text": "<PERSON>, American journalist and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, French rugby player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vremont\" title=\"<PERSON>\"><PERSON></a>, French rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vre<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marc_Li%C3%A8vremont"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Dutch cardiologist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cardiologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cardiologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Puerto Rican screenwriter and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Austrian politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Filipino politician and Secretary of Foreign Affairs of the Philippines", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician and <a href=\"https://wikipedia.org/wiki/Secretary_of_Foreign_Affairs\" class=\"mw-redirect\" title=\"Secretary of Foreign Affairs\">Secretary of Foreign Affairs of the Philippines</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician and <a href=\"https://wikipedia.org/wiki/Secretary_of_Foreign_Affairs\" class=\"mw-redirect\" title=\"Secretary of Foreign Affairs\">Secretary of Foreign Affairs of the Philippines</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of Foreign Affairs", "link": "https://wikipedia.org/wiki/Secretary_of_Foreign_Affairs"}]}, {"year": "1970", "text": "<PERSON>, American voice actor and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greg_<PERSON>\" title=\"Greg Eagles\"><PERSON></a>, American voice actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Romanian soprano and actress (d. 2010)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_Briban\" title=\"<PERSON><PERSON><PERSON> B<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian soprano and actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Briban\" title=\"R<PERSON><PERSON> B<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian soprano and actress (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roxana_Briban"}]}, {"year": "1971", "text": "<PERSON>, English businesswoman and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American football player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American reality star", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Trista_Sutter\" title=\"Trista Sutter\"><PERSON><PERSON></a>, American reality star", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sta_Sutter\" title=\"Trista Sutter\"><PERSON><PERSON></a>, American reality star", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trista_Sutter"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American wrestler and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Aleksandar_Stanojevi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksandar_Stanojevi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksandar_Stanojevi%C4%87"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1974", "text": "<PERSON>, Spanish footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Joaquin_<PERSON>\" title=\"Joaquin Phoenix\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaquin_Phoenix\" title=\"Joaquin Phoenix\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joaquin_Phoenix"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Serbian footballer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Puerto Rican actress and singer, Miss Universe 1993", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican actress and singer, <a href=\"https://wikipedia.org/wiki/Miss_Universe_1993\" title=\"Miss Universe 1993\">Miss Universe 1993</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican actress and singer, <a href=\"https://wikipedia.org/wiki/Miss_Universe_1993\" title=\"Miss Universe 1993\">Miss Universe 1993</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Miss Universe 1993", "link": "https://wikipedia.org/wiki/Miss_Universe_1993"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, British rugby league player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British rugby league player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Estonian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Italian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Christie"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter and actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Olcay_%C3%87etinkaya\" title=\"Olcay Çetinkaya\">Olcay Çetinkaya</a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olcay_%C3%87etinkaya\" title=\"Olcay Çetinkaya\">Olcay Çetinkaya</a>, Turkish footballer", "links": [{"title": "Olcay Çetinkaya", "link": "https://wikipedia.org/wiki/Olcay_%C3%87etinkaya"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American computer scientist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American rapper and actress (d. 2012)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper and actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper and actress (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Czech ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0ko<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0ko<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C5%A0koula"}]}, {"year": "1980", "text": "<PERSON>, American wrestler and ring announcer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and ring announcer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and ring announcer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Danish singer-songwriter and pianist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English footballer and coach", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1980)\" title=\"<PERSON> (footballer, born 1980)\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1980)\" title=\"<PERSON> (footballer, born 1980)\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON> (footballer, born 1980)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1980)"}]}, {"year": "1981", "text": "<PERSON>, Czech footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Milan_Baro%C5%A1\" title=\"Milan Baroš\"><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Baro%C5%A1\" title=\"Milan Baroš\"><PERSON></a>, Czech footballer", "links": [{"title": "Milan <PERSON>š", "link": "https://wikipedia.org/wiki/Milan_Baro%C5%A1"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English-Scottish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Enver_J%C3%A4%C3%A4ger\" title=\"Enver Jääger\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Enver_J%C3%A4%C3%A4ger\" title=\"Enver Jää<PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enver_J%C3%A4%C3%A4ger"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English actor and director", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and director", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jack\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jarrett Jack\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Japanese singer and actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1983", "text": "<PERSON>, English actor and screenwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1984", "text": "<PERSON>, New Zealand rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1984)\" title=\"<PERSON> (rugby union, born 1984)\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1984)\" title=\"<PERSON> (rugby union, born 1984)\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON> (rugby union, born 1984)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1984)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Finn_W<PERSON>rock\" title=\"Finn W<PERSON>rock\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finn_W<PERSON>rock\" title=\"Finn W<PERSON>rock\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ittrock"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American music critic", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American music critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American music critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Egyptian-Palestinian actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/May_Calama<PERSON>\" title=\"May <PERSON>\">May <PERSON></a>, Egyptian-Palestinian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_Calama<PERSON>\" title=\"May <PERSON>\">May <PERSON></a>, Egyptian-Palestinian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/May_Calamawy"}]}, {"year": "1986", "text": "<PERSON>, Swedish athlete", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(football)\" class=\"mw-redirect\" title=\"<PERSON> (football)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(football)\" class=\"mw-redirect\" title=\"<PERSON> (football)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(football)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Japanese voice actress and singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank Ocean\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank Ocean\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, English cartoonist and animator (d. 2012)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cartoonist and animator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cartoonist and animator (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edd_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Irish actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Devon_Murray\" title=\"Devon Murray\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Devon_Murray\" title=\"Devon Murray\"><PERSON></a>, Irish actor", "links": [{"title": "Devon Murray", "link": "https://wikipedia.org/wiki/Devon_Murray"}]}, {"year": "1988", "text": "<PERSON>, English musician, DJ, record producer and remixer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_xx\" title=\"<PERSON> xx\"><PERSON> xx</a>, English musician, DJ, record producer and remixer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_xx\" title=\"<PERSON> xx\"><PERSON> xx</a>, English musician, DJ, record producer and remixer", "links": [{"title": "<PERSON> xx", "link": "https://wikipedia.org/wiki/<PERSON>_xx"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Devin_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, French swimmer (d. 2015)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French swimmer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French swimmer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bronze\" title=\"Lucy Bronze\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bronze\" title=\"Lucy Bronze\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, South Korean table tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hee\" title=\"<PERSON><PERSON>-hee\"><PERSON><PERSON>hee</a>, South Korean table tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hee\" title=\"<PERSON><PERSON>-hee\"><PERSON><PERSON>hee</a>, South Korean table tennis player", "links": [{"title": "<PERSON><PERSON>hee", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hee"}]}, {"year": "1992", "text": "<PERSON>, Estonian figure skater", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1995", "text": "<PERSON>, Finnish footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glen_<PERSON>mara"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27S<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jae%27S<PERSON>_Tate"}]}, {"year": "1996", "text": "<PERSON>, American actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Irish cricketer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fritz\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian artistic gymnast", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Georgia_Godwin\" title=\"Georgia Godwin\"><PERSON>win</a>, Australian artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_Godwin\" title=\"Georgia Godwin\"><PERSON>win</a>, Australian artistic gymnast", "links": [{"title": "Georgia Godwin", "link": "https://wikipedia.org/wiki/Georgia_Godwin"}]}, {"year": "1998", "text": "<PERSON>, American actor", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, British tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Sonay_Kartal\" title=\"Sonay Kartal\"><PERSON><PERSON></a>, British tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sonay_Kartal\" title=\"Sonay Kartal\"><PERSON><PERSON></a>, British tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sonay_Kartal"}]}, {"year": "2006", "text": "<PERSON><PERSON>, South Korean footballer", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-young\" title=\"<PERSON><PERSON> Do-young\"><PERSON><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-young\" title=\"<PERSON><PERSON> Do-young\"><PERSON><PERSON>-<PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON><PERSON><PERSON>young", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-young"}]}], "Deaths": [{"year": "312", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 278)", "html": "312 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 278)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 278)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ent<PERSON>"}]}, {"year": "457", "text": "<PERSON><PERSON> of Edessa, Syrian bishop", "html": "457 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Edessa\" title=\"<PERSON><PERSON> of Edessa\"><PERSON><PERSON> of Edessa</a>, Syrian bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Edessa\" title=\"<PERSON><PERSON> of Edessa\"><PERSON><PERSON> of Edessa</a>, Syrian bishop", "links": [{"title": "<PERSON><PERSON> of Edessa", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_<PERSON><PERSON><PERSON>"}]}, {"year": "816", "text": "<PERSON><PERSON><PERSON>, count of Toulouse and Paris", "html": "816 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Toulouse\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Count of Toulouse\"><PERSON><PERSON><PERSON></a>, count of <a href=\"https://wikipedia.org/wiki/County_of_Toulouse\" title=\"County of Toulouse\">Toulouse</a> and <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Toulouse\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Count of Toulouse\"><PERSON><PERSON><PERSON></a>, count of <a href=\"https://wikipedia.org/wiki/County_of_Toulouse\" title=\"County of Toulouse\">Toulouse</a> and <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Toulouse", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Toulouse"}, {"title": "County of Toulouse", "link": "https://wikipedia.org/wiki/County_of_Toulouse"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "875", "text": "<PERSON><PERSON><PERSON><PERSON> of Lyon, Frankish archbishop", "html": "875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Lyon\" title=\"<PERSON><PERSON><PERSON><PERSON> of Lyon\"><PERSON><PERSON><PERSON><PERSON> of Lyon</a>, <PERSON>ish <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Lyon\" title=\"<PERSON><PERSON><PERSON><PERSON> of Lyon\"><PERSON><PERSON><PERSON><PERSON> of Lyon</a>, Frankish <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Lyon", "link": "https://wikipedia.org/wiki/Remigi<PERSON>_of_Lyon"}, {"title": "Archbishop", "link": "https://wikipedia.org/wiki/Archbishop"}]}, {"year": "1138", "text": "King <PERSON><PERSON><PERSON> Wrymouth of Poland", "html": "1138 - King <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_III_Wrymouth\" title=\"Bo<PERSON><PERSON> III Wrymouth\"><PERSON><PERSON><PERSON> III Wrymouth</a> of <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a>", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_III_Wrymouth\" title=\"Bolesław III Wrymouth\"><PERSON><PERSON><PERSON> III Wrymouth</a> of <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a>", "links": [{"title": "<PERSON><PERSON><PERSON> III Wrymouth", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_III_Wrymouth"}, {"title": "Poland", "link": "https://wikipedia.org/wiki/Poland"}]}, {"year": "1225", "text": "<PERSON><PERSON>, Japanese monk, historian, and poet (b. 1155)", "html": "1225 - <a href=\"https://wikipedia.org/wiki/Jien\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese monk, historian, and poet (b. 1155)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jien\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese monk, historian, and poet (b. 1155)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>en"}]}, {"year": "1266", "text": "Saint Arsenije <PERSON>ema<PERSON>", "html": "1266 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON>rseni<PERSON>ema<PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arsen<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON> Arsenije <PERSON> Sremac</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1310", "text": "Ecumenical Patriarch <PERSON><PERSON><PERSON> of Constantinople (b. 1230)", "html": "1310 - <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_<PERSON><PERSON><PERSON>_<PERSON>_of_Constantinople\" class=\"mw-redirect\" title=\"Ecumenical Patriarch <PERSON><PERSON><PERSON> of Constantinople\">Ecumenical Patriarch <PERSON><PERSON><PERSON> of Constantinople</a> (b. 1230)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_<PERSON><PERSON><PERSON>_<PERSON>_of_Constantinople\" class=\"mw-redirect\" title=\"Ecumenical Patriarch <PERSON><PERSON><PERSON> of Constantinople\">Ecumenical Patriarch <PERSON><PERSON><PERSON> of Constantinople</a> (b. 1230)", "links": [{"title": "Ecumenical Patriarch <PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Ecumenical_Patriarch_<PERSON><PERSON><PERSON>_<PERSON>_of_Constantinople"}]}, {"year": "1312", "text": "<PERSON> of Carinthia, Queen of Germany (b. 1262)", "html": "1312 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Carinthia,_Queen_of_Germany\" class=\"mw-redirect\" title=\"<PERSON> of Carinthia, Queen of Germany\"><PERSON> of Carinthia, Queen of Germany</a> (b. 1262)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Carinthia,_Queen_of_Germany\" class=\"mw-redirect\" title=\"<PERSON> of Carinthia, Queen of Germany\"><PERSON> of Carinthia, Queen of Germany</a> (b. 1262)", "links": [{"title": "<PERSON> of Carinthia, Queen of Germany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Carinthia,_Queen_of_Germany"}]}, {"year": "1412", "text": "<PERSON> of Denmark (b. 1353)", "html": "1412 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1353)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1353)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1468", "text": "<PERSON>, Duchess of Milan (b. 1425)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Duchess of Milan (b. 1425)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Duchess of Milan (b. 1425)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1568", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (b. 1539)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/Ash<PERSON><PERSON>_Yoshihide\" title=\"Ashikaga Yoshihide\"><PERSON><PERSON><PERSON></a>, Japanese shō<PERSON> (b. 1539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yoshihide\" title=\"Ashika<PERSON> Yoshihide\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1539)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ika<PERSON>_Yoshihide"}]}, {"year": "1592", "text": "<PERSON><PERSON><PERSON>, Flemish diplomat", "html": "1592 - <a href=\"https://wikipedia.org/wiki/Ogier_Ghiselin_de_Busbecq\" title=\"Ogier Ghiselin de Busbecq\"><PERSON><PERSON><PERSON> G<PERSON></a>, Flemish diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ogier_Ghiselin_de_Busbecq\" title=\"Ogier Ghiselin de Busbecq\"><PERSON><PERSON><PERSON> G<PERSON></a>, Flemish diplomat", "links": [{"title": "<PERSON><PERSON><PERSON> Ghiselin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1594", "text": "<PERSON><PERSON><PERSON>, Japanese general (b. 1532)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/%C5%8C<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ku<PERSON> Tadayo\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1532)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8C<PERSON><PERSON>_<PERSON><PERSON>o"}]}, {"year": "1627", "text": "<PERSON><PERSON><PERSON><PERSON>, Mughal Emperor of India (b. 1569)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/Jahangir\" title=\"Jahangir\"><PERSON><PERSON><PERSON><PERSON></a>, Mughal Emperor of India (b. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jahangir\" title=\"Jahangir\"><PERSON><PERSON><PERSON><PERSON></a>, Mughal Emperor of India (b. 1569)", "links": [{"title": "Jahangir", "link": "https://wikipedia.org/wiki/Jahangir"}]}, {"year": "1639", "text": "<PERSON>, Italian composer and educator (b. 1587)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1587)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1587)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1646", "text": "<PERSON>, English painter (b. 1610)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1661", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish priest and playwright (b. 1618)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Moreto_y_Cavana\" title=\"A<PERSON><PERSON><PERSON> y <PERSON>avan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish priest and playwright (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Moreto_y_Cavana\" title=\"A<PERSON><PERSON><PERSON> y Cavana\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish priest and playwright (b. 1618)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> y Cavana", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_Moreto_y_Cavana"}]}, {"year": "1676", "text": "<PERSON>, French author, poet, and playwright (b. 1595)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (b. 1595)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1703", "text": "<PERSON>, English mathematician and cryptographer (b. 1616)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and cryptographer (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and cryptographer (b. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, English physician and philosopher (b. 1632)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and philosopher (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and philosopher (b. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1708", "text": "<PERSON> of Denmark (b. 1653)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Denmark\" title=\"Prince <PERSON> of Denmark\">Prince <PERSON> of Denmark</a> (b. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Denmark\" title=\"Prince <PERSON> of Denmark\">Prince <PERSON> of Denmark</a> (b. 1653)", "links": [{"title": "Prince <PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1716", "text": "<PERSON>, English politician (b. 1627)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1627)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON> of Russia (b. 1693)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/Anna_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_of_Russia\" title=\"Anna of Russia\"><PERSON> of Russia</a> (b. 1693)", "links": [{"title": "Anna of Russia", "link": "https://wikipedia.org/wiki/Anna_of_Russia"}]}, {"year": "1754", "text": "<PERSON>, German poet (b. 1708)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet (b. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet (b. 1708)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, French composer (b. 1689)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1689)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, German general and politician (b. 1700)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%<PERSON>hl\" title=\"<PERSON>\"><PERSON></a>, German general and politician (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%BChl\" title=\"<PERSON>\"><PERSON></a>, German general and politician (b. 1700)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BChl"}]}, {"year": "1768", "text": "<PERSON>, French flute player and composer (b. 1700)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French flute player and composer (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French flute player and composer (b. 1700)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, German author (b. 1735)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A4us\" title=\"<PERSON>\"><PERSON></a>, German author (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A4us\" title=\"<PERSON>\"><PERSON></a>, German author (b. 1735)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Mu<PERSON>%C3%A4us"}]}, {"year": "1792", "text": "<PERSON>, German physician, botanist, and zoologist (b. 1710)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hring\" title=\"<PERSON>\"><PERSON></a>, German physician, botanist, and zoologist (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hring\" title=\"<PERSON>\"><PERSON></a>, German physician, botanist, and zoologist (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_M%C3%B6hring"}]}, {"year": "1792", "text": "<PERSON>, English engineer, designed the Coldstream Bridge and Perth Bridge (b. 1724)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/Coldstream_Bridge\" title=\"Coldstream Bridge\">Coldstream Bridge</a> and <a href=\"https://wikipedia.org/wiki/Perth_Bridge\" title=\"Perth Bridge\">Perth Bridge</a> (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/Coldstream_Bridge\" title=\"Coldstream Bridge\">Coldstream Bridge</a> and <a href=\"https://wikipedia.org/wiki/Perth_Bridge\" title=\"Perth Bridge\">Perth Bridge</a> (b. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Coldstream Bridge", "link": "https://wikipedia.org/wiki/Coldstream_Bridge"}, {"title": "Perth Bridge", "link": "https://wikipedia.org/wiki/Perth_Bridge"}]}, {"year": "1800", "text": "<PERSON><PERSON>, American general and politician (b. 1727)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/Artemas_Ward\" title=\"Artemas Ward\"><PERSON><PERSON></a>, American general and politician (b. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arte<PERSON>_Ward\" title=\"Artemas Ward\"><PERSON><PERSON></a>, American general and politician (b. 1727)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artemas_Ward"}]}, {"year": "1806", "text": "<PERSON>, English poet and author (b. 1749)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, American writer and second First Lady of the United States (b. 1744)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and second <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and second <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (b. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1841", "text": "<PERSON>, Swedish chemist and academic (b. 1792)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish chemist and academic (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish chemist and academic (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_<PERSON><PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON><PERSON>, French general and politician, 26th Prime Minister of France (b. 1802)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and politician, 26th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and politician, 26th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1802)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Louis-Eug%C3%A8ne_<PERSON>ignac"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1877", "text": "<PERSON>, English ornithologist and entomologist (b. 1835)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and entomologist (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and entomologist (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, French economist and politician (b. 1799)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician (b. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, German-American engineer, invented the Linotype machine (b. 1854)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American engineer, invented the <a href=\"https://wikipedia.org/wiki/Linotype_machine\" title=\"Linotype machine\">Linotype machine</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American engineer, invented the <a href=\"https://wikipedia.org/wiki/Linotype_machine\" title=\"Linotype machine\">Linotype machine</a> (b. 1854)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Linotype machine", "link": "https://wikipedia.org/wiki/Linotype_machine"}]}, {"year": "1900", "text": "<PERSON>, German philologist and orientalist (b. 1823)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Max_M%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German philologist and orientalist (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Max_M%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German philologist and orientalist (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Max_M%C3%BCller"}]}, {"year": "1906", "text": "<PERSON>, French artist (b. 1836)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Austrian composer and critic (b. 1850)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and critic (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and critic (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American meteorologist and academic (b. 1838)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Cleveland_Abbe\" title=\"Cleveland Abbe\"><PERSON> Abbe</a>, American meteorologist and academic (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cleveland_Abbe\" title=\"Cleveland Abbe\"><PERSON> Abbe</a>, American meteorologist and academic (b. 1838)", "links": [{"title": "Cleveland Abbe", "link": "https://wikipedia.org/wiki/Cleveland_Abbe"}]}, {"year": "1916", "text": "<PERSON>, German WWI flying ace (b. 1891)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German WWI flying ace (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German WWI flying ace (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON> <PERSON> of Schleswig-Holstein (b. 1831)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Schleswig-Holstein\" title=\"Prince <PERSON> of Schleswig-Holstein\">Prince <PERSON> of Schleswig-Holstein</a> (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Schleswig-Holstein\" title=\"Prince <PERSON> of Schleswig-Holstein\">Prince <PERSON> of Schleswig-Holstein</a> (b. 1831)", "links": [{"title": "Prince <PERSON> of Schleswig-Holstein", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_of_Schleswig-Holstein"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Greek lawyer and politician (b. 1841)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician (b. 1841)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dimitri<PERSON>_<PERSON>is"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Italian mathematician and politician (b. 1845)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mathematician and politician (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mathematician and politician (b. 1845)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1929", "text": "<PERSON>, German soldier and politician, Chancellor of Germany (b. 1849)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Germany</a> (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Germany</a> (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BClow"}, {"title": "List of Chancellors of Germany", "link": "https://wikipedia.org/wiki/List_of_Chancellors_of_Germany"}]}, {"year": "1936", "text": "<PERSON>, Australian soldier and politician, 8th Premier of Western Australia (b. 1870)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1939", "text": "<PERSON>, American actress (b. 1892)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Soviet politician (b. 1876)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet politician (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet politician (b. 1876)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>n"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Japanese general (b. 1881)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English-Australian politician, 7th Prime Minister of Australia (b. 1862)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1957", "text": "<PERSON>, German-American physician and gynecologist (b. 1881)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physician and gynecologist (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physician and gynecologist (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_Gr%C3%A4fenberg"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Cuban soldier (b. 1932)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cienfuegos\" title=\"<PERSON><PERSON> Cienfuegos\"><PERSON><PERSON></a>, Cuban soldier (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cienfuegos\" title=\"<PERSON><PERSON> Cienfuegos\"><PERSON><PERSON></a>, Cuban soldier (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camilo_Cienfuegos"}]}, {"year": "1963", "text": "<PERSON>, Estonian organist and composer (b. 1882)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Mart_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian organist and composer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart_Saar\" title=\"Mart <PERSON>\"><PERSON></a>, Estonian organist and composer (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart_Saar"}]}, {"year": "1965", "text": "<PERSON>, Scottish mountaineer and physiologist (b. 1882)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mountaineer and physiologist (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mountaineer and physiologist (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American model and actress (b. 1920)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter (b. 1944)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1944)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(singer)"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Egyptian historian, author, and academic (b. 1889)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian historian, author, and academic (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian historian, author, and academic (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Italian actor, director, and playwright (b. 1883)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and playwright (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and playwright (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, French boxer and actor (b. 1894)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French boxer and actor (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French boxer and actor (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American saxophonist, clarinet player, and composer (b. 1932)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, clarinet player, and composer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, clarinet player, and composer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Finnish army captain (b. 1904)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish army captain (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish army captain (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Sri Lankan singer and actress (b. 1923)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan singer and actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan singer and actress (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American animator and screenwriter (b. 1892)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and screenwriter (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and screenwriter (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English author (b. 1922)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, French soldier and painter (b. 1896)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and painter (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and painter (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>on"}]}, {"year": "1989", "text": "<PERSON>, English bandleader, composer, and actor (b. 1898)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)\" title=\"<PERSON> (bandleader)\"><PERSON></a>, English bandleader, composer, and actor (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bandleader)\" title=\"<PERSON> (bandleader)\"><PERSON></a>, English bandleader, composer, and actor (b. 1898)", "links": [{"title": "<PERSON> (bandleader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Russian-Estonian historian and scholar (b. 1922)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Estonian historian and scholar (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Estonian historian and scholar (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American screenwriter and producer (b. 1915)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English poet and playwright (b. 1930)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Greek singer-songwriter (b. 1931)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player (b. 1969)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/And%C3%BAjar_Cede%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/And%C3%BAjar_Cede%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player (b. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/And%C3%BAjar_Cede%C3%B1o"}]}, {"year": "2001", "text": "<PERSON>, Dutch pianist, composer, and educator (b. 1910)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pianist, composer, and educator (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pianist, composer, and educator (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American screenwriter and producer (b. 1898)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Swedish businessman, founded H&M (b. 1917)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish businessman, founded <a href=\"https://wikipedia.org/wiki/H%26M\" title=\"H&amp;M\">H&amp;M</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish businessman, founded <a href=\"https://wikipedia.org/wiki/H%26M\" title=\"H&amp;M\">H&amp;M</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erlin<PERSON>_<PERSON>"}, {"title": "H&M", "link": "https://wikipedia.org/wiki/H%26M"}]}, {"year": "2003", "text": "<PERSON>, Scottish social sciences professor (b. 1940)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish social sciences professor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish social sciences professor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American colonel and author, US Commandant of Spandau Prison (b. 1926)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and author, US Commandant of Spandau Prison (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and author, US Commandant of Spandau Prison (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American soldier and journalist (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and journalist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and journalist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, French photographer (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American basketball player (b. 1942)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1942)\" title=\"<PERSON> (basketball, born 1942)\"><PERSON></a>, American basketball player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1942)\" title=\"<PERSON> (basketball, born 1942)\"><PERSON></a>, American basketball player (b. 1942)", "links": [{"title": "<PERSON> (basketball, born 1942)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1942)"}]}, {"year": "2005", "text": "<PERSON>, Cape Verdean-Portuguese singer-songwriter (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cape Verdean-Portuguese singer-songwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cape Verdean-Portuguese singer-songwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (b. 1943)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Serbian actor and screenwriter (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Ljuba_Tadi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian actor and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lju<PERSON>_Tadi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian actor and screenwriter (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ljuba_Tadi%C4%87"}]}, {"year": "2006", "text": "<PERSON>, American basketball player and coach (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>bach\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Au<PERSON>bach\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Au<PERSON>bach"}]}, {"year": "2006", "text": "<PERSON>, Jamaican-Canadian boxer (b. 1954)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-Canadian boxer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-Canadian boxer (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, American guitarist and songwriter (b. 1920)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Wilkin\"><PERSON><PERSON><PERSON></a>, American guitarist and songwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Wilkin\"><PERSON><PERSON><PERSON></a>, American guitarist and songwriter (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>kin"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Japanese lawyer and politician (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Takao_<PERSON>nami"}]}, {"year": "2007", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Porter Wagoner\"><PERSON></a>, American singer-songwriter and guitarist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Porter Wagoner\"><PERSON></a>, American singer-songwriter and guitarist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "2009", "text": "<PERSON>, Canadian singer-songwriter and guitarist (b. 1990)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Chinese historian and activist, founded Friends of Nature (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese historian and activist, founded <a href=\"https://wikipedia.org/wiki/Friends_of_Nature_(China)\" title=\"Friends of Nature (China)\">Friends of Nature</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese historian and activist, founded <a href=\"https://wikipedia.org/wiki/Friends_of_Nature_(China)\" title=\"Friends of Nature (China)\">Friends of Nature</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Friends of Nature (China)", "link": "https://wikipedia.org/wiki/Friends_of_Nature_(China)"}]}, {"year": "2010", "text": "<PERSON>, American actor (b. 1937)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Greenlandic politician, 1st Prime Minister of Greenland (b. 1938)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greenlandic politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greenland\" title=\"Prime Minister of Greenland\">Prime Minister of Greenland</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greenlandic politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greenland\" title=\"Prime Minister of Greenland\">Prime Minister of Greenland</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Greenland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greenland"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Israeli archaeologist, architect, and educator (b. 1934)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli archaeologist, architect, and educator (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli archaeologist, architect, and educator (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zer"}]}, {"year": "2011", "text": "<PERSON>, English soldier (b. 1919)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian dentist and politician (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian dentist and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian dentist and politician (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian footballer and coach (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English businessman (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese baseball player and manager (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Polish journalist and politician, Prime Minister of Poland (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zowiecki"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian journalist (b. 1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Aleksandar_Tijani%C4%87\" title=\"Aleksandar <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian journalist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksandar_Tijani%C4%87\" title=\"Aleksan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian journalist (b. 1949)", "links": [{"title": "<PERSON>ek<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksandar_Tijani%C4%87"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Indian author (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>v\"><PERSON><PERSON></a>, Indian author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American poet and academic (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Galway_<PERSON>ll\" title=\"Galway Kinnell\"><PERSON></a>, American poet and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Galway_Kinnell\" title=\"<PERSON> Kinnell\"><PERSON></a>, American poet and academic (b. 1927)", "links": [{"title": "<PERSON> Kinnell", "link": "https://wikipedia.org/wiki/Galway_<PERSON>ll"}]}, {"year": "2014", "text": "<PERSON>, Zambian police officer and politician, 5th President of Zambia (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian police officer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian police officer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Zambia", "link": "https://wikipedia.org/wiki/President_of_Zambia"}]}, {"year": "2018", "text": "<PERSON>, Australian rules footballer (b. 1985)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer (b. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American singer-songwriter and pianist (b. 1935)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American-Canadian actor (b. 1969)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American ice hockey player (b. 1994)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player (b. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player (b. 1994)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Italian Roman Catholic cardinal (b. 1932)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian Roman Catholic cardinal (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian Roman Catholic cardinal (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American director, producer, and screenwriter (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, German-American affiliate of Kingdom Assembly of Iran (b. 1955)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American affiliate of Kingdom Assembly of Iran (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American affiliate of Kingdom Assembly of Iran (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Japanese manga artist (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese manga artist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese manga artist (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}]}}